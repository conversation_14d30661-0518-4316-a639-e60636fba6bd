# Service的名字，必选，需自定义修改
Name = "licloud_sde_vm"

# 各种自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用bns，超时参数不需要配置，若配置会覆盖 BNS 配置的值
# 连接超时，单位 ms,默认5000
ConnTimeOut = 5000
# 写数据超时，单位 ms,默认5000
WriteTimeOut = 30000
# 读数据超时，单位 ms,默认5000
ReadTimeOut = 30000

# 可选，请求失败后的重试次数：总请求次数 = Retry + 1,默认0
Retry = 2

[Strategy]
# 资源使用策略，非必选，默认使用 RoundRobin
# RoundRobin-依次轮询 Random-随机 LocalityAware-la加权轮询，需要策略配置，
Name="RoundRobin"

# 资源定位配置必须有且只有一项
# 资源定位：使用 BNS
# [Resource.BNS]
# BNSName = "group.test-service.all"
# PortKey = "pbrpc"
# 资源定位：手动配置 - 使用IP、端口
[Resource.Manual]
[[Resource.Manual.default]]
Host = "devops-sde-valet-api.prod-devops.k8s.chj.cloud"
Port = 80

[LiCloudVmConfig.Prod]
DefaultOwner = "caoyuning_89499"
DefaultTenantID = "zoksoz"
DefaultVdc = "euc01"
DefaultEnv = "prod"
DefaultSubnetID = "subnet-0ba087ea9600c857f"
DefaultInstanceType = "cal-4c8g.i.v1"

[LiCloudVmConfig.Test]
DefaultOwner = "caoyuning_89499"
DefaultTenantID = "zoksoz"
DefaultVdc = "euc01"
DefaultEnv = "test"
DefaultSubnetID = "subnet-03baa1394de970b26"
DefaultInstanceType = "cal-4c8g.i.v1"

[[LiCloudVmConfig.LiSpecs]]
InstanceType = "cal-2c4g.a.v1"
Core = 2
MemGB = 4

[[LiCloudVmConfig.LiSpecs]]
InstanceType = "com-2c8g.a.v1"
Core = 2
MemGB = 8

[[LiCloudVmConfig.LiSpecs]]
InstanceType = "mem-2c16g.a.v1"
Core = 2
MemGB = 16

[[LiCloudVmConfig.LiSpecs]]
InstanceType = "mem-4c32g.a.v1"
Core = 4
MemGB = 32