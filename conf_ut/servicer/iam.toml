# Service的名字，必选，需自定义修改
Name = "iam"

# 连接超时，单位 ms,默认5000
ConnTimeOut = 1000
# 写数据超时，单位 ms,默认5000
WriteTimeOut = 2000
# 读数据超时，单位 ms,默认5000
ReadTimeOut = 2000

# 可选，请求失败后的重试次数：总请求次数 = Retry + 1,默认0
Retry = 2

# 资源定位
[Resource.Manual]
    [[Resource.Manual.default]]
        Host = "iam.bj.internal-qasandbox.baidu-int.com"
        Port = 80

# 其他专有配置，如mysql、redis等都有专有配置
[IAM]
    Protocol = "http"
    UserName = "scs"
    Password = "scs"
    Domain = "default"
