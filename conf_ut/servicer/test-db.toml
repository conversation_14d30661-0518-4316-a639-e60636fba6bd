Name = "test-db"
ConnTimeOut = 200
WriteTimeOut = 200
ReadTimeOut = 1000
[Strategy]
Name="RoundRobin"
[Resource.Manual]
[[Resource.Manual.default]]
Host = "*************"
Port = 3306

[MySQL]
Username    = "root"
Password    = "paastest123"
DBName      = "x1task"
DBDriver    = "mysql"
MaxOpenPerIP= 5
MaxIdlePerIP= 5
ConnMaxLifeTime = 5000
LogIDTransport = true
DSNParams ="charset=utf8&timeout=90s&collation=utf8mb4_unicode_ci&parseTime=true"

[MySQL.IDC_SPEC.test]
Username    = "test"
Password    = "test"
