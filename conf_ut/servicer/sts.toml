# Service的名字，必选，需自定义修改
Name = "sts"

# 各种自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用bns，超时参数不需要配置，若配置会覆盖 BNS 配置的值
# 连接超时，单位 ms,默认5000
ConnTimeOut = 500
# 写数据超时，单位 ms,默认5000
WriteTimeOut = 500
# 读数据超时，单位 ms,默认5000
ReadTimeOut = 1000

# 可选，请求失败后的重试次数：总请求次数 = Retry + 1,默认0
Retry = 1

[Strategy]
    # 资源使用策略，非必选，默认使用 RoundRobin
    # RoundRobin-依次轮询 Random-随机 LocalityAware-la加权轮询，需要策略配置，
    Name="RoundRobin"

# 资源定位配置必须有且只有一项
# 资源定位：使用 BNS
# [Resource.BNS]
# BNSName = "group.test-service.all"
# PortKey = "pbrpc"
# 资源定位：手动配置 - 使用IP、端口
[Resource.Manual]
    [[Resource.Manual.default]]
        Host = "sts.bj.internal-qasandbox.baidu-int.com"
        Port = 8586

#其他专有配置，如mysql、redis等都有专有配置
[STS]
    ServiceId = "5bb35ef26f944f118cf0d569867db929"
    ServiceAk = "74a500c63f6b4d0e9889813c74f03644"
    ServiceSk = "dd00078db7844fd8bec69ea71787d906"
    DefaultRole = "BceServiceRole_SCS"
    DurationSec = 7200
    AuthCachedSec = 300

    ResourceID = "ea2c4a2286ca4540afcb7f7d4ba2d199"
    ResourceAk = "73562ca68d7d4f35b0cfdb9415d9d7fa"
    ResourceSk = "9c4829349d434e6a9ec5d8115cc2ad58"
