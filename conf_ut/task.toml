[EngineConfig]
EngineRunInterval = 300
EngineRunningTimeout = 10000
EngineLockTTL = 11000

[ScheduleConfig]
ScheduleRunInterval = 300
ScheduleRunningTimeout = 10000
ScheduleLockTTL = 11000

[WorkerConfig]
WorkerMaxReportErrorTime = 60000
WorkerHighPriorityCount = 50
WorkerMediumPriorityCount = 500
WorkerLowPriorityCount = 50
WorkerRunInterval = 300

[RetryConfig]
Level1RetryCount = 10
Level1RetryInterval = 300
Level2RetryCount = 50
Level2RetryInterval = 3000
Level3RetryCount = 500
Level3RetryInterval = 30000
ManualRetryCount = 1000

[HTTPServerConfig]
HTTPServerRunMode = "release"
HTTPServerPort = 8000
