
InstanceTypesResizeAllowed=["N5"]
ForbidLiveResize=false
OrderOverTime=120
MasterEndpoint="scs.agilecloud.com"

[[DefaultConfigList]]
AZone="Azone-bjyz"
StoreType="DRAM"
DefaultInstanceType="N3"
AlternativeInstanceTypes=[]
RootDiskStorageType="ssd"
RootDiskUseLocalDisk=true
DataDiskStorageType="ssd"
DataDiskUseLocalDisk=true

[[DefaultConfigList]]
AZone="AZONE-bdfsg"
StoreType="DRAM"
DefaultInstanceType="N1"
AlternativeInstanceTypes=[]
RootDiskStorageType="ssd"
RootDiskUseLocalDisk=true
DataDiskStorageType="ssd"
DataDiskUseLocalDisk=true

[[DefaultConfigList]]
AZone="AZONE-bjddfsg"
StoreType="DRAM"
DefaultInstanceType="N1"
AlternativeInstanceTypes=[]
RootDiskStorageType="ssd"
RootDiskUseLocalDisk=true
DataDiskStorageType="ssd"
DataDiskUseLocalDisk=true

[[DefaultConfigList]]
AZone="AZONE-bdbl"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="cloud_hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-fsh"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="cloud_hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-gzhxy"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="cloud_hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-gzns"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="cloud_hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-gznj"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="cloud_hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-szth"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="cloud_hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="AZONE-wxtky"
StoreType="DRAM"
DefaultInstanceType="N5"
AlternativeInstanceTypes=[]
RootDiskStorageType="cloud_hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="*"
StoreType="DRAM"
DefaultInstanceType="N6"
AlternativeInstanceTypes=["N5"]
RootDiskStorageType="cloud_hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[DefaultConfigList]]
AZone="*"
StoreType="AEP"
DefaultInstanceType="AEP"
AlternativeInstanceTypes=[]
RootDiskStorageType="cloud_hp1"
RootDiskUseLocalDisk=false
DataDiskStorageType="cloud_hp1"
DataDiskUseLocalDisk=false

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=1
CpuList=[1]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=2
CpuList=[1, 2]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=4
CpuList=[1, 2, 4]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=8
CpuList=[1, 2, 4, 8]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=12
CpuList=[2, 4, 8, 12]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=16
CpuList=[2, 4, 8, 12, 16]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=32
CpuList=[2, 4, 8, 12, 16]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=48
CpuList=[2, 4, 8, 12, 16]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=64
CpuList=[2, 4, 8, 12, 16]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=96
CpuList=[2, 4, 24, 48]

[[BCCAllowedFlavorList]]
MemoryCapacityInGB=128
CpuList=[2, 4, 16, 24, 32, 48, 64]
