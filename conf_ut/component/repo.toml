##
# 以下是各种repo定义
#
[Repos]

# default_http
[Repos.default_http]
    Name = "default_http"
    Type = "http"
    Endpoint = "http://10.136.153.51:8069"

# package_bos
[Repos.package_bos]
    Name = "package_bos"
    Type = "bos"
    Endpoint = "http://bj-bos-sandbox.baidu-int.com"
    Ak = "********************************"
    Sk = "QL9pe7E4ugtzRO8znUEOyV1BDcli9I86rUOtbnQHfaSORNl5cUl5xxAJvdRCZb8dMAbjNzNnG89eFnor_atgyQ"
    Bucket = "scs-sandbox-packages"
    AuthDuration = 1800

# backup_bos
[Repos.backup_bos]
    Name = "backup_bos"
    Type = "bos"
    Endpoint = "http://bj-bos-sandbox.baidu-int.com"
    Ak = "********************************"
    Sk = "QL9pe7E4ugtzRO8znUEOyV1BDcli9I86rUOtbnQHfaSORNl5cUl5xxAJvdRCZb8dMAbjNzNnG89eFnor_atgyQ"
    AuthDuration = 1800
