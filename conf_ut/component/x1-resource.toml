# x1-resource configuration for testing
DeploySetTagPrefix = "test-deployset-"

# Test configuration with exact match
[[SpecResourceConfig]]
AZone = "AZONE-bjyz"
Spec = "pega.l5ds1.small"
ResourceTag = "test-tag-exact"
DeviceCapacityInGB = 100
DeviceWriteIOPS = 10000000
DeviceReadIOPS = 100000000
DeviceWriteIOBPS = 100000000
DeviceReadIOBPS = 1000000000
DeviceWriteIOPSLimitRate = 1000
DeviceReadIOPSLimitRate = 1000
DeviceWriteIOBPSLimitRate = 1000
DeviceReadIOBPSLimitRate = 1000

# Test configuration with wildcard zone
[[SpecResourceConfig]]
AZone = "*"
Spec = "pega.l5ds1.medium"
ResourceTag = "test-tag-wildcard-zone"
DeviceCapacityInGB = 200
DeviceWriteIOPS = 20000000
DeviceReadIOPS = 200000000
DeviceWriteIOBPS = 200000000
DeviceReadIOBPS = 2000000000
DeviceWriteIOPSLimitRate = 2000
DeviceReadIOPSLimitRate = 2000
DeviceWriteIOBPSLimitRate = 2000
DeviceReadIOBPSLimitRate = 2000

# Test configuration with wildcard spec
[[SpecResourceConfig]]
AZone = "AZONE-gzyz"
Spec = "*"
ResourceTag = "test-tag-wildcard-spec"
DeviceCapacityInGB = 300
DeviceWriteIOPS = 30000000
DeviceReadIOPS = 300000000
DeviceWriteIOBPS = 300000000
DeviceReadIOBPS = 3000000000
DeviceWriteIOPSLimitRate = 3000
DeviceReadIOPSLimitRate = 3000
DeviceWriteIOBPSLimitRate = 3000
DeviceReadIOBPSLimitRate = 3000

# Test configuration with both wildcards
[[SpecResourceConfig]]
AZone = "*"
Spec = "*"
ResourceTag = "test-tag-both-wildcard"
DeviceCapacityInGB = 400
DeviceWriteIOPS = 40000000
DeviceReadIOPS = 400000000
DeviceWriteIOBPS = 400000000
DeviceReadIOBPS = 4000000000
DeviceWriteIOPSLimitRate = 4000
DeviceReadIOPSLimitRate = 4000
DeviceWriteIOBPSLimitRate = 4000
DeviceReadIOBPSLimitRate = 4000
