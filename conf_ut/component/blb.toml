[BLBConf]
    HealthCheck = "TCP"
    HealthCheckTimeoutInSecond = 3
    HealthCheckUpRetry = 3
    HealthCheckDownRetry = 3
    HealthCheckIntervalInSecond = 3

[BLBTCPListenerConf]
    TCPSessionTimeout = 900

[BLBHTTPListenerConf]
    TcpSessionTimeoutInMiliSecond = 4000
    KeepSession = false
    KeepSessionTimeout = 3600
    XForwardedFor = false
    XForwardedProto = false
    ServerTimeout = 30

[BLBEnv]
    ResourcePrivateUserId = "0c0b3c9dbb6e41308d3bfd587d908922"
    ResourcePrivateVpcId = "d50f04f3-bb01-4b31-9c09-ba46939308ab"

    ResourceCloudUserId = "dd6080cc7e7544fbbc83c70ef57789d4"
    ResourceCloudVpcId = "a45f11da-ca7d-4377-9379-e49f167bf07f"
    [[BLBEnv.ResourceCloudSubnet]]
        Zone = "AZONE-gzns"
        SubnetId = "72a5a504-41d4-405e-8476-26630401eb7f"
    [[BLBEnv.ResourceCloudSubnet]]
        Zone = "AZONE-gzhxy"
        SubnetId = "89f553e9-c2ac-4320-b938-9f698c045acf"
    [[BLBEnv.ResourceCloudSubnet]]
        Zone = "AZONE-bjdd"
        SubnetId = "da187181-43b4-493f-86e6-8e948e1020d4"

    # Sandbox privite
    [[BLBEnv.ResourcePrivateSubnet]]
        Zone = "AZONE-gzns"
        SubnetId = "e12447e0-73e3-4b7a-8097-1472e6cc91b1"
    [[BLBEnv.ResourcePrivateSubnet]]
        Zone = "AZONE-gzhxy"
        SubnetId = "ab34c1c9-7132-480d-9ed8-0dfc10728cd0"

    # CloudUserOfUsePrivateResource
    [[BLBEnv.CloudUserOfUsePrivateResource]]
        # http://cloud-sandbox-center.amis.baidu.com/quota/AccountUsage?perPage=100&page=1&project=SCS
        UserId = "204d95b9fa0c4cad8eeea3860b5ad6cf"
    [[BLBEnv.CloudUserOfUsePrivateResource]]
        # wenku
        UserId = "df3e95db5bea4223b07a641de71386aa"

[BLBEnv.IDC_SPEC.testnew1]
    ResourcePrivateUserId = "0c0b3c9dbb6e41308d3bfd587d908922"

    ResourceCloudUserId = "3f201035e46c47f79dcd210505e73acb"
    ResourceCloudVpcId = "d50f04f3-bb01-4b31-9c09-ba46939308ab"
    [[BLBEnv.IDC_SPEC.testnew1.ResourceCloudSubnet]]
        Zone = "AZONE-gzns"
        SubnetId = "e12447e0-73e3-4b7a-8097-1472e6cc91b1"
    [[BLBEnv.IDC_SPEC.testnew1.ResourceCloudSubnet]]
        Zone = "AZONE-gzhxy"
        SubnetId = "ab34c1c9-7132-480d-9ed8-0dfc10728cd0"
