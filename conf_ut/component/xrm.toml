Region = "bj"
ResizeType = "combo"
VolumeType = "docker_bind"
VolumePath = "/home/<USER>"
# 内存考虑碎片情况下的配额申请，如果原始申请为10G，则该实例Quota为10 * MemoryRatioIncludeFrag
MemoryRatioIncludeFrag = 1.3
# CPU和Memory两种资源Limit除以Quota的比例，如果申请的cpu quota为1核，则该实例最大可用CPU为1*LimitMaxRatio
LimitMaxRatio = 2
DockerRootPath = "docker_root_path"
ScheduleStrategyMaxPerRack = 0
ScheduleStrategyMaxPerHost = 1
ScheduleStrategyPortType = "static"
ImageID = "registry.baidubce.com/scs_test_cce/scs:xagent1.0"