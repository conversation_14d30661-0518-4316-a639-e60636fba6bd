package main

import (
	"flag"
	"fmt"

	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
)

func main() {
	isDecryption := false
	flag.BoolVar(&isDecryption, "d", false, "do decrypting")
	keyLen := 0
	flag.IntVar(&keyLen, "n", 32, "key len")
	flag.Parse()

	oriKey := flag.Arg(0)

	if isDecryption {
		if oriKey == "" {
			fmt.Println("encrypted key needed")
			return
		}
		doDecrypting(oriKey)
	} else {
		if oriKey == "" {
			oriKey = crypto_utils.GenRandomKey(keyLen)
		}
		doEncrypting(oriKey)
	}
}

func doDecrypting(oriKey string) {
	decryptedKey, err := crypto_utils.DecryptKey(oriKey)
	if err != nil {
		fmt.Println("decrypt key fail,", err.Error())
		return
	}

	fmt.Println("[encrypted]", oriKey)
	fmt.Println("[original]", decryptedKey)
}

func doEncrypting(oriKey string) {
	encryptedKey, err := crypto_utils.EncryptKey(oriKey)
	if err != nil {
		fmt.Println("encrypt key fail,", err.Error())
		return
	}

	decryptedKey, err := crypto_utils.DecryptKey(encryptedKey)
	if err != nil {
		fmt.Println("decrypt key fail,", err.Error())
		return
	}

	if decryptedKey != oriKey {
		fmt.Println("internal error")
		return
	}

	fmt.Println("[original]", oriKey)
	fmt.Println("[encrypted]", encryptedKey)
}
