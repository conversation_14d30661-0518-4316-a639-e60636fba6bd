package main

import (
	"context"
	"flag"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/go-cmd/cmd"
	"log"
	"os"
	"time"
)

const dlvStartScript = `#!/usr/bin/env bash
CODE_DIR="${1}"
DEFAULT_PORT="${2}"
LOG_DEST="${CODE_DIR}/dlv.log"
LOG_OUTPUT="debugger,gdbwire,lldbout,debuglineerr,rpc,dap,fncall,minidump"
cd "${CODE_DIR}"
ps -ef | grep dlv | grep -v grep | grep "${LOG_DEST}" | awk '{print $2}' | xargs kill -9
dlv debug --headless --listen=:${DEFAULT_PORT} --api-version=2 --accept-multiclient --log --log-output="${LOG_OUTPUT}" --log-dest="${LOG_DEST}"
`

const buildScript = `#!/usr/bin/env bash
CODE_DIR="${1}"
TARGET="${2}"
START="${3}"
cd "${CODE_DIR}"
ps -ef | grep "${CODE_DIR}"/bin/"${TARGET}"  | grep -v grep | awk '{print $2}' | xargs kill -9
go mod download && go build -o "${CODE_DIR}/bin/${TARGET}"
nohup "${START}" &
`

type DlvStartRequest struct {
	CodeDir   string `json:"codeDir"`
	Token     string `json:"token"`
	DebugPort int    `json:"debugPort"`
}

type BuildRequest struct {
	CodeDir string `json:"codeDir"`
	Target  string `json:"target"`
	Start   string `json:"start"`
	Token   string `json:"token"`
}

func pathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

func dumpScript(filename string, script string) error {
	if exist, _ := pathExists(filename); exist {
		_ = os.Remove(filename)
	}
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()
	_, err = file.WriteString(script)
	if err != nil {
		return err
	}
	return nil
}

func executeDlv(ctx context.Context, codeDir string, debugPort int) error {
	if err := dumpScript("dbgserver_dlv.sh", dlvStartScript); err != nil {
		return err
	}
	executor := cmd.NewCmd("sh", "dbgserver_dlvstart.sh", codeDir, fmt.Sprintf("%d", debugPort))
	_ = executor.Start()
	for {
		select {
		case <-ctx.Done():
			err := executor.Stop()
			if err != nil {
				return err
			}
			return ctx.Err()
		default:
			time.Sleep(10 * time.Second)
		}
	}
}

func executeBuild(ctx context.Context, codeDir string, target string, start string) error {
	if err := dumpScript("dbgserver_build.sh", buildScript); err != nil {
		return err
	}
	executor := cmd.NewCmd("sh", "dbgserver_build.sh", codeDir, target, start)
	_ = executor.Start()
	for {
		select {
		case <-ctx.Done():
			err := executor.Stop()
			if err != nil {
				return err
			}
			return ctx.Err()
		default:
			time.Sleep(10 * time.Second)
		}
	}
}

func main() {
	var port int
	flag.IntVar(&port, "port", 8344, "port")
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	server := gin.Default()
	server.POST("/start", func(c *gin.Context) {
		var request DlvStartRequest
		if err := c.ShouldBindJSON(&request); err != nil {
			c.JSON(400, gin.H{
				"error": err.Error(),
			})
			return
		}
		if request.Token != "1dcf76295aeb9104e48af8025f78ea1d" {
			c.JSON(403, gin.H{
				"error": "token error",
			})
			return
		}
		go executeDlv(ctx, request.CodeDir, request.DebugPort)
		c.JSON(200, gin.H{"message": "ok"})
		return
	})
	server.POST("/build", func(c *gin.Context) {
		var request BuildRequest
		if err := c.ShouldBindJSON(&request); err != nil {
			c.JSON(400, gin.H{
				"error": err.Error(),
			})
			return
		}
		if request.Token != "1dcf76295aeb9104e48af8025f78ea1d" {
			c.JSON(403, gin.H{
				"error": "token error",
			})
			return
		}
		go executeBuild(ctx, request.CodeDir, request.Target, request.Start)
		c.JSON(200, gin.H{"message": "ok"})
		return
	})
	err := server.Run(fmt.Sprintf(":%d", port))
	if err != nil {
		log.Fatalf("failed to run server: %v", err)
		return
	}
}
