/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/04/25
 * File: build_cyx.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package middleware TODO package function desc
package middleware

import (
	"context"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/utils/api_utils/format_resp"
)

func BuildEnvCtxMiddleWareFunc() ghttp.MiddleWareFunc {
	return func(ctx context.Context, w ghttp.Writer, req ghttp.Request, next ghttp.MiddleWareQueue) bool {
		// todo随后续开发注入需要的变量，可以参考 x1-base/model/csmaster/utils.go
		requestId := req.HeaderDefault("x-bce-request-id", "no-input-request-id")
		ctx = format_resp.AddRequesetId(ctx, requestId)

		fields := map[string]string{
			"requestId":          requestId,
			"reqTimestamp":       req.HeaderDefault("timestamp", ""),
			"token":              req.HeaderDefault("token", ""),
			"action":             req.QueryDefault("action", ""),
			"cacheClusterShowId": req.QueryDefault("cacheClusterShowId", ""),
			"cacheClusterId":     req.QueryDefault("cacheClusterId", ""),
			"groupId":            req.QueryDefault("groupId", ""),
		}
		logit.SetLogID(ctx, requestId)

		for key, val := range fields {
			logit.AddAllLevel(ctx, logit.String(key, val))
		}

		return next.Next(ctx, w, req)
	}
}
