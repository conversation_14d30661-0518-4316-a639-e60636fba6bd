/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/04/24
 * File: api_auth.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package middleware TODO package function desc
package middleware

import (
	"context"
	"strings"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	dbstackSsoSdk "icode.baidu.com/baidu/scs/x1-base/privatecloud/dbstack/iam/sdk"
	"icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/sdk/iam"
	"icode.baidu.com/baidu/scs/x1-base/utils/api_utils/format_resp"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const (
	PrivateCloudTokenPrefix = "scs-private|2a5d355806e24876ab025d14529d26e5"
)

func NewAuthMiddleWareFunc(logger logit.Logger, supportSubAccount bool) ghttp.MiddleWareFunc {
	return newAuthMiddleWareFuncCore(logger, supportSubAccount, false)
}

func NewAuthMiddleWareFuncWithSsoGroup(logger logit.Logger, supportSubAccount bool) ghttp.MiddleWareFunc {
	return newAuthMiddleWareFuncCore(logger, supportSubAccount, true)
}

// NewAuthMiddleWareFunc 返回验签中间件
// 对应csmaster中的api_prepare验签步骤 BCCS_CSMASTER_ERROR_NO NewMasterServiceImpl::resolve_head_and_auth
func newAuthMiddleWareFuncCore(logger logit.Logger, supportSubAccount bool, useSsoGroup bool) ghttp.MiddleWareFunc {
	return func(ctx context.Context, w ghttp.Writer, req ghttp.Request, next ghttp.MiddleWareQueue) bool {
		authFailRsp := format_resp.FormatResp(ctx, format_resp.BceAccessDeniedErr, nil)

		requestId, hasRequestId := req.Header("x-bce-request-id")
		if !hasRequestId {
			if logger != nil {
				logger.Warning(ctx, "x-bce-request-id not exist in query string")
			}
			// 原代码里是warning级报错，因此重构时认为应该在此处报错
			_ = authFailRsp.WriteTo(w)
			return false
		}

		pToken, hasPToken := req.Header("x-auth-token")
		if !hasPToken && logger != nil {
			logger.Trace(ctx, requestId+" token is missing in header")
		}

		authorization, hasAuthorization := req.Header("Authorization")
		if !hasAuthorization && logger != nil {
			logger.Trace(ctx, requestId+" authorization is missing in header")
		}

		pSecurityToken, hasPSecurityToken := req.Header("x-bce-security-token")
		if !hasPSecurityToken && logger != nil {
			logger.Trace(ctx, requestId+" security_token is missing in header")
		}

		privateUserId, has := privateCloudIdentify(ctx, req.HeaderDefault("Cookie", ""),
			pToken, authorization, pSecurityToken, useSsoGroup)
		if has {
			return next.Next(csmaster.NewAuthedContext(ctx, privateUserId), w, req)
		}

		if !hasPToken && !hasAuthorization {
			if logger != nil {
				logger.Warning(ctx, requestId+"x-auth-token and authorization both missing in header")
			}
			_ = authFailRsp.WriteTo(w)
			return false
		}
		iamUserId := ""

		if !hasPToken || pToken == "" {
			head := req.HTTPRequest().Header.Clone()
			head.Add("host", req.HTTPRequest().Host)
			if supportSubAccount {
				head.Add("x-subuser-support", "true")
			}
			checkSignReq := iam.CheckSignatureRequest{
				TransactionId: requestId,
				Authorization: authorization,
				SessionToken:  pSecurityToken,
				HttpMethod:    req.HTTPRequest().Method,
				Uri:           req.Path(),
				Queries:       req.HTTPRequest().URL.Query(),
				Headers:       head,
			}
			checkSignRsp, err := iam.NewDefaultIamSdk().CheckSignature(ctx, &checkSignReq)
			if err != nil {
				if logger != nil {
					logger.Warning(ctx, "get iam_user_id in auth middleware failed",
						logit.String("req", base_utils.Format(checkSignReq)),
						logit.Error("err", err))
				}
				_ = authFailRsp.WriteTo(w)
				return false
			}
			if checkSignRsp.UserId == "" {
				if logger != nil {
					logger.Warning(ctx, "get iam_user_id in auth middleware failed, user id is empty",
						logit.String("req", base_utils.Format(checkSignReq)),
						logit.String("resp", base_utils.Format(checkSignRsp)))
				}
				_ = authFailRsp.WriteTo(w)
				return false
			}
			iamUserId = checkSignRsp.AccountId

			if logger != nil {
				logger.Trace(ctx, "get iam_user_id in auth middleware success",
					logit.String("req", base_utils.Format(checkSignReq)),
					logit.String("resp", base_utils.Format(checkSignRsp)))
			}
		} else {
			checkTokenReq := iam.GetUserIdByTokenRequest{
				TransactionId: requestId,
				UserToken:     pToken,
			}
			if logger != nil {
				logger.Trace(ctx, "check token in auth middleware", logit.String("req", base_utils.Format(checkTokenReq)))
			}
			checkTokenResp, err := iam.NewDefaultIamSdk().GetUserIdByToken(ctx, &checkTokenReq)
			if err != nil {
				if logger != nil {
					logger.Warning(ctx, "check token in auth middleware failed",
						logit.String("req", base_utils.Format(checkTokenReq)),
						logit.Error("err", err))
				}
				_ = authFailRsp.WriteTo(w)
				return false
			}
			if checkTokenResp.UserId == "" {
				if logger != nil {
					logger.Warning(ctx, "check token in auth middleware failed, user id is empty",
						logit.String("req", base_utils.Format(checkTokenReq)),
						logit.String("resp", base_utils.Format(checkTokenResp)))
				}
				_ = authFailRsp.WriteTo(w)
				return false
			}
			iamUserId = checkTokenResp.UserId

			logger.Trace(ctx, "check token in auth middleware success",
				logit.String("req", base_utils.Format(checkTokenReq)),
				logit.String("resp", base_utils.Format(checkTokenResp)))
		}
		logger.Trace(ctx, "auth midware suc, build new ctx and go on", logit.String("iamuserid", iamUserId))

		return next.Next(csmaster.NewAuthedContext(ctx, iamUserId), w, req)
	}
}

func privateCloudIdentify(ctx context.Context, cookies, pToken,
	authorization string, pSecurityToekn string, useSsoGroup bool) (string, bool) {
	if privatecloud.IsPrivateENV() &&
		privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix &&
		len(cookies) > 0 {
		ssoUserId, err := getUserIDFromSSO(ctx, cookies, useSsoGroup)
		if err == nil {
			return ssoUserId, true
		}
	}
	if len(pToken) > 0 && strings.HasPrefix(pToken, PrivateCloudTokenPrefix) {
		userId := getIamUserIDForPrivateCloud(pToken)
		return userId, true
	}
	if len(authorization) > 0 && strings.HasPrefix(authorization, PrivateCloudTokenPrefix) {
		userId := getIamUserIDForPrivateCloud(authorization)
		return userId, true
	}
	if len(pSecurityToekn) > 0 && strings.HasPrefix(pSecurityToekn, PrivateCloudTokenPrefix) {
		userId := getIamUserIDForPrivateCloud(pSecurityToekn)
		return userId, true
	}
	return "", false
}

func getIamUserIDForPrivateCloud(authToken string) (userID string) {
	tokens := strings.Split(authToken, "|")
	if len(tokens) == 3 {
		userID = tokens[2]
	}
	return
}

func getUserIDFromSSO(ctx context.Context, cookies string, useSsoGroup bool) (string, error) {
	checkReq := &dbstackSsoSdk.CheckRequest{
		Token: getCheckString(cookies),
	}
	checkResp, err := dbstackSsoSdk.NewDefaultSdk().Check(ctx, checkReq)
	if err != nil {
		return "", err
	}
	if useSsoGroup && len(checkResp.Result.GroupList) > 0 {
		return checkResp.Result.GroupList[0].GroupId, nil
	}
	return checkResp.Result.UserName, nil
}

func getCheckString(cookies string) string {
	cookieMap := make(map[string]string)
	cookieItems := strings.Split(cookies, ";")
	for _, item := range cookieItems {
		cookie := strings.Split(item, "=")
		if len(cookie) == 2 {
			cookieMap[strings.TrimSpace(cookie[0])] = cookie[1]
		}
	}
	ssoToken, has := cookieMap["BCE_SSO"]
	if !has {
		return ""
	}
	return ssoToken
}
