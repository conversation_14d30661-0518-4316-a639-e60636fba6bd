package middleware

import (
	"context"
	"icode.baidu.com/baidu/gdp/env"
	"testing"
)

func Test_privateCloudIdentify(t *testing.T) {
	type args struct {
		ctx           context.Context
		cookies       string
		pToken        string
		authorization string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 bool
	}{
		{
			name: "case1",
			args: args{
				ctx:           context.Background(),
				cookies:       "",
				pToken:        "",
				authorization: "",
			},
			want:  "",
			want1: false,
		},
		{
			name: "case2",
			args: args{
				ctx:           context.Background(),
				cookies:       "invalid-cookie",
				pToken:        "",
				authorization: "",
			},
			want:  "",
			want1: false,
		},
		{
			name: "case3",
			args: args{
				ctx:           context.Background(),
				cookies:       "",
				pToken:        "scs-private|2a5d355806e24876ab025d14529d26e5|admin",
				authorization: "",
			},
			want:  "admin",
			want1: true,
		},
		{
			name: "case4",
			args: args{
				ctx:           context.Background(),
				cookies:       "",
				pToken:        "",
				authorization: "scs-private|2a5d355806e24876ab025d14529d26e5|admin",
			},
			want:  "admin",
			want1: true,
		},
		{
			name: "case4",
			args: args{
				ctx: context.Background(),
				cookies: "BCE_SSO=2dd95660f9014fb286764601aaa6d16b|781928739813457920; " +
					"BCE_SSO_REPLICA=2dd95660f9014fb286764601aaa6d16b|781928739813457920",
				pToken:        "",
				authorization: "",
			},
			want:  "",
			want1: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env.Default = env.New(env.Option{
				IDC: "dbstacktest",
			})
			got, got1 := privateCloudIdentify(tt.args.ctx, tt.args.cookies, tt.args.pToken, tt.args.authorization, "", false)
			if got != tt.want {
				t.Errorf("privateCloudIdentify() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("privateCloudIdentify() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_getCheckString(t *testing.T) {
	type args struct {
		cookies string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "case1",
			args: args{
				cookies: "Hm_lvt_5590ff9e1a9aec85e8e45b66baf9eb36=1724393080,1724638433; RT=\"z=1&dm=test.com&si=7d7cc20" +
					"d-3015-4da5-9ad2-d75033d4ca47&ss=m0asjnxm&sl=14&tt=so5&bcn=https%3A%2F%2Ffclog.baidu.com%2Flog%2Fwei" +
					"rwood%3Ftype%3Dperf&ld=juzk\"; BCE_SSO=283590922e2544119131bf5fc88a29bf|*******39813457920; Hm_lpvt_55" +
					"90ff9e1a9aec85e8e45b66baf9eb36=1724665689",
			},
			want: "283590922e2544119131bf5fc88a29bf|*******39813457920",
		},
		{
			name: "case2",
			args: args{
				cookies: "BCE_SSO=2dd95660f9014fb286764601aaa6d16b|781928739813457920;" +
					"BCE_SSO_REPLICA=2dd95660f9014fb286764601aaa6d16b|781928739813457920",
			},
			want: "2dd95660f9014fb286764601aaa6d16b|781928739813457920",
		},
		{
			name: "case3",
			args: args{
				cookies: "BCE_SSO_sdfasdf=2dd95660f9014fb286764601aaa6d16b|781928739813457920;" +
					"BCE_SSO_REPLICA=2dd95660f9014fb286764601aaa6d16b|781928739813457920",
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getCheckString(tt.args.cookies); got != tt.want {
				t.Errorf("getCheckString() = %v, want %v", got, tt.want)
			}
		})
	}
}
