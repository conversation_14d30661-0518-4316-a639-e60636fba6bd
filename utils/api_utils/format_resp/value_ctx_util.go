/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/04/24
 * File: value_ctx_util.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package common TODO package function desc
package format_resp

import "context"

type ctxRequestIdKey string

var requestIdKey ctxRequestIdKey

func AddRequesetId(ctx context.Context, id string) context.Context {
	return context.WithValue(ctx, requestIdKey, id)
}

func GetRequesetId(ctx context.Context) (string, bool) {
	u, ok := ctx.Value(requestIdKey).(string)
	return u, ok
}
