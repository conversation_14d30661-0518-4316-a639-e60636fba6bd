/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/04/24
 * File: api_error.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package common TODO package function desc
package format_resp

import (
	"context"
)

const (
	HTTP_OK                    = 200
	HTTP_CREATED               = 201
	HTTP_ACCEPTED              = 202
	HTTP_BAD_REQUEST           = 400
	HTTP_UNAUTHORIZED          = 401
	HTTP_FORBIDDEN             = 403
	HTTP_NOT_FOUND             = 404
	HTTP_METHOD_NOT_ALLOWED    = 405
	HTTP_REQUEST_TIMEOUT       = 408
	HTTP_CONFLICT              = 409
	HTTP_INTERNAL_SERVER_ERROR = 500
)

const (
	BceInternalErrorMsg = "We encountered an internal error Please try again."
)

// 内置的报错，如果没有的话可以在这里添加或者在使用的地方用CreateServerError()方法创建
var (

	// BCE 公共错误码 https://cloud.baidu.com/doc/SCS/s/Yjwvxtsti
	SuccessErr = CreateServerError(HTTP_OK, "SUCCESS", "SUCCESS")

	// BceAccessDeniedErr 无权限访问对应的资源。
	BceAccessDeniedErr = CreateServerError(HTTP_FORBIDDEN, "AccessDenied",
		"Access denied.")

	// BceInappropriateJSONErr 请求中的JSON格式正确，但语义上不符合要求。
	// 如缺少某个必需项，或值类型不匹配等。出于兼容性考虑，对于所有无法识别的项应直接忽略，不应该返回这个错误。
	BceInappropriateJSONErr = CreateServerError(HTTP_BAD_REQUEST, "InappropriateJSON",
		"The JSON you provided was well-formed and valid, but not appropriate forthis operation.")

	// BceInternalErrorErr 所有未定义的其他错误。
	// 在有明确对应的其他类型的错误时（包括通用的和服务自定义的）不应该使用。
	BceInternalErrorErr = CreateServerError(HTTP_INTERNAL_SERVER_ERROR, "InternalError",
		"We encountered an internal error Please try again.")

	// BceInvalidAccessKeyIdErr Access Key ID不存在。
	BceInvalidAccessKeyIdErr = CreateServerError(HTTP_FORBIDDEN, "InvalidAccessKeyId",
		"The Access Key ID you provided doesnot exist in our records.")

	// BceInvalidHTTPAuthHeaderErr Authorization头域格式错误。
	BceInvalidHTTPAuthHeaderErr = CreateServerError(HTTP_BAD_REQUEST, "InvalidHTTPAuthHeader",
		"The Access Key ID you provided does notexist in our records.")

	// BceInvalidHTTPRequestErr HTTP body格式错误。例如不符合指定的Encoding等。
	BceInvalidHTTPRequestErr = CreateServerError(HTTP_BAD_REQUEST, "InvalidHTTPRequest",
		"There was an error in the body of your HTTP request.")

	// BceInvalidURIErr URI形式不正确。例如一些服务定义的关键词不匹配等。
	// 对于ID不匹配的问题，应定义更加具体的错误码，如NoSuchKey。
	BceInvalidURIErr = CreateServerError(HTTP_BAD_REQUEST, "InvalidURI",
		"Could not parse the specified URI.")

	// BceMalformedJSONErr JSON格式不合法。
	BceMalformedJSONErr = CreateServerError(HTTP_BAD_REQUEST, "MalformedJSON",
		"The JSON you provided was not well-formed.")

	// BceInvalidVersionErr URI的版本号不合法。
	BceInvalidVersionErr = CreateServerError(HTTP_NOT_FOUND, "InvalidVersion",
		"The API version specified was invalid.")

	// BceOptInRequiredErr 没有开通对应的服务。
	BceOptInRequiredErr = CreateServerError(HTTP_FORBIDDEN, "OptInRequired",
		"A subscription for the service is required.")

	// BcePreconditionFailedErr 详见Etag。
	BcePreconditionFailedErr = CreateServerError(412, "PreconditionFailed",
		"The specified If-Match header doesn’tmatch the ETag header.")

	// BceRequestExpiredErr 请求超时。要改成x-bce-date。若请求中只有Date，需将Date转成datetime。
	BceRequestExpiredErr = CreateServerError(HTTP_BAD_REQUEST, "RequestExpired",
		"Request has expired. ")

	// BceIdempotentParameterMismatchErr clientToken对应的API参数不一样。
	BceIdempotentParameterMismatchErr = CreateServerError(HTTP_FORBIDDEN, "IdempotentParameterMismatch",
		"The request uses the same client token asa previous, but non-identical request.")

	// BceSignatureDoesNotMatchErr Authorization头域中附带的签名和服务端验证不一致。
	BceSignatureDoesNotMatchErr = CreateServerError(HTTP_BAD_REQUEST, "SignatureDoesNotMatch",
		"The request signature we calculated does not match the signature you provided. Check yourSecret Access Key and signing method. Consultthe service documentation for details.")

	// BceForbidWriteFailAndNeedManualErr
	BceForbidWriteFailAndNeedManualErr = CreateServerError(HTTP_INTERNAL_SERVER_ERROR, "ForbidWriteFailAndNeedManual",
		"forbid write before switch master fail and need manual.")

	// todo 在这里继续新增csmaster特有的
)

type ApiError struct {
	HttpCode int    // http状态码
	Code     string // 错误码
	ErrMsg   string // 错误信息
}

func (a ApiError) Error() string {
	return a.ErrMsg
}

func CreateServerError(httpCode int, code string, message string) ApiError {
	ae := ApiError{
		HttpCode: httpCode,
		Code:     code,
		ErrMsg:   message,
	}
	return ae
}

type ApiErrData struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
}

func buildApiErrData(ctx context.Context, ae ApiError, insertData interface{}) map[string]interface{} {
	// todo 可以根据一些特殊错误拼装msg
	ret := make(map[string]interface{}, 0)
	if data, ok := insertData.(map[string]interface{}); ok {
		if data != nil {
			ret = data
		}
	}
	ret["code"] = ae.Code
	ret["message"] = ae.ErrMsg
	requestId, _ := GetRequesetId(ctx)
	ret["requestId"] = requestId
	return ret
}
