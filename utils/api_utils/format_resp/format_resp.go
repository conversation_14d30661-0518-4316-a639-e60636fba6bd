/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/04/24
 * File: format_resp.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package common TODO package function desc
package format_resp

import (
	"context"
	"encoding/json"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"github.com/pkg/errors"
)

// FormatRespWithMsg
// 结合日常业务迭代更新的可以定制内容的返回
func FormatRespWithMsg(ctx context.Context, err error, respDataStruct interface{}, message string) *ghttp.JSONResponse {
	if err == nil {
		err = SuccessErr
	}
	ae, ok := errors.Cause(err).(ApiError)
	if !ok {
		ae = BceInternalErrorErr
	}
	return ghttp.NewJSONResponse(ae.HttpCode, buildApiErrDataForStruct(ctx, ae, respDataStruct, message))
}

// FormatRespForStruct 纯净版返回成json resp
// data为struct时候用这个，结果根据struct的json tag封装，会自动拼接上openapi标准字段(code,message,requestid)
func FormatRespForStruct(ctx context.Context, err error, data interface{}) *ghttp.JSONResponse {
	if err == nil {
		err = SuccessErr
	}
	if ae, ok := errors.Cause(err).(ApiError); ok {
		return ghttp.NewJSONResponse(ae.HttpCode, buildApiErrDataForStruct(ctx, ae, data, ""))
	}
	return ghttp.NewJSONResponse(BceInternalErrorErr.HttpCode, buildApiErrDataForStruct(ctx, BceInternalErrorErr, data, ""))
}

// FormatResp 通用openapi响应封装
// data为map[string]interface{} 用这个，会自动拼接上openapi标准字段(code,message,requestid)
// err为api_error.go中定义的或者用CreateServerError()方法创建的，其他err均认为未知并返回500
func FormatResp(ctx context.Context, err error, data interface{}) ghttp.Response {
	if err == nil {
		err = SuccessErr
	}
	if ae, ok := errors.Cause(err).(ApiError); ok {
		return FormatResponseWithHttpCode(ctx, ae.HttpCode, buildApiErrData(ctx, ae, data))
	}
	return FormatResponseWithHttpCode(ctx, BceInternalErrorErr.HttpCode, buildApiErrData(ctx, BceInternalErrorErr, data))
}

func FormatResponseWithHttpCode(ctx context.Context, httpCode int, rspData interface{}) *ghttp.JSONResponse {
	var response *ghttp.JSONResponse
	requestId, _ := GetRequesetId(ctx)
	defaultRsp := map[string]interface{}{
		"code":      "Success",
		"message":   "Success",
		"requestId": requestId,
	}
	if value, ok := rspData.(map[string]interface{}); ok && value != nil {
		for k, v := range value {
			defaultRsp[k] = v
		}
		response = ghttp.NewJSONResponse(httpCode, defaultRsp)
	} else {
		// 其他格式，直接返回
		response = ghttp.NewJSONResponse(httpCode, defaultRsp)
	}

	return response
}

// message>insertData里面的message字段>ae里面的默认的message
func buildApiErrDataForStruct(ctx context.Context, ae ApiError, insertData interface{}, message string) map[string]interface{} {

	mapData := make(map[string]interface{}, 0)
	ret := make(map[string]interface{}, 0)
	ret["code"] = ae.Code
	ret["message"] = ae.ErrMsg
	requestId, _ := GetRequesetId(ctx)
	ret["requestId"] = requestId

	if insertData != nil {
		jsonData, err := json.Marshal(insertData)
		if err != nil {
			logger.DefaultLogger.Error(ctx, "marshal data fail,err:%s", base_utils.Format(err))
			ret["code"] = BceInternalErrorErr.Code
			ret["message"] = BceInternalErrorErr.ErrMsg
		} else {
			err = json.Unmarshal(jsonData, &mapData)
			if err != nil {
				logger.DefaultLogger.Error(ctx, "unmarshal data fail,err:%s", base_utils.Format(err))
				ret["code"] = BceInternalErrorErr.Code
				ret["message"] = BceInternalErrorErr.ErrMsg
			} else {
				for k, v := range mapData {
					ret[k] = v
				}
			}
		}
	}
	if message != "" {
		ret["message"] = message
	}
	return ret
}
