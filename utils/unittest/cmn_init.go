/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/11/30, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
单测通用的init方法

*/

package unittest

import (
	"context"
	"log"
	"os"
	"path/filepath"

	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/baidu/gdp/net/servicer"
)

// UnitTestInit 单测初始化
// 使用代码根目录下conf_ut中的配置; 配置的组织方式与GDP框架的组织方式相同;
// servicer配置过的下游，均可通过Name调用
// conf_ut
// ├── app.toml
// ├── logit
// │   ├── ral.toml
// │   └── ral-worker.toml
// ├── port.conf
// └── servicer    // 所有ral下游配置
//     └── broker-redis.toml
func UnitTestInit(deps int) {
	type Config struct {
		AppName string
		IDC     string
		RunMode string
	}
	dp := ""
	for i := 0; i < deps; i++ {
		dp = "../" + dp
	}
	confPath, err := filepath.Abs(dp + "conf_ut/app.toml")
	if err != nil {
		panic(err.Error())
	}
	var c *Config
	if err := conf.Parse(confPath, &c); err != nil {
		panic(err.Error())
	}
	rootDir := filepath.Dir(filepath.Dir(confPath))
	opt := env.Option{
		AppName: c.AppName,
		IDC:     c.IDC,
		RunMode: c.RunMode,
		RootDir: rootDir,
		DataDir: filepath.Join(rootDir, "data_ut"),
		LogDir:  filepath.Join(rootDir, "log_ut"),
		ConfDir: filepath.Join(rootDir, filepath.Base(filepath.Dir(confPath))),
	}
	os.Mkdir(opt.LogDir, os.ModePerm)
	// GDP的env包初始化时，直接使用log.Output在标准输出打印日志；
	// 为了单测输出的整洁，将这些日志输出到文件中
	lf, err := os.OpenFile(filepath.Join(opt.LogDir, "stdlog.log"), os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0666)
	if err != nil {
		panic(err)
	}
	log.SetOutput(lf)

	// 初始化env中参数
	env.Default = env.New(opt)

	// 初始化ral; 单测时ral日志输出的配置在conf_ut/logit中
	ral.InitDefault(context.Background())
	// 初始化service
	pattern := filepath.Join(env.ConfDir(), "servicer", "*.toml")
	servicer.MustLoad(context.Background(), servicer.LoadOptFilesGlob(pattern, false))
}
