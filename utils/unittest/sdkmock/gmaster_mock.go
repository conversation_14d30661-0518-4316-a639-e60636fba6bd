// Code generated by MockGen. DO NOT EDIT.
// Source: ../../../sdk/gmaster/interface.go

// Package mock_gmaster is a generated GoMock package.
package sdkmock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	gmaster "icode.baidu.com/baidu/scs/x1-base/sdk/gmaster"
)

// MockGmasterService is a mock of GmasterService interface.
type MockGmasterService struct {
	ctrl     *gomock.Controller
	recorder *MockGmasterServiceMockRecorder
}

func (m *MockGmasterService) InitStandaloneTopo(ctx context.Context, req *gmaster.InitStandaloneTopoReq) (err error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockGmasterService) UpdateBnsService(ctx context.Context, req *gmaster.UpdateBnsServiceRequest) (resp *gmaster.UpdateBnsServiceResponse, err error) {
	//TODO implement me
	panic("implement me")
}

// MockGmasterServiceMockRecorder is the mock recorder for MockGmasterService.
type MockGmasterServiceMockRecorder struct {
	mock *MockGmasterService
}

// NewMockGmasterService creates a new mock instance.
func NewMockGmasterService(ctrl *gomock.Controller) *MockGmasterService {
	mock := &MockGmasterService{ctrl: ctrl}
	mock.recorder = &MockGmasterServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGmasterService) EXPECT() *MockGmasterServiceMockRecorder {
	return m.recorder
}

// AddModifyStatus mocks base method.
func (m *MockGmasterService) AddModifyStatus(ctx context.Context, req *gmaster.AddModifyStatusRequest) (*gmaster.AddModifyStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddModifyStatus", ctx, req)
	ret0, _ := ret[0].(*gmaster.AddModifyStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddModifyStatus indicates an expected call of AddModifyStatus.
func (mr *MockGmasterServiceMockRecorder) AddModifyStatus(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddModifyStatus", reflect.TypeOf((*MockGmasterService)(nil).AddModifyStatus), ctx, req)
}

// AddNodes mocks base method.
func (m *MockGmasterService) AddNodes(ctx context.Context, req *gmaster.AddRedisRequest) (*gmaster.AddRedisResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddNodes", ctx, req)
	ret0, _ := ret[0].(*gmaster.AddRedisResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddNodes indicates an expected call of AddNodes.
func (mr *MockGmasterServiceMockRecorder) AddNodes(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNodes", reflect.TypeOf((*MockGmasterService)(nil).AddNodes), ctx, req)
}

// AddProxies mocks base method.
func (m *MockGmasterService) AddProxies(ctx context.Context, req *gmaster.AddProxyRequest) (*gmaster.AddProxyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddProxies", ctx, req)
	ret0, _ := ret[0].(*gmaster.AddProxyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddProxies indicates an expected call of AddProxies.
func (mr *MockGmasterServiceMockRecorder) AddProxies(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddProxies", reflect.TypeOf((*MockGmasterService)(nil).AddProxies), ctx, req)
}

// AddShards mocks base method.
func (m *MockGmasterService) AddShards(ctx context.Context, req *gmaster.AddShardRequest) (*gmaster.AddShardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddShards", ctx, req)
	ret0, _ := ret[0].(*gmaster.AddShardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddShards indicates an expected call of AddShards.
func (mr *MockGmasterServiceMockRecorder) AddShards(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddShards", reflect.TypeOf((*MockGmasterService)(nil).AddShards), ctx, req)
}

// ApplyTemplate mocks base method.
func (m *MockGmasterService) ApplyTemplate(ctx context.Context, req *gmaster.ApplyTemplateRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyTemplate", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// ApplyTemplate indicates an expected call of ApplyTemplate.
func (mr *MockGmasterServiceMockRecorder) ApplyTemplate(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyTemplate", reflect.TypeOf((*MockGmasterService)(nil).ApplyTemplate), ctx, req)
}

// DeleteNodes mocks base method.
func (m *MockGmasterService) DeleteNodes(ctx context.Context, req *gmaster.DeleteRedisRequest) (*gmaster.DeleteRedisResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNodes", ctx, req)
	ret0, _ := ret[0].(*gmaster.DeleteRedisResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteNodes indicates an expected call of DeleteNodes.
func (mr *MockGmasterServiceMockRecorder) DeleteNodes(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNodes", reflect.TypeOf((*MockGmasterService)(nil).DeleteNodes), ctx, req)
}

// DeleteProxies mocks base method.
func (m *MockGmasterService) DeleteProxies(ctx context.Context, req *gmaster.DeleteProxyRequest) (*gmaster.DeleteProxyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteProxies", ctx, req)
	ret0, _ := ret[0].(*gmaster.DeleteProxyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteProxies indicates an expected call of DeleteProxies.
func (mr *MockGmasterServiceMockRecorder) DeleteProxies(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteProxies", reflect.TypeOf((*MockGmasterService)(nil).DeleteProxies), ctx, req)
}

// DeleteShards mocks base method.
func (m *MockGmasterService) DeleteShards(ctx context.Context, req *gmaster.DeleteShardRequest) (*gmaster.DeleteShardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteShards", ctx, req)
	ret0, _ := ret[0].(*gmaster.DeleteShardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteShards indicates an expected call of DeleteShards.
func (mr *MockGmasterServiceMockRecorder) DeleteShards(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteShards", reflect.TypeOf((*MockGmasterService)(nil).DeleteShards), ctx, req)
}

// GetModifyStatus mocks base method.
func (m *MockGmasterService) GetModifyStatus(ctx context.Context, req *gmaster.GetModifyStatusRequest) (*gmaster.GetModifyStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetModifyStatus", ctx, req)
	ret0, _ := ret[0].(*gmaster.GetModifyStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetModifyStatus indicates an expected call of GetModifyStatus.
func (mr *MockGmasterServiceMockRecorder) GetModifyStatus(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetModifyStatus", reflect.TypeOf((*MockGmasterService)(nil).GetModifyStatus), ctx, req)
}

// GetNodes mocks base method.
func (m *MockGmasterService) GetNodes(ctx context.Context, req *gmaster.GetRedisRequest) (*gmaster.GetRedisResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodes", ctx, req)
	ret0, _ := ret[0].(*gmaster.GetRedisResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodes indicates an expected call of GetNodes.
func (mr *MockGmasterServiceMockRecorder) GetNodes(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodes", reflect.TypeOf((*MockGmasterService)(nil).GetNodes), ctx, req)
}

// GetProxies mocks base method.
func (m *MockGmasterService) GetProxies(ctx context.Context, req *gmaster.GetProxyRequest) (*gmaster.GetProxyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProxies", ctx, req)
	ret0, _ := ret[0].(*gmaster.GetProxyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProxies indicates an expected call of GetProxies.
func (mr *MockGmasterServiceMockRecorder) GetProxies(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProxies", reflect.TypeOf((*MockGmasterService)(nil).GetProxies), ctx, req)
}

// GetShards mocks base method.
func (m *MockGmasterService) GetShards(ctx context.Context, req *gmaster.GetShardRequest) (*gmaster.GetShardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShards", ctx, req)
	ret0, _ := ret[0].(*gmaster.GetShardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShards indicates an expected call of GetShards.
func (mr *MockGmasterServiceMockRecorder) GetShards(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShards", reflect.TypeOf((*MockGmasterService)(nil).GetShards), ctx, req)
}

// GroupStatusCAS mocks base method.
func (m *MockGmasterService) GroupStatusCAS(ctx context.Context, req *gmaster.GroupStatusCASRequest) (*gmaster.GroupStatusCASResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GroupStatusCAS", ctx, req)
	ret0, _ := ret[0].(*gmaster.GroupStatusCASResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GroupStatusCAS indicates an expected call of GroupStatusCAS.
func (mr *MockGmasterServiceMockRecorder) GroupStatusCAS(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GroupStatusCAS", reflect.TypeOf((*MockGmasterService)(nil).GroupStatusCAS), ctx, req)
}

// ListCacheGroupDetail mocks base method.
func (m *MockGmasterService) ListCacheGroupDetail(ctx context.Context, req *gmaster.ListCacheGroupDetailRequest) (*gmaster.ListCacheGroupDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCacheGroupDetail", ctx, req)
	ret0, _ := ret[0].(*gmaster.ListCacheGroupDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCacheGroupDetail indicates an expected call of ListCacheGroupDetail.
func (mr *MockGmasterServiceMockRecorder) ListCacheGroupDetail(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCacheGroupDetail", reflect.TypeOf((*MockGmasterService)(nil).ListCacheGroupDetail), ctx, req)
}

// LocalFailover mocks base method.
func (m *MockGmasterService) LocalFailover(ctx context.Context, req *gmaster.LocalFailoverRequest) (*gmaster.LocalFailoverResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LocalFailover", ctx, req)
	ret0, _ := ret[0].(*gmaster.LocalFailoverResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LocalFailover indicates an expected call of LocalFailover.
func (mr *MockGmasterServiceMockRecorder) LocalFailover(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LocalFailover", reflect.TypeOf((*MockGmasterService)(nil).LocalFailover), ctx, req)
}

// SlaveOfMaster mocks base method.
func (m *MockGmasterService) SlaveOfMaster(ctx context.Context, req *gmaster.SlaveOfMasterRequest) (*gmaster.SlaveOfMasterResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SlaveOfMaster", ctx, req)
	ret0, _ := ret[0].(*gmaster.SlaveOfMasterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SlaveOfMaster indicates an expected call of SlaveOfMaster.
func (mr *MockGmasterServiceMockRecorder) SlaveOfMaster(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SlaveOfMaster", reflect.TypeOf((*MockGmasterService)(nil).SlaveOfMaster), ctx, req)
}

// UpdateInnerSecurity mocks base method.
func (m *MockGmasterService) UpdateInnerSecurity(ctx context.Context, req *gmaster.UpdateInnerSecurityRequest) (*gmaster.UpdateInnerSecurityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInnerSecurity", ctx, req)
	ret0, _ := ret[0].(*gmaster.UpdateInnerSecurityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInnerSecurity indicates an expected call of UpdateInnerSecurity.
func (mr *MockGmasterServiceMockRecorder) UpdateInnerSecurity(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInnerSecurity", reflect.TypeOf((*MockGmasterService)(nil).UpdateInnerSecurity), ctx, req)
}

// UpdateModifyStatusStage mocks base method.
func (m *MockGmasterService) UpdateModifyStatusStage(ctx context.Context, req *gmaster.UpdateModifyStatusStageRequest) (*gmaster.UpdateModifyStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateModifyStatusStage", ctx, req)
	ret0, _ := ret[0].(*gmaster.UpdateModifyStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateModifyStatusStage indicates an expected call of UpdateModifyStatusStage.
func (mr *MockGmasterServiceMockRecorder) UpdateModifyStatusStage(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateModifyStatusStage", reflect.TypeOf((*MockGmasterService)(nil).UpdateModifyStatusStage), ctx, req)
}

// UpdateSecurity mocks base method.
func (m *MockGmasterService) UpdateSecurity(ctx context.Context, req *gmaster.UpdateInnerSecurityRequest) (*gmaster.UpdateInnerSecurityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSecurity", ctx, req)
	ret0, _ := ret[0].(*gmaster.UpdateInnerSecurityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSecurity indicates an expected call of UpdateSecurity.
func (mr *MockGmasterServiceMockRecorder) UpdateSecurity(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSecurity", reflect.TypeOf((*MockGmasterService)(nil).UpdateSecurity), ctx, req)
}
