/*
modification history
--------------------
2023/12/08, by wang<PERSON><PERSON>(<EMAIL>), create
2024/08/01, by wangbin<PERSON>(<EMAIL>), update
            add ShowServer
*/

/*
DESCRIPTION
Mock xrm sdk
*/

// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock_xrm is a generated GoMock package.
package sdkmock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	xrm "icode.baidu.com/baidu/scs/x1-base/sdk/xrm"
)

// MockXrmService is a mock of XrmService interface.
type MockXrmService struct {
	ctrl     *gomock.Controller
	recorder *MockXrmServiceMockRecorder
}

// MockXrmServiceMockRecorder is the mock recorder for MockXrmService.
type MockXrmServiceMockRecorder struct {
	mock *MockXrmService
}

// NewMockXrmService creates a new mock instance.
func NewMockXrmService(ctrl *gomock.Controller) *MockXrmService {
	mock := &MockXrmService{ctrl: ctrl}
	mock.recorder = &MockXrmServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockXrmService) EXPECT() *MockXrmServiceMockRecorder {
	return m.recorder
}

// CreateServer mocks base method.
func (m *MockXrmService) CreateServer(ctx context.Context, req *xrm.CreateServerRequest) (*xrm.CreateServerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateServer", ctx, req)
	ret0, _ := ret[0].(*xrm.CreateServerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateServer indicates an expected call of CreateServer.
func (mr *MockXrmServiceMockRecorder) CreateServer(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateServer", reflect.TypeOf((*MockXrmService)(nil).CreateServer), ctx, req)
}

// DeleteServer mocks base method.
func (m *MockXrmService) DeleteServer(ctx context.Context, req *xrm.DeleteServerRequest) (*xrm.DeleteServerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteServer", ctx, req)
	ret0, _ := ret[0].(*xrm.DeleteServerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteServer indicates an expected call of DeleteServer.
func (mr *MockXrmServiceMockRecorder) DeleteServer(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServer", reflect.TypeOf((*MockXrmService)(nil).DeleteServer), ctx, req)
}

// QueryServerCreationTask mocks base method.
func (m *MockXrmService) QueryServerCreationTask(ctx context.Context, req *xrm.QueryServerCreationTaskRequest) (*xrm.QueryServerCreationTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryServerCreationTask", ctx, req)
	ret0, _ := ret[0].(*xrm.QueryServerCreationTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryServerCreationTask indicates an expected call of QueryServerCreationTask.
func (mr *MockXrmServiceMockRecorder) QueryServerCreationTask(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryServerCreationTask", reflect.TypeOf((*MockXrmService)(nil).QueryServerCreationTask), ctx, req)
}

// UpdateServer mocks base method.
func (m *MockXrmService) UpdateServer(ctx context.Context, req *xrm.UpdateServerRequest) (*xrm.UpdateServerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateServer", ctx, req)
	ret0, _ := ret[0].(*xrm.UpdateServerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateServer indicates an expected call of UpdateServer.
func (mr *MockXrmServiceMockRecorder) UpdateServer(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateServer", reflect.TypeOf((*MockXrmService)(nil).UpdateServer), ctx, req)
}

// ShowServer mocks base method.
func (m *MockXrmService) ShowServer(ctx context.Context, req *xrm.ShowServerRequest) (*xrm.ShowServerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShowServer", ctx, req)
	ret0, _ := ret[0].(*xrm.ShowServerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShowServer indicates an expected call of ShowServer.
func (mr *MockXrmServiceMockRecorder) ShowServer(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShowServer", reflect.TypeOf((*MockXrmService)(nil).ShowServer), ctx, req)
}
