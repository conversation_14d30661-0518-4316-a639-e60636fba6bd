/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
Mock sts sdk
*/

package sdkmock

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
)

type MockStsSdk struct {
}

func (m *MockStsSdk) GetAssumeRole(ctx context.Context, req *sts.GetAssumeRoleRequest) (rsp *sts.GetAssumeRoleResponse, err error) {
	resp := &sts.GetAssumeRoleResponse{
		AccessKeyId:     "mock-ak",
		SecretAccessKey: "mock-sk",
		SessionToken:    "mock-token",
		Expiration:      "1800",
		UserId:          "mock-user",
		RoleId:          "mock-role",
		Token: &sts.Token{
			Id: "mock-token-id",
		},
	}
	return resp, nil
}

func (m *MockStsSdk) GetEncryptResourceAccountId(ctx context.Context) (rsp *sts.EncryptResourceAccountIdResponse, err error) {
	return &sts.EncryptResourceAccountIdResponse{
		EncryptAccountId: "mock-encrypt-id",
		ResourceAk:       "resource-ak",
	}, nil
}

func (m *MockStsSdk) GetOpenApiAuth(ctx context.Context, req *sts.GetOpenApiAuthRequest) (rsp *sts.GetOpenApiAuthResponse, err error) {
	return &sts.GetOpenApiAuthResponse{Auth: &common.Authentication{
		IamUserId:     "mock-user",
		TransactionId: "test",
		ResourceAccount: &common.ResourceAccount{
			ResourceAk:       "resource-ak",
			EncryptAccountId: "mock-encrypt-id",
		},
		Credential: &common.Credential{
			Ak:           "mock-ak",
			Sk:           "mock-sk",
			SessionToken: "mode-token",
		},
	}}, nil
}
