/*
modification history
--------------------
2024/08/09, by wang<PERSON><PERSON>(<EMAIL>), create
2025/03/21, by wangbin34(<EMAIL>), update
2025/03/31, by wangbin34(<EMAIL>), add QueryPointInTimeRestoreRange
2025/05/14, by wangbin34(<EMAIL>), add QueryBackupUsage
*/

// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock_dbrs is a generated GoMock package.
package sdkmock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	dbrs "icode.baidu.com/baidu/scs/x1-base/sdk/dbrs"
)

// MockDbrsService is a mock of DbrsService interface.
type MockDbrsService struct {
	ctrl     *gomock.Controller
	recorder *MockDbrsServiceMockRecorder
}

// MockDbrsServiceMockRecorder is the mock recorder for MockDbrsService.
type MockDbrsServiceMockRecorder struct {
	mock *MockDbrsService
}

// NewMockDbrsService creates a new mock instance.
func NewMockDbrsService(ctrl *gomock.Controller) *MockDbrsService {
	mock := &MockDbrsService{ctrl: ctrl}
	mock.recorder = &MockDbrsServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDbrsService) EXPECT() *MockDbrsServiceMockRecorder {
	return m.recorder
}

// CreateBackupPolicy mocks base method.
func (m *MockDbrsService) CreateBackupPolicy(ctx context.Context, req *dbrs.CreatePolicyRequest) (*dbrs.CommonPolicyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBackupPolicy", ctx, req)
	ret0, _ := ret[0].(*dbrs.CommonPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateBackupPolicy indicates an expected call of CreateBackupPolicy.
func (mr *MockDbrsServiceMockRecorder) CreateBackupPolicy(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBackupPolicy", reflect.TypeOf((*MockDbrsService)(nil).CreateBackupPolicy), ctx, req)
}

// CreateBackupRestore mocks base method.
func (m *MockDbrsService) CreateBackupRestore(ctx context.Context, req *dbrs.CreateRestoreRequest) (*dbrs.CreateRestoreResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBackupRestore", ctx, req)
	ret0, _ := ret[0].(*dbrs.CreateRestoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateBackupRestore indicates an expected call of CreateBackupRestore.
func (mr *MockDbrsServiceMockRecorder) CreateBackupRestore(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBackupRestore", reflect.TypeOf((*MockDbrsService)(nil).CreateBackupRestore), ctx, req)
}

// CreateBackupTask mocks base method.
func (m *MockDbrsService) CreateBackupTask(ctx context.Context, req *dbrs.CreateBackupTaskRequest) (*dbrs.CreateBackupTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBackupTask", ctx, req)
	ret0, _ := ret[0].(*dbrs.CreateBackupTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateBackupTask indicates an expected call of CreateBackupTask.
func (mr *MockDbrsServiceMockRecorder) CreateBackupTask(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBackupTask", reflect.TypeOf((*MockDbrsService)(nil).CreateBackupTask), ctx, req)
}

// CreatePointInTimeRestore mocks base method.
func (m *MockDbrsService) CreatePointInTimeRestore(ctx context.Context, req *dbrs.CreatePointInTimeRestoreRequest) (*dbrs.CreatePointInTimeRestoreResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePointInTimeRestore", ctx, req)
	ret0, _ := ret[0].(*dbrs.CreatePointInTimeRestoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePointInTimeRestore indicates an expected call of CreatePointInTimeRestore.
func (mr *MockDbrsServiceMockRecorder) CreatePointInTimeRestore(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePointInTimeRestore", reflect.TypeOf((*MockDbrsService)(nil).CreatePointInTimeRestore), ctx, req)
}

// DeleteBackupPolicy mocks base method.
func (m *MockDbrsService) DeleteBackupPolicy(ctx context.Context, req *dbrs.DeletePolicyRequest) (*dbrs.CommonPolicyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBackupPolicy", ctx, req)
	ret0, _ := ret[0].(*dbrs.CommonPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteBackupPolicy indicates an expected call of DeleteBackupPolicy.
func (mr *MockDbrsServiceMockRecorder) DeleteBackupPolicy(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBackupPolicy", reflect.TypeOf((*MockDbrsService)(nil).DeleteBackupPolicy), ctx, req)
}

// DeleteBackupRecord mocks base method.
func (m *MockDbrsService) DeleteBackupRecord(ctx context.Context, req *dbrs.DeleteBackupRecordRequest) (*dbrs.DeleteBackupRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBackupRecord", ctx, req)
	ret0, _ := ret[0].(*dbrs.DeleteBackupRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteBackupRecord indicates an expected call of DeleteBackupRecord.
func (mr *MockDbrsServiceMockRecorder) DeleteBackupRecord(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBackupRecord", reflect.TypeOf((*MockDbrsService)(nil).DeleteBackupRecord), ctx, req)
}

// ModifyBackupEncryptPolicy mocks base method.
func (m *MockDbrsService) ModifyBackupEncryptPolicy(ctx context.Context, req *dbrs.ModifyEncryptPolicyRequest) (*dbrs.CommonPolicyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyBackupEncryptPolicy", ctx, req)
	ret0, _ := ret[0].(*dbrs.CommonPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyBackupEncryptPolicy indicates an expected call of ModifyBackupEncryptPolicy.
func (mr *MockDbrsServiceMockRecorder) ModifyBackupEncryptPolicy(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyBackupEncryptPolicy", reflect.TypeOf((*MockDbrsService)(nil).ModifyBackupEncryptPolicy), ctx, req)
}

// ModifyBackupPolicy mocks base method.
func (m *MockDbrsService) ModifyBackupPolicy(ctx context.Context, req *dbrs.CommonPolicyRequest) (*dbrs.CommonPolicyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyBackupPolicy", ctx, req)
	ret0, _ := ret[0].(*dbrs.CommonPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyBackupPolicy indicates an expected call of ModifyBackupPolicy.
func (mr *MockDbrsServiceMockRecorder) ModifyBackupPolicy(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyBackupPolicy", reflect.TypeOf((*MockDbrsService)(nil).ModifyBackupPolicy), ctx, req)
}

// PrecheckPointInTimeRestore mocks base method.
func (m *MockDbrsService) PrecheckPointInTimeRestore(ctx context.Context, req *dbrs.PrecheckPointInTimeRestoreRequest) (*dbrs.PrecheckPointInTimeRestoreResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrecheckPointInTimeRestore", ctx, req)
	ret0, _ := ret[0].(*dbrs.PrecheckPointInTimeRestoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PrecheckPointInTimeRestore indicates an expected call of PrecheckPointInTimeRestore.
func (mr *MockDbrsServiceMockRecorder) PrecheckPointInTimeRestore(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrecheckPointInTimeRestore", reflect.TypeOf((*MockDbrsService)(nil).PrecheckPointInTimeRestore), ctx, req)
}

// QueryAppBackupDetailByAppBackupId mocks base method.
func (m *MockDbrsService) QueryAppBackupDetailByAppBackupId(ctx context.Context, req *dbrs.QueryAppBackupDetailRequest) (*dbrs.QueryAppBackupDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryAppBackupDetailByAppBackupId", ctx, req)
	ret0, _ := ret[0].(*dbrs.QueryAppBackupDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryAppBackupDetailByAppBackupId indicates an expected call of QueryAppBackupDetailByAppBackupId.
func (mr *MockDbrsServiceMockRecorder) QueryAppBackupDetailByAppBackupId(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryAppBackupDetailByAppBackupId", reflect.TypeOf((*MockDbrsService)(nil).QueryAppBackupDetailByAppBackupId), ctx, req)
}

// QueryBackupDetailByBackupId mocks base method.
func (m *MockDbrsService) QueryBackupDetailByBackupId(ctx context.Context, req *dbrs.QueryShardBackupDetailRequest) (*dbrs.QueryShardBackupDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryBackupDetailByBackupId", ctx, req)
	ret0, _ := ret[0].(*dbrs.QueryShardBackupDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryBackupDetailByBackupId indicates an expected call of QueryBackupDetailByBackupId.
func (mr *MockDbrsServiceMockRecorder) QueryBackupDetailByBackupId(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryBackupDetailByBackupId", reflect.TypeOf((*MockDbrsService)(nil).QueryBackupDetailByBackupId), ctx, req)
}

// QueryBackupList mocks base method.
func (m *MockDbrsService) QueryBackupList(ctx context.Context, req *dbrs.QueryBackupListRequest) (*dbrs.QueryBackupListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryBackupList", ctx, req)
	ret0, _ := ret[0].(*dbrs.QueryBackupListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryBackupList indicates an expected call of QueryBackupList.
func (mr *MockDbrsServiceMockRecorder) QueryBackupList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryBackupList", reflect.TypeOf((*MockDbrsService)(nil).QueryBackupList), ctx, req)
}

// QueryBackupPolicy mocks base method.
func (m *MockDbrsService) QueryBackupPolicy(ctx context.Context, req *dbrs.QueryPolicyRequest) (*dbrs.QueryPolicyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryBackupPolicy", ctx, req)
	ret0, _ := ret[0].(*dbrs.QueryPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryBackupPolicy indicates an expected call of QueryBackupPolicy.
func (mr *MockDbrsServiceMockRecorder) QueryBackupPolicy(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryBackupPolicy", reflect.TypeOf((*MockDbrsService)(nil).QueryBackupPolicy), ctx, req)
}

// QueryBackupRestore mocks base method.
func (m *MockDbrsService) QueryBackupRestore(ctx context.Context, req *dbrs.QueryRestoreRequest) (*dbrs.QueryRestoreResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryBackupRestore", ctx, req)
	ret0, _ := ret[0].(*dbrs.QueryRestoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryBackupRestore indicates an expected call of QueryBackupRestore.
func (mr *MockDbrsServiceMockRecorder) QueryBackupRestore(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryBackupRestore", reflect.TypeOf((*MockDbrsService)(nil).QueryBackupRestore), ctx, req)
}

// QueryPointInTimeRestore mocks base method.
func (m *MockDbrsService) QueryPointInTimeRestore(ctx context.Context, req *dbrs.QueryPointInTimeRestoreRequest) (*dbrs.QueryPointInTimeRestoreResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryPointInTimeRestore", ctx, req)
	ret0, _ := ret[0].(*dbrs.QueryPointInTimeRestoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryPointInTimeRestore indicates an expected call of QueryPointInTimeRestore.
func (mr *MockDbrsServiceMockRecorder) QueryPointInTimeRestore(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryPointInTimeRestore", reflect.TypeOf((*MockDbrsService)(nil).QueryPointInTimeRestore), ctx, req)
}

// QueryPointInTimeRestoreRange mocks base method.
func (m *MockDbrsService) QueryPointInTimeRestoreRange(ctx context.Context, req *dbrs.QueryPointInTimeRestoreRangeRequest) (*dbrs.QueryPointInTimeRestoreRangeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryPointInTimeRestoreRange", ctx, req)
	ret0, _ := ret[0].(*dbrs.QueryPointInTimeRestoreRangeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryPointInTimeRestoreRange indicates an expected call of QueryPointInTimeRestoreRange.
func (mr *MockDbrsServiceMockRecorder) QueryPointInTimeRestoreRange(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryPointInTimeRestoreRange", reflect.TypeOf((*MockDbrsService)(nil).QueryPointInTimeRestoreRange), ctx, req)
}

// QueryBackupUsage mocks base method.
func (m *MockDbrsService) QueryBackupUsage(ctx context.Context, req *dbrs.QueryBackupUsageRequest) (*dbrs.QueryBackupUsageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryBackupUsage", ctx, req)
	ret0, _ := ret[0].(*dbrs.QueryBackupUsageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryBackupUsage indicates an expected call of QueryBackupUsage.
func (mr *MockDbrsServiceMockRecorder) QueryBackupUsage(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryBackupUsage", reflect.TypeOf((*MockDbrsService)(nil).QueryBackupUsage), ctx, req)
}
