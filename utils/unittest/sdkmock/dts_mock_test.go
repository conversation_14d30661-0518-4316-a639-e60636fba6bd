package sdkmock

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"icode.baidu.com/baidu/scs/x1-base/sdk/dts"
)

// TestMockDTSServiceCreateTask TestMockDTSServiceCreateTask(t *testing.T) 函数用于对MockDTSService接口的CreateTask方法进行单元测试，
// 该方法返回一个*gomock.Call类型，表示预期调用次数和参数是否正确。
func TestMockDTSServiceCreateTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().CreateTask(gomock.Any(), gomock.Any()).Return(&dts.CreateTaskResponse{}, nil)

	ctx := context.Background()
	req := &dts.CreateTaskRequest{}
	resp, err := mock.CreateTask(ctx, req)
	if err != nil {
		t.Fatalf("CreateTask() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("CreateTask() resp is nil")
	}
}

// TestMockDTSServiceConfigTask TestMockDTSServiceConfigTask 是一个测试函数，用于测试MockDTSService的ConfigTask方法。
// 它使用gomock框架来创建一个控制器和mock对象，并验证调用ConfigTask时的返回值和错误信息。
// 参数t是*testing.T类型，表示当前测试用例。
func TestMockDTSServiceConfigTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().ConfigTask(gomock.Any(), gomock.Any()).Return(&dts.ConfigTaskResponse{}, nil)

	ctx := context.Background()
	req := &dts.ConfigTaskRequest{}
	resp, err := mock.ConfigTask(ctx, req)
	if err != nil {
		t.Fatalf("ConfigTask() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ConfigTask() resp is nil")
	}
}

// TestMockDTSServiceShowTask TestMockDTSServiceShowTask 是一个测试函数，用于测试MockDTSService的ShowTask方法。
// 它使用gomock来控制mock对象的行为，并验证了ShowTask返回值和错误信息是否符合预期。
// 参数t是*testing.T类型，表示当前测试用例；返回值没有。
func TestMockDTSServiceShowTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().ShowTask(gomock.Any(), gomock.Any()).Return(&dts.ShowTaskResponse{}, nil)

	ctx := context.Background()
	req := &dts.ShowTaskRequest{}
	resp, err := mock.ShowTask(ctx, req)
	if err != nil {
		t.Fatalf("ShowTask() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ShowTask() resp is nil")
	}
}

// TestMockDTSServiceLaunchTaskPrecheck TestMockDTSServiceLaunchTaskPrecheck 是一个测试函数，用于测试MockDTSService的LaunchTaskPrecheck方法。
// 它使用gomock框架来控制mock对象的行为，并验证了LaunchTaskPrecheck的正确性。
func TestMockDTSServiceLaunchTaskPrecheck(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().LaunchTaskPrecheck(gomock.Any(), gomock.Any()).Return(&dts.CommonResponse{}, nil)

	ctx := context.Background()
	req := &dts.CommonRequest{}
	resp, err := mock.LaunchTaskPrecheck(ctx, req)
	if err != nil {
		t.Fatalf("LaunchTaskPrecheck() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("LaunchTaskPrecheck() resp is nil")
	}
}

// TestMockDTSServiceShowTaskPrecheck 测试MockDTSServiceShowTaskPrecheck函数，该函数用于模拟DTSService接口的ShowTaskPrecheck方法
// 参数t：*testing.T类型，表示测试对象
// 返回值：无
func TestMockDTSServiceShowTaskPrecheck(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().ShowTaskPrecheck(gomock.Any(), gomock.Any()).Return(&dts.ShowTaskPrecheckResponse{}, nil)

	ctx := context.Background()
	req := &dts.CommonRequest{}
	resp, err := mock.ShowTaskPrecheck(ctx, req)
	if err != nil {
		t.Fatalf("ShowTaskPrecheck() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ShowTaskPrecheck() resp is nil")
	}
}

// TestMockDTSServiceSkipTaskPrecheck TestMockDTSServiceSkipTaskPrecheck 是一个测试函数，用于测试MockDTSService的SkipTaskPrecheck方法。
// 它使用gomock来控制mock对象的行为，并验证了SkipTaskPrecheck的返回值和错误情况。
func TestMockDTSServiceSkipTaskPrecheck(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().SkipTaskPrecheck(gomock.Any(), gomock.Any()).Return(&dts.CommonResponse{}, nil)

	ctx := context.Background()
	req := &dts.CommonRequest{}
	resp, err := mock.SkipTaskPrecheck(ctx, req)
	if err != nil {
		t.Fatalf("SkipTaskPrecheck() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("SkipTaskPrecheck() resp is nil")
	}
}

// TestMockDTSServiceStartTask TestMockDTSServiceStartTask 是一个测试函数，用于测试MockDTSService的StartTask方法。
// 它使用gomock框架来创建一个mock对象，并验证其StartTask方法的调用情况。
// 参数t是*testing.T类型，用于记录测试结果；
// 返回值是nil
func TestMockDTSServiceStartTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().StartTask(gomock.Any(), gomock.Any()).Return(&dts.CommonResponse{}, nil)

	ctx := context.Background()
	req := &dts.CommonRequest{}
	resp, err := mock.StartTask(ctx, req)
	if err != nil {
		t.Fatalf("StartTask() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("StartTask() resp is nil")
	}
}

// TestMockDTSServiceShutdownTask TestMockDTSServiceShutdownTask 是一个测试函数，用于测试MockDTSService的ShutdownTask方法。
// 它使用gomock来控制mock对象的行为，并验证了ShutdownTask方法的正确性。
// 参数t是*testing.T类型，用于记录测试结果；
// 返回值是无返回值
func TestMockDTSServiceShutdownTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().ShutdownTask(gomock.Any(), gomock.Any()).Return(&dts.CommonResponse{}, nil)

	ctx := context.Background()
	req := &dts.CommonRequest{}
	resp, err := mock.ShutdownTask(ctx, req)
	if err != nil {
		t.Fatalf("ShutdownTask() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ShutdownTask() resp is nil")
	}
}

// TestMockDTSServiceDeleteTask TestMockDTSServiceDeleteTask 是一个测试函数，用于测试MockDTSService的DeleteTask方法。
// 它使用gomock来控制mock对象的行为，并验证了DeleteTask的正确性。
// 参数t是*testing.T类型，表示当前测试用例。
func TestMockDTSServiceDeleteTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().DeleteTask(gomock.Any(), gomock.Any()).Return(&dts.CommonResponse{}, nil)

	ctx := context.Background()
	req := &dts.CommonRequest{}
	resp, err := mock.DeleteTask(ctx, req)
	if err != nil {
		t.Fatalf("DeleteTask() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("DeleteTask() resp is nil")
	}
}

// TestMockDTSServiceListTask TestMockDTSServiceListTask 是一个测试函数，用于测试MockDTSService的ListTask方法。
// 它使用gomock框架创建了一个控制器和一组预期调用，并在完成后断言了返回值和错误信息。
// 参数t是*testing.T类型，表示当前测试用例。
func TestMockDTSServiceListTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().ListTask(gomock.Any(), gomock.Any()).Return(&dts.ListTaskResponse{}, nil)

	ctx := context.Background()
	req := &dts.ListTaskRequest{}
	resp, err := mock.ListTask(ctx, req)
	if err != nil {
		t.Fatalf("ListTask() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ListTask() resp is nil")
	}
}

// TestMockDTSServiceCreateChecksum TestMockDTSServiceCreateChecksum 函数用于对MockDTSService接口的CreateChecksum方法进行单元测试，
// 该方法返回一个*dts.CreateChecksumResponse和error类型的结果。
func TestMockDTSServiceCreateChecksum(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().CreateChecksum(gomock.Any(), gomock.Any()).Return(&dts.CreateChecksumResponse{}, nil)

	ctx := context.Background()
	req := &dts.CreateChecksumRequest{}
	resp, err := mock.CreateChecksum(ctx, req)
	if err != nil {
		t.Fatalf("CreateChecksum() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("CreateChecksum() resp is nil")
	}
}

// TestMockDTSServiceConfigChecksum TestMockDTSServiceConfigChecksum 是一个测试函数，用于测试MockDTSService的ConfigChecksum方法。
// 它使用gomock框架来创建一个控制器和mock对象，并验证ConfigChecksum方法的调用情况。
// 参数t是*testing.T类型，表示当前正在运行的单元测试；返回值没有。
func TestMockDTSServiceConfigChecksum(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().ConfigChecksum(gomock.Any(), gomock.Any()).Return(&dts.CommonResponse{}, nil)

	ctx := context.Background()
	req := &dts.ConfigChecksumRequest{}
	resp, err := mock.ConfigChecksum(ctx, req)
	if err != nil {
		t.Fatalf("ConfigChecksum() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ConfigChecksum() resp is nil")
	}
}

// TestMockDTSServiceLaunchChecksumPrecheck 测试MockDTSServiceLaunchChecksumPrecheck函数，该函数用于模拟DTSService的LaunchChecksumPrecheck方法。
// 参数t是*testing.T类型，表示当前测试用例；返回值无。
func TestMockDTSServiceLaunchChecksumPrecheck(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().LaunchChecksumPrecheck(gomock.Any(), gomock.Any()).Return(&dts.CommonResponse{}, nil)

	ctx := context.Background()
	req := &dts.CommonRequest{}
	resp, err := mock.LaunchChecksumPrecheck(ctx, req)
	if err != nil {
		t.Fatalf("LaunchChecksumPrecheck() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("LaunchChecksumPrecheck() resp is nil")
	}
}

// TestMockDTSServiceStartChecksum TestMockDTSServiceStartChecksum 是一个测试函数，用于测试MockDTSService的StartChecksum方法。
// 它使用gomock框架来控制mock对象的行为，并验证了StartChecksum方法的返回值和错误情况。
// 参数t是*testing.T类型，表示当前测试用例；返回值没有。
func TestMockDTSServiceStartChecksum(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().StartChecksum(gomock.Any(), gomock.Any()).Return(&dts.CommonResponse{}, nil)

	ctx := context.Background()
	req := &dts.CommonRequest{}
	resp, err := mock.StartChecksum(ctx, req)
	if err != nil {
		t.Fatalf("StartChecksum() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("StartChecksum() resp is nil")
	}
}

// TestMockDTSServiceShutdownChecksum TestMockDTSServiceShutdownChecksum 是一个测试函数，用于测试MockDTSService的ShutdownChecksum方法。
// 它使用gomock来控制mock对象的行为，并验证了ShutdownChecksum的返回值和错误信息是否符合预期。
func TestMockDTSServiceShutdownChecksum(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().ShutdownChecksum(gomock.Any(), gomock.Any()).Return(&dts.CommonResponse{}, nil)

	ctx := context.Background()
	req := &dts.CommonRequest{}
	resp, err := mock.ShutdownChecksum(ctx, req)
	if err != nil {
		t.Fatalf("ShutdownChecksum() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ShutdownChecksum() resp is nil")
	}
}

// TestMockDTSServiceShowChecksum 测试MockDTSServiceShowChecksum函数，该函数用于模拟DTSService接口的ShowChecksum方法。
// 参数t是*testing.T类型，表示单元测试对象；返回值没有。
func TestMockDTSServiceShowChecksum(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockDTSService(ctrl)
	mock.EXPECT().ShowChecksum(gomock.Any(), gomock.Any()).Return(&dts.ShowChecksumResponse{}, nil)

	ctx := context.Background()
	req := &dts.CommonRequest{}
	resp, err := mock.ShowChecksum(ctx, req)
	if err != nil {
		t.Fatalf("ShowChecksum() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ShowChecksum() resp is nil")
	}
}
