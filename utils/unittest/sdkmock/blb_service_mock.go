// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock_elbv2 is a generated GoMock package.
package sdkmock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	elb_v2 "icode.baidu.com/baidu/scs/x1-base/sdk/elb_v2"
)

// MockELBPublishEndpointService is a mock of ELBPublishEndpointService interface.
type MockELBPublishEndpointService struct {
	ctrl     *gomock.Controller
	recorder *MockELBPublishEndpointServiceMockRecorder
}

// MockELBPublishEndpointServiceMockRecorder is the mock recorder for MockELBPublishEndpointService.
type MockELBPublishEndpointServiceMockRecorder struct {
	mock *MockELBPublishEndpointService
}

// NewMockELBPublishEndpointService creates a new mock instance.
func NewMockELBPublishEndpointService(ctrl *gomock.Controller) *MockELBPublishEndpointService {
	mock := &MockELBPublishEndpointService{ctrl: ctrl}
	mock.recorder = &MockELBPublishEndpointServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockELBPublishEndpointService) EXPECT() *MockELBPublishEndpointServiceMockRecorder {
	return m.recorder
}

// BindService mocks base method.
func (m *MockELBPublishEndpointService) BindService(ctx context.Context, req *elb_v2.BindServiceRequest) (*elb_v2.CommonPublishEndpointResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindService", ctx, req)
	ret0, _ := ret[0].(*elb_v2.CommonPublishEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BindService indicates an expected call of BindService.
func (mr *MockELBPublishEndpointServiceMockRecorder) BindService(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindService", reflect.TypeOf((*MockELBPublishEndpointService)(nil).BindService), ctx, req)
}

// CreatePublishEndpoint mocks base method.
func (m *MockELBPublishEndpointService) CreatePublishEndpoint(ctx context.Context, req *elb_v2.CreatePublishEndpointRequest) (*elb_v2.CreatePublishEndpointResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePublishEndpoint", ctx, req)
	ret0, _ := ret[0].(*elb_v2.CreatePublishEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePublishEndpoint indicates an expected call of CreatePublishEndpoint.
func (mr *MockELBPublishEndpointServiceMockRecorder) CreatePublishEndpoint(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePublishEndpoint", reflect.TypeOf((*MockELBPublishEndpointService)(nil).CreatePublishEndpoint), ctx, req)
}

// DeletePublishEndpoint mocks base method.
func (m *MockELBPublishEndpointService) DeletePublishEndpoint(ctx context.Context, req *elb_v2.DeletePublishEndpointRequest) (*elb_v2.CommonPublishEndpointResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePublishEndpoint", ctx, req)
	ret0, _ := ret[0].(*elb_v2.CommonPublishEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeletePublishEndpoint indicates an expected call of DeletePublishEndpoint.
func (mr *MockELBPublishEndpointServiceMockRecorder) DeletePublishEndpoint(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePublishEndpoint", reflect.TypeOf((*MockELBPublishEndpointService)(nil).DeletePublishEndpoint), ctx, req)
}

// DeletePublishEndpointACL mocks base method.
func (m *MockELBPublishEndpointService) DeletePublishEndpointACL(ctx context.Context, req *elb_v2.DeletePublishEndpointACLRequest) (*elb_v2.CommonPublishEndpointResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePublishEndpointACL", ctx, req)
	ret0, _ := ret[0].(*elb_v2.CommonPublishEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeletePublishEndpointACL indicates an expected call of DeletePublishEndpointACL.
func (mr *MockELBPublishEndpointServiceMockRecorder) DeletePublishEndpointACL(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePublishEndpointACL", reflect.TypeOf((*MockELBPublishEndpointService)(nil).DeletePublishEndpointACL), ctx, req)
}

// GetPublishEndpointDetail mocks base method.
func (m *MockELBPublishEndpointService) GetPublishEndpointDetail(ctx context.Context, req *elb_v2.GetPublishEndpointDetailRequest) (*elb_v2.GetPublishEndpointDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPublishEndpointDetail", ctx, req)
	ret0, _ := ret[0].(*elb_v2.GetPublishEndpointDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPublishEndpointDetail indicates an expected call of GetPublishEndpointDetail.
func (mr *MockELBPublishEndpointServiceMockRecorder) GetPublishEndpointDetail(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPublishEndpointDetail", reflect.TypeOf((*MockELBPublishEndpointService)(nil).GetPublishEndpointDetail), ctx, req)
}

// ListPublishEndpoint mocks base method.
func (m *MockELBPublishEndpointService) ListPublishEndpoint(ctx context.Context, req *elb_v2.ListPublishEndpointRequest) (*elb_v2.ListPublishEndpointResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPublishEndpoint", ctx, req)
	ret0, _ := ret[0].(*elb_v2.ListPublishEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPublishEndpoint indicates an expected call of ListPublishEndpoint.
func (mr *MockELBPublishEndpointServiceMockRecorder) ListPublishEndpoint(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPublishEndpoint", reflect.TypeOf((*MockELBPublishEndpointService)(nil).ListPublishEndpoint), ctx, req)
}

// ModifyPublishEndpointACL mocks base method.
func (m *MockELBPublishEndpointService) ModifyPublishEndpointACL(ctx context.Context, req *elb_v2.CommonPublishEndpointACLRequest) (*elb_v2.CommonPublishEndpointResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyPublishEndpointACL", ctx, req)
	ret0, _ := ret[0].(*elb_v2.CommonPublishEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyPublishEndpointACL indicates an expected call of ModifyPublishEndpointACL.
func (mr *MockELBPublishEndpointServiceMockRecorder) ModifyPublishEndpointACL(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyPublishEndpointACL", reflect.TypeOf((*MockELBPublishEndpointService)(nil).ModifyPublishEndpointACL), ctx, req)
}

// SetPublishEndpointACL mocks base method.
func (m *MockELBPublishEndpointService) SetPublishEndpointACL(ctx context.Context, req *elb_v2.CommonPublishEndpointACLRequest) (*elb_v2.CommonPublishEndpointResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPublishEndpointACL", ctx, req)
	ret0, _ := ret[0].(*elb_v2.CommonPublishEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPublishEndpointACL indicates an expected call of SetPublishEndpointACL.
func (mr *MockELBPublishEndpointServiceMockRecorder) SetPublishEndpointACL(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPublishEndpointACL", reflect.TypeOf((*MockELBPublishEndpointService)(nil).SetPublishEndpointACL), ctx, req)
}

// UnbindService mocks base method.
func (m *MockELBPublishEndpointService) UnbindService(ctx context.Context, req *elb_v2.UnbindServiceRequest) (*elb_v2.CommonPublishEndpointResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindService", ctx, req)
	ret0, _ := ret[0].(*elb_v2.CommonPublishEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnbindService indicates an expected call of UnbindService.
func (mr *MockELBPublishEndpointServiceMockRecorder) UnbindService(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindService", reflect.TypeOf((*MockELBPublishEndpointService)(nil).UnbindService), ctx, req)
}

// UpdatePublishEndpoint mocks base method.
func (m *MockELBPublishEndpointService) UpdatePublishEndpoint(ctx context.Context, req *elb_v2.UpdatePublishEndpointRequest) (*elb_v2.CommonPublishEndpointResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePublishEndpoint", ctx, req)
	ret0, _ := ret[0].(*elb_v2.CommonPublishEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePublishEndpoint indicates an expected call of UpdatePublishEndpoint.
func (mr *MockELBPublishEndpointServiceMockRecorder) UpdatePublishEndpoint(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePublishEndpoint", reflect.TypeOf((*MockELBPublishEndpointService)(nil).UpdatePublishEndpoint), ctx, req)
}
