/*
modification history
--------------------
2023/12/08, by wangbin<PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
Mock xrm sdk test
*/
package sdkmock

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"icode.baidu.com/baidu/scs/x1-base/sdk/xrm"
)

// TestMockXrmServiceCreateServer tests the MockXrmService's CreateServer method.
// It creates a new mock controller and finishes it when tests are finished.
// The test expects that an XrmService object will be created with the help of gomock.
// Then, it sets expectations for the service to create a server by expecting the request
// parameter to match any value. If the request matches this expectation, the service
// should return a CreateServerResponse struct and no error. Finally, it invokes the
// CreateServer method on the mock service instance using a background context, passing in
// the request and response variables to store the result and any errors encountered.
// After the call, it checks the returned response for nil, indicating failure; if there
// was an error, it fails the test. Otherwise, it verifies that both the response and error
// are non-nil before ending the test.
// Args:
//
//	t - pointer to a testing.T object used for assertions.
//
// Returns: None.
func TestMockXrmServiceCreateServer(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockXrmService := NewMockXrmService(ctrl)
	mockXrmService.EXPECT().CreateServer(gomock.Any(), gomock.Any()).Return(&xrm.CreateServerResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &xrm.CreateServerRequest{}
	resp, err := mockXrmService.CreateServer(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("CreateServer() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("CreateServer() resp is nil")
	}
}

// TestMockXrmServiceDeleteServer tests the DeleteServer method of XrmService using mocked dependencies.
func TestMockXrmServiceDeleteServer(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockXrmService := NewMockXrmService(ctrl)
	mockXrmService.EXPECT().DeleteServer(gomock.Any(), gomock.Any()).Return(&xrm.DeleteServerResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &xrm.DeleteServerRequest{}
	resp, err := mockXrmService.DeleteServer(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("DeleteServer() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("DeleteServer() resp is nil")
	}
}

// TestMockXrmServiceQueryServerCreationTask runs a test to check the Mocked XRM Service's QueryServerCreationTask method
// with various inputs and returns the result of the query operation as well as any errors encountered during execution.
// The test utilizes GoMock for dependency injection and gomock_test.go for setting up the MockXMRService object.
// This function takes in a pointer to a testing.T type object and expects no return values.
func TestMockXrmServiceQueryServerCreationTask(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockXrmService := NewMockXrmService(ctrl)
	mockXrmService.EXPECT().QueryServerCreationTask(gomock.Any(), gomock.Any()).Return(&xrm.QueryServerCreationTaskResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &xrm.QueryServerCreationTaskRequest{}
	resp, err := mockXrmService.QueryServerCreationTask(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("QueryServerCreationTask() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("QueryServerCreationTask() resp is nil")
	}
}

// TestMockXrmServiceUpdateServer 测试 MockXrmService 的 UpdateServer 方法
func TestMockXrmServiceUpdateServer(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockXrmService := NewMockXrmService(ctrl)
	mockXrmService.EXPECT().UpdateServer(gomock.Any(), gomock.Any()).Return(&xrm.UpdateServerResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &xrm.UpdateServerRequest{}
	resp, err := mockXrmService.UpdateServer(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("UpdateServer() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("UpdateServer() resp is nil")
	}
}

// TestMockXrmServiceShowServer 测试 MockXrmService 的 ShowServer 方法
func TestMockXrmServiceShowServer(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockXrmService := NewMockXrmService(ctrl)
	mockXrmService.EXPECT().ShowServer(gomock.Any(), gomock.Any()).Return(&xrm.ShowServerResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &xrm.ShowServerRequest{}
	resp, err := mockXrmService.ShowServer(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("ShowServer() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ShowServer() resp is nil")
	}
}
