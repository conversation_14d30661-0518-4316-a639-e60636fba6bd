// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock_x1_resource is a generated GoMock package.
package sdkmock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	x1_resource "icode.baidu.com/baidu/scs/x1-base/sdk/x1_resource"
)

// MockX1ResourceService is a mock of X1ResourceService interface.
type MockX1ResourceService struct {
	ctrl     *gomock.Controller
	recorder *MockX1ResourceServiceMockRecorder
}

// MockX1ResourceServiceMockRecorder is the mock recorder for MockX1ResourceService.
type MockX1ResourceServiceMockRecorder struct {
	mock *MockX1ResourceService
}

// NewMockX1ResourceService creates a new mock instance.
func NewMockX1ResourceService(ctrl *gomock.Controller) *MockX1ResourceService {
	mock := &MockX1ResourceService{ctrl: ctrl}
	mock.recorder = &MockX1ResourceServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockX1ResourceService) EXPECT() *MockX1ResourceServiceMockRecorder {
	return m.recorder
}

// CreateInstance mocks base method.
func (m *MockX1ResourceService) CreateInstance(ctx context.Context, req *x1_resource.CreateInstanceReq) (*x1_resource.CreateInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstance", ctx, req)
	ret0, _ := ret[0].(*x1_resource.CreateInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstance indicates an expected call of CreateInstance.
func (mr *MockX1ResourceServiceMockRecorder) CreateInstance(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstance", reflect.TypeOf((*MockX1ResourceService)(nil).CreateInstance), ctx, req)
}

// DeleteInstance mocks base method.
func (m *MockX1ResourceService) DeleteInstance(ctx context.Context, req *x1_resource.DeleteInstancesReq) (*x1_resource.DeleteInstancesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstance", ctx, req)
	ret0, _ := ret[0].(*x1_resource.DeleteInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteInstance indicates an expected call of DeleteInstance.
func (mr *MockX1ResourceServiceMockRecorder) DeleteInstance(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstance", reflect.TypeOf((*MockX1ResourceService)(nil).DeleteInstance), ctx, req)
}

// ResizeInstance mocks base method.
func (m *MockX1ResourceService) ResizeInstance(ctx context.Context, req *x1_resource.ResizeInstanceReq) (*x1_resource.ResizeInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResizeInstance", ctx, req)
	ret0, _ := ret[0].(*x1_resource.ResizeInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResizeInstance indicates an expected call of ResizeInstance.
func (mr *MockX1ResourceServiceMockRecorder) ResizeInstance(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResizeInstance", reflect.TypeOf((*MockX1ResourceService)(nil).ResizeInstance), ctx, req)
}

// ShowCreateOrder mocks base method.
func (m *MockX1ResourceService) ShowCreateOrder(ctx context.Context, req *x1_resource.ShowCreateInstanceOrderReq) (*x1_resource.ShowCreateOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShowCreateOrder", ctx, req)
	ret0, _ := ret[0].(*x1_resource.ShowCreateOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShowCreateOrder indicates an expected call of ShowCreateOrder.
func (mr *MockX1ResourceServiceMockRecorder) ShowCreateOrder(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShowCreateOrder", reflect.TypeOf((*MockX1ResourceService)(nil).ShowCreateOrder), ctx, req)
}

// ShowResizeOrder mocks base method.
func (m *MockX1ResourceService) ShowResizeOrder(ctx context.Context, req *x1_resource.ShowResizeInstanceReq) (*x1_resource.ShowResizeOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShowResizeOrder", ctx, req)
	ret0, _ := ret[0].(*x1_resource.ShowResizeOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShowResizeOrder indicates an expected call of ShowResizeOrder.
func (mr *MockX1ResourceServiceMockRecorder) ShowResizeOrder(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShowResizeOrder", reflect.TypeOf((*MockX1ResourceService)(nil).ShowResizeOrder), ctx, req)
}
