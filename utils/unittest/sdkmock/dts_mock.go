// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock_dts is a generated GoMock package.
package sdkmock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	dts "icode.baidu.com/baidu/scs/x1-base/sdk/dts"
)

// MockDTSService is a mock of DTSService interface.
type MockDTSService struct {
	ctrl     *gomock.Controller
	recorder *MockDTSServiceMockRecorder
}

// MockDTSServiceMockRecorder is the mock recorder for MockDTSService.
type MockDTSServiceMockRecorder struct {
	mock *MockDTSService
}

// NewMockDTSService creates a new mock instance.
func NewMockDTSService(ctrl *gomock.Controller) *MockDTSService {
	mock := &MockDTSService{ctrl: ctrl}
	mock.recorder = &MockDTSServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDTSService) EXPECT() *MockDTSServiceMockRecorder {
	return m.recorder
}

// ConfigChecksum mocks base method.
func (m *MockDTSService) ConfigChecksum(ctx context.Context, req *dts.ConfigChecksumRequest) (*dts.CommonResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfigChecksum", ctx, req)
	ret0, _ := ret[0].(*dts.CommonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfigChecksum indicates an expected call of ConfigChecksum.
func (mr *MockDTSServiceMockRecorder) ConfigChecksum(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigChecksum", reflect.TypeOf((*MockDTSService)(nil).ConfigChecksum), ctx, req)
}

// ConfigTask mocks base method.
func (m *MockDTSService) ConfigTask(ctx context.Context, req *dts.ConfigTaskRequest) (*dts.ConfigTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfigTask", ctx, req)
	ret0, _ := ret[0].(*dts.ConfigTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfigTask indicates an expected call of ConfigTask.
func (mr *MockDTSServiceMockRecorder) ConfigTask(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigTask", reflect.TypeOf((*MockDTSService)(nil).ConfigTask), ctx, req)
}

// CreateChecksum mocks base method.
func (m *MockDTSService) CreateChecksum(ctx context.Context, req *dts.CreateChecksumRequest) (*dts.CreateChecksumResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChecksum", ctx, req)
	ret0, _ := ret[0].(*dts.CreateChecksumResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChecksum indicates an expected call of CreateChecksum.
func (mr *MockDTSServiceMockRecorder) CreateChecksum(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChecksum", reflect.TypeOf((*MockDTSService)(nil).CreateChecksum), ctx, req)
}

// CreateTask mocks base method.
func (m *MockDTSService) CreateTask(ctx context.Context, req *dts.CreateTaskRequest) (*dts.CreateTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTask", ctx, req)
	ret0, _ := ret[0].(*dts.CreateTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTask indicates an expected call of CreateTask.
func (mr *MockDTSServiceMockRecorder) CreateTask(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTask", reflect.TypeOf((*MockDTSService)(nil).CreateTask), ctx, req)
}

// DeleteTask mocks base method.
func (m *MockDTSService) DeleteTask(ctx context.Context, req *dts.CommonRequest) (*dts.CommonResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTask", ctx, req)
	ret0, _ := ret[0].(*dts.CommonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTask indicates an expected call of DeleteTask.
func (mr *MockDTSServiceMockRecorder) DeleteTask(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTask", reflect.TypeOf((*MockDTSService)(nil).DeleteTask), ctx, req)
}

// LaunchChecksumPrecheck mocks base method.
func (m *MockDTSService) LaunchChecksumPrecheck(ctx context.Context, req *dts.CommonRequest) (*dts.CommonResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LaunchChecksumPrecheck", ctx, req)
	ret0, _ := ret[0].(*dts.CommonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LaunchChecksumPrecheck indicates an expected call of LaunchChecksumPrecheck.
func (mr *MockDTSServiceMockRecorder) LaunchChecksumPrecheck(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LaunchChecksumPrecheck", reflect.TypeOf((*MockDTSService)(nil).LaunchChecksumPrecheck), ctx, req)
}

// LaunchTaskPrecheck mocks base method.
func (m *MockDTSService) LaunchTaskPrecheck(ctx context.Context, req *dts.CommonRequest) (*dts.CommonResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LaunchTaskPrecheck", ctx, req)
	ret0, _ := ret[0].(*dts.CommonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LaunchTaskPrecheck indicates an expected call of LaunchTaskPrecheck.
func (mr *MockDTSServiceMockRecorder) LaunchTaskPrecheck(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LaunchTaskPrecheck", reflect.TypeOf((*MockDTSService)(nil).LaunchTaskPrecheck), ctx, req)
}

// ListTask mocks base method.
func (m *MockDTSService) ListTask(ctx context.Context, req *dts.ListTaskRequest) (*dts.ListTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTask", ctx, req)
	ret0, _ := ret[0].(*dts.ListTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTask indicates an expected call of ListTask.
func (mr *MockDTSServiceMockRecorder) ListTask(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTask", reflect.TypeOf((*MockDTSService)(nil).ListTask), ctx, req)
}

// ShowChecksum mocks base method.
func (m *MockDTSService) ShowChecksum(ctx context.Context, req *dts.CommonRequest) (*dts.ShowChecksumResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShowChecksum", ctx, req)
	ret0, _ := ret[0].(*dts.ShowChecksumResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShowChecksum indicates an expected call of ShowChecksum.
func (mr *MockDTSServiceMockRecorder) ShowChecksum(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShowChecksum", reflect.TypeOf((*MockDTSService)(nil).ShowChecksum), ctx, req)
}

// ShowTask mocks base method.
func (m *MockDTSService) ShowTask(ctx context.Context, req *dts.ShowTaskRequest) (*dts.ShowTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShowTask", ctx, req)
	ret0, _ := ret[0].(*dts.ShowTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShowTask indicates an expected call of ShowTask.
func (mr *MockDTSServiceMockRecorder) ShowTask(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShowTask", reflect.TypeOf((*MockDTSService)(nil).ShowTask), ctx, req)
}

// ShowTaskPrecheck mocks base method.
func (m *MockDTSService) ShowTaskPrecheck(ctx context.Context, req *dts.CommonRequest) (*dts.ShowTaskPrecheckResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShowTaskPrecheck", ctx, req)
	ret0, _ := ret[0].(*dts.ShowTaskPrecheckResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShowTaskPrecheck indicates an expected call of ShowTaskPrecheck.
func (mr *MockDTSServiceMockRecorder) ShowTaskPrecheck(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShowTaskPrecheck", reflect.TypeOf((*MockDTSService)(nil).ShowTaskPrecheck), ctx, req)
}

// ShutdownChecksum mocks base method.
func (m *MockDTSService) ShutdownChecksum(ctx context.Context, req *dts.CommonRequest) (*dts.CommonResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShutdownChecksum", ctx, req)
	ret0, _ := ret[0].(*dts.CommonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShutdownChecksum indicates an expected call of ShutdownChecksum.
func (mr *MockDTSServiceMockRecorder) ShutdownChecksum(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShutdownChecksum", reflect.TypeOf((*MockDTSService)(nil).ShutdownChecksum), ctx, req)
}

// ShutdownTask mocks base method.
func (m *MockDTSService) ShutdownTask(ctx context.Context, req *dts.CommonRequest) (*dts.CommonResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShutdownTask", ctx, req)
	ret0, _ := ret[0].(*dts.CommonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShutdownTask indicates an expected call of ShutdownTask.
func (mr *MockDTSServiceMockRecorder) ShutdownTask(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShutdownTask", reflect.TypeOf((*MockDTSService)(nil).ShutdownTask), ctx, req)
}

// SkipTaskPrecheck mocks base method.
func (m *MockDTSService) SkipTaskPrecheck(ctx context.Context, req *dts.CommonRequest) (*dts.CommonResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SkipTaskPrecheck", ctx, req)
	ret0, _ := ret[0].(*dts.CommonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SkipTaskPrecheck indicates an expected call of SkipTaskPrecheck.
func (mr *MockDTSServiceMockRecorder) SkipTaskPrecheck(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SkipTaskPrecheck", reflect.TypeOf((*MockDTSService)(nil).SkipTaskPrecheck), ctx, req)
}

// StartChecksum mocks base method.
func (m *MockDTSService) StartChecksum(ctx context.Context, req *dts.CommonRequest) (*dts.CommonResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartChecksum", ctx, req)
	ret0, _ := ret[0].(*dts.CommonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartChecksum indicates an expected call of StartChecksum.
func (mr *MockDTSServiceMockRecorder) StartChecksum(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartChecksum", reflect.TypeOf((*MockDTSService)(nil).StartChecksum), ctx, req)
}

// StartTask mocks base method.
func (m *MockDTSService) StartTask(ctx context.Context, req *dts.CommonRequest) (*dts.CommonResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartTask", ctx, req)
	ret0, _ := ret[0].(*dts.CommonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartTask indicates an expected call of StartTask.
func (mr *MockDTSServiceMockRecorder) StartTask(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartTask", reflect.TypeOf((*MockDTSService)(nil).StartTask), ctx, req)
}
