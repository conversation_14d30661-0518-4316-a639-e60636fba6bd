package sdkmock

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	gmaster "icode.baidu.com/baidu/scs/x1-base/sdk/gmaster"
)

func TestSlaveOfMaster(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.SlaveOfMasterRequest{}
	resp := &gmaster.SlaveOfMasterResponse{}
	mockService.EXPECT().SlaveOfMaster(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.SlaveOfMaster(ctx, req)
	if err != nil {
		t.Errorf("SlaveOfMaster() error = %v", err)
	}
}

func TestAddModifyStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.AddModifyStatusRequest{}
	resp := &gmaster.AddModifyStatusResponse{}
	mockService.EXPECT().AddModifyStatus(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.AddModifyStatus(ctx, req)
	if err != nil {
		t.Errorf("AddModifyStatus() error = %v", err)
	}
}

func TestAddModifyStatus_1(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.AddModifyStatusRequest{}
	resp := &gmaster.AddModifyStatusResponse{}
	mockService.EXPECT().AddModifyStatus(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	r, err := mockService.AddModifyStatus(ctx, req)
	if err != nil {
		t.Errorf("AddModifyStatus() error = %v", err)
	}
	if !reflect.DeepEqual(r, resp) {
		t.Errorf("AddModifyStatus() response = %v, want = %v", r, resp)
	}
}

func TestAddNodes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.AddRedisRequest{}
	resp := &gmaster.AddRedisResponse{}
	mockService.EXPECT().AddNodes(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.AddNodes(ctx, req)
	if err != nil {
		t.Errorf("AddNodes() error = %v", err)
	}
}

func TestAddProxies(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.AddProxyRequest{}
	resp := &gmaster.AddProxyResponse{}
	mockService.EXPECT().AddProxies(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.AddProxies(ctx, req)
	if err != nil {
		t.Errorf("AddProxies() error = %v", err)
	}
}

func TestAddShards(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.AddShardRequest{}
	resp := &gmaster.AddShardResponse{}
	mockService.EXPECT().AddShards(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.AddShards(ctx, req)
	if err != nil {
		t.Errorf("AddShards() error = %v", err)
	}
}

func TestMockGmasterService_ApplyTemplate(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.ApplyTemplateRequest{}
	mockService.EXPECT().ApplyTemplate(gomock.Any(), req).Return(nil)
	ctx := context.Background()
	err := mockService.ApplyTemplate(ctx, req)
	if err != nil {
		t.Errorf("ApplyTemplate() error = %v", err)
	}
}

func TestDeleteNodes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.DeleteRedisRequest{}
	resp := &gmaster.DeleteRedisResponse{}
	mockService.EXPECT().DeleteNodes(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.DeleteNodes(ctx, req)
	if err != nil {
		t.Errorf("DeleteNodes() error = %v", err)
	}
}

func TestMockGmasterService_DeleteProxies(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.DeleteProxyRequest{}
	resp := &gmaster.DeleteProxyResponse{}
	mockService.EXPECT().DeleteProxies(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.DeleteProxies(ctx, req)
	if err != nil {
		t.Errorf("DeleteProxies() error = %v", err)
	}
}

func TestDeleteShards(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.DeleteShardRequest{}
	resp := &gmaster.DeleteShardResponse{}
	mockService.EXPECT().DeleteShards(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	r, err := mockService.DeleteShards(ctx, req)
	if err != nil {
		t.Errorf("DeleteShards() error = %v", err)
	}
	if !reflect.DeepEqual(r, resp) {
		t.Errorf("DeleteShards() got = %v, want %v", r, resp)
	}
}

func TestMockGmasterService_GetModifyStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.GetModifyStatusRequest{}
	resp := &gmaster.GetModifyStatusResponse{}
	mockService.EXPECT().GetModifyStatus(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.GetModifyStatus(ctx, req)
	if err != nil {
		t.Errorf("GetModifyStatus() error = %v", err)
	}
}

func TestMockGmasterService_GetNodes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.GetRedisRequest{}
	resp := &gmaster.GetRedisResponse{}
	mockService.EXPECT().GetNodes(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.GetNodes(ctx, req)
	if err != nil {
		t.Errorf("GetNodes() error = %v", err)
	}
}

func TestGetProxies(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.GetProxyRequest{}
	resp := &gmaster.GetProxyResponse{}
	mockService.EXPECT().GetProxies(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	r, err := mockService.GetProxies(ctx, req)
	if err != nil {
		t.Errorf("GetProxies() error = %v", err)
	}
	if !reflect.DeepEqual(r, resp) {
		t.Errorf("GetProxies() got = %v, want %v", r, resp)
	}
}

func TestGetShards(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.GetShardRequest{}
	resp := &gmaster.GetShardResponse{}
	mockService.EXPECT().GetShards(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.GetShards(ctx, req)
	if err != nil {
		t.Errorf("GetShards() error = %v", err)
	}
}

func TestGroupStatusCAS(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.GroupStatusCASRequest{}
	resp := &gmaster.GroupStatusCASResponse{}
	mockService.EXPECT().GroupStatusCAS(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	r, err := mockService.GroupStatusCAS(ctx, req)
	if err != nil {
		t.Errorf("GroupStatusCAS() error = %v", err)
	}
	if !reflect.DeepEqual(r, resp) {
		t.Errorf("GroupStatusCAS() got = %v, want %v", r, resp)
	}
}

func TestListCacheGroupDetail(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.ListCacheGroupDetailRequest{}
	resp := &gmaster.ListCacheGroupDetailResponse{}
	mockService.EXPECT().ListCacheGroupDetail(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.ListCacheGroupDetail(ctx, req)
	if err != nil {
		t.Errorf("ListCacheGroupDetail() error = %v", err)
	}
}

func TestMockGmasterService_LocalFailover(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.LocalFailoverRequest{}
	resp := &gmaster.LocalFailoverResponse{}
	mockService.EXPECT().LocalFailover(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.LocalFailover(ctx, req)
	if err != nil {
		t.Errorf("LocalFailover() error = %v", err)
	}
}

func TestSlaveOfMaster_1(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.SlaveOfMasterRequest{}
	resp := &gmaster.SlaveOfMasterResponse{}
	mockService.EXPECT().SlaveOfMaster(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.SlaveOfMaster(ctx, req)
	if err != nil {
		t.Errorf("SlaveOfMaster() error = %v", err)
	}
}

func TestMockGmasterService_UpdateInnerSecurity(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.UpdateInnerSecurityRequest{}
	resp := &gmaster.UpdateInnerSecurityResponse{}
	mockService.EXPECT().UpdateInnerSecurity(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.UpdateInnerSecurity(ctx, req)
	if err != nil {
		t.Errorf("UpdateInnerSecurity() error = %v", err)
	}
}

func TestMockGmasterService_UpdateModifyStatusStage(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.UpdateModifyStatusStageRequest{}
	resp := &gmaster.UpdateModifyStatusResponse{}
	mockService.EXPECT().UpdateModifyStatusStage(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	r, err := mockService.UpdateModifyStatusStage(ctx, req)
	if err != nil {
		t.Errorf("UpdateModifyStatusStage() error = %v", err)
	}
	if !reflect.DeepEqual(r, resp) {
		t.Errorf("UpdateModifyStatusStage() got = %v, want %v", r, resp)
	}
}

func TestMockGmasterService_UpdateSecurity(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockService := NewMockGmasterService(ctrl)
	req := &gmaster.UpdateInnerSecurityRequest{}
	resp := &gmaster.UpdateInnerSecurityResponse{}
	mockService.EXPECT().UpdateSecurity(gomock.Any(), req).Return(resp, nil)
	ctx := context.Background()
	_, err := mockService.UpdateSecurity(ctx, req)
	if err != nil {
		t.Errorf("UpdateSecurity() error = %v", err)
	}
}
