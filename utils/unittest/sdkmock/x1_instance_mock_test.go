package sdkmock

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"icode.baidu.com/baidu/scs/x1-base/sdk/x1_resource"
)

func TestMockX1ResourceServiceCreateInstance(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockX1ResourceService(ctrl)
	mock.EXPECT().CreateInstance(gomock.Any(), gomock.Any()).Return(&x1_resource.CreateInstanceResponse{}, nil)

	ctx := context.Background()
	req := &x1_resource.CreateInstanceReq{}
	resp, err := mock.CreateInstance(ctx, req)
	if err != nil {
		t.Fatalf("CreateInstance() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("CreateInstance() resp is nil")
	}
}

func TestMockX1ResourceServiceDeleteInstance(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockX1ResourceService(ctrl)
	mock.EXPECT().DeleteInstance(gomock.Any(), gomock.Any()).Return(&x1_resource.DeleteInstancesResponse{}, nil)

	ctx := context.Background()
	req := &x1_resource.DeleteInstancesReq{}
	resp, err := mock.DeleteInstance(ctx, req)
	if err != nil {
		t.Fatalf("DeleteInstance() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("DeleteInstance() resp is nil")
	}
}

func TestMockX1ResourceServiceResizeInstance(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockX1ResourceService(ctrl)
	mock.EXPECT().ResizeInstance(gomock.Any(), gomock.Any()).Return(&x1_resource.ResizeInstanceResponse{}, nil)

	ctx := context.Background()
	req := &x1_resource.ResizeInstanceReq{}
	resp, err := mock.ResizeInstance(ctx, req)
	if err != nil {
		t.Fatalf("ResizeInstance() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ResizeInstance() resp is nil")
	}
}

func TestMockX1ResourceServiceShowCreateOrder(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockX1ResourceService(ctrl)
	mock.EXPECT().ShowCreateOrder(gomock.Any(), gomock.Any()).Return(&x1_resource.ShowCreateOrderResponse{}, nil)

	ctx := context.Background()
	req := &x1_resource.ShowCreateInstanceOrderReq{}
	resp, err := mock.ShowCreateOrder(ctx, req)
	if err != nil {
		t.Fatalf("ShowCreateOrder() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ShowCreateOrder() resp is nil")
	}
}

func TestMockX1ResourceServiceShowResizeOrder(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockX1ResourceService(ctrl)
	mock.EXPECT().ShowResizeOrder(gomock.Any(), gomock.Any()).Return(&x1_resource.ShowResizeOrderResponse{}, nil)

	ctx := context.Background()
	req := &x1_resource.ShowResizeInstanceReq{}
	resp, err := mock.ShowResizeOrder(ctx, req)
	if err != nil {
		t.Fatalf("ShowResizeOrder() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ShowResizeOrder() resp is nil")
	}
}
