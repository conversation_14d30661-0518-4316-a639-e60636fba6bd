package sdkmock

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	elbv2 "icode.baidu.com/baidu/scs/x1-base/sdk/elb_v2"
)

func TestMockCreatePublishEndpoint(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockELBPublishEndpointService(ctrl)
	mock.EXPECT().CreatePublishEndpoint(gomock.Any(), gomock.Any()).Return(&elbv2.CreatePublishEndpointResponse{}, nil)

	ctx := context.Background()
	req := &elbv2.CreatePublishEndpointRequest{}
	resp, err := mock.CreatePublishEndpoint(ctx, req)
	if err != nil {
		t.Fatalf("CreatePublishEndpoint() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("CreatePublishEndpoint() resp is nil")
	}
}
func TestMockDeletePublishEndpoint(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockELBPublishEndpointService(ctrl)
	mock.EXPECT().DeletePublishEndpoint(gomock.Any(), gomock.Any()).Return(&elbv2.CommonPublishEndpointResponse{}, nil)

	ctx := context.Background()
	req := &elbv2.DeletePublishEndpointRequest{}
	resp, err := mock.DeletePublishEndpoint(ctx, req)
	if err != nil {
		t.Fatalf("DeletePublishEndpoint() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("DeletePublishEndpoint() resp is nil")
	}
}
func TestMockUpdatePublishEndpoint(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockELBPublishEndpointService(ctrl)
	mock.EXPECT().UpdatePublishEndpoint(gomock.Any(), gomock.Any()).Return(&elbv2.CommonPublishEndpointResponse{}, nil)

	ctx := context.Background()
	req := &elbv2.UpdatePublishEndpointRequest{}
	resp, err := mock.UpdatePublishEndpoint(ctx, req)
	if err != nil {
		t.Fatalf("UpdatePublishEndpoint() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("UpdatePublishEndpoint() resp is nil")
	}
}
func TestMockBindService(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockELBPublishEndpointService(ctrl)
	mock.EXPECT().BindService(gomock.Any(), gomock.Any()).Return(&elbv2.CommonPublishEndpointResponse{}, nil)

	ctx := context.Background()
	req := &elbv2.BindServiceRequest{}
	resp, err := mock.BindService(ctx, req)
	if err != nil {
		t.Fatalf("BindService() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("BindService() resp is nil")
	}
}
func TestMockUnbindService(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockELBPublishEndpointService(ctrl)
	mock.EXPECT().UnbindService(gomock.Any(), gomock.Any()).Return(&elbv2.CommonPublishEndpointResponse{}, nil)

	ctx := context.Background()
	req := &elbv2.UnbindServiceRequest{}
	resp, err := mock.UnbindService(ctx, req)
	if err != nil {
		t.Fatalf("UnbindService() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("UnbindService() resp is nil")
	}
}
func TestMockListPublishEndpoint(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockELBPublishEndpointService(ctrl)
	mock.EXPECT().ListPublishEndpoint(gomock.Any(), gomock.Any()).Return(&elbv2.ListPublishEndpointResponse{}, nil)

	ctx := context.Background()
	req := &elbv2.ListPublishEndpointRequest{}
	resp, err := mock.ListPublishEndpoint(ctx, req)
	if err != nil {
		t.Fatalf("ListPublishEndpoint() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ListPublishEndpoint() resp is nil")
	}
}
func TestMockGetPublishEndpointDetail(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockELBPublishEndpointService(ctrl)
	mock.EXPECT().GetPublishEndpointDetail(gomock.Any(), gomock.Any()).Return(&elbv2.GetPublishEndpointDetailResponse{}, nil)

	ctx := context.Background()
	req := &elbv2.GetPublishEndpointDetailRequest{}
	resp, err := mock.GetPublishEndpointDetail(ctx, req)
	if err != nil {
		t.Fatalf("GetPublishEndpointDetail() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("GetPublishEndpointDetail() resp is nil")
	}
}
func TestMockSetPublishEndpointACL(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockELBPublishEndpointService(ctrl)
	mock.EXPECT().SetPublishEndpointACL(gomock.Any(), gomock.Any()).Return(&elbv2.CommonPublishEndpointResponse{}, nil)

	ctx := context.Background()
	req := &elbv2.CommonPublishEndpointACLRequest{}
	resp, err := mock.SetPublishEndpointACL(ctx, req)
	if err != nil {
		t.Fatalf("SetPublishEndpointACL() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("SetPublishEndpointACL() resp is nil")
	}
}
func TestMockModifyPublishEndpointACL(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockELBPublishEndpointService(ctrl)
	mock.EXPECT().ModifyPublishEndpointACL(gomock.Any(), gomock.Any()).Return(&elbv2.CommonPublishEndpointResponse{}, nil)

	ctx := context.Background()
	req := &elbv2.CommonPublishEndpointACLRequest{}
	resp, err := mock.ModifyPublishEndpointACL(ctx, req)
	if err != nil {
		t.Fatalf("ModifyPublishEndpointACL() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ModifyPublishEndpointACL() resp is nil")
	}
}
func TestMockDeletePublishEndpointACL(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockELBPublishEndpointService(ctrl)
	mock.EXPECT().DeletePublishEndpointACL(gomock.Any(), gomock.Any()).Return(&elbv2.CommonPublishEndpointResponse{}, nil)

	ctx := context.Background()
	req := &elbv2.DeletePublishEndpointACLRequest{}
	resp, err := mock.DeletePublishEndpointACL(ctx, req)
	if err != nil {
		t.Fatalf("DeletePublishEndpointACL() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("DeletePublishEndpointACL() resp is nil")
	}
}
