/*
modification history
--------------------
2024/08/09, by wang<PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
Mock dbrs sdk test
*/
package sdkmock

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"icode.baidu.com/baidu/scs/x1-base/sdk/dbrs"
)

func TestMockDbrsServiceCreateBackupPolicy(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().CreateBackupPolicy(gomock.Any(), gomock.Any()).Return(&dbrs.CommonPolicyResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.CreatePolicyRequest{}
	resp, err := mockDbrsService.CreateBackupPolicy(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("CreateBackupPolicy() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("CreateBackupPolicy() resp is nil")
	}
}

func TestMockDbrsServiceDeleteBackupPolicy(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().DeleteBackupPolicy(gomock.Any(), gomock.Any()).Return(&dbrs.CommonPolicyResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.DeletePolicyRequest{}
	resp, err := mockDbrsService.DeleteBackupPolicy(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("DeleteBackupPolicy() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("DeleteBackupPolicy() resp is nil")
	}
}

func TestMockDbrsServiceQueryBackupPolicy(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().QueryBackupPolicy(gomock.Any(), gomock.Any()).Return(&dbrs.QueryPolicyResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.QueryPolicyRequest{}
	resp, err := mockDbrsService.QueryBackupPolicy(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("QueryBackupPolicy() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("QueryBackupPolicy() resp is nil")
	}
}

func TestMockDbrsServiceModifyBackupPolicy(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().ModifyBackupPolicy(gomock.Any(), gomock.Any()).Return(&dbrs.CommonPolicyResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.CommonPolicyRequest{}
	resp, err := mockDbrsService.ModifyBackupPolicy(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("ModifyBackupPolicy() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ModifyBackupPolicy() resp is nil")
	}
}

func TestMockDbrsServiceQueryBackupList(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().QueryBackupList(gomock.Any(), gomock.Any()).Return(&dbrs.QueryBackupListResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.QueryBackupListRequest{}
	resp, err := mockDbrsService.QueryBackupList(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("QueryBackupList() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("QueryBackupList() resp is nil")
	}
}

func TestMockDbrsServiceCreateBackupTask(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().CreateBackupTask(gomock.Any(), gomock.Any()).Return(&dbrs.CreateBackupTaskResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.CreateBackupTaskRequest{}
	resp, err := mockDbrsService.CreateBackupTask(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("CreateBackupTask() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("CreateBackupTask() resp is nil")
	}
}

func TestMockDbrsServiceDeleteBackupRecord(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().DeleteBackupRecord(gomock.Any(), gomock.Any()).Return(&dbrs.DeleteBackupRecordResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.DeleteBackupRecordRequest{}
	resp, err := mockDbrsService.DeleteBackupRecord(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("DeleteBackupRecord() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("DeleteBackupRecord() resp is nil")
	}
}

func TestMockDbrsServiceQueryAppBackupDetailByAppBackupId(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().QueryAppBackupDetailByAppBackupId(gomock.Any(), gomock.Any()).Return(&dbrs.QueryAppBackupDetailResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.QueryAppBackupDetailRequest{}
	resp, err := mockDbrsService.QueryAppBackupDetailByAppBackupId(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("QueryAppBackupDetailByAppBackupId() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("QueryAppBackupDetailByAppBackupId() resp is nil")
	}
}

func TestMockDbrsServiceQueryBackupDetailByBackupId(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().QueryBackupDetailByBackupId(gomock.Any(), gomock.Any()).Return(&dbrs.QueryShardBackupDetailResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.QueryShardBackupDetailRequest{}
	resp, err := mockDbrsService.QueryBackupDetailByBackupId(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("QueryBackupDetailByBackupId() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("QueryBackupDetailByBackupId() resp is nil")
	}
}

func TestMockDbrsServiceModifyBackupEncryptPolicy(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().ModifyBackupEncryptPolicy(gomock.Any(), gomock.Any()).Return(&dbrs.CommonPolicyResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.ModifyEncryptPolicyRequest{}
	resp, err := mockDbrsService.ModifyBackupEncryptPolicy(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("ModifyBackupEncryptPolicy() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("ModifyBackupEncryptPolicy() resp is nil")
	}
}

func TestMockDbrsServiceCreateBackupRestore(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().CreateBackupRestore(gomock.Any(), gomock.Any()).Return(&dbrs.CreateRestoreResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.CreateRestoreRequest{}
	resp, err := mockDbrsService.CreateBackupRestore(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("CreateBackupRestore() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("CreateBackupRestore() resp is nil")
	}
}

func TestMockDbrsServiceQueryBackupRestore(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().QueryBackupRestore(gomock.Any(), gomock.Any()).Return(&dbrs.QueryRestoreResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.QueryRestoreRequest{}
	resp, err := mockDbrsService.QueryBackupRestore(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("QueryBackupRestore() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("QueryBackupRestore() resp is nil")
	}
}
func TestMockDbrsServicePrecheckPointInTimeRestore(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().PrecheckPointInTimeRestore(gomock.Any(), gomock.Any()).Return(&dbrs.PrecheckPointInTimeRestoreResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.PrecheckPointInTimeRestoreRequest{}
	resp, err := mockDbrsService.PrecheckPointInTimeRestore(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("PrecheckPointInTimeRestore() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("PrecheckPointInTimeRestore() resp is nil")
	}
}

func TestMockDbrsServiceQueryPointInTimeRestoreRange(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().QueryPointInTimeRestoreRange(gomock.Any(), gomock.Any()).Return(&dbrs.QueryPointInTimeRestoreRangeResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.QueryPointInTimeRestoreRangeRequest{}
	resp, err := mockDbrsService.QueryPointInTimeRestoreRange(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("QueryPointInTimeRestoreRange() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("QueryPointInTimeRestoreRange() resp is nil")
	}
}

func TestMockDbrsServiceCreatePointInTimeRestore(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().CreatePointInTimeRestore(gomock.Any(), gomock.Any()).Return(&dbrs.CreatePointInTimeRestoreResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.CreatePointInTimeRestoreRequest{}
	resp, err := mockDbrsService.CreatePointInTimeRestore(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("CreatePointInTimeRestore() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("CreatePointInTimeRestore() resp is nil")
	}
}
func TestMockDbrsServiceQueryPointInTimeRestore(t *testing.T) {
	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockDbrsService := NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().QueryPointInTimeRestore(gomock.Any(), gomock.Any()).Return(&dbrs.QueryPointInTimeRestoreResponse{}, nil)

	// Set up calls.
	ctx := context.Background()
	req := &dbrs.QueryPointInTimeRestoreRequest{}
	resp, err := mockDbrsService.QueryPointInTimeRestore(ctx, req)

	// Verify results.
	if err != nil {
		t.Fatalf("QueryPointInTimeRestore() error: %v", err)
	}
	if resp == nil {
		t.Fatalf("QueryPointInTimeRestore() resp is nil")
	}
}
