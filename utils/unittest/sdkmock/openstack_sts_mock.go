/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
Mock bcc sdk
*/

package sdkmock

import (
	"context"
	"strings"

	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
)

type MockBccSdk struct {
}

func (m *MockBccSdk) ShowServer(ctx context.Context, req *bcc.ShowServerRequest) (rsp *bcc.ShowServerResponse, err error) {
	return nil, nil
}
func (m *MockBccSdk) ShowTransaction(ctx context.Context, req *bcc.ShowTransactionRequest) (rsp *bcc.ShowTransactionResponse, err error) {
	return nil, nil
}
func (m *MockBccSdk) SetShowTransactionForUt(ctx context.Context, req *bcc.SetTransactionRequest) (rsp *bcc.SetTransactionResponse, err error) {
	return nil, nil
}
func (m *MockBccSdk) ExchangeId(ctx context.Context, req *bcc.ExchangeIdRequest) (rsp *bcc.ExchangeIdResponse, err error) {
	resp := &bcc.ExchangeIdResponse{}
	for _, id := range req.InstanceIds {
		if strings.Contains(id, "short") {
			resp.Mappings = append(resp.Mappings, &bcc.Mappings{
				Id:   id,
				Uuid: strings.ReplaceAll(id, "short", "long"),
			})
		} else if strings.Contains(id, "long") {
			resp.Mappings = append(resp.Mappings, &bcc.Mappings{
				Id:   strings.ReplaceAll(id, "long", "short"),
				Uuid: id,
			})
		}
	}
	return resp, nil
}
func (m *MockBccSdk) BatchCreateServers(ctx context.Context, req *bcc.BatchCreateServersRequest) (rsp *bcc.BatchCreateServersResponse, err error) {
	resp := &bcc.BatchCreateServersResponse{
		OrderId: "mock-bcc",
	}
	return resp, nil
}
func (m *MockBccSdk) BatchDeleteServers(ctx context.Context, req *bcc.BatchDeleteServersRequest) (rsp *bcc.BatchDeleteServersResponse, err error) {
	return &bcc.BatchDeleteServersResponse{
		Result: int32(len(req.InstanceIds)),
	}, nil
}
func (m *MockBccSdk) ShowOrder(ctx context.Context, req *bcc.ShowOrderRequest) (rsp *bcc.ShowOrderResponse, err error) {
	resp := &bcc.ShowOrderResponse{
		Status: "succ",
	}
	switch req.OrderId {
	case "mock-bcc":
		resp.ZoneName = "zoneA"
		resp.Instances = append(resp.Instances, &bcc.Instances{
			Name:       "host-01",
			AdminPass:  "pass-01",
			InstanceId: "instance-short-01",
			FloatingIp: "*********",
			InternalIp: "***********",
			Flavor:     "2_32_20_20",
			MetaData:   "{entity_ids=node-01__node-02, instance_type=3}",
		})
		resp.Instances = append(resp.Instances, &bcc.Instances{
			Name:       "host-02",
			AdminPass:  "pass-02",
			InstanceId: "instance-short-02",
			FloatingIp: "*********",
			InternalIp: "***********",
			Flavor:     "2_32_20_20",
			MetaData:   "{entity_ids=node-01__node-02, instance_type=3}",
		})
	}
	return resp, nil
}
func (m *MockBccSdk) ShowInstanceInfo(ctx context.Context, req *bcc.ShowInstanceRequest) (rsp *bcc.ShowInstanceResponse, err error) {
	return nil, nil
}
func (m *MockBccSdk) ResizeInstance(ctx context.Context, req *bcc.ResizeInstanceRequest) (rsp *bcc.ResizeInstanceResponse, err error) {
	return nil, nil
}
func (m *MockBccSdk) InstanceAttachedCdsList(ctx context.Context, req *bcc.CdsMountListRequest) (rsp *bcc.CdsMountListResponse, err error) {
	return nil, nil
}
func (m *MockBccSdk) ResizeCds(ctx context.Context, req *bcc.CdsResizeRequest) (rsp *bcc.CdsResizeResponse, err error) {
	return nil, nil
}
func (m *MockBccSdk) CreateDeploySet(ctx context.Context, req *bcc.CreateDeploySetRequest) (rsp *bcc.DeploySetIdsResponse, err error) {
	return nil, nil
}

func (m *MockBccSdk) GetCdsStockWithZone(ctx context.Context, req *bcc.GetCdsStockRequest) (rsp *bcc.GetCdsStockResponse, err error) {
	return nil, nil
}

func (m *MockBccSdk) SubnetDetail(ctx context.Context, req *bcc.GetSubnetDetailRequest) (rsp *bcc.GetSubnetDetailResponse, err error) {
	return nil, nil
}

func (m *MockBccSdk) AttachCds(ctx context.Context, req *bcc.CdsAttachRequest) (rsp *bcc.CdsAttachResponse, err error) {
	return nil, nil
}

func (m *MockBccSdk) DetachCds(ctx context.Context, req *bcc.CdsDetachRequest) (rsp *bcc.CdsDetachResponse, err error) {
	return nil, nil
}

func (m *MockBccSdk) DeleteCds(ctx context.Context, req *bcc.CdsDeleteRequest) (rsp *bcc.CdsDeleteResponse, err error) {
	return nil, nil
}

func (m *MockBccSdk) GetCdsDetail(ctx context.Context, req *bcc.GetCdsDetailRequest) (rsp *bcc.GetCdsDetailResponse, err error) {
	return nil, nil
}
