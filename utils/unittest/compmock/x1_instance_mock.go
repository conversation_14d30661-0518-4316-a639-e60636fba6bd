// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock_x1_instance is a generated GoMock package.
package compmock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	x1_instance "icode.baidu.com/baidu/scs/x1-base/component/x1_instance"
)

// MockX1Instance is a mock of X1Instance interface.
type MockX1Instance struct {
	ctrl     *gomock.Controller
	recorder *MockX1InstanceMockRecorder
}

// MockX1InstanceMockRecorder is the mock recorder for MockX1Instance.
type MockX1InstanceMockRecorder struct {
	mock *MockX1Instance
}

// NewMockX1Instance creates a new mock instance.
func NewMockX1Instance(ctrl *gomock.Controller) *MockX1Instance {
	mock := &MockX1Instance{ctrl: ctrl}
	mock.recorder = &MockX1InstanceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockX1Instance) EXPECT() *MockX1InstanceMockRecorder {
	return m.recorder
}

// CreateInstance mocks base method.
func (m *MockX1Instance) CreateInstance(ctx context.Context, params *x1_instance.CreateInstanceParams) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstance", ctx, params)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstance indicates an expected call of CreateInstance.
func (mr *MockX1InstanceMockRecorder) CreateInstance(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstance", reflect.TypeOf((*MockX1Instance)(nil).CreateInstance), ctx, params)
}

// DeleteInstances mocks base method.
func (m *MockX1Instance) DeleteInstances(ctx context.Context, params *x1_instance.DeleteInstanceParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstances", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteInstances indicates an expected call of DeleteInstances.
func (mr *MockX1InstanceMockRecorder) DeleteInstances(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstances", reflect.TypeOf((*MockX1Instance)(nil).DeleteInstances), ctx, params)
}

// ResizeInstance mocks base method.
func (m *MockX1Instance) ResizeInstance(ctx context.Context, params *x1_instance.ResizeInstanceParam) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResizeInstance", ctx, params)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResizeInstance indicates an expected call of ResizeInstance.
func (mr *MockX1InstanceMockRecorder) ResizeInstance(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResizeInstance", reflect.TypeOf((*MockX1Instance)(nil).ResizeInstance), ctx, params)
}

// ShowCreateInstanceByOrder mocks base method.
func (m *MockX1Instance) ShowCreateInstanceByOrder(ctx context.Context, params *x1_instance.ShowInstanceParams) ([]x1_instance.ResInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShowCreateInstanceByOrder", ctx, params)
	ret0, _ := ret[0].([]x1_instance.ResInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShowCreateInstanceByOrder indicates an expected call of ShowCreateInstanceByOrder.
func (mr *MockX1InstanceMockRecorder) ShowCreateInstanceByOrder(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShowCreateInstanceByOrder", reflect.TypeOf((*MockX1Instance)(nil).ShowCreateInstanceByOrder), ctx, params)
}

// ShowResizeInstanceByOrder mocks base method.
func (m *MockX1Instance) ShowResizeInstanceByOrder(ctx context.Context, params *x1_instance.ShowInstanceParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShowResizeInstanceByOrder", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// ShowResizeInstanceByOrder indicates an expected call of ShowResizeInstanceByOrder.
func (mr *MockX1InstanceMockRecorder) ShowResizeInstanceByOrder(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShowResizeInstanceByOrder", reflect.TypeOf((*MockX1Instance)(nil).ShowResizeInstanceByOrder), ctx, params)
}
