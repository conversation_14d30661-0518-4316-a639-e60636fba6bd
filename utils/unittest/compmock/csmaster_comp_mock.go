/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
Mock csmaster  component
*/

package compmock

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
)

type MockCsmasterOp struct {
	UpdateClusterModelParams *csmaster.UpdateClusterModelParams
	SaveInstancesParams      *csmaster.SaveInstancesParams
	DeleteInstanceParams     *csmaster.DeleteInstanceParams
}

func (m *MockCsmasterOp) UpdateClusterModel(ctx context.Context, params *csmaster.UpdateClusterModelParams) error {
	m.UpdateClusterModelParams = params
	return nil
}

func (m *MockCsmasterOp) GetClusterModel(ctx context.Context, userId string, appId string) (*csmaster.CsmasterCluster, error) {
	return &csmaster.CsmasterCluster{
		Id:            123,
		UserId:        1,
		ClusterShowId: "mock-app",
	}, nil
}

func (m *MockCsmasterOp) SaveInstanceModels(ctx context.Context, params *csmaster.SaveInstancesParams) error {
	m.SaveInstancesParams = params
	return nil
}

func (m *MockCsmasterOp) GetInstanceModels(ctx context.Context, userId string, appId string) ([]*csmaster.CsmasterInstance, error) {
	for idx, model := range m.SaveInstancesParams.Models {
		model.Id = int64(idx)
	}
	return m.SaveInstancesParams.Models, nil
}

func (m *MockCsmasterOp) DeleteInstanceModels(ctx context.Context, params *csmaster.DeleteInstanceParams) error {
	m.DeleteInstanceParams = params
	return nil
}
