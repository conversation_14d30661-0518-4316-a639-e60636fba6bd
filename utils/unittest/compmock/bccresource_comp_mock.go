/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
Mock bcc reousrce component
*/

package compmock

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
)

type MockBccResource struct {
	ZoneAReq *bccresource.CreateBccResourceParams
	ZoneBReq *bccresource.CreateBccResourceParams
	ResizeVmCallCount int
	ResizeCdsCallCount int
}

func (m *MockBccResource) ShowBccResourcesByOrder(ctx context.Context, params *bccresource.ShowBccResourcesParams) ([]bccresource.BccResources, error) {
	var resp []bccresource.BccResources
	switch params.OrderID {
	case "mock-order-zonea":
		resp = []bccresource.BccResources{{
			ID:           "instance-01",
			Name:         "host-01",
			RootPassword: "pw-01",
			FixIP:        "***********",
			FloatingIP:   "*********",
			Flavor:       "2_2_20_20",
			EntityID:     "mock-app-0.0",
		}}
	case "mock-order-zoneb":
		resp = []bccresource.BccResources{{
			ID:           "instance-02",
			Name:         "host-02",
			RootPassword: "pw-01",
			FixIP:        "***********",
			FloatingIP:   "*********",
			Flavor:       "2_2_20_20",
			EntityID:     "mock-app-0.1",
		}}
	}
	return resp, nil
}

func (m *MockBccResource) CreateBccResources(ctx context.Context, params *bccresource.CreateBccResourceParams) (string, error) {
	switch params.LogicalZone {
	case "zoneA":
		m.ZoneAReq = params
		return "mock-order-zonea", nil
	case "zoneB":
		m.ZoneBReq = params
		return "mock-order-zoneb", nil
	}
	return "mock-order", nil
}

func (m *MockBccResource) DeleteBccResource(ctx context.Context, params *bccresource.DeleteBccResourceParams) error {
	return nil
}

func (m *MockBccResource) ResizeVm(ctx context.Context, params *bccresource.ResizeVmParam) error {
	m.ResizeVmCallCount++
	switch m.ResizeVmCallCount % 2 {
	case 0:
		return cerrs.ErrBCCHasResized
	case 1:
		return nil
	}
	return cerrs.ErrBCCHasResized
}

func (m *MockBccResource) ResizeCds(ctx context.Context, params *bccresource.ResizeCdsParam) error {
	m.ResizeCdsCallCount++
	switch m.ResizeCdsCallCount % 2 {
	case 0:
		return cerrs.ErrBCCHasResized
	case 1:
		return nil
	}
	return cerrs.ErrBCCHasResized
}

func (m *MockBccResource) IsVmResizeInOperation(ctx context.Context, params *bccresource.IsVmResizeInOperationParam) (bool, error) {
	return false, nil
}
	
func (m *MockBccResource) IsVmSupportLiveResize(ctx context.Context, params *bccresource.IsVmSupportResizeParam) (bool, error) {
	return true, nil
}
	
func (m *MockBccResource) GetBccVmInfo(ctx context.Context, params *bccresource.GetBccVmInfoRequest) (*bcc.InstanceInfo, error) {
	return nil, nil
}
