/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
Mock bcc reousrce component
*/

package compmock

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/component/zone"
)

type MockZoneOp struct {
}

func (m *MockZoneOp) GetZoneMap(ctx context.Context, userId string) (zone.ZoneMapperFunc, error) {
	return func(zone string, isLogic bool) (string, bool) {
		switch zone {
		case "zoneA":
			return "AZONE-bjyz", true
		case "zoneB":
			return "AZONE-bjrs", true
		case "AZONE-bjyz":
			return "zoneA", true
		case "AZONE-bjrs":
			return "zoneB", true
		default:
			return "", false
		}
	}, nil
}
