package compmock

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"icode.baidu.com/baidu/scs/x1-base/component/x1_instance"
)

func TestMockX1InstanceCreateInstance(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockX1Instance(ctrl)

	// 预期行为
	mock.EXPECT().CreateInstance(gomock.Any(), gomock.Any()).Return("instanceID", nil)

	// 调用 mock 方法
	instanceID, err := mock.CreateInstance(context.Background(), &x1_instance.CreateInstanceParams{})

	// 验证结果
	if err != nil {
		t.Errorf("CreateInstance returned error: %v", err)
	}
	if instanceID != "instanceID" {
		t.Errorf("CreateInstance returned instanceID: %v, want: %v", instanceID, "instanceID")
	}
}

func TestMockX1InstanceDeleteInstances(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockX1Instance(ctrl)

	// 预期行为
	mock.EXPECT().DeleteInstances(gomock.Any(), gomock.Any()).Return(nil)

	// 调用 mock 方法
	err := mock.DeleteInstances(context.Background(), &x1_instance.DeleteInstanceParams{})

	// 验证结果
	if err != nil {
		t.Errorf("DeleteInstance returned error: %v", err)
	}
}

func TestMockX1InstanceResizeInstance(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockX1Instance(ctrl)

	// 预期行为
	mock.EXPECT().ResizeInstance(gomock.Any(), gomock.Any()).Return("instanceID", nil)

	// 调用 mock 方法
	instanceID, err := mock.ResizeInstance(context.Background(), &x1_instance.ResizeInstanceParam{})

	// 验证结果
	if err != nil {
		t.Errorf("ResizeInstance returned error: %v", err)
	}
	if instanceID != "instanceID" {
		t.Errorf("ResizeInstance returned instanceID: %v, want: %v", instanceID, "instanceID")
	}
}

func TestMockX1InstanceShowCreateInstanceByOrder(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockX1Instance(ctrl)

	// 预期行为
	mock.EXPECT().ShowCreateInstanceByOrder(gomock.Any(), gomock.Any()).Return([]x1_instance.ResInstance{}, nil)

	// 调用 mock 方法
	_, err := mock.ShowCreateInstanceByOrder(context.Background(), &x1_instance.ShowInstanceParams{})

	// 验证结果
	if err != nil {
		t.Errorf("ShowCreateInstanceByOrder returned error: %v", err)
	}
}

func TestMockX1InstanceShowResizeInstanceByOrder(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mock := NewMockX1Instance(ctrl)

	// 预期行为
	mock.EXPECT().ShowResizeInstanceByOrder(gomock.Any(), gomock.Any()).Return(nil)

	// 调用 mock 方法
	err := mock.ShowResizeInstanceByOrder(context.Background(), &x1_instance.ShowInstanceParams{})

	// 验证结果
	if err != nil {
		t.Errorf("ShowResizeInstanceByOrder returned error: %v", err)
	}
}
