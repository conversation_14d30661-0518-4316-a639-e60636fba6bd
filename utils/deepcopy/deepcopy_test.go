package deepcopy

import (
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type TestStruct struct {
	A int
	B string
	C []int
	D map[string]int
	E *TestStruct
	F any
}

func TestCopy(t *testing.T) {
	ts := &TestStruct{
		A: 1,
		B: "2",
		C: []int{1, 2, 3},
		D: map[string]int{"a": 1, "b": 2},
		E: &TestStruct{
			A: 1,
			B: "2",
			C: []int{1, 2, 3},
			D: map[string]int{"a": 1, "b": 2}},
		F: 123,
	}
	ts2 := Copy(ts)
	ts.A = 2
	ts.B = "3"
	ts.C[0] = 2
	ts.D["a"] = 2
	ts.E.A = 2
	ts.E.B = "3"
	ts.E.C[0] = 2
	ts.E.D["a"] = 2
	ts.E.F = 456
	if base_utils.Format(ts) == base_utils.Format(ts2) {
		t.<PERSON>r("copy failed, got: ", base_utils.Format(ts))
	}
	t.Logf("ts: %s, ts2: %s", base_utils.Format(ts), base_utils.Format(ts2))
}

func TestCopyIgnoreSlice(t *testing.T) {
	ts := &TestStruct{
		A: 1,
		B: "2",
		C: []int{1, 2, 3},
		D: map[string]int{"a": 1, "b": 2},
		E: &TestStruct{
			A: 1,
			B: "2",
			C: []int{1, 2, 3},
			D: map[string]int{"a": 1, "b": 2}},
		F: 123,
	}
	ts2 := CopyIgnoreSlice(ts)
	if base_utils.Format(ts2) != "{\"A\":1,\"B\":\"2\",\"C\":null,\"D\":{\"a\":1,\"b\":2},\"E\":"+
		"{\"A\":1,\"B\":\"2\",\"C\":null,\"D\":{\"a\":1,\"b\":2},\"E\":null,\"F\":null},\"F\":123}" {
		t.Errorf("copy failed, got: %s", base_utils.Format(ts2))
	}
}
