/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/04/26
 * File: resolve_cluster_id.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package scs_utils TODO package function desc
package scs_utils

const ErrResolveClusterShowId = "get clusterinfo fail,err: %s"

//// ResolveClusterShowId 对应老代码的NewMasterServiceImpl::resolve_cluster_show_id
//func ResolveClusterShowId(ctx context.Context, req ghttp.Request) (cachecluster *csmaster_model_interface.CacheCluster, err error) {
//	clusterShowId := req.QueryDefault("cacheClusterShowId", "")
//	if clusterShowId != "" {
//		cachecluster, err = csmaster_model_api.CsmasterOp().GetClusterModelByAppId(ctx, clusterShowId)
//		if err != nil {
//			logger.SdkLogger.Error(ctx, "get clusterinfo by showid fail, cluster show id:%s , err:%s ", clusterShowId, err.Error())
//			return nil, errors.Errorf(ErrResolveClusterShowId, err.Error())
//		}
//		return
//	}
//
//	queryClusterId := req.QueryDefault("cacheClusterId", "")
//	if queryClusterId != "" {
//		cachecluster, err = csmaster_model_api.CsmasterOp().GetClusterModel(ctx, cast.ToInt64(queryClusterId))
//		if err != nil {
//			logger.SdkLogger.Error(ctx, "get clusterinfo by clusterId fail, clusterId show id:%s , err:%s ", queryClusterId, err.Error())
//			return nil, errors.Errorf(ErrResolveClusterShowId, err.Error())
//		}
//		return
//	}
//
//	logger.SdkLogger.Error(ctx, "miss showid and clusterid")
//	return nil, errors.Errorf(ErrResolveClusterShowId, "miss showid and clusterid")
//
//}
