/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* show_id_gen.go */
/*
modification history
--------------------
2022/05/20 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package scs_utils

import (
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"math/rand"
	"time"
)

func RandLenString(len int) string {
	now := time.Now()
	timestamp := now.Unix()*1000000 + now.UnixMicro()
	rand.Seed(timestamp)
	var randBytes []byte
	for i := 0; i < len; i++ {
		c := 'a' + rand.Int()%26
		randBytes = append(randBytes, cast.ToUint8(c)) //byte是uint8的内置别名
	}
	return base_utils.Format(randBytes)
}
