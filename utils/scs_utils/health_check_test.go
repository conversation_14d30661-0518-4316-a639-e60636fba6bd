package scs_utils

import (
	"context"
	"github.com/pkg/errors"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"testing"
	"time"

	. "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
)

func TestPingViaXagent(t *testing.T) {
	Convey("PingViaXagent", t, func() {
		Convey("test ping, not success", func() {
			outputs := []OutputCell{
				{Values: Params{true, nil}},
				{Values: Params{false, nil}},
				{Values: Params{false, xagent.ErrCallXagentFail}},
				{Values: Params{false, errors.New("Foo")}},
			}
			patches := ApplyFuncSeq(xagent.LivenessProbe, outputs)
			defer patches.Reset()
			tests := []struct {
				name        string
				oldMaster   string
				cluster     *x1model.Cluster
				proxys      []*x1model.Proxy
				password    string
				wantIsAlive bool
			}{
				{
					name:      "不是从节点",
					oldMaster: "127.0.0.1:8888",
					cluster: &x1model.Cluster{
						ClusterId: "cluster1",
						Nodes: []*x1model.Node{
							{
								Role:       x1model.RoleTypeMaster,
								Status:     x1model.NodeOrProxyStatusInUse,
								FloatingIP: "127.0.0.1:8888",
							},
						},
					},
					proxys: []*x1model.Proxy{
						{
							ProxyId:    "proxy1",
							FloatingIP: "127.0.0.1:8888",
						},
					},
					password:    "test",
					wantIsAlive: true,
				},
				{
					name:      "状态不对",
					oldMaster: "127.0.0.1:8888",
					cluster: &x1model.Cluster{
						ClusterId: "cluster1",
						Nodes: []*x1model.Node{
							{
								Role:       x1model.RoleTypeSlave,
								Status:     x1model.NodeOrProxyStatusToCreate,
								FloatingIP: "127.0.0.1:8888",
							},
						},
					},
					proxys: []*x1model.Proxy{
						{
							ProxyId:    "proxy1",
							FloatingIP: "127.0.0.1:8888",
						},
					},
					password:    "test",
					wantIsAlive: true,
				},
				{
					name:      "success",
					oldMaster: "127.0.0.1:8888",
					cluster: &x1model.Cluster{
						ClusterId: "cluster1",
						Nodes: []*x1model.Node{
							{
								Role:       x1model.RoleTypeSlave,
								Status:     x1model.NodeOrProxyStatusInUse,
								FloatingIP: "127.0.0.1:8888",
							},
						},
					},
					proxys: []*x1model.Proxy{
						{
							ProxyId:    "proxy1",
							FloatingIP: "127.0.0.1:8888",
						},
					},
					password:    "test",
					wantIsAlive: true,
				},
				{
					name:      "success",
					oldMaster: "127.0.0.1:8888",
					cluster: &x1model.Cluster{
						ClusterId: "cluster1",
						Nodes: []*x1model.Node{
							{
								Role:       x1model.RoleTypeSlave,
								Status:     x1model.NodeOrProxyStatusInUse,
								FloatingIP: "127.0.0.1:8888",
							},
						},
					},
					proxys: []*x1model.Proxy{
						{
							ProxyId:    "proxy1",
							FloatingIP: "127.0.0.1:8888",
						},
					},
					password:    "test",
					wantIsAlive: true,
				},
				{
					name:      "success",
					oldMaster: "127.0.0.1:8888",
					cluster: &x1model.Cluster{
						ClusterId: "cluster1",
						Nodes: []*x1model.Node{
							{
								Role:       x1model.RoleTypeSlave,
								Status:     x1model.NodeOrProxyStatusInUse,
								FloatingIP: "127.0.0.1:8888",
							},
						},
					},
					proxys: []*x1model.Proxy{
						{
							ProxyId:    "proxy1",
							FloatingIP: "127.0.0.1:8888",
						},
					},
					password:    "test",
					wantIsAlive: true,
				},
				{
					name:      "success",
					oldMaster: "127.0.0.1:8888",
					cluster: &x1model.Cluster{
						ClusterId: "cluster1",
						Nodes: []*x1model.Node{
							{
								Role:       x1model.RoleTypeSlave,
								Status:     x1model.NodeOrProxyStatusInUse,
								FloatingIP: "127.0.0.1:8888",
							},
						},
					},
					proxys: []*x1model.Proxy{
						{
							ProxyId:    "proxy1",
							FloatingIP: "127.0.0.1:8888",
						},
					},
					password:    "test",
					wantIsAlive: true,
				},
			}
			for _, tt := range tests {
				t.Run(tt.name, func(t *testing.T) {
					ctx, cancel := context.WithTimeout(context.Background(), time.Second)
					defer cancel()
					_, _ = PingViaXagent(ctx, &x1model.Node{
						Role:       x1model.RoleTypeSlave,
						Status:     x1model.NodeOrProxyStatusInUse,
						FloatingIP: "127.0.0.1:8888",
					}, tt.cluster, tt.proxys, tt.password)
				})
			}
		})
	})
}

func TestCheckSlavesOnline(t *testing.T) {
	Convey("PingViaXagent", t, func() {
		Convey("test ping, not success", func() {
			outputs := []OutputCell{
				{Values: Params{&single_redis.ReplicationInfo{
					Role:             "slave",
					MasterHost:       "127.0.0.1",
					MasterPort:       6379,
					MasterLinkStatus: "up",
				}, nil}},
				{Values: Params{&single_redis.ReplicationInfo{Role: "foo"}, nil}},
				{Values: Params{nil, errors.New("Foo")}},
			}
			patches := ApplyFuncSeq(single_redis.GetReplicationInfo, outputs)
			defer patches.Reset()

			type args struct {
				oldMaster *x1model.Node
				cluster   *x1model.Cluster
				password  string
			}
			tests := []struct {
				name    string
				args    args
				want    bool
				wantErr bool
			}{
				{
					name: "不是从",
					args: args{
						oldMaster: &x1model.Node{
							Ip:   "127.0.0.1",
							Port: 6379,
						},
						cluster: &x1model.Cluster{
							ClusterId: "cluster1",
							Nodes: []*x1model.Node{
								{
									Role:       x1model.RoleTypeMaster,
									Status:     x1model.NodeOrProxyStatusInUse,
									FloatingIP: "127.0.0.1:8888",
								},
							},
						},
						password: "123456",
					},
					want:    true,
					wantErr: false,
				},
				{
					name: "状态不对",
					args: args{
						oldMaster: &x1model.Node{
							Ip:   "127.0.0.1",
							Port: 6379,
						},
						cluster: &x1model.Cluster{
							ClusterId: "cluster1",
							Nodes: []*x1model.Node{
								{
									Role:       x1model.RoleTypeSlave,
									Status:     x1model.NodeOrProxyStatusToCreate,
									FloatingIP: "127.0.0.1:8888",
								},
							},
						},
						password: "123456",
					},
					want:    true,
					wantErr: false,
				},
				{
					name: "test check",
					args: args{
						oldMaster: &x1model.Node{
							Ip:   "127.0.0.1",
							Port: 6379,
						},
						cluster: &x1model.Cluster{
							ClusterId: "cluster1",
							Nodes: []*x1model.Node{
								{
									Role:       x1model.RoleTypeSlave,
									Status:     x1model.NodeOrProxyStatusInUse,
									FloatingIP: "127.0.0.1:8888",
								},
							},
						},
						password: "123456",
					},
					want:    true,
					wantErr: false,
				},
				{
					name: "test check",
					args: args{
						oldMaster: &x1model.Node{
							Ip:   "127.0.0.1",
							Port: 6379,
						},
						cluster: &x1model.Cluster{
							ClusterId: "cluster1",
							Nodes: []*x1model.Node{
								{
									Role:       x1model.RoleTypeSlave,
									Status:     x1model.NodeOrProxyStatusInUse,
									FloatingIP: "127.0.0.1:8888",
								},
							},
						},
						password: "123456",
					},
					want:    true,
					wantErr: false,
				},
				{
					name: "test check",
					args: args{
						oldMaster: &x1model.Node{
							Ip:   "127.0.0.1",
							Port: 6379,
						},
						cluster: &x1model.Cluster{
							ClusterId: "cluster1",
							Nodes: []*x1model.Node{
								{
									Role:       x1model.RoleTypeSlave,
									Status:     x1model.NodeOrProxyStatusInUse,
									FloatingIP: "127.0.0.1:8888",
								},
							},
						},
						password: "123456",
					},
					want:    true,
					wantErr: false,
				},
			}
			for _, tt := range tests {
				t.Run(tt.name, func(t *testing.T) {
					_ = CheckSlavesOnline(context.Background(), tt.args.oldMaster, tt.args.cluster, tt.args.password)
				})
			}
		})
	})
}
