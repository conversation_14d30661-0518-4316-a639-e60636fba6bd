package scs_utils

import (
	"context"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
)

func PingViaXagent(ctx context.Context, oldMaster *x1model.Node, cluster *x1model.Cluster,
	proxys []*x1model.Proxy, password string) (isAlive bool, needDownGrade bool) {
	type detectT struct {
		agentID       string
		callXagentSuc bool
		masterAlive   bool
	}
	var slaveDetects []*detectT
	g := gtask.Group{}
	for _, node := range x1model.FetchAllNodesOfCluster(ctx, cluster) {
		node := node
		if node.Role != x1model.RoleTypeSlave {
			continue
		}
		if node.Status != x1model.NodeOrProxyStatusInUse {
			continue
		}
		slaveDetect := &detectT{
			agentID:       node.NodeId,
			callXagentSuc: false,
			masterAlive:   false,
		}
		slaveDetects = append(slaveDetects, slaveDetect)
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				isLive, err := xagent.LivenessProbe(ctx, &xagent.Addr{
					Host: node.FloatingIP,
					Port: int32(node.XagentPort),
				}, oldMaster, password)
				if err != nil {
					if err == xagent.ErrCallXagentFail {
						logger.SdkLogger.Trace(ctx, "call xagent fail", logit.String("id", node.NodeId), logit.Error("err", err))
						return nil
					}
					slaveDetect.callXagentSuc = true
					logger.SdkLogger.Trace(ctx, "detection via xagent not alive", logit.String("id", node.NodeId), logit.Error("err", err))
					return nil
				}
				if isLive {
					slaveDetect.callXagentSuc = true
					slaveDetect.masterAlive = true
					logger.SdkLogger.Trace(ctx, "detection via xagent alive", logit.String("node_id", node.NodeId))
					return nil
				}
				logger.SdkLogger.Trace(ctx, "detection via xagent not alive", logit.String("node_id", node.NodeId))
				return nil
			})
		})
	}
	proxyIdx := 0
	for len(slaveDetects) <= 11 {
		if proxyIdx >= len(proxys) {
			break
		}
		proxy := proxys[proxyIdx]
		slaveDetect := &detectT{
			agentID:       proxy.ProxyId,
			callXagentSuc: false,
			masterAlive:   false,
		}
		slaveDetects = append(slaveDetects, slaveDetect)
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				isLive, err := xagent.LivenessProbe(ctx, &xagent.Addr{
					Host: proxy.FloatingIP,
					Port: int32(proxy.XagentPort),
				}, oldMaster, password)
				if err != nil {
					if err == xagent.ErrCallXagentFail {
						logger.SdkLogger.Trace(ctx, "call xagent fail", logit.String("id", proxy.ProxyId), logit.Error("err", err))
						return nil
					}
					slaveDetect.callXagentSuc = true
					logger.SdkLogger.Trace(ctx, "detection via xagent not alive", logit.String("id", proxy.ProxyId), logit.Error("err", err))
					return nil
				}
				if isLive {
					slaveDetect.callXagentSuc = true
					slaveDetect.masterAlive = true
					logger.SdkLogger.Trace(ctx, "detection via xagent alive", logit.String("node_id", proxy.ProxyId))
					return nil
				}
				logger.SdkLogger.Trace(ctx, "detection via xagent not alive", logit.String("node_id", proxy.ProxyId))
				return nil
			})
		})
		proxyIdx++
	}

	_, err := g.Wait()
	if err != nil {
		logger.SdkLogger.Warning(ctx, "multi-point via xagent detection panic", logit.Error("error", err))
		return false, true
	}
	callXagentSucCnt := 0
	aliveCount := 0
	for _, slaveDetect := range slaveDetects {
		if slaveDetect.masterAlive {
			aliveCount++
		}
		if slaveDetect.callXagentSuc {
			callXagentSucCnt++
		}
	}
	// 调用xagent全都失败，降级到老版本二次探测
	if callXagentSucCnt == 0 {
		return false, true
	}

	if aliveCount >= len(slaveDetects)/2+1 {
		return true, false
	}
	return false, false
}

func CheckSlavesOnline(ctx context.Context, oldMaster *x1model.Node, cluster *x1model.Cluster, password string) bool {
	type detectT struct {
		node        *x1model.Node
		masterAlive bool
	}
	var slaveDetects []*detectT
	g := gtask.Group{}
	for _, node := range x1model.FetchAllNodesOfCluster(ctx, cluster) {
		node := node
		if node.Role != x1model.RoleTypeSlave {
			continue
		}
		if node.Status != x1model.NodeOrProxyStatusInUse {
			continue
		}
		slaveDetect := &detectT{
			node:        node,
			masterAlive: false,
		}
		slaveDetects = append(slaveDetects, slaveDetect)
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				replicationInfo, err := single_redis.GetReplicationInfo(ctx, node.FloatingIP, node.Port, password)
				if err != nil {
					logger.SdkLogger.Trace(ctx, "get replication slave info for multi-point detection failed",
						logit.Error("error", err), logit.String("node_id", node.NodeId))
					return nil
				}
				if replicationInfo.Role == "slave" &&
					replicationInfo.MasterHost == oldMaster.Ip &&
					replicationInfo.MasterPort == oldMaster.Port &&
					replicationInfo.MasterLinkStatus == "up" {
					slaveDetect.masterAlive = true
					logger.SdkLogger.Trace(ctx, "multi-point detection master alive", logit.String("node_id", node.NodeId))
					return nil
				}
				logger.SdkLogger.Trace(ctx, "multi-point detection master not alive", logit.String("node_id", node.NodeId))
				return nil
			})
		})
	}
	_, err := g.Wait()
	if err != nil {
		logger.SdkLogger.Warning(ctx, "multi-point detection panic", logit.Error("error", err))
		return false
	}
	aliveCount := 0
	for _, slaveDetect := range slaveDetects {
		if slaveDetect.masterAlive {
			aliveCount++
		}
	}
	if aliveCount >= len(slaveDetects)/2+1 {
		return true
	}
	return false
}
