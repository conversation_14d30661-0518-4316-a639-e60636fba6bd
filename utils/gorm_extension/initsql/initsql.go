/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/06, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
获取Gorm风格Model的数据库初始化语句
*/

package initsql

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type strLogger struct {
	logger.Interface
	Statements []string
}

func (r *strLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	sql, _ := fc()
	r.Statements = append(r.Statements, sql)
}

// GetInitialSQL 获取model在Mysql数据库的初始化语句
func GetInitialSQL(ctx context.Context, db *gorm.DB, tableName string, model interface{}) string {
	returns := []string{}
	recorder := strLogger{logger.Default.LogMode(logger.Info), []string{}}
	tx := db.Session(&gorm.Session{DryRun: true, Context: ctx, Logger: &recorder})
	err := tx.Migrator().DropTable(model)
	if err != nil {
		returns = append(returns, fmt.Sprintf("Get Initial Sql failed; errmsg: %s.", err.Error()))
		returns = append(returns, recorder.Statements...)
		return strings.Join(returns, "\n")
	}
	err = tx.Migrator().CreateTable(model)
	if err != nil {
		returns = append(returns, fmt.Sprintf("Get Initial Sql failed; errmsg: %s.", err.Error()))
		returns = append(returns, recorder.Statements...)
		return strings.Join(returns, "\n")
	}
	returns = append(returns, fmt.Sprintf("\n-- SHOW CREATE TABLE `%s`", tableName))
	for _, stmt := range recorder.Statements {
		stmtFieldList := strings.Split(stmt, ",")
		for idx, stmtField := range stmtFieldList {
			if strings.Contains(stmtField, "FOREIGN KEY") || strings.Contains(stmtField, "FOREIGN_KEY_CHECKS") {
				continue
			}
			if strings.Contains(stmtField, "`id` bigint(20)") {
				stmtField += " AUTO_INCREMENT"
			}
			if strings.Contains(stmtField, "`updated_at` timestamp") {
				stmtField += " ON UPDATE CURRENT_TIMESTAMP"
			}
			if strings.Contains(stmtField, " varchar(") {
				stmtField += " DEFAULT ''"
			}
			if strings.Contains(stmtField, " text") {
				stmtField += " DEFAULT ''"
			}
			if strings.Contains(stmtField, " longtext") {
				stmtField += " DEFAULT ''"
			}
			if strings.Contains(stmtField, " int(") {
				stmtField += " DEFAULT 0"
			}
			if strings.Contains(stmtField, " tinyint(") {
				stmtField += " DEFAULT 0"
			}
			if idx < len(stmtFieldList)-1 {
				returns = append(returns, stmtField+",")
			} else {
				returns = append(returns, stmtField)
			}

		}
	}
	if !strings.HasSuffix(returns[len(returns)-1], "))") {
		if strings.HasSuffix(returns[len(returns)-1], ",") {
			returns[len(returns)-1] = strings.Replace(returns[len(returns)-1], ",", ")", 1)
		} else {
			returns[len(returns)-1] = returns[len(returns)-1] + ")"
		}
	}
	returns[len(returns)-1] = returns[len(returns)-1] + " ENGINE=InnoDB DEFAULT CHARSET=utf8"
	for idx := range returns {
		if !strings.HasSuffix(returns[idx], ";") && !strings.HasSuffix(returns[idx], ",") {
			returns[idx] = returns[idx] + ";"
		}
	}
	return strings.Join(returns, "\n")
}
