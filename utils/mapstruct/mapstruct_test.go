/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-10
 * File: mapstruct_test.go
 */

/*
 * DESCRIPTION
 *   mapstruct ut
 */

// Package debug_utils
package mapstruct

import (
	"encoding/json"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func TestDecodeExWithMeta_NormalCase(t *testing.T) {
	jsonStr := `{
	"a": 1,
	"b": [2,3,4],
	"c": {
		"d": 5,
		"e": 9223372036854775807
	},
	"f": "7"
}`
	var in interface{}
	if err := base_utils.UnmarshalUseNumber([]byte(jsonStr), &in); err != nil {
		t.Fatalf("unmarshal fail :(  %s", err.Error())
	}

	type tmpType struct {
		A int
		B []string
		C map[string]interface{}
	}
	out := &tmpType{}

	meta, err := DecodeExWithMeta(in, out, true, "")
	if err != nil {
		t.Fatalf("[TestDecodeExWithMeta_NormalCase] decode fail")
	}

	if !func() bool {
		if out.A != 1 || len(out.B) != 3 || out.B[2] != "4" || len(out.C) != 2 {
			return false
		} else if e, ok := out.C["e"]; !ok {
			return false
		} else if e, ok := e.(json.Number); !ok {
			return false
		} else if e, err := e.Int64(); err != nil {
			return false
		} else if e != 9223372036854775807 {
			return false
		}
		return true
	}() {
		t.Fatalf("[TestDecodeExWithMeta_NormalCase] invalid result, in: %+v, out: %+v", in, out)
	}

	if len(meta.Unused) != 1 || meta.Unused[0] != "f" {
		t.Fatalf("[TestDecodeExWithMeta_NormalCase] meta invalid, meta: %+v", meta)
	}
}

//
func TestDecodeExWithMeta_EscapePanicBug(t *testing.T) {
	src := map[interface{}]interface{}{"a": "a", 1: 1}
	dst := struct{ A string }{}
	_, err := DecodeExWithMeta(src, &dst, false, "")
	if err != nil {
		fmt.Printf("error occurs as expected: %s\n", err.Error())
	}
}
