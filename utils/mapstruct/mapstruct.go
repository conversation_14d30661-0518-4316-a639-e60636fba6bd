/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-07
 * File: mapstruct.go
 */

/*
 * DESCRIPTION
 *   mapstruct
 */

// Package mapstruct
package mapstruct

import (
	"fmt"

	"github.com/mitchellh/mapstructure"
)

// Decode - 将map[string]interface{}解码到结构体中, 或将结构体反向解码为map[string]interface{}
func Decode(input, output interface{}) error {
	return mapstructure.Decode(input, output)
}

// WeakDecode - 同Decode，但不对结构体字段类型和map[string]interface{}的对应键值做强类型一致的校验
func WeakDecode(input, output interface{}) error {
	return mapstructure.WeakDecode(input, output)
}

// DecodeEx - 同Decode或WeakDecode，可指定fieldTag
func DecodeEx(input, output interface{}, isWeak bool, fieldTag string) error {
	decoder, err := mapstructure.NewDecoder(&mapstructure.DecoderConfig{
		WeaklyTypedInput: isWeak,
		Result:           output,
		TagName:          fieldTag,
	})
	if err != nil {
		return err
	}

	return decoder.Decode(input)
}

// DecodeExWithMeta - 同DecodeEx，同时返回meta
func DecodeExWithMeta(input, output interface{}, isWeak bool, fieldTag string) (meta *mapstructure.Metadata, err error) {
	// 如果input是个map，并存在非string类型的key，output是个struct
	// 此时执行decode会导致mapstructure panic，此处进行recover规避
	defer func() {
		if p := recover(); p != nil {
			err = fmt.Errorf("[panic] %v", p)
			return
		}
	}()

	meta = &mapstructure.Metadata{}
	decoder, err := mapstructure.NewDecoder(&mapstructure.DecoderConfig{
		WeaklyTypedInput: isWeak,
		Result:           output,
		TagName:          fieldTag,
		Metadata:         meta,
	})
	if err != nil {
		return
	}

	err = decoder.Decode(input)
	return
}
