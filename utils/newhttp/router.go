package newhttp

import (
	"context"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/logit"
	"net/http"
	"strconv"
	"sync"
)

type HookHttpResponseWriter struct {
	HeaderMap http.Header
	Status    int
	Data      []byte
	hmlock    sync.Mutex
}

func (h *HookHttpResponseWriter) Header() http.Header {
	return h.HeaderMap
}

func (h *HookHttpResponseWriter) Write(bytes []byte) (int, error) {
	h.Data = bytes
	return len(bytes), nil
}

func (h *HookHttpResponseWriter) WriteHeader(statusCode int) {
	h.Status = statusCode
}

func (h *HookHttpResponseWriter) HeaderInvoker(fn func(h http.Header)) {
	h.hmlock.Lock()
	defer h.hmlock.Unlock()
	fn(h.HeaderMap)
}

func newHttpResponseWriter(resp ghttp.Response) *HookHttpResponseWriter {
	r := &HookHttpResponseWriter{}
	_ = resp.WriteTo(r)
	return r
}

type hookHttpResponse struct {
	w *HookHttpResponseWriter
}

func copyHeaders(dst http.Header, src http.Header) {
	for k, vs := range src {
		for _, v := range vs {
			dst.Add(k, v)
		}
	}
}

func (hr *hookHttpResponse) WriteTo(w http.ResponseWriter) error {
	ghttp.WithHeaderSafely(w, func(h http.Header) {
		copyHeaders(h, hr.w.HeaderMap)
		h.Set("Content-Length", strconv.Itoa(len(hr.w.Data)))
	})
	w.WriteHeader(hr.w.Status)
	_, err := w.Write(hr.w.Data)
	return err
}

func newHookHttpResponse(hw *HookHttpResponseWriter) *hookHttpResponse {
	return &hookHttpResponse{
		w: hw,
	}
}

type AfterHandleHookFunc func(ctx context.Context, req ghttp.Request, hw *HookHttpResponseWriter)

type Router interface {
	ghttp.Router
	AddAfterHandleHook(ahfs ...AfterHandleHookFunc)
}

type router struct {
	r    ghttp.Router
	ahfl []AfterHandleHookFunc
}

func (r *router) cloneAfterHooks() []AfterHandleHookFunc {
	if len(r.ahfl) == 0 {
		return nil
	}
	fs := make([]AfterHandleHookFunc, len(r.ahfl))
	copy(fs, r.ahfl)
	return fs
}

func (r *router) ServeHTTP(writer http.ResponseWriter, request *http.Request) {
	r.r.ServeHTTP(writer, request)
}

func (r *router) HandleFunc(method string, path string, handler ghttp.HandlerFunc, middleWareFuncs ...ghttp.MiddleWareFunc) {
	// clone注册时的hook函数列表，router注册后加入的hook不会生效
	curHookFunc := r.cloneAfterHooks()
	r.r.HandleFunc(method, path, func(ctx context.Context, req ghttp.Request) ghttp.Response {
		resp := handler(ctx, req)
		hw := newHttpResponseWriter(resp)
		for _, ahf := range curHookFunc {
			ahf(ctx, req, hw)
		}
		return newHookHttpResponse(hw)
	}, middleWareFuncs...)
}

func (r *router) HandleStreamFunc(method string, path string, handler ghttp.StreamHandlerFunc, middleWareFuncs ...ghttp.MiddleWareFunc) {
	r.r.HandleStreamFunc(method, path, handler, middleWareFuncs...)
}

func (r *router) HandleStd(method string, path string, handler http.Handler, middleWareFuncs ...ghttp.MiddleWareFunc) {
	r.r.HandleStd(method, path, handler, middleWareFuncs...)
}

func (r *router) Group(prefixPath string, filters ...ghttp.MiddleWareFunc) ghttp.Router {
	return r.r.Group(prefixPath, filters...)
}

func (r *router) Use(filters ...ghttp.MiddleWareFunc) {
	r.r.Use(filters...)
}

func (r *router) NotFound(h ghttp.StreamHandlerFunc) {
	r.r.NotFound(h)
}

func (r *router) SetLogger(logger logit.Logger) {
	r.r.SetLogger(logger)
}

func (r *router) Logger() logit.Logger {
	return r.r.Logger()
}

func (r *router) AddAfterHandleHook(ahfs ...AfterHandleHookFunc) {
	ahfs = append(r.ahfl, ahfs...)
}

func NewRouter(r ghttp.Router) Router {
	return &router{
		r: r,
	}
}
