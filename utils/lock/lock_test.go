/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/06, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
分布式锁测试
*/

package lock

import (
	"icode.baidu.com/baidu/gdp/redis"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

func init() {
	unittest.UnitTestInit(2)
	opts := []redis.ClientOption{
		redis.OptHooker(redis.NewLogHook()),
	}
	client, err := redis.NewClient("broker-redis", opts...)
	if err != nil {
		panic(err.Error())
	}
	MustInit(client)
}

func TestLock(t *testing.T) {
	// TODO can not connect redis 127.0.0.1
	/*
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := redisClient.Do(ctx, "FLUSHALL").Err(); err != nil {
		t.Fatalf("flush redis error; err: %s", err.Error())
	}
	if err := Lock(ctx, "lock1", "owner1", time.Second); err != nil {
		t.Fatalf("lock failed; err: %s", err.Error())
	}
	// 锁还没有超时
	time.Sleep(500 * time.Millisecond)
	if err := Lock(ctx, "lock1", "owner2", time.Second); err == nil {
		t.Fatalf("expect err != nil, actual err == nil")
	}
	// 锁已经超时
	time.Sleep(500 * time.Millisecond)
	if err := Lock(ctx, "lock1", "owner2", time.Second); err != nil {
		t.Fatalf("lock failed; err: %s", err.Error())
	}
	// 锁是可重入的
	if err := Lock(ctx, "lock1", "owner2", time.Second); err != nil {
		t.Fatalf("lock failed; err: %s", err.Error())
	}
	// 只有owner可以执行解锁
	if err := UnLock(ctx, "lock1", "owner1"); err == nil {
		t.Fatalf("expect err != nil, actual err == nil")
	}
	if err := UnLock(ctx, "lock1", "owner2"); err != nil {
		t.Fatalf("unlock failed; err: %s", err.Error())
	}
	if err := UnLock(ctx, "lock1", "owner2"); err != nil {
		t.Fatalf("unlock failed; err: %s", err.Error())
	}
	if err := Lock(ctx, "lock1", "owner1", time.Second); err != nil {
		t.Fatalf("lock failed; err: %s", err.Error())
	}*/
}
