/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/06, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
分布式锁
*/

package lock

import (
	"context"
	"math/rand"
	"time"

	"github.com/google/uuid"
	"icode.baidu.com/baidu/gdp/redis"
)

// 加锁、解锁使用的LUA
const (
	LockLuaScript   = "if redis.call(\"get\", KEYS[1]) == false then return redis.call(\"psetex\", KEYS[1], ARGV[2], ARGV[1]) else if redis.call(\"get\", KEYS[1]) == ARGV[1] then return redis.call(\"psetex\", KEYS[1], ARGV[2], ARGV[1]) else return redis.error_reply(\"Lock Failed\") end end"
	UnLockLuaScript = "if redis.call(\"get\", KEYS[1]) == false then return 0 else if redis.call(\"get\", KEYS[1]) == ARGV[1] then return redis.call(\"del\", KEYS[1]) else return redis.error_reply(\"UnLock Failed\") end end"
)

var redisClient redis.Client

func MustInit(cli redis.Client) {
	redisClient = cli
}

// Lock 分布式锁，超时最小为1ms
// TODO 性能未进行优化；后续可优化为evalsha
func Lock(ctx context.Context, id string, owner string, expire time.Duration) error {
	return redisClient.Do(ctx, "eval", LockLuaScript, "1", id, owner, int(expire/time.Millisecond)).Err()
}

// UnLock 分布式锁，只有owner可以解锁; 锁不存在时不会报错
// TODO 性能未进行优化；后续可优化为evalsha
func UnLock(ctx context.Context, id string, owner string) error {
	return redisClient.Do(ctx, "eval", UnLockLuaScript, "1", id, owner).Err()
}

func BlockLock(ctx context.Context, id string, lockExpiration time.Duration, blockTimeout time.Duration) (unlock func(), err error) {
	owner := uuid.NewString()
	sctx, cancel := context.WithTimeout(ctx, blockTimeout)
	defer cancel()
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for {
		err = redisClient.Do(sctx, "eval", LockLuaScript, "1", id, owner, int(lockExpiration/time.Millisecond)).Err()
		if err == nil{
			break
		}
		select {
		case <-sctx.Done():
			err = sctx.Err()
		// 随机休眠100~200ms
		case <-time.After((time.Duration(r.Intn(100) + 100)) * time.Millisecond):
			continue
		}
		return
	}
	
	unlock = func() {
		redisClient.Do(ctx, "eval", UnLockLuaScript, "1", id, owner)
	}
	return
}
