package lcc_zone

import (
	"context"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/scs/x1-base/component/zone"
)

// MockZoneOp 是一个模拟的 ZoneOp 结构体
type MockZoneOp struct{}

// GetZoneMap 模拟 GetZoneMap 方法
func (m *MockZoneOp) GetZoneMap(ctx context.Context, userID string) (zone.ZoneMapperFunc, error) {
	// 返回一个模拟的映射函数
	return func(zone string, isLogic bool) (string, bool) {
		if isLogic {
			if zone == "zoneA" {
				return "AZONE-xx", true
			}
			if zone == "zoneB" {
				return "AZONE-cdhmlcc001", true
			}
		}
		return "", false
	}, nil
}

func TestIsHaveLccAzone(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	userID := "test-user"
	azDeployInfo := "zoneA,info1,info2,info3;zoneB,info4,info5,info6"

	// 应用 MockZoneOp
	patches := gomonkey.ApplyFunc(zone.ZoneOp, func() zone.ZoneOpIface {
		return &MockZoneOp{}
	})
	defer patches.Reset()

	// (1) 调用被测试函数, 有 LCC
	haveLcc, err := IsHaveLccAzone(ctx, userID, azDeployInfo)
	assert.NoError(t, err)
	assert.True(t, haveLcc)

	// (2) 测试另一个场景：没有 LCC 区域
	azDeployInfoNoLcc := "zoneA,info4,info5,info6"
	haveLccNoLcc, err := IsHaveLccAzone(ctx, userID, azDeployInfoNoLcc)
	assert.NoError(t, err)
	assert.False(t, haveLccNoLcc)

	// (3) 测试空 azDeployInfo
	emptyAzDeployInfo, err := IsHaveLccAzone(ctx, userID, "")
	assert.Error(t, err)
	assert.False(t, emptyAzDeployInfo)

	// (4) 测试不存在 Zone
	azDeployInfoZoneNoExist := "zoneC,info4,info5,info6"
	haveLccZoneNoExist, err := IsHaveLccAzone(ctx, userID, azDeployInfoZoneNoExist)
	assert.Error(t, err)
	assert.False(t, haveLccZoneNoExist)
}
