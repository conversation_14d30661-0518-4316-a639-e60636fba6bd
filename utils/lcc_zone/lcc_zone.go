package lcc_zone

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/logger"
)

func IsHaveLccAzone(ctx context.Context, userID string, azDeployInfo string) (bool, error) {
	if azDeployInfo == "" {
		logger.SdkLogger.Warning(ctx, "azDeployInfo empty")
		return false, cerrs.Errorf("azDeployInfo empty")
	}

	// 1 获取集群逻辑可用区
	zoneList := []string{}
	rawList := strings.Split(azDeployInfo, ";")
	for _, rawLine := range rawList {
		rawData := strings.Split(rawLine, ",")
		if len(rawData) != 4 {
			continue
		}
		zoneList = append(zoneList, rawData[0])
	}

	// 2 获取 zone map
	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, userID)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "get zone map failed", logit.Error("error", err))
		return false, err
	}

	// 检查 lcc
	for _, zone := range zoneList {
		physicalZone, found := zoneMapper(zone, true)
		if !found {
			logger.SdkLogger.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", zone))
			return false, cerrs.Errorf("not found azone for lzone %s", zone)
		}
		if strings.Contains(physicalZone, "lcc") {
			return true, nil
		}
	}
	return false, nil
}
