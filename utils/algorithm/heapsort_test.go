/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/03/08
 * File: heapsort_test.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package algorithm TODO package function desc
package algorithm

import (
	"container/heap"
	"testing"
)

func TestHeapSort(t *testing.T) {
	h := &IntHeap{5, 7, 3, 6, 8, 2, 4}
	heap.Init(h)
	heap.Push(h, 1)
	heap.Push(h, 0)
	counter := 0
	cases := []int{0, 1, 2, 3, 4, 5, 6, 7, 8}
	for h.Len() > 0 {
		if heap.Pop(h) != cases[counter] {
			t.Fatalf("Fail")
		}
		counter++
	}

}
