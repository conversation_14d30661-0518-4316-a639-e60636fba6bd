/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* multi_region.go */
/*
modification history
--------------------
2022/05/11 , by <PERSON> (<PERSON><PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
多地域工具方法
*/

package gmaster

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/utils/common"
)

func GetInnerRegion(ctx context.Context, region string) (innerRegion string) {
	return common.GetInnerRegion(ctx, region)
}

func SetRegionMap(region string, innerRegion string) {
	common.RegionMap[region] = innerRegion
}

func ResetRegionMap(newMap map[string]string) {
	common.RegionMap = newMap
}

var ErrCantGetUserId = errors.Errorf("get user id from ctx fail")

func GetToken(ctx context.Context, region string) (token string, err error) {
	iamUserId, ok := csmaster.GetIamUserIdFromCtx(ctx)
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return fmt.Sprintf("scs-private|2a5d355806e24876ab025d14529d26e5|%s", iamUserId), nil
	}
	if !ok {
		return "", ErrCantGetUserId
	}
	innerRegion := GetInnerRegion(ctx, region)
	return compo_utils.MrGetOpenapiToken(ctx, iamUserId, innerRegion)
}
