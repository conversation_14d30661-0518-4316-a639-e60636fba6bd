/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* before_modify_check.go */
/*
modification history
--------------------
2022/05/11 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
group 操作前的前置检查
*/

package gmaster

import (
	"context"

	"strings"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	cc "icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
)

func ModifyGroupCommonCheck(ctx context.Context, groupInfo *global_model.AppGroup) error {
	if err := AuthUserGroup(ctx, groupInfo); err != nil {
		return err
	}
	if err := IsRunning(ctx, groupInfo); err != nil {
		return err
	}
	return nil
}

func AuthUserGroup(ctx context.Context, groupInfo *global_model.AppGroup) error {
	uid, ok := csmaster.GetIamUserIdFromCtx(ctx)
	if !ok || uid == "" {
		return errors.Errorf("auth fail:cant get iam user id from ctx")
	}
	if uid != groupInfo.UserId {
		return errors.Errorf("is not authorized to access group")
	}
	return nil
}

func IsRunning(ctx context.Context, groupInfo *global_model.AppGroup) error {
	if groupInfo.Status != global_model.StatusRuning {
		return errors.Errorf("status not running, operation forbidden")
	}
	return nil
}

// CanBeLeaderForNewGroup 判断是否满足一个新只读实例组的主实例的条件:
//  a. 主实例不能属于其他热活实例组
//  b. 主实例必须为集群版
//  c. 主实例状态为【运行中】
func CanBeLeaderForNewGroup(ctx context.Context, leaderClusterInfo *csmaster.CsmasterClusterResponse) error {
	//  a. 主实例不能属于其他热活实例组
	if leaderClusterInfo.Model.GroupId != "" {
		return errors.Errorf("cluster already in group")
	}
	// //  b. 主实例必须为集群版
	// if leaderClusterInfo.Model.Version != cc.CsmasterVersionCluser {
	// 	return errors.Errorf("not cluster mod/not 5001，version:%s", base_utils.Format(leaderClusterInfo.Model.Version))
	// }
	//  c. 主实例状态为【运行中】
	if leaderClusterInfo.Model.Status != cc.CsmasterStatusRunning {
		return errors.Errorf("not in running status")
	}

	engineVersion := strings.Split(leaderClusterInfo.Model.KernelVersion, ".")
	if len(engineVersion) == 0 {
		return errors.Errorf("engine version ilegal")
	}
	if cast.ToInt(engineVersion[0]) < 4 {
		return errors.Errorf("engine version too low,verion:%s", leaderClusterInfo.Model.KernelVersion)
	}

	return nil
}
