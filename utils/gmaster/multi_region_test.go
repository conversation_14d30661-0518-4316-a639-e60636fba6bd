package gmaster

import (
	"context"
	"icode.baidu.com/baidu/gdp/env"
	"testing"
)

func TestGetToken(t *testing.T) {
	type args struct {
		ctx    context.Context
		region string
	}
	tests := []struct {
		name      string
		args      args
		wantToken string
		wantErr   bool
	}{
		{
			name: "test1",
			args: args{
				ctx:    context.Background(),
				region: "bj",
			},
			wantToken: "scs-private|2a5d355806e24876ab025d14529d26e5|",
			wantErr:   false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env.Default = env.New(env.Option{
				IDC: "dbstacktest",
			})
			gotToken, err := GetToken(tt.args.ctx, tt.args.region)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotToken != tt.wantToken {
				t.Errorf("GetToken() gotToken = %v, want %v", gotToken, tt.wantToken)
			}
		})
	}
}
