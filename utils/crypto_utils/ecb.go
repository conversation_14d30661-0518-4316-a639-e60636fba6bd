/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-08
 * File: ecb.go
 */

/*
 * DESCRIPTION
 *   ecb加解密模式
 */

// Package crypto_utils
package crypto_utils

import "crypto/cipher"

// ECBEncrypt - ECB加密
func ECBEncrypt(block cipher.Block, src []byte, padding PaddingType) ([]byte, error) {
	blockSize := block.BlockSize()
	src = Padding(padding, src, blockSize)

	encryptData := make([]byte, len(src))

	for index := 0; index < len(src); index += blockSize {
		block.Encrypt(encryptData[index:], src[index:index+blockSize])
	}

	return encryptData, nil
}

// ECBDecrypt - ECB解密
func ECBDecrypt(block cipher.Block, src []byte, padding PaddingType) ([]byte, error) {
	dst := make([]byte, len(src))

	blockSize := block.BlockSize()

	for index := 0; index < len(src); index += blockSize {
		block.Decrypt(dst[index:], src[index:index+blockSize])
	}

	dst = UnPadding(padding, dst)

	return dst, nil
}
