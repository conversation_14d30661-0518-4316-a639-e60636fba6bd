/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-08
 * File: cbc.go
 */

/*
 * DESCRIPTION
 *   cbc加解密模式
 */

// Package crypto_utils
package crypto_utils

import "crypto/cipher"

// CBCEncrypt - CBC加密
func CBCEncrypt(block cipher.Block, src, iv []byte, padding PaddingType) ([]byte, error) {
	blockSize := block.BlockSize()
	src = Padding(padding, src, blockSize)

	encryptData := make([]byte, len(src))

	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(encryptData, src)

	return encryptData, nil
}

// CBCDecrypt - CBC解密
func CBCDecrypt(block cipher.Block, src, iv []byte, padding PaddingType) ([]byte, error) {
	dst := make([]byte, len(src))

	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(dst, src)

	dst = UnPadding(padding, dst)

	return dst, nil
}
