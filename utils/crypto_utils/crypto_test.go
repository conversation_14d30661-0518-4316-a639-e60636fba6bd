package crypto_utils

import (
	"fmt"
	"testing"
)

func TestAesECBEncryptString(t *testing.T) {
	key := "1234567890123456"
	src := "123456789012345678901234567890"

	dst, err := AesECBEncryptString(src, key, PKCS7PaddingType, HexCodecType)
	if err != nil {
		t.Fatal("[TestAesECBEncryptString] encrypt fail")
	}

	fmt.Printf("encrypted: %s\n", dst)
	decrypted, err := AesECBDecryptString(dst, key, PKCS7PaddingType, HexCodecType)
	if err != nil {
		t.Fatal("[TestAesECBEncryptString] decrypt fail")
	}
	fmt.Printf("decrypted: %s\n", decrypted)

	if decrypted != src {
		t.Errorf("data changed after encrypt and decrypt")
	}
}
