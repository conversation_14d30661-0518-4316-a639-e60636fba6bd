/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-08
 * File: codec.go
 */

/*
 * DESCRIPTION
 *   codec
 */

// Package crypto_utils
package crypto_utils

import (
	"encoding/base64"
	"encoding/hex"
)

type CodecType string

const (
	HexCodecType    CodecType = "Hex"
	Base64CodecType CodecType = "Base64"
)

type encodeFunc func([]byte) string
type decodeFunc func(s string) ([]byte, error)

var (
	encodeFuncMap = map[CodecType]encodeFunc{
		HexCodecType:    encodeFunc(hex.EncodeToString),
		Base64CodecType: encodeFunc(base64.RawURLEncoding.EncodeToString),
	}
	decodeFuncMap = map[CodecType]decodeFunc{
		HexCodecType:    decodeFunc(hex.DecodeString),
		Base64CodecType: decodeFunc(base64.RawURLEncoding.DecodeString),
	}
)

// Encode - 编码
func Encode(codec CodecType, src []byte) string {
	encodeFunc, ok := encodeFuncMap[codec]
	if !ok {
		panic("codec type not supported: " + codec)
	}
	return encodeFunc(src)
}

// Decode - 解码
func Decode(codec CodecType, src string) ([]byte, error) {
	decodeFunc, ok := decodeFuncMap[codec]
	if !ok {
		panic("codec type not supported: " + codec)
	}
	return decodeFunc(src)
}
