/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-08
 * File: aes.go
 */

/*
 * DESCRIPTION
 *   aes加解密
 */

// Package crypto_utils
package crypto_utils

import (
	"crypto/aes"
)

// AesCBCEncryptString - AES-xxx-CBC 字符串加密
func AesCBCEncryptString(src, key, iv string, padding PaddingType, codec CodecType) (string, error) {
	dst, err := AesCBCEncrypt([]byte(src), []byte(key), []byte(iv), padding)
	if err != nil {
		return "", err
	}
	return Encode(codec, dst), nil
}

// AesCBCDecryptString - AES-xxx-CBC 字符串解密
func AesCBCDecryptString(src, key, iv string, padding PaddingType, codec CodecType) (string, error) {
	encryptData, err := Decode(codec, src)
	if err != nil {
		return "", err
	}
	dst, err := AesCBCDecrypt(encryptData, []byte(key), []byte(iv), padding)
	if err != nil {
		return "", err
	}

	return string(dst), nil
}

// AesECBEncryptString - AES-xxx-ECB 字符串加密
func AesECBEncryptString(src, key string, padding PaddingType, codec CodecType) (string, error) {
	dst, err := AesECBEncrypt([]byte(src), []byte(key), padding)
	if err != nil {
		return "", err
	}

	return Encode(codec, dst), nil
}

// AesECBDecryptString - AES-xxx-ECB 字符串解密
func AesECBDecryptString(src, key string, padding PaddingType, codec CodecType) (string, error) {
	encryptData, err := Decode(codec, src)
	if err != nil {
		return "", err
	}
	dst, err := AesECBDecrypt(encryptData, []byte(key), padding)
	if err != nil {
		return "", err
	}

	return string(dst), nil
}

// AesCBCEncrypt - AES-xxx-CBC bytes加密
func AesCBCEncrypt(src, key, iv []byte, padding PaddingType) ([]byte, error) {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return nil, err
	}

	return CBCEncrypt(block, src, iv, padding)
}

// AesCBCDecrypt - AES-xxx-CBC bytes解密
func AesCBCDecrypt(src, key, iv []byte, padding PaddingType) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	return CBCDecrypt(block, src, iv, padding)
}

// AesECBEncrypt - AES-xxx-ECB bytes加密
func AesECBEncrypt(src, key []byte, padding PaddingType) ([]byte, error) {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return nil, err
	}

	return ECBEncrypt(block, src, padding)
}

// AesECBDecrypt - AES-xxx-ECB bytes加密
func AesECBDecrypt(src, key []byte, padding PaddingType) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	return ECBDecrypt(block, src, padding)
}
