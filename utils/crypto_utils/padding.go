/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-08
 * File: padding.go
 */

/*
 * DESCRIPTION
 *   padding
 */

// Package crypto_utils
package crypto_utils

import "bytes"

type PaddingType string

const (
	NonePaddingType  PaddingType = "None"
	PKCS7PaddingType PaddingType = "PKCS#7"
	ZerosPaddingType PaddingType = "Zeros"
)

// Padding - padding
func Padding(padding PaddingType, src []byte, blockSize int) []byte {
	switch padding {
	case PKCS7PaddingType:
		return PKCS7Padding(src, blockSize)
	case ZerosPaddingType:
		return ZerosPadding(src, blockSize)
	case NonePaddingType:
	default:
		panic("padding type not supported: " + padding)
	}

	return src
}

// UnPadding - un-padding
func UnPadding(padding PaddingType, src []byte) []byte {
	switch padding {
	case PKCS7PaddingType:
		return PKCS7UnPadding(src)
	case ZerosPaddingType:
		return ZerosUnPadding(src)
	case NonePaddingType:
	default:
		panic("padding type not supported: " + padding)
	}

	return src
}

// PKCS7Padding - PKCS#7 padding
func PKCS7Padding(src []byte, blockSize int) []byte {
	padding := blockSize - len(src)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(src, padtext...)
}

// PKCS7UnPadding - PKCS#7 un-padding
func PKCS7UnPadding(src []byte) []byte {
	length := len(src)
	unpadding := int(src[length-1])
	return src[:(length - unpadding)]
}

// ZerosPadding - Zeros padding
func ZerosPadding(src []byte, blockSize int) []byte {
	paddingCount := blockSize - len(src)%blockSize
	if paddingCount == 0 {
		return src
	} else {
		return append(src, bytes.Repeat([]byte{byte(0)}, paddingCount)...)
	}
}

// ZerosUnPadding - Zeros un-padding
func ZerosUnPadding(src []byte) []byte {
	for i := len(src) - 1; i >= 0; i-- {
		if src[i] != 0 {
			return src[:i+1]
		}
	}
	return nil
}
