package crypto_utils

import (
	"crypto/rand"
)

const cryptKey = "9ab504b3401a10dc"

func Encrypt<PERSON>ey(key string) (string, error) {
	iv := genRandomBytes(len(cryptKey))
	bytes, err := AesCBCEncrypt([]byte(key), []byte(cryptKey), iv, PKCS7PaddingType)
	if err != nil {
		return "", err
	}
	bytes = append(bytes, iv...)
	return Encode(Base64CodecType, bytes), nil
}

func DecryptKey(key string) (string, error) {
	bytes, err := Decode(Base64CodecType, key)
	if err != nil {
		return "", err
	}
	iv := bytes[len(bytes)-len(cryptKey):]
	bytes = bytes[:len(bytes)-len(cryptKey)]
	bytes, err = AesCBCDecrypt(bytes, []byte(cryptKey), iv, PKCS7PaddingType)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

func GenRandomKey(n int) string {
	bytes := genRandomBytes(n / 2)
	return Encode(HexCodecType, bytes)[:n]
}

func genRandomBytes(n int) []byte {
	bytes := make([]byte, n)
	_, _ = rand.Read(bytes)
	return bytes
}
