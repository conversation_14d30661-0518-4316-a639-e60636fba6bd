package crypto_utils

import (
	"fmt"
	"testing"
)

func TestCodec_Base64(t *testing.T) {
	src := "1234567898765432123456789"
	dst := Encode(Base64CodecType, []byte(src))
	fmt.Printf("%s\n", dst)
	decoded, err := Decode(Base64CodecType, dst)
	if err != nil {
		t.Fatalf("decode fail: %s", err.<PERSON><PERSON><PERSON>())
	}
	decodedStr := string(decoded)
	if decodedStr != src {
		t.<PERSON><PERSON>("data changed after encoding and decoding")
	}
}
