package common

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/logger"
)

var RegionMap = map[string]string{
	// 幂等map
	"bj":     "bj",
	"bd":     "bd",
	"sin":    "sin",
	"bjfsg":  "bjfsg",
	"gz":     "gz",
	"hkg":    "hkg",
	"su":     "su",
	"szfsg":  "szfsg",
	"wh":     "wh",
	"bdfsg":  "bdfsg",
	"bjtest": "bjtest",
	"yq":     "yq",
	"cd":     "cd",
	"nj":     "nj",
	// 下面是非幂等map
	"onlinebj":    "bj",
	"onlinebd":    "bd",
	"onlinesin":   "sin",
	"onlinebjfsg": "bjfsg",
	"onlinegz":    "gz",
	"onlinehkg":   "hkg",
	"onlinesu":    "su",
	"onlineszfsg": "szfsg",
	"test":        "test",
	"onlinewh":    "wh",
	"onlineyq":    "yq",
	"onlinecd":    "cd",
	"onlinenj":    "nj",
	// 下面不确定
	"fwh":          "wh",
	"hb-fsg":       "bdfsg",
	"onlinefwh":    "wh",
	"onlinehb-fsg": "bdfsg",
	"onlinefsh":    "fsh",
}

func GetInnerRegion(ctx context.Context, region string) (innerRegion string) {
	if mapedRegion, ok := RegionMap[region]; ok {
		return mapedRegion
	}
	logger.DefaultLogger.Warning(ctx, "unknow region,region:%s", region)
	return region
}
