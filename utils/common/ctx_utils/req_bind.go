/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/03/22
 * File: req_bind.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package ctx_utils TODO package function desc
package ctx_utils

import (
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"icode.baidu.com/baidu/gdp/ghttp"
)

// ShouldBind bind both post form and query
func ShouldBind(req ghttp.Request, obj interface{}) error {
	ginCtx := gin.Context{
		Request: req.HTTPRequest(),
	}
	if err := ginCtx.ShouldBind(obj); err != nil {
		return err
	}
	return nil
}

// ShouldBindFormUrlencoded application/x-www-form-urlencoded
func ShouldBindFormUrlencoded(req ghttp.Request, obj interface{}) error {
	ginCtx := gin.Context{
		Request: req.HTTPRequest(),
	}
	if err := ginCtx.ShouldBindWith(obj, binding.FormPost); err != nil {
		return err
	}
	return nil
}

// ShouldBindQuery BindQuery
func ShouldBindQuery(req ghttp.Request, obj interface{}) error {
	ginCtx := gin.Context{
		Request: req.HTTPRequest(),
	}
	if err := ginCtx.ShouldBindQuery(obj); err != nil {
		return err
	}
	return nil
}

// ShouldBindManual Manual binding
func ShouldBindManual(req ghttp.Request, obj interface{}, b binding.Binding) error {
	ginCtx := gin.Context{
		Request: req.HTTPRequest(),
	}
	if err := ginCtx.ShouldBindWith(obj, b); err != nil {
		return err
	}
	return nil
}
