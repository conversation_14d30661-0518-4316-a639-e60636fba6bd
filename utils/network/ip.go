/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/22
 * File: ip.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package network TODO package function desc
package network

import (
	"math"
	"strings"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
)

const (
	ipV4CidrBitsWidth = 32
)

func CidrToIpNum(cidr string) (num int64, err error) {
	num = 0
	err = nil
	splitedCidr := strings.Split(cidr, "/")
	if len(splitedCidr) != 2 {
		return 0, errors.Errorf("Invalid Cidr: %s", cidr)
	}

	networkPrefixWide := cast.ToInt(splitedCidr[1])
	hostBits := ipV4CidrBitsWidth - networkPrefixWide
	num = cast.ToInt64(math.Pow(2, cast.ToFloat64(hostBits)))

	return
}

// CidrToIpNumV6 ipv6版本，未实现
func CidrToIpNumV6(cidr string) (num int64, err error) {
	num = 0
	err = nil
	return
}
