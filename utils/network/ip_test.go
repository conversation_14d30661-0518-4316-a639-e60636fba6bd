/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON>uni<PERSON>@baidu.com)
 * Date: 2021/12/22
 * File: ip_test.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package network TODO package function desc
package network

import (
	"testing"
)

func TestCidrToIpNum(t *testing.T) {
	cases := map[string]int64{}
	for cidr, numAnswer := range cases {
		num, err := CidrToIpNum(cidr)
		if err != nil {
			t.Fatalf("call CidrToIpNum func fail, err: %v", err.Error())
		}
		if num != numAnswer {
			t.Fatalf("answer wrong, cidr:%s , answer:%v , but get:%v", cidr, numAnswer, num)
		}
	}
}
