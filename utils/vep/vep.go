package vep

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"icode.baidu.com/baidu/gdp/redis"

	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/model"
)

/*
VpcEndpoint Table

CREATE TABLE `vpc_endpoint` (

`id` bigint(20) NOT NULL AUTO_INCREMENT,
`endpoint_id` varchar(64) NOT NULL DEFAULT ” COMMENT 'endpoint id',
`entity` varchar(64) NOT NULL DEFAULT ” COMMENT 'entity',
`name` varchar(64) NOT NULL DEFAULT ” COMMENT 'name',
`tenant_id` varchar(64) NOT NULL DEFAULT ” COMMENT 'tenant id',
`vpc_id` varchar(64) NOT NULL DEFAULT ” COMMENT 'vpc id',
`creator` varchar(64) NOT NULL DEFAULT ” COMMENT 'creator',
`protocol` varchar(64) NOT NULL DEFAULT ” COMMENT 'protocol',
`endpoint_ip` varchar(64) NOT NULL DEFAULT ” COMMENT 'endpoint ip',
`endpoint_port` int(11) NOT NULL DEFAULT '0' COMMENT 'endpoint port',
`backend_ip` varchar(64) NOT NULL DEFAULT ” COMMENT 'backend_ip',
`backend_port` int(11) NOT NULL DEFAULT '0' COMMENT 'backend_port',
`type` varchar(64) NOT NULL DEFAULT ” COMMENT 'type',
`status` varchar(64) NOT NULL DEFAULT ” COMMENT 'status',
`ext_subnet_id` varchar(64) NOT NULL DEFAULT ” COMMENT 'ext_subnet_id',
`exclusive_endpoint_ip` varchar(64) NOT NULL DEFAULT ” COMMENT 'exclusive_endpoint_ip',
`created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
`updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',

PRIMARY KEY (`id`),
KEY `idx_entity_app_id` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='entity info'
*/
type VpcEndpoint struct {
	ID                  int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	EndpointID          string    `gorm:"column:endpoint_id;type:varchar(64);not null" json:"endpoint_id" ukey:"id"`
	Entity              string    `gorm:"column:entity;type:varchar(64);not null" json:"entity"`
	Name                string    `gorm:"column:name;type:varchar(64);not null" json:"name"`
	TenantID            string    `gorm:"column:tenant_id;type:varchar(64);not null" json:"tenant_id"`
	VpcID               string    `gorm:"column:vpc_id;type:varchar(64);not null" json:"vpc_id"`
	Creator             string    `gorm:"column:creator;type:varchar(64);not null" json:"creator"`
	Protocol            string    `gorm:"column:protocol;type:varchar(64);not null" json:"protocol"`
	EndpointIP          string    `gorm:"column:endpoint_ip;type:varchar(64);not null" json:"endpoint_ip"`
	EndpointPort        int       `gorm:"column:endpoint_port;type:int;not null" json:"endpoint_port"`
	BackendIP           string    `gorm:"column:backend_ip;type:varchar(64);not null" json:"backend_ip"`
	BackendPort         int       `gorm:"column:backend_port;type:int;not null" json:"backend_port"`
	Type                string    `gorm:"column:type;type:varchar(64);not null" json:"type"`
	Description         string    `gorm:"column:description;type:varchar(256);not null" json:"description"`
	Status              string    `gorm:"column:status;type:varchar(64);not null" json:"status"`
	ExtSubnetID         string    `gorm:"column:ext_subnet_id;type:varchar(64);not null" json:"ext_subnet_id"`
	ExclusiveEndpointIP string    `gorm:"column:exclusive_endpoint_ip;type:varchar(64);not null" json:"exclusive_endpoint_ip"`
	CreatedAt           time.Time `gorm:"column:created_at;type:timestamp;not null" json:"created_at"`
	UpdatedAt           time.Time `gorm:"column:updated_at;type:timestamp;not null" json:"updated_at"`
}

func (VpcEndpoint) TableName() string {
	return "vpc_endpoint"
}

type VpcEndpointModelServices interface {
	SaveEndpoints(ctx context.Context, endpoints []*VpcEndpoint) error
	DeleteEndpoints(ctx context.Context, endpoints []*VpcEndpoint) error
	GetEndpointsByPort(ctx context.Context, entity string, port int) (*VpcEndpoint, error)
	ParseVpcEndpoint(ip string, port int) (string, int, error)
	Resource() *model.Resource
}

type vpcEndpointModelServices struct {
	r        *model.Resource
	redisCli redis.Client
	once     sync.Once
	inited   bool
}

var modelInstance vpcEndpointModelServices

func InitVpcEndpointModelServices(ctx context.Context, cfg model.ResourceCfg, redisCli redis.Client) error {
	var err error
	modelInstance.once.Do(func() {
		modelInstance.r, err = model.InitModel(ctx, cfg)
		if err != nil {
			return
		}
		modelInstance.redisCli = redisCli
		modelInstance.inited = true
	})
	return err
}

func GetVpcEndpointModelServices() VpcEndpointModelServices {
	return &modelInstance
}

func (m *vpcEndpointModelServices) SaveEndpoints(ctx context.Context, endpoints []*VpcEndpoint) error {
	for _, endpoint := range endpoints {
		_ = m.redisCli.Del(ctx, getEndpointKey(endpoint.EndpointID))
	}
	return m.r.FullSaveAssociationsSave(ctx, &endpoints)
}

func (m *vpcEndpointModelServices) DeleteEndpoints(ctx context.Context, endpoints []*VpcEndpoint) error {
	for _, endpoint := range endpoints {
		_ = m.redisCli.Del(ctx, getEndpointKey(endpoint.EndpointID))
	}
	return m.r.DeleteMulti(ctx, &endpoints)
}

func (m *vpcEndpointModelServices) GetEndpointsByPort(ctx context.Context, entity string, port int) (*VpcEndpoint, error) {
	endpoint := &VpcEndpoint{}
	raw, err := m.redisCli.Get(ctx, getEntityPortKey(entity, port)).Result()
	if err == nil {
		if err := json.Unmarshal([]byte(raw), endpoint); err == nil {
			return endpoint, nil
		}
	}
	var endpoints []*VpcEndpoint
	if err := m.r.GetAllByCond(ctx, &endpoints, "entity = ? AND backend_port = ?", entity, port); err != nil {
		return nil, err
	}
	if len(endpoints) == 0 {
		return nil, fmt.Errorf("endpoint not found")
	}
	endpoint = endpoints[0]
	rawByte, err := json.Marshal(endpoint)
	if err == nil {
		_ = m.redisCli.Set(ctx, getEntityPortKey(entity, port), string(rawByte), time.Second*3600).Err()
	}
	return endpoint, nil
}

func getEndpointKey(endpointID string) string {
	return "x1-task:sdk:vep:vpc-endpoint-cache:" + endpointID
}

func getEntityPortKey(entity string, port int) string {
	return "x1-task:sdk:vep:vpc-endpoint-cache:" + entity + ":" + strconv.Itoa(port)
}

func (m *vpcEndpointModelServices) ParseVpcEndpoint(ip string, port int) (string, int, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	if !strings.HasPrefix(ip, "vep://") {
		return ip, port, nil
	}
	if !m.inited {
		return "", 0, fmt.Errorf("vpc endpoint model services not inited")
	}
	entity := strings.TrimPrefix(ip, "vep://")
	endpoint, err := m.GetEndpointsByPort(ctx, entity, port)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "get vpc endpoint failed, err: %v", err)
		return "", 0, err
	}
	logger.SdkLogger.Trace(ctx, "get vpc endpoint %s:%s -> %s:%s", ip, strconv.Itoa(port), endpoint.EndpointIP, strconv.Itoa(endpoint.EndpointPort))
	return endpoint.EndpointIP, endpoint.EndpointPort, nil
}

func (m *vpcEndpointModelServices) Resource() *model.Resource {
	return m.r
}
