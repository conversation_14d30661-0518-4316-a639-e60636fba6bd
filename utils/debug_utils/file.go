/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-07
 * File: debug.go
 */

/*
 * DESCRIPTION
 *   debug utils
 */

// Package debug_utils
package debug_utils

import (
	"path/filepath"
	"runtime"
)

// GetCurFileDir -
func GetCurFileDir(depth int) string {
	curFilename := GetCurFileName(depth + 1)

	return filepath.Dir(curFilename)
}

// GetCurFileName -
func GetCurFileName(depth int) string {
	_, curFilename, _, _ := runtime.Caller(depth + 1)

	return curFilename
}
