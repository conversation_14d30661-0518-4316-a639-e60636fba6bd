/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-10
 * File: func.go
 */

/*
 * DESCRIPTION
 *   debug utils
 */

// Package debug_utils
package debug_utils

import (
	"runtime"
	"strings"
)

// GetCurFuncName -
func GetCurFuncName(depth int) string {
	pc, _, _, _ := runtime.Caller(depth + 1)
	func_ := runtime.FuncForPC(pc)
	funcName := func_.Name()
	if pos := strings.LastIndex(funcName, "."); pos >= 0 {
		funcName = funcName[pos+1:]
	}
	return funcName
}
