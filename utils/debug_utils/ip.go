/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-07
 * File: ip.go
 */

/*
 * DESCRIPTION
 *   debug utils
 */

// Package debug_utils
package debug_utils

import (
	"context"
	"net"
	"regexp"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/scs/x1-base/logger"
)

// IsInnerNet - 本地网络是否是内网
func IsInnerNet() bool {
	ips, err := GetLocalIps()
	if err != nil {
		return false
	}

	for _, ip := range ips {
		if IsInnerNetIp(ip) {
			return true
		}
	}

	return false
}

// IsOfficeNet - 本地网络是否是办公网
func IsOfficeNet() bool {
	ips, err := GetLocalIps()
	if err != nil {
		return false
	}

	for _, ip := range ips {
		if IsOfficeNetIp(ip) {
			return true
		}
	}

	return false
}

var innerNetSeg = []string{
	"10.0.0.0/8",
	"***********/16",
}

// IsInnerNetIp - 是否是内网
func IsInnerNetIp(ip string) bool {
	return CheckNetSeg(ip, innerNetSeg)
}

var officialNetSeg = []string{
	"**********/12",
	"************/24",
	"************/24",
	"*************/28",
	"*************/28",
}

// IsOfficeNetIp - 是否是办公网
func IsOfficeNetIp(ip string) bool {
	return CheckNetSeg(ip, officialNetSeg)
}

// CheckNetSeg - 检测ip是否在网段列表中
func CheckNetSeg(ip string, netSegList []string) bool {
	for _, netSeg := range netSegList {
		ipAndMask := strings.Split(netSeg, "/")
		if len(ipAndMask) == 2 {
			mask, _ := strconv.ParseInt(ipAndMask[1], 10, 64)
			intSegMask := int64(-1 << (32 - mask))
			if (Ip2long(ipAndMask[0]) & intSegMask) == (Ip2long(ip) & intSegMask) {
				return true
			}
		}
	}
	return false
}

var ipRegExp = regexp.MustCompile(`^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})`)

// Ip2long - ip转long
func Ip2long(ip string) int64 {
	ips := ipRegExp.FindStringSubmatch(ip)
	if ips == nil {
		return 0
	}

	ip1, _ := strconv.Atoi(ips[1])
	ip2, _ := strconv.Atoi(ips[2])
	ip3, _ := strconv.Atoi(ips[3])
	ip4, _ := strconv.Atoi(ips[4])

	if ip1 > 255 || ip2 > 255 || ip3 > 255 || ip4 > 255 {
		return 0
	}

	ipInt := (ip1 << 24) | (ip2 << 16) | (ip3 << 8) | ip1

	return int64(ipInt)
}

// GetLocalIps - 获取本地ip
func GetLocalIps() ([]string, error) {
	netAddresses, err := net.InterfaceAddrs()
	if err != nil {
		logger.DefaultLogger.Warning(context.Background(),
			"fail to get local addresses, err = %s",
			err.Error())
		return nil, err
	}

	ips := make([]string, 0)
	for _, address := range netAddresses {
		if ipNet, ok := address.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			ipv4 := ipNet.IP.To4()
			if ipv4 != nil {
				ips = append(ips, ipv4.String())
			}
		}
	}
	return ips, nil
}
