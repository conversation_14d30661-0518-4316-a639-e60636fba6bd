package base_utils

import "github.com/spf13/cast"

//
// "Ptr" series
//

func BoolPtr(v bool) *bool {
	return &v
}

func IntPtr(v int) *int {
	return &v
}

func Int32Ptr(v int32) *int32 {
	return &v
}

func Int64Ptr(v int64) *int64 {
	return &v
}

func UintPtr(v uint) *uint {
	return &v
}

func Uint32Ptr(v uint32) *uint32 {
	return &v
}

func Uint64Ptr(v uint64) *uint64 {
	return &v
}

func Float32Ptr(v float32) *float32 {
	return &v
}

func Float64Ptr(v float64) *float64 {
	return &v
}

func StringPtr(v string) *string {
	return &v
}

//
// "To" series
//

func ToBool(v interface{}) bool {
	return cast.ToBool(v)
}

func ToInt(v interface{}) int {
	return cast.ToInt(v)
}

func ToInt32(v interface{}) int32 {
	return cast.ToInt32(v)
}

func ToInt64(v interface{}) int64 {
	return cast.ToInt64(v)
}

func ToUint(v interface{}) uint {
	return cast.ToUint(v)
}

func ToUint32(v interface{}) uint32 {
	return cast.ToUint32(v)
}

func ToUint64(v interface{}) uint64 {
	return cast.ToUint64(v)
}

func ToFloat32(v interface{}) float32 {
	return cast.ToFloat32(v)
}

func ToFloat64(v interface{}) float64 {
	return cast.ToFloat64(v)
}

func ToString(v interface{}) string {
	return cast.ToString(v)
}
