/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/10, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
将http Request的Query转换为SQL查询的条件
*/

package base_utils

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
)

type SQLFilter struct {
	Fmt     string
	A       []interface{}
	Offset  int64
	Limit   int64
	OrderBy string
}

var opMap map[string]string = map[string]string{
	"gt": ">",
	"lt": "<",
	"ge": ">=",
	"le": "<=",
	"in": "IN",
	"lk": "LIKE",
}

func parseKey(k string) (string, string, error) {
	if !strings.Contains(k, "[") {
		return "=", k, nil
	}
	sp := strings.Split(k, "[")
	if len(sp) != 2 {
		return "", "", fmt.<PERSON>("invalid key %s", k)
	}
	ssp := strings.Split(sp[1], "]")
	if len(ssp) != 2 {
		return "", "", fmt.Errorf("invalid key %s", k)
	}
	if _, found := opMap[ssp[0]]; !found {
		return "", "", fmt.Errorf("invalid key %s", k)
	}
	return opMap[ssp[0]], sp[0], nil
}

// ToSQLFilter 从Http Request中获取查询的条件
// Http Request的Query为status[in]=prerun,runnning&entity=app-01
// 转换为的条件为 status IN ? AND entity = ?, []string{"prerun", "runnning"}, "app-01"
func ToSQLFilter(ctx context.Context, httpReq *http.Request) (*SQLFilter, error) {
	sqlFilter := &SQLFilter{
		Limit:   10000,
		Offset:  1,
		OrderBy: "asc",
	}
	condList := []string{}
	var err error
	for k, vals := range httpReq.URL.Query() {
		switch k {
		case "offset":
			if sqlFilter.Offset, err = strconv.ParseInt(vals[0], 10, 64); err != nil {
				return nil, err
			}
			continue
		case "limit":
			if sqlFilter.Limit, err = strconv.ParseInt(vals[0], 10, 64); err != nil {
				return nil, err
			}
			continue
		case "orderby":
			if vals[0] != "asc" && vals[0] != "desc" {
				return nil, fmt.Errorf("order by must be asc or desc")
			}
			sqlFilter.OrderBy = vals[0]
			continue
		}
		op, k, err := parseKey(k)
		if err != nil {
			return nil, err
		}
		condList = append(condList, k+" "+op+" ?")
		if op == "IN" {
			sqlFilter.A = append(sqlFilter.A, strings.Split(vals[0], ","))
		} else {
			sqlFilter.A = append(sqlFilter.A, vals[0])
		}
	}
	sqlFilter.Fmt = strings.Join(condList, " AND ")
	return sqlFilter, nil
}
