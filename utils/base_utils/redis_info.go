/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* redis_info.go */
/*
modification history
--------------------
2022/11/15 , by <PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com) , create
*/
/*
DESCRIPTION
todo
*/

package base_utils

// GetRedisInfo
// map[Section][Key]Val
// func GetRedisInfo(ctx context.Context, floatingIP string, port string, password string, section ...string) (map[string]map[string]string, error) {
//	c := single_redis.NewClient(floatingIP, port,
//		single_redis.WithPassword(password),
//	)
//	defer c.Close()
//	redisInfo, err := c.Info(ctx, section...).Result()
//	if err != nil {
//		return nil, err
//	}
//	redisInfo = strings.Replace(redisInfo, "\r", "", -1)
//	ret := make(map[string]map[string]string, 0)
//	index := ""
//	for _, line := range strings.Split(redisInfo, "\n") {
//		if strings.Contains(line, "#") {
//			section := strings.TrimSpace(line[1:])
//			if len(section) == 0 {
//				continue
//			}
//			if _, ok := ret[section]; ok {
//				logger.SdkLogger.Trace(ctx, "duplicate section:%s\n", section)
//				continue
//			}
//			ret[section] = make(map[string]string, 0)
//			index = section
//		} else if strings.Contains(line, ":") {
//			if index == "" {
//				logger.SdkLogger.Trace(ctx, "no section now")
//				continue
//			}
//			kv := strings.Split(line, ":")
//			if len(kv) == 0 {
//				continue
//			}
//			k := kv[0]
//			v := ""
//			if len(kv) >= 2 {
//				v = strings.Join(kv[1:], ":")
//			}
//			if _, ok := ret[index][k]; ok {
//				logger.SdkLogger.Trace(ctx, "duplicate key\n")
//				continue
//			}
//			ret[index][k] = v
//		}
//	}
//	return ret, nil
// }
