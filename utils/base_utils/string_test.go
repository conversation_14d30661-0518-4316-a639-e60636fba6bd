package base_utils

import (
	"reflect"
	"testing"
)

func TestAddslashes(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Addslashes(tt.args.str); got != tt.want {
				t.<PERSON>rrorf("Addslashes() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChr(t *testing.T) {
	type args struct {
		ascii int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Chr(tt.args.ascii); got != tt.want {
				t.Errorf("Chr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChunkSplit(t *testing.T) {
	type args struct {
		body     string
		chunklen uint
		end      string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ChunkSplit(tt.args.body, tt.args.chunklen, tt.args.end); got != tt.want {
				t.Errorf("ChunkSplit() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCompressJson(t *testing.T) {
	type args struct {
		input string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CompressJson(tt.args.input); got != tt.want {
				t.Errorf("CompressJson() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCrc32(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want uint32
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Crc32(tt.args.str); got != tt.want {
				t.Errorf("Crc32() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestExplode(t *testing.T) {
	type args struct {
		delimiter string
		str       string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Explode(tt.args.delimiter, tt.args.str); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Explode() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFormat(t *testing.T) {
	type args struct {
		v interface{}
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Format(tt.args.v); got != tt.want {
				t.Errorf("Format() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFormatText(t *testing.T) {
	type args struct {
		text string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FormatText(tt.args.text); got != tt.want {
				t.Errorf("FormatText() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestHTMLEntityDecode(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := HTMLEntityDecode(tt.args.str); got != tt.want {
				t.Errorf("HTMLEntityDecode() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestHtmlentities(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Htmlentities(tt.args.str); got != tt.want {
				t.Errorf("Htmlentities() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsPrintable(t *testing.T) {
	type args struct {
		v interface{}
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsPrintable(tt.args.v); got != tt.want {
				t.Errorf("IsPrintable() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestJSONDecode(t *testing.T) {
	type args struct {
		data []byte
		val  interface{}
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := JSONDecode(tt.args.data, tt.args.val); (err != nil) != tt.wantErr {
				t.Errorf("JSONDecode() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestJSONEncode(t *testing.T) {
	type args struct {
		val interface{}
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := JSONEncode(tt.args.val)
			if (err != nil) != tt.wantErr {
				t.Errorf("JSONEncode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("JSONEncode() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLcfirst(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Lcfirst(tt.args.str); got != tt.want {
				t.Errorf("Lcfirst() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLevenshtein(t *testing.T) {
	type args struct {
		str1    string
		str2    string
		costIns int
		costRep int
		costDel int
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Levenshtein(tt.args.str1, tt.args.str2, tt.args.costIns, tt.args.costRep, tt.args.costDel); got != tt.want {
				t.Errorf("Levenshtein() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLtrim(t *testing.T) {
	type args struct {
		str           string
		characterMask []string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Ltrim(tt.args.str, tt.args.characterMask...); got != tt.want {
				t.Errorf("Ltrim() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMbStrlen(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MbStrlen(tt.args.str); got != tt.want {
				t.Errorf("MbStrlen() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMd5(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Md5(tt.args.str); got != tt.want {
				t.Errorf("Md5() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMd5File(t *testing.T) {
	type args struct {
		path string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := Md5File(tt.args.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("Md5File() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("Md5File() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNl2br(t *testing.T) {
	type args struct {
		str     string
		isXhtml bool
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Nl2br(tt.args.str, tt.args.isXhtml); got != tt.want {
				t.Errorf("Nl2br() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNumberFormat(t *testing.T) {
	type args struct {
		number       float64
		decimals     uint
		decPoint     string
		thousandsSep string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NumberFormat(tt.args.number, tt.args.decimals, tt.args.decPoint, tt.args.thousandsSep); got != tt.want {
				t.Errorf("NumberFormat() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrd(t *testing.T) {
	type args struct {
		char string
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Ord(tt.args.char); got != tt.want {
				t.Errorf("Ord() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestParseStr(t *testing.T) {
	type args struct {
		encodedString string
		result        map[string]interface{}
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ParseStr(tt.args.encodedString, tt.args.result); (err != nil) != tt.wantErr {
				t.Errorf("ParseStr() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestQuotemeta(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Quotemeta(tt.args.str); got != tt.want {
				t.Errorf("Quotemeta() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRtrim(t *testing.T) {
	type args struct {
		str           string
		characterMask []string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Rtrim(tt.args.str, tt.args.characterMask...); got != tt.want {
				t.Errorf("Rtrim() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSelectColumn(t *testing.T) {
	type args struct {
		i interface{}
		c string
	}
	type test struct {
		name  string
		phone int64
	}
	tests := []struct {
		name  string
		args  args
		wantS []string
	}{
		{
			name: "string test",
			args: args{
				i: []*test{
					{
						name:  "abc",
						phone: 121231,
					},
					{
						name:  "def",
						phone: 12314,
					},
				},
				c: "name",
			},
			wantS: []string{"abc", "def"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotS := SelectColumn(tt.args.i, tt.args.c); !reflect.DeepEqual(gotS, tt.wantS) {
				t.Errorf("SelectColumn() = %v, want %v", gotS, tt.wantS)
			}
		})
	}
}

func TestSha1(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Sha1(tt.args.str); got != tt.want {
				t.Errorf("Sha1() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSha1File(t *testing.T) {
	type args struct {
		path string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := Sha1File(tt.args.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("Sha1File() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("Sha1File() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSimilarText(t *testing.T) {
	type args struct {
		first   string
		second  string
		percent *float64
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SimilarText(tt.args.first, tt.args.second, tt.args.percent); got != tt.want {
				t.Errorf("SimilarText() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSoundex(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := Soundex(tt.args.str)
			if (err != nil) != tt.wantErr {
				t.Errorf("Soundex() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("Soundex() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStrRepeat(t *testing.T) {
	type args struct {
		input      string
		multiplier int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := StrRepeat(tt.args.input, tt.args.multiplier); got != tt.want {
				t.Errorf("StrRepeat() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStrReplace(t *testing.T) {
	type args struct {
		search  string
		replace string
		subject string
		count   int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := StrReplace(tt.args.search, tt.args.replace, tt.args.subject, tt.args.count); got != tt.want {
				t.Errorf("StrReplace() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStrShuffle(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := StrShuffle(tt.args.str); got != tt.want {
				t.Errorf("StrShuffle() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStrWordCount(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := StrWordCount(tt.args.str); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("StrWordCount() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStripos(t *testing.T) {
	type args struct {
		haystack string
		needle   string
		offset   int
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Stripos(tt.args.haystack, tt.args.needle, tt.args.offset); got != tt.want {
				t.Errorf("Stripos() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStripslashes(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Stripslashes(tt.args.str); got != tt.want {
				t.Errorf("Stripslashes() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStrlen(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Strlen(tt.args.str); got != tt.want {
				t.Errorf("Strlen() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStrpos(t *testing.T) {
	type args struct {
		haystack string
		needle   string
		offset   int
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Strpos(tt.args.haystack, tt.args.needle, tt.args.offset); got != tt.want {
				t.Errorf("Strpos() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStrrev(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Strrev(tt.args.str); got != tt.want {
				t.Errorf("Strrev() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStrripos(t *testing.T) {
	type args struct {
		haystack string
		needle   string
		offset   int
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Strripos(tt.args.haystack, tt.args.needle, tt.args.offset); got != tt.want {
				t.Errorf("Strripos() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStrrpos(t *testing.T) {
	type args struct {
		haystack string
		needle   string
		offset   int
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Strrpos(tt.args.haystack, tt.args.needle, tt.args.offset); got != tt.want {
				t.Errorf("Strrpos() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStrstr(t *testing.T) {
	type args struct {
		haystack string
		needle   string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Strstr(tt.args.haystack, tt.args.needle); got != tt.want {
				t.Errorf("Strstr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStrtolower(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Strtolower(tt.args.str); got != tt.want {
				t.Errorf("Strtolower() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStrtoupper(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Strtoupper(tt.args.str); got != tt.want {
				t.Errorf("Strtoupper() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStrtr(t *testing.T) {
	type args struct {
		haystack string
		params   []interface{}
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Strtr(tt.args.haystack, tt.args.params...); got != tt.want {
				t.Errorf("Strtr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSubstr(t *testing.T) {
	type args struct {
		str    string
		start  uint
		length int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Substr(tt.args.str, tt.args.start, tt.args.length); got != tt.want {
				t.Errorf("Substr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTrim(t *testing.T) {
	type args struct {
		str           string
		characterMask []string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Trim(tt.args.str, tt.args.characterMask...); got != tt.want {
				t.Errorf("Trim() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUcfirst(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Ucfirst(tt.args.str); got != tt.want {
				t.Errorf("Ucfirst() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUcwords(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Ucwords(tt.args.str); got != tt.want {
				t.Errorf("Ucwords() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUniqid(t *testing.T) {
	type args struct {
		prefix string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Uniqid(tt.args.prefix); got != tt.want {
				t.Errorf("Uniqid() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWordwrap(t *testing.T) {
	type args struct {
		str   string
		width uint
		br    string
		cut   bool
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := Wordwrap(tt.args.str, tt.args.width, tt.args.br, tt.args.cut)
			if (err != nil) != tt.wantErr {
				t.Errorf("Wordwrap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("Wordwrap() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGenerateRandomString(t *testing.T) {
	type args struct {
		n int
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "Test with positive length",
			args:    args{n: 10},
			wantErr: false,
		},
		{
			name:    "Test with zero length",
			args:    args{n: 0},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GenerateRandomString(tt.args.n)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateRandomString() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if len(got) != tt.args.n {
				t.Errorf("GenerateRandomString() got = %v, want %v", len(got), tt.args.n)
			}
		})
	}
}
