// Copyright(C) 2024 Baidu Inc. All Rights Reserved.
// Author: <PERSON><PERSON> (<EMAIL>)
// Date: 2024/10/29

package base_utils

import "testing"

func TestStringArrayChunk(t *testing.T) {
	t.Log("TestStringArrayChunk")
	testCases := [][]string{
		{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"},
		{"a", "b", "c", "d", "e", "f", "g", "h", "i"},
		{"a", "b", "c", "d", "e", "f", "g", "h"},
		{"a", "b", "c", "d", "e", "f", "g"},
		{"a", "b", "c", "d", "e", "f"},
	}
	t.Log("2")
	for _, testCase := range testCases {
		t.Log("testcase:", testCase)
		chunks, err := StringArrayChunk(testCase, 2)
		if err != nil {
			t.Fatalf("StringArrayChunk failed: %v", err)
		}
		lens := 0
		for _, chunk := range chunks {
			lens += len(chunk)
		}
		if lens != len(testCase) {
			t.Fatalf("StringArrayChunk sum len not match %v", chunks)
		}
		t.Log("result:", chunks)
	}
	t.Log("3")
	for _, testCase := range testCases {
		t.Log("testcase:", testCase)
		chunks, err := StringArrayChunk(testCase, 3)
		if err != nil {
			t.Fatalf("StringArrayChunk failed: %v", err)
		}
		lens := 0
		for _, chunk := range chunks {
			lens += len(chunk)
		}
		if lens != len(testCase) {
			t.Fatalf("StringArrayChunk sum len not match %v", chunks)
		}
		t.Log("result:", chunks)
	}
	t.Log("4")
	for _, testCase := range testCases {
		t.Log("testcase:", testCase)
		chunks, err := StringArrayChunk(testCase, 4)
		if err != nil {
			t.Fatalf("StringArrayChunk failed: %v", err)
		}
		lens := 0
		for _, chunk := range chunks {
			lens += len(chunk)
		}
		if lens != len(testCase) {
			t.Fatalf("StringArrayChunk sum len not match %v", chunks)
		}
		t.Log("result:", chunks)
	}
	t.Log("100")
	for _, testCase := range testCases {
		t.Log("testcase:", testCase)
		chunks, err := StringArrayChunk(testCase, 100)
		if err != nil {
			t.Fatalf("StringArrayChunk failed: %v", err)
		}
		lens := 0
		for _, chunk := range chunks {
			lens += len(chunk)
		}
		if lens != len(testCase) {
			t.Fatalf("StringArrayChunk sum len not match %v", chunks)
		}
		t.Log("result:", chunks)
	}
}
