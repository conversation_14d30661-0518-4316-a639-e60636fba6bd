package base_utils

import (
	"testing"

	"github.com/spf13/cast"
)

type versionTestCase struct {
	Version1     string
	Version2     string
	ExpectResult int
}

type versionCompareTestCase struct {
	Version1     string
	Version2     string
	ExpectResult bool
}

func TestCompareVersion(t *testing.T) {
	testCases := []versionTestCase{
		{
			Version1:     "1.0",
			Version2:     "1.0",
			ExpectResult: 0,
		},
		{
			Version1:     "1.0.1",
			Version2:     "1.0",
			ExpectResult: 0,
		},
		{
			Version1:     "1",
			Version2:     "1",
			ExpectResult: 0,
		},
		{
			Version1:     "",
			Version2:     "",
			ExpectResult: 0,
		},
		{
			Version1:     "1.0",
			Version2:     "0.1",
			ExpectResult: 1,
		},
		{
			Version1:     "6.06",
			Version2:     "6.9",
			ExpectResult: -1,
		},
	}

	for _, testCase := range testCases {
		result := CompareVersion(testCase.Version1, testCase.Version2)
		if result != testCase.ExpectResult {
			t.<PERSON><PERSON><PERSON>("wrong compare result, ver1:%s, ver2%s, expectResult:%d, got:%d",
				testCase.Version1, testCase.Version2, testCase.ExpectResult, result)
		}
	}
}

func TestIsVersionGt(t *testing.T) {
	testCases := []versionCompareTestCase{
		{
			Version1:     "1.0",
			Version2:     "1.0",
			ExpectResult: false,
		},
		{
			Version1:     "1.0.1",
			Version2:     "1.0",
			ExpectResult: false,
		},
		{
			Version1:     "1",
			Version2:     "1",
			ExpectResult: false,
		},
		{
			Version1:     "",
			Version2:     "",
			ExpectResult: false,
		},
		{
			Version1:     "1.0",
			Version2:     "0.1",
			ExpectResult: true,
		},
		{
			Version1:     "6.06",
			Version2:     "6.9",
			ExpectResult: false,
		},
	}

	for _, testCase := range testCases {
		result := IsVersionGt(testCase.Version1, testCase.Version2)
		if result != testCase.ExpectResult {
			t.Errorf("wrong compare result, ver1:%s, ver2%s, expectResult:%s, got:%s",
				testCase.Version1, testCase.Version2, cast.ToString(testCase.ExpectResult), cast.ToString(result))
		}
	}
}

func TestIsVersionEq(t *testing.T) {
	testCases := []versionCompareTestCase{
		{
			Version1:     "1.0",
			Version2:     "1.0",
			ExpectResult: true,
		},
		{
			Version1:     "1.0.1",
			Version2:     "1.0",
			ExpectResult: true,
		},
		{
			Version1:     "1",
			Version2:     "1",
			ExpectResult: true,
		},
		{
			Version1:     "",
			Version2:     "",
			ExpectResult: true,
		},
		{
			Version1:     "1.0",
			Version2:     "0.1",
			ExpectResult: false,
		},
		{
			Version1:     "6.06",
			Version2:     "6.9",
			ExpectResult: false,
		},
	}

	for _, testCase := range testCases {
		result := IsVersionEq(testCase.Version1, testCase.Version2)
		if result != testCase.ExpectResult {
			t.Errorf("wrong compare result, ver1:%s, ver2%s, expectResult:%s, got:%s",
				testCase.Version1, testCase.Version2, cast.ToString(testCase.ExpectResult), cast.ToString(result))
		}
	}
}

func TestIsVersionLt(t *testing.T) {
	testCases := []versionCompareTestCase{
		{
			Version1:     "1.0",
			Version2:     "1.0",
			ExpectResult: false,
		},
		{
			Version1:     "1.0.1",
			Version2:     "1.0",
			ExpectResult: false,
		},
		{
			Version1:     "1",
			Version2:     "1",
			ExpectResult: false,
		},
		{
			Version1:     "",
			Version2:     "",
			ExpectResult: false,
		},
		{
			Version1:     "1.0",
			Version2:     "0.1",
			ExpectResult: false,
		},
		{
			Version1:     "6.06",
			Version2:     "6.9",
			ExpectResult: true,
		},
	}

	for _, testCase := range testCases {
		result := IsVersionLt(testCase.Version1, testCase.Version2)
		if result != testCase.ExpectResult {
			t.Errorf("wrong compare result, ver1:%s, ver2%s, expectResult:%s, got:%s",
				testCase.Version1, testCase.Version2, cast.ToString(testCase.ExpectResult), cast.ToString(result))
		}
	}
}

func TestIsVersionGE(t *testing.T) {
	testCases := []versionCompareTestCase{
		{
			Version1:     "1.0",
			Version2:     "1.0",
			ExpectResult: true,
		},
		{
			Version1:     "1.0.1",
			Version2:     "1.0",
			ExpectResult: true,
		},
		{
			Version1:     "1",
			Version2:     "1",
			ExpectResult: true,
		},
		{
			Version1:     "",
			Version2:     "",
			ExpectResult: true,
		},
		{
			Version1:     "1.0",
			Version2:     "0.1",
			ExpectResult: true,
		},
		{
			Version1:     "6.06",
			Version2:     "6.9",
			ExpectResult: false,
		},
	}

	for _, testCase := range testCases {
		result := IsVersionGE(testCase.Version1, testCase.Version2)
		if result != testCase.ExpectResult {
			t.Errorf("wrong compare result, ver1:%s, ver2%s, expectResult:%s, got:%s",
				testCase.Version1, testCase.Version2, cast.ToString(testCase.ExpectResult), cast.ToString(result))
		}
	}
}

func TestIsVersionLE(t *testing.T) {
	testCases := []versionCompareTestCase{
		{
			Version1:     "1.0",
			Version2:     "1.0",
			ExpectResult: true,
		},
		{
			Version1:     "1.0.1",
			Version2:     "1.0",
			ExpectResult: true,
		},
		{
			Version1:     "1",
			Version2:     "1",
			ExpectResult: true,
		},
		{
			Version1:     "",
			Version2:     "",
			ExpectResult: true,
		},
		{
			Version1:     "1.0",
			Version2:     "0.1",
			ExpectResult: false,
		},
		{
			Version1:     "6.06",
			Version2:     "6.9",
			ExpectResult: true,
		},
	}

	for _, testCase := range testCases {
		result := IsVersionLE(testCase.Version1, testCase.Version2)
		if result != testCase.ExpectResult {
			t.Errorf("wrong compare result, ver1:%s, ver2%s, expectResult:%s, got:%s",
				testCase.Version1, testCase.Version2, cast.ToString(testCase.ExpectResult), cast.ToString(result))
		}
	}
}

func TestIsVerSameLength(t *testing.T) {
	testCases := []versionCompareTestCase{
		{
			Version1:     "1.0",
			Version2:     "1.0",
			ExpectResult: true,
		},
		{
			Version1:     "1.0.1",
			Version2:     "1.0",
			ExpectResult: false,
		},
		{
			Version1:     "1",
			Version2:     "1",
			ExpectResult: true,
		},
		{
			Version1:     "",
			Version2:     "",
			ExpectResult: true,
		},
		{
			Version1:     "1.0",
			Version2:     "0.1",
			ExpectResult: true,
		},
		{
			Version1:     "6.06",
			Version2:     "6.9",
			ExpectResult: true,
		},
	}

	for _, testCase := range testCases {
		result := IsVerSameLength(testCase.Version1, testCase.Version2)
		if result != testCase.ExpectResult {
			t.Errorf("wrong compare result, ver1:%s, ver2%s, expectResult:%s, got:%s",
				testCase.Version1, testCase.Version2, cast.ToString(testCase.ExpectResult), cast.ToString(result))
		}
	}
}

func TestIsDevVer(t *testing.T) {
	testCases := []versionCompareTestCase{
		{
			Version1:     "1.0",
			ExpectResult: false,
		},
		{
			Version1:     "1.0.1",
			ExpectResult: false,
		},
		{
			Version1:     "1",
			ExpectResult: false,
		},
		{
			Version1:     "",
			ExpectResult: true,
		},
		{
			Version1:     "1.0.fake.1",
			ExpectResult: true,
		},
	}

	for _, testCase := range testCases {
		result := IsDevVer(testCase.Version1)
		if result != testCase.ExpectResult {
			t.Errorf("wrong compare result, ver1:%s, ver2%s, expectResult:%s, got:%s",
				testCase.Version1, testCase.Version2, cast.ToString(testCase.ExpectResult), cast.ToString(result))
		}
	}
}
