/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/06
 * File: time.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package base_utils TODO package function desc
package base_utils

import "time"

// 原生不支持的time format
const (
	CSTFormat     = "2006-01-02 15:04:05"
	RFC822Format  = "Mon, 02 Jan 2006 15:04:05 MST"
	ISO8601Format = "2006-01-02T15:04:05Z"
)

func FormatWithCST(dateTime time.Time) string {
	return dateTime.Format(CSTFormat)
}

func FormatWithRFC822(dateTime time.Time) string {
	return dateTime.Format(RFC822Format)
}

func FormatWithISO8601(dateTime time.Time) string {
	return dateTime.Format(ISO8601Format)
}

func ParseWithISO8601(dateTime string) (time.Time, error) {
	return time.Parse(ISO8601Format, dateTime)
}


// ////////// Date/Time Functions ////////////

// Time time()
func Time() int64 {
	return time.Now().Unix()
}

// Strtotime strtotime()
// Strtotime("02/01/2006 15:04:05", "02/01/2016 15:04:05") == 1451747045
// Strtotime("3 04 PM", "8 41 PM") == -62167144740
func Strtotime(format, strtime string) (int64, error) {
	t, err := time.Parse(format, strtime)
	if err != nil {
		return 0, err
	}
	return t.Unix(), nil
}

// Date date()
// Date("02/01/2006 15:04:05 PM", 1524799394)
func Date(format string, timestamp int64) string {
	return time.Unix(timestamp, 0).Format(format)
}

// Checkdate checkdate()
// Validate a Gregorian date
func Checkdate(month, day, year int) bool {
	if month < 1 || month > 12 || day < 1 || day > 31 || year < 1 || year > 32767 {
		return false
	}
	switch month {
	case 4, 6, 9, 11:
		if day > 30 {
			return false
		}
	case 2:
		// leap year
		if year%4 == 0 && (year%100 != 0 || year%400 == 0) {
			if day > 29 {
				return false
			}
		} else if day > 28 {
			return false
		}
	}

	return true
}

// Sleep sleep()
func Sleep(t int64) {
	time.Sleep(time.Duration(t) * time.Second)
}

// Usleep usleep()
func Usleep(t int64) {
	time.Sleep(time.Duration(t) * time.Microsecond)
}
