/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (<EMAIL>)
 * Date: 2021/12/16
 * File: reqid.go
 */

/*
 * DESCRIPTION
 *   在context中添加、获取、修改reqid
 */

package base_utils

import (
	"context"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"icode.baidu.com/baidu/gdp/logit"
)

func SetReqID(ctx context.Context, reqId string) {
	logit.SetLogID(ctx, reqId)
}

func GetReqID(ctx context.Context) string {
	f := logit.FindLogIDField(ctx)
	if f == nil {
		return uuid.New().String() + "_" + strconv.FormatInt(time.Now().UnixNano(), 10)
	} else {
		if strings.Contains(f.Value().(string), "_") {
			logidList := strings.Split(f.Value().(string), "_")
			if len(logidList) > 0 && len(logidList[0]) > 0 {
				return logidList[0] + "_" + strconv.FormatInt(time.Now().UnixNano(), 10)
			}
			return f.Value().(string)
		}
		return f.Value().(string) + "_" + strconv.FormatInt(time.Now().UnixNano(), 10)
	}
}
