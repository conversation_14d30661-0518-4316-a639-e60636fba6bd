package base_utils

import (
	"strconv"
	"strings"
)

// CompareVersion 比较x.x.x.x形势版本号的
// 注意1，如果长度不一样，则按最短的一组比较，比如 "1.0.1", "1.0", 会返回0
// 注意2，strconv.ParseInt("如果这不是数字而是其他字符串比如fake", 10, 0)，这个方法会返回0
// ver1==ver2 0
// ver1>ver2 1
// ver2<ver2 -1
func CompareVersion(ver1, ver2 string) int {
	parts1 := strings.Split(ver1, ".")
	parts2 := strings.Split(ver2, ".")

	for i := 0; i < len(parts1) && i < len(parts2); i++ {
		v1, _ := strconv.ParseInt(parts1[i], 10, 0)
		v2, _ := strconv.ParseInt(parts2[i], 10, 0)
		if v1 > v2 {
			return 1
		}
		if v1 < v2 {
			return -1
		}
	}

	return 0
}

// IsDevVer 判断是不是一个开发包
func IsDevVer(ver string) bool {
	parts1 := strings.Split(ver, ".")
	for _, part := range parts1 {
		if !IsNumeric(part) {
			return true
		}
	}
	return false
}

// IsVerSameLength 判断比较的版本号位数是否一致
func IsVerSameLength(ver1, ver2 string) bool {
	return len(strings.Split(ver1, ".")) == len(strings.Split(ver2, "."))
}

// IsVersionGt 判断 v1 > v2
func IsVersionGt(ver1, ver2 string) bool {
	return CompareVersion(ver1, ver2) == 1
}

// IsVersionEq 判断 v1 == v2
func IsVersionEq(ver1, ver2 string) bool {
	return CompareVersion(ver1, ver2) == 0
}

// IsVersionLt 判断 v1 < v2
func IsVersionLt(ver1, ver2 string) bool {
	return CompareVersion(ver1, ver2) == -1
}

// IsVersionGE 判断 v1 >= v2
func IsVersionGE(ver1, ver2 string) bool {
	return CompareVersion(ver1, ver2) >= 0
}

// IsVersionLE 判断 v1 <= v2
func IsVersionLE(ver1, ver2 string) bool {
	return CompareVersion(ver1, ver2) <= 0
}
