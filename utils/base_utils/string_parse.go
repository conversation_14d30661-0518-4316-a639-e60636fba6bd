/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
解析特定字符串
*/

package base_utils

import (
	"fmt"
	"strings"
)

func ParseBosAccess(access string) (bucketName string, objectName string, err error) {
	i := 0
	for _, chunk := range(strings.Split(access, "/")) {
		if chunk == "" {
			continue
		}
		if i == 0 && chunk != "bos:" {
			err = fmt.Errorf("invalid bos access format %s", access); return
		}
		if i == 1 {
			bucketName = chunk
		} else if i > 1 {
			if objectName != "" {
				objectName += "/" + chunk
			} else {
				objectName = chunk
			}
		}
		i++
	}
	return
}
