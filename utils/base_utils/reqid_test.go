/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (<EMAIL>)
 * Date: 2021/12/16
 * File: reqid.go
 */

/*
 * DESCRIPTION
 *   在context中添加、获取、修改reqid
 */

package base_utils_test

import (
	"context"
	"strings"
	"testing"

	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func TestReqID(t *testing.T) {
	ctx := logit.WithContext(context.Background())
	base_utils.SetReqID(ctx, "testreqid-01")
	if !strings.HasPrefix(base_utils.GetReqID(ctx), "testreqid-01") {
		t.Fatalf("expect prefix testreqid-01, actual %s", base_utils.GetReqID(ctx))
	}
	base_utils.SetReqID(ctx, "testreqid-02")
	if !strings.HasPrefix(base_utils.GetReqID(ctx), "testreqid-02") {
		t.Fatalf("expect prefix testreqid-02, actual %s", base_utils.GetReqID(ctx))
	}
}
