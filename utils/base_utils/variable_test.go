/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 2022/12/06
 * File: variable.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package base_utils TODO package function desc
package base_utils

import (
	"testing"
)

func TestFlagExists(t *testing.T) {
	type args struct {
		flags string
		flag  string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "exist",
			args: args{
				flags: "|a|b",
				flag: "a",
			},
			want: true,
		},
		{
			name: "not exist",
			args: args{
				flags: "|a|b",
				flag: "c",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FlagExists(tt.args.flags, tt.args.flag); got != tt.want {
				t.Errorf("FlagExists() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFlagAdd(t *testing.T) {
	type args struct {
		flags string
		flag  string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "add empty",
			args: args{
				flags: "",
				flag: "a",
			},
			want: "|a",
		},
		{
			name: "add new",
			args: args{
				flags: "|a",
				flag: "b",
			},
			want: "|a|b",
		},
		{
			name: "add exist",
			args: args{
				flags: "|a|b",
				flag: "a",
			},
			want: "|a|b",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FlagAdd(tt.args.flags, tt.args.flag); got != tt.want {
				t.Errorf("FlagAdd() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFlagDel(t *testing.T) {
	type args struct {
		flags string
		flag  string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "del empty",
			args: args{
				flags: "",
				flag: "a",
			},
			want: "",
		},
		{
			name: "del exist",
			args: args{
				flags: "|a",
				flag: "a",
			},
			want: "",
		},
		{
			name: "del exist",
			args: args{
				flags: "|a|b",
				flag: "b",
			},
			want: "|a",
		},
		{
			name: "del not exist",
			args: args{
				flags: "|a|b",
				flag: "c",
			},
			want: "|a|b",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FlagDel(tt.args.flags, tt.args.flag); got != tt.want {
				t.Errorf("FlagDel() = %v, want %v", got, tt.want)
			}
		})
	}
}
