/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/10, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
将http Request的Query转换为SQL查询的条件
*/

package base_utils

import (
	"context"
	"net/http"
	"testing"
)

func TestFilter(t *testing.T) {
	httpReq, _ := http.NewRequest("GET", "http://www.baidu.com", nil)
	q := httpReq.URL.Query()
	q.Add("status[in]", "running,prerun")
	q.Add("entity", "app-01")
	httpReq.URL.RawQuery = q.Encode()
	sqlFilter, err := ToSQLFilter(context.Background(), httpReq)
	if err != nil {
		t.Fatalf("get filter failed; err: %s", err.Error())
	}
	if sqlFilter.Fmt != "entity = ? AND status IN ?" && sqlFilter.Fmt != "status IN ? AND entity = ?" {
		t.Fatalf("expect entity = ? AND status IN ?; actual %s", sqlFilter.Fmt)
	}
}
