package xrm

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const (
	TestServerID = "0.scs-bj-jdbkqiqejhhr-bj_hjeabvcqyjxp_1-AZONE-bjdd.scs"
)

type testReqFunc func(t *testing.T, ctx context.Context, method, path string,
	queries, posts, headers, result interface{}) error

type dummyCaller struct {
	sdk_utils.RalCaller
	t           *testing.T
	testReqFunc testReqFunc
}

func (c *dummyCaller) WithOption(opts ...sdk_utils.ROption) sdk_utils.RalCaller {
	return c
}

func (c *dummyCaller) HttpRequest(ctx context.Context, method, path string, queries, posts, headers,
	result interface{}) error {
	return c.testReqFunc(c.t, ctx, method, path, queries, posts, headers, result)
}

func newDummyCaller(t *testing.T, httpReqFunc testReqFunc) *dummyCaller {
	return &dummyCaller{
		t:           t,
		testReqFunc: httpReqFunc,
	}
}

func TestXrmSdk_QueryServerCreationTask_InvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newXrmSdk(DefaultServiceName)

	_, err := s.QueryServerCreationTask(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.QueryServerCreationTask(ctx, &QueryServerCreationTaskRequest{
		RequestId: "",
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test requestId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}

func TestXrmSdk_CreateServer_InvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newXrmSdk(DefaultServiceName)

	_, err := s.CreateServer(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

}

func TestXrmSdk_DeleteServer_InvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newXrmSdk(DefaultServiceName)

	_, err := s.DeleteServer(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.DeleteServer(ctx, &DeleteServerRequest{})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.DeleteServer(ctx, &DeleteServerRequest{
		ServerIds: []string{},
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

}

func TestXrmSdk_UpdateServer_InvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newXrmSdk(DefaultServiceName)

	_, err := s.UpdateServer(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.UpdateServer(ctx, &UpdateServerRequest{})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.UpdateServer(ctx, &UpdateServerRequest{
		ServerId: "",
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

}

func TestXrmSdk_UpdateServer_OK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newXrmSdk(DefaultServiceName)
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&UpdateServerResponse{
		Success: 1,
	}, nil, http.StatusOK))

	_, err := s.UpdateServer(ctx, &UpdateServerRequest{
		ServerId: "0.xxx.scs",
	})
	if err != nil {
		fmt.Printf("%s\n", err.Error())
	}

}

func TestXrmSdk_ShowServer_InvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newXrmSdk(DefaultServiceName)

	_, err := s.ShowServer(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.ShowServer(ctx, &ShowServerRequest{})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.ShowServer(ctx, &ShowServerRequest{
		ServerID: "",
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}

func TestXrmSdk_ShowServer_OK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newXrmSdk(DefaultServiceName)
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ShowServerResponse{
		Success: 1,
	}, nil, http.StatusOK))

	_, err := s.ShowServer(ctx, &ShowServerRequest{
		ServerID: TestServerID,
	})
	if err != nil {
		t.Errorf("[%s] test ShowServer fail", t.Name())
	} else {
		fmt.Printf("[%s] test ShowServer OK", t.Name())
	}
}

func TestXrmSdk_ShowServer_ERR(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newXrmSdk(DefaultServiceName)
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ShowServerResponse{
		Success: 0,
	}, nil, http.StatusOK))

	_, err := s.ShowServer(ctx, &ShowServerRequest{
		ServerID: TestServerID,
	})
	if !cerrs.ErrXRMShowServerFail.Is(err) {
		t.Errorf("[%s] test ShowServer fail", t.Name())
	} else {
		fmt.Printf("[%s] test ShowServer OK", t.Name())
	}
}
