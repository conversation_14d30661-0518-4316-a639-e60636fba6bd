/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: zhang<PERSON>uepeng (<EMAIL>)
 * Date: 2022-06-06
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据xrm.proto生成的interface文件
 */

// Package xrm
package xrm

import (
	"context"
)

type Port struct {
	Name  string `json:"name"`
	Type  string `json:"type"`
	Min   int32  `json:"min"`
	Max   int32  `json:"max"`
	Value int32  `json:"value,omitempty"`
}

type ScheduleStrategy struct {
	MaxPerHost int64   `json:"maxPerHost"`
	MaxPerRack int64   `json:"maxPerRack"`
	Port       []*Port `json:"port"`
}

type AuthArray struct {
	Token string `json:"token,omitempty"`
}

type FlavorDiskAttr struct {
	Quota int64  `json:"quota"`
	Limit int64  `json:"limit"`
	Type  string `json:"type"`
}

type FlavorAttr struct {
	Quota int64 `json:"quota"`
	Limit int64 `json:"limit"`
}

type Flavor struct {
	DiskInGB      []*FlavorDiskAttr `json:"diskInGB"`
	CpuInCore     *FlavorAttr       `json:"cpuInCore"`
	RamInMB       *FlavorAttr       `json:"ramInMB"`
	NetworkInMbps *FlavorAttr       `json:"networkInMbps"`
}

type VolumeAttributes struct {
	ID          string `json:"id"`
	Format      bool   `json:"format"`
	SnapshotID  string `json:"snapshotId"`
	StorageType string `json:"storageType"`
}

type VolumeRequest struct {
	Type              string            `json:"Type"`
	VolumeName        string            `json:"VolumeName,omitempty"`
	VolumeSizeGBQuota int64             `json:"VolumeSizeGBQuota"`
	VolumeSizeGBLimit int64             `json:"VolumeSizeGBLimit"`
	VolumePath        string            `json:"VolumePath"`
	DiskLabels        map[string]string `json:"diskLabels,omitempty"`
	Attributes        *VolumeAttributes `json:"attributes"`
}

type VolumeResponse struct {
	Type                    string            `json:"Type"`
	VolumeName              string            `json:"VolumeName"`
	VolumeSizeMBQuota       int64             `json:"VolumeSizeMBQuota"`
	VolumeSizeMBLimit       int64             `json:"VolumeSizeMBLimit"`
	VolumePath              string            `json:"VolumePath"`
	DiskLabels              map[string]string `json:"diskLabels"`
	ExpectMountParentPath   string            `json:"ExpectMountParentPath"`
	ExpectVolumeSizeGBQuota int64             `json:"expectVolumeSizeGBQuota"`
	MountPoint              string            `json:"MountPoint"`
	DiskPath                string            `json:"DiskPath"`
	Attributes              *VolumeAttributes `json:"attributes"`
}

type AttributesRequest struct {
	VpcId   string            `json:"vpcId"`
	Region  string            `json:"region"`
	Zone    string            `json:"zone"`
	PoolId  string            `json:"poolId"`
	Label   map[string]string `json:"label,omitempty"`
	AppId   string            `json:"appId,omitempty"`
	ImageId string            `json:"imageId"`
	Volumes []*VolumeRequest  `json:"volumes"`
	// optional
	Entrypoint  []string               `json:"entrypoint"`
	StartParams map[string]interface{} `json:"startParams"`
}

type AttributesResponse struct {
	VpcId   string            `json:"vpcId"`
	Region  string            `json:"region"`
	Zone    string            `json:"zone"`
	PoolId  string            `json:"poolId"`
	Label   map[string]string `json:"label"`
	AppId   string            `json:"appId"`
	ImageId string            `json:"imageId"`
	Volumes []*VolumeResponse `json:"volumes"`
}

type ServerRequest struct {
	Flavor     *Flavor            `json:"flavor"`
	Count      int64              `json:"count"`
	Attributes *AttributesRequest `json:"attributes"`
}

type CreateServerRequest struct {
	ServiceId        string            `json:"serviceId"`
	Source           string            `json:"source"`
	ScheduleStrategy *ScheduleStrategy `json:"scheduleStrategy"`
	AuthArray        *AuthArray        `json:"authArray"`
	Servers          []*ServerRequest  `json:"servers"`
}

type CreateServerResponse struct {
	Success   int32  `json:"success"`
	Msg       string `json:"msg"`
	RequestId string `json:"requestId"`
	Code      string `json:"code"`
	Message   string `json:"message"`
}

type QueryServerCreationTaskRequest struct {
	RequestId string `json:"requestId"`
}

type StaticPort struct {
	DbPort         int32 `json:"dbPort"`
	StatPort       int32 `json:"statPort"`
	XagentPort     int32 `json:"xagentPort"`
	XagentSyncPort int32 `json:"xagentSyncPort"`
}

type ServerResponse struct {
	ServerId      string              `json:"serverId"`
	ServiceId     string              `json:"serviceId"`
	Ip            string              `json:"ip"`
	Path          string              `json:"path"`
	StaticPort    *StaticPort         `json:"staticPort"`
	Attributes    *AttributesResponse `json:"attributes"`
	CpuCoresQuota int64               `json:"cpu_cores_quota"`
	MemMbQuota    int64               `json:"mem_mb_quota"`
	DiskGb        int64               `json:"disk_gb"`
	Source        string              `json:"source"`
	// MachineLabel is array when num of elements equal 0, otherwise map
	MachineLabel interface{} `json:"machineLabel"`
}

type QueryServerCreationTaskResponseData struct {
	Success   int32             `json:"success"`
	Status    string            `json:"status"`
	Servers   []*ServerResponse `json:"servers"`
	FailedIPs []string          `json:"failed_ips"`
}

type QueryServerCreationTaskResponse struct {
	Success int32                                `json:"success"`
	Msg     string                               `json:"msg"`
	Data    *QueryServerCreationTaskResponseData `json:"data"`
}

type DeleteServerRequest struct {
	ServerIds []string `json:"server_ids"`
}

type DeleteServerResponse struct {
	Success   int32  `json:"success"`
	Msg       string `json:"msg"`
	RequestId string `json:"requestId"`
	Code      string `json:"code"`
	Message   string `json:"message"`
}

type UpdateData struct {
	RamInMB        int64 `json:"ramInMB,omitempty"`
	CpuInCore      int64 `json:"cpuInCore,omitempty"`
	RamInMBLimit   int64 `json:"ramInMBLimit,omitempty"`
	CpuInCoreLimit int64 `json:"cpuInCoreLimit,omitempty"`

	VolumeID            string `json:"volumeId,omitempty"`
	VolumeType          string `json:"volumeType,omitempty"`
	VolumePath          string `json:"volumePath,omitempty"`
	VolumeSizeInGBQuota int64  `json:"volumeSizeInGBQuota,omitempty"`
}

type UpdateServerRequest struct {
	ServerId string      `json:"serverId"`
	Type     string      `json:"type"`
	Data     *UpdateData `json:"data"`
}

type UpdateServerResponse struct {
	Success   int32  `json:"success"`
	Msg       string `json:"msg"`
	RequestId string `json:"requestId"`
	Code      string `json:"code"`
	Message   string `json:"message"`
}

type ShowServerRequest struct {
	ServerID string `json:"serverId"`
}

type ShowServerResponse struct {
	Success int32   `json:"success"`
	Msg     string  `json:"msg"`
	Rmunit  *Rmunit `json:"rmunit"`
}

type Rmunit struct {
	Status  string            `json:"status"`
	Volumes []*VolumeResponse `json:"volumes"`
}

type XrmService interface {
	CreateServer(ctx context.Context, req *CreateServerRequest) (rsp *CreateServerResponse, err error)
	QueryServerCreationTask(ctx context.Context, req *QueryServerCreationTaskRequest) (rsp *QueryServerCreationTaskResponse, err error)
	DeleteServer(ctx context.Context, req *DeleteServerRequest) (rsp *DeleteServerResponse, err error)
	UpdateServer(ctx context.Context, req *UpdateServerRequest) (rsp *UpdateServerResponse, err error)
	ShowServer(ctx context.Context, req *ShowServerRequest) (rsp *ShowServerResponse, err error)
}
