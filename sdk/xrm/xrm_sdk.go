/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-20
 * File: interface_impl.go
 */

/*
 * DESCRIPTION
 *   http://wiki.baidu.com/display/XOne/X1+XRM
 */

// Package xrm
package xrm

import (
	"context"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"net/http"
	"strings"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

// xrmSdk - XrmService implement
type xrmSdk struct {
	serviceName string
	ralCaller   sdk_utils.RalCaller
}

// NewDefaultXrmSdk - new xrmSdk instance with DefaultServiceName
func NewDefaultXrmSdk() XrmService {
	return newXrmSdk(DefaultServiceName)
}

// NewXrmSdk - new xrmSdk instance
func NewXrmSdk(serviceName string) XrmService {
	return newXrmSdk(serviceName)
}

// newXrmSdk - new xrmSdk instance
func newXrmSdk(serviceName string) *xrmSdk {
	s := &xrmSdk{
		serviceName: serviceName,
		ralCaller: sdk_utils.NewRalCaller(
			serviceName,
			sdk_utils.ROptGenCurlLog(logit.TraceLevel),
			sdk_utils.ROptEncoder(sdk_utils.JSONEncoder)),
	}

	return s
}

// SetRalCaller - 替换ralCaller(用于ut)
func (s *xrmSdk) SetRalCaller(caller sdk_utils.RalCaller) sdk_utils.RalCaller {
	last := s.ralCaller
	s.ralCaller = caller
	return last
}

func (s *xrmSdk) doRequest(ctx context.Context, actionName string, httpMethod, uri string,
	req, rsp interface{}) (err error) {

	if err = s.ralCaller.HttpRequest(ctx, httpMethod, uri, nil, req, nil, rsp); err != nil {
		logger.SdkLogger.Warning(ctx,
			"[%s] ral request fail, httpMethod: %s, uri: %s, req: %v, err: %s",
			actionName, httpMethod, uri, base_utils.Format(req), err.Error())
		return err
	}

	return
}

// isErrCodeResourceNotAvailable judge string contains specific error code ResourceNotAvailable
func isErrCodeResourceNotAvailable(s string) bool {
	return strings.Contains(s, ErrCodeResourceNotAvailable)
}

// isErrNotFoundToDelete judge string contains keyword: not found
func isErrNotFoundToDelete(s string) bool {
	return strings.Contains(s, ErrNotFoundKeyWord)
}

// CreateServer - 创建Server
func (s *xrmSdk) CreateServer(ctx context.Context, req *CreateServerRequest) (rsp *CreateServerResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CreateServerResponse{}
	err = s.doRequest(ctx, "CreateServer", http.MethodPost, ServersApi, req, rsp)
	if err != nil {
		return nil, err
	}
	if rsp.Success != 1 {
		logger.SdkLogger.Warning(ctx, "xrm create servers fail, msg: %s, %+v", rsp.Msg, rsp)
		if isErrCodeResourceNotAvailable(rsp.Msg) {
			return nil, cerrs.ErrXRMResourceNotAvailable.Errorf("CreateServer request fail: resource not available,req: %s, msg: %s",
				base_utils.Format(req), rsp.Msg)
		}
		return nil, cerrs.ErrXRMCreateServerFail.Errorf("CreateServer request fail,req: %s, msg: %s",
			base_utils.Format(req), rsp.Msg)
	}
	return
}

// QueryServerCreationTask - 查询创建Server任务结果
func (s *xrmSdk) QueryServerCreationTask(ctx context.Context, req *QueryServerCreationTaskRequest) (rsp *QueryServerCreationTaskResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.RequestId == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("null requestId")
	}

	uri := RequestApi + "/" + req.RequestId
	rsp = &QueryServerCreationTaskResponse{}
	err = s.doRequest(ctx, "QueryServerCreationTask", http.MethodGet, uri, nil, rsp)
	if err != nil {
		return nil, err
	}
	if rsp.Success != 1 {
		logger.SdkLogger.Warning(ctx, "xrm query server creation task fail, msg: %s", rsp.Msg)
		return nil, cerrs.ErrXRMQueryServerCreationTaskFail.Errorf("QueryServerCreationTask request fail,req:%s, msg: %s",
			base_utils.Format(req), rsp.Msg)
	}
	return
}

// DeleteServer - 释放Server
func (s *xrmSdk) DeleteServer(ctx context.Context, req *DeleteServerRequest) (rsp *DeleteServerResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if len(req.ServerIds) == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("serverId param is null array")
	}

	rsp = &DeleteServerResponse{}
	err = s.doRequest(ctx, "DeleteServer", http.MethodDelete, ServersApi, req.ServerIds, rsp)
	if err != nil {
		return nil, err
	}
	if rsp.Success != 1 {
		logger.SdkLogger.Warning(ctx, "xrm delete server fail, msg: %s", rsp.Msg)
		if isErrNotFoundToDelete(rsp.Msg) {
			return nil, cerrs.ErrXRMResourceNotFound.Errorf("DeleteServer request fail: resource not found,req: %s, msg: %s",
				base_utils.Format(req.ServerIds), rsp.Msg)
		}
		return nil, cerrs.ErrXRMDeleteServerFail.Errorf("DeleteServer request fail, req: %s, msg: %s",
			base_utils.Format(req.ServerIds), rsp.Msg)
	}
	return
}

// UpdateServer - 支持 resizeServer, resizeServerVolume, detachServerVolume
func (s *xrmSdk) UpdateServer(ctx context.Context, req *UpdateServerRequest) (rsp *UpdateServerResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.ServerId == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("serverId param is null")
	}

	uri := ServerApi + "/" + req.ServerId
	rsp = &UpdateServerResponse{}
	err = s.doRequest(ctx, "UpdateServer", http.MethodPut, uri, req, rsp)
	if err != nil {
		return nil, err
	}
	if rsp.Success != 1 {
		logger.SdkLogger.Warning(ctx, "xrm update server fail, msg: %s", rsp.Msg)
		return nil, cerrs.ErrXRMUpdateServerFail.Errorf("UpdateServer request fail, req: %s, msg: %s",
			base_utils.Format(req), rsp.Msg)
	}
	return

}

// ShowServer - 查询Server详情
func (s *xrmSdk) ShowServer(ctx context.Context, req *ShowServerRequest) (rsp *ShowServerResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.ServerID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("null serverID")
	}

	uri := ServerApi + "/" + req.ServerID
	rsp = &ShowServerResponse{}
	err = s.doRequest(ctx, "ShowServer", http.MethodGet, uri, nil, rsp)
	if err != nil {
		return nil, err
	}
	if rsp.Success != 1 {
		logger.SdkLogger.Warning(ctx, "xrm show server fail, msg: %s", rsp.Msg)
		return nil, cerrs.ErrXRMShowServerFail.Errorf("Show Server request fail,req:%s, msg: %s",
			base_utils.Format(req), rsp.Msg)
	}
	return
}
