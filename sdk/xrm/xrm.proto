//protofsg -with_context -json_tag=1 xrm.proto interface.go
package x1base.sdk.xrm;
option cc_generic_services = true;

// http://wiki.baidu.com/display/XOne/X1+XRM

message Port {
    optional string name = 1;
    optional string type = 2;
    optional int32 min = 3;
    optional int32 max = 4;
    //@inject_tag json:",omitempty"
    optional int32 value = 5;
}

message ScheduleStrategy {
    optional int64 maxPerHost = 1;
    optional int64 maxPerRack = 2;
    repeated Port port = 3;
}

message AuthArray {
    //@inject_tag json:",omitempty"
    optional string token = 1;
}

message FlavorDiskAttr {
    optional int64 quota = 1;
    optional int64 limit = 2;
    optional string type = 3;
}

message FlavorAttr {
    optional int64 quota = 1;
    optional int64 limit = 2;
}

message Flavor {
    repeated FlavorDiskAttr diskInGB = 1;
    optional FlavorAttr cpuInCore = 2;
    optional FlavorAttr ramInMB = 3;
    optional FlavorAttr networkInMbps = 4;
}

// size in request is gb, in response is mb
message VolumeRequest {
    optional string Type = 1;
    //@inject_tag json:",omitempty"
    optional string VolumeName=2;
    optional int64 VolumeSizeGBQuota = 3;
    optional int64 VolumeSizeGBLimit = 4;
    optional string VolumePath = 5;
    //@inject_tag json:",omitempty"
    map<string, string> diskLabels = 6;
}

message VolumeResponse {
    optional string Type = 1;
    optional string VolumeName=2;
    optional int64 VolumeSizeMBQuota = 3;
    optional int64 VolumeSizeMBLimit = 4;
    optional string VolumePath = 5;
    map<string, string> diskLabels = 6;
    optional string ExpectMountParentPath = 7;
    optional string MountPoint = 8;
    optional string DiskPath = 9;
}

message AttributesRequest {
    optional string vpcId = 1;
    optional string region = 2;
    optional string zone = 3;
    optional string poolId =4;
    //@inject_tag json:",omitempty"
    map<string, string> label = 5;
    //@inject_tag json:",omitempty"
    optional string appId = 6;
    optional string imageId = 7;
    repeated VolumeRequest volumes = 8;
}

message AttributesResponse {
    optional string vpcId = 1;
    optional string region = 2;
    optional string zone = 3;
    optional string poolId =4;
    map<string, string> label = 5;
    optional string appId = 6;
    optional string imageId = 7;
    repeated VolumeResponse volumes = 8;
}

message ServerRequest {
    optional Flavor flavor = 1;
    optional int64 count = 2;
    optional AttributesRequest attributes = 3;
}

message CreateServerRequest {
    optional string serviceId = 1;
    optional string source = 2;
    optional ScheduleStrategy scheduleStrategy = 3;
    optional AuthArray authArray = 4;
    repeated ServerRequest servers = 5;
}

message CreateServerResponse {
    optional int32 success = 1;
    optional string msg = 2;
    optional string requestId = 3;
    optional string code =4;
    optional string message =5;
}

message QueryServerCreationTaskRequest {
    optional string requestId = 1;
}

message StaticPort {
    optional int32 dbPort = 1;
    optional int32 statPort = 2;
    optional int32 xagentPort = 3;
    optional int32 xagentSyncPort = 4;
}

message ServerResponse {
    optional string serverId = 1;
    optional string serviceId = 2;
    optional string ip = 3;
    optional string path = 4;
    optional StaticPort staticPort = 5;
    optional AttributesResponse attributes = 6;
    optional int64 cpu_cores_quota = 7;
    optional int64 mem_mb_quota = 8;
    optional int64 disk_gb = 9;
    optional string source = 10;
    // machineLabel is array when num of elements equal 0, otherwise map
    optional interface machineLabel = 11;
}

message QueryServerCreationTaskResponseData {
    optional int32 success = 1;
    optional string status = 2;
    repeated ServerResponse servers = 3;
}

message QueryServerCreationTaskResponse {
    optional int32 success = 1;
    optional string msg =2;
    optional QueryServerCreationTaskResponseData data = 3;
}

message DeleteServerRequest {
    repeated string server_ids = 1;
}

message DeleteServerResponse {
    optional int32 success = 1;
    optional string msg = 2;
    optional string requestId = 3;
    optional string code =4;
    optional string message =5;
}

message ResizeFlavor {
    //@inject_tag json:",omitempty"
    optional int64 ramInMB = 1;
    //@inject_tag json:",omitempty"
    optional int64 cpuInCore = 2;
    //@inject_tag json:",omitempty"
    optional int64 ramInMBLimit = 3;
    //@inject_tag json:",omitempty"
    optional int64 cpuInCoreLimit = 4;
}

message ResizeServerRequest {
    optional string serverId = 1;
    optional string type = 2;
    optional ResizeFlavor data = 3;
}

message ResizeServerResponse {
    optional int32 success = 1;
    optional string msg = 2;
    optional string requestId = 3;
    optional string code =4;
    optional string message =5;
}

service XrmService {
    rpc create_server(CreateServerRequest) returns (CreateServerResponse);
    rpc query_server_creation_task(QueryServerCreationTaskRequest) returns (QueryServerCreationTaskResponse);
    //rpc query_server(QueryServerRequest) returns (QueryServerResponse);
    rpc delete_server(DeleteServerRequest) returns (DeleteServerResponse);
    rpc resize_server(ResizeServerRequest) returns (ResizeServerResponse);
}
