/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: z<PERSON><PERSON>gyou
 * Date: 2022-5-20
 * File: x1_resource_sdk.go
 */

/*
 * DESCRIPTION
 *   x1 resouce
 */

// Package x1_resource

package x1_resource

import (
	"context"
	"net/http"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type X1ResourceSdk struct {
	ralCaller sdk_utils.RalCaller
}

func NewDefaultX1ResourceSdk() X1ResourceService {
	return newX1ResourceSdk(DefaultServiceName)
}

func NewX1ResourceSdk(serviceName string) X1ResourceService {
	return newX1ResourceSdk(serviceName)
}

func newX1ResourceSdk(serviceName string) *X1ResourceSdk {
	if serviceName == "" {
		panic("invalid service name")
	}

	s := &X1ResourceSdk{
		ralCaller: sdk_utils.NewRalCaller(
			serviceName,
			sdk_utils.ROptGenCurlLog(logit.TraceLevel),
			sdk_utils.ROptPrepareChecker(
				func(ctx context.Context, httpRsp *sdk_utils.HttpResponse, invoker sdk_utils.PrepareCheckFunc) error {
					logger.SdkLogger.Debug(ctx, "x1 resource http rsp: %v", httpRsp)
					return invoker(ctx, httpRsp)
				}),
		),
	}

	return s
}

func (s *X1ResourceSdk) SetRalCaller(caller sdk_utils.RalCaller) sdk_utils.RalCaller {
	last := s.ralCaller
	s.ralCaller = caller
	return last
}

// doRequest - 通用服务请求方法
func (s *X1ResourceSdk) doRequest(ctx context.Context, actionName string,
	httpMethod, uri string, req, rsp interface{}) (err error) {

	if err = s.ralCaller.HttpRequest(ctx, httpMethod, uri, nil, req, nil, rsp); err != nil {
		logger.SdkLogger.Warning(ctx,
			"[%s] ral request fail, httpMethod: %s, uri: %s, req: %v, err: %s",
			actionName, httpMethod, uri, base_utils.Format(req), err.Error())
		return err
	}

	return
}

func (s *X1ResourceSdk) CreateInstance(ctx context.Context, req *CreateInstanceReq) (rsp *CreateInstanceResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CreateInstanceResponse{}
	err = s.doRequest(ctx, "Create", http.MethodPost, x1ResouceServiceCreateUri, req, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "call CreateInstance request fail,req:%s , err: %s",
			base_utils.Format(req), err.Error())
		return nil, err
	}

	if rsp.Code != 0 {
		logger.SdkLogger.Warning(ctx, "CreateInstance request return fail,req:%s , RetCode: %d , ErrorMsg: %s",
			base_utils.Format(req), rsp.Code, rsp.ErrMsg)
		return nil, err
	}

	return rsp, nil
}

func (s *X1ResourceSdk) ShowCreateOrder(ctx context.Context, req *ShowCreateInstanceOrderReq) (rsp *ShowCreateOrderResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &ShowCreateOrderResponse{}
	err = s.doRequest(ctx, "showCreateOrder", http.MethodPost, x1ResouceServiceShowCreateOrderUri, req, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "call ShowCreateOrder request fail,req:%s , err: %s",
			base_utils.Format(req), err.Error())
		return nil, err
	}

	if rsp.Code != 0 {
		logger.SdkLogger.Warning(ctx, "ShowCreateOrder request return fail,req:%s , RetCode: %d , ErrorMsg: %s",
			base_utils.Format(req), rsp.Code, rsp.ErrMsg)
		return nil, err
	}

	return rsp, nil
}

func (s *X1ResourceSdk) DeleteInstance(ctx context.Context, req *DeleteInstancesReq) (rsp *DeleteInstancesResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &DeleteInstancesResponse{}
	err = s.doRequest(ctx, "delete", http.MethodPost, x1ResouceServiceDeleteUri, req, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "call DeleteInstance request fail,req:%s , err: %s",
			base_utils.Format(req), err.Error())
		return nil, err
	}

	if rsp.Code != 0 {
		logger.SdkLogger.Warning(ctx, "DeleteInstance request return fail,req:%s , RetCode: %d , ErrorMsg: %s",
			base_utils.Format(req), rsp.Code, rsp.ErrMsg)
		return nil, err
	}

	return rsp, nil
}

func (s *X1ResourceSdk) ResizeInstance(ctx context.Context, req *ResizeInstanceReq) (rsp *ResizeInstanceResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &ResizeInstanceResponse{}
	err = s.doRequest(ctx, "resizeInstance", http.MethodPost, x1ResouceServiceResizeUri, req, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "call ResizeInstance request fail,req:%s , err: %s",
			base_utils.Format(req), err.Error())
		return nil, err
	}

	if rsp.Code != 0 {
		logger.SdkLogger.Warning(ctx, "ResizeInstance request return fail,req:%s , RetCode: %d , ErrorMsg: %s",
			base_utils.Format(req), rsp.Code, rsp.ErrMsg)
		return nil, err
	}

	return rsp, nil
}

func (s *X1ResourceSdk) ShowResizeOrder(ctx context.Context, req *ShowResizeInstanceReq) (rsp *ShowResizeOrderResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &ShowResizeOrderResponse{}
	err = s.doRequest(ctx, "showResizeOrder", http.MethodPost, x1ResouceServiceShowResizeOrderUri, req, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "call ShowResizeOrder request fail,req:%s , err: %s",
			base_utils.Format(req), err.Error())
		return nil, err
	}

	return rsp, nil
}
