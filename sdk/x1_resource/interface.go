/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: z<PERSON><PERSON><PERSON><PERSON>
 * Date: 2022-5-20
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   x1 resouce
 */

// Package x1_resource
package x1_resource

import (
	"context"
)

type CreateInstanceReq struct {
	AppID                string          `json:"appId"`
	UserID               string          `json:"userId"`
	Product              string          `json:"product"`
	ImageID              string          `json:"imageId"`
	VpcID                string          `json:"vpcId"`
	CustomerDeploySetIDs []string        `json:"customDeploySetId"`
	LogicalZone          string          `json:"logicalZone"`
	Azone                string          `json:"azone"`
	StoreType            string          `json:"storeType"`
	ResouceType          string          `json:"resourceType"`
	Priority             string          `json:"priority"`
	Items                []InstanceParam `json:"Items"`
	X1TaskID             string          `json:"x1TaskId,omitempty"`
	CustomLabels         []string        `json:"customLabels,omitempty"`
	LogicalRegion        string          `json:"logicalRegion,omitempty"`
}

type InstanceParam struct {
	Spec            InstSpec `json:"spec"`
	Subnet          string   `json:"subnet"`
	Engine          string   `json:"engine"`
	Count           int64    `json:"count"`
	DeploySetID     string   `json:"deploySetId"`
	EntityIDs       []string `json:"entityIds"`
	SecurityGroupID string   `json:"securityGroupId"`
}

type InstSpec struct {
	AvailableVolume      int64  `json:"availableVolume"`
	Name                 string `json:"name"`
	CPUCount             int64  `json:"cpuCount"`
	MemoryCapacityInMB   int64  `json:"memoryCapacityInMB"`
	RootDiskCapacityInGB int64  `json:"rootDiskCapacityInGB"`
	DataDiskCapacityInGB int64  `json:"dataDiskCapacityInGB"`
}

type CreateInstanceResponse struct {
	Message   string `json:"message"`
	Code      int32  `json:"code"`
	Requestid string `json:"requestId"`
	OrderId   string `json:"orderId"`
	Status    string `json:"status"`
	ErrMsg    string `json:"errMsg"`
}

type ShowCreateInstanceOrderReq struct {
	OrderID string `json:"orderId"`
	UserID  string `json:"userId"`
}

type InstancePort struct {
	DbPort         int `json:"dbPort"`
	StatPort       int `json:"statPort"`
	XagentPort     int `json:"xagentPort"`
	XagentSyncPort int `json:"xagentSyncPort"`
	McpackPort     int `json:"mcpackPort"`
}

type Instance struct {
	Id           string            `json:"id"`
	ResourceType string            `json:"resourceType"`
	Name         string            `json:"name"`
	RootPassword string            `json:"rootPassword"`
	FixedIp      string            `json:"fixIP"`
	FloatingIp   string            `json:"floatingIP"`
	Ipv6FixedIp  string            `json:"fixIPv6"`
	Flavor       string            `json:"flavor"`
	EntityID     string            `json:"entityID"`
	CreateTime   string            `json:"createTime"`
	Port         InstancePort      `json:"portData"`
	HostUUID     string            `json:"bccUUID"`
	HostName     string            `json:"bccName"`
	Meta         map[string]string `json:"metaData"`
}

type ShowCreateOrderResponse struct {
	Message   string     `json:"message"`
	Code      int32      `json:"code"`
	Requestid string     `json:"requestId"`
	OrderId   string     `json:"orderId"`
	Status    string     `json:"status"`
	ErrMsg    string     `json:"errMsg"`
	Instances []Instance `json:"instances"`
}

type DeleteInstancesReq struct {
	UserID      string   `json:"userId"`
	InstanceIds []string `json:"instanceIds"`
	X1TaskID    string   `json:"x1TaskID"`
}

type DeleteInstancesResponse struct {
	Message   string `json:"message"`
	Code      int32  `json:"code"`
	Requestid string `json:"requestId"`
	OrderId   string `json:"orderId"`
	Status    string `json:"status"`
	ErrMsg    string `json:"errMsg"`
}

type ResizeInstanceReq struct {
	InstanceId                 string `json:"instanceId"`
	UserID                     string `json:"userId"`
	TargetCPUCount             int64  `json:"targetCPUCount"`
	TargetMemoryCapacityInMB   int64  `json:"targetMemoryCapacityInMB"`
	TargetDataDiskCapacityInGB int64  `json:"targetDataDiskCapacityInGB"`
	Engine                     string `json:"engine"`
	X1TaskID                   string `json:"x1TaskID"`
}

type ResizeInstanceResponse struct {
	Message   string `json:"message"`
	Code      int32  `json:"code"`
	Requestid string `json:"requestId"`
	OrderId   string `json:"orderId"`
	Status    string `json:"status"`
	ErrMsg    string `json:"errMsg"`
}

type ShowResizeInstanceReq struct {
	UserID  string `json:"userId"`
	OrderID string `json:"orderId"`
}

type ShowResizeOrderResponse struct {
	Message   string `json:"message"`
	Code      int32  `json:"code"`
	Requestid string `json:"requestId"`
	OrderId   string `json:"orderId"`
	Status    string `json:"status"`
	ErrMsg    string `json:"errMsg"`
}

type X1ResourceService interface {
	CreateInstance(ctx context.Context, req *CreateInstanceReq) (rsp *CreateInstanceResponse, err error)
	ShowCreateOrder(ctx context.Context, req *ShowCreateInstanceOrderReq) (rsp *ShowCreateOrderResponse, err error)
	DeleteInstance(ctx context.Context, req *DeleteInstancesReq) (rsp *DeleteInstancesResponse, err error)
	ResizeInstance(ctx context.Context, req *ResizeInstanceReq) (rsp *ResizeInstanceResponse, err error)
	ShowResizeOrder(ctx context.Context, req *ShowResizeInstanceReq) (rsp *ShowResizeOrderResponse, err error)
}
