/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/06/10 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file application_elb.go
 * <AUTHOR>
 * @date 2022/06/10 10:37:48
 * @brief app elb stdk
 *
 **/

package elbv2

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
)

type appELBSdk struct {
	conf *elbConf
	common.OpenApi
}

// NewDefaultAppELBSdk 默认方法
func NewDefaultAppELBSdk() AppELBService {
	return newAppELBSdk(DefaultServiceName)
}

// NewAppELBSdk 指定服务名称
func NewAppELBSdk(serviceName string) AppELBService {
	return newAppELBSdk(serviceName)
}

func newAppELBSdk(serviceName string) *appELBSdk {
	s := &appELBSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}

	return s
}

// doAPPELBRequest - 通用openstack服务请求方法
func (s *appELBSdk) doAppELBRequest(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}

	// 请求 openapi
	return s.DoRequest(ctx, params, rsp)
}

// doAPPELBRequestWithResourceID - 通用openstack服务请求方法 自定义header
func (s *appELBSdk) doAppELBRequestWithResourceID(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {
	// // 方法1:通过BLB 获取加密账号
	// getEncryptParam := GetEncryptAccountRequest{
	// 	AccountID: s.conf.ResourceID,
	// 	Source:    "scs",
	// 	Auth:      auth,
	// }
	// resp, err := s.GetEncryptAccountID(ctx, &getEncryptParam)
	// if err != nil {
	// 	return err
	// }

	// headers := map[string]interface{}{
	// 	"resource-accountId": resp.EncryptAccountID,
	// 	"resource-source":    "scs",
	// }

	// 方法2:进行aes/ecb/pkcs5（pkcs7）加密
	// 获取当前时间
	tmStr := time.Now().UTC().Format("2006-01-02T15:04:05Z")
	rawContent := fmt.Sprintf("%s/%s", s.conf.ResourceID, tmStr)

	encryptAccountID, err := crypto_utils.AesECBEncryptString(rawContent, s.conf.EncryptKey,
		crypto_utils.PKCS7PaddingType, crypto_utils.HexCodecType)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "aes ecb encrypt fail, content: %s, key: %s", rawContent, s.conf.EncryptKey)
		return cerrs.ErrSTSEncryptAccountFail.Wrap(err)
	}
	productResource := "scs"
	if s.conf.Product != "" {
		productResource = s.conf.Product
	}
	headers := map[string]interface{}{
		"resource-accountId": strings.ToUpper(encryptAccountID),
		"resource-source":    productResource,
	}

	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Headers:    headers,
		Product:    s.conf.Product,
	}

	_, err = s.DoRequestWithCustomizeHeader(ctx, params, rsp)

	return err
}

// GetEncryptAccountID 获取加密账号
func (s *appELBSdk) GetEncryptAccountID(ctx context.Context, req *GetEncryptAccountRequest) (rsp *GetEncryptAccountResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"accountId": req.AccountID,
		"source":    req.Source,
	}

	rsp = &GetEncryptAccountResponse{}
	err = s.doAppELBRequest(ctx, "GetEncryptAccountID", req.Auth, http.MethodGet,
		GetEncryptAccountURI, queries, nil, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "GetEncryptAccountID request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("GetEncryptAccountID fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

func (s *appELBSdk) CreateAppELB(ctx context.Context, req *CreateELBRequest) (rsp *CreateELBResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	rsp = &CreateELBResponse{}
	err = s.doAppELBRequestWithResourceID(ctx, "CreateAppELB", req.Auth, http.MethodPost,
		ApplicationBlbInstanceURI, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "CreateAppELB request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("CreateAppELB fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// DeleteAppELB delete applicaiton blb
func (s *appELBSdk) DeleteAppELB(ctx context.Context, req *DeleteELBRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BLBID
	rsp = &OperationResponse{}
	err = s.doAppELBRequestWithResourceID(ctx, "DeleteAppELB", req.Auth, http.MethodDelete,
		url, nil, nil, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "DeleteAppELB request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("DeleteAppELB fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// UpdateAppELB update applicaiton blb
func (s *appELBSdk) UpdateAppELB(ctx context.Context, req *UpdateELBRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BLBID
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "UpdateAppELB", req.Auth, http.MethodPut,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "UpdateAppELB request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("UpdateAppELB fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// UpdateAppELB update applicaiton blb
func (s *appELBSdk) ListAppELB(ctx context.Context, req *ListELBRequest) (rsp *ListELBResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	queries := map[string]interface{}{
		"address":      req.Address,
		"name":         req.Name,
		"blbId":        req.BLBID,
		"bccId":        req.BccID,
		"exactlyMatch": req.ExactlyMatch,
		"marker":       req.Marker,
	}

	if req.MaxKeys != 0 {
		queries["maxKeys"] = req.MaxKeys
	}

	rsp = &ListELBResponse{}
	err = s.doAppELBRequestWithResourceID(ctx, "ListAppELB", req.Auth, http.MethodGet,
		ApplicationBlbInstanceURI, queries, nil, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "ListAppELB request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("ListAppELB fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// GetAppELBDetail get app blb detail
func (s *appELBSdk) GetAppELBDetail(ctx context.Context, req *GetAppELBDetailRequest) (rsp *GetAppELBDetailResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	url := ApplicationBlbInstanceURI + "/" + req.BLBID
	rsp = &GetAppELBDetailResponse{}
	err = s.doAppELBRequestWithResourceID(ctx, "GetAppELBDetail", req.Auth, http.MethodGet,
		url, nil, nil, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "GetAppELBDetail request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("GetAppELBDetail fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// CreateAppServerGroup impl
func (s *appELBSdk) CreateAppServerGroup(ctx context.Context, req *CreateServerGroupRequest) (rsp *CreateServerGroupResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/appservergroup"
	rsp = &CreateServerGroupResponse{}
	err = s.doAppELBRequest(ctx, "CreateAppServerGroup", req.Auth, http.MethodPost,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "CreateAppServerGroup request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("CreateAppServerGroup fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// DeleteAppServerGroup impl
func (s *appELBSdk) DeleteAppServerGroup(ctx context.Context, req *DeleteServerGroupRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"delete": nil,
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/appservergroup"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "DeleteAppServerGroup", req.Auth, http.MethodPut,
		url, queries, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "DeleteAppServerGroup request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("DeleteAppServerGroup fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// UpdateAppServerGroup impl
func (s *appELBSdk) UpdateAppServerGroup(ctx context.Context, req *UpdateServerGroupRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/appservergroup"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "UpdateAppServerGroup", req.Auth, http.MethodPut,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "UpdateAppServerGroup request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("UpdateAppServerGroup fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// ListAppServerGroup impl
func (s *appELBSdk) ListAppServerGroup(ctx context.Context, req *ListServerGroupRequest) (rsp *ListServerGroupResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	queries := map[string]interface{}{
		"name":         req.Name,
		"exactlyMatch": req.ExactlyMatch,
		"marker":       req.Marker,
	}
	if req.MaxKeys != 0 {
		queries["maxKeys"] = req.MaxKeys
	}
	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/appservergroup"
	rsp = &ListServerGroupResponse{}
	err = s.doAppELBRequest(ctx, "ListAppServerGroup", req.Auth, http.MethodGet,
		url, queries, nil, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "ListAppServerGroup request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("ListAppServerGroup fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// CreateAppServerGroupPort impl
func (s *appELBSdk) CreateAppServerGroupPort(ctx context.Context, req *CommonServerGroupPortRequest) (rsp *CreateServerGroupPortResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/appservergroupport"
	rsp = &CreateServerGroupPortResponse{}
	err = s.doAppELBRequest(ctx, "CreateAppServerGroupPort", req.Auth, http.MethodPost,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "CreateAppServerGroupPort request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("CreateAppServerGroupPort fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// UpdateAppServerGroupPort impl
func (s *appELBSdk) UpdateAppServerGroupPort(ctx context.Context, req *CommonServerGroupPortRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/appservergroupport"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "UpdateAppServerGroupPort", req.Auth, http.MethodPut,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "UpdateAppServerGroupPort request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("UpdateAppServerGroupPort fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// DeleteAppServerGroupPort impl
func (s *appELBSdk) DeleteAppServerGroupPort(ctx context.Context, req *DeleteServerGroupPortRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"batchdelete": nil,
		"clientToken": req.Auth.TransactionId,
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/appservergroupport"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "DeleteAppServerGroupPort", req.Auth, http.MethodPut,
		url, queries, req, rsp)
	return
}

// CreateAppBLBRs impl
func (s *appELBSdk) CreateAppBLBRs(ctx context.Context, req *CommonAppBlbRsRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/blbrs"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "CreateAppBLBRs", req.Auth, http.MethodPost,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "CreateAppBLBRs request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("CreateAppBLBRs fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// UpdateAppBLBRs impl
func (s *appELBSdk) UpdateAppBLBRs(ctx context.Context, req *CommonAppBlbRsRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/blbrs"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "UpdateAppBLBRs", req.Auth, http.MethodPut,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "UpdateAppBLBRs request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("UpdateAppBLBRs fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// ListAppBLBRs impl
func (s *appELBSdk) ListAppBLBRs(ctx context.Context, req *ListAppBlbRsRequest) (rsp *ListAppBlbRsResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	queries := map[string]interface{}{
		"sgId":   req.ServerGroupID,
		"marker": req.Marker,
	}
	if req.MaxKeys != 0 {
		queries["maxKeys"] = req.MaxKeys
	}
	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/blbrs"
	rsp = &ListAppBlbRsResponse{}
	err = s.doAppELBRequest(ctx, "ListAppBLBRs", req.Auth, http.MethodGet,
		url, queries, nil, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "ListAppBLBRs request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("ListAppBLBRs fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// DeleteAppBLBRs impl
func (s *appELBSdk) DeleteAppBLBRs(ctx context.Context, req *DeleteAppBlbRsRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"batchdelete": nil,
		"clientToken": req.Auth.TransactionId,
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/blbrs"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "DeleteAppBLBRs", req.Auth, http.MethodPut,
		url, queries, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "DeleteAppBLBRs request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("DeleteAppBLBRs fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// ListAppBLBRsMount impl
func (s *appELBSdk) ListAppBLBRsMount(ctx context.Context, req *CommonAppBlbRsListRequest) (rsp *CommonAppBlbRsListResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"sgId": req.ServerGroupID,
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/blbrsmount"
	rsp = &CommonAppBlbRsListResponse{}
	err = s.doAppELBRequest(ctx, "ListAppBLBRsMount", req.Auth, http.MethodGet,
		url, queries, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "ListAppBLBRsMount request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("ListAppBLBRsMount fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// ListAppBLBRsUnmount impl
func (s *appELBSdk) ListAppBLBRsUnmount(ctx context.Context, req *CommonAppBlbRsListRequest) (rsp *CommonAppBlbRsListResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"sgId": req.ServerGroupID,
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/blbrsunmount"
	rsp = &CommonAppBlbRsListResponse{}
	err = s.doAppELBRequest(ctx, "ListAppBLBRsUnmount", req.Auth, http.MethodGet,
		url, queries, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "ListAppBLBRsUnmount request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("ListAppBLBRsUnmount fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// CreateAppTCPListener will create tcp listener
func (s *appELBSdk) CreateAppTCPListener(ctx context.Context, req *CreateAppTCPListenerRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/TCPlistener"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "CreateAppTCPListener", req.Auth, http.MethodPost,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "CreateAppTCPListener request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("CreateAppTCPListener fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// UpdateAppTCPListener update linstener
func (s *appELBSdk) UpdateAppTCPListener(ctx context.Context, req *CommonAppTCPListenerRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"listenerPort": req.ListenerPort,
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/TCPlistener"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "UpdateAppTCPListener", req.Auth, http.MethodPut,
		url, queries, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "UpdateAppTCPListener request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("UpdateAppTCPListener fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// ListAppTCPListener will list all tcp listener
func (s *appELBSdk) ListAppTCPListener(ctx context.Context, req *CommonListAppListenerRequest) (rsp *ListAppTCPListenerResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"marker": req.Marker,
	}
	if req.MaxKeys != 0 {
		queries["maxKeys"] = req.MaxKeys
	}
	if req.ListenerPort != 0 {
		queries["listenerPort"] = req.ListenerPort
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/TCPlistener"
	rsp = &ListAppTCPListenerResponse{}
	err = s.doAppELBRequest(ctx, "ListAppTCPListener", req.Auth, http.MethodGet,
		url, queries, nil, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "ListAppTCPListener request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("ListAppTCPListener fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// CreateAppHTTPListener will create tcp listener
func (s *appELBSdk) CreateAppHTTPListener(ctx context.Context, req *CommonAppHTTPListenerRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/HTTPlistener"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "CreateAppHTTPListener", req.Auth, http.MethodPost,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "CreateAppHTTPListener request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("CreateAppHTTPListener fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// UpdateAppHTTPListener update linstener
func (s *appELBSdk) UpdateAppHTTPListener(ctx context.Context, req *CommonAppHTTPListenerRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"listenerPort": req.ListenerPort,
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/HTTPlistener"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "UpdateAppHTTPListener", req.Auth, http.MethodPut,
		url, queries, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "UpdateAppHTTPListener request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("UpdateAppHTTPListener fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// ListAppHTTPListener will list all http listener
func (s *appELBSdk) ListAppHTTPListener(ctx context.Context, req *CommonListAppListenerRequest) (rsp *ListAppHTTPListenerResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"marker": req.Marker,
	}
	if req.MaxKeys != 0 {
		queries["maxKeys"] = req.MaxKeys
	}
	if req.ListenerPort != 0 {
		queries["listenerPort"] = req.ListenerPort
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/HTTPlistener"
	rsp = &ListAppHTTPListenerResponse{}
	err = s.doAppELBRequest(ctx, "ListAppHTTPListener", req.Auth, http.MethodGet,
		url, queries, nil, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "ListAppHTTPListener request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("ListAppHTTPListener fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// DeleteAppListener will delete listener
func (s *appELBSdk) DeleteAppListener(ctx context.Context, req *DeleteAppListenerRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"batchdelete": nil,
		"clientToken": req.Auth.TransactionId,
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/listener"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "DeleteAppListener", req.Auth, http.MethodPut,
		url, queries, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "DeleteAppListener request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("DeleteAppListener fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// CreateAppPolicys will create policys
func (s *appELBSdk) CreateAppPolicys(ctx context.Context, req *CreateAppPolicysRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/policys"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "CreateAppPolicys", req.Auth, http.MethodPost,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "CreateAppPolicys request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("CreateAppPolicys fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// ListAppPolicys will list policys
func (s *appELBSdk) ListAppPolicys(ctx context.Context, req *ListAppPolicysRequest) (rsp *ListAppPolicysResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"port":   req.Port,
		"marker": req.Marker,
		"type":   req.Type,
	}
	if req.MaxKeys != 0 {
		queries["maxKeys"] = req.MaxKeys
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/policys"
	rsp = &ListAppPolicysResponse{}
	err = s.doAppELBRequest(ctx, "ListAppPolicys", req.Auth, http.MethodGet,
		url, queries, nil, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "ListAppPolicys request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("ListAppPolicys fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// DeleteAppPolicys will delete policys
func (s *appELBSdk) DeleteAppPolicys(ctx context.Context, req *DeleteAppPolicysRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"batchdelete": nil,
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/policys"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "DeleteAppPolicys", req.Auth, http.MethodPut,
		url, queries, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "DeleteAppPolicys request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("DeleteAppPolicys fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// CreateAppIpGroup will create ip group
func (s *appELBSdk) CreateAppIPGroup(ctx context.Context, req *CreateAppIPGroupRequest) (rsp *CreateAppIPGroupResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/ipgroup"
	rsp = &CreateAppIPGroupResponse{}
	err = s.doAppELBRequest(ctx, "CreateAppIPGroup", req.Auth, http.MethodPost,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "CreateAppIPGroup request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("CreateAppIPGroup fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// UpdateAppIpGroup will update ip group
func (s *appELBSdk) UpdateAppIPGroup(ctx context.Context, req *UpdateAppIPGroupRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/ipgroup"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "UpdateAppIPGroup", req.Auth, http.MethodPut,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "UpdateAppIPGroup request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("UpdateAppIPGroup fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// DeleteAppIpGroup will delete ip group
func (s *appELBSdk) DeleteAppIPGroup(ctx context.Context, req *DeleteAppIPGroupRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"delete":      nil,
		"clientToken": req.Auth.TransactionId,
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/ipgroup"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "DeleteAppIPGroup", req.Auth, http.MethodPut,
		url, queries, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "DeleteAppIPGroup request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("DeleteAppIPGroup fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// ListAppIPGroup will list ip group
func (s *appELBSdk) ListAppIPGroup(ctx context.Context, req *ListAppIPGroupRequest) (rsp *ListAppIPGroupResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"name":         req.Name,
		"exactlyMatch": req.ExactlyMatch,
		"marker":       req.Marker,
	}
	if req.MaxKeys != 0 {
		queries["maxKeys"] = req.MaxKeys
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/ipgroup"
	rsp = &ListAppIPGroupResponse{}
	err = s.doAppELBRequest(ctx, "ListAppIPGroup", req.Auth, http.MethodGet,
		url, queries, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "ListAppIPGroup request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("ListAppIPGroup fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// CreateAppIPGroupPolicy will create ip group policy
func (s *appELBSdk) CreateAppIPGroupPolicy(ctx context.Context, req *CommonAppIPGroupPolicyRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/ipgroup/backendpolicy"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "CreateAppIPGroupPolicy", req.Auth, http.MethodPost,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "CreateAppIPGroupPolicy request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("CreateAppIPGroupPolicy fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// UpdateAppIPGroupPolicy will update ip group policy
func (s *appELBSdk) UpdateAppIPGroupPolicy(ctx context.Context, req *CommonAppIPGroupPolicyRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/ipgroup/backendpolicy"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "UpdateAppIPGroupPolicy", req.Auth, http.MethodPut,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "UpdateAppIPGroupPolicy request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("UpdateAppIPGroupPolicy fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// DeleteAppIPGroupPolicy will delete ip group policy
func (s *appELBSdk) DeleteAppIPGroupPolicy(ctx context.Context, req *DeleteAppIPGroupPolicyRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/ipgroup/backendpolicy"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "DeleteAppPolicys", req.Auth, http.MethodDelete,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "DeleteAppPolicys request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("DeleteAppPolicys fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// CreateAppIPGroupMember will create ip group member
func (s *appELBSdk) CreateAppIPGroupMember(ctx context.Context, req *CommonAppIPGroupMemberRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/ipgroup/member"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "CreateAppIPGroupMember", req.Auth, http.MethodPost,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "CreateAppIPGroupMember request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("CreateAppIPGroupMember fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// UpdateAppIPGroupMember will update ip group member
func (s *appELBSdk) UpdateAppIPGroupMember(ctx context.Context, req *CommonAppIPGroupMemberRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/ipgroup/member"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "UpdateAppIPGroupMember", req.Auth, http.MethodPut,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "UpdateAppIPGroupMember request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("UpdateAppIPGroupMember fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// DeleteAppIPGroupMember will delete ip group member
func (s *appELBSdk) DeleteAppIPGroupMember(ctx context.Context, req *DeleteAppIPGroupMemberRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"delete":      nil,
		"clientToken": req.Auth.TransactionId,
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/ipgroup/member"
	rsp = &OperationResponse{}
	err = s.doAppELBRequest(ctx, "DeleteAppIPGroupMember", req.Auth, http.MethodPut,
		url, queries, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "DeleteAppIPGroupMember request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("DeleteAppIPGroupMember fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// ListAppIPGroupMember will list ip group member
func (s *appELBSdk) ListAppIPGroupMember(ctx context.Context, req *ListAppIPGroupMemberRequest) (rsp *ListAppIPGroupMemberResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	queries := map[string]interface{}{
		"ipGroupId": req.IPGroupID,
		"marker":    req.Marker,
	}
	if req.MaxKeys != 0 {
		queries["maxKeys"] = req.MaxKeys
	}

	url := ApplicationBlbInstanceURI + "/" + req.BlbID + "/ipgroup/member"
	rsp = &ListAppIPGroupMemberResponse{}
	err = s.doAppELBRequest(ctx, "ListAppIPGroupMember", req.Auth, http.MethodGet,
		url, queries, nil, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "ListAppIPGroupMember request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("ListAppIPGroupMember fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}
