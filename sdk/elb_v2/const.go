/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/06/07 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file const.go
 * <AUTHOR>
 * @date 2022/06/07 14:25:34
 * @brief elb v1 const
 *
 **/

package elbv2

// DefaultServiceName of elb v2
const DefaultServiceName = "elb-v2"

const (
	// PublishEndpointURI uri
	PublishEndpointURI = "/v1/service"
	// NormalBlbInstanceURI uri
	NormalBlbInstanceURI = "/v1/blb"
	// ApplicationBlbInstanceURI uri
	ApplicationBlbInstanceURI = "/v1/appblb"
	// GetEncryptAccountURI uri
	GetEncryptAccountURI = "/api/ca"
)
