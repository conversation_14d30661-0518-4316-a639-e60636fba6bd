/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/06/07 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file publish_endpoint.go
 * <AUTHOR>
 * @date 2022/06/07 14:13:23
 * @brief publish endpoint sdk
 *
 **/

package elbv2

import (
	"context"
	"net/http"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type elbEndpointSdk struct {
	conf *elbConf
	common.OpenApi
}

// NewDefaultElbEndpointSdk 返回默认sdk
func NewDefaultElbEndpointSdk() ELBPublishEndpointService {
	return newElbEndpointSdk(DefaultServiceName)
}

// NewElbEndpointSdk 指定服务名创建sdk
func NewElbEndpointSdk(serviceName string) ELBPublishEndpointService {
	return newElbEndpointSdk(serviceName)
}

// newElbEndpointSdk 创建sdk的私有方法
func newElbEndpointSdk(serviceName string) *elbEndpointSdk {
	s := &elbEndpointSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}

	return s
}

// doRequest - 通用openstack服务请求方法
func (s *elbEndpointSdk) doRequest(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}

	// 请求 openapi
	err = s.DoRequest(ctx, params, rsp)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return err
	}
	return nil
}

// CreatePublishEndpoint impl
func (s *elbEndpointSdk) CreatePublishEndpoint(ctx context.Context, req *CreatePublishEndpointRequest) (rsp *CreatePublishEndpointResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Name == "" || req.InstanceID == "" || req.ServiceName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is not valid")
	}

	rsp = &CreatePublishEndpointResponse{}
	if err = s.doRequest(ctx, "CreatePublishEndpoint", req.Auth, http.MethodPost,
		PublishEndpointURI, nil, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "CreatePublishEndpoint request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("CreatePublishEndpoint fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

func (s *elbEndpointSdk) DeletePublishEndpoint(ctx context.Context, req *DeletePublishEndpointRequest) (rsp *CommonPublishEndpointResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Service == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is not valid")
	}

	rsp = &CommonPublishEndpointResponse{}
	url := PublishEndpointURI + "/" + req.Service
	if err = s.doRequest(ctx, "DeletePublishEndpoint", req.Auth, http.MethodDelete,
		url, nil, nil, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "DeletePublishEndpoint request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("DeletePublishEndpoint fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

func (s *elbEndpointSdk) UpdatePublishEndpoint(ctx context.Context, req *UpdatePublishEndpointRequest) (rsp *CommonPublishEndpointResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Name == "" || req.Description == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is not valid")
	}

	rsp = &CommonPublishEndpointResponse{}
	url := PublishEndpointURI + "/" + req.Service
	queries := map[string]interface{}{
		"modifyAttribute": nil,
	}
	if err = s.doRequest(ctx, "UpdatePublishEndpoint", req.Auth, http.MethodPut,
		url, queries, nil, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "UpdatePublishEndpoint request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("UpdatePublishEndpoint fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

func (s *elbEndpointSdk) BindService(ctx context.Context, req *BindServiceRequest) (rsp *CommonPublishEndpointResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CommonPublishEndpointResponse{}
	url := PublishEndpointURI + "/" + req.Service
	queries := map[string]interface{}{
		"bind": nil,
	}
	if err = s.doRequest(ctx, "BindService", req.Auth, http.MethodPut,
		url, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "BindService request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("BindService fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

func (s *elbEndpointSdk) UnbindService(ctx context.Context, req *UnbindServiceRequest) (rsp *CommonPublishEndpointResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CommonPublishEndpointResponse{}
	url := PublishEndpointURI + "/" + req.Service
	queries := map[string]interface{}{
		"unbind": nil,
	}
	if err = s.doRequest(ctx, "UnbindService", req.Auth, http.MethodPut,
		url, queries, nil, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "UnbindService request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("UnbindService fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

func (s *elbEndpointSdk) ListPublishEndpoint(ctx context.Context, req *ListPublishEndpointRequest) (rsp *ListPublishEndpointResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &ListPublishEndpointResponse{}
	queries := map[string]interface{}{
		"marker":  req.Marker,
		"maxKeys": req.MaxKeys,
	}
	if err = s.doRequest(ctx, "ListPublishEndpoint", req.Auth, http.MethodGet,
		PublishEndpointURI, queries, nil, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "ListPublishEndpoint request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("ListPublishEndpoint fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

func (s *elbEndpointSdk) GetPublishEndpointDetail(ctx context.Context, req *GetPublishEndpointDetailRequest) (rsp *GetPublishEndpointDetailResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	url := PublishEndpointURI + "/" + req.Service
	rsp = &GetPublishEndpointDetailResponse{}
	if err = s.doRequest(ctx, "GetPublishEndpointDetail", req.Auth, http.MethodGet,
		url, nil, nil, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "GetPublishEndpointDetail request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("GetPublishEndpointDetail fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

func (s *elbEndpointSdk) SetPublishEndpointACL(ctx context.Context, req *CommonPublishEndpointACLRequest) (rsp *CommonPublishEndpointResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.AuthList == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is not valid")
	}

	url := PublishEndpointURI + "/" + req.Service
	rsp = &CommonPublishEndpointResponse{}
	queries := map[string]interface{}{
		"addAuth": nil,
	}
	if err = s.doRequest(ctx, "SetPublishEndpointACL", req.Auth, http.MethodPut,
		url, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "SetPublishEndpointACL request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("SetPublishEndpointACL fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

func (s *elbEndpointSdk) ModifyPublishEndpointACL(ctx context.Context, req *CommonPublishEndpointACLRequest) (rsp *CommonPublishEndpointResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.AuthList == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is not valid")
	}

	url := PublishEndpointURI + "/" + req.Service
	rsp = &CommonPublishEndpointResponse{}
	queries := map[string]interface{}{
		"editAuth": nil,
	}
	if err = s.doRequest(ctx, "ModifyPublishEndpointACL", req.Auth, http.MethodPut,
		url, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "ModifyPublishEndpointACL request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("ModifyPublishEndpointACL fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

func (s *elbEndpointSdk) DeletePublishEndpointACL(ctx context.Context, req *DeletePublishEndpointACLRequest) (rsp *CommonPublishEndpointResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.UIDList == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is not valid")
	}

	url := PublishEndpointURI + "/" + req.Service
	rsp = &CommonPublishEndpointResponse{}
	queries := map[string]interface{}{
		"removeAuth": nil,
	}
	if err = s.doRequest(ctx, "DeletePublishEndpointACL", req.Auth, http.MethodPut,
		url, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "DeletePublishEndpointACL request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("DeletePublishEndpointACL fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}
