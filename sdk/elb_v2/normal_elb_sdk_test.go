package elbv2

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

func TestQueryBlbDetail(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newElbSdk(DefaultServiceName)
	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, nil, http.StatusNotFound))

	// case 1
	resp, err := s.QueryBlbDetail(ctx, &QueryBlbDetail{
		BlbID: "lb-xxx",
		Auth: &common.Authentication{
			IamUserId:     "mock-user-id",
			TransactionId: "mock-trans-id",
			Credential: &common.Credential{
				Ak:           "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
				Sk:           "bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
				SessionToken: "cccccccccccccccccccccccccccccccc",
			},
		},
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	if resp.Code != "" {
		fmt.Println(resp.Code)
	}
	fmt.Println(resp)
	fmt.Println("success")

	// case 2
	resp, err = s.QueryBlbDetail(ctx, nil)
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	} else if resp.Code != "" {
		fmt.Println(resp.Code)
	}
	fmt.Println(resp)
	fmt.Println("success")

	// case 3
	resp, err = s.QueryBlbDetail(ctx, &QueryBlbDetail{
		BlbID: "lb-xxx",
		Auth:  nil,
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	} else if resp.Code != "" {
		fmt.Println(resp.Code)
	}
	fmt.Println(resp)
	fmt.Println("success")
}
