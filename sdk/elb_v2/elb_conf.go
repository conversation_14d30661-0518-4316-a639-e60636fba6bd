/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/06/10 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file elb_conf.go
 * <AUTHOR>
 * @date 2022/06/10 09:45:24
 * @brief elbv2 conf
 *
 **/

package elbv2

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "BLB"

// elbConf definition
type elbConf struct {
	// encrypt resource account id
	ResourceID string `toml:"ResourceID, omitempty"`
	EncryptKey string `toml:"EncryptKey, omitempty"`
	Product    string `toml:"Product, omitempty"`
}

var elbConfMap = &sync.Map{}

func getConf(serviceName string) *elbConf {
	if conf, ok := elbConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*elbConf); ok {
			return conf
		}
	}

	conf := &elbConf{}
	conf.mustLoad(serviceName)

	elbConfMap.Store(serviceName, conf)

	return conf
}

func (conf *elbConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
