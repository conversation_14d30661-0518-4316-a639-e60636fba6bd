/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/06/09 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file elb_sdk.go
 * <AUTHOR>
 * @date 2022/06/09 20:55:15
 * @brief elbv2 sdk
 *
 **/

package elbv2

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
)

type elbSdk struct {
	conf *elbConf
	common.OpenApi
}

// NewDefaultElbSdk 默认方法
func NewDefaultElbSdk() ELBService {
	return newElbSdk(DefaultServiceName)
}

// NewElbSdk 指定服务名称
func NewElbSdk(serviceName string) ELBService {
	return newElbSdk(serviceName)
}

func newElbSdk(serviceName string) *elbSdk {
	s := &elbSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}

	return s
}

// doELBRequest - 通用openstack服务请求方法
func (s *elbSdk) doELBRequest(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}

	// 请求 openapi
	return s.DoRequest(ctx, params, rsp)
}

// doELBRequestWithResourceID - 通用openstack服务请求方法 自定义header
func (s *elbSdk) doELBRequestWithResourceID(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {
	// 获取当前时间
	tmStr := time.Now().UTC().Format("2006-01-02T15:04:05Z")
	rawContent := fmt.Sprintf("%s/%s", s.conf.ResourceID, tmStr)

	encryptAccountID, err := crypto_utils.AesECBEncryptString(rawContent, s.conf.EncryptKey,
		crypto_utils.PKCS7PaddingType, crypto_utils.HexCodecType)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "aes ecb encrypt fail, content: %s, key: %s", rawContent, s.conf.EncryptKey)
		return cerrs.ErrSTSEncryptAccountFail.Wrap(err)
	}
	productResource := "scs"
	if s.conf.Product != "" {
		productResource = s.conf.Product
	}
	headers := map[string]interface{}{
		"resource-accountId": encryptAccountID,
		"resource-source":    productResource,
	}

	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Headers:    headers,
		Product:    s.conf.Product,
	}

	_, err = s.DoRequestWithCustomizeHeader(ctx, params, rsp)

	return err
}

func (s *elbSdk) CreateELB(ctx context.Context, req *CreateELBRequest) (rsp *CreateELBResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	params := &common.OpenApiParams{
		ActionName: "CreateELB",
		Auth:       req.Auth,
		HttpMethod: http.MethodPost,
		Uri:        NormalBlbInstanceURI,
		Posts:      req,
	}

	rsp = &CreateELBResponse{}
	err = s.doELBRequestWithResourceID(ctx, "CreateNormalELB", params.Auth, http.MethodPost,
		NormalBlbInstanceURI, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "CreateELB request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("CreateELB fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// ExchangeBlbID will exchange blb id
func (s *elbSdk) QueryBlbDetail(ctx context.Context, req *QueryBlbDetail) (rsp *BlbDetailResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := NormalBlbInstanceURI + "/" + req.BlbID
	rsp = &BlbDetailResponse{}
	err = s.doELBRequestWithResourceID(ctx, "QueryBlbDetail", req.Auth, http.MethodGet,
		url, nil, nil, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "QueryBlbDetail request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("QueryBlbDetail fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// ExchangeBlbID will exchange blb id
func (s *elbSdk) ExchangeBlbID(ctx context.Context, req *ExchangeBlbIDRequest) (rsp *ExchangeBlbIDResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	url := NormalBlbInstanceURI + "/idMapping"
	rsp = &ExchangeBlbIDResponse{}
	err = s.doELBRequest(ctx, "ExchangeBlbID", req.Auth, http.MethodPost,
		url, nil, req, rsp)
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "ExchangeBlbID request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBLBLogicalFail.Errorf("ExchangeBlbID fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}
