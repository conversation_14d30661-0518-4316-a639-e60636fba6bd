/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/06/07 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file interface.go
 * <AUTHOR>
 * @date 2022/06/07 11:33:10
 * @brief elb interface
 *
 **/

package elbv2

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

// CommonPublishEndpointResponse 通用返回
type CommonPublishEndpointResponse struct {
	RequestID string `json:"requestId"`
	Message   string `json:"message"`
	Code      string `json:"code"`
}

// Authentication definition
type Authentication struct {
	UID  string `json:"uid"`
	Auth string `json:"auth"`
}

// CommonPublishEndpointACLRequest 服务发布点通用鉴权
type CommonPublishEndpointACLRequest struct {
	Service  string                 `json:"service"`
	AuthList []*Authentication      `json:"authList"`
	Auth     *common.Authentication `json:"-"`
}

// DeletePublishEndpointACLRequest 删除服务发布点鉴权
type DeletePublishEndpointACLRequest struct {
	Service string                 `json:"service"`
	UIDList []string               `json:"uidList"`
	Auth    *common.Authentication `json:"-"`
}

// BindServiceRequest definition
type BindServiceRequest struct {
	Service    string                 `json:"service"`
	InstanceID string                 `json:"instanceId"`
	Auth       *common.Authentication `json:"-"`
}

// UnbindServiceRequest definition
type UnbindServiceRequest struct {
	Service string                 `json:"service"`
	Auth    *common.Authentication `json:"-"`
}

// CreatePublishEndpointRequest definition
type CreatePublishEndpointRequest struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description,omitempty"`
	ServiceName string                 `json:"serviceName"`
	InstanceID  string                 `json:"instanceId"`
	AuthList    []*Authentication      `json:"authList,omitempty"`
	Auth        *common.Authentication `json:"-"`
}

// CreatePublishEndpointResponse definition
type CreatePublishEndpointResponse struct {
	Service   string `json:"service"`
	RequestID string `json:"requestId"`
	Message   string `json:"message"`
	Code      string `json:"code"`
}

// DeletePublishEndpointRequest definition
type DeletePublishEndpointRequest struct {
	Service string                 `json:"service"`
	Auth    *common.Authentication `json:"-"`
}

// UpdatePublishEndpointRequest definition
type UpdatePublishEndpointRequest struct {
	Service     string                 `json:"service"`
	Name        string                 `json:"name,omitempty"`
	Description string                 `json:"description,omitempty"`
	Auth        *common.Authentication `json:"-"`
}

// ListPublishEndpointRequest definition
type ListPublishEndpointRequest struct {
	Marker  string                 `json:"marker,omitempty"`
	MaxKeys int64                  `json:"maxKeys,omitempty"`
	Auth    *common.Authentication `json:"-"`
}

// Endpoint definition
type Endpoint struct {
	EndpointID string `json:"endpointId"`
	UID        string `json:"uid"`
	AttachTime string `json:"attachTime"`
}

// Service definition
type Service struct {
	ServiceID     string            `json:"serviceId"`
	Name          string            `json:"name"`
	Description   string            `json:"description"`
	ServiceName   string            `json:"serviceName"`
	InstanceID    string            `json:"instanceId"`
	BindType      string            `json:"bindType"`
	Status        string            `json:"status"`
	Service       string            `json:"service"`
	CreateTime    string            `json:"createTime"`
	EndpointCount int               `json:"endopointCount"`
	EndpointList  []*Endpoint       `json:"endpointList"`
	AuthList      []*Authentication `json:"authList"`
}

// ListPublishEndpointResponse definition
type ListPublishEndpointResponse struct {
	Marker      string     `json:"marker"`
	NextMarker  string     `json:"nextMarker"`
	IsTruncated bool       `json:"isTruncated"`
	MaxKeys     int64      `json:"maxKeys"`
	Services    []*Service `json:"services"`
	RequestID   string     `json:"requestId"`
	Message     string     `json:"message"`
	Code        string     `json:"code"`
}

// GetPublishEndpointDetailRequest definition
type GetPublishEndpointDetailRequest struct {
	Service string                 `json:"service"`
	Auth    *common.Authentication `json:"-"`
}

// GetPublishEndpointDetailResponse definition
type GetPublishEndpointDetailResponse struct {
	Service
	RequestID string `json:"requestId"`
	Message   string `json:"message"`
	Code      string `json:"code"`
}

// ELBPublishEndpointService 服务发布点接口定义
type ELBPublishEndpointService interface {
	CreatePublishEndpoint(ctx context.Context, req *CreatePublishEndpointRequest) (rep *CreatePublishEndpointResponse, err error)
	DeletePublishEndpoint(ctx context.Context, req *DeletePublishEndpointRequest) (rsp *CommonPublishEndpointResponse, err error)
	UpdatePublishEndpoint(ctx context.Context, req *UpdatePublishEndpointRequest) (rsp *CommonPublishEndpointResponse, err error)
	BindService(ctx context.Context, req *BindServiceRequest) (rsp *CommonPublishEndpointResponse, err error)
	UnbindService(ctx context.Context, req *UnbindServiceRequest) (rsp *CommonPublishEndpointResponse, err error)
	ListPublishEndpoint(ctx context.Context, req *ListPublishEndpointRequest) (rsp *ListPublishEndpointResponse, err error)
	GetPublishEndpointDetail(ctx context.Context, req *GetPublishEndpointDetailRequest) (rsp *GetPublishEndpointDetailResponse, err error)
	SetPublishEndpointACL(ctx context.Context, req *CommonPublishEndpointACLRequest) (rsp *CommonPublishEndpointResponse, err error)
	ModifyPublishEndpointACL(ctx context.Context, req *CommonPublishEndpointACLRequest) (rsp *CommonPublishEndpointResponse, err error)
	DeletePublishEndpointACL(ctx context.Context, req *DeletePublishEndpointACLRequest) (rsp *CommonPublishEndpointResponse, err error)
}

// ElbType definition
const (
	ElbTypeNormal = "normal"
	ElbTypeIPV6   = "ipv6"
)

// OperationResponse defnition
type OperationResponse struct {
	RequestID string `json:"requestId"`
	Code      string `json:"code"`
	Message   string `json:"message"`
}

// Tag definition
type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

// CreateELBRequest definition
type CreateELBRequest struct {
	Eip                    string                 `json:"eip,omitempty"`
	Name                   string                 `json:"name,omitempty"`
	Desc                   string                 `json:"desc,omitempty"`
	Address                string                 `json:"address,omitempty"`
	VpcID                  string                 `json:"vpcId"`
	SubnetID               string                 `json:"subnetId"`
	Type                   string                 `json:"type,omitempty"`
	Tags                   []*Tag                 `json:"tags,omitempty"`
	Layer4MasterAz         string                 `json:"layer4MasterAz,omitempty"`
	AllocateVip            bool                   `json:"allocateVip,omitempty"`
	Layer4ClusterID        string                 `json:"layer4ClusterId,omitempty"`
	Layer4ClusterExclusive bool                   `json:"layer4ClusterExclusive,omitempty"`
	Auth                   *common.Authentication `json:"-"`
}

// CreateELBResponse definition
type CreateELBResponse struct {
	BLBID       string `json:"blbId"`
	Name        string `json:"name"`
	Desc        string `json:"desc"`
	Address     string `json:"address"`
	UnderlayVip string `json:"underlayVip"`
	RequestID   string `json:"requestId"`
	Message     string `json:"message"`
	Code        string `json:"code"`
}

// DeleteELBRequest definition
type DeleteELBRequest struct {
	BLBID string                 `json:"blbId"`
	Auth  *common.Authentication `json:"-"`
}

// UpdateELBRequest definition
type UpdateELBRequest struct {
	BLBID       string                 `json:"blbId"`
	Name        string                 `json:"name,omitempty"`
	Desc        string                 `json:"desc,omitempty"`
	AllowDelete bool                   `json:"allowDelete,omitempty"`
	Auth        *common.Authentication `json:"-"`
}

// ListELBRequest definition
type ListELBRequest struct {
	Address      string                 `json:"address,omitempty"`
	Name         string                 `json:"name,omitempty"`
	BLBID        string                 `json:"blbId,omitempty"`
	BccID        string                 `json:"bccId,omitempty"`
	ExactlyMatch string                 `json:"exactlyMatch,omitempty"`
	Marker       string                 `json:"marker,omitempty"`
	MaxKeys      int                    `json:"maxKeys,omitempty"`
	Auth         *common.Authentication `json:"-"`
}

// AppBLB definition
type AppBLB struct {
	BLBID       string `json:"blbId"`
	Address     string `json:"address"`
	Name        string `json:"name"`
	Status      string `json:"status"`
	VpcID       string `json:"vpcId"`
	SubnetID    string `json:"subnetId"`
	PublicIP    string `json:"publicIp"`
	Tags        []*Tag `json:"tags"`
	AllowDelete bool   `json:"allowDelete"`
}

// ListELBResponse definition
type ListELBResponse struct {
	BLBList     []*AppBLB `json:"blbList"`
	Marker      string    `json:"marker"`
	MaxKeys     int       `json:"maxKeys"`
	IsTruncated bool      `json:"isTruncated"`
	NextMarker  string    `json:"nextMarker"`
	RequestID   string    `json:"requestId"`
	Message     string    `json:"message"`
	Code        string    `json:"code"`
}

// GetAppELBDetailRequest definition
type GetAppELBDetailRequest struct {
	BLBID string                 `json:"blbId"`
	Auth  *common.Authentication `json:"-"`
}

// ListenerInfo definition
type ListenerInfo struct {
	Port string `json:"port"`
	Type string `json:"type"`
}

// GetAppELBDetailResponse definition
type GetAppELBDetailResponse struct {
	BlbID           string          `json:"blbId"`
	Status          string          `json:"status"`
	Name            string          `json:"name"`
	Desc            string          `json:"desc"`
	Address         string          `json:"address"`
	PublicIP        string          `json:"publicIp"`
	Cidr            string          `json:"cidr"`
	VpcName         string          `json:"vpcName"`
	SubnetName      string          `json:"subnetName"`
	SubnetCider     string          `json:"subnetCider"`
	CreateTime      string          `json:"createTime"`
	ReleaseTime     string          `json:"releaseTime"`
	Listener        []*ListenerInfo `json:"listener"`
	Tags            []*Tag          `json:"tags"`
	AllowDelete     bool            `json:"allowDelete"`
	RequestID       string          `json:"requestId"`
	Message         string          `json:"message"`
	Code            string          `json:"code"`
	Layer4ClusterId string          `json:"layer4ClusterId"`
	Layer4MasterAz  string          `json:"layer4MasterAz"`
	Layer4SlaveAz   string          `json:"layer4SlaveAz"`
}

// CreateServerGroupRequest definition
type CreateServerGroupRequest struct {
	Name              string                 `json:"name,omitempty"`
	Desc              string                 `json:"desc,omitempty"`
	BlbID             string                 `json:"blbId"`
	BackendServerList []*BackendServerList   `json:"backendServerList,omitempty"`
	Auth              *common.Authentication `json:"-"`
}

// CreateServerGroupResponse definition
type CreateServerGroupResponse struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	Desc      string `json:"desc"`
	Status    string `json:"status"`
	RequestID string `json:"requestId"`
	Message   string `json:"message"`
	Code      string `json:"code"`
}

// UpdateServerGroupRequest definition
type UpdateServerGroupRequest struct {
	BlbID string                 `json:"blbId"`
	SgID  string                 `json:"sgId"`
	Name  string                 `json:"name,omitempty"`
	Desc  string                 `json:"desc,omitempty"`
	Auth  *common.Authentication `json:"-"`
}

// ListServerGroupRequest definition
type ListServerGroupRequest struct {
	Name         string                 `json:"name,omitempty"`
	BlbID        string                 `json:"blbId"`
	ExactlyMatch string                 `json:"exactlyMatch,omitempty"`
	Marker       string                 `json:"marker,omitempty"`
	MaxKeys      int                    `json:"maxKeys,omitempty"`
	Auth         *common.Authentication `json:"-"`
}

// ListServerGroupResponse definition
type ListServerGroupResponse struct {
	AppServerGroupList []*AppServerGroupList `json:"appServerGroupList"`
	Marker             string                `json:"marker"`
	IsTruncated        bool                  `json:"isTruncated"`
	MaxKeys            int                   `json:"maxKeys"`
	NextMarker         string                `json:"nextMarker"`
	RequestID          string                `json:"requestId"`
	Message            string                `json:"message"`
	Code               string                `json:"code"`
}

// PortList definition
type PortList struct {
	ID                          string `json:"id"`
	Port                        int    `json:"port"`
	Type                        string `json:"type"`
	HealthCheck                 string `json:"healthCheck"`
	HealthCheckNormalStatus     string `json:"healthCheckNormalStatus"`
	HealthCheckPort             int    `json:"healthCheckPort"`
	HealthCheckTimeoutInSecond  int    `json:"healthCheckTimeoutInSecond"`
	HealthCheckDownRetry        int    `json:"healthCheckDownRetry"`
	HealthCheckUpRetry          int    `json:"healthCheckUpRetry"`
	HealthCheckIntervalInSecond int    `json:"healthCheckIntervalInSecond"`
	HealthCheckURLPath          string `json:"healthCheckUrlPath"`
	HealtchCheckHost            string `json:"healtchCheckHost"`
	Status                      string `json:"status"`
}

// AppServerGroupList definition
type AppServerGroupList struct {
	ID       string      `json:"id"`
	Name     string      `json:"name"`
	Desc     string      `json:"desc"`
	Status   string      `json:"status"`
	PortList []*PortList `json:"portList"`
}

// DeleteServerGroupRequest definition
type DeleteServerGroupRequest struct {
	BlbID         string                 `json:"blbId"`
	ServerGroupID string                 `json:"sgId"`
	Auth          *common.Authentication `json:"-"`
}

// CommonServerGroupPortRequest definition
type CommonServerGroupPortRequest struct {
	BlbID                       string                 `json:"blbId"`
	ServerGroupID               string                 `json:"sgId"`
	Port                        int                    `json:"port"`
	Type                        string                 `json:"type"`
	HealthCheck                 string                 `json:"healthCheck,omitempty"`
	HealthCheckPort             int                    `json:"healthCheckPort,omitempty"`
	HealthCheckURLPath          string                 `json:"healthCheckUrlPath,omitempty"`
	HealthCheckTimeoutInSecond  int                    `json:"healthCheckTimeoutInSecond,omitempty"`
	HealthCheckIntervalInSecond int                    `json:"healthCheckIntervalInSecond,omitempty"`
	HealthCheckDownRetry        int                    `json:"healthCheckDownRetry,omitempty"`
	HealthCheckUpRetry          int                    `json:"healthCheckUpRetry,omitempty"`
	HealthCheckNormalStatus     string                 `json:"healthCheckNormalStatus,omitempty"`
	HealthCheckHost             string                 `json:"healthCheckHost,omitempty"`
	UDPHealthCheckString        string                 `json:"udpHealthCheckString,omitempty"`
	Auth                        *common.Authentication `json:"-"`
}

// CreateServerGroupPortResponse definition
type CreateServerGroupPortResponse struct {
	ID        string `json:"id"`
	Status    string `json:"status"`
	RequestID string `json:"requestId"`
	Message   string `json:"message"`
	Code      string `json:"code"`
}

// DeleteServerGroupPortRequest definition
type DeleteServerGroupPortRequest struct {
	BlbID         string                 `json:"blbId"`
	ServerGroupID string                 `json:"sgId"`
	PortIDList    []string               `json:"portIdList"`
	Auth          *common.Authentication `json:"-"`
}

// CommonAppBlbRsRequest definition
type CommonAppBlbRsRequest struct {
	BlbID             string                 `json:"blbId"`
	ServerGroupID     string                 `json:"sgId"`
	BackendServerList []*BackendServerList   `json:"backendServerList"`
	Auth              *common.Authentication `json:"-"`
}

// ListAppBlbRsRequest definition
type ListAppBlbRsRequest struct {
	BlbID         string                 `json:"blbId"`
	ServerGroupID string                 `json:"sgId"`
	Marker        string                 `json:"marker,omitempty"`
	MaxKeys       int                    `json:"maxKeys,omitempty"`
	Auth          *common.Authentication `json:"-"`
}

// BackendServerList definition
type BackendServerList struct {
	InstanceID string      `json:"instanceId"`
	PrivateIP  string      `json:"privateIp,omitempty"`
	Weight     int         `json:"weight"`
	PortList   []*PortList `json:"portList,omitempty"`
}

// DeleteAppBlbRsRequest definition
type DeleteAppBlbRsRequest struct {
	BlbID               string                 `json:"blbId"`
	SgID                string                 `json:"sgId"`
	BackendServerIDList []string               `json:"backendServerIdList"`
	Auth                *common.Authentication `json:"-"`
}

// CommonAppBlbRsListRequest definition
type CommonAppBlbRsListRequest struct {
	BlbID         string                 `json:"blbId"`
	ServerGroupID string                 `json:"sgId"`
	Auth          *common.Authentication `json:"-"`
}

// CommonAppBlbRsListResponse definition
type CommonAppBlbRsListResponse struct {
	BackendServerList []*BackendServers `json:"backendServerList"`
	RequestID         string            `json:"requestId"`
	Message           string            `json:"message"`
	Code              string            `json:"code"`
}

// GetEncryptAccountRequest 获取加密账号的请求
type GetEncryptAccountRequest struct {
	AccountID string                 `json:"accountId"`
	Source    string                 `json:"source"`
	Auth      *common.Authentication `json:"-"`
}

// GetEncryptAccountResponse 获取加密账号的响应
type GetEncryptAccountResponse struct {
	EncryptAccountID string `json:"encryptedAccountId"`
	RequestID        string `json:"requestId"`
	Message          string `json:"message"`
	Code             string `json:"code"`
}

// ListAppBlbRsResponse definition
type ListAppBlbRsResponse struct {
	Marker            string            `json:"marker"`
	IsTruncated       bool              `json:"isTruncated"`
	MaxKeys           int               `json:"maxKeys"`
	NextMarker        string            `json:"nextMarker"`
	BackendServerList []*BackendServers `json:"backendServerList"`
	RequestID         string            `json:"requestId"`
	Message           string            `json:"message"`
	Code              string            `json:"code"`
}

// PortInfo definition
type PortInfo struct {
	ListenerPort        string `json:"listenerPort"`
	BackendPort         string `json:"backendPort"`
	PortType            string `json:"portType"`
	HealthCheckPortType string `json:"healthCheckPortType"`
	PortID              string `json:"portId"`
	PolicyID            string `json:"policyId"`
	Status              string `json:"status"`
}

// BackendServers definition
type BackendServers struct {
	InstanceID string      `json:"instanceId"`
	PrivateIP  string      `json:"privateIp"`
	Weight     int         `json:"weight"`
	PortList   []*PortInfo `json:"portList"`
}

// CommonAppTCPListenerRequest definition
type CommonAppTCPListenerRequest struct {
	BlbID             string                 `json:"BlbId"`
	ListenerPort      int                    `json:"listenerPort"`
	Scheduler         string                 `json:"scheduler"`
	TCPSessionTimeout int                    `json:"tcpSessionTimeout,omitempty"`
	Auth              *common.Authentication `json:"-"`
}

// CreateAppTCPListenerRequest definition
type CreateAppTCPListenerRequest struct {
	BlbID             string                 `json:"BlbId"`
	ListenerPort      int                    `json:"listenerPort"`
	Scheduler         string                 `json:"scheduler"`
	TCPSessionTimeout int                    `json:"tcpSessionTimeout,omitempty"`
	DrainingTimeout   int                    `json:"drainingTimeout"`
	Auth              *common.Authentication `json:"-"`
}

// CommonAppHTTPListenerRequest definition
type CommonAppHTTPListenerRequest struct {
	BlbID                 string                 `json:"BlbId"`
	ListenerPort          int                    `json:"listenerPort"`
	Scheduler             string                 `json:"scheduler"`
	XForwardedFor         bool                   `json:"xForwardedFor,omitempty"`
	ServerTimeout         int                    `json:"serverTimeout,omitempty"`
	RedirectPort          int                    `json:"redirectPort,omitempty"`
	KeepSession           bool                   `json:"keepSession,omitempty"`
	KeepSessionType       string                 `json:"keepSessionType,omitempty"`
	KeepSessionTimeout    int                    `json:"keepSessionTimeout,omitempty"`
	KeepSessionCookieName string                 `json:"keepSessionCookieName,omitempty"`
	XForwardedProto       bool                   `json:"xForwardedProto,omitempty"`
	Auth                  *common.Authentication `json:"-"`
}

// TCPListenerList definition
type TCPListenerList struct {
	ListenerPort      int    `json:"listenerPort"`
	Scheduler         string `json:"scheduler"`
	TCPSessionTimeout int    `json:"tcpSessionTimeout"`
}

// HTTPListenerList definition
type HTTPListenerList struct {
	ListenerPort          int    `json:"listenerPort"`
	BackendPort           int    `json:"backendPort"`
	Scheduler             string `json:"scheduler"`
	KeepSession           bool   `json:"keepSession"`
	KeepSessionType       string `json:"keepSessionType"`
	KeepSessionTimeout    int    `json:"keepSessionTimeout"`
	KeepSessionCookieName string `json:"keepSessionCookieName"`
	XForwardedFor         bool   `json:"xForwardedFor"`
	XForwardedProto       bool   `json:"xForwardedProto"`
	ServerTimeout         int    `json:"serverTimeout"`
	RedirectPort          int    `json:"redirectPort"`
}

// CommonListAppListenerRequest definition
type CommonListAppListenerRequest struct {
	BlbID        string                 `json:"blbId"`
	ListenerPort int                    `json:"listenerPort,omitempty"`
	Marker       string                 `json:"marker,omitempty"`
	MaxKeys      int                    `json:"maxKeys,omitempty"`
	Auth         *common.Authentication `json:"-"`
}

// ListAppTCPListenerResponse definition
type ListAppTCPListenerResponse struct {
	ListenerList []*TCPListenerList `json:"listenerList"`
	Marker       string             `json:"marker"`
	NextMarker   string             `json:"nextMarker"`
	IsTruncated  bool               `json:"isTruncated"`
	MaxKeys      int                `json:"maxKeys"`
	RequestID    string             `json:"requestId"`
	Message      string             `json:"message"`
	Code         string             `json:"code"`
}

// ListAppHTTPListenerResponse definition
type ListAppHTTPListenerResponse struct {
	ListenerList []*HTTPListenerList `json:"listenerList"`
	Marker       string              `json:"marker"`
	NextMarker   string              `json:"nextMarker"`
	IsTruncated  bool                `json:"isTruncated"`
	MaxKeys      int                 `json:"maxKeys"`
	RequestID    string              `json:"requestId"`
	Message      string              `json:"message"`
	Code         string              `json:"code"`
}

// DeleteAppListenerRequest definition
type DeleteAppListenerRequest struct {
	BlbID        string                 `json:"blbId"`
	PortList     []int                  `json:"portList,omitempty"`
	PortTypeList []*PortTypeList        `json:"portTypeList,omitempty"`
	Auth         *common.Authentication `json:"-"`
}

// PortTypeList definition
type PortTypeList struct {
	Port int    `json:"port"`
	Type string `json:"type"`
}

// CreateAppPolicysRequest definition
type CreateAppPolicysRequest struct {
	BlbID        string                 `json:"blbId"`
	ListenerPort int                    `json:"listenerPort"`
	Type         string                 `json:"type,omitempty"`
	AppPolicyVos []*AppPolicyVos        `json:"appPolicyVos"`
	Auth         *common.Authentication `json:"-"`
}

// RuleList definition
type RuleList struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

// AppPolicyVos definition
type AppPolicyVos struct {
	AppServerGroupID string      `json:"appServerGroupId,omitempty"`
	IPGroupID        string      `json:"appIpGroupId,omitempty"`
	BackendPort      int         `json:"backendPort,omitempty"`
	Priority         int         `json:"priority"`
	Desc             string      `json:"desc"`
	RuleList         []*RuleList `json:"ruleList"`
}

// ListAppPolicysRequest definition
type ListAppPolicysRequest struct {
	BlbID   string                 `json:"blbId"`
	Port    int                    `json:"port"`
	Type    string                 `json:"type,omitempty"`
	Marker  string                 `json:"marker,omitempty"`
	MaxKeys int                    `json:"maxKeys,omitempty"`
	Auth    *common.Authentication `json:"-"`
}

// ListAppPolicysResponse definition
type ListAppPolicysResponse struct {
	Marker      string        `json:"marker"`
	IsTruncated bool          `json:"isTruncated"`
	MaxKeys     int           `json:"maxKeys"`
	NextMarker  string        `json:"nextMarker"`
	PolicyList  []*PolicyList `json:"policyList"`
	RequestID   string        `json:"requestId"`
	Message     string        `json:"message"`
	Code        string        `json:"code"`
}

// PolicyList definition
type PolicyList struct {
	ID                 string      `json:"id"`
	AppServerGroupID   string      `json:"appServerGroupId"`
	AppServerGroupName string      `json:"appServerGroupName"`
	FrontendPort       int         `json:"frontendPort"`
	Type               string      `json:"type"`
	BackendPort        int         `json:"backendPort"`
	PortType           string      `json:"portType"`
	Priority           int         `json:"priority"`
	Desc               string      `json:"desc"`
	GroupType          string      `json:"groupType"`
	RuleList           []*RuleList `json:"ruleList"`
}

// DeleteAppPolicysRequest definition
type DeleteAppPolicysRequest struct {
	BlbID        string                 `json:"blbId"`
	Port         int                    `json:"port"`
	Type         string                 `json:"type,omitempty"`
	PolicyIDList []string               `json:"policyIdList"`
	Auth         *common.Authentication `json:"-"`
}

type QueryBlbDetail struct {
	BlbID string                 `json:"blbId"`
	Auth  *common.Authentication `json:"-"`
}

type BlbDetailResponse struct {
	Layer7ClusterId        string              `json:"layer7ClusterId"`
	AllowDelete            bool                `json:"allowDelete"`
	VpcName                string              `json:"vpcName"`
	Layer7ClusterExclusive bool                `json:"layer7ClusterExclusive"`
	VtepIp                 string              `json:"vtepIp"`
	Layer4ClusterExclusive bool                `json:"layer4ClusterExclusive"`
	UnderlayVip            string              `json:"underlayVip"`
	Status                 string              `json:"status"` // creating:创建中  available:运行中  updating:更新中  paused:已欠费  unavailable:暂不可用
	Tags                   []BlbTagModel       `json:"tags"`
	Vni                    string              `json:"vni"`
	Listener               []BlbListenerDetail `json:"listener"`
	BlbId                  string              `json:"blbId"`
	Address                string              `json:"address"`
	Cidr                   string              `json:"cidr"`
	CreateTime             string              `json:"createTime"`
	Desc                   string              `json:"desc"`
	Name                   string              `json:"name"`
	Layer4ClusterId        string              `json:"layer4ClusterId"`
	SupportAcl             bool                `json:"supportAcl"`
	Layer4MasterAz         string              `json:"layer4MasterAz"`
	Layer4SlaveAz          string              `json:"layer4SlaveAz"`
	RequestID              string              `json:"requestId"`
	Message                string              `json:"message"`
	Code                   string              `json:"code"`
}

type BlbListenerDetail struct {
	HealthCheckstatus string `json:"healthCheckstatus"`
	BackendPort       int    `json:"backendPort"`
	Type              string `json:"type"`
	Port              string `json:"port"`
	HealthCheckType   string `json:"healthCheckType"`
}

type BlbTagModel struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

// ExchangeBlbIDRequest definition
type ExchangeBlbIDRequest struct {
	BlbIDList []string               `json:"blbIdList"`
	Auth      *common.Authentication `json:"-"`
}

// ExchangeBlbIDResponse definition
type ExchangeBlbIDResponse struct {
	BlbIDMappingList []BlbIDMappingList `json:"blbIdMappingList"`
	RequestID        string             `json:"requestId"`
	Message          string             `json:"message"`
	Code             string             `json:"code"`
}

// BlbIDMappingList definition
type BlbIDMappingList struct {
	ShortID string `json:"shortId"`
	LongID  string `json:"longId"`
}

// CreateAppIPGroupRequest definition
type CreateAppIPGroupRequest struct {
	BlbID      string                 `json:"blbId"`
	Name       string                 `json:"name,omitempty"`
	Desc       string                 `json:"desc,omitempty"`
	MemberList []*MemberList          `json:"memberList,omitempty"`
	Auth       *common.Authentication `json:"-"`
}

// MemberList definition
type MemberList struct {
	IP       string `json:"ip,omitempty"`
	Weight   int    `json:"weight"` // 取值范围 0-100
	Port     int    `json:"port,omitempty"`
	MemberID string `json:"memberId,omitempty"`
}

// CreateAppIPGroupResponse definition
type CreateAppIPGroupResponse struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	Desc      string `json:"desc"`
	RequestID string `json:"requestId"`
	Message   string `json:"message"`
	Code      string `json:"code"`
}

// UpdateAppIPGroupRequest definition
type UpdateAppIPGroupRequest struct {
	BlbID     string                 `json:"blbId"`
	IPGroupID string                 `json:"ipGroupId"`
	Name      string                 `json:"name,omitempty"`
	Desc      string                 `json:"desc,omitempty"`
	Auth      *common.Authentication `json:"-"`
}

// DeleteAppIPGroupRequest definition
type DeleteAppIPGroupRequest struct {
	BlbID     string                 `json:"blbId"`
	IPGroupID string                 `json:"ipGroupId"`
	Auth      *common.Authentication `json:"-"`
}

// ListAppIPGroupRequest definition
type ListAppIPGroupRequest struct {
	BlbID        string                 `json:"blbId"`
	Name         string                 `json:"name,omitempty"`
	Marker       string                 `json:"marker,omitempty"`
	MaxKeys      int                    `json:"maxKeys,omitempty"`
	ExactlyMatch string                 `json:"exactlyMatch,omitempty"`
	Auth         *common.Authentication `json:"-"`
}

// ListAppIPGroupResponse definition
type ListAppIPGroupResponse struct {
	NextMarker     string            `json:"nextMarker"`
	Marker         string            `json:"marker"`
	MaxKeys        int               `json:"maxKeys"`
	IsTruncated    bool              `json:"isTruncated"`
	AppIPGroupList []*AppIPGroupList `json:"appIpGroupList"`
	RequestID      string            `json:"requestId"`
	Message        string            `json:"message"`
	Code           string            `json:"code"`
}

// BackendPolicyList definiton
type BackendPolicyList struct {
	ID                          string `json:"id"`
	Type                        string `json:"type"`
	HealthCheck                 string `json:"healthCheck"`
	HealthCheckPort             int    `json:"healthCheckPort"`
	HealthCheckURLPath          string `json:"healthCheckUrlPath"`
	HealthCheckTimeoutInSecond  int    `json:"healthCheckTimeoutInSecond"`
	HealthCheckIntervalInSecond int    `json:"healthCheckIntervalInSecond"`
	HealthCheckDownRetry        int    `json:"healthCheckDownRetry"`
	HealthCheckUpRetry          int    `json:"healthCheckUpRetry"`
	HealthCheckNormalStatus     string `json:"healthCheckNormalStatus"`
	HealthCheckHost             string `json:"healthCheckHost,omitempty"`
	UDPHealthCheckString        string `json:"udpHealthCheckString,omitempty"`
}

// AppIPGroupList definition
type AppIPGroupList struct {
	ID                string               `json:"id"`
	Name              string               `json:"name"`
	Desc              string               `json:"desc"`
	BackendPolicyList []*BackendPolicyList `json:"backendPolicyList"`
}

// CommonAppIPGroupPolicyRequest definition
type CommonAppIPGroupPolicyRequest struct {
	BlbID                       string                 `json:"blbId"`
	IPGroupID                   string                 `json:"ipGroupId"`
	Type                        string                 `json:"type"`
	HealthCheck                 string                 `json:"healthCheck,omitempty"`
	HealthCheckPort             int                    `json:"healthCheckPort,omitempty"`
	HealthCheckURLPath          string                 `json:"healthCheckUrlPath,omitempty"`
	HealthCheckTimeoutInSecond  int                    `json:"healthCheckTimeoutInSecond,omitempty"`
	HealthCheckIntervalInSecond int                    `json:"healthCheckIntervalInSecond,omitempty"`
	HealthCheckDownRetry        int                    `json:"healthCheckDownRetry,omitempty"`
	HealthCheckUpRetry          int                    `json:"healthCheckUpRetry,omitempty"`
	HealthCheckNormalStatus     string                 `json:"healthCheckNormalStatus,omitempty"`
	HealthCheckHost             string                 `json:"healthCheckHost,omitempty"`
	UDPHealthCheckString        string                 `json:"udpHealthCheckString,omitempty"`
	Auth                        *common.Authentication `json:"-"`
}

// DeleteAppIPGroupPolicyRequest definition
type DeleteAppIPGroupPolicyRequest struct {
	BlbID               string                 `json:"blbId"`
	IPGroupID           string                 `json:"ipGroupId"`
	BackendPolicyIDList []string               `json:"backendPolicyIdList"`
	Auth                *common.Authentication `json:"-"`
}

// CommonAppIPGroupMemberRequest definition
type CommonAppIPGroupMemberRequest struct {
	BlbID      string                 `json:"blbId"`
	IPGroupID  string                 `json:"ipGroupId"`
	MemberList []*MemberList          `json:"memberList"`
	Auth       *common.Authentication `json:"-"`
}

// DeleteAppIPGroupMemberRequest definition
type DeleteAppIPGroupMemberRequest struct {
	BlbID        string                 `json:"blbId"`
	IPGroupID    string                 `json:"ipGroupId"`
	MemberIDList []string               `json:"memberIdList"`
	Auth         *common.Authentication `json:"-"`
}

// ListAppIPGroupMemberRequest definition
type ListAppIPGroupMemberRequest struct {
	BlbID     string                 `json:"blbId"`
	IPGroupID string                 `json:"ipGroupId"`
	Marker    string                 `json:"marker,omitempty"`
	MaxKeys   int                    `json:"maxKeys,omitempty"`
	Auth      *common.Authentication `json:"-"`
}

// ListAppIPGroupMemberResponse definition
type ListAppIPGroupMemberResponse struct {
	NextMarker  string        `json:"nextMarker"`
	Marker      string        `json:"marker"`
	MaxKeys     int           `json:"maxKeys"`
	IsTruncated bool          `json:"isTruncated"`
	MemberList  []*MemberList `json:"memberList"`
	RequestID   string        `json:"requestId"`
	Message     string        `json:"message"`
	Code        string        `json:"code"`
}

// ELBService 接口定义
type ELBService interface {
	CreateELB(ctx context.Context, req *CreateELBRequest) (rsp *CreateELBResponse, err error)
	QueryBlbDetail(ctx context.Context, req *QueryBlbDetail) (rsp *BlbDetailResponse, err error)
	// 长短ID转换
	ExchangeBlbID(ctx context.Context, req *ExchangeBlbIDRequest) (rep *ExchangeBlbIDResponse, err error)
}

// AppELBService 接口定义
type AppELBService interface {
	// 实例相关接口
	CreateAppELB(ctx context.Context, req *CreateELBRequest) (rsp *CreateELBResponse, err error)
	DeleteAppELB(ctx context.Context, req *DeleteELBRequest) (rsp *OperationResponse, err error)
	UpdateAppELB(ctx context.Context, req *UpdateELBRequest) (rsp *OperationResponse, err error)
	ListAppELB(ctx context.Context, req *ListELBRequest) (rsp *ListELBResponse, err error)
	GetAppELBDetail(ctx context.Context, req *GetAppELBDetailRequest) (rep *GetAppELBDetailResponse, err error)

	// 获取加密资源账号
	GetEncryptAccountID(ctx context.Context, req *GetEncryptAccountRequest) (rsp *GetEncryptAccountResponse, err error)

	// 服务器组相关接口
	CreateAppServerGroup(ctx context.Context, req *CreateServerGroupRequest) (rsp *CreateServerGroupResponse, err error)
	DeleteAppServerGroup(ctx context.Context, req *DeleteServerGroupRequest) (rsp *OperationResponse, err error)
	UpdateAppServerGroup(ctx context.Context, req *UpdateServerGroupRequest) (rsp *OperationResponse, err error)
	ListAppServerGroup(ctx context.Context, req *ListServerGroupRequest) (rsp *ListServerGroupResponse, err error)
	CreateAppServerGroupPort(ctx context.Context, req *CommonServerGroupPortRequest) (rsp *CreateServerGroupPortResponse, err error)
	UpdateAppServerGroupPort(ctx context.Context, req *CommonServerGroupPortRequest) (rsp *OperationResponse, err error)
	DeleteAppServerGroupPort(ctx context.Context, req *DeleteServerGroupPortRequest) (rsp *OperationResponse, err error)
	CreateAppBLBRs(ctx context.Context, req *CommonAppBlbRsRequest) (rsp *OperationResponse, err error)
	UpdateAppBLBRs(ctx context.Context, req *CommonAppBlbRsRequest) (rsp *OperationResponse, err error)
	ListAppBLBRs(ctx context.Context, req *ListAppBlbRsRequest) (rsp *ListAppBlbRsResponse, err error)
	DeleteAppBLBRs(ctx context.Context, req *DeleteAppBlbRsRequest) (rsp *OperationResponse, err error)
	ListAppBLBRsMount(ctx context.Context, req *CommonAppBlbRsListRequest) (rsp *CommonAppBlbRsListResponse, err error)
	ListAppBLBRsUnmount(ctx context.Context, req *CommonAppBlbRsListRequest) (rsp *CommonAppBlbRsListResponse, err error)

	// 监听器相关接口
	CreateAppTCPListener(ctx context.Context, req *CreateAppTCPListenerRequest) (rsp *OperationResponse, err error)
	UpdateAppTCPListener(ctx context.Context, req *CommonAppTCPListenerRequest) (rsp *OperationResponse, err error)
	ListAppTCPListener(ctx context.Context, req *CommonListAppListenerRequest) (rsp *ListAppTCPListenerResponse, err error)
	CreateAppHTTPListener(ctx context.Context, req *CommonAppHTTPListenerRequest) (rsp *OperationResponse, err error)
	UpdateAppHTTPListener(ctx context.Context, req *CommonAppHTTPListenerRequest) (rsp *OperationResponse, err error)
	ListAppHTTPListener(ctx context.Context, req *CommonListAppListenerRequest) (rsp *ListAppHTTPListenerResponse, err error)
	DeleteAppListener(ctx context.Context, req *DeleteAppListenerRequest) (rsp *OperationResponse, err error)
	CreateAppPolicys(ctx context.Context, req *CreateAppPolicysRequest) (rsp *OperationResponse, err error)
	ListAppPolicys(ctx context.Context, req *ListAppPolicysRequest) (rsp *ListAppPolicysResponse, err error)
	DeleteAppPolicys(ctx context.Context, req *DeleteAppPolicysRequest) (rsp *OperationResponse, err error)

	// ip组相关接口
	CreateAppIPGroup(ctx context.Context, req *CreateAppIPGroupRequest) (rsp *CreateAppIPGroupResponse, err error)
	DeleteAppIPGroup(ctx context.Context, req *DeleteAppIPGroupRequest) (rsp *OperationResponse, err error)
	UpdateAppIPGroup(ctx context.Context, req *UpdateAppIPGroupRequest) (rsp *OperationResponse, err error)
	ListAppIPGroup(ctx context.Context, req *ListAppIPGroupRequest) (rsp *ListAppIPGroupResponse, err error)
	CreateAppIPGroupPolicy(ctx context.Context, req *CommonAppIPGroupPolicyRequest) (rsp *OperationResponse, err error)
	UpdateAppIPGroupPolicy(ctx context.Context, req *CommonAppIPGroupPolicyRequest) (rsp *OperationResponse, err error)
	DeleteAppIPGroupPolicy(ctx context.Context, req *DeleteAppIPGroupPolicyRequest) (rsp *OperationResponse, err error)
	CreateAppIPGroupMember(ctx context.Context, req *CommonAppIPGroupMemberRequest) (rsp *OperationResponse, err error)
	UpdateAppIPGroupMember(ctx context.Context, req *CommonAppIPGroupMemberRequest) (rsp *OperationResponse, err error)
	DeleteAppIPGroupMember(ctx context.Context, req *DeleteAppIPGroupMemberRequest) (rsp *OperationResponse, err error)
	ListAppIPGroupMember(ctx context.Context, req *ListAppIPGroupMemberRequest) (rsp *ListAppIPGroupMemberResponse, err error)
}
