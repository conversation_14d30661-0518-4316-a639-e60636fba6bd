/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><EMAIL>)
 * Date: 2022/04/26
 * File: openapi.go
 */

/*
 * DESCRIPTION
 *   openapi sdk基类
 */

// Package common
package common

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/sdk/bce_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/common"
)

type MultiRegionOpenApi interface {
	// SetRalCaller 设置ral caller，目前仅用于ut
	SetRalCaller(caller sdk_utils.RalCaller) sdk_utils.RalCaller

	// DoRequest - 通用openapi请求方法
	DoRequest(ctx context.Context, params *MultiRegionOpenApiParams, result interface{}, opts ...sdk_utils.ROption) error

	// DoRequestEx - 通用openapi请求方法 (返回httpRsp)
	DoRequestEx(ctx context.Context, params *MultiRegionOpenApiParams, result interface{}, opts ...sdk_utils.ROption) (
		*sdk_utils.HttpResponse, error)
}

type MultiRegionOpenApiParams struct {
	OpenApiRequset *OpenApiParams
	Region         string
}

type multiRegionOpenApi struct {
	serviceName string
	serviceMap  map[string]*openApi
}

type MultiRegionCnf struct {
	ServiceName string
	RegionList  []string
}

// NewMultiRegionOpenApi 根据指定serviceName(ral名)创建OpenApi
func NewMultiRegionOpenApi(conf MultiRegionCnf) MultiRegionOpenApi {
	var m multiRegionOpenApi
	m.serviceMap = make(map[string]*openApi, 0)
	for _, region := range conf.RegionList {
		regionServiceName := conf.ServiceName + region
		s := &openApi{
			serviceName: regionServiceName,
			ralCaller: sdk_utils.NewRalCaller(
				regionServiceName,
				sdk_utils.ROptGenCurlLog(logit.TraceLevel),
			),
			signer: bce_utils.NewV1Signer(),
		}
		m.serviceMap[region] = s
	}

	return &m
}

func (m multiRegionOpenApi) SetRalCaller(caller sdk_utils.RalCaller) sdk_utils.RalCaller {
	// TODO implement me
	panic("implement me")
}

func (m multiRegionOpenApi) DoRequest(ctx context.Context, params *MultiRegionOpenApiParams, result interface{}, opts ...sdk_utils.ROption) error {
	s := m.pickOpenApi(ctx, params.Region)
	if s == nil {
		return errors.Errorf("cant pick a valid openapi,service:%s, region:%s", m.serviceName, params.Region)
	}
	return s.DoRequest(ctx, params.OpenApiRequset, result, opts...)
}

func (m multiRegionOpenApi) DoRequestEx(ctx context.Context, params *MultiRegionOpenApiParams, result interface{}, opts ...sdk_utils.ROption) (*sdk_utils.HttpResponse, error) {
	s := m.pickOpenApi(ctx, params.Region)
	if s == nil {
		return nil, errors.Errorf("cant pick a valid openapi,service:%s, region:%s", m.serviceName, params.Region)
	}
	return s.DoRequestEx(ctx, params.OpenApiRequset, result, opts...)
}

func (m multiRegionOpenApi) pickOpenApi(ctx context.Context, region string) *openApi {
	innerRegion := common.GetInnerRegion(ctx, region)
	if s, ok := m.serviceMap[innerRegion]; ok {
		return s
	}
	return nil
}
