// protofsg -with_context -json_tag=1 common.proto interface.go
option cc_generic_services = true;
option go_package = "./;common";
package x1-base.sdk.common;

message ResourceAccount {
    optional string resource_ak = 1;
    optional string encrypt_account_id = 2;
}

message Credential {
    optional string ak = 1;
    optional string sk = 2;
    optional string session_token = 3;
}

message Authentication {
    optional string iam_user_id = 1;
    optional string transaction_id = 2;
    optional Credential credential = 3;
    //@inject_tag json:",omitempty"
    optional ResourceAccount resource_account = 4;
}
