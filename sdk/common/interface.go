/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-01-24
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据common.proto生成的interface文件
 */

// Package common
package common

type ResourceAccount struct {
	ResourceAk       string `json:"resource_ak"`
	EncryptAccountId string `json:"encrypt_account_id"`
}

type Credential struct {
	Ak           string `json:"ak"`
	Sk           string `json:"sk"`
	SessionToken string `json:"session_token"`
}

type Authentication struct {
	IamUserId       string           `json:"iam_user_id"`
	TransactionId   string           `json:"transaction_id"`
	Credential      *Credential      `json:"credential"`
	ResourceAccount *ResourceAccount `json:"resource_account,omitempty"`
}
