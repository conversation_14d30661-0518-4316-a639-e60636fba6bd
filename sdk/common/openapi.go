/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-01-24
 * File: openapi.go
 */

/*
 * DESCRIPTION
 *   openapi sdk基类
 */

// Package common
package common

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bce_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/common"
)

const (
	AdaptorToken = "qwefasfgwqeuh43241adsf"
)

type OpenApi interface {
	// SetRalCaller 设置ral caller，目前仅用于ut
	SetRalCaller(caller sdk_utils.RalCaller) sdk_utils.RalCaller

	// DoRequest - 通用openapi请求方法
	DoRequest(ctx context.Context, params *OpenApiParams, result any, opts ...sdk_utils.ROption) error

	// DoRequestEx - 通用openapi请求方法 (返回httpRsp)
	DoRequestEx(ctx context.Context, params *OpenApiParams, result any, opts ...sdk_utils.ROption) (
		*sdk_utils.HttpResponse, error)

	// DoRequestWithCustomizeHeader - 通用openapi请求方法 (自定义header)
	DoRequestWithCustomizeHeader(ctx context.Context, params *OpenApiParams, result any, opts ...sdk_utils.ROption) (
		*sdk_utils.HttpResponse, error)

	DoRequestWithCustomizeHeaderEx(ctx context.Context, params *OpenApiParams, result any, opts ...sdk_utils.ROption) (
		*sdk_utils.HttpResponse, error)
}

type OpenApiParams struct {
	ActionName      string          `json:"action_name"`                 // 必填, 用于log
	Auth            *Authentication `json:"auth,omitempty"`              // 可选, 通过sts SDK的GetOpenApiAuth获得
	Token           string          `json:"token,omitempty"`             // 可选, 通过sts SDK的GetAssumeRole获得(Token.ID)
	HttpMethod      string          `json:"http_method"`                 // 必填, http method
	Uri             string          `json:"uri,omitempty"`               // 可选, url without host
	Queries         any             `json:"queries,omitempty"`           // 可选, url queries
	Headers         any             `json:"headers,omitempty"`           // 可选, http headers
	Posts           any             `json:"posts,omitempty"`             // 可选, raw post body
	PrivateCloudEnv string          `json:"private_cloud_env,omitempty"` // 可选，在自由化场景使用
	Product         string          `json:"product,omitempty"`           // 可选，product name
}

type openApi struct {
	serviceName string
	ralCaller   sdk_utils.RalCaller
	signer      bce_utils.Signer
}

// NewOpenApi 根据指定serviceName(ral名)创建OpenApi
func NewOpenApi(serviceName string) OpenApi {
	s := &openApi{
		serviceName: serviceName,
		ralCaller: sdk_utils.NewRalCaller(
			serviceName,
			sdk_utils.ROptGenCurlLog(logit.TraceLevel),
		),
		signer: bce_utils.NewV1Signer(),
	}

	return s
}

// SetRalCaller 设置ral caller，目前仅用于ut
func (s *openApi) SetRalCaller(caller sdk_utils.RalCaller) sdk_utils.RalCaller {
	last := s.ralCaller
	s.ralCaller = caller
	return last
}

// DoRequest - 通用openapi请求方法
func (s *openApi) DoRequest(ctx context.Context, params *OpenApiParams, result any,
	opts ...sdk_utils.ROption) error {
	// 设置ROption
	opts = append([]sdk_utils.ROption{
		// final check
		sdk_utils.ROptFinalChecker(s.getFinalChecker(ctx, params)),
	}, opts...)

	_, err := s.DoRequestEx(ctx, params, result, opts...)
	return err
}

// DoRequestWithCustomizeHeader - 通用openapi请求方法
func (s *openApi) DoRequestWithCustomizeHeader(ctx context.Context, params *OpenApiParams, result any,
	opts ...sdk_utils.ROption) (*sdk_utils.HttpResponse, error) {
	// 设置ROption
	opts = append([]sdk_utils.ROption{
		// final check
		sdk_utils.ROptFinalChecker(s.getFinalChecker(ctx, params)),
	}, opts...)

	httpRsp, err := s.DoRequestWithCustomizeHeaderEx(ctx, params, result, opts...)
	return httpRsp, err
}

// DoRequestWithCustomizeHeader - 通用openapi请求方法
func (s *openApi) DoRequestWithCustomizeHeaderEx(ctx context.Context, params *OpenApiParams, result any,
	opts ...sdk_utils.ROption) (*sdk_utils.HttpResponse, error) {
	// 参数检查
	if err := s.checkParams(ctx, params); err != nil {
		logger.SdkLogger.Warning(ctx, "params error: %s", err)
		return nil, err
	}

	// 设置ROption
	opts = append([]sdk_utils.ROption{
		// 设置auth header
		sdk_utils.ROptHttpReqBuilder(s.getHttpReqBuilder(ctx, params)),

		// prepare check
		sdk_utils.ROptPrepareChecker(s.getPrepareChecker(ctx, params)),

		// Decode error处理
		sdk_utils.ROptDecoder(s.getOnErrorDecoderWrapper(ctx, params)),
	}, opts...)

	logger.SdkLogger.Trace(ctx,
		"%s: ral request start, params: %s", params.ActionName, base_utils.Format(params))
	// 请求 openapi
	httpRsp, err := s.ralCaller.WithOption(opts...).HttpRequestWithCustomizeHeader(ctx, params.HttpMethod, params.Uri,
		params.Queries, params.Posts, params.Headers, result)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.SdkLogger.Warning(ctx,
			"%s: ral request fail, params: %s, err: %s",
			params.ActionName, base_utils.Format(params), err)
	}

	return httpRsp, err
}

// DoRequestEx - 通用openapi请求方法
func (s *openApi) DoRequestEx(ctx context.Context, params *OpenApiParams, result any,
	opts ...sdk_utils.ROption) (*sdk_utils.HttpResponse, error) {
	// 参数检查
	if err := s.checkParams(ctx, params); err != nil {
		logger.SdkLogger.Warning(ctx, "params error: %s", err)
		return nil, err
	}

	// 设置ROption
	opts = append([]sdk_utils.ROption{
		// 设置auth header
		sdk_utils.ROptHttpReqBuilder(s.getHttpReqBuilder(ctx, params)),

		// prepare check
		sdk_utils.ROptPrepareChecker(s.getPrepareChecker(ctx, params)),

		// Decode error处理
		sdk_utils.ROptDecoder(s.getOnErrorDecoderWrapper(ctx, params)),
	}, opts...)

	// 请求 openapi
	httpRsp, err := s.ralCaller.WithOption(opts...).HttpRequestEx(ctx, params.HttpMethod, params.Uri,
		params.Queries, params.Posts, params.Headers, result)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.SdkLogger.Warning(ctx,
			"%s: ral request fail, params: %s, err: %s",
			params.ActionName, base_utils.Format(params), err)
	}

	return httpRsp, err
}

// checkParams 检查OpenApiParams
func (s *openApi) checkParams(ctx context.Context, params *OpenApiParams) error {
	if params == nil {
		return cerrs.ErrInvalidParams.Errorf("null params")
	}
	if params.ActionName == "" {
		return cerrs.ErrInvalidParams.Errorf("actionName needed")
	}
	if params.Auth == nil && params.Token == "" {
		return cerrs.ErrInvalidParams.Errorf("Auth or Token needed")
	}
	if params.HttpMethod == "" {
		return cerrs.ErrInvalidParams.Errorf("httpMethod needed")
	}
	return nil
}

// getHttpReqBuilder 获取设置auth header的http req builder
func (s *openApi) getHttpReqBuilder(ctx context.Context, params *OpenApiParams) sdk_utils.HttpReqBuilder {
	if params.PrivateCloudEnv == PrivateCloudEnvDbStackAdapterBCC {
		return s.getDbstackAdaptorBccHttpReqBuilder(ctx, params)
	}
	if params.Auth != nil {
		return s.getHttpReqBuilderWithCredential(ctx, params)
	}
	if params.PrivateCloudEnv != "" {
		return s.getPrivateCloudHttpReqBuilder(ctx, params)
	}
	return s.getHttpReqBuilderWithToken(ctx, params)
}

// getHttpReqBuilderWithCredential 获取设置credential header的http req builder
func (s *openApi) getHttpReqBuilderWithCredential(ctx context.Context, params *OpenApiParams) sdk_utils.HttpReqBuilder {
	return func(_ context.Context, opt sdk_utils.Option, addr sdk_utils.Addr, httpReq *sdk_utils.HttpRequest) error {
		// 获取当前时间
		nowUtc := time.Now().UTC()

		// 获取host
		host := httpReq.GetHost()
		host, _ = sdk_utils.SplitHostPort(host)

		// 设置headers
		auth := params.Auth
		product := "scs"
		if params.Product != "" {
			product = params.Product
		}
		httpReq.SetHeader("Host", host)
		httpReq.SetHeader("User-Agent", product)
		httpReq.SetHeader("X-Auth-Token", auth.IamUserId)
		httpReq.SetHeader("x-bce-security-Token", auth.Credential.SessionToken)
		httpReq.SetHeader("x-bce-request-id", auth.TransactionId)
		httpReq.SetHeader("x-bce-date", base_utils.FormatWithISO8601(nowUtc))
		if common.IsEdgeRegion() {
			httpReq.SetHeader("x1-adaptor-token", AdaptorToken+"|"+auth.IamUserId)
		}
		if auth.ResourceAccount != nil {
			httpReq.SetHeader("resource-account-id", auth.ResourceAccount.EncryptAccountId)
			httpReq.SetHeader("paas-accesskey", auth.ResourceAccount.ResourceAk)
			httpReq.SetHeader("paas-application", product)
		}

		// 设置 openapi paas 标志
		if params.Headers != nil {
			ResourceAccountID, ok := params.Headers.(map[string]any)["resource-accountId"]
			if ok {
				httpReq.SetHeader("resource-accountId", ResourceAccountID.(string))
			}
			ResourceSource, ok := params.Headers.(map[string]any)["resource-source"]
			if ok {
				httpReq.SetHeader("resource-source", ResourceSource.(string))
			}
		}

		// 获取authorization string
		credReq := &bce_utils.CredentialRequest{
			Ak:              auth.Credential.Ak,
			Sk:              auth.Credential.Sk,
			HttpMethod:      params.HttpMethod,
			Uri:             params.Uri,
			Queries:         params.Queries,
			Headers:         httpReq.Header,
			HeadersMustSign: []string{"Host"},
			Timestamp:       nowUtc.Unix(),
			DurationSec:     1800,
		}
		authString, err := s.signer.GetSignature(credReq)
		if err != nil {
			logger.SdkLogger.Warning(ctx, "%s: get authorization fail, err: %s",
				params.ActionName, err)
			return cerrs.ErrAuthFail.Wrap(err)
		}

		// 添加authorization header
		httpReq.SetHeader("Authorization", authString)
		return nil
	}
}

// getHttpReqBuilderWithToken 获取设置token header的http req builder
func (s *openApi) getHttpReqBuilderWithToken(ctx context.Context, params *OpenApiParams) sdk_utils.HttpReqBuilder {
	return func(_ context.Context, opt sdk_utils.Option, addr sdk_utils.Addr, httpReq *sdk_utils.HttpRequest) error {
		// 设置headers
		product := "scs"
		if params.Product != "" {
			product = params.Product
		}
		httpReq.SetHeader("User-Agent", product)
		httpReq.SetHeader("X-Auth-Token", params.Token)
		httpReq.SetHeader("x-bce-request-id", base_utils.GetReqID(ctx))
		httpReq.SetHeader("x-bce-date", base_utils.FormatWithISO8601(time.Now().UTC()))
		return nil
	}
}

// getFinalChecker 获取默认的final checker
func (s *openApi) getFinalChecker(ctx context.Context, params *OpenApiParams) sdk_utils.FinalChecker {
	return func(_ context.Context, httpRsp *sdk_utils.HttpResponse, result any, invoker sdk_utils.FinalCheckFunc) error {
		switch {
		case httpRsp.StatusCode == http.StatusOK ||
			httpRsp.StatusCode == http.StatusCreated ||
			httpRsp.StatusCode == http.StatusAccepted:
			return nil
		case httpRsp.StatusCode == http.StatusUnauthorized ||
			httpRsp.StatusCode == http.StatusForbidden:
			logger.SdkLogger.Warning(ctx, "%s: %v, params: %s",
				params.ActionName, httpRsp, base_utils.Format(params))
			return cerrs.ErrAuthFail.Errorf("%s: %v", params.ActionName, httpRsp)
		case (params.HttpMethod == http.MethodGet ||
			params.HttpMethod == http.MethodDelete ||
			params.HttpMethod == http.MethodHead) &&
			httpRsp.StatusCode == http.StatusNotFound:
			logger.SdkLogger.Trace(ctx, "%s: %v, params: %s",
				params.ActionName, httpRsp, base_utils.Format(params))
			return cerrs.ErrNotFound.Errorf("%s: %v", params.ActionName, httpRsp)
		case params.HttpMethod != http.MethodGet &&
			params.HttpMethod != http.MethodHead &&
			httpRsp.StatusCode > 200 && httpRsp.StatusCode < 300:
			logger.SdkLogger.Trace(ctx, "%s: %v, params: %s",
				params.ActionName, httpRsp, base_utils.Format(params))
			return nil
		default:
			logger.SdkLogger.Warning(ctx, "%s: %v, params: %s",
				params.ActionName, httpRsp, base_utils.Format(params))
			return cerrs.ErrHttpStatusError.Errorf("%s: %v", params.ActionName, httpRsp)
		}
	}
}

// getOnErrorDecoderWrapper 获取OnErrorDecoderWrapper
func (s *openApi) getOnErrorDecoderWrapper(ctx context.Context, params *OpenApiParams) sdk_utils.Decoder {
	return sdk_utils.OnErrorDecoderWrapper(nil, func(err error, rawBytes []byte) error {
		// 有些api仅在失败时返回result
		if params.HttpMethod != http.MethodGet && len(rawBytes) == 0 {
			return nil
		}
		logger.SdkLogger.Warning(ctx, "%s: decode fail, err: %s, rawBytes: %s",
			params.ActionName, err.Error(), base_utils.Format(rawBytes))
		return err
	})
}

// getPrepareChecker 获取默认的prepare check
func (s *openApi) getPrepareChecker(ctx context.Context, params *OpenApiParams) sdk_utils.PrepareChecker {
	return func(_ context.Context, httpRsp *sdk_utils.HttpResponse, invoker sdk_utils.PrepareCheckFunc) error {
		logger.SdkLogger.Debug(ctx, "%s: http rsp: %v", params.ActionName, httpRsp)
		return invoker(ctx, httpRsp)
	}
}

const PrivateCloudEnvLiXiangSDE = "li_cloud"
const PrivateCloudEnvDbStackAdapterBCC = "dbstackadapterbcc"

func (s *openApi) getPrivateCloudHttpReqBuilder(ctx context.Context, params *OpenApiParams) sdk_utils.HttpReqBuilder {
	switch params.PrivateCloudEnv {
	case PrivateCloudEnvLiXiangSDE:
		return func(_ context.Context, opt sdk_utils.Option, addr sdk_utils.Addr, httpReq *sdk_utils.HttpRequest) error {
			// 设置headers
			httpReq.SetHeader("M2M-SDE-AUTH", params.Token)
			httpReq.SetHeader("X-SDE-Access-Token", "d63592bd2132695c1f331761080f22ab")
			return nil
		}
	default:
		panic(fmt.Sprintf("not support this private cloud env:%s", params.PrivateCloudEnv))
	}
}

func (s *openApi) getDbstackAdaptorBccHttpReqBuilder(ctx context.Context, params *OpenApiParams) sdk_utils.HttpReqBuilder {
	return func(_ context.Context, opt sdk_utils.Option, addr sdk_utils.Addr, httpReq *sdk_utils.HttpRequest) error {
		// 设置headers
		product := "scs"
		if params.Product != "" {
			product = params.Product
		}
		httpReq.SetHeader("User-Agent", product)
		httpReq.SetHeader("X-Auth-Token", params.Auth.IamUserId)
		httpReq.SetHeader("x-bce-request-id", base_utils.GetReqID(ctx))
		httpReq.SetHeader("x-bce-date", base_utils.FormatWithISO8601(time.Now().UTC()))
		return nil
	}
}
