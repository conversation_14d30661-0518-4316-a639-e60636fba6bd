package common

import (
	"github.com/spf13/cast"
)

// ReuseWithTransaction 修改auth的TransactionId
func (auth *Authentication) ReuseWithTransaction(transactionId string) *Authentication {
	return &Authentication{
		IamUserId:       auth.IamUserId,
		TransactionId:   transactionId,
		ResourceAccount: auth.ResourceAccount,
		Credential:      auth.Credential,
	}
}

// Reuse 在原TransactionId上追加tag postfix
func (auth *Authentication) Reuse(tag interface{}) *Authentication {
	return auth.ReuseWithTransaction(auth.TransactionId + "_" + cast.ToString(tag))
}
