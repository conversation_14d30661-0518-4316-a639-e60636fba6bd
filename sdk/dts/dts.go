/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 * 2024/04/10 <EMAIL>
 *
 **************************************************************************/
package dts

import (
	"context"
	"net/http"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type dtsSdk struct {
	conf *dtsConf
	common.OpenApi
	ralCaller sdk_utils.RalCaller
}

// NewDefaultDtsSdk 新建一个默认的DTS服务实例，返回值为DTSService类型
func NewDefaultDtsSdk() DTSService {
	dtsSDK := newDtsSdk(DefaultServiceName)
	dtsSDK.conf = getConf(DefaultServiceName)
	return dtsSDK
}

// NewDtsSdk 新建一个DTSService类型的实例，返回值为DTSService类型
func NewDtsSdk(serviceName string) DTSService {
	return newDtsSdk(serviceName)
}

// newDtsSdk 新建一个 dtsSdk 实例，返回指向该实例的指针
func newDtsSdk(serviceName string) *dtsSdk {
	s := &dtsSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
		ralCaller: sdk_utils.NewRalCaller(
			serviceName,
			sdk_utils.ROptGenCurlLog(logit.TraceLevel),
			sdk_utils.ROptPrepareChecker(
				func(ctx context.Context, httpRsp *sdk_utils.HttpResponse, invoker sdk_utils.PrepareCheckFunc) error {
					logger.SdkLogger.Debug(ctx, "dts http rsp: %v", httpRsp)
					return invoker(ctx, httpRsp)
				}),
		),
	}

	return s
}

// doRequest - 通用openstack服务请求方法
func (s *dtsSdk) doRequest(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}

	// 请求 openapi
	err = s.DoRequest(ctx, params, rsp)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return err
	}
	return nil
}

// CreateTask 创建任务函数，接收一个上下文context和一个CreateTaskRequest类型的请求参数req，返回一个CreateTaskResponse类型的响应参数rsp和一个error类型的错误err
func (s *dtsSdk) CreateTask(ctx context.Context, req *CreateTaskRequest) (rsp *CreateTaskResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CreateTaskResponse{}
	if err = s.doRequest(ctx, "CreateTask", req.Auth, http.MethodPost,
		taskUri, nil, req, rsp); err != nil {
		return nil, err
	}
	return
}

// ConfigTask ConfigTask 配置任务，包括创建和修改任务信息
// ctx context.Context 请求上下文，可用于传递超时、取消等参数
// req *ConfigTaskRequest 配置任务请求参数，不能为nil
// rsp *ConfigTaskResponse 返回结果，包含taskId和errcode，如果errcode不为0则表示失败
// err error 错误信息，如果请求过程中发生错误则不为nil
func (s *dtsSdk) ConfigTask(ctx context.Context, req *ConfigTaskRequest) (rsp *ConfigTaskResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &ConfigTaskResponse{}
	if err = s.doRequest(ctx, "ConfigTask", req.Auth, http.MethodPost,
		taskUri+"/"+req.DtsID+"/config", nil, req, rsp); err != nil {
		return nil, err
	}
	return
}

// ShowTask 显示任务信息
//
// 参数：
//
//	ctx context.Context                  上下文信息，不能为空
//	req *ShowTaskRequest                 请求参数，结构体指针，不能为nil，具体字段说明见struct ShowTaskRequest
//
// 返回值：
//
//	rsp *ShowTaskResponse                 响应参数，结构体指针，不能为nil，具体字段说明见struct ShowTaskResponse
//	err error                             错误信息，如果请求成功，则err为nil，否则表示请求失败，需要查看err的详细信息
//
// 异常：
//
//	cerrs.ErrInvalidParams                请求参数req为nil时抛出此异常
func (s *dtsSdk) ShowTask(ctx context.Context, req *ShowTaskRequest) (rsp *ShowTaskResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &ShowTaskResponse{}
	if err = s.doRequest(ctx, "ShowTask", req.Auth, http.MethodGet,
		taskUri+"/"+req.DtsID, nil, nil, rsp); err != nil {
		return nil, err
	}
	return
}

// LaunchTaskPrecheck 检查任务是否可以启动，如果不能则返回错误信息。
// 参数：
//
//	ctx context.Context  上下文信息，可用于传递请求的超时、取消等信息
//	req *CommonRequest   请求参数，包含auth字段和dtsID字段，其中auth为认证信息，dtsID为任务ID
//
// 返回值：
//
//	rsp *CommonResponse    响应结果，包含success字段和message字段，其中success表示是否成功，message为错误信息（如果有）
//	err error              调用过程中发生的错误，如果没有错误则为nil
func (s *dtsSdk) LaunchTaskPrecheck(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CommonResponse{}
	if err = s.doRequest(ctx, "LaunchTaskPrecheck", req.Auth, http.MethodPost,
		taskUri+"/"+req.DtsID+"/precheck", nil, nil, rsp); err != nil {
		return nil, err
	}

	if !rsp.Success {
		return nil, cerrs.ErrDTSRequestFail.Errorf("LaunchTaskPrecheck request fail, req:%s, rsp:%s",
			base_utils.Format(req), base_utils.Format(rsp))
	}
	return
}

// ShowTaskPrecheck 显示任务预检查接口，参数为上下文和请求对象，返回值为响应对象和错误信息
func (s *dtsSdk) ShowTaskPrecheck(ctx context.Context, req *CommonRequest) (rsp *ShowTaskPrecheckResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &ShowTaskPrecheckResponse{}
	if err = s.doRequest(ctx, "ShowTaskPrecheck", req.Auth, http.MethodGet,
		taskUri+"/"+req.DtsID+"/precheck", nil, nil, rsp); err != nil {
		return nil, err
	}

	if !rsp.Success {
		return nil, cerrs.ErrDTSRequestFail.Errorf("ShowTaskPrecheck request fail, req:%s, rsp:%s",
			base_utils.Format(req), base_utils.Format(rsp))
	}
	return
}

// SkipTaskPrecheck 跳过任务预检查，不进行预检查，直接开始同步或迁移任务。
// 参数：
//
//	ctx context.Context - 请求上下文，可用于传递超时、取消信号等参数；如果没有该参数，默认为nil。
//	req *CommonRequest - 请求对象，包含DTS ID和认证信息等必要参数；如果该参数为nil，返回cerrs.ErrInvalidParams错误。
//
// 返回值：
//
//	rsp *CommonResponse - 响应对象，包含成功与否、错误码、错误信息等字段；如果发生系统级错误（如网络连接异常），则返回nil和错误信息。
//	err error - Go语言标准库中的error类型，表示调用SDK API出现的异常，如签名失败、参数格式错误等；如果调用成功，则err为nil
func (s *dtsSdk) SkipTaskPrecheck(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CommonResponse{}
	url := taskUri + "/" + req.DtsID
	queries := map[string]interface{}{
		"skipPrecheck": nil,
	}
	if err = s.doRequest(ctx, "SkipTaskPrecheck", req.Auth, http.MethodPut,
		url, queries, nil, rsp); err != nil {
		return nil, err
	}

	if !rsp.Success {
		return nil, cerrs.ErrDTSRequestFail.Errorf("SkipTaskPrecheck request fail, req:%s, rsp:%s",
			base_utils.Format(req), base_utils.Format(rsp))
	}
	return
}

// StartTask 开始任务函数，接收一个上下文对象和一个CommonRequest类型的请求参数，返回一个CommonResponse类型的响应参数和一个error类型的错误信息
func (s *dtsSdk) StartTask(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CommonResponse{}
	if err = s.doRequest(ctx, "StartTask", req.Auth, http.MethodPost,
		taskUri+"/"+req.DtsID+"/start", nil, nil, rsp); err != nil {
		return nil, err
	}

	if !rsp.Success {
		return nil, cerrs.ErrDTSRequestFail.Errorf("StartTask request fail, req:%s, rsp:%s",
			base_utils.Format(req), base_utils.Format(rsp))
	}
	return
}

// ShutdownTask 关闭任务，参数为上下文、请求对象，返回值为通用响应和错误信息
func (s *dtsSdk) ShutdownTask(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CommonResponse{}
	if err = s.doRequest(ctx, "ShutdownTask", req.Auth, http.MethodPost,
		taskUri+"/"+req.DtsID+"/shutdown", nil, nil, rsp); err != nil {
		return nil, err
	}

	if !rsp.Success {
		return nil, cerrs.ErrDTSRequestFail.Errorf("ShutdownTask request fail, req:%s, rsp:%s",
			base_utils.Format(req), base_utils.Format(rsp))
	}
	return
}

// DeleteTask 删除任务
//
// 参数：
//
//	ctx context.Context  上下文信息，可用于传递请求的超时、取消等参数
//	req *CommonRequest   请求参数，包含auth字段和dtsID字段，auth为认证信息，dtsID为任务ID
//
// 返回值：
//
//	rsp *CommonResponse   响应结果，包含success字段和message字段，success表示是否成功，message为错误信息（如有）
//	err error             调用过程中发生的错误，如网络连接失败等
func (s *dtsSdk) DeleteTask(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CommonResponse{}
	if err = s.doRequest(ctx, "DeleteTask", req.Auth, http.MethodDelete,
		taskUri+"/"+req.DtsID, nil, nil, rsp); err != nil {
		return nil, err
	}

	if !rsp.Success {
		return nil, cerrs.ErrDTSRequestFail.Errorf("DeleteTask request fail, req:%s, rsp:%s",
			base_utils.Format(req), base_utils.Format(rsp))
	}
	return
}

// ListTask 函数名称：ListTask
// 功能描述：调用ListTask接口获取任务列表信息
// 参数列表：
//   - ctx context.Context                  上下文信息，代表请求的唯一标识和超时设置
//   - req *ListTaskRequest                 请求参数结构体指针，包含认证信息和其他可选参数
//
// 返回值列表：
//   - rsp *ListTaskResponse                 响应参数结构体指针，包含任务列表信息和错误信息
//   - err error                             调用过程中出现的错误，如果没有错误则为nil
func (s *dtsSdk) ListTask(ctx context.Context, req *ListTaskRequest) (rsp *ListTaskResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &ListTaskResponse{}
	if err = s.doRequest(ctx, "ListTask", req.Auth, http.MethodPost,
		taskUri+"/list", nil, req, rsp); err != nil {
		return nil, err
	}
	return
}

// CreateChecksum 创建校验和函数，用于计算待传输的数据的MD5值。
// 参数：
//
//	ctx context.Context  上下文信息，可以为nil
//	req *CreateChecksumRequest  请求参数，不能为nil
//
// 返回值：
//
//	rsp *CreateChecksumResponse  响应结果，包括成功与否、错误信息等，当成功时err为nil，反之则不为nil
//	err error  请求过程中出现的错误，可能包括参数非法、服务器连接失败等
func (s *dtsSdk) CreateChecksum(ctx context.Context, req *CreateChecksumRequest) (rsp *CreateChecksumResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CreateChecksumResponse{}
	if err = s.doRequest(ctx, "CreateChecksum", req.Auth, http.MethodPost,
		checksumUri, nil, req, rsp); err != nil {
		return nil, err
	}

	if !rsp.Success {
		return nil, cerrs.ErrDTSRequestFail.Errorf("CreateChecksum request fail, req:%s, rsp:%s",
			base_utils.Format(req), base_utils.Format(rsp))
	}
	return
}

// ConfigChecksum ConfigChecksum 校验配置文件的MD5值，用于判断是否需要更新。
// 参数：
//
//	ctx context.Context - 请求上下文，不能为空
//	req *ConfigChecksumRequest - 请求参数，不能为nil，详情可查看ConfigChecksumRequest结构体定义
//
// 返回值：
//
//	rsp *CommonResponse - 响应结果，包括code、message和data字段，code为0表示成功，其他错误码见cerrors包，data为string类型，data为"success"或者"fail"
//	 err error - 请求出现错误，返回对应的错误，errno为cerrors包中定义的错误码
func (s *dtsSdk) ConfigChecksum(ctx context.Context, req *ConfigChecksumRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CommonResponse{}
	if err = s.doRequest(ctx, "ConfigChecksum", req.Auth, http.MethodPost,
		checksumUri+"/"+req.DtsID+"/config", nil, req, rsp); err != nil {
		return nil, err
	}

	if !rsp.Success {
		return nil, cerrs.ErrDTSRequestFail.Errorf("ConfigChecksum request fail, req:%s, rsp:%s",
			base_utils.Format(req), base_utils.Format(rsp))
	}
	return
}

// LaunchChecksumPrecheck 启动校验前置检查接口，用于校验数据源是否可以进行数据同步任务的创建和启动。
// 参数：
//
//	ctx context.Context  上下文信息，包含请求的超时、取消等信息。
//	req *CommonRequest   公共请求结构体，包含了所有请求的必需参数，如秘钥、地域等。
//
// 返回值：
//
//	rsp *CommonResponse    公共响应结构体，包含了所有响应的必需信息，如状态码、错误信息等。
//	err error              调用过程中出现的错误，如网络连接失败、参数错误等。
func (s *dtsSdk) LaunchChecksumPrecheck(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CommonResponse{}
	if err = s.doRequest(ctx, "LaunchChecksumPrecheck", req.Auth, http.MethodPost,
		checksumUri+"/"+req.DtsID+"/precheck", nil, nil, rsp); err != nil {
		return nil, err
	}

	if !rsp.Success {
		return nil, cerrs.ErrDTSRequestFail.Errorf("LaunchChecksumPrecheck request fail, req:%s, rsp:%s",
			base_utils.Format(req), base_utils.Format(rsp))
	}
	return
}

// StartChecksum 开始校验任务，参数为上下文和请求对象，返回值为响应对象和错误信息
func (s *dtsSdk) StartChecksum(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CommonResponse{}
	if err = s.doRequest(ctx, "StartChecksum", req.Auth, http.MethodPost,
		checksumUri+"/"+req.DtsID+"/start", nil, nil, rsp); err != nil {
		return nil, err
	}

	if !rsp.Success {
		return nil, cerrs.ErrDTSRequestFail.Errorf("StartChecksum request fail, req:%s, rsp:%s",
			base_utils.Format(req), base_utils.Format(rsp))
	}
	return
}

// ShutdownChecksum 关闭校验和功能，参数为上下文、请求对象，返回值为公共响应对象、错误信息
func (s *dtsSdk) ShutdownChecksum(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CommonResponse{}
	if err = s.doRequest(ctx, "ShutdownChecksum", req.Auth, http.MethodPost,
		checksumUri+"/"+req.DtsID+"/shutdown", nil, nil, rsp); err != nil {
		return nil, err
	}

	if !rsp.Success {
		return nil, cerrs.ErrDTSRequestFail.Errorf("ShutdownChecksum request fail, req:%s, rsp:%s",
			base_utils.Format(req), base_utils.Format(rsp))
	}
	return
}

// ShowChecksum 显示校验和信息
//
// 参数：
//
//	ctx context.Context   - 请求上下文，不能为空
//	req *CommonRequest    - 公共请求结构体，不能为nil，包含了Auth字段用于鉴权，DtsID字段表示任务ID
//
// 返回值：
//
//	rsp *ShowChecksumResponse - 响应结果，包含了Success字段判断是否成功，如果失败则会有具体的错误信息
//	err error                 - 调用过程中出现的错误，一般为nil，如果非nil则说明调用失败
func (s *dtsSdk) ShowChecksum(ctx context.Context, req *CommonRequest) (rsp *ShowChecksumResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &ShowChecksumResponse{}
	if err = s.doRequest(ctx, "ShowChecksum", req.Auth, http.MethodPost,
		checksumUri+"/"+req.DtsID+"/result", nil, nil, rsp); err != nil {
		return nil, err
	}

	if !rsp.Success {
		return nil, cerrs.ErrDTSRequestFail.Errorf("ShowChecksum request fail, req:%s, rsp:%s",
			base_utils.Format(req), base_utils.Format(rsp))
	}
	return
}
