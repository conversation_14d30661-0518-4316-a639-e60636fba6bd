/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 * 2024/04/10 <EMAIL>
 *
 **************************************************************************/
package dts

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "DTS"

// dtsConf definition
type dtsConf struct {
	Product string `toml:"Product, omitempty"`
}

var dtsConfMap = &sync.Map{}

// getConf 获取指定服务名的配置信息，如果不存在则加载并返回，否则直接返回已缓存的配置信息
func getConf(serviceName string) *dtsConf {
	if conf, ok := dtsConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*dtsConf); ok {
			return conf
		}
	}

	conf := &dtsConf{}
	conf.mustLoad(serviceName)

	dtsConfMap.Store(serviceName, conf)

	return conf
}

// mustLoad 加载并校验配置，若失败则panic
// serviceName: 服务名称，用于获取RAL配置
func (conf *dtsConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
