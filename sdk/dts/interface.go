/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * author: wangbin34 (<EMAIL>)
 * Date: 2024-04-10
 * File: interface.go
 */

// Package dts
package dts

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

// common
type CommonRequest struct {
	// DTS taskID or checkID
	DtsID string                 `json:"DtsID"`
	Auth  *common.Authentication `json:"-"`
}
type CommonResponse struct {
	Success bool   `json:"success"`
	Result  string `json:"result"`
}

// create
type CreateTaskRequest struct {
	ClientToken        string                 `json:"-"`
	ProductType        string                 `json:"productType"`
	Type               string                 `json:"type"`
	Standard           string                 `json:"standard"`
	SourceInstanceType string                 `json:"sourceInstanceType"`
	TargetInstanceType string                 `json:"targetInstanceType"`
	CrossRegionTag     int                    `json:"crossRegionTag"`
	DirectionType      string                 `json:"directionType"`
	OrderInfo          *OrderInfo             `json:"orderInfo"`
	Auth               *common.Authentication `json:"-"`
}

type OrderInfo struct {
	Src *Src `json:"src"`
	Dst *Dst `json:"dst"`
}

type Src struct {
	InstanceType string `json:"instanceType"`
	DbType       string `json:"dbType"`
	SliceNum     string `json:"sliceNum"`
}

type Dst struct {
	InstanceType string `json:"instanceType"`
	DbType       string `json:"dbType"`
}

type CreateTaskResponse struct {
	DtsTasks []DtsTask `json:"dtsTasks"`
}

type DtsTask struct {
	DtsID string `json:"dtsId"`
}

// config
type ConfigTaskRequest struct {
	Type          string                 `json:"type,omitempty"`
	DtsID         string                 `json:"dtsId,omitempty"`
	TaskName      string                 `json:"taskName"`
	DataType      []string               `json:"dataType"`
	SrcConnection *Connection            `json:"srcConnection"`
	DstConnection *Connection            `json:"dstConnection"`
	SchemaMapping []Schema               `json:"schemaMapping"`
	Granularity   string                 `json:"granularity,omitempty"`
	ProductType   string                 `json:"productType,omitempty"`
	QueueType     string                 `json:"queueType,omitempty"`
	InitPosition  InitPosition           `json:"initPosition,omitempty"`
	NetType       string                 `json:"netType,omitempty"`
	Admin         string                 `json:"admin,omitempty"`
	Auth          *common.Authentication `json:"-"`
}

type ConfigTaskResponse struct {
	DtsID string `json:"dtsId"`
}

// show(query)
type ShowTaskRequest struct {
	DtsID string
	Auth  *common.Authentication
}

type ShowTaskResponse struct {
	DtsID               string       `json:"dtsId"`
	TaskName            string       `json:"taskName"`
	Status              string       `json:"status"`
	DataType            []string     `json:"dataType"`
	Region              string       `json:"region"`
	CreateTime          string       `json:"createTime"`
	SrcConnection       *Connection  `json:"srcConnection"`
	DstConnection       *Connection  `json:"dstConnection"`
	SchemaMapping       []Schema     `json:"schemaMapping,omitempty"`
	RunningTime         int          `json:"runningTime"`
	SubStatus           []SubStatus  `json:"subStatus,omitempty"`
	DynamicInfo         *DynamicInfo `json:"dynamicInfo,omitempty"`
	Errmsg              string       `json:"errmsg,omitempty"`
	SdkRealtimeProgress string       `json:"sdkRealtimeProgress,omitempty"`
	Granularity         string       `json:"granularity,omitempty"`
	SubDataScope        SubDataScope `json:"subDataScope,omitempty"`
	PayInfo             PayInfo      `json:"payInfo,omitempty"`
	LockStatus          string       `json:"lockStatus,omitempty"`
	DtsIDPos            string       `json:"dtsIdPos,omitempty"`
	DtsIDNeg            string       `json:"dtsIdNeg,omitempty"`
}

type InitPosition struct {
	Type     string `json:"type"`
	Position string `json:"position"`
}

type Schema struct {
	Type  string `json:"type"`
	Src   string `json:"src"`
	Dst   string `json:"dst"`
	Where string `json:"where"`
}

type Connection struct {
	Region           string `json:"region"`
	DbType           string `json:"dbType"`
	DbUser           string `json:"dbUser,omitempty"`
	DbPass           string `json:"dbPass"`
	DbPort           int    `json:"dbPort"`
	DbHost           string `json:"dbHost"`
	InstanceID       string `json:"instanceId,omitempty"`
	DbServer         string `json:"dbServer,omitempty"`
	InstanceType     string `json:"instanceType"`
	InstanceShortID  string `json:"instanceShortId,omitempty"`
	FieldWhitelist   string `json:"field_whitelist,omitempty"`
	FieldBlacklist   string `json:"field_blacklist,omitempty"`
	StartTime        string `json:"startTime,omitempty"`
	EndTime          string `json:"endTime,omitempty"`
	SqlType          string `json:"sqlType,omitempty"`
	VpcID            string `json:"vpcId,omitempty"`
	VpcName          string `json:"vpcName,omitempty"`
	VpcCidr          string `json:"vpcCidr,omitempty"`
	VpcShortID       string `json:"vpcShortId,omitempty"`
	PositionStrategy string `json:"positionStrategy,omitempty"`
}

type SubStatus struct {
	S string `json:"s"`
	B string `json:"b"`
	I string `json:"i"`
}

type DynamicInfo struct {
	Schema    []SchemaInfo `json:"schema"`
	Base      []SchemaInfo `json:"base"`
	Increment Increment    `json:"increment"`
}

type Increment struct {
	Delay      int64  `json:"delay"`
	Position   string `json:"position"`
	SyncStatus string `json:"syncStatus"`
}

type SchemaInfo struct {
	Current          string `json:"current"`
	Count            string `json:"count"`
	Speed            string `json:"speed"`
	ExpectFinishTime string `json:"expectFinishTime"`
}

type SubDataScope struct {
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
}

type PayInfo struct {
	ProductType        string `json:"productType"`
	SourceInstanceType string `json:"sourceInstanceType"`
	TargetInstanceType string `json:"targetInstanceType"`
	CrossRegionTag     int    `json:"crossRegionTag"`
	CreateTime         int    `json:"createTime"`
	Standard           string `json:"standard"`
	EndTime            string `json:"endTime"`
}

// showTaskPrecheck
type CheckResult struct {
	Name         string `json:"name"`
	Status       string `json:"status"`
	Message      string `json:"message"`
	Subscription string `json:"subscription"`
}

type ShowTaskPrecheckResponse struct {
	Success bool          `json:"success"`
	Result  []CheckResult `json:"result"`
}

// listTask
type ListTaskRequest struct {
	MaxKeys int                    `json:"maxKeys"`
	Marker  string                 `json:"marker"`
	Type    string                 `json:"type"`
	Auth    *common.Authentication `json:"-"`
}

type ListTaskResponse struct {
	Marker      string             `json:"marker"`
	MaxKeys     int                `json:"maxKeys"`
	IsTruncated bool               `json:"isTruncated"`
	NextMarker  string             `json:"nextMarker"`
	Task        []ShowTaskResponse `json:"task"`
}

//-------------------------------------------------------------------------checksum

// create
type CreateChecksumRequest struct {
	Type       string                 `json:"type"`       // 校验类型
	Region     string                 `json:"region"`     // 区域
	AssocDtsID string                 `json:"assocDtsId"` // 关联迁移任务dtsId
	Amount     int                    `json:"amount"`     // 数量
	Version    string                 `json:"version"`    // 版本
	Auth       *common.Authentication `json:"-"`
}

type DtsCheckTask struct {
	DtsCheckId string `json:"dtsCheckId"`
}

type CreateChecksumResult struct {
	DtsCheckTasks []DtsCheckTask `json:"dtsCheckTasks"`
}

type CreateChecksumResponse struct {
	Success bool                  `json:"success"`
	Result  *CreateChecksumResult `json:"result"`
}

// config
type ChecksumConnection struct {
	InstanceType string `json:"instanceType"`
	DbType       string `json:"dbType"`
	Region       string `json:"region"`
}

type DetailConfig struct {
	UseAssocDtsTaskConn string `json:"useAssocDtsTaskConn"`
}

type SchemaMapping []interface{}

type ConfigChecksumRequest struct {
	DtsID         string                 `json:"DtsID"`
	SrcConnection *ChecksumConnection    `json:"srcConnection"`
	DstConnection *ChecksumConnection    `json:"dstConnection"`
	Granularity   string                 `json:"granularity"`
	DetailConfig  DetailConfig           `json:"detailConfig"`
	SchemaMapping SchemaMapping          `json:"schemaMapping"`
	Auth          *common.Authentication `json:"-"`
}

// showChecksum
type ChecksumResult struct {
	Status            string `json:"status"`
	ChecksumResult    string `json:"checksumResult"`
	TotalKeyCount     string `json:"totalKeyCount"`
	CheckedKeyCount   string `json:"checkedKeyCount"`
	UncheckedKeyCount string `json:"uncheckedKeyCount"`
	UncheckedKeyList  []struct {
		DbName  string `json:"dbName"`
		KeyName string `json:"keyName"`
		Message string `json:"message"`
	} `json:"uncheckedKeyList"`
	InconsistentCount string `json:"inconsistentCount"`
	InconsistentList  []struct {
		DbName           string `json:"dbName"`
		InconsistentType string `json:"inconsistentType"`
		SrcData          string `json:"srcData"`
		DstData          string `json:"dstData"`
	} `json:"inconsistentList"`
}

type ShowChecksumResult struct {
	ChecksumResult *ChecksumResult `json:"checksumResult"`
}

type ShowChecksumResponse struct {
	Success bool               `json:"success"`
	Result  ShowChecksumResult `json:"result"`
}

type DTSService interface {
	CreateTask(ctx context.Context, req *CreateTaskRequest) (rsp *CreateTaskResponse, err error)
	ConfigTask(ctx context.Context, req *ConfigTaskRequest) (rsp *ConfigTaskResponse, err error)
	ShowTask(ctx context.Context, req *ShowTaskRequest) (rsp *ShowTaskResponse, err error)
	LaunchTaskPrecheck(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error)
	ShowTaskPrecheck(ctx context.Context, req *CommonRequest) (rsp *ShowTaskPrecheckResponse, err error)
	SkipTaskPrecheck(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error)
	StartTask(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error)
	ShutdownTask(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error)
	DeleteTask(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error)
	ListTask(ctx context.Context, req *ListTaskRequest) (rsp *ListTaskResponse, err error)

	CreateChecksum(ctx context.Context, req *CreateChecksumRequest) (rsp *CreateChecksumResponse, err error)
	ConfigChecksum(ctx context.Context, req *ConfigChecksumRequest) (rsp *CommonResponse, err error)
	LaunchChecksumPrecheck(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error)
	StartChecksum(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error)
	ShutdownChecksum(ctx context.Context, req *CommonRequest) (rsp *CommonResponse, err error)
	ShowChecksum(ctx context.Context, req *CommonRequest) (rsp *ShowChecksumResponse, err error)
}
