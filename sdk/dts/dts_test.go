/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 * 2024/04/10 <EMAIL>
 *
 **************************************************************************/
package dts

import (
	"context"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	//"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const (
	TestDtsID            = "dtsmtpjo0nym456mhqsf"
	TestTaskName         = "SCS_MODIFYTYPE:scs-test-app"
	TaskDtsStatus        = "running"
	TestSrcRedisIP       = "127.0.0.1"
	TestSrcRedisPort     = 6379
	TestSrcRedisPassword = ""
	TestDstRedisIP       = "*********"
	TestDstRedisPort     = 6379
	TestDstRedisPassword = ""
)

// TestCreateTaskOk 测试CreateTaskOk函数，创建一个DTS任务并验证返回值是否正确
// 参数t：*testing.T类型，表示当前测试用例的对象指针
// 返回值：无
func TestCreateTaskOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	var dtsTasks []DtsTask
	dtsTasks = append(dtsTasks, DtsTask{DtsID: TestDtsID})
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CreateTaskResponse{
		DtsTasks: dtsTasks,
	}, nil, http.StatusOK))

	createRsp, err := s.CreateTask(ctx, &CreateTaskRequest{
		ProductType:        "postpay",
		Type:               "migration",
		Standard:           "large",
		SourceInstanceType: "public",
		TargetInstanceType: "public",
		OrderInfo: &OrderInfo{
			Src: &Src{
				InstanceType: "public",
				DbType:       "redis",
			},
			Dst: &Dst{
				InstanceType: "public",
				DbType:       "redis",
			},
		},
		Auth: &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test create dts task fail", t.Name())
		return
	}
	dtsTask := createRsp.DtsTasks[0]
	taskID := dtsTask.DtsID
	assert.Equal(t, taskID, TestDtsID)
}

// TestConfigTaskOk 测试ConfigTaskOk函数，确保配置任务成功。
// 参数t：*testing.T类型，表示当前测试用例的上下文信息。
// 返回值：无返回值
func TestConfigTaskOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ConfigTaskResponse{
		DtsID: TestDtsID,
	}, nil, http.StatusOK))

	_, err := s.ConfigTask(ctx, &ConfigTaskRequest{
		Type:     "migration",
		DtsID:    TestDtsID,
		TaskName: TestTaskName,
		DataType: []string{"increment", "base"},
		SrcConnection: &Connection{
			InstanceType:     "public",
			DbType:           "redis",
			Region:           "bj",
			DbHost:           TestSrcRedisIP,
			DbPort:           TestSrcRedisPort,
			DbPass:           TestSrcRedisPassword,
			PositionStrategy: "fail",
		},
		DstConnection: &Connection{
			InstanceType:     "public",
			DbType:           "redis",
			Region:           "bj",
			DbHost:           TestDstRedisIP,
			DbPort:           TestDstRedisPort,
			DbPass:           TestDstRedisPassword,
			PositionStrategy: "fail",
		},
		SchemaMapping: []Schema{},
		Granularity:   "instance",
		Auth:          &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test config dts task fail", t.Name())
		return
	}
}

// TestShowTaskOk 测试ShowTaskOk函数，该函数用于显示任务信息。
// 参数t是*testing.T类型的指针，表示当前正在执行的单元测试。
// 返回值没有，但可能会输出错误信息到标准错误输出。
func TestShowTaskOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ShowTaskResponse{}, nil, http.StatusOK))

	_, err := s.ShowTask(ctx, &ShowTaskRequest{
		DtsID: TestDtsID,
		Auth:  &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test show dts task fail", t.Name())
		return
	}
}

// TestLaunchTaskPrecheckOk 测试 LaunchTaskPrecheckOk 函数，该函数用于启动任务前的预检查，返回值为 CommonResponse 类型和 error 类型
func TestLaunchTaskPrecheckOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CommonResponse{
		Success: true,
	}, nil, http.StatusOK))

	_, err := s.LaunchTaskPrecheck(ctx, &CommonRequest{
		DtsID: TestDtsID,
		Auth:  &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test launch dts precheck fail", t.Name())
		return
	}
}

// TestShowTaskPrecheckOk 测试ShowTaskPrecheckOk函数，预检查成功
//
// 参数：
//   - t *testing.T   testing框架，用于记录错误
//
// 返回值：
//   - bool          无
//   - error         如果有错误则返回，否则为nil
func TestShowTaskPrecheckOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ShowTaskPrecheckResponse{
		Success: true,
	}, nil, http.StatusOK))

	_, err := s.ShowTaskPrecheck(ctx, &CommonRequest{
		DtsID: TestDtsID,
		Auth:  &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test show dts precheck fail", t.Name())
		return
	}
}

// TestSkipTaskPrecheckOk 测试SkipTaskPrecheckOk函数，该函数用于检查跳过任务预检查是否成功
// 参数t *testing.T - 测试对象指针
func TestSkipTaskPrecheckOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CommonResponse{
		Success: true,
	}, nil, http.StatusOK))

	_, err := s.SkipTaskPrecheck(ctx, &CommonRequest{
		DtsID: TestDtsID,
		Auth:  &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test skip dts precheck fail", t.Name())
		return
	}
}

// TestStartTaskOk 测试StartTaskOk函数，成功启动任务。
//
// 参数：
//   - t *testing.T   testing框架，用于记录错误信息。
//
// 返回值：
//   - (none)        无返回值，通过t.Errorf来输出错误信息。
func TestStartTaskOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CommonResponse{
		Success: true,
	}, nil, http.StatusOK))

	_, err := s.StartTask(ctx, &CommonRequest{
		DtsID: TestDtsID,
		Auth:  &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test start task fail", t.Name())
		return
	}
}

// TestShutdownTaskOk 测试ShutdownTaskOk函数，成功关闭任务
// 参数t *testing.T - 单元测试的上下文，用于标记错误
func TestShutdownTaskOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CommonResponse{
		Success: true,
	}, nil, http.StatusOK))

	_, err := s.ShutdownTask(ctx, &CommonRequest{
		DtsID: TestDtsID,
		Auth:  &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test shutdown taskfail", t.Name())
		return
	}
}

// TestDeleteTaskOk 测试DeleteTaskOk函数，删除任务成功
func TestDeleteTaskOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CommonResponse{
		Success: true,
	}, nil, http.StatusOK))

	_, err := s.DeleteTask(ctx, &CommonRequest{
		DtsID: TestDtsID,
		Auth:  &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test delete taskfail", t.Name())
		return
	}
}

func TestListTaskOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ListTaskResponse{}, nil, http.StatusOK))

	_, err := s.ListTask(ctx, &ListTaskRequest{
		Auth: &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test list task fail", t.Name())
		return
	}
}

func TestCreateChecksumOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CreateChecksumResponse{
		Success: true,
	}, nil, http.StatusOK))

	_, err := s.CreateChecksum(ctx, &CreateChecksumRequest{
		Auth: &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test create checksum fail", t.Name())
		return
	}
}

func TestConfigChecksumOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CommonResponse{
		Success: true,
	}, nil, http.StatusOK))

	_, err := s.ConfigChecksum(ctx, &ConfigChecksumRequest{
		Auth: &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test config checksum fail", t.Name())
		return
	}
}

func TestLaunchChecksumPrecheckOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CommonResponse{
		Success: true,
	}, nil, http.StatusOK))

	_, err := s.LaunchChecksumPrecheck(ctx, &CommonRequest{
		Auth: &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test launch checksum precheck fail", t.Name())
		return
	}
}

func TestStartChecksumOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CommonResponse{
		Success: true,
	}, nil, http.StatusOK))

	_, err := s.StartChecksum(ctx, &CommonRequest{
		Auth: &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test start checksum precheck fail", t.Name())
		return
	}
}

func TestShutdownChecksumOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CommonResponse{
		Success: true,
	}, nil, http.StatusOK))

	_, err := s.ShutdownChecksum(ctx, &CommonRequest{
		Auth: &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test shutdown checksum precheck fail", t.Name())
		return
	}
}

func TestShowChecksumOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDtsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ShowChecksumResponse{
		Success: true,
	}, nil, http.StatusOK))

	_, err := s.ShowChecksum(ctx, &CommonRequest{
		Auth: &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test show checksum precheck fail", t.Name())
		return
	}
}
