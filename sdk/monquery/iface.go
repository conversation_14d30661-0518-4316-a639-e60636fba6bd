package monquery

import "context"

const (
	GetHistoryItemDataRequestUrl string = "/monquery/getHistoryitemdata"
	GetLatestItemDataRequestUrl  string = "/monquery/getlastestitemdata"
)

type CommonRequest struct {
	Namespaces string `json:"namespaces"`
	Items      string `json:"items"`
	Start      string `json:"start"`
	End        string `json:"end"`
	Interval   string `json:"interval"`
	SampleFunc string `json:"sampleFunc"`
	Token      string `json:"token"`
}

type CommonResponse struct {
	Data    []*NamespaceData `json:"data"`
	Message string           `json:"message"`
	Success bool             `json:"success"`
}

type DataPoint struct {
	Timestamp interface{} `json:"Timestamp"`
	Value     float64     `json:"Value"`
}

type NamespaceData struct {
	NameSpace string                 `json:"NameSpace"`
	Items     map[string][]DataPoint `json:"Items"`
}

type MonqueryService interface {
	GetHistoryItemData(ctx context.Context, req *CommonRequest) (*CommonResponse, error)
	GetLatestItemData(ctx context.Context, req *CommonRequest) (*CommonResponse, error)
}
