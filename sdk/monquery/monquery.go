package monquery

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"net/http"
	"strings"
)

type monQuerySdk struct {
	common.OpenApi
}

func NewDefaultMonquerySdk() MonqueryService {
	return newMonquerySdk("monquery")
}

func NewMonquerySdk(serviceName string) MonqueryService {
	return newMonquerySdk(serviceName)
}

func newMonquerySdk(serviceName string) *monQuerySdk {
	s := &monQuerySdk{
		OpenApi: common.NewOpenApi(serviceName),
	}
	return s
}

func (s *monQuerySdk) doRequest(ctx context.Context, actionName string, token string,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	params := &common.OpenApiParams{
		ActionName: actionName,
		Token:      token,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
	}

	// 请求 openapi
	err = s.DoRequest(ctx, params, rsp)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return err
	}
	return nil
}

func (m *monQuerySdk) GetHistoryItemData(ctx context.Context, req *CommonRequest) (resp *CommonResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &CommonResponse{}
	queries := map[string]interface{}{
		"namespaces": req.Namespaces,
		"items":      req.Items,
		"start":      req.Start,
		"end":        req.End,
		"interval":   req.Interval,
		"sampleFunc": req.SampleFunc,
	}
	if err = m.doRequest(ctx, "BnsNodeServiceCreate", req.Token, http.MethodPost,
		GetHistoryItemDataRequestUrl, queries, req, resp); err != nil {
		return nil, err
	}

	for _, item := range strings.Split(req.Items, ",") {
		for _, ns := range resp.Data {
			if _, ok := ns.Items[item]; !ok {
				ns.Items[item] = []DataPoint{}
			}
		}
	}

	return resp, nil
}

func (m *monQuerySdk) GetLatestItemData(ctx context.Context, req *CommonRequest) (resp *CommonResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &CommonResponse{}
	queries := map[string]interface{}{
		"namespaces": req.Namespaces,
		"items":      req.Items,
	}
	if err = m.doRequest(ctx, "BnsNodeServiceCreate", req.Token, http.MethodPost,
		GetLatestItemDataRequestUrl, queries, req, resp); err != nil {
		return nil, err
	}

	for _, item := range strings.Split(req.Items, ",") {
		for _, ns := range resp.Data {
			if _, ok := ns.Items[item]; !ok {
				ns.Items[item] = []DataPoint{}
			}
		}
	}

	return resp, nil
}
