package monquery

//func Test_monQuerySdk_GetHistoryItemData(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	s := NewDefaultMonquerySdk()
//	resp, err := s.GetHistoryItemData(ctx, &CommonRequest{
//		Namespaces: "118196.cluster-25760-redisbjonline.BCE.all",
//		Items:      "used_memory,maxmemory,proc_cpu_usage",
//		Start:      "20221102130000",
//		End:        "20221107160000",
//		Interval:   "3600",
//		SampleFunc: "max",
//		Token:      "a11602e1c4c24e59865b9bb9209ff16d",
//	})
//	fmt.Println(base_utils.Format(resp))
//	if err != nil {
//		t.Error(err)
//		return
//	}
//}

//func Test_monQuerySdk_GetLatestItemData(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	s := NewDefaultMonquerySdk()
//	resp, err := s.GetLatestItemData(ctx, &CommonRequest{
//		Namespaces: "118196.cluster-25760-redisbjonline.BCE.all",
//		Items:      "used_memory,maxmemory,proc_cpu_usage,pega_used_disk_size,disk_used_percent",
//		Token:      "a11602e1c4c24e59865b9bb9209ff16d",
//	})
//	fmt.Println(base_utils.Format(resp))
//	if err != nil {
//		t.Error(err)
//		return
//	}
//}
