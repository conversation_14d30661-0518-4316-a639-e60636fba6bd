package monquery

import (
	"math"
	"sort"
)

type Vals []float64

func (v Vals) Len() int {
	return len(v)
}

func (v Vals) Less(i, j int) bool {
	return v[i] < v[j]
}

func (v Vals) Swap(i, j int) {
	v[i], v[j] = v[j], v[i]
}

func GetPercentile(vals Vals, p float64) float64 {
	if len(vals) == 0 {
		return 0
	}
	sort.Sort(vals)
	k := int(math.Ceil(p * float64(len(vals))))
	if k <= 0 {
		return vals[0]
	}
	if k >= len(vals) {
		return vals[len(vals)-1]
	}
	return vals[k-1]
}

func GetPercentileFromMonquery(resp *CommonResponse, p float64) map[string]float64 {
	mret := make(map[string]Vals)
	for _, ns := range resp.Data {
		subret := make(map[string]Vals)
		for item, dps := range ns.Items {
			if len(dps) == 0 {
				subret[item] = append(subret[item], 0)
			}
			for _, dp := range dps {
				subret[item] = append(subret[item], dp.Value)
			}
		}
		for item, vals := range subret {
			mret[item] = append(mret[item], GetPercentile(vals, p))
		}
	}
	ret := make(map[string]float64)
	for item, vals := range mret {
		ret[item] = GetPercentile(vals, 1)
	}
	return ret
}
