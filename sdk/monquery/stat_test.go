package monquery

import (
	"testing"
)

func TestGetPercentile(t *testing.T) {
	type args struct {
		vals Vals
		p    float64
	}
	tests := []struct {
		name string
		args args
		want float64
	}{
		{
			name: testing.CoverMode(),
			args: args{
				vals: Vals{},
				p:    0.5,
			},
			want: 0,
		},
		{
			name: testing.CoverMode(),
			args: args{
				vals: Vals{1, 2, 3, 4, 5, 6, 7, 8, 9, 10},
				p:    0.99,
			},
			want: 10,
		},
		{
			name: testing.CoverMode(),
			args: args{
				vals: Vals{1, 2, 3, 4, 5, 6, 7, 8, 9, 10},
				p:    1,
			},
			want: 10,
		},
		{
			name: testing.CoverMode(),
			args: args{
				vals: Vals{1, 2, 3, 4, 5, 6, 7, 8, 9, 10},
				p:    2,
			},
			want: 10,
		},
		{
			name: testing.CoverMode(),
			args: args{
				vals: Vals{1, 2, 3, 4, 5, 6, 7, 8, 9, 10},
				p:    -1,
			},
			want: 1,
		},
		{
			name: testing.CoverMode(),
			args: args{
				vals: Vals{1, 2, 3, 4, 5, 6, 7, 8, 9, 10},
				p:    0.5,
			},
			want: 5,
		},
		{
			name: testing.CoverMode(),
			args: args{
				vals: Vals{95, 56, 42, 13, 6, 55, 99, 41, 59, 8, 88, 2, 82, 68, 27, 26,
					1, 61, 70, 43, 7, 58, 3, 37, 63, 16, 98, 54, 93, 79, 46, 91, 66,
					52, 47, 25, 97, 30, 96, 33, 38, 15, 9, 31, 17, 40, 76, 92, 89, 67,
					57, 81, 85, 20, 14, 0, 11, 73, 65, 32, 21, 69, 23, 53, 86, 18, 77,
					74, 78, 94, 35, 12, 49, 84, 51, 10, 50, 24, 29, 44, 34, 64, 71, 87,
					5, 22, 60, 48, 19, 45, 80, 28, 83, 4, 36, 39, 72, 62, 75, 90},
				p: 0.99,
			},
			want: 98,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetPercentile(tt.args.vals, tt.args.p); got != tt.want {
				t.Errorf("GetPercentile() = %v, want %v", got, tt.want)
			}
		})
	}
}

//func TestGetPercentileFromMonquery(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	s := NewDefaultMonquerySdk()
//	resp, err := s.GetHistoryItemData(ctx, &CommonRequest{
//		Namespaces: "118196.cluster-25760-redisbjonline.BCE.all",
//		Items:      "used_memory,maxmemory,proc_cpu_usage,pega_used_disk_size,disk_used_percent",
//		Start:      "20221102130000",
//		End:        "20221107160000",
//		Token:      "a11602e1c4c24e59865b9bb9209ff16d",
//	})
//	if err != nil {
//		t.Errorf("GetHistoryItemData() error = %v", err)
//		return
//	}
//
//	ret := GetPercentileFromMonquery(resp, 0.99)
//	fmt.Println(ret)
//}
