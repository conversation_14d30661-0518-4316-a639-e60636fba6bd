/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/27 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file interface.go
 * <AUTHOR>
 * @date 2023/03/27 11:33:10
 * @brief eip interface
 *
 **/

package eip

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type EipModel struct {
	Name                      string `json:"name"`
	Eip                       string `json:"eip"`
	EipId                     string `json:"eipId"`
	Status                    string `json:"status"`
	EipInstanceType           string `json:"eipInstanceType"`
	InstanceType              string `json:"instanceType"`
	InstanceId                string `json:"instanceId"`
	ShareGroupId              string `json:"shareGroupId"`
	DefaultDomesticBandwidth  int    `json:"defaultDomesticBandwidth"`
	BandwidthInMbps           int    `json:"bandwidthInMbps"`
	BwShortId                 string `json:"bwShortId"`
	BwBandwidthInMbps         int    `json:"bwBandwidthInMbps"`
	DomesticBwShortId         string `json:"domesticBwShortId"`
	DomesticBwBandwidthInMbps int    `json:"domesticBwBandwidthInMbps"`
	PaymentTiming             string `json:"paymentTiming"`
	BillingMethod             string `json:"billingMethod"`
	CreateTime                string `json:"createTime"`
	DxpireTime                string `json:"expireTime"`
	Region                    string `json:"region"`
	RouteType                 string `json:"routeType"`
}

type QueryEipRequest struct {
	Eip  string                 `json:"-"`
	Auth *common.Authentication `json:"-"`
}

type QueryEipResponse struct {
	EipList    []*EipModel `json:"eipList"`
	NextMarker string      `json:"nextMarker"`
	Marker     string      `json:"marker"`
	MaxKeys    int         `json:"maxKeys"`
}

type BindEipRequest struct {
	InstanceId           string                 `json:"instanceId,omitempty"`
	InstanceType         string                 `json:"instanceType,omitempty"`
	InstanceInternalId   string                 `json:"instanceInternalId,omitempty"`
	InstanceInternalType string                 `json:"instanceInternalType,omitempty"`
	Eip                  string                 `json:"-"`
	Auth                 *common.Authentication `json:"-"`
}

// AppELBService 接口定义
type EIPService interface {
	// EIP相关接口
	QueryEip(ctx context.Context, req *QueryEipRequest) (rsp *QueryEipResponse, err error)
	BindEip(ctx context.Context, req *BindEipRequest) (err error)
}
