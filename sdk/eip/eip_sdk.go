/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * author: wangbin34 (<EMAIL>)
 * Date: 2023-03-27
 * File: eip_sdk.go
 */

/*
 * DESCRIPTION
 *   根据elb.proto生成的implement文件
 */

package eip

import (
	"context"
	"net/http"
	"strings"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type eipSdk struct {
	common.OpenApi
}

func NewDefaultEipSdk() EIPService {
	return newEipSdk(DefaultServiceName)
}

func NewEipSdk(serviceName string) EIPService {
	return newEipSdk(serviceName)
}

func newEipSdk(serviceName string) *eipSdk {
	s := &eipSdk{
		OpenApi: common.NewOpenApi(serviceName),
	}

	return s
}

func (s *eipSdk) QueryEip(ctx context.Context, req *QueryEipRequest) (rsp *QueryEipResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.Eip == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("eip param is null")
	}

	queries := map[string]interface{}{
		"eip": req.Eip,
	}
	params := &common.OpenApiParams{
		ActionName: "query_eip",
		Auth:       req.Auth,
		HttpMethod: http.MethodGet,
		Uri:        EipURI,
		Queries:    queries,
	}

	rsp = &QueryEipResponse{}
	err = cerrs.ErrEIPQueryFail.Wrap(s.DoRequest(ctx, params, rsp))
	return
}

func (s *eipSdk) BindEip(ctx context.Context, req *BindEipRequest) (err error) {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	clientToken := strings.Split(req.Auth.TransactionId, "_")[0] + "_bind" + req.Eip
	queries := map[string]interface{}{
		"bind":        nil,
		"clientToken": clientToken,
	}

	data := map[string]string{
		"instanceId":           req.InstanceId,
		"instanceType":         req.InstanceType,
		"instanceInternalId":   req.InstanceInternalId,
		"instanceInternalType": req.InstanceInternalType,
	}

	params := &common.OpenApiParams{
		ActionName: "bind_eip",
		Auth:       req.Auth,
		HttpMethod: http.MethodPut,
		Uri:        EipURI + "/" + req.Eip,
		Queries:    queries,
		Posts:      data,
		Product:    req.InstanceType,
	}
	err = s.DoRequest(ctx, params, nil)
	return
}
