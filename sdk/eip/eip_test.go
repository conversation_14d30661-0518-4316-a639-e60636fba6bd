package eip

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const (
	TestIamUserID = "ea2c4a2286ca4540afcb7f7d4ba2d199"
)

func TestEipQuerySdk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     TestIamUserID,
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	auth := authRsp.Auth

	s := NewDefaultEipSdk()
	queryReq := QueryEipRequest{
		Eip:  "*************",
		Auth: auth,
	}
	rsp, err := s.QueryEip(ctx, &queryReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestEipBindSdk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     TestIamUserID,
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	auth := authRsp.Auth

	s := NewDefaultEipSdk()
	bindReq := BindEipRequest{
		InstanceId:           "scs-bj-sghxikunzuve",
		InstanceType:         "SCS",
		InstanceInternalId:   "endpoint-38c97fbe",
		InstanceInternalType: "SNIC",
		Eip:                  "*************",
		Auth:                 auth,
	}
	err = s.BindEip(ctx, &bindReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("success")
}
