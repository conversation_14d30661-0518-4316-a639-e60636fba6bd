/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-27
 * File: redis.go
 */

/*
 * DESCRIPTION
 *   gdp redis wrapper
 */

// Package redis
package redis

import (
	gdpRedis "icode.baidu.com/baidu/gdp/redis"
)

// NewClient 创建gdp redis client
func NewClient(serviceName string, cliOpts ...gdpRedis.ClientOption) gdpRedis.Client {
	cliOpts = append(cliOpts, gdpRedis.OptHooker(newHook()))
	c, err := gdpRedis.NewClient(serviceName, cliOpts...)
	if err != nil {
		panic("new redis client failed, err: " + err.Error())
	}
	return c
}
