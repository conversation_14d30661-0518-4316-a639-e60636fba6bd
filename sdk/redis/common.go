/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-27
 * File: group_modify_before_check.go
 */

/*
 * DESCRIPTION
 *   common
 */

// Package redis
package redis

import (
	"strings"

	"github.com/go-redis/redis/v8"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
)

// IsNilErr 是否是nil错误
func IsNilErr(err error) bool {
	if err == nil || cerrs.Is(err, redis.Nil) {
		return true
	}
	return false
}

// GetErrCode 获取错误码
func GetErrCode(err error) int {
	if IsNilErr(err) {
		return ErrCodeSuccess
	}
	s := err.Error()

	switch {
	case strings.Contains(s, "timeout"):
		return ErrCodeTimeout
	case strings.Contains(s, "connection refused"):
		return ErrCodeConnectRefused
	default:
		return ErrCodeUnknown
	}
}
