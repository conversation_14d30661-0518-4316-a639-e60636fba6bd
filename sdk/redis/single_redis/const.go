/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-27
 * File: const.go
 */

/*
 * DESCRIPTION
 *   single redis constants
 */

// Package single_redis
package single_redis

import "time"

const (
	defaultMaxRetries     = 2
	defaultConnectTimeout = 500 * time.Millisecond
	defaultReadTimeout    = 1000 * time.Millisecond
	defaultWriteTimeout   = 500 * time.Millisecond
)
