/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2023/11/14
 * File: parse_info_test.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package single_redis TODO package function desc
package single_redis

import (
	"context"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func TestParseKeyspaceInfo(t *testing.T) {
	raw := `# Keyspace
db0:keys=418903,expires=960,avg_ttl=1024272419931099
`
	ret, err := ParseKeyspaceInfo(context.Background(), raw, false)
	if err != nil {
		t.Errorf("%s", err.Error())
	}
	if len(ret.KeySpaces) != 1 {
		t.Errorf("wrong number")
	}
	if ret.KeySpaces[0].Keys != 418903 || ret.KeySpaces[0].Expires != 960 || ret.KeySpaces[0].AvgTTL != 1024272419931099 {
		fmt.Println(base_utils.Format(ret))
		t.Errorf("wrong number")
	}
	fmt.Println(base_utils.Format(ret))

	raw = `db:keys=4,expires=0`
	ret, err = ParseKeyspaceInfo(context.Background(), raw, true)
	if err != nil {
		t.Errorf("%s", err.Error())
	}
	if len(ret.KeySpaces) != 1 {
		t.Errorf("wrong number")
	}
	if ret.KeySpaces[0].Keys != 4 {
		fmt.Println(base_utils.Format(ret))
		t.Errorf("wrong number")
	}
	fmt.Println(base_utils.Format(ret))

	raw = `db:keys,expires`
	_, err = ParseKeyspaceInfo(context.Background(), raw, true)
	if err == nil {
		t.Errorf("should be error")
	}
	raw = `db:keys=hello`
	_, err = ParseKeyspaceInfo(context.Background(), raw, true)
	if err == nil {
		t.Errorf("should be error")
	}
	raw = `db:expires=hello`
	_, err = ParseKeyspaceInfo(context.Background(), raw, true)
	if err == nil {
		t.Errorf("should be error")
	}
	raw = `db:avg_ttl=hello`
	_, err = ParseKeyspaceInfo(context.Background(), raw, true)
	if err == nil {
		t.Errorf("should be error")
	}
}
