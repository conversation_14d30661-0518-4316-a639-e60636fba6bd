/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-27
 * File: hook.go
 */

/*
 * DESCRIPTION
 *   single redis hook
 */

// Package single_redis
package single_redis

import (
	"context"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/net/ral"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	sdkRedis "icode.baidu.com/baidu/scs/x1-base/sdk/redis"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

type hook struct {
	options *redis.Options
}

func (h hook) BeforeProcess(ctx context.Context, cmd redis.Cmder) (context.Context, error) {
	wl := sdk_utils.GetRalWorkLogger()
	if wl == nil {
		return ctx, nil
	}

	ctx = logit.NewContext(ctx)
	ral.InitRalStatisItems(ctx)

	now := time.Now()
	logFieldProtocol := logit.String(ral.LogFieldProtocol, "redis")
	logFieldCaller := logit.String(ral.LogFieldCaller, "Redis")
	logFieldProtocol.SetLevel(logit.AllLevels)
	logFieldCaller.SetLevel(logit.AllLevels)
	logFields := []logit.Field{
		logFieldCaller,
		logit.String(ral.LogFieldService, "single_redis"),
		logFieldProtocol,
		logit.String(ral.LogFieldMethod, cmd.Name()),
		logit.String(ral.LogFieldRemoteIP, h.options.Addr),
		logit.Time(ral.LogFieldReqStartTime, now),
		logit.Time(ral.LogFieldTalkStartTime, now),
		logit.String(ral.LogFieldRetry, ""),
	}
	logit.ReplaceFields(ctx, logFields...)

	return ctx, nil
}

func getCmdArgsStr(cmd redis.Cmder) string {
	var args strings.Builder
	for i, arg := range cmd.Args() {
		if i > 0 {
			args.WriteString(" ")
		}
		s := cast.ToString(arg)
		if len(s)+args.Len() > 99 {
			args.WriteString(s[0 : 100-args.Len()])
			args.WriteString("...")
			break
		}
		args.WriteString(s)
	}
	return args.String()
}

func (h hook) AfterProcess(ctx context.Context, cmd redis.Cmder) error {
	var err error
	if !sdkRedis.IsNilErr(cmd.Err()) {
		err = cerrs.ErrRedisCallFail.Wrap(cmd.Err())
	}

	wl := sdk_utils.GetRalWorkLogger()
	if wl == nil {
		return err
	}

	now := time.Now()
	startTime, _ := logit.FindField(ctx, ral.LogFieldReqStartTime).Value().(time.Time)
	cost := now.Sub(startTime)

	logFields := []logit.Field{
		logit.String("args", getCmdArgsStr(cmd)),
		logit.String(ral.LogFieldAPI, ""),
		logit.String(ral.LogFieldURI, ""),
		logit.Error(ral.LogFieldErrmsg, cmd.Err()),
		logit.Int(ral.LogFieldErrno, sdkRedis.GetErrCode(cmd.Err())),
		logit.Duration(ral.LogFieldCost, cost),
		logit.Duration(ral.LogFieldTalk, cost),
		logit.Duration(ral.LogFieldRead, cost),
		logit.String(ral.LogFieldType, "E_SUM"),
	}

	wl.Trace(ctx, "", logFields...)

	if !sdkRedis.IsNilErr(cmd.Err()) {
		wl.Warning(ctx, cmd.Err().Error(), logFields...)
	}

	return err
}

func (h hook) BeforeProcessPipeline(ctx context.Context, cmds []redis.Cmder) (context.Context, error) {
	return h.BeforeProcess(ctx, nil)
}

func (h hook) AfterProcessPipeline(ctx context.Context, cmds []redis.Cmder) error {
	for _, cmd := range cmds {
		err := h.AfterProcess(ctx, cmd)
		cmd.SetErr(err)
	}
	return nil
}

// newHook -
func newHook(options *redis.Options) redis.Hook {
	return &hook{options: options}
}
