/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><EMAIL>)
 * Date: 2023/11/14
 * File: parse_info.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package single_redis TODO package function desc
package single_redis

import (
	"context"
	"fmt"
	"strconv"
	"strings"
)

type KeySpaceDBInfo struct {
	Keys    int `json:"keys"`
	Expires int `json:"expires"`
	AvgTTL  int `json:"avg_ttl"`
}

type KeyspaceInfo struct {
	KeySpaces []*KeySpaceDBInfo `json:"keySpaces"`
}

func ParseKeyspaceInfo(ctx context.Context, raw string, isTkeySpace bool) (*KeyspaceInfo, error) {
	var err error
	ret := &KeyspaceInfo{}
	raw = strings.ReplaceAll(raw, "\r\n", "\n")
	raw = strings.ReplaceAll(raw, "\r", "")
	subStrFlag := ",expires"
	if isTkeySpace {
		subStrFlag = "db:"
	}

	for _, line := range strings.Split(raw, "\n") {
		if strings.HasPrefix(line, "#") {
			continue
		}

		kv := strings.Split(line, ":")
		if len(kv) < 2 {
			continue
		}
		// # Keyspace
		// db0:keys=418903,expires=960,avg_ttl=1024272419931099
		// # TKeyspace
		// info TKeyspace format: db:keys=4,expires=0
		if strings.Contains(line, subStrFlag) {
			dbInfo := &KeySpaceDBInfo{}
			for _, item := range strings.Split(kv[1], ",") {
				itemKv := strings.Split(item, "=")
				if len(itemKv) < 2 {
					return nil, fmt.Errorf("invalid format itemKv %s", line)
				}
				switch itemKv[0] {
				case "keys":
					if dbInfo.Keys, err = strconv.Atoi(itemKv[1]); err != nil {
						return nil, err
					}
				case "expires":
					if dbInfo.Expires, err = strconv.Atoi(itemKv[1]); err != nil {
						return nil, err
					}
				case "avg_ttl":
					if dbInfo.AvgTTL, err = strconv.Atoi(itemKv[1]); err != nil {
						return nil, err
					}
				}
			}
			ret.KeySpaces = append(ret.KeySpaces, dbInfo)
		}
	}
	return ret, nil
}
