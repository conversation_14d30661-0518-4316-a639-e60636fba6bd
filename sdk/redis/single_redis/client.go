/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-27
 * File: singleClient.go
 */

/*
 * DESCRIPTION
 *   single redis client
 */

// Package single_redis
package single_redis

import (
	"strings"

	"github.com/go-redis/redis/v8"
	"github.com/spf13/cast"

	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/utils/vep"
)

type SingleClient struct {
	*redis.Client
}

// NewClient 创建single redis client
func NewClient(host string, port interface{}, opts ...Option) *SingleClient {
	if strings.HasPrefix(host, "vep:") {
		vepHost, vepPort, err := vep.GetVpcEndpointModelServices().ParseVpcEndpoint(host, cast.ToInt(port))
		if err != nil {
			logger.SdkLogger.Error(nil, "parse vep host failed, err: %s", err.<PERSON>rror())
			return nil
		}
		host = vepHost
		port = vepPort
	}
	options := newRedisOptions(host, port, opts...)
	c := redis.NewClient(options)
	c.AddHook(newHook(options))
	return &SingleClient{c}
}

func (c *SingleClient) Close() {
	if err := c.Client.Close(); err != nil {
		logger.SdkLogger.Warning(c.Context(), "fail to close single redis client, addr: %s",
			c.Options().Addr)
	}
}
