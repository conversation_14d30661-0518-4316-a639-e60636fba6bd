package single_redis

import (
	"context"
	"errors"
	r "github.com/go-redis/redis/v8"
	"github.com/go-redis/redismock/v8"
	"io"
	"reflect"
	"strings"
	"testing"
)

func TestPingTest(t *testing.T) {
	tests := []struct {
		name     string
		host     string
		port     int
		password string
		wantErr  bool
	}{
		{
			name:     "ping test",
			host:     "localhost",
			port:     3333,
			password: "test",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_ = PingTest(context.Background(), tt.host, tt.port, 3, "")
		})
	}
}

func TestReplicationInfoSlave_errConvert(t *testing.T) {
	type args struct {
		err error
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "error",
			args: args{
				err: context.DeadlineExceeded,
			},
			wantErr: true,
		},
		{
			name: "cancel",
			args: args{
				err: context.Canceled,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := errConvert(tt.args.err); (err != nil) != tt.wantErr {
				t.Errorf("errConvert() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_shouldRetry(t *testing.T) {
	tests := []struct {
		err      error
		expected bool
	}{
		{context.Canceled, true},
		{context.DeadlineExceeded, true},
		{io.EOF, true},
		{io.ErrUnexpectedEOF, true},
		{r.ErrClosed, true},
		{errors.New("ERR max number of clients reached"), true},
		{errors.New("LOADING "), true},
		{errors.New("READONLY "), true},
		{errors.New("CLUSTERDOWN "), true},
		{errors.New("TRYAGAIN "), true},
		{errors.New("timeout"), true},
		{errors.New("ERR restoring the db from backup"), true},
		{errors.New("ERR wrong number of arguments"), true},
		{errors.New("Redis is loading the dataset in memory"), true},
	}

	for _, tt := range tests {
		_ = shouldRetry(tt.err)
	}
}

func TestParsePegaDBMemoryInfo(t *testing.T) {
	type args struct {
		ctx context.Context
		raw string
	}
	tests := []struct {
		name    string
		args    args
		want    *PegaDBMemoryInfo
		wantErr bool
	}{
		{
			name: "test parse",
			args: args{
				ctx: context.Background(),
				raw: strings.Join([]string{"# Memory",
					"used_real_memory:18817024",
					"used_real_memory_rss:18817024",
					"used_real_memory_human:17.95M",
					"used_real_memory_peak:18812928",
					"used_real_memory_peak_human:17.94M",
					"used_memory_lua:36864",
					"used_memory_lua_human:36.00K",
					"used_dbsize:0",
					"used_dbsize_human:0B",
					"used_memory:0",
					"used_memory_human:0B",
					"max_dbsize_byte:214748364800",
					"max_dbsize:200G",
					"used_db_percent:0%",
					"used_dbsize_peak:0",
					"used_dbsize_peak_human:0B",
					"used_disk_size:1340086530048",
					"used_disk_size_human:1.22T",
					"disk_capacity:1792895266816",
					"disk_capacity_human:1.63T",
					"used_disk_percent:74%",
					"used_disk_size_peak:1341315915776",
					"used_disk_size_peak_human:1.22T"}, "\r\n")},
			want: &PegaDBMemoryInfo{
				UsedRealMemoryRss:       18817024,
				UsedRealMemory:          18817024,
				UsedDbPercent:           "0%",
				UsedMemoryLuaHuman:      "36.00K",
				UsedMemory:              0,
				UsedRealMemoryHuman:     "17.95M",
				UsedMemoryLua:           36864,
				UsedDiskSizePeak:        1341315915776,
				MaxDbsize:               "200G",
				DiskCapacityHuman:       "1.63T",
				UsedDbsizePeakHuman:     "0B",
				UsedDiskSize:            1340086530048,
				UsedDbsizeHuman:         "0B",
				UsedMemoryHuman:         "0B",
				UsedDbsize:              0,
				DiskCapacity:            1792895266816,
				UsedDbsizePeak:          0,
				UsedRealMemoryPeak:      18812928,
				UsedDiskSizeHuman:       "1.22T",
				UsedRealMemoryPeakHuman: "17.94M",
				UsedDiskSizePeakHuman:   "1.22T",
				MaxDbsizeByte:           214748364800,
				UsedDiskPercent:         "74%",
			},
			wantErr: false,
		},
		{
			name: "test ignore string no :",
			args: args{
				ctx: context.Background(),
				raw: strings.Join([]string{"# Memory",
					"used_real_memory",
					"used_real_memory_rss:18817024",
					"used_real_memory_human:17.95M",
					"used_real_memory_peak:18812928",
					"used_real_memory_peak_human:17.94M",
					"used_memory_lua:36864",
					"used_memory_lua_human:36.00K",
					"used_dbsize:0",
					"used_dbsize_human:0B",
					"used_memory:0",
					"used_memory_human:0B",
					"max_dbsize_byte:214748364800",
					"max_dbsize:200G",
					"used_db_percent:0%",
					"used_dbsize_peak:0",
					"used_dbsize_peak_human:0B",
					"used_disk_size:1340086530048",
					"used_disk_size_human:1.22T",
					"disk_capacity:1792895266816",
					"disk_capacity_human:1.63T",
					"used_disk_percent:74%",
					"used_disk_size_peak:1341315915776",
					"used_disk_size_peak_human:1.22T"}, "\r\n")},
			want: &PegaDBMemoryInfo{
				UsedRealMemoryRss:       18817024,
				UsedRealMemory:          0,
				UsedDbPercent:           "0%",
				UsedMemoryLuaHuman:      "36.00K",
				UsedMemory:              0,
				UsedRealMemoryHuman:     "17.95M",
				UsedMemoryLua:           36864,
				UsedDiskSizePeak:        1341315915776,
				MaxDbsize:               "200G",
				DiskCapacityHuman:       "1.63T",
				UsedDbsizePeakHuman:     "0B",
				UsedDiskSize:            1340086530048,
				UsedDbsizeHuman:         "0B",
				UsedMemoryHuman:         "0B",
				UsedDbsize:              0,
				DiskCapacity:            1792895266816,
				UsedDbsizePeak:          0,
				UsedRealMemoryPeak:      18812928,
				UsedDiskSizeHuman:       "1.22T",
				UsedRealMemoryPeakHuman: "17.94M",
				UsedDiskSizePeakHuman:   "1.22T",
				MaxDbsizeByte:           214748364800,
				UsedDiskPercent:         "74%",
			},
			wantErr: false,
		},
		{
			name: "test parse int err", args: args{ctx: context.Background(),
				raw: strings.Join([]string{"# Memory",
					"used_real_memory:18817024L"}, "\r\n")},
			want:    nil,
			wantErr: true,
		},
		{
			name: "test parse int err", args: args{ctx: context.Background(),
				raw: strings.Join([]string{"# Memory",
					"used_real_memory_rss:18817024L"}, "\r\n")},
			want:    nil,
			wantErr: true,
		},
		{
			name: "test parse int err", args: args{ctx: context.Background(),
				raw: strings.Join([]string{"# Memory",
					"used_real_memory_peak:18812928L"}, "\r\n")},
			want:    nil,
			wantErr: true,
		},
		{
			name: "test parse int err", args: args{ctx: context.Background(),
				raw: strings.Join([]string{"# Memory",
					"used_memory_lua:36864l"}, "\r\n")},
			want:    nil,
			wantErr: true,
		},
		{
			name: "test parse int err", args: args{ctx: context.Background(),
				raw: strings.Join([]string{"# Memory",
					"used_dbsize:0l"}, "\r\n")},
			want:    nil,
			wantErr: true,
		},
		{
			name: "test parse int err", args: args{ctx: context.Background(),
				raw: strings.Join([]string{"# Memory",
					"used_memory:0l"}, "\r\n")},
			want:    nil,
			wantErr: true,
		},
		{
			name: "test parse int err", args: args{ctx: context.Background(),
				raw: strings.Join([]string{"# Memory",
					"max_dbsize_byte:214748364800l"}, "\r\n")},
			want:    nil,
			wantErr: true,
		},
		{
			name: "test parse int err", args: args{ctx: context.Background(),
				raw: strings.Join([]string{"# Memory",
					"used_dbsize_peak:011f"}, "\r\n")},
			want:    nil,
			wantErr: true,
		},
		{
			name: "test parse int err", args: args{ctx: context.Background(),
				raw: strings.Join([]string{
					"used_disk_size:13400865300L48"}, "\r\n")},
			want:    nil,
			wantErr: true,
		},
		{
			name: "test parse int err", args: args{ctx: context.Background(),
				raw: strings.Join([]string{
					"disk_capacity:1792895266816l"}, "\r\n")},
			want:    nil,
			wantErr: true,
		},
		{
			name: "test parse int err", args: args{ctx: context.Background(),
				raw: strings.Join([]string{
					"used_disk_size_peak:1341315915776l"}, "\r\n")},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParsePegaDBMemoryInfo(tt.args.ctx, tt.args.raw)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParsePegaDBMemoryInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParsePegaDBMemoryInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetPegaDBMemoryInfo(t *testing.T) {
	db, mock := redismock.NewClientMock()

	type args struct {
		ctx context.Context
		c   *SingleClient
	}
	tests := []struct {
		name    string
		args    args
		want    *PegaDBMemoryInfo
		wantErr bool
	}{
		{
			name: "test parse",
			args: args{
				ctx: context.Background(),
				c:   &SingleClient{db},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "test parse",
			args: args{
				ctx: context.Background(),
				c: NewClient("10.10.22.44", 8771,
					WithPassword("")),
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock.ExpectInfo("memory").SetErr(errors.New("test error"))
			got, err := GetPegaDBMemoryInfo(tt.args.ctx, tt.args.c)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPegaDBMemoryInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPegaDBMemoryInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}
