/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-27
 * File: singleClient.go
 */

/*
 * DESCRIPTION
 *   single redis client
 */

// Package single_redis
package single_redis

import (
	"context"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	r "github.com/go-redis/redis/v8"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const (
	pingDelay             = 1 * time.Second
	RoleMaster            = "master"
	RedisCmdCertification = "fbbef3155aa64306ba4df82721fc039a"
)

type ReplicationInfoSlave struct {
	IP     string `json:"ip"`
	Port   int    `json:"port"`
	State  string `json:"state"`
	Offset int64  `json:"offset"`
	Lag    int64  `json:"lag"`
}

type ReplicationInfo struct {
	Role                       string                  `json:"role"`
	ConnectedSlaves            int                     `json:"connected_slaves"`
	Slaves                     []*ReplicationInfoSlave `json:"slave"`
	MasterReplOffset           int64                   `json:"master_repl_offset"`
	SlaveReplOffset            int64                   `json:"slave_repl_offset"`
	ReplBacklogActive          bool                    `json:"repl_backlog_active"`
	ReplBacklogSize            int64                   `json:"repl_backlog_size"`
	ReplBacklogFirstByteOffset int64                   `json:"repl_backlog_first_byte_offset"`
	ReplBacklogHistLen         int64                   `json:"repl_backlog_histlen"`
	MasterHost                 string                  `json:"master_host"`
	MasterPort                 int                     `json:"master_port"`
	MasterLinkStatus           string                  `json:"master_link_status"`
}

type PegaDBMemoryInfo struct {
	UsedRealMemoryRss       int64  `json:"used_real_memory_rss"`
	UsedRealMemory          int64  `json:"used_real_memory"`
	UsedDbPercent           string `json:"used_db_percent"`
	UsedMemoryLuaHuman      string `json:"used_memory_lua_human"`
	UsedMemory              int64  `json:"used_memory"`
	UsedRealMemoryHuman     string `json:"used_real_memory_human"`
	UsedMemoryLua           int64  `json:"used_memory_lua"`
	UsedDiskSizePeak        int64  `json:"used_disk_size_peak"`
	MaxDbsize               string `json:"max_dbsize"`
	DiskCapacityHuman       string `json:"disk_capacity_human"`
	UsedDbsizePeakHuman     string `json:"used_dbsize_peak_human"`
	UsedDiskSize            int64  `json:"used_disk_size"`
	UsedDbsizeHuman         string `json:"used_dbsize_human"`
	UsedMemoryHuman         string `json:"used_memory_human"`
	UsedDbsize              int64  `json:"used_dbsize"`
	DiskCapacity            int64  `json:"disk_capacity"`
	UsedDbsizePeak          int64  `json:"used_dbsize_peak"`
	UsedRealMemoryPeak      int64  `json:"used_real_memory_peak"`
	UsedDiskSizeHuman       string `json:"used_disk_size_human"`
	UsedRealMemoryPeakHuman string `json:"used_real_memory_peak_human"`
	UsedDiskSizePeakHuman   string `json:"used_disk_size_peak_human"`
	MaxDbsizeByte           int64  `json:"max_dbsize_byte"`
	UsedDiskPercent         string `json:"used_disk_percent"`
}

func GetInfo(ctx context.Context, c *SingleClient) (*ReplicationInfo, error) {
	infoRaw, err := c.Info(ctx, "replication").Result()
	if err != nil {
		return nil, err
	}
	info, err := ParseReplicationInfo(ctx, infoRaw)
	if err != nil {
		return nil, err
	}
	return info, nil
}

func CheckCmdSync(ctx context.Context, c *SingleClient, quorum int, retry int, checkInterval time.Duration) error {
	if quorum <= 0 {
		return nil
	}
	info, err := GetInfo(ctx, c)
	if err != nil {
		return err
	}
	if info.Role != "master" {
		return fmt.Errorf("role %s of client %s not valid", info.Role, c.Options().Addr)
	}

	syncNodeCount := 0
	for i := 0; i < retry; i++ {
		syncNodeCount = 0
		time.Sleep(checkInterval)
		sInfo, err := GetInfo(ctx, c)
		if err != nil {
			return err
		}
		if sInfo.Role != "master" {
			return fmt.Errorf("role %s of client %s not valid", sInfo.Role, c.Options().Addr)
		}
		for _, slave := range sInfo.Slaves {
			if slave.State == "online" && slave.Offset >= info.MasterReplOffset {
				syncNodeCount++
			}
		}
		if syncNodeCount >= quorum {
			return nil
		}
	}
	return fmt.Errorf("not enough sync slave nods, cur %d, quorum %d", syncNodeCount, quorum)
}

func ParseReplicationInfo(ctx context.Context, raw string) (*ReplicationInfo, error) {
	var err error
	ret := &ReplicationInfo{}
	raw = strings.Replace(raw, "\r\n", "\n", -1)
	raw = strings.Replace(raw, "\r", "", -1)
	for _, line := range strings.Split(raw, "\n") {
		if strings.HasPrefix(line, "#") {
			continue
		}
		//resource.LoggerTask.Notice(ctx, "REPLICATION LINE", logit.String("line", base_utils.Format(line)))
		kv := strings.Split(line, ":")
		if len(kv) < 2 {
			continue
		}
		if strings.Contains(line, ",state") {
			slave := &ReplicationInfoSlave{}
			for _, item := range strings.Split(kv[1], ",") {
				itemKv := strings.Split(item, "=")
				if len(itemKv) < 2 {
					return nil, fmt.Errorf("invalid format itemKv %s", line)
				}
				switch itemKv[0] {
				case "ip":
					slave.IP = itemKv[1]
				case "port":
					if slave.Port, err = strconv.Atoi(itemKv[1]); err != nil {
						return nil, err
					}
				case "state":
					slave.State = itemKv[1]
				case "offset":
					if slave.Offset, err = strconv.ParseInt(itemKv[1], 10, 64); err != nil {
						return nil, err
					}
				case "lag":
					if slave.Lag, err = strconv.ParseInt(itemKv[1], 10, 64); err != nil {
						return nil, err
					}
				}
			}
			ret.Slaves = append(ret.Slaves, slave)
		} else {
			switch kv[0] {
			case "role":
				ret.Role = kv[1]
			case "connected_slaves":
				if ret.ConnectedSlaves, err = strconv.Atoi(kv[1]); err != nil {
					return nil, err
				}
			case "master_repl_offset":
				if ret.MasterReplOffset, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
					return nil, err
				}
			case "slave_repl_offset":
				if ret.SlaveReplOffset, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
					return nil, err
				}
			case "repl_backlog_active":
				replBacklogActive, err := strconv.Atoi(kv[1])
				if err != nil {
					return nil, err
				}
				ret.ReplBacklogActive = replBacklogActive == 1
			case "repl_backlog_size":
				if ret.ReplBacklogSize, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
					return nil, err
				}
			case "repl_backlog_first_byte_offset":
				if ret.ReplBacklogFirstByteOffset, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
					return nil, err
				}
			case "repl_backlog_histlen":
				if ret.ReplBacklogHistLen, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
					return nil, err
				}
			case "master_host":
				ret.MasterHost = kv[1]
			case "master_port":
				if ret.MasterPort, err = strconv.Atoi(kv[1]); err != nil {
					return nil, err
				}
			case "master_link_status":
				ret.MasterLinkStatus = kv[1]
			}
		}
	}
	return ret, nil
}

// PingTest 检查redis能否ping成功
func PingTest(ctx context.Context, host string, port any, timeoutSec int, password string) error {
	c := NewClient(host, port,
		WithPassword(password),
		WithRetry(0),
	)
	defer c.Close()

	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeoutSec)*time.Second)
	defer cancel()

	for {
		result, err := c.Ping(ctx).Result()
		if err != nil {
			if !shouldRetry(err) {
				return errConvert(err)
			}
		} else if strings.EqualFold(result, "PONG") {
			break
		}

		select {
		case <-ctx.Done():
			return errConvert(ctx.Err())
		case <-time.After(pingDelay):
			break
		}
	}

	return nil
}

func errConvert(err error) error {
	if cerrs.Is(err, context.Canceled) {
		return cerrs.ErrCanceled.Wrap(err)
	} else if cerrs.Is(err, context.DeadlineExceeded) {
		return cerrs.ErrTimeout.Wrap(err)
	}
	return cerrs.ErrRedisCallFail.Wrap(err)
}

// 与gdp-redis基本一致
func shouldRetry(err error) bool {
	if err == nil ||
		cerrs.Is(err, context.Canceled) ||
		cerrs.Is(err, context.DeadlineExceeded) {
		return false
	}

	if sdk_utils.IsConnectFail(err) {
		return true
	}

	if cerrs.Is(err, io.EOF) ||
		cerrs.Is(err, io.ErrUnexpectedEOF) ||
		cerrs.Is(err, r.ErrClosed) {
		return true
	}

	if _, ok := err.(interface{ Timeout() bool }); ok {
		return true
	}

	s := err.Error()
	if s == "ERR max number of clients reached" {
		return true
	}
	if strings.HasPrefix(s, "LOADING ") {
		return true
	}
	if strings.HasPrefix(s, "READONLY ") {
		return true
	}
	if strings.HasPrefix(s, "CLUSTERDOWN ") {
		return true
	}
	if strings.HasPrefix(s, "TRYAGAIN ") {
		return true
	}
	if strings.Contains(s, "timeout") {
		return true
	}
	if strings.Contains(strings.ToUpper(s), strings.ToUpper("ERR restoring the db from backup")) {
		return true
	}
	if strings.Contains(strings.ToUpper(s), strings.ToUpper("ERR wrong number of arguments")) {
		return true
	}
	if strings.Contains(strings.ToUpper(s), strings.ToUpper("Redis is loading the dataset in memory")) {
		return true
	}

	return false
}

// GetReplicationInfo 向redis发送replication info命令，获取相关结果
func GetReplicationInfo(ctx context.Context, host string, port any, password string) (*ReplicationInfo, error) {
	c := NewClient(host, port,
		WithPassword(password),
	)
	defer c.Close()

	raw, err := c.Info(ctx, "replication").Result()
	if err != nil {
		return nil, err
	}
	return ParseReplicationInfo(ctx, raw)
}

// GetPegaDBMemoryInfo 向pega发送memory info命令，获取相关结果
func GetPegaDBMemoryInfo(ctx context.Context, c *SingleClient) (*PegaDBMemoryInfo, error) {

	raw, err := c.Info(ctx, "memory").Result()
	if err != nil {
		return nil, err
	}
	return ParsePegaDBMemoryInfo(ctx, raw)
}

// nolint:gocyclo
func ParsePegaDBMemoryInfo(ctx context.Context, raw string) (*PegaDBMemoryInfo, error) {
	var err error
	ret := &PegaDBMemoryInfo{}
	raw = strings.Replace(raw, "\r\n", "\n", -1)
	raw = strings.Replace(raw, "\r", "", -1)
	for _, line := range strings.Split(raw, "\n") {
		if strings.HasPrefix(line, "#") {
			continue
		}
		kv := strings.Split(line, ":")
		if len(kv) < 2 {
			continue
		}
		switch kv[0] {
		case "used_dbsize":
			if ret.UsedDbsize, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
				return nil, err
			}
		case "used_dbsize_human":
			ret.UsedDbsizeHuman = kv[1]
		case "used_dbsize_peak":
			if ret.UsedDbsizePeak, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
				return nil, err
			}
		case "used_dbsize_peak_human":
			ret.UsedDbsizePeakHuman = kv[1]
		case "max_dbsize_byte":
			if ret.MaxDbsizeByte, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
				return nil, err
			}
		case "max_dbsize":
			ret.MaxDbsize = kv[1]
		case "used_disk_size":
			if ret.UsedDiskSize, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
				return nil, err
			}
		case "used_disk_size_human":
			ret.UsedDiskSizeHuman = kv[1]
		case "disk_capacity":
			if ret.DiskCapacity, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
				return nil, err
			}
		case "disk_capacity_human":
			ret.DiskCapacityHuman = kv[1]
		case "used_disk_size_peak":
			if ret.UsedDiskSizePeak, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
				return nil, err
			}
		case "used_disk_size_peak_human":
			ret.UsedDiskSizePeakHuman = kv[1]
		case "used_real_memory":
			if ret.UsedRealMemory, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
				return nil, err
			}
		case "used_real_memory_rss":
			if ret.UsedRealMemoryRss, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
				return nil, err
			}
		case "used_real_memory_human":
			ret.UsedRealMemoryHuman = kv[1]
		case "used_real_memory_peak":
			if ret.UsedRealMemoryPeak, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
				return nil, err
			}
		case "used_real_memory_peak_human":
			ret.UsedRealMemoryPeakHuman = kv[1]
		case "used_memory_lua":
			if ret.UsedMemoryLua, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
				return nil, err
			}
		case "used_memory_lua_human":
			ret.UsedMemoryLuaHuman = kv[1]
		case "used_memory":
			if ret.UsedMemory, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
				return nil, err
			}
		case "used_memory_human":
			ret.UsedMemoryHuman = kv[1]
		case "used_db_percent":
			ret.UsedDbPercent = kv[1]
		case "used_disk_percent":
			ret.UsedDiskPercent = kv[1]
		}
	}
	return ret, nil
}
