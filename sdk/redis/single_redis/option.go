/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-27
 * File: option.go
 */

/*
 * DESCRIPTION
 *   single redis option
 */

// Package single_redis
package single_redis

import (
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/net/gaddr"
)

type Option func(options *redis.Options)

func WithPassword(password string) Option {
	return func(options *redis.Options) {
		options.Password = password
	}
}

func WithAccount(accountName, password string) Option {
	return func(options *redis.Options) {
		options.Username = accountName
		options.Password = password
	}
}

func WithRetry(retry int) Option {
	return func(options *redis.Options) {
		if retry <= 0 {
			retry = -1
		}
		options.MaxRetries = retry
	}
}

type ConfigTimeout struct {
	Connect time.Duration
	Read    time.Duration
	Write   time.Duration
}

func WithTimeout(timeout *ConfigTimeout) Option {
	return func(options *redis.Options) {
		options.DialTimeout = timeout.Connect
		options.ReadTimeout = timeout.Read
		options.WriteTimeout = timeout.Write
	}
}

func newRedisOptions(host string, port interface{}, opts ...Option) *redis.Options {
	options := &redis.Options{
		Addr:         gaddr.JoinHostPort(host, cast.ToInt(port)),
		MaxRetries:   defaultMaxRetries,
		DialTimeout:  defaultConnectTimeout,
		ReadTimeout:  defaultReadTimeout,
		WriteTimeout: defaultWriteTimeout,
	}
	for _, opt := range opts {
		opt(options)
	}
	return options
}
