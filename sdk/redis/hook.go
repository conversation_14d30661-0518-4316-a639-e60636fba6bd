/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-27
 * File: hook.go
 */

/*
 * DESCRIPTION
 *   hook
 */

// Package redis
package redis

import (
	"context"

	"github.com/go-redis/redis/v8"
	gdpRedis "icode.baidu.com/baidu/gdp/redis"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
)

type hook struct {}

func (h hook) BeforeProcess(ctx context.Context, cmd redis.Cmder) (context.Context, error) {
	return ctx, nil
}

func (h hook) AfterProcess(ctx context.Context, cmd redis.Cmder) error {
	var err error
	if !IsNilErr(cmd.Err()) {
		err = cerrs.ErrRedisCallFail.Wrap(cmd.Err())
	}
	return err
}

func (h hook) BeforeProcessPipeline(ctx context.Context, cmds []redis.Cmder) (context.Context, error) {
	return ctx, nil
}

func (h hook) AfterProcessPipeline(ctx context.Context, cmds []redis.Cmder) error {
	for _, cmd := range cmds {
		err := h.AfterProcess(ctx, cmd)
		cmd.SetErr(err)
	}
	return nil
}

// newHook -
func newHook() gdpRedis.Hook {
	return &hook{}
}
