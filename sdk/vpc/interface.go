/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package vpc

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type SecurityGroupOpRequest struct {
	UserId             string                 `json:"-"`
	Auth               *common.Authentication `json:"-"`
	InstanceUuids      []string               `json:"instanceUuids"`
	InstanceType       string                 `json:"instanceType"`
	SubInstanceType    string                 `json:"subInstanceType"`
	SecurityGroupUuids []string               `json:"securityGroupUuids"`
}

type CommonResponse struct {
	Message   string `json:"message"`
	Code      string `json:"code"`
	RequestID string `json:"requestId"`
}

type ListInstancesBySgIDRequest struct {
	UserId          string                 `json:"-"`
	Auth            *common.Authentication `json:"-"`
	VpcId           string                 `json:"vpcId"`
	SecurityGroupId string                 `json:"securityGroupId"`
	ServerType      string                 `json:"serverType"`
	PageNo          int                    `json:"pageNo"`
	PageSize        int                    `json:"pageSize"`
}

type BindedInstance struct {
	Id      string `json:"id"`
	ScsName string `json:"scsName"`
	LbId    string `json:"lbId"`
	ScsType string `json:"scsType"`
	LbIp    string `json:"lbIp"`
	VpcId   string `json:"vpcId"`
}

type ListInstancesBySgIDResponse struct {
	Message   string `json:"message"`
	Code      string `json:"code"`
	RequestID string `json:"requestId"`
	Success   bool   `json:"success"`
	Status    int    `json:"status"`
	Page      struct {
		OrderBy    string            `json:"orderBy"`
		Order      string            `json:"order"`
		PageNo     int               `json:"pageNo"`
		PageSize   int               `json:"pageSize"`
		TotalCount int               `json:"totalCount"`
		Result     []*BindedInstance `json:"result"`
	} `json:"page"`
}

type ListSgsByInstanceIDRequest struct {
	UserId          string                 `json:"-"`
	Auth            *common.Authentication `json:"-"`
	InstanceId      string                 `json:"instanceId"`
	InstanceType    string                 `json:"instanceType"`
	SubInstanceType string                 `json:"subInstanceType"`
}

type ActiveRule struct {
	Direction           string `json:"direction"`
	Ethertype           string `json:"ethertype"`
	Id                  string `json:"id"`
	Name                string `json:"name"`
	PortRange           string `json:"portRange"`
	Protocol            string `json:"protocol"`
	RemoteGroupId       string `json:"remoteGroupId"`
	RemoteIP            string `json:"remoteIP"`
	SecurityGroupId     string `json:"securityGroupId"`
	SecurityGroupRuleId string `json:"securityGroupRuleId"`
	SecurityGroupUuid   string `json:"securityGroupUuid"`
	TenantId            string `json:"tenantId"`
}

type SecurityGroupRule struct {
	Direction           string `json:"direction"`
	Ethertype           string `json:"ethertype"`
	Id                  string `json:"id"`
	Name                string `json:"name"`
	PortRange           string `json:"portRange"`
	Protocol            string `json:"protocol"`
	RemoteGroupId       string `json:"remoteGroupId"`
	RemoteGroupName     string `json:"remoteGroupName"`
	RemoteIP            string `json:"remoteIP"`
	SecurityGroupId     string `json:"securityGroupId"`
	SecurityGroupRuleId string `json:"securityGroupRuleId"`
	SecurityGroupUuid   string `json:"securityGroupUuid"`
	TenantId            string `json:"tenantId"`
}

type SecurityGroup struct {
	Desc            string               `json:"desc"`
	Id              string               `json:"id"`
	Instances       []string             `json:"instances"`
	Name            string               `json:"name"`
	Rules           []*SecurityGroupRule `json:"rules"`
	SecurityGroupId string               `json:"securityGroupId"`
	TenantId        string               `json:"tenantId"`
	VpcId           string               `json:"vpcId"`
	VpcName         string               `json:"vpcName"`
}

type ListSgsByInstanceIDResult struct {
	ActiveRules    []*ActiveRule    `json:"activeRules"`
	SecurityGroups []*SecurityGroup `json:"securityGroups"`
}

type ListSgsByInstanceIDResponse struct {
	Message   string                     `json:"message"`
	Code      string                     `json:"code"`
	RequestID string                     `json:"requestId"`
	Result    *ListSgsByInstanceIDResult `json:"result"`
	Status    int                        `json:"status"`
	Success   bool                       `json:"success"`
}

type VpcService interface {
	ListInstancesBySgID(ctx context.Context, req *ListInstancesBySgIDRequest) (rsp *ListInstancesBySgIDResponse, err error)
	BindSecurityGroup(ctx context.Context, req *SecurityGroupOpRequest) (rsp *CommonResponse, err error)
	UnBindSecurityGroup(ctx context.Context, req *SecurityGroupOpRequest) (rsp *CommonResponse, err error)
	ListSgsByInstanceID(ctx context.Context, req *ListSgsByInstanceIDRequest) (rsp *ListSgsByInstanceIDResponse, err error)
}
