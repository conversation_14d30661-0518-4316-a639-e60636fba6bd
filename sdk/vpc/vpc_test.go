/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package vpc

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

const (
	TestIamUserId = "b8096fa260534277ae0058138284416f"
)

// Test_vpcSdk_BindSecurityGroup 测试 vpcSdk 的 BindSecurityGroup 方法，包括参数为空、Auth 为空、请求失败和成功等场景。
func Test_vpcSdk_BindSecurityGroup(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newvpcSdk(DefaultServiceName)
	_, err := s.BindSecurityGroup(ctx, nil)
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.BindSecurityGroup(ctx, &SecurityGroupOpRequest{Auth: nil})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test Auth nil checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CommonResponse{
		Message:   "Test Message",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusNotFound))
	rsp, err := s.BindSecurityGroup(ctx, &SecurityGroupOpRequest{
		UserId:             "testUserId",
		Auth:               &common.Authentication{},
		InstanceUuids:      []string{"a"},
		InstanceType:       "TEST INSTANCE TYPE",
		SubInstanceType:    "TEST SUB INSTANCE TYPE",
		SecurityGroupUuids: []string{"a"},
	})
	if err == nil || rsp.Code != "TestCode" ||
		rsp.RequestID != "787df68f-bade-4664-9225-5afec2c17ec2" ||
		rsp.Message != "Test Message" {
		t.Errorf("[%s] test create resource fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	rsp, err = s.BindSecurityGroup(ctx, &SecurityGroupOpRequest{
		UserId:             "testUserId",
		Auth:               &common.Authentication{},
		InstanceUuids:      []string{},
		InstanceType:       "TEST INSTANCE TYPE",
		SubInstanceType:    "TEST SUB INSTANCE TYPE",
		SecurityGroupUuids: []string{},
	})
	if err == nil {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CommonResponse{
		Message:   "",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusOK))
	rsp, err = s.BindSecurityGroup(ctx, &SecurityGroupOpRequest{
		UserId:             "testUserId",
		Auth:               &common.Authentication{},
		InstanceUuids:      []string{"a"},
		InstanceType:       "TEST INSTANCE TYPE",
		SubInstanceType:    "TEST SUB INSTANCE TYPE",
		SecurityGroupUuids: []string{"a"},
	})
	if err != nil {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("pass\n")
	}
}

//func Test_vpcSdk_BindSecurityGroupOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
//		TransactionId: uuid.NewString(),
//		IamUserId:     TestIamUserId,
//	})
//	if err != nil {
//		t.Errorf("[%s] iam auth fail", t.Name())
//		return
//	}
//	auth := authRsp.Auth
//	s := newvpcSdk(DefaultServiceName)
//	rsp, err := s.BindSecurityGroup(ctx, &SecurityGroupOpRequest{
//		UserId: TestIamUserId,
//		Auth:   auth,
//		//InstanceUuids:      []string{"426b6b316730724e514975744834446a67366b6836513d3d"}, //lb-841cbef1
//		//InstanceUuids:      []string{"4a655634586676785a2b5453427175776363646836513d3d"}, //lb-b3127873
//		//InstanceUuids:      []string{"6a49384275316870686f464e433666665957373755413d3d"},
//		InstanceUuids:      []string{"endpoint-4d26d559"},
//		InstanceType:       "SCS",
//		SubInstanceType:    "snic",
//		SecurityGroupUuids: []string{"6bac476c-1bbc-4c4a-8a0d-4d1e4dc87df2"}, //g-3rpjc4rtx13e
//	})
//	fmt.Printf("rsp: %+v, err: %v", rsp, err)
//}

func Test_vpcSdk_UnBindSecurityGroup(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newvpcSdk(DefaultServiceName)
	_, err := s.UnBindSecurityGroup(ctx, nil)
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.UnBindSecurityGroup(ctx, &SecurityGroupOpRequest{Auth: nil})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test Auth nil checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CommonResponse{
		Message:   "Test Message",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusNotFound))
	rsp, err := s.UnBindSecurityGroup(ctx, &SecurityGroupOpRequest{
		UserId:             "testUserId",
		Auth:               &common.Authentication{},
		InstanceUuids:      []string{"a"},
		InstanceType:       "TEST INSTANCE TYPE",
		SubInstanceType:    "TEST SUB INSTANCE TYPE",
		SecurityGroupUuids: []string{"a"},
	})
	if err == nil || rsp.Code != "TestCode" ||
		rsp.RequestID != "787df68f-bade-4664-9225-5afec2c17ec2" ||
		rsp.Message != "Test Message" {
		t.Errorf("[%s] test create resource fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	rsp, err = s.UnBindSecurityGroup(ctx, &SecurityGroupOpRequest{
		UserId:             "testUserId",
		Auth:               &common.Authentication{},
		InstanceUuids:      []string{},
		InstanceType:       "TEST INSTANCE TYPE",
		SubInstanceType:    "TEST SUB INSTANCE TYPE",
		SecurityGroupUuids: []string{},
	})
	if err == nil {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CommonResponse{
		Message:   "",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusOK))
	rsp, err = s.UnBindSecurityGroup(ctx, &SecurityGroupOpRequest{
		UserId:             "testUserId",
		Auth:               &common.Authentication{},
		InstanceUuids:      []string{"a"},
		InstanceType:       "TEST INSTANCE TYPE",
		SubInstanceType:    "TEST SUB INSTANCE TYPE",
		SecurityGroupUuids: []string{"a"},
	})
	if err != nil {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("pass\n")
	}

}

//func Test_vpcSdk_UnBindSecurityGroupOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
//		TransactionId: uuid.NewString(),
//		IamUserId:     TestIamUserId,
//	})
//	if err != nil {
//		t.Errorf("[%s] iam auth fail", t.Name())
//		return
//	}
//	auth := authRsp.Auth
//	s := newvpcSdk(DefaultServiceName)
//	rsp, err := s.UnBindSecurityGroup(ctx, &SecurityGroupOpRequest{
//		UserId: TestIamUserId,
//		Auth:   auth,
//		//InstanceUuids:      []string{"426b6b316730724e514975744834446a67366b6836513d3d"}, //lb-841cbef1
//		InstanceUuids:      []string{"4a655634586676785a2b5453427175776363646836513d3d"}, //lb-841cbef1
//		InstanceType:       "SCS",
//		SubInstanceType:    "blb",
//		SecurityGroupUuids: []string{"6bac476c-1bbc-4c4a-8a0d-4d1e4dc87df2"}, //g-3rpjc4rtx13e
//	})
//	fmt.Printf("rsp: %+v, err: %v", rsp, err)
//}

func Test_vpcSdk_ListInstanceIDBySgID(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newvpcSdk(DefaultServiceName)
	_, err := s.ListInstancesBySgID(ctx, nil)
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.ListInstancesBySgID(ctx, &ListInstancesBySgIDRequest{Auth: nil})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test Auth nil checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ListInstancesBySgIDResponse{
		Message:   "Test Message",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusNotFound))
	rsp, err := s.ListInstancesBySgID(ctx, &ListInstancesBySgIDRequest{
		UserId:          "testUserId",
		Auth:            &common.Authentication{},
		VpcId:           "test vpc",
		SecurityGroupId: "test SecurityGroupId",
		ServerType:      "test ServerType",
		PageNo:          1,
		PageSize:        1,
	})
	if err == nil || rsp.Code != "TestCode" ||
		rsp.RequestID != "787df68f-bade-4664-9225-5afec2c17ec2" ||
		rsp.Message != "Test Message" {
		t.Errorf("[%s] test create resource fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	rsp, err = s.ListInstancesBySgID(ctx, &ListInstancesBySgIDRequest{
		UserId:          "testUserId",
		Auth:            &common.Authentication{},
		VpcId:           "test vpc",
		SecurityGroupId: "test SecurityGroupId",
		ServerType:      "test ServerType",
		PageNo:          1,
		PageSize:        1,
	})
	if err == nil {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ListInstancesBySgIDResponse{
		Message:   "",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
		Success:   false,
		Status:    0,
	}, nil, http.StatusOK))
	rsp, err = s.ListInstancesBySgID(ctx, &ListInstancesBySgIDRequest{
		UserId:          "testUserId",
		Auth:            &common.Authentication{},
		VpcId:           "test vpc",
		SecurityGroupId: "test SecurityGroupId",
		ServerType:      "test ServerType",
		PageNo:          1,
		PageSize:        1,
	})
	if err == nil {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ListInstancesBySgIDResponse{
		Message:   "",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
		Success:   true,
		Status:    200,
	}, nil, http.StatusOK))
	rsp, err = s.ListInstancesBySgID(ctx, &ListInstancesBySgIDRequest{
		UserId:          "testUserId",
		Auth:            &common.Authentication{},
		VpcId:           "test vpc",
		SecurityGroupId: "test SecurityGroupId",
		ServerType:      "test ServerType",
		PageNo:          1,
		PageSize:        1,
	})
	if err != nil {
		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
	}
}

//func Test_vpcSdk_ListSecurityGroupBindedInstanceOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
//		TransactionId: uuid.NewString(),
//		IamUserId:     "b8096fa260534277ae0058138284416f",
//	})
//	if err != nil {
//		t.Errorf("[%s] iam auth fail", t.Name())
//		return
//	}
//	auth := authRsp.Auth
//	s := newvpcSdk(DefaultServiceName)
//	rsp, err := s.ListInstancesBySgID(ctx, &ListInstancesBySgIDRequest{
//		UserId:          "b8096fa260534277ae0058138284416f",
//		Auth:            auth,
//		VpcId:           "56532807-3ccb-4762-b964-26513cd389f8",
//		SecurityGroupId: "6bac476c-1bbc-4c4a-8a0d-4d1e4dc87df2",
//		ServerType:      "SCS",
//		PageNo:          1,
//		PageSize:        5,
//	})
//	/**
//	{
//	    "success": true,
//	    "status": 200,
//	    "page": {
//	        "orderBy": "",
//	        "order": "",
//	        "pageNo": 1,
//	        "pageSize": 5,
//	        "totalCount": 1,
//	        "result": [
//	            {
//	                "id": "scs-test-acdsojpodiiw",
//	                "scsName": "雪鹏-测试",
//	                "lbId": "426b6b316730724e514975744834446a67366b6836513d3d",
//	                "scsType": "redis",
//	                "lbIp": "***********",
//	                "vpcId": "56532807-3ccb-4762-b964-26513cd389f8"
//	            }
//	        ]
//	    }
//	}
//	*/
//	fmt.Printf("rsp: %+v, err: %v", rsp, err)
//	fmt.Printf("result: %+v, err: %v", rsp.Page.Result[0], err)
//}

func Test_vpcSdk_ListSgsByInstanceID(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newvpcSdk(DefaultServiceName)
	_, err := s.ListSgsByInstanceID(ctx, nil)
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.ListSgsByInstanceID(ctx, &ListSgsByInstanceIDRequest{Auth: nil})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test Auth nil checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ListSgsByInstanceIDResponse{
		Message:   "Test Message",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusNotFound))
	rsp, err := s.ListSgsByInstanceID(ctx, &ListSgsByInstanceIDRequest{
		UserId:          "testUserId",
		Auth:            &common.Authentication{},
		InstanceId:      "test instance id",
		InstanceType:    "test instance type",
		SubInstanceType: "test sub instance type",
	})
	if err == nil || rsp.Code != "TestCode" ||
		rsp.RequestID != "787df68f-bade-4664-9225-5afec2c17ec2" ||
		rsp.Message != "Test Message" {
		t.Errorf("[%s] test create resource fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	rsp, err = s.ListSgsByInstanceID(ctx, &ListSgsByInstanceIDRequest{
		UserId:          "testUserId",
		Auth:            &common.Authentication{},
		InstanceId:      "test instance id",
		InstanceType:    "test instance type",
		SubInstanceType: "test sub instance type",
	})
	if err == nil {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ListSgsByInstanceIDResponse{
		Message:   "",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
		Success:   false,
		Status:    0,
	}, nil, http.StatusOK))
	rsp, err = s.ListSgsByInstanceID(ctx, &ListSgsByInstanceIDRequest{
		UserId:          "testUserId",
		Auth:            &common.Authentication{},
		InstanceId:      "test instance id",
		InstanceType:    "test instance type",
		SubInstanceType: "test sub instance type",
	})
	if err == nil {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ListSgsByInstanceIDResponse{
		Message:   "",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
		Success:   true,
		Status:    200,
	}, nil, http.StatusOK))
	rsp, err = s.ListSgsByInstanceID(ctx, &ListSgsByInstanceIDRequest{
		UserId:          "testUserId",
		Auth:            &common.Authentication{},
		InstanceId:      "test instance id",
		InstanceType:    "test instance type",
		SubInstanceType: "test sub instance type",
	})
	if err != nil {
		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
	}
}

//func Test_vpcSdk_ListSGByInstanceIDOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
//		TransactionId: uuid.NewString(),
//		IamUserId:     "b8096fa260534277ae0058138284416f",
//	})
//	if err != nil {
//		t.Errorf("[%s] iam auth fail", t.Name())
//		return
//	}
//	auth := authRsp.Auth
//	s := newvpcSdk(DefaultServiceName)
//	rsp, err := s.ListSgsByInstanceID(ctx, &ListSgsByInstanceIDRequest{
//		UserId: "b8096fa260534277ae0058138284416f",
//		Auth:   auth,
//		//InstanceId:      "4a655634586676785a2b5453427175776363646836513d3d",
//		InstanceId:   "endpoint-4d26d559",
//		InstanceType: "SCS",
//		//SubInstanceType: "blb",
//		SubInstanceType: "snic",
//	})
//	/**
//	{
//	    "success": true,
//	    "status": 200,
//	    "result": {
//	        "securityGroups": [
//	            {
//	                "desc": "default",
//	                "id": "6bac476c-1bbc-4c4a-8a0d-4d1e4dc87df2",
//	                "securityGroupId": "g-3rpjc4rtx13e",
//	                "tenantId": "d7e6755cad084982bc6ade3b06da0b28",
//	                "vpcId": "56532807-3ccb-4762-b964-26513cd389f8",
//	                "vpcName": "vpcA",
//	                "instances": null,
//	                "name": "默认安全组",
//	                "rules": [
//	                    {
//	                        "direction": "ingress",
//	                        "ethertype": "IPv4",
//	                        "id": "r-e34edhex0hcd",
//	                        "name": "",
//	                        "portRange": "",
//	                        "protocol": "",
//	                        "remoteGroupId": "",
//	                        "remoteGroupName": null,
//	                        "remoteIP": "",
//	                        "securityGroupId": "g-3rpjc4rtx13e",
//	                        "securityGroupRuleId": "r-e34edhex0hcd",
//	                        "securityGroupUuid": "6bac476c-1bbc-4c4a-8a0d-4d1e4dc87df2",
//	                        "tenantId": "d7e6755cad084982bc6ade3b06da0b28"
//	                    },
//	                    {
//	                        "direction": "ingress",
//	                        "ethertype": "IPv6",
//	                        "id": "r-u9x6kux92ffe",
//	                        "name": "",
//	                        "portRange": "",
//	                        "protocol": "",
//	                        "remoteGroupId": "",
//	                        "remoteGroupName": null,
//	                        "remoteIP": "",
//	                        "securityGroupId": "g-3rpjc4rtx13e",
//	                        "securityGroupRuleId": "r-u9x6kux92ffe",
//	                        "securityGroupUuid": "6bac476c-1bbc-4c4a-8a0d-4d1e4dc87df2",
//	                        "tenantId": "d7e6755cad084982bc6ade3b06da0b28"
//	                    },
//	                    {
//	                        "direction": "egress",
//	                        "ethertype": "IPv4",
//	                        "id": "r-ap9cbzy4sayh",
//	                        "name": "",
//	                        "portRange": "",
//	                        "protocol": "",
//	                        "remoteGroupId": "",
//	                        "remoteGroupName": null,
//	                        "remoteIP": "",
//	                        "securityGroupId": "g-3rpjc4rtx13e",
//	                        "securityGroupRuleId": "r-ap9cbzy4sayh",
//	                        "securityGroupUuid": "6bac476c-1bbc-4c4a-8a0d-4d1e4dc87df2",
//	                        "tenantId": "d7e6755cad084982bc6ade3b06da0b28"
//	                    },
//	                    {
//	                        "direction": "egress",
//	                        "ethertype": "IPv6",
//	                        "id": "r-vr29dj0krucj",
//	                        "name": "",
//	                        "portRange": "",
//	                        "protocol": "",
//	                        "remoteGroupId": "",
//	                        "remoteGroupName": null,
//	                        "remoteIP": "",
//	                        "securityGroupId": "g-3rpjc4rtx13e",
//	                        "securityGroupRuleId": "r-vr29dj0krucj",
//	                        "securityGroupUuid": "6bac476c-1bbc-4c4a-8a0d-4d1e4dc87df2",
//	                        "tenantId": "d7e6755cad084982bc6ade3b06da0b28"
//	                    }
//	                ]
//	            }
//	        ],
//	        "activeRules": [
//	            {
//	                "direction": "ingress",
//	                "ethertype": "IPv4",
//	                "id": "r-e34edhex0hcd",
//	                "name": "",
//	                "portRange": "",
//	                "protocol": "",
//	                "remoteGroupId": "",
//	                "remoteGroupName": null,
//	                "remoteIP": "",
//	                "securityGroupId": "g-3rpjc4rtx13e",
//	                "securityGroupRuleId": "r-e34edhex0hcd",
//	                "securityGroupUuid": "6bac476c-1bbc-4c4a-8a0d-4d1e4dc87df2",
//	                "tenantId": "d7e6755cad084982bc6ade3b06da0b28"
//	            },
//	            {
//	                "direction": "ingress",
//	                "ethertype": "IPv6",
//	                "id": "r-u9x6kux92ffe",
//	                "name": "",
//	                "portRange": "",
//	                "protocol": "",
//	                "remoteGroupId": "",
//	                "remoteGroupName": null,
//	                "remoteIP": "",
//	                "securityGroupId": "g-3rpjc4rtx13e",
//	                "securityGroupRuleId": "r-u9x6kux92ffe",
//	                "securityGroupUuid": "6bac476c-1bbc-4c4a-8a0d-4d1e4dc87df2",
//	                "tenantId": "d7e6755cad084982bc6ade3b06da0b28"
//	            },
//	            {
//	                "direction": "egress",
//	                "ethertype": "IPv4",
//	                "id": "r-ap9cbzy4sayh",
//	                "name": "",
//	                "portRange": "",
//	                "protocol": "",
//	                "remoteGroupId": "",
//	                "remoteGroupName": null,
//	                "remoteIP": "",
//	                "securityGroupId": "g-3rpjc4rtx13e",
//	                "securityGroupRuleId": "r-ap9cbzy4sayh",
//	                "securityGroupUuid": "6bac476c-1bbc-4c4a-8a0d-4d1e4dc87df2",
//	                "tenantId": "d7e6755cad084982bc6ade3b06da0b28"
//	            },
//	            {
//	                "direction": "egress",
//	                "ethertype": "IPv6",
//	                "id": "r-vr29dj0krucj",
//	                "name": "",
//	                "portRange": "",
//	                "protocol": "",
//	                "remoteGroupId": "",
//	                "remoteGroupName": null,
//	                "remoteIP": "",
//	                "securityGroupId": "g-3rpjc4rtx13e",
//	                "securityGroupRuleId": "r-vr29dj0krucj",
//	                "securityGroupUuid": "6bac476c-1bbc-4c4a-8a0d-4d1e4dc87df2",
//	                "tenantId": "d7e6755cad084982bc6ade3b06da0b28"
//	            }
//	        ]
//	    }
//	}
//	*/
//	fmt.Printf("rsp: %+v, err: %v", rsp.Result, err)
//	fmt.Printf("rsp: %+v, err: %v", rsp, err)
//}
