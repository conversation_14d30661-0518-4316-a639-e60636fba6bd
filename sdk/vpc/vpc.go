/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package vpc

import (
	"context"
	"net/http"

	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type vpcSdk struct {
	conf *vpcConf
	common.OpenApi
}

// NewDefaultvpcSdk 新建一个默认的VpcService实例，返回值为VpcService类型
func NewDefaultvpcSdk() VpcService {
	return newvpcSdk(DefaultServiceName)
}

// NewvpcSdk 新建一个VpcService类型的实例，返回值为VpcService类型
func NewvpcSdk(serviceName string) VpcService {
	return newvpcSdk(serviceName)
}

// newvpcSdk 新建一个vpcSdk实例，返回指向该实例的指针
func newvpcSdk(serviceName string) *vpcSdk {
	s := &vpcSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}

	return s
}

// doRequest - 通用openstack服务请求方法
func (s *vpcSdk) doRequest(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}
	// 请求 openapi
	err = s.DoRequest(ctx, params, rsp)
	if err != nil {
		return err
	}
	return nil
}

// BindSecurityGroup 绑定客户创建的安全组
func (s *vpcSdk) BindSecurityGroup(ctx context.Context, req *SecurityGroupOpRequest) (rsp *CommonResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.SubInstanceType == "" || req.InstanceType == "" || len(req.SecurityGroupUuids) == 0 ||
		len(req.InstanceUuids) == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("param is null")
	}

	rsp = &CommonResponse{}
	err = s.doRequest(ctx, "BindSecurityGroup", req.Auth, http.MethodPut,
		BindSecurityGroupUri, nil, req, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "bind security group fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID, logit.Error("err", err))
		return rsp, err
	}
	return
}

// UnBindSecurityGroup 绑定客户创建的安全组
func (s *vpcSdk) UnBindSecurityGroup(ctx context.Context, req *SecurityGroupOpRequest) (rsp *CommonResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.SubInstanceType == "" || req.InstanceType == "" || len(req.SecurityGroupUuids) == 0 ||
		len(req.InstanceUuids) == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("param is null")
	}

	rsp = &CommonResponse{}
	err = s.doRequest(ctx, "BindSecurityGroup", req.Auth, http.MethodPost,
		UnBindSecurityGroupUri, nil, req, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "bind security group fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID, logit.Error("err", err))
		return rsp, err
	}
	return
}

// ListSecurityGroupBindedInstance 查询安全组绑定的实例
func (s *vpcSdk) ListInstancesBySgID(ctx context.Context, req *ListInstancesBySgIDRequest) (rsp *ListInstancesBySgIDResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	rsp = &ListInstancesBySgIDResponse{}
	querys := map[string]interface{}{
		"VpcId":           req.VpcId,
		"SecurityGroupId": req.SecurityGroupId,
		"serverType":      req.ServerType,
		"periodInSecond":  req.PageNo,
		"pageSize":        req.PageSize,
	}
	err = s.doRequest(ctx, "ListInstancesBySgID", req.Auth, http.MethodGet,
		ListSecurityGroupBindedInstanceUri, querys, nil, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "list security group binded instance fail, success: %s, status: %s",
			rsp.Success, rsp.Status, logit.Error("err", err))
		return
	}
	if rsp.Status != 200 || !rsp.Success {
		return rsp, cerrs.ErrVPCRequestFail.Errorf("list security group by instance id fail")
	}
	return
}

// ListSgsByInstanceID 根据实例ID查询安全组列表，参数为*ListSgsByInstanceIDRequest类型，返回值为*ListSgsByInstanceIDResponse类型，可能会抛出cerrs.ErrInvalidParams和cerrs.ErrVPCRequestFail错误
func (s *vpcSdk) ListSgsByInstanceID(ctx context.Context, req *ListSgsByInstanceIDRequest) (rsp *ListSgsByInstanceIDResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	rsp = &ListSgsByInstanceIDResponse{}
	err = s.doRequest(ctx, "ListSgsByInstanceID", req.Auth, http.MethodPost,
		ListSecurityGroupByInstanceID, nil, req, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "list security group by instance id fail, success: %s, status: %s",
			rsp.Success, rsp.Status, logit.Error("err", err))
		return
	}
	if rsp.Status != 200 || !rsp.Success {
		return rsp, cerrs.ErrVPCRequestFail.Errorf("list security group by instance id fail")
	}
	return
}
