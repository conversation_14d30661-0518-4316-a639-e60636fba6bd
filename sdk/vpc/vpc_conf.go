/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package vpc

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "vpc"

// vpcConf definition
type vpcConf struct {
	Product string `toml:"Product,omitempty"`
}

var vpcConfMap = &sync.Map{}

func getConf(serviceName string) *vpcConf {
	if conf, ok := vpcConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*vpcConf); ok {
			return conf
		}
	}

	conf := &vpcConf{}
	conf.mustLoad(serviceName)

	vpcConfMap.Store(serviceName, conf)

	return conf
}

func (conf *vpcConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
