/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-16
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据iam.proto生成的interface文件
 */

// Package iam
package iam

import (
	"context"
)

type CheckSignatureRequest struct {
	TransactionId string      `json:"transaction_id"`
	Authorization string      `json:"authorization"`
	SessionToken  string      `json:"session_token"`
	HttpMethod    string      `json:"http_method"`
	Uri           string      `json:"uri"`
	Queries       interface{} `json:"queries"`
	Headers       interface{} `json:"headers"`
}

type CheckSignatureResponse struct {
	UserTenantId string `json:"user_tenant_id"`
	UserId       string `json:"user_id"`
	AccountId    string `json:"account_id"`
}

type GetUserIdByTokenRequest struct {
	TransactionId string `json:"transaction_id"`
	UserToken     string `json:"user_token"`
}

type GetUserIdByTokenResponse struct {
	UserTenantId string `json:"user_tenant_id"`
	UserId       string `json:"user_id"`
}

type GetUserTenantIdByUserIdRequest struct {
	TransactionId string `json:"transaction_id"`
	UserId        string `json:"user_id"`
}

type GetUserTenantIdByUserIdResponse struct {
	UserTenantId string `json:"user_tenant_id"`
}

type GetServiceTokenRequest struct {
	TransactionId string `json:"transaction_id"`
}

type GetServiceTokenResponse struct {
	Token string `json:"token"`
}

type CipherDecryptRequest struct {
	CipherHex     string `json:"cipher_hex"`
	Authorization string `json:"authorization"`
}

type CipherDecryptResponse struct {
	UserId          string `json:"user_id"`
	CipherDecrypted string `json:"cipher_decrypted"`
}

type KeystoneCipherDecryptRequest struct {
	CipherHex   string `json:"cipher_hex"`
	AccesskeyId string `json:"accesskey_id"`
}

type KeystoneCipherDecryptResponse struct {
	UserId string `json:"user_id"`
	RawHex string `json:"raw_hex"`
}

type GetUserInfoByUserIdRequest struct {
	TransactionId string `json:"transaction_id"`
	UserId        string `json:"user_id"`
}

type GetUserInfoByUserIdResponse struct {
	User *UserInfo `json:"user"`
}

type UserInfo struct {
	Id               string      `json:"id"`
	Name             string      `json:"name"`
	Email            string      `json:"email"`
	Enabled          bool        `json:"enabled"`
	DomainId         string      `json:"domain_id"`
	DefaultProjectId string      `json:"default_project_id"`
	Description      interface{} `json:"description"`
}

type GetTokenByUserNameRequest struct {
	TransactionId string `json:"transaction_id"`
	UserName      string `json:"user_name"`
	DomainId      string `json:"domain_id"`
}

type GetTokenByUserNameResponse struct {
	Token string `json:"token"`
}

type IamService interface {
	CheckSignature(ctx context.Context, req *CheckSignatureRequest) (rsp *CheckSignatureResponse, err error)
	GetUserIdByToken(ctx context.Context, req *GetUserIdByTokenRequest) (rsp *GetUserIdByTokenResponse, err error)
	GetUserTenantIdByUserId(ctx context.Context, req *GetUserTenantIdByUserIdRequest) (rsp *GetUserTenantIdByUserIdResponse, err error)
	GetTokenByUserName(ctx context.Context, req *GetTokenByUserNameRequest) (rsp *GetTokenByUserNameResponse, err error)
	GetServiceToken(ctx context.Context, req *GetServiceTokenRequest) (rsp *GetServiceTokenResponse, err error)
	CipherDecrypt(ctx context.Context, req *CipherDecryptRequest) (rsp *CipherDecryptResponse, err error)
	GetUserInfoByUserId(ctx context.Context, req *GetUserInfoByUserIdRequest) (rsp *GetUserInfoByUserIdResponse, err error)
}
