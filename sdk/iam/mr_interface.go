/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-16
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据iam.proto生成的interface文件
 */

// Package iam
package iam

import (
	"context"
)

type MrCheckSignatureRequest struct {
	Req    *CheckSignatureRequest
	Region string
}

type MrGetUserIdByTokenRequest struct {
	Req    *GetUserIdByTokenRequest
	Region string
}

type MrGetUserTenantIdByUserIdRequest struct {
	Req    *GetUserTenantIdByUserIdRequest
	Region string
}

type MrGetServiceTokenRequest struct {
	Req    *GetServiceTokenRequest
	Region string
}

type MrCipherDecryptRequest struct {
	Req    *CipherDecryptRequest
	Region string
}

type MrKeystoneCipherDecryptRequest struct {
	Req    *KeystoneCipherDecryptRequest
	Region string
}

type MrGetUserInfoByUserIdRequest struct {
	Req    *GetUserInfoByUserIdRequest
	Region string
}

type MrGetTokenByUserNameRequest struct {
	Req    *GetTokenByUserNameRequest
	Region string
}
type MrIamService interface {
	PickIamSdk(ctx context.Context, region string) IamService
	CheckSignature(ctx context.Context, req *MrCheckSignatureRequest) (rsp *CheckSignatureResponse, err error)
	GetUserIdByToken(ctx context.Context, req *MrGetUserIdByTokenRequest) (rsp *GetUserIdByTokenResponse, err error)
	GetUserTenantIdByUserId(ctx context.Context, req *MrGetUserTenantIdByUserIdRequest) (rsp *GetUserTenantIdByUserIdResponse, err error)
	GetTokenByUserName(ctx context.Context, req *MrGetTokenByUserNameRequest) (rsp *GetTokenByUserNameResponse, err error)
	GetServiceToken(ctx context.Context, req *MrGetServiceTokenRequest) (rsp *GetServiceTokenResponse, err error)
	CipherDecrypt(ctx context.Context, req *MrCipherDecryptRequest) (rsp *CipherDecryptResponse, err error)
	GetUserInfoByUserId(ctx context.Context, req *MrGetUserInfoByUserIdRequest) (rsp *GetUserInfoByUserIdResponse, err error)
}
