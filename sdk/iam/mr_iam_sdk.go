/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/04/28
 * File: mr_iam_sdk.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package iam TODO package function desc
package iam

import (
	"context"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	commonUtil "icode.baidu.com/baidu/scs/x1-base/utils/common"
)

var ErrCantPickIamSdk = errors.Errorf("cant pick a valid iam sdk")

var MrIamSdk *mrIamSdk = &mrIamSdk{SdkMap: make(map[string]IamService, 0)}

type MrIamConf struct {
	common.MultiRegionCnf
}

type mrIamSdk struct {
	SdkMap map[string]IamService
}

func MustInitMrIamSdk(ctx context.Context, conf MrIamConf) {
	for _, region := range conf.RegionList {
		MrIamSdk.SdkMap[region] = NewIamSdk(conf.ServiceName + "_" + region)
	}
}

func (m *mrIamSdk) PickIamSdk(ctx context.Context, region string) IamService {
	innerRegion := commonUtil.GetInnerRegion(ctx, region)
	if iamSdk, ok := m.SdkMap[innerRegion]; ok {
		return iamSdk
	}
	return nil
}

func (m *mrIamSdk) CheckSignature(ctx context.Context, req *MrCheckSignatureRequest) (rsp *CheckSignatureResponse, err error) {
	iamSdk := m.PickIamSdk(ctx, req.Region)
	if iamSdk == nil {
		return nil, ErrCantPickIamSdk
	}
	return iamSdk.CheckSignature(ctx, req.Req)
}

func (m *mrIamSdk) GetUserIdByToken(ctx context.Context, req *MrGetUserIdByTokenRequest) (rsp *GetUserIdByTokenResponse, err error) {
	iamSdk := m.PickIamSdk(ctx, req.Region)
	if iamSdk == nil {
		return nil, ErrCantPickIamSdk
	}
	return iamSdk.GetUserIdByToken(ctx, req.Req)
}

func (m *mrIamSdk) GetUserTenantIdByUserId(ctx context.Context, req *MrGetUserTenantIdByUserIdRequest) (rsp *GetUserTenantIdByUserIdResponse, err error) {
	iamSdk := m.PickIamSdk(ctx, req.Region)
	if iamSdk == nil {
		return nil, ErrCantPickIamSdk
	}
	return iamSdk.GetUserTenantIdByUserId(ctx, req.Req)
}

func (m *mrIamSdk) GetTokenByUserName(ctx context.Context, req *MrGetTokenByUserNameRequest) (rsp *GetTokenByUserNameResponse, err error) {
	iamSdk := m.PickIamSdk(ctx, req.Region)
	if iamSdk == nil {
		return nil, ErrCantPickIamSdk
	}
	return iamSdk.GetTokenByUserName(ctx, req.Req)
}

func (m *mrIamSdk) GetServiceToken(ctx context.Context, req *MrGetServiceTokenRequest) (rsp *GetServiceTokenResponse, err error) {
	iamSdk := m.PickIamSdk(ctx, req.Region)
	if iamSdk == nil {
		return nil, ErrCantPickIamSdk
	}
	return iamSdk.GetServiceToken(ctx, req.Req)
}

func (m *mrIamSdk) CipherDecrypt(ctx context.Context, req *MrCipherDecryptRequest) (rsp *CipherDecryptResponse, err error) {
	iamSdk := m.PickIamSdk(ctx, req.Region)
	if iamSdk == nil {
		return nil, ErrCantPickIamSdk
	}
	return iamSdk.CipherDecrypt(ctx, req.Req)
}

func (m *mrIamSdk) GetUserInfoByUserId(ctx context.Context, req *MrGetUserInfoByUserIdRequest) (rsp *GetUserInfoByUserIdResponse, err error) {
	iamSdk := m.PickIamSdk(ctx, req.Region)
	if iamSdk == nil {
		return nil, ErrCantPickIamSdk
	}
	return iamSdk.GetUserInfoByUserId(ctx, req.Req)
}
