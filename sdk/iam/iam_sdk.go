/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-06
 * File: interface_impl.go
 */

/*
 * DESCRIPTION
 *   根据iam.proto生成的implement文件
 */

// Package iam
package iam

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/spf13/cast"
	iamHttp "icode.baidu.com/baidu/bce-iam/sdk-go/http"
	iamClient "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/net/ral"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bce_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
)

// iamSdk - IamService implement
type iamSdk struct {
	conf   *iamConf
	client *iamClient.BceClient
	signer bce_utils.Signer
}

// NewDefaultIamSdk - 使用默认serviceName创建IamSdk
func NewDefaultIamSdk() IamService {
	return newIamSdk(DefaultServiceName)
}

// NewIamSdk - 使用指定serviceName创建IamSdk
func NewIamSdk(serviceName string) IamService {
	return newIamSdk(serviceName)
}

// newIamSdk - inner new instance
func newIamSdk(serviceName string) *iamSdk {
	if serviceName == "" {
		panic("invalid service name")
	}

	conf := getConf(serviceName)

	client := iamClient.NewBceClient(&iamClient.BceClientConfiguration{
		Endpoint:    conf.EndPoint,
		IamEndpoint: conf.EndPoint,
		Retry: iamClient.NewBackOffRetryPolicy(
			conf.RetryPolicy.MaxRetry,
			conf.RetryPolicy.MaxDelay,
			conf.RetryPolicy.MaxInterval,
		),
		UserName: conf.UserName,
		Password: conf.Password,
		Domain:   conf.Domain,
	})

	signer := bce_utils.NewV1Signer()

	s := &iamSdk{
		conf:   conf,
		client: client,
		signer: signer,
	}

	return s
}

// SetClient - 修改iam client
func (s *iamSdk) SetClient(client *iamClient.BceClient) *iamClient.BceClient {
	last := s.client
	s.client = client
	return last
}

// GetSignatureByCredential - 已经转到bce_utils.NewV1Signer().GetSignature()

// CheckSignature - 请求iam服务，检查签名并返回用户id
// TODO 暂不支持特权服务本地校验（缓存ak->sk和ak->token,进行本地validate signature校验）
// iamClient的ValidatorRequest()函数使用了getConsoleTokenID()不能直接使用（参见getServiceToken()的说明）
func (s *iamSdk) CheckSignature(ctx context.Context, req *CheckSignatureRequest) (rsp *CheckSignatureResponse, err error) {
	// build auth request
	authRequest := iamClient.Request{
		Method:  req.HttpMethod,
		Uri:     req.Uri,
		Params:  map[string]string{},
		Headers: map[string]string{},
	}

	if queries, err := sdk_utils.CastRequestQueriesToMap(req.Queries); err != nil {
		return nil, cerrs.ErrInvalidParams.Wrap(err)
	} else {
		for k, v := range queries {
			authRequest.Params[k] = cast.ToString(v)
		}
	}

	if headers, err := sdk_utils.CastRequestHeadersToMap(req.Headers); err != nil {
		return nil, cerrs.ErrInvalidParams.Wrap(err)
	} else {
		for k, v := range headers {
			authRequest.Headers[k] = cast.ToString(v)
		}
	}
	if req.SessionToken != "" {
		authRequest.Headers[iamClient.SECURITY_TOKEN_HEADER] = req.SessionToken
	}

	// build validator data
	validator := &iamClient.SignatureValidator{
		Auth: iamClient.Auth{
			Authorization: req.Authorization,
			Request:       authRequest,
			SecurityToken: req.SessionToken,
		},
	}
	dataBytes, _ := json.Marshal(validator)
	body, _ := iamClient.NewBodyFromBytes(dataBytes)

	// 构造bce request执行并返回token
	bceRequest := &iamClient.BceRequest{}
	bceRequest.SetMethod(iamHttp.POST)
	bceRequest.SetUri(Version + "/BCE-CRED/accesskeys")
	bceRequest.SetBody(body)

	token, err := s.doRequestAndGetToken(ctx, true, bceRequest)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "validate request fail, transId: %s, err: %s", req.TransactionId, err.Error())
		return nil, err
	}
	logger.SdkLogger.Trace(ctx, "get iam token suc:%s", base_utils.Format(token.User))

	rsp = &CheckSignatureResponse{
		UserTenantId: token.Project.ID,
		UserId:       token.User.ID,
		AccountId:    accountId(token),
	}
	return
}

//	baidu/bce-iam/sdk-cpp 	 const std::string& account_id() const {
//	    if (_user.domain().id() == "default") {
//	        return _user.id();
//	    } else {
//	        return _user.domain().id();
//	    }
//	}
func accountId(token *iamClient.Token) string {
	_user := token.User
	if _user.Domain.ID == "default" {
		return _user.ID
	} else {
		return _user.Domain.ID
	}
}

// GetUserIdByToken - 通过token获取user Id和tenant Id
func (s *iamSdk) GetUserIdByToken(ctx context.Context, req *GetUserIdByTokenRequest) (rsp *GetUserIdByTokenResponse, err error) {
	bceRequest := &iamClient.BceRequest{}
	bceRequest.SetMethod(iamHttp.GET)
	bceRequest.SetUri(Version + "/auth/tokens")
	bceRequest.SetHeader(subjectTokenHeader, req.UserToken)

	token, err := s.doRequestAndGetToken(ctx, true, bceRequest)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "GetUserIdByToken fail, transId: %s, err: %s", req.TransactionId, err.Error())
		return nil, err
	}

	rsp = &GetUserIdByTokenResponse{
		UserTenantId: token.Project.ID,
		UserId:       token.User.ID,
	}
	return
}

// GetUserTenantIdByUserId - 通过iam_user_id获得tenantid
func (s *iamSdk) GetUserTenantIdByUserId(ctx context.Context, req *GetUserTenantIdByUserIdRequest) (
	rsp *GetUserTenantIdByUserIdResponse, err error) {

	bceRequest := &iamClient.BceRequest{}
	bceRequest.SetMethod(iamHttp.GET)
	bceRequest.SetUri(Version + "/BCE-CRED/tokens")
	bceRequest.SetParam("user_id", req.UserId)

	token, err := s.doRequestAndGetToken(ctx, true, bceRequest)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "GetUserTenantIdByUserId fail, transId: %s, err: %s", req.TransactionId, err.Error())
		return nil, err
	}

	rsp = &GetUserTenantIdByUserIdResponse{
		UserTenantId: token.Project.ID,
	}
	return
}

// GetTokenByUserName - 通过username获取token
func (s *iamSdk) GetTokenByUserName(ctx context.Context, req *GetTokenByUserNameRequest) (rsp *GetTokenByUserNameResponse, err error) {
	bceRequest := &iamClient.BceRequest{}
	bceRequest.SetMethod(iamHttp.GET)
	bceRequest.SetUri(Version + "/BCE-CRED/tokens")
	bceRequest.SetParam("username", req.UserName)
	if req.DomainId != "" {
		bceRequest.SetParam("domain_id", req.UserName)
	}

	token, err := s.doRequestAndGetToken(ctx, true, bceRequest)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "GetTokenByUserName fail, transId: %s, err: %s", req.TransactionId, err.Error())
		return nil, err
	}

	rsp = &GetTokenByUserNameResponse{
		Token: token.ID,
	}
	return
}

// GetServiceToken - 获取service token
func (s *iamSdk) GetServiceToken(ctx context.Context, req *GetServiceTokenRequest) (rsp *GetServiceTokenResponse, err error) {
	token, err := s.getServiceTokenCached(ctx)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "iam get console tokenId fail, transId: %s, err: %s", req.TransactionId, err.Error())
		return nil, err
	}
	fmt.Printf("service token: %s\n", base_utils.Format(token))

	rsp = &GetServiceTokenResponse{Token: token.ID}
	return
}

// CipherDecrypt -
func (s *iamSdk) CipherDecrypt(ctx context.Context, req *CipherDecryptRequest) (rsp *CipherDecryptResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.CipherHex == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("cipher invalid")
	}
	auth := s.signer.ParseAuthorization(req.Authorization)
	if auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("authorization parse fail")
	}

	bceRequest := &iamClient.BceRequest{}
	bceRequest.SetMethod(iamHttp.POST)
	bceRequest.SetUri(Version + "/BCE-CRED/ciphers?decrypt")

	iamReq := &KeystoneCipherDecryptRequest{
		CipherHex:   req.CipherHex,
		AccesskeyId: auth.Ak,
	}
	bytes, _ := json.Marshal(iamReq)
	body, _ := iamClient.NewBodyFromBytes(bytes)
	bceRequest.SetBody(body)

	resp := &iamClient.BceResponse{}
	if err := s.doRequest(ctx, true, bceRequest, resp); err != nil {
		logger.SdkLogger.Warning(ctx, "iam send decrypt request fail, err: %s", err.Error())
		return nil, cerrs.ErrIAMRequestFail.Wrap(err)
	}
	defer func() { _ = resp.Body().Close() }()

	iamRsp := &KeystoneCipherDecryptResponse{}
	if err := resp.ParseJsonBody(iamRsp); err != nil {
		logger.SdkLogger.Warning(ctx, "iam parse response fail, err: %s", err.Error())
		return nil, cerrs.ErrIAMDecryptFail.Wrap(err)
	}

	passwd, err := crypto_utils.Decode(crypto_utils.HexCodecType, iamRsp.RawHex)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "passwd decoded fail, err: %s", err.Error())
		return nil, cerrs.ErrIAMDecryptFail.Wrap(err)
	}

	rsp = &CipherDecryptResponse{
		UserId:          iamRsp.UserId,
		CipherDecrypted: string(passwd),
	}

	return
}

// GetUserInfoByUserId -
func (s *iamSdk) GetUserInfoByUserId(ctx context.Context, req *GetUserInfoByUserIdRequest) (rsp *GetUserInfoByUserIdResponse, err error) {
	bceRequest := &iamClient.BceRequest{}
	bceRequest.SetMethod(iamHttp.GET)
	bceRequest.SetUri(Version + "/users/" + req.UserId)

	resp := &iamClient.BceResponse{}
	if err := s.doRequest(ctx, true, bceRequest, resp); err != nil {
		logger.SdkLogger.Warning(ctx, "iam send request fail, transId: %s, err: %s", req.TransactionId, err.Error())
		return nil, cerrs.ErrIAMRequestFail.Wrap(err)
	}
	defer func() { _ = resp.Body().Close() }()

	rsp = &GetUserInfoByUserIdResponse{}
	if err := resp.ParseJsonBody(rsp); err != nil {
		logger.SdkLogger.Warning(ctx, "iam parse response fail, transId: %s, err: %s", req.TransactionId, err.Error())
		return nil, cerrs.ErrIAMResponseInvalid.Wrap(err)
	}
	return
}

// getServiceTokenCached - 获取service token，并做缓存处理
func (s *iamSdk) getServiceTokenCached(ctx context.Context) (token *iamClient.Token, err error) {
	if confToken := s.conf.getServiceToken(); confToken != nil {
		return confToken, nil
	}

	newToken, err := s.getServiceToken(ctx)
	if err != nil {
		return
	}

	s.conf.setServiceToken(newToken)

	return newToken, nil
}

// getServiceToken - 获取service token
// iamClient的GetConsoleToken()函数有2个问题:
// 1. 里面写死protocol是https，沙盒访问会失败
// 2. 执行后会回写bceClient的xAuthToken字段，不能并发(只能每次都new新的client)
// 综合上述原因，不直接使用上述函数，而将其中部分逻辑拷贝过来使用自己实现
func (s *iamSdk) getServiceToken(ctx context.Context) (token *iamClient.Token, err error) {
	methods := []string{
		"password",
	}
	auth := iamClient.Authentication{
		Identity: iamClient.Identity{
			Methods: methods,
			Password: iamClient.Password{
				User: iamClient.PasswordUser{
					Domain: iamClient.Domain{
						ID: s.conf.Domain,
					},
					Name:     s.conf.UserName,
					Password: s.conf.Password,
				},
			},
		},
		Scope: iamClient.Scope{
			Domain: iamClient.Domain{
				ID: s.conf.Domain,
			},
		},
	}
	bceRequest := &iamClient.BceRequest{}
	bceRequest.SetUri(Version + "/auth/tokens")
	bceRequest.SetMethod(iamHttp.POST)
	authBody := map[string]interface{}{
		"auth": auth,
	}
	dataBytes, _ := json.Marshal(&authBody)
	body, _ := iamClient.NewBodyFromBytes(dataBytes)
	bceRequest.SetBody(body)

	return s.doRequestAndGetToken(ctx, false, bceRequest)
}

// doRequestAndGetToken - 请求token通用处理
func (s *iamSdk) doRequestAndGetToken(ctx context.Context, needServiceToken bool,
	bceRequest *iamClient.BceRequest) (token *iamClient.Token, err error) {

	resp := &iamClient.BceResponse{}
	if err := s.doRequest(ctx, needServiceToken, bceRequest, resp); err != nil {
		logger.SdkLogger.Warning(ctx, "iam send request fail, err: %s", err.Error())
		return nil, cerrs.ErrIAMRequestFail.Wrap(err)
	}
	defer func() { _ = resp.Body().Close() }()

	tokenWrapper := &iamClient.TokenWrapper{}
	if err := resp.ParseJsonBody(tokenWrapper); err != nil {
		logger.SdkLogger.Warning(ctx, "iam parse response fail, err: %s", err.Error())
		return nil, cerrs.ErrIAMResponseInvalid.Wrap(err)
	}

	token = &tokenWrapper.Token

	tokenId := resp.Header(subjectTokenHeader)
	if tokenId != "" {
		logger.SdkLogger.Debug(ctx, "authenticate returns token: %s", tokenId)
		token.ID = tokenId
	}

	return
}

// doRequest - 添加x-bce-token到请求头，并发起请求
func (s *iamSdk) doRequest(ctx context.Context, needServiceToken bool,
	bceRequest *iamClient.BceRequest, bceResponse *iamClient.BceResponse) error {
	if needServiceToken {
		serviceToken, err := s.getServiceTokenCached(ctx)
		if err != nil {
			logger.SdkLogger.Warning(ctx, "get service token fail, err: %s", err.Error())
			return cerrs.ErrIAMGetTokenFail.Wrap(err)
		}
		bceRequest.SetTokenId(serviceToken.ID)
	}

	bceRequest.SetHeader(subUserSupportHeader, "true")

	var err error
	doneFunc := s.ralLogStart(ctx, bceRequest)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("[panic] %v", r)
		}
		doneFunc(bceResponse, err)
	}()
	err = s.client.SendRequest(bceRequest, bceResponse)
	return err
}

// ralLogStart - 添加ral worker日志
func (s *iamSdk) ralLogStart(ctx context.Context, bceRequest *iamClient.BceRequest) func(
	bceResponse *iamClient.BceResponse, err error) {

	wl := sdk_utils.GetRalWorkLogger()
	if wl == nil {
		return func(bceResponse *iamClient.BceResponse, err error) {}
	}

	ctx = logit.NewContext(ctx)
	ral.InitRalStatisItems(ctx)

	now := time.Now()
	logFieldProtocol := logit.String(ral.LogFieldProtocol, s.conf.Protocol)
	logFieldCaller := logit.String(ral.LogFieldCaller, "IAM")
	logFieldProtocol.SetLevel(logit.AllLevels)
	logFieldCaller.SetLevel(logit.AllLevels)
	logFields := []logit.Field{
		logFieldCaller,
		logit.String(ral.LogFieldService, s.conf.ServiceName),
		logFieldProtocol,
		logit.String(ral.LogFieldMethod, bceRequest.Method()),
		logit.String(ral.LogFieldRemoteHost, s.conf.EndPoint),
		logit.Time(ral.LogFieldReqStartTime, now),
		logit.Time(ral.LogFieldTalkStartTime, now),
		logit.String(ral.LogFieldRetry, ""),
	}
	logit.ReplaceFields(ctx, logFields...)

	return func(bceResponse *iamClient.BceResponse, err error) {
		cost := time.Now().Sub(now)

		errNo := 0
		if err != nil {
			errNo = bceResponse.StatusCode()
		}
		logFields := []logit.Field{
			logit.String(ral.LogFieldAPI, ""),
			logit.String(ral.LogFieldURI, bceRequest.Uri()),
			logit.Error(ral.LogFieldErrmsg, err),
			logit.Int(ral.LogFieldErrno, errNo),
			logit.Duration(ral.LogFieldCost, cost),
			logit.Duration(ral.LogFieldTalk, cost),
			logit.Duration(ral.LogFieldRead, cost),
			logit.String(ral.LogFieldType, "E_SUM"),
		}

		wl.Trace(ctx, "", logFields...)

		if err != nil {
			wl.Warning(ctx, err.Error(), logFields...)
		}
	}
}
