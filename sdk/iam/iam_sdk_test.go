/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-14
 * File: iam_sdk_test.go
 */

/*
 * DESCRIPTION
 *   iam sdk tests
 */

// Package iam
package iam

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/scs/x1-base/sdk/bce_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/debug_utils"
)

const (
	TestIamUserId = "a318fe1fe9f5464d92478dab0aa4f5ff"
)

func TestIamSdk_GetUserInfoByUserId(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newIamSdk(DefaultServiceName)

	req := &GetUserInfoByUserIdRequest{
		TransactionId: "test",
		UserId:        TestIamUserId,
	}

	rsp, err := s.GetUserInfoByUserId(ctx, req)
	fmt.Println(rsp, err) // TODO access denied
	//if err != nil {
	//	t.Fatalf("GetUserInfoByUserId fail, req: %v, err: %v", base_utils.Format(req), err.Error())
	//}
	//
	//fmt.Printf("get userinfo: %s\n", base_utils.Format(rsp.User))
}

func TestIamSdk_GetUserTenantIdByUserId(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newIamSdk(DefaultServiceName)

	req := &GetUserTenantIdByUserIdRequest{
		TransactionId: "test",
		UserId:        TestIamUserId,
	}

	rsp, err := s.GetUserTenantIdByUserId(ctx, req)
	fmt.Println(rsp, err) // TODO access denied
	//if err != nil {
	//	t.Fatalf("GetUserTenantIdByUserId fail, req: %v, err: %v", base_utils.Format(req), err.Error())
	//}
	//
	//fmt.Printf("get user tenantId: %s\n", rsp.UserTenantId)
}

func TestIamSdk_GetServiceToken(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newIamSdk(DefaultServiceName)

	req := &GetServiceTokenRequest{
		TransactionId: "test",
	}

	rsp, err := s.GetServiceToken(ctx, req)
	if err != nil {
		t.Fatalf("GetServiceToken fail, req: %v, err: %v", base_utils.Format(req), err.Error())
	}

	fmt.Printf("get service token: %s\n", rsp.Token)
}

func TestIamSdk_GetAssumeRole_GetUserIdByToken(t *testing.T) {
	if !debug_utils.IsInnerNet() {
		t.Skipf("NOT INNER NET, SKIP THE TEST %s", t.Name())
	}

	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// get token
	stsSdk := sts.NewStsSdk(sts.DefaultServiceName)

	stsReq := &sts.GetAssumeRoleRequest{
		IamUserId:     TestIamUserId,
		TransactionId: uuid.New().String(),
	}

	stsRsp, err := stsSdk.GetAssumeRole(ctx, stsReq)
	if err != nil {
		t.Fatalf("get assume role fail")
	}
	fmt.Printf("get assume role, credRequest: %s, authString: %s\n",
		base_utils.Format(stsReq), base_utils.Format(stsRsp))

	iamSdk := newIamSdk(DefaultServiceName)

	req := &GetUserIdByTokenRequest{
		TransactionId: "test",
		UserToken:     stsRsp.Token.Id,
	}

	rsp, err := iamSdk.GetUserIdByToken(ctx, req)
	if err != nil {
		t.Fatalf("GetUserIdByToken fail, req: %v, err: %v", base_utils.Format(req), err.Error())
	}

	fmt.Printf("get userId: %s, tentantId: %s\n", rsp.UserId, rsp.UserTenantId)
	if stsReq.IamUserId != rsp.UserId {
		t.Fatalf("user id mot match, in: %s, out: %s", stsReq.IamUserId, rsp.UserId)
	}

}

func TestIamSdk_GetAssumeRole_Sign_CheckSign(t *testing.T) {
	if !debug_utils.IsInnerNet() {
		t.Skipf("NOT INNER NET, SKIP THE TEST %s", t.Name())
	}

	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	httpMethod := "GET"
	uri := "/v1/list/clusters"
	headers := map[string]interface{}{
		"Host": "scs.baidu.com",
	}

	// get token
	stsSdk := sts.NewStsSdk(sts.DefaultServiceName)

	stsReq := &sts.GetAssumeRoleRequest{
		IamUserId:     TestIamUserId,
		TransactionId: uuid.New().String(),
	}

	stsRsp, err := stsSdk.GetAssumeRole(ctx, stsReq)
	if err != nil {
		t.Fatalf("get assume role fail")
	}
	fmt.Printf("get assume role, credRequest: %s, authString: %s\n",
		base_utils.Format(stsReq), base_utils.Format(stsRsp))

	// get sign
	credRequest := &bce_utils.CredentialRequest{
		Ak:              stsRsp.AccessKeyId,
		Sk:              stsRsp.SecretAccessKey,
		SessionToken:    stsRsp.SessionToken,
		HttpMethod:      httpMethod,
		Uri:             uri,
		Queries:         nil,
		Headers:         headers,
		HeadersMustSign: []string{"Host"},
		Timestamp:       time.Now().Unix(),
		DurationSec:     1000,
	}
	authString, err := bce_utils.NewV1Signer().GetSignature(credRequest)
	if err != nil {
		t.Fatalf("GetSignatureByCredential fail, credRequest: %v, err: %v", base_utils.Format(credRequest), err.Error())
	}
	fmt.Printf("get authString.Authorization: %s\n", authString)

	// verify sign
	iamSdk := NewIamSdk(DefaultServiceName)
	reqVerify := &CheckSignatureRequest{
		TransactionId: "test",
		Authorization: authString,
		SessionToken:  credRequest.SessionToken,
		HttpMethod:    httpMethod,
		Uri:           uri,
		Queries:       nil,
		Headers:       headers,
	}
	verifyRsp, err := iamSdk.CheckSignature(ctx, reqVerify)
	if err != nil {
		t.Fatalf("CheckSignature fail, reqVerify: %v, err: %v", base_utils.Format(reqVerify), err.Error())
	}
	fmt.Printf("get verifyRsp.UserId: %v\n", verifyRsp.UserId)

	if stsReq.IamUserId != verifyRsp.UserId {
		t.Fatalf("user id mot match, in: %s, out: %s", stsReq.IamUserId, verifyRsp.UserId)
	}
}
