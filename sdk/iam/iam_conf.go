package iam

import (
	"fmt"
	"icode.baidu.com/baidu/gdp/env"
	"regexp"
	"sync"
	"time"

	iamClient "icode.baidu.com/baidu/bce-iam/sdk-go/iam"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "IAM"

type iamRetryPolicyConf struct {
	MaxRetry    int
	MaxDelay    int64
	MaxInterval int64
}

type iamConf struct {
	ServiceName string `toml:"-"`

	EndPoint string `toml:"EndPoint"`
	Protocol string `toml:"Protocol"`
	UserName string `toml:"UserName"`
	Password string `toml:"Password"`
	Domain   string `toml:"Domain"`

	RetryPolicy *iamRetryPolicyConf `toml:"-"`

	serviceToken *iamClient.Token `toml:"-"`
	tokenMutex   sync.RWMutex     `toml:"-"`
}

var iamConfMap = &sync.Map{}

func getConf(serviceName string) *iamConf {
	if conf, ok := iamConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*iamConf); ok {
			return conf
		}
	}

	conf := &iamConf{ServiceName: serviceName}
	conf.mustLoad()

	iamConfMap.Store(serviceName, conf)

	return conf
}

var protocolPatternRegExp = regexp.MustCompile(`^(?i)(https?)://`)

func (conf *iamConf) mustLoad() {
	ralConf, err := sdk_utils.GetRalConf(conf.ServiceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}

	if conf.EndPoint == "" {
		if defaultEndpoint, ok := ralConf.Resource.Manual["default"]; ok {
			conf.EndPoint = fmt.Sprintf("%s:%d", defaultEndpoint[0].Host, defaultEndpoint[0].Port)
		}
		for idc, v := range ralConf.Resource.Manual {
			if idc == env.IDC() {
				conf.EndPoint = fmt.Sprintf("%s:%d", v[0].Host, v[0].Port)
				break
			}
		}
		if conf.EndPoint == "" {
			panic(fmt.Sprintf("can't get an endpoint from ral conf"))
		}
	}

	if m := protocolPatternRegExp.FindStringSubmatch(conf.EndPoint); m != nil {
		conf.Protocol = m[1]
	} else if conf.Protocol != "" {
		conf.EndPoint = conf.Protocol + "://" + conf.EndPoint
	}

	conf.RetryPolicy = &iamRetryPolicyConf{
		MaxRetry:    int(ralConf.Retry),
		MaxDelay:    ralConf.ReadTimeOut + ralConf.WriteTimeOut,
		MaxInterval: 1000,
	}
}

func (conf *iamConf) getServiceToken() *iamClient.Token {
	conf.tokenMutex.RLock()
	defer conf.tokenMutex.RUnlock()

	if conf.serviceToken != nil {
		ttl := conf.serviceToken.ExpiresAt.Sub(time.Now())
		if ttl > serviceTokenDiscardTTL*time.Second {
			return conf.serviceToken
		}
	}

	return nil
}

func (conf *iamConf) setServiceToken(token *iamClient.Token) {
	conf.tokenMutex.Lock()
	defer conf.tokenMutex.Unlock()

	conf.serviceToken = token
}
