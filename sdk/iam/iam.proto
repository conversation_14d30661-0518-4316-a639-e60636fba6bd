// protofsg -with_context -json_tag=2 iam.proto interface.go
option cc_generic_services = true;
option go_package = "./;iam";
package x1base.sdk.iam;

message Interface {}

message CheckSignatureRequest {
	optional string TransactionId = 1;
	optional string Authorization = 2;
	optional string SessionToken = 3;
	optional string HttpMethod = 4;
	optional string Uri = 5;
	optional Interface Queries = 6;
	optional Interface Headers = 7;
}

message CheckSignatureResponse {
    optional string UserTenantId = 1;
    optional string UserId = 2;
}

message GetUserIdByTokenRequest {
	optional string TransactionId = 1;
	optional string UserToken = 2;
}

message GetUserIdByTokenResponse {
    optional string UserTenantId = 1;
    optional string UserId = 2;
}

message GetUserTenantIdByUserIdRequest {
	optional string TransactionId = 1;
	optional string UserId = 2;
}

message GetUserTenantIdByUserIdResponse {
    optional string UserTenantId = 1;
}

message GetServiceTokenRequest {
	optional string TransactionId = 1;
}

message GetServiceTokenResponse {
	optional string Token = 1;
}

message CipherDecryptRequest {
    optional string cipher_hex = 1;
    optional string authorization = 2;
}

message CipherDecryptResponse {
    optional string user_id = 1;
    optional string cipher_decrypted = 2;
}

message KeystoneCipherDecryptRequest {
    optional string cipher_hex = 1;
    optional string accesskey_id = 2;
}

message KeystoneCipherDecryptResponse {
    optional string user_id = 1;
    optional string raw_hex = 2;
}

message GetUserInfoByUserIdRequest {
	optional string TransactionId = 1;
	optional string UserId = 2;
}

message GetUserInfoByUserIdResponse {
	optional UserInfo user = 1;
}

message UserInfo {
	optional string id = 1;
	optional string name = 2;
	optional string email = 3;
	optional bool enabled = 4;
	optional string domain_id = 5;
	optional string default_project_id = 6;
	optional Interface description = 7;
}

message GetTokenByUserNameRequest {
	optional string TransactionId = 1;
	optional string UserName = 2;
	optional string DomainId = 3;
}

message GetTokenByUserNameResponse {
	optional string token = 1;
}

service IamService {
	rpc CheckSignature(CheckSignatureRequest) returns (CheckSignatureResponse)
	rpc GetUserIdByToken(GetUserIdByTokenRequest) returns (GetUserIdByTokenResponse)
	rpc GetUserTenantIdByUserId(GetUserTenantIdByUserIdRequest) returns (GetUserTenantIdByUserIdResponse)
    rpc GetTokenByUserName(GetTokenByUserNameRequest) returns (GetTokenByUserNameResponse)
	rpc GetServiceToken(GetServiceTokenRequest) returns (GetServiceTokenResponse)
	rpc CipherDecrypt(CipherDecryptRequest) returns (CipherDecryptResponse)
	rpc GetUserInfoByUserId(GetUserInfoByUserIdRequest) returns (GetUserInfoByUserIdResponse)
}

