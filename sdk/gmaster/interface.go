/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-28
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据zone.proto生成的interface文件
 */

// Package zone
package gmaster

import (
	"context"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
)

type Redis struct {
	ShardId string `json:"shardId"`
	NodeId  string `json:"uniqueId"`
	//Type string `json:"type"`
	Role       string `json:"role"`
	Ip         string `json:"ip"`
	FloatingIp string `json:"floatingIp"`
	Port       int    `json:"port"`
}

type Proxy struct {
	ProxyId    string `json:"proxyId"`
	Ip         string `json:"ip"`
	FloatingIp string `json:"floatingIp"`
	Port       int    `json:"port"`
}

type AddRedisRequest struct {
	AppID      string                        `json:"appId"`
	AppGroupID string                        `json:"appGroupId"`
	Token      string                        `json:"-"`
	Nodes      []*global_model.AppGroupRedis `json:"nodes"`
}

type AddRedisResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"requestId"`
}

type GetRedisRequest struct {
	AppID       string `json:"appId"`
	ShardID     string `json:"shardId"`
	AppGroupID  string `json:"appGroupId"`
	Token       string `json:"-"`
	WithoutLock bool   `json:"withoutLock"`
}

type GetRedisResponse struct {
	Code      string                        `json:"code"`
	Message   string                        `json:"message"`
	RequestID string                        `json:"requestId"`
	Nodes     []*global_model.AppGroupRedis `json:"nodes"`
}

type DeleteRedisRequest struct {
	AppID      string   `json:"appId"`
	AppGroupID string   `json:"appGroupId"`
	Token      string   `json:"-"`
	NodeIDs    []string `json:"nodeIds"`
}

type DeleteRedisResponse struct {
	Code      string   `json:"code"`
	Message   string   `json:"message"`
	RequestID string   `json:"requestId"`
	Nodes     []*Redis `json:"nodes"`
}

type AddProxyRequest struct {
	AppID      string                        `json:"appId"`
	AppGroupID string                        `json:"appGroupId"`
	Token      string                        `json:"-"`
	Proxies    []*global_model.AppGroupProxy `json:"proxies"`
}

type AddProxyResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"requestId"`
}

type GetProxyRequest struct {
	AppID      string `json:"appId"`
	AppGroupID string `json:"appGroupId"`
	Token      string `json:"-"`
}

type GetProxyResponse struct {
	Code      string                        `json:"code"`
	Message   string                        `json:"message"`
	RequestID string                        `json:"requestId"`
	Proxies   []*global_model.AppGroupProxy `json:"proxies"`
}

type DeleteProxyRequest struct {
	AppID      string   `json:"appId"`
	AppGroupID string   `json:"appGroupId"`
	Token      string   `json:"-"`
	ProxyIDs   []string `json:"proxyIds"`
}

type DeleteProxyResponse struct {
	Code      string   `json:"code"`
	Message   string   `json:"message"`
	RequestID string   `json:"requestId"`
	Proxies   []*Proxy `json:"proxies"`
}

type AddShardRequest struct {
	AppID            string `json:"appId"`
	AppGroupID       string `json:"appGroupId"`
	Token            string `json:"-"`
	TargetShardCount int    `json:"targetShardCount"`
}

type AddShardResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"requestId"`
}

type GetShardRequest struct {
	AppID      string `json:"appId"`
	AppGroupID string `json:"appGroupId"`
	Token      string `json:"-"`
}

type GetShardResponse struct {
	Code      string                        `json:"code"`
	Message   string                        `json:"message"`
	RequestID string                        `json:"requestId"`
	Shards    []*global_model.AppGroupShard `json:"shards"`
}

type DeleteShardRequest struct {
	AppID      string   `json:"appId"`
	AppGroupID string   `json:"appGroupId"`
	Token      string   `json:"-"`
	ShardIDs   []string `json:"shardIds"`
}

type DeleteShardResponse struct {
	Code      string                        `json:"code"`
	Message   string                        `json:"message"`
	RequestID string                        `json:"requestId"`
	Shards    []*global_model.AppGroupShard `json:"shards"`
}

type LocalFailoverRequest struct {
	AppID          string `json:"appId"`
	AppGroupID     string `json:"appGroupId"`
	Token          string `json:"-"`
	ShardID        string `json:"shardId"`
	NewNodeID      string `json:"newNodeId"`
	IsManualSwitch bool   `json:"isManualSwitch"`
	UseForbidWrite bool   `json:"useForbidWrite"`
	SyncOffsetDiff int64  `json:"syncOffsetDiff"`
}

type LocalFailoverResponse struct {
	Code               string `json:"code"`
	Message            string `json:"message"`
	RequestID          string `json:"requestId"`
	SwitchMasterErrMsg string `json:"switchMasterErrMsg"`
}

type SlaveOfMasterRequest struct {
	AppID      string `json:"appId"`
	AppGroupID string `json:"appGroupId"`
	Token      string `json:"-"`
	ShardID    string `json:"shardId"`
	NewNodeID  string `json:"newNodeId"`
}

type SlaveOfMasterResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"requestId"`
}

type UpdateInnerSecurityRequest struct {
	AppID       string   `json:"appId"`
	AppGroupID  string   `json:"appGroupId"`
	Token       string   `json:"-"`
	ToAddIpList []string `json:"to_add_ip_list"`
	ToDelIpList []string `json:"to_del_ip_list"`
	PortList    []int32  `json:"port_list"`
}

type UpdateInnerSecurityResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"requestId"`
}

type GroupStatusCASRequest struct {
	AppID          string   `json:"appId"`
	AppGroupID     string   `json:"appGroupId"`
	Token          string   `json:"-"`
	AssumeStatuses []string `json:"assumeStatuses"`
	DestStatus     string   `json:"destStatus"`
}

type GetModifyStatusRequest struct {
	AppID            string `json:"appId"`
	AppGroupID       string `json:"appGroupId"`
	Token            string `json:"-"`
	IncludeCompleted bool   `json:"includeCompleted"`
}

type AddModifyStatusRequest struct {
	AppID            string `json:"appId"`
	AppGroupID       string `json:"appGroupId"`
	Token            string `json:"-"`
	Type             string `json:"type"`
	Stage            string `json:"stage"`
	Progress         string `json:"progress"`
	TargetNodeType   string `json:"targetNodeType"`
	TargetShardCount int    `json:"targetShardCount"`
}

type UpdateModifyStatusStageRequest struct {
	AppID      string `json:"appId"`
	AppGroupID string `json:"appGroupId"`
	Token      string `json:"-"`
	ID         int    `json:"id"`
	Stage      string `json:"stage"`
	Progress   string `json:"progress"`
	Version    int    `json:"version"`
}

type GroupStatusCASResponse struct {
	Message   string `json:"message"`
	RequestID string `json:"requestId"`
	Status    string `json:"status"`
}

type ModifyStatus struct {
	ID               int       `json:"id"`
	GroupID          string    `json:"groupId"`
	CreateAt         time.Time `json:"createAt"`
	UpdateAt         time.Time `json:"updateAt"`
	Type             string    `json:"type"`
	Stage            string    `json:"stage"`
	Progress         string    `json:"progress"`
	TargetNodeType   string    `json:"targetNodeType"`
	TargetShardCount int       `json:"targetShardCount"`
	Version          int       `json:"version"`
}

type GetModifyStatusResponse struct {
	Message        string          `json:"message"`
	RequestID      string          `json:"requestId"`
	ModifyStatuses []*ModifyStatus `json:"modifyStatuses"`
}

type AddModifyStatusResponse struct {
	Message   string `json:"message"`
	RequestID string `json:"requestId"`
}

type UpdateModifyStatusResponse struct {
	Message   string `json:"message"`
	RequestID string `json:"requestId"`
}

type ClusterDetail struct {
	ClusterName       string  `json:"clusterName"`
	ClusterShowId     string  `json:"clusterShowId"`
	Region            string  `json:"region"`
	TotalCapacityInGB float64 `json:"totalCapacityInGB"`
	UsedCapacityInGB  float64 `json:"usedCapacityInGB"`
	ShardNum          int     `json:"shardNum"`
	Flavor            float64 `json:"flavor"`
	QpsWrite          int     `json:"qpsWrite"`
	QpsRead           int     `json:"qpsRead"`
	AvailabilityZone  string  `json:"availabilityZone"`
	SyncStatus        string  `json:"syncStatus"`
	StaleReadable     bool    `json:"staleReadable"`
	MaxOffset         int     `json:"maxOffset"`
	Lag               int     `json:"lag"`
	ForbidWrite       int     `json:"forbidWrite"`
	Status            string  `json:"status"`
}

type ListCacheGroupDetailResponse struct {
	GroupId         string           `json:"groupId"`
	GroupName       string           `json:"groupName"`
	GroupStatus     string           `json:"groupStatus"`
	GroupCreateTime string           `json:"groupCreateTime"`
	Leader          *ClusterDetail   `json:"leader"`
	Followers       []*ClusterDetail `json:"followers"`
	RequestId       string           `json:"requestId"`
	Code            int              `json:"code"`
	Message         string           `json:"message"`
	ClusterNum      int              `json:"clusterNum"`
	BnsGroup        string           `json:"bnsGroup"`
}

type ListCacheGroupDetailRequest struct {
	AppID      string `json:"appId"`
	AppGroupID string `json:"appGroupId"`
	Token      string `json:"-"`
}

type ApplyItem struct {
	Region             string `json:"region"`
	CacheClusterID     int    `json:"cacheClusterId"`
	CacheClusterShowID string `json:"cacheClusterShowId"`
}

type Param struct {
	ConfName   string `json:"confName"`
	ConfValue  string `json:"confValue"`
	ConfModule int    `json:"confModule"`
	ConfType   int    `json:"confType"`
}

type ApplyTemplateRequest struct {
	TemplateID       int64        `json:"templateId"`
	TemplateShowID   string       `json:"templateShowId"`
	CacheClusterList []*ApplyItem `json:"cacheClusterList"`
	Extra            int          `json:"extra"`
	RebootType       int          `json:"rebootType"` // 0 不重启 1 维护时间重启 2 立即重启
	Parameters       []*Param     `json:"parameters"`
	Token            string       `json:"-"`
}

type InitStandaloneTopoReq struct {
	AppID          string `json:"appId"`
	AppGroupID     string `json:"appGroupId"`
	Token          string `json:"-"`
	ShardID        string `json:"shardId"`
	NewMasterID    string `json:"newMasterId"`
	NewMasterFixIP string `json:"newMasterFixIp"`
	NewMasterPort  int    `json:"newMasterPort"`
}

type UpdateBnsServiceRequest struct {
	AppID         string `json:"appId"`
	GroupID       string `json:"-"`
	OldBnsService string `json:"oldBnsService"`
	NewBnsService string `json:"newBnsService"`
	Token         string `json:"-"`
}

type UpdateBnsServiceResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"requestId"`
}

type GmasterService interface {
	AddNodes(ctx context.Context, req *AddRedisRequest) (resp *AddRedisResponse, err error)
	GetNodes(ctx context.Context, req *GetRedisRequest) (resp *GetRedisResponse, err error)
	DeleteNodes(ctx context.Context, req *DeleteRedisRequest) (resp *DeleteRedisResponse, err error)
	AddProxies(ctx context.Context, req *AddProxyRequest) (resp *AddProxyResponse, err error)
	GetProxies(ctx context.Context, req *GetProxyRequest) (resp *GetProxyResponse, err error)
	DeleteProxies(ctx context.Context, req *DeleteProxyRequest) (resp *DeleteProxyResponse, err error)
	AddShards(ctx context.Context, req *AddShardRequest) (resp *AddShardResponse, err error)
	GetShards(ctx context.Context, req *GetShardRequest) (resp *GetShardResponse, err error)
	DeleteShards(ctx context.Context, req *DeleteShardRequest) (resp *DeleteShardResponse, err error)
	LocalFailover(ctx context.Context, req *LocalFailoverRequest) (resp *LocalFailoverResponse, err error)
	SlaveOfMaster(ctx context.Context, req *SlaveOfMasterRequest) (resp *SlaveOfMasterResponse, err error)
	UpdateInnerSecurity(ctx context.Context, req *UpdateInnerSecurityRequest) (resp *UpdateInnerSecurityResponse, err error)
	GetModifyStatus(ctx context.Context, req *GetModifyStatusRequest) (resp *GetModifyStatusResponse, err error)
	AddModifyStatus(ctx context.Context, req *AddModifyStatusRequest) (resp *AddModifyStatusResponse, err error)
	UpdateModifyStatusStage(ctx context.Context, req *UpdateModifyStatusStageRequest) (resp *UpdateModifyStatusResponse, err error)
	GroupStatusCAS(ctx context.Context, req *GroupStatusCASRequest) (resp *GroupStatusCASResponse, err error)
	ListCacheGroupDetail(ctx context.Context, req *ListCacheGroupDetailRequest) (resp *ListCacheGroupDetailResponse, err error)
	UpdateSecurity(ctx context.Context, req *UpdateInnerSecurityRequest) (resp *UpdateInnerSecurityResponse, err error)
	ApplyTemplate(ctx context.Context, req *ApplyTemplateRequest) (err error)
	InitStandaloneTopo(ctx context.Context, req *InitStandaloneTopoReq) (err error)
	UpdateBnsService(ctx context.Context, req *UpdateBnsServiceRequest) (resp *UpdateBnsServiceResponse, err error)
}
