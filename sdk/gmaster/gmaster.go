/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-28
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据zone.proto生成的interface文件
 */

// Package zone
package gmaster

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type gmasterSdk struct {
	common.OpenApi
}

func NewDefaultGmasterSdk() GmasterService {
	return newGmasterSdk(DefaultServiceName)
}

// NewZoneSdk - new zoneSdk instance
func NewGmasterSdk(serviceName string) GmasterService {
	return newGmasterSdk(serviceName)
}

// newZoneSdk - new zoneSdk instance
func newGmasterSdk(serviceName string) *gmasterSdk {
	s := &gmasterSdk{
		OpenApi: common.NewOpenApi(serviceName),
	}
	return s
}

func (m *gmasterSdk) doGmasterRequest(ctx context.Context, actionName string, token string,
	httpMethod, uri string, queries, req, rsp interface{}) (err error) {
	params := &common.OpenApiParams{
		ActionName: actionName,
		Token:      token,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
	}

	return m.DoRequest(ctx, params, rsp)
}

func (m *gmasterSdk) AddNodes(ctx context.Context, req *AddRedisRequest) (resp *AddRedisResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &AddRedisResponse{}
	err = m.doGmasterRequest(ctx, "AddNodes", req.Token, "POST", NodesUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) GetNodes(ctx context.Context, req *GetRedisRequest) (resp *GetRedisResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &GetRedisResponse{}
	err = m.doGmasterRequest(ctx, "GetNodes", req.Token, "GET", NodesUrl, map[string]interface{}{
		"appGroupId": req.AppGroupID, "appId": req.AppID, "shardId": req.ShardID, "withoutLock": req.WithoutLock}, req, resp)
	return
}

func (m *gmasterSdk) DeleteNodes(ctx context.Context, req *DeleteRedisRequest) (resp *DeleteRedisResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &DeleteRedisResponse{}
	err = m.doGmasterRequest(ctx, "DeleteNodes", req.Token, "DELETE", NodesUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) AddProxies(ctx context.Context, req *AddProxyRequest) (resp *AddProxyResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &AddProxyResponse{}
	err = m.doGmasterRequest(ctx, "AddProxies", req.Token, "POST", ProxiesUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) GetProxies(ctx context.Context, req *GetProxyRequest) (resp *GetProxyResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &GetProxyResponse{}
	err = m.doGmasterRequest(ctx, "GetProxies", req.Token, "GET", ProxiesUrl, map[string]interface{}{"appGroupId": req.AppGroupID, "appId": req.AppID}, req, resp)
	return
}

func (m *gmasterSdk) DeleteProxies(ctx context.Context, req *DeleteProxyRequest) (resp *DeleteProxyResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &DeleteProxyResponse{}
	err = m.doGmasterRequest(ctx, "DeleteProxies", req.Token, "DELETE", ProxiesUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) AddShards(ctx context.Context, req *AddShardRequest) (resp *AddShardResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &AddShardResponse{}
	err = m.doGmasterRequest(ctx, "AddShards", req.Token, "POST", ShardsUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) GetShards(ctx context.Context, req *GetShardRequest) (resp *GetShardResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &GetShardResponse{}
	err = m.doGmasterRequest(ctx, "GetShards", req.Token, "GET", ShardsUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) DeleteShards(ctx context.Context, req *DeleteShardRequest) (resp *DeleteShardResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &DeleteShardResponse{}
	err = m.doGmasterRequest(ctx, "DeleteShards", req.Token, "DELETE", ShardsUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) LocalFailover(ctx context.Context, req *LocalFailoverRequest) (resp *LocalFailoverResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &LocalFailoverResponse{}
	err = m.doGmasterRequest(ctx, "LocalFailover", req.Token, "PATCH", LocalFailoverUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) SlaveOfMaster(ctx context.Context, req *SlaveOfMasterRequest) (resp *SlaveOfMasterResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &SlaveOfMasterResponse{}
	err = m.doGmasterRequest(ctx, "SlaveOfMaster", req.Token, "PATCH", SlaveOfMasterUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) UpdateInnerSecurity(ctx context.Context, req *UpdateInnerSecurityRequest) (resp *UpdateInnerSecurityResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &UpdateInnerSecurityResponse{}
	err = m.doGmasterRequest(ctx, "UpdateInnerSecurity", req.Token, "PATCH", UpdateInnerSecurityUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) GetModifyStatus(ctx context.Context, req *GetModifyStatusRequest) (resp *GetModifyStatusResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &GetModifyStatusResponse{}
	err = m.doGmasterRequest(ctx, "GetModifyStatus", req.Token, "POST", GetModifyStatusUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) AddModifyStatus(ctx context.Context, req *AddModifyStatusRequest) (resp *AddModifyStatusResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &AddModifyStatusResponse{}
	err = m.doGmasterRequest(ctx, "AddModifyStatus", req.Token, "POST", AddModifyStatusUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) UpdateModifyStatusStage(ctx context.Context, req *UpdateModifyStatusStageRequest) (resp *UpdateModifyStatusResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &UpdateModifyStatusResponse{}
	err = m.doGmasterRequest(ctx, "UpdateModifyStatusStage", req.Token, "POST", UpdateModifyStatusStageUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) GroupStatusCAS(ctx context.Context, req *GroupStatusCASRequest) (resp *GroupStatusCASResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &GroupStatusCASResponse{}
	err = m.doGmasterRequest(ctx, "GroupStatusCAS", req.Token, "POST", GroupStatusCASUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) ListCacheGroupDetail(ctx context.Context, req *ListCacheGroupDetailRequest) (resp *ListCacheGroupDetailResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &ListCacheGroupDetailResponse{}
	err = m.doGmasterRequest(ctx, "ListCacheGroupDetail", req.Token, "GET", ListCacheGroupDetailUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) UpdateSecurity(ctx context.Context, req *UpdateInnerSecurityRequest) (resp *UpdateInnerSecurityResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &UpdateInnerSecurityResponse{}
	err = m.doGmasterRequest(ctx, "UpdateSecurity", req.Token, "PATCH", UpdateSecurityUrl, map[string]interface{}{"appGroupId": req.AppGroupID}, req, resp)
	return
}

func (m *gmasterSdk) ApplyTemplate(ctx context.Context, req *ApplyTemplateRequest) (err error) {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	var resp interface{}
	err = m.doGmasterRequest(ctx, "UpdateSecurity", req.Token, "POST", ApplyTemplateUrl, nil, req, resp)
	return
}

func (m *gmasterSdk) InitStandaloneTopo(ctx context.Context, req *InitStandaloneTopoReq) (err error) {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	var resp interface{}
	err = m.doGmasterRequest(ctx, "InitStandaloneTopo", req.Token, "PATCH", InitStandaloneTopoUrl, nil, req, resp)
	return
}

func (m *gmasterSdk) UpdateBnsService(ctx context.Context, req *UpdateBnsServiceRequest) (resp *UpdateBnsServiceResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &UpdateBnsServiceResponse{}
	err = m.doGmasterRequest(ctx, "UpdateBnsService", req.Token, "POST",
		UpdateBnsServiceUrl, map[string]any{"groupId": req.GroupID}, req, resp)
	return
}
