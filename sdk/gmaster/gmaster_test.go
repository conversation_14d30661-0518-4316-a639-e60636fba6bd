package gmaster

import (
	"context"
	"net/http"
	"testing"

	"github.com/smartystreets/goconvey/convey"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

func TestSlaveOfMaster(t *testing.T) {
	convey.Convey("SlaveOfMaster", t, func() {
		ctx := context.Background()
		req := &SlaveOfMasterRequest{
			AppGroupID: "test",
			Token:      "test",
		}
		gmSdk := newGmasterSdk("DefaultServiceName")
		convey.Convey("when req is nil", func() {
			resp, err := gmSdk.SlaveOfMaster(ctx, nil)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(resp, convey.ShouldBeNil)
		})
		convey.Convey("when api return error", func() {
			gmSdk.SetRalCaller(sdk_utils.NewTestRalCaller(&SlaveOfMasterResponse{
				Message:   "",
				Code:      "",
				RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
			}, nil, http.StatusBadRequest))
			resp, err := gmSdk.SlaveOfMaster(ctx, req)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})
		convey.Convey("when api success", func() {
			gmSdk.SetRalCaller(sdk_utils.NewTestRalCaller(&SlaveOfMasterResponse{
				Message:   "",
				Code:      "",
				RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
			}, nil, http.StatusOK))
			resp, err := gmSdk.SlaveOfMaster(ctx, req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})
	})
}
