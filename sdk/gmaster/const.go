/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-28
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据zone.proto生成的interface文件
 */

// Package zone
package gmaster

const DefaultServiceName = "global-master"

const (
	NodesUrl                   = "/v1/scs/group/nodes"
	ProxiesUrl                 = "/v1/scs/group/proxies"
	ShardsUrl                  = "/v1/scs/group/shards"
	LocalFailoverUrl           = "/v1/scs/group/localfailover"
	SlaveOfMasterUrl           = "/v1/scs/group/slaveofmaster"
	UpdateInnerSecurityUrl     = "/v1/scs/group/updateinnersecurity"
	GetModifyStatusUrl         = "/v1/scs/group/getmodifystatus"
	AddModifyStatusUrl         = "/v1/scs/group/addmodifystatus"
	UpdateModifyStatusStageUrl = "/v1/scs/group/updatemodifystatusstage"
	GroupStatusCASUrl          = "/v1/scs/group/statuscas"
	ListCacheGroupDetailUrl    = "/v1/scs/group/detail"
	UpdateSecurityUrl          = "/v1/scs/group/updatesecurity"
	ApplyTemplateUrl           = "/v1/scs/template/apply"
	InitStandaloneTopoUrl      = "/v1/scs/group/initstandalonetopo"
	UpdateBnsServiceUrl        = "/v1/scs/bns/updateBnsService"
)
