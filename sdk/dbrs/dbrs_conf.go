/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/24 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file dbrs_conf.go
 * <AUTHOR>
 * @date 2023/03/24 15:15:47
 * @brief dbrs conf

 *
 **/

package dbrs

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "dbrs"

// dbrsConf definition
type dbrsConf struct {
	Product string `toml:"Product,omitempty"`
}

var bcmConfMap = &sync.Map{}

func getConf(serviceName string) *dbrsConf {
	if conf, ok := bcmConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*dbrsConf); ok {
			return conf
		}
	}

	conf := &dbrsConf{}
	conf.mustLoad(serviceName)

	bcmConfMap.Store(serviceName, conf)

	return conf
}

func (conf *dbrsConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
