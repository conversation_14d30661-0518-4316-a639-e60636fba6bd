package dbrs

import (
	"context"
	"net/http"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

// dbrs Service 的sdk实现
type dbrsSdk struct {
	conf *dbrsConf
	common.OpenApi
}

func NewBefaultDbrsSdk() DbrsService {
	return newDbrsSdk(DefaultServiceName)
}

func NewDbrsSdk(serviceName string) DbrsService {
	return newDbrsSdk(serviceName)
}

func newDbrsSdk(serviceName string) *dbrsSdk {
	s := &dbrsSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}

	return s
}

// doRequest - 通用openstack服务请求方法
func (s *dbrsSdk) dbrsRequest(ctx context.Context, actionName string, httpMethod,
	uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	params := &common.OpenApiParams{
		ActionName: actionName,
		Token:      "dbrs", // 底层封装有判空
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}

	// 请求 openapi
	err = s.DoRequest(ctx, params, rsp)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return err
	}
	return nil
}

// 【dbrs — api】
// 创建app备份策略
func (s *dbrsSdk) CreateBackupPolicy(ctx context.Context, req *CreatePolicyRequest) (resp *CommonPolicyResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.PolicyType == "" && req.DataBackupTime == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataBackupTime is null")
	}
	if req.PolicyType == "" && len(req.DataBackupRetainStrategys) == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataBackupRetainStrategys is null")
	}
	if req.DbaasType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DbaasType is null")
	}
	if req.PolicyType == "" && len(req.DataStorages) == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataStorages is null")
	}
	if req.Region == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param Region is null")
	}
	if req.PolicyType == "logical_log_backup" && len(req.BackupStorages) == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("req param BackupStorages is null")
	}
	if req.PolicyType == "logical_log_backup" && len(req.BackupRetainStrategys) == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("req param BackupRetainStrategys is null")
	}

	resp = &CommonPolicyResponse{}

	if err = s.dbrsRequest(ctx, "CreateBackupPolicy", http.MethodPost,
		OperatePolicyUri, nil, req, resp); err != nil {
		return nil, err
	}
	if resp.AppID != req.AppID {
		return nil, cerrs.ErrRalRequestFail.Errorf("CreateBackupPolicy request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}

func (s *dbrsSdk) DeleteBackupPolicy(ctx context.Context, req *DeletePolicyRequest) (resp *CommonPolicyResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.DbaasType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DbaasType is null")
	}

	resp = &CommonPolicyResponse{}

	if err = s.dbrsRequest(ctx, "DeleteBackupPolicy", http.MethodDelete,
		OperatePolicyUri, nil, req, resp); err != nil {
		return nil, err
	}
	if resp.AppID != req.AppID {
		return nil, cerrs.ErrRalRequestFail.Errorf("DeleteBackupPolicy request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}

// 【dbrs — api】
// 查询app备份策略
func (s *dbrsSdk) QueryBackupPolicy(ctx context.Context, req *QueryPolicyRequest) (resp *QueryPolicyResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.DbaasType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DbaasType is null")
	}

	resp = &QueryPolicyResponse{}

	queries := map[string]interface{}{
		"dataType":  req.DataType,
		"appID":     req.AppID,
		"marker":    req.Marker,
		"maxKeys":   req.MaxKeys,
		"dbaasType": req.DbaasType,
	}

	if err = s.dbrsRequest(ctx, "QueryBackupPolicy", http.MethodGet,
		OperatePolicyUri, queries, nil, resp); err != nil {
		return nil, err
	}
	if resp.AppID != req.AppID {
		return nil, cerrs.ErrRalRequestFail.Errorf("QueryBackupPolicy request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}

// 【dbrs — api】
// 查询app备份策略
func (s *dbrsSdk) ModifyBackupPolicy(ctx context.Context, req *CommonPolicyRequest) (resp *CommonPolicyResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.DataBackupTime == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataBackupTime is null")
	}
	if len(req.DataBackupRetainStrategys) == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataBackupRetainStrategys is null")
	}
	if req.DbaasType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DbaasType is null")
	}

	resp = &CommonPolicyResponse{}

	if err = s.dbrsRequest(ctx, "ModifyBackupPolicy", http.MethodPut,
		OperatePolicyUri, nil, req, resp); err != nil {
		return nil, err
	}
	if resp.AppID != req.AppID {
		return nil, cerrs.ErrRalRequestFail.Errorf("ModifyBackupPolicy request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}

func (s *dbrsSdk) QueryBackupList(ctx context.Context, req *QueryBackupListRequest) (resp *QueryBackupListResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.DbaasType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DbaasType is null")
	}

	resp = &QueryBackupListResponse{}

	queries := map[string]interface{}{
		"dataType":  req.DataType,
		"appID":     req.AppID,
		"marker":    req.Marker,
		"maxKeys":   req.MaxKeys,
		"dbaasType": req.DbaasType,
	}

	if err = s.dbrsRequest(ctx, "QueryBackupList", http.MethodGet,
		OperateBackupUri, queries, nil, resp); err != nil {
		return nil, err
	}
	if resp.AppID != req.AppID {
		return nil, cerrs.ErrRalRequestFail.Errorf("QueryBackupList request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}

func (s *dbrsSdk) CreateBackupTask(ctx context.Context, req *CreateBackupTaskRequest) (resp *CreateBackupTaskResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.Mode == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param Mode is null")
	}
	if req.DbaasType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DbaasType is null")
	}

	resp = &CreateBackupTaskResponse{}

	if err = s.dbrsRequest(ctx, "CreateBackupTask", http.MethodPost,
		OperateBackupUri, nil, req, resp); err != nil {
		return nil, err
	}
	if resp.AppID != req.AppID {
		return nil, cerrs.ErrRalRequestFail.Errorf("CreateBackupTask request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}

func (s *dbrsSdk) DeleteBackupRecord(ctx context.Context, req *DeleteBackupRecordRequest) (resp *DeleteBackupRecordResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.AppDataBackupID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppDataBackupID is null")
	}
	if req.DbaasType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DbaasType is null")
	}

	resp = &DeleteBackupRecordResponse{}

	DeleteBackupRecordUri := OperateBackupUri + "/" + req.AppDataBackupID

	queries := map[string]interface{}{
		"dataType": req.DataType,
		"appID":    req.AppID,
	}

	if err = s.dbrsRequest(ctx, "DeleteBackupRecord", http.MethodDelete,
		DeleteBackupRecordUri, queries, nil, resp); err != nil {
		return nil, err
	}
	if resp.AppID != req.AppID {
		return nil, cerrs.ErrRalRequestFail.Errorf("DeleteBackupRecord request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}

func (s *dbrsSdk) QueryAppBackupDetailByAppBackupId(ctx context.Context, req *QueryAppBackupDetailRequest) (resp *QueryAppBackupDetailResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.AppDataBackupID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppDataBackupID is null")
	}
	if req.DownloadUrlExpireSec == 0 {
		req.DownloadUrlExpireSec = 43200 // 默认值
	}
	if req.MaxKeys == 0 {
		req.MaxKeys = 1000 // 默认值
	}
	if req.DbaasType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DbaasType is null")
	}

	resp = &QueryAppBackupDetailResponse{}

	QueryAppBackupDetailUri := OperateBackupUri + "/" + req.AppDataBackupID

	queries := map[string]interface{}{
		"dataType":             req.DataType,
		"appID":                req.AppID,
		"downloadUrlExpireSec": req.DownloadUrlExpireSec,
		"marker":               req.Marker,
		"maxKeys":              req.MaxKeys,
		"dbaasType":            req.DbaasType,
	}

	if err = s.dbrsRequest(ctx, "QueryAppBackupDetailByAppBackupId", http.MethodGet,
		QueryAppBackupDetailUri, queries, nil, resp); err != nil {
		return nil, err
	}
	if resp.AppDataBackupID != req.AppDataBackupID {
		return nil, cerrs.ErrRalRequestFail.Errorf("QueryAppBackupDetailByAppBackupId request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}

func (s *dbrsSdk) QueryBackupDetailByBackupId(ctx context.Context, req *QueryShardBackupDetailRequest) (resp *QueryShardBackupDetailResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.ClusterID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param ClusterID is null")
	}
	if req.DatabackupID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DatabackupID is null")
	}
	if req.DownloadUrlExpireSec == 0 {
		req.DownloadUrlExpireSec = 43200 // 默认值
	}
	if req.DbaasType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DbaasType is null")
	}

	resp = &QueryShardBackupDetailResponse{}

	QueryBackupDetailUri := OperateBackupUri + "/" + req.DatabackupID

	queries := map[string]interface{}{
		"dataType":             req.DataType,
		"appID":                req.AppID,
		"clusterID":            req.ClusterID,
		"downloadUrlExpireSec": req.DownloadUrlExpireSec,
	}

	if err = s.dbrsRequest(ctx, "QueryBackupDetailByBackupId", http.MethodGet,
		QueryBackupDetailUri, queries, nil, resp); err != nil {
		return nil, err
	}
	if resp.AppID != req.AppID {
		return nil, cerrs.ErrRalRequestFail.Errorf("QueryBackupDetailByBackupId request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}

func (s *dbrsSdk) ModifyBackupEncryptPolicy(ctx context.Context, req *ModifyEncryptPolicyRequest) (resp *CommonPolicyResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is empty")
	}
	if req.EncryptStrategy == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param EncryptStrategy is null")
	}

	resp = &CommonPolicyResponse{}

	if err = s.dbrsRequest(ctx, "ModifyBackupEncryptPolicy", http.MethodPut,
		OperateEncryptPolicyUri, nil, req, resp); err != nil {
		return nil, err
	}
	if resp.AppID != req.AppID {
		return nil, cerrs.ErrRalRequestFail.Errorf("ModifyBackupEncryptPolicy request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}

func (s *dbrsSdk) CreateBackupRestore(ctx context.Context, req *CreateRestoreRequest) (resp *CreateRestoreResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is empty")
	}
	if req.ClusterID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param ClusterID is empty")
	}
	if req.DestInstances == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DestInstances is null")
	}

	resp = &CreateRestoreResponse{}

	if err = s.dbrsRequest(ctx, "CreateRestoreResponse", http.MethodPost,
		OperateRestoreUri, nil, req, resp); err != nil {
		return nil, err
	}
	if resp.AppID != req.AppID {
		return nil, cerrs.ErrRalRequestFail.Errorf("CreateRestoreResponse request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}

func (s *dbrsSdk) QueryBackupRestore(ctx context.Context, req *QueryRestoreRequest) (resp *QueryRestoreResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.DataBackupRestoreServiceID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataBackupRestoreServiceID is empty")
	}

	resp = &QueryRestoreResponse{}

	queryRestoreDetailUri := OperateRestoreUri + "/" + req.DataBackupRestoreServiceID
	queries := map[string]interface{}{
		"dataType": req.DataType,
	}
	if err = s.dbrsRequest(ctx, "QueryRestoreResponse", http.MethodGet,
		queryRestoreDetailUri, queries, nil, resp); err != nil {
		return nil, err
	}
	if resp.DataBackupRestoreServiceID != req.DataBackupRestoreServiceID {
		return nil, cerrs.ErrRalRequestFail.Errorf("QueryRestoreResponse request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}
func (s *dbrsSdk) PrecheckPointInTimeRestore(ctx context.Context, req *PrecheckPointInTimeRestoreRequest) (
	resp *PrecheckPointInTimeRestoreResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is empty")
	}
	if req.ClusterID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param ClusterID is empty")
	}
	if req.RestorePointInDateTime == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param RestorePointInDateTime is null")
	}

	resp = &PrecheckPointInTimeRestoreResponse{}

	if err = s.dbrsRequest(ctx, "PrecheckPointInTimeRestoreResponse", http.MethodPost,
		PrecheckPointInTimeRestoreUri, nil, req, resp); err != nil {
		return nil, err
	}
	if resp.AppID != req.AppID {
		return nil, cerrs.ErrRalRequestFail.Errorf("PrecheckPointInTimeRestoreResponse request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}
func (s *dbrsSdk) QueryPointInTimeRestoreRange(ctx context.Context, req *QueryPointInTimeRestoreRangeRequest) (
	resp *QueryPointInTimeRestoreRangeResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is empty")
	}
	if req.ClusterID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param ClusterID is empty")
	}

	resp = &QueryPointInTimeRestoreRangeResponse{}
	queries := map[string]interface{}{
		"dataType":  req.DataType,
		"appID":     req.AppID,
		"clusterID": req.ClusterID,
	}

	if err = s.dbrsRequest(ctx, "QueryPointInTimeRestoreRangeResponse", http.MethodGet,
		QueryPointInTimeRestoreRangeUri, queries, nil, resp); err != nil {
		return nil, err
	}
	if resp.AppID != req.AppID {
		return nil, cerrs.ErrRalRequestFail.Errorf("QueryPointInTimeRestoreRangeResponse request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}
func (s *dbrsSdk) CreatePointInTimeRestore(ctx context.Context, req *CreatePointInTimeRestoreRequest) (resp *CreatePointInTimeRestoreResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is empty")
	}
	if req.ClusterID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param ClusterID is empty")
	}
	if req.DestInstances == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DestInstances is null")
	}

	resp = &CreatePointInTimeRestoreResponse{}

	if err = s.dbrsRequest(ctx, "CreatePointInTimeRestoreResponse", http.MethodPost,
		OperatePointInTimeRestoreUri, nil, req, resp); err != nil {
		return nil, err
	}
	if resp.AppID != req.AppID {
		return nil, cerrs.ErrRalRequestFail.Errorf("CreateRestoreResponse request fail, req:%s , resp:%s",
			base_utils.Format(req), base_utils.Format(resp))
	}
	return
}

func (s *dbrsSdk) QueryPointInTimeRestore(ctx context.Context, req *QueryPointInTimeRestoreRequest) (resp *QueryPointInTimeRestoreResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.PointInTimeRestoreServiceID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param PointInTimeRestoreServiceID is empty")
	}

	resp = &QueryPointInTimeRestoreResponse{}

	queryRestoreDetailUri := OperatePointInTimeRestoreUri + "/" + req.PointInTimeRestoreServiceID
	queries := map[string]interface{}{
		"dataType": req.DataType,
	}
	if err = s.dbrsRequest(ctx, "QueryPointInTimeRestoreResponse", http.MethodGet,
		queryRestoreDetailUri, queries, nil, resp); err != nil {
		return nil, err
	}
	return
}

func (s *dbrsSdk) QueryBackupUsage(ctx context.Context, req *QueryBackupUsageRequest) (resp *QueryBackupUsageResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is empty")
	}
	if req.DbaasType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DbaasType is empty")
	}

	resp = &QueryBackupUsageResponse{}

	queries := map[string]interface{}{
		"dataType":  req.DataType,
		"appID":     req.AppID,
		"dbaasType": req.DbaasType,
	}
	if err = s.dbrsRequest(ctx, "QueryBackupUsage", http.MethodGet,
		OperateBackupUsageUri, queries, nil, resp); err != nil {
		return nil, err
	}
	return
}
