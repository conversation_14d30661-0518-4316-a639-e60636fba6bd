package dbrs

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const (
	TestAppID                      = "scs-fsh-pnqbzeysdpyv"
	TestClusterID                  = "49601"
	TestDataBackupRestoreServiceID = "1682393863812128701"
)

func TestCreateBackupPolicySdk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := NewBefaultDbrsSdk()
	fmt.Println("case_1")
	req := CreatePolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Sunday", "Monday"},
		DataBackupTime:    "20:21:22Z",
		// DbaasType:         "SCS",
		DataBackupRetainStrategys: []BackupRetainStrategys{
			{
				StartSeconds: 0,
				EndSeconds:   -259200,
				RetainCount:  7,
				Precision:    86400,
			},
		},
	}
	rsp, err := s.CreateBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	s = NewBefaultDbrsSdk()
	rsp, err = s.CreateBackupPolicy(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	req = CreatePolicyRequest{
		DataType: "PegaDB",
	}
	rsp, err = s.CreateBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	req = CreatePolicyRequest{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	}
	rsp, err = s.CreateBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_5")
	req = CreatePolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{},
	}
	rsp, err = s.CreateBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_6")
	req = CreatePolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: nil,
	}
	rsp, err = s.CreateBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_7")
	req = CreatePolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Sunday", "Monday"},
	}
	rsp, err = s.CreateBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_8")
	req = CreatePolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Sunday", "Monday"},
		DataBackupTime:    "20:21:22Z",
	}
	rsp, err = s.CreateBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_9")
	req = CreatePolicyRequest{
		DataType:                  "PegaDB",
		AppID:                     "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay:         []string{"Sunday", "Monday"},
		DataBackupTime:            "20:21:22Z",
		DataBackupRetainStrategys: []BackupRetainStrategys{},
	}
	rsp, err = s.CreateBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_10")
	s = NewDbrsSdk("dbrs")
	rsp, err = s.CreateBackupPolicy(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_11")
	req = CreatePolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Sunday", "Monday"},
		DataBackupTime:    "20:21:22Z",
		// DbaasType:         "SCS",
		DataBackupRetainStrategys: []BackupRetainStrategys{
			{
				StartSeconds: 0,
				EndSeconds:   -259200,
				RetainCount:  7,
				Precision:    86400,
			},
		},
		DbaasType: "public-scs",
	}
	rsp, err = s.CreateBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_12")
	req = CreatePolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Sunday", "Monday"},
		DataBackupTime:    "20:21:22Z",
		// DbaasType:         "SCS",
		DataBackupRetainStrategys: []BackupRetainStrategys{
			{
				StartSeconds: 0,
				EndSeconds:   -259200,
				RetainCount:  7,
				Precision:    86400,
			},
		},
		DbaasType:    "public-scs",
		DataStorages: []DataStoragesConf{},
	}
	rsp, err = s.CreateBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_13")
	req = CreatePolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Sunday", "Monday"},
		DataBackupTime:    "20:21:22Z",
		// DbaasType:         "SCS",
		DataBackupRetainStrategys: []BackupRetainStrategys{
			{
				StartSeconds: 0,
				EndSeconds:   -259200,
				RetainCount:  7,
				Precision:    86400,
			},
		},
		DbaasType: "public-scs",
		DataStorages: []DataStoragesConf{
			{
				Type:     "bos",
				BucketId: "scs-backup-rdb-bucket-test",
			},
		},
	}
	rsp, err = s.CreateBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_14")
	req = CreatePolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Sunday", "Monday"},
		DataBackupTime:    "20:21:22Z",
		// DbaasType:         "SCS",
		DataBackupRetainStrategys: []BackupRetainStrategys{
			{
				StartSeconds: 0,
				EndSeconds:   -259200,
				RetainCount:  7,
				Precision:    86400,
			},
		},
		DbaasType: "public-scs",
		DataStorages: []DataStoragesConf{
			{
				Type:     "bos",
				BucketId: "scs-backup-rdb-bucket-test",
			},
		},
		Region: "bj",
	}
	rsp, err = s.CreateBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestDeleteBackupPolicySdk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := NewBefaultDbrsSdk()

	fmt.Println("case_1")
	req := DeletePolicyRequest{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	}
	rsp, err := s.DeleteBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = s.DeleteBackupPolicy(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	req = DeletePolicyRequest{}
	rsp, err = s.DeleteBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	req = DeletePolicyRequest{
		DataType: "PegaDB",
	}
	rsp, err = s.DeleteBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_5")
	req = DeletePolicyRequest{
		DataType:  "PegaDB",
		AppID:     "scs-fsh-pnqbzeysdpyv",
		DbaasType: "public-scs",
	}
	rsp, err = s.DeleteBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestQueryBackupPolicySdk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := NewBefaultDbrsSdk()

	fmt.Println("case_1")
	req := QueryPolicyRequest{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	}
	rsp, err := s.QueryBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = s.QueryBackupPolicy(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	req = QueryPolicyRequest{}
	rsp, err = s.QueryBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	req = QueryPolicyRequest{
		DataType: "PegaDB",
	}
	rsp, err = s.QueryBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_5")
	req = QueryPolicyRequest{
		DataType:  "PegaDB",
		AppID:     "scs-fsh-pnqbzeysdpyv",
		DbaasType: "public-scs",
	}
	rsp, err = s.QueryBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestModifyBackupPolicySdk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := NewBefaultDbrsSdk()
	fmt.Println("case_1")
	req := CommonPolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Sunday", "Monday"},
		DataBackupTime:    "20:21:22Z",
		DataBackupRetainStrategys: []BackupRetainStrategys{
			{
				StartSeconds: 0,
				EndSeconds:   -259200,
				RetainCount:  7,
				Precision:    86400,
			},
		},
	}
	rsp, err := s.ModifyBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = s.ModifyBackupPolicy(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	req = CommonPolicyRequest{
		DataType: "PegaDB",
	}
	rsp, err = s.ModifyBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	req = CommonPolicyRequest{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	}
	rsp, err = s.ModifyBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_5")
	req = CommonPolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{},
	}
	rsp, err = s.ModifyBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_6")
	req = CommonPolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: nil,
	}
	rsp, err = s.ModifyBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_7")
	req = CommonPolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Sunday", "Monday"},
	}
	rsp, err = s.ModifyBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_8")
	req = CommonPolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Sunday", "Monday"},
		DataBackupTime:    "20:21:22Z",
	}
	rsp, err = s.ModifyBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_9")
	req = CommonPolicyRequest{
		DataType:                  "PegaDB",
		AppID:                     "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay:         []string{"Sunday", "Monday"},
		DataBackupTime:            "20:21:22Z",
		DataBackupRetainStrategys: []BackupRetainStrategys{},
	}
	rsp, err = s.ModifyBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_10")
	req = CommonPolicyRequest{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Sunday", "Monday"},
		DataBackupTime:    "20:21:22Z",
		DataBackupRetainStrategys: []BackupRetainStrategys{
			{
				StartSeconds: 0,
				EndSeconds:   -259200,
				RetainCount:  7,
				Precision:    86400,
			},
		},
		DbaasType: "public-scs",
	}
	rsp, err = s.ModifyBackupPolicy(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestQueryBackupListSdk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := NewBefaultDbrsSdk()

	fmt.Println("case_1")
	req := QueryBackupListRequest{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	}
	rsp, err := s.QueryBackupList(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = s.QueryBackupList(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	req = QueryBackupListRequest{}
	rsp, err = s.QueryBackupList(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	req = QueryBackupListRequest{
		DataType: "PegaDB",
	}
	rsp, err = s.QueryBackupList(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_5")
	req = QueryBackupListRequest{
		DataType:  "PegaDB",
		AppID:     "scs-fsh-pnqbzeysdpyv",
		DbaasType: "public-scs",
	}
	rsp, err = s.QueryBackupList(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestCreateBackupTaskSdk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := NewBefaultDbrsSdk()

	fmt.Println("case_1")
	req := CreateBackupTaskRequest{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
		Mode:     "manual",
	}
	rsp, err := s.CreateBackupTask(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = s.CreateBackupTask(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	req = CreateBackupTaskRequest{}
	rsp, err = s.CreateBackupTask(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	req = CreateBackupTaskRequest{
		DataType: "PegaDB",
	}
	rsp, err = s.CreateBackupTask(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_5")
	req = CreateBackupTaskRequest{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	}
	rsp, err = s.CreateBackupTask(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_6")
	req = CreateBackupTaskRequest{
		DataType:  "PegaDB",
		AppID:     "scs-fsh-pnqbzeysdpyv",
		Mode:      "manual",
		DbaasType: "public-scs",
	}
	rsp, err = s.CreateBackupTask(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestDeleteBackupRecordSdk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := NewBefaultDbrsSdk()

	fmt.Println("case_1")
	req := DeleteBackupRecordRequest{
		DataType:        "PegaDB",
		AppID:           "scs-fsh-pnqbzeysdpyv",
		AppDataBackupID: "1686212002154871920",
	}
	rsp, err := s.DeleteBackupRecord(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = s.DeleteBackupRecord(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	req = DeleteBackupRecordRequest{}
	rsp, err = s.DeleteBackupRecord(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	req = DeleteBackupRecordRequest{
		DataType: "PegaDB",
	}
	rsp, err = s.DeleteBackupRecord(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_5")
	req = DeleteBackupRecordRequest{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	}
	rsp, err = s.DeleteBackupRecord(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_6")
	req = DeleteBackupRecordRequest{
		DataType:        "PegaDB",
		AppID:           "scs-fsh-pnqbzeysdpyv",
		AppDataBackupID: "1686212002154871920xx",
	}
	rsp, err = s.DeleteBackupRecord(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_7")
	req = DeleteBackupRecordRequest{
		DataType:        "PegaDB",
		AppID:           "scs-fsh-pnqbzeysdpyv",
		AppDataBackupID: "1686212002154871920",
		DbaasType:       "public-scs",
	}
	rsp, err = s.DeleteBackupRecord(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestQueryAppBackupDetailByAppBackupIdSdk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := NewBefaultDbrsSdk()

	fmt.Println("case_1")
	rsp, err := s.QueryAppBackupDetailByAppBackupId(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	req := QueryAppBackupDetailRequest{}
	rsp, err = s.QueryAppBackupDetailByAppBackupId(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	req = QueryAppBackupDetailRequest{
		DataType: "PegaDB",
	}
	rsp, err = s.QueryAppBackupDetailByAppBackupId(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	req = QueryAppBackupDetailRequest{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	}
	rsp, err = s.QueryAppBackupDetailByAppBackupId(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_5")
	req = QueryAppBackupDetailRequest{
		DataType:        "PegaDB",
		AppID:           "scs-fsh-pnqbzeysdpyv",
		AppDataBackupID: "1686212002154871920xx",
	}
	rsp, err = s.QueryAppBackupDetailByAppBackupId(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_6")
	req = QueryAppBackupDetailRequest{
		DataType:             "PegaDB",
		AppID:                "scs-fsh-pnqbzeysdpyv",
		AppDataBackupID:      "1686212002154871920xx",
		DownloadUrlExpireSec: 86400,
	}
	rsp, err = s.QueryAppBackupDetailByAppBackupId(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_7")
	req = QueryAppBackupDetailRequest{
		DataType:             "PegaDB",
		AppID:                "scs-fsh-pnqbzeysdpyv",
		AppDataBackupID:      "1686212002154871920xx",
		DownloadUrlExpireSec: 86400,
		Marker:               0,
		MaxKeys:              10,
	}
	rsp, err = s.QueryAppBackupDetailByAppBackupId(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_8")
	req = QueryAppBackupDetailRequest{
		DataType:             "PegaDB",
		AppID:                "scs-fsh-pnqbzeysdpyv",
		AppDataBackupID:      "1686212002154871920xx",
		DownloadUrlExpireSec: 86400,
		Marker:               0,
		MaxKeys:              10,
		DbaasType:            "public-scs",
	}
	rsp, err = s.QueryAppBackupDetailByAppBackupId(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestQueryBackupDetailByBackupIdSdk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := NewBefaultDbrsSdk()

	fmt.Println("case_1")
	rsp, err := s.QueryBackupDetailByBackupId(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	req := QueryShardBackupDetailRequest{}
	rsp, err = s.QueryBackupDetailByBackupId(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	req = QueryShardBackupDetailRequest{
		DataType: "PegaDB",
	}
	rsp, err = s.QueryBackupDetailByBackupId(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	req = QueryShardBackupDetailRequest{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	}
	rsp, err = s.QueryBackupDetailByBackupId(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_5")
	req = QueryShardBackupDetailRequest{
		DataType:  "PegaDB",
		AppID:     "scs-fsh-pnqbzeysdpyv",
		ClusterID: "12345",
	}
	rsp, err = s.QueryBackupDetailByBackupId(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_6")
	req = QueryShardBackupDetailRequest{
		DataType:     "PegaDB",
		AppID:        "scs-fsh-pnqbzeysdpyv",
		ClusterID:    "12345",
		DatabackupID: "12344",
	}
	rsp, err = s.QueryBackupDetailByBackupId(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_7")
	req = QueryShardBackupDetailRequest{
		DataType:             "PegaDB",
		AppID:                "scs-fsh-pnqbzeysdpyv",
		ClusterID:            "12345",
		DatabackupID:         "12344",
		DownloadUrlExpireSec: 86400,
	}
	rsp, err = s.QueryBackupDetailByBackupId(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_8")
	req = QueryShardBackupDetailRequest{
		DataType:             "PegaDB",
		AppID:                "scs-fsh-pnqbzeysdpyv",
		ClusterID:            "12345",
		DatabackupID:         "12344",
		DownloadUrlExpireSec: 86400,
		DbaasType:            "public-scs",
	}
	rsp, err = s.QueryBackupDetailByBackupId(ctx, &req)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestModifyBackupEncryptPolicyOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDbrsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CommonPolicyResponse{
		AppID: TestAppID,
	}, nil, http.StatusOK))

	_, err := s.ModifyBackupEncryptPolicy(ctx, &ModifyEncryptPolicyRequest{
		DataType: "Redis",
		AppID:    TestAppID,
		EncryptStrategy: &EncryptStrategy{
			EncryptEnable:     true,
			KeyManagementType: "self_kms",
		},
	})
	if err != nil {
		t.Errorf("[%s] test modify backup encrypt policy fail", t.Name())
		return
	}
}

func TestModifyBackupEncryptPolicyErrInvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDbrsSdk(DefaultServiceName)

	// req param is null
	_, err := s.ModifyBackupEncryptPolicy(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// req param DataType is empty
	_, err = s.ModifyBackupEncryptPolicy(ctx, &ModifyEncryptPolicyRequest{})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test check param DataType fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// req param AppID is empty
	_, err = s.ModifyBackupEncryptPolicy(ctx, &ModifyEncryptPolicyRequest{
		DataType: "Redis",
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test check param AppID fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// req param EncryptStrategy is null
	_, err = s.ModifyBackupEncryptPolicy(ctx, &ModifyEncryptPolicyRequest{
		DataType: "Redis",
		AppID:    TestAppID,
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test check param EncryptStrategy fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}

func TestCreateBackupRestoreOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDbrsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CreateRestoreResponse{
		AppID: TestAppID,
	}, nil, http.StatusOK))

	createRequest := CreateRestoreRequest{
		DataType:              "Redis",
		AppID:                 TestAppID,
		ClusterID:             TestClusterID,
		Name:                  "data backup restore",
		DataBackupID:          "1718610686873522801",
		PerformanceFlavorID:   "middle",
		SpeedLimitBytesPerSec: 536870912,
	}
	createRequest.DestInstances = append(createRequest.DestInstances, DestInstance{
		AgentPoolName:  "public-scs",
		InstanceID:     "xxxuuid",
		XagentHost:     "127.0.0.1",
		XagentPort:     8500,
		AgentHost:      "127.0.0.1",
		AgentPort:      8995,
		AgentLocalPort: 8995,
		DatabaseHost:   "127.0.0.1",
		DatabasePort:   6379,
		DatabasePath:   "/home/<USER>/dbrs/database/redis_1",
	})
	_, err := s.CreateBackupRestore(ctx, &createRequest)
	if err != nil {
		t.Errorf("[%s] test create backup restore fail", t.Name())
		return
	}
}

func TestCreateBackupRestoreErrInvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDbrsSdk(DefaultServiceName)

	// req param is null
	_, err := s.CreateBackupRestore(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// req param DataType is empty
	_, err = s.CreateBackupRestore(ctx, &CreateRestoreRequest{})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test check param DataType fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// req param AppID is empty
	_, err = s.CreateBackupRestore(ctx, &CreateRestoreRequest{
		DataType: "Redis",
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test check param AppID fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// req param ClusterID is empty
	_, err = s.CreateBackupRestore(ctx, &CreateRestoreRequest{
		DataType: "Redis",
		AppID:    TestAppID,
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test check param ClusterID fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// req param Destinstances is null
	_, err = s.CreateBackupRestore(ctx, &CreateRestoreRequest{
		DataType:  "Redis",
		AppID:     TestAppID,
		ClusterID: TestClusterID,
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test check param Destinstances fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}

func TestQueryBackupRestoreOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDbrsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&QueryRestoreResponse{
		DataBackupRestoreServiceID: TestDataBackupRestoreServiceID,
	}, nil, http.StatusOK))

	queryRequest := QueryRestoreRequest{
		DataType:                   "Redis",
		DataBackupRestoreServiceID: TestDataBackupRestoreServiceID,
	}
	_, err := s.QueryBackupRestore(ctx, &queryRequest)
	if err != nil {
		t.Errorf("[%s] test query backup restore fail", t.Name())
		return
	}
}

func TestQueryBackupRestoreErrInvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDbrsSdk(DefaultServiceName)

	// req param is null
	_, err := s.QueryBackupRestore(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// req param DataType is empty
	_, err = s.QueryBackupRestore(ctx, &QueryRestoreRequest{})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test check param DataType fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// req param DataBackupRestoreServiceID is empty
	_, err = s.QueryBackupRestore(ctx, &QueryRestoreRequest{
		DataType: "Redis",
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test check param DataBackupRestoreServiceID fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}

func TestQueryBackupUsageOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDbrsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&QueryBackupUsageResponse{}, nil, http.StatusOK))

	queryRequest := QueryBackupUsageRequest{
		DataType:  "Redis",
		AppID:     "scs-bj-xxxx",
		DbaasType: "public-scs",
	}
	_, err := s.QueryBackupUsage(ctx, &queryRequest)
	if err != nil {
		t.Errorf("[%s] test query backup usage fail", t.Name())
		return
	}
}

func TestQueryBackupUsageErrInvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newDbrsSdk(DefaultServiceName)

	// req param is null
	_, err := s.QueryBackupUsage(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// req param DataType is empty
	_, err = s.QueryBackupUsage(ctx, &QueryBackupUsageRequest{})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test check param DataType fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// req param AppID is empty
	_, err = s.QueryBackupUsage(ctx, &QueryBackupUsageRequest{
		DataType: "Redis",
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test check param AppID fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}
