package dbrs

import "context"

type CreatePolicyRequest struct {
	DataType                  string                  `json:"dataType"` // 例：PegaDB
	AppID                     string                  `json:"appID"`
	DataBackupWeekDay         []string                `json:"dataBackupWeekDay"`
	DataBackupTime            string                  `json:"dataBackupTime"` // 备份开始时间，UTC格式
	DataBackupRetainStrategys []BackupRetainStrategys `json:"dataBackupRetainStrategys"`
	DbaasType                 string                  `json:"dbaasType"`
	DataStorages              []DataStoragesConf      `json:"dataStorages"`
	Region                    string                  `json:"region"`

	PoolName              string                  `json:"poolName"`   // 相比于 RDB 备份新增的参数
	PolicyType            string                  `json:"policyType"` // 策略类型：logical_log_backup（逻辑日志备份）
	PolicyName            string                  `json:"policyName"` // 策略名称
	BackupStorages        []DataStoragesConf      `json:"backupStorages"`
	BackupRetainStrategys []BackupRetainStrategys `json:"backupRetainStrategys"`
}

type DataStoragesConf struct {
	Type     string `json:"type"` // "bos"
	BucketId string `json:"id"`
}

type CommonPolicyRequest struct {
	DataType                  string                  `json:"dataType"` // 例：PegaDB
	AppID                     string                  `json:"appID"`
	DataBackupWeekDay         []string                `json:"dataBackupWeekDay"`
	DataBackupTime            string                  `json:"dataBackupTime"` // 备份开始时间，UTC格式
	DataBackupRetainStrategys []BackupRetainStrategys `json:"dataBackupRetainStrategys"`
	DbaasType                 string                  `json:"dbaasType"`
}

type BackupRetainStrategys struct {
	StartSeconds int64 `json:"startSeconds"`
	EndSeconds   int64 `json:"endSeconds"`
	RetainCount  int   `json:"retainCount"`
	Precision    int64 `json:"precision"`
}

type CommonPolicyResponse struct {
	DataType string `json:"dataType"` // 例：PegaDB
	AppID    string `json:"appID"`
}

type DeletePolicyRequest struct {
	DataType   string `json:"dataType"` // 例：PegaDB
	AppID      string `json:"appID"`
	DbaasType  string `json:"dbaasType"`
	PolicyType string `json:"policyType"` // 策略类型：logical_log_backup（逻辑日志备份）, 默认是 RDB/快照备份
}

type QueryPolicyRequest struct {
	DataType   string `json:"dataType"` // 例：PegaDB
	AppID      string `json:"appID"`
	Marker     string `json:"marker"`
	MaxKeys    string `json:"maxKeys"`
	DbaasType  string `json:"dbaasType"`
	PolicyType string `json:"policyType"` // 策略类型：logical_log_backup（逻辑日志备份）, 默认是 RDB/快照备份
}

type QueryPolicyResponse struct {
	AppID    string         `json:"appID"`
	DataType string         `json:"dataType"` // 例：PegaDB
	Policys  []BackupPolicy `json:"policys"`
}

type BackupPolicy struct {
	DataBackupRetainStrategys []BackupRetainStrategys `json:"dataBackupRetainStrategys"` // 备份 RDB 策略
	DataBackupTime            string                  `json:"dataBackupTime"`            // 备份开始时间，UTC格式
	DataBackupWeekDay         []string                `json:"dataBackupWeekDay"`
	BackupStorages            []DataStoragesConf      `json:"backupStorages"`        // 备份 AOF storage 信息
	BackupRetainStrategys     []BackupRetainStrategys `json:"backupRetainStrategys"` // 备份 AOF 策略
}

type QueryBackupListRequest struct {
	DataType  string `json:"dataType"` // 例：PegaDB
	AppID     string `json:"appID"`
	Marker    int    `json:"marker"`
	MaxKeys   int    `json:"maxKeys"`
	DbaasType string `json:"dbaasType"`
}

type QueryBackupListResponse struct {
	DataType        string               `json:"dataType"` // 例：PegaDB
	AppID           string               `json:"appID"`
	ClusterID       string               `json:"clusterID"`
	ModeManualCount int                  `json:"modeManualCount"`
	AppDataBackups  []AppDataBackupList  `json:"appDataBackups"`
	Pagination      PaginationDefinition `json:"pagination"`
}

type AppDataBackupList struct {
	AppDataBackupID    string                  `json:"appDataBackupID"`
	ClusterDataBackups []ClusterDataBackupList `json:"clusterDataBackups"`
	Method             string                  `json:"method"`
	Mode               string                  `json:"mode"`
	Type               string                  `json:"type"`
}

type ClusterDataBackupList struct {
	AppID               string `json:"appID"`
	BackupSize          string `json:"backupSize"`
	BackupSizeBytes     int64  `json:"backupSizeBytes"`
	ClusterID           string `json:"clusterID"`
	DataBackupID        string `json:"dataBackupID"`
	DataDateTime        string `json:"dataDateTime"`
	DataSize            string `json:"dataSize"`
	DbVersion           string `json:"dbVersion"`
	DstStorageAccountID string `json:"dstStorageAccountID"`
	DstStorageObjectID  string `json:"dstStorageObjectID"`
	Duration            string `json:"duration"`
	EndDateTime         string `json:"endDateTime"`
	Id                  int64  `json:"id"`
	KeyringFileData     string `json:"keyringFileData"`
	Method              string `json:"method"`
	Mode                string `json:"mode"`
	Name                string `json:"name"`
	StartDateTime       string `json:"startDateTime"`
	Status              string `json:"status"`
	Type                string `json:"type"`
}

type PaginationDefinition struct {
	IsTruncated bool   `json:"isTruncated"`
	Marker      string `json:"marker"`
	MaxKeys     int    `json:"maxKeys"`
	NextMarker  string `json:"nextMarker"`
	TotalKeys   int    `json:"totalKeys"`
}

type CreateBackupTaskRequest struct {
	DataType  string `json:"dataType"` // 例：PegaDB
	AppID     string `json:"appID"`
	Mode      string `json:"mode"` // app数据备份模式：automatic（自动）、manual（手动）
	DbaasType string `json:"dbaasType"`
	Product   string `json:"product"`
}

type CreateBackupTaskResponse struct {
	DataType        string `json:"dataType"` // 例：PegaDB
	AppID           string `json:"appID"`
	AppDataBackupID string `json:"appDataBackupID"`
}

type DeleteBackupRecordRequest struct {
	DataType        string `json:"dataType"` // 例：PegaDB
	AppID           string `json:"appID"`
	AppDataBackupID string `json:"appDataBackupID"`
	DbaasType       string `json:"dbaasType"`
	Product         string `json:"product"`
}

type DeleteBackupRecordResponse struct {
	DataType        string `json:"dataType"` // 例：PegaDB
	AppID           string `json:"appID"`
	AppDataBackupID string `json:"appDataBackupID"`
}

type QueryAppBackupDetailRequest struct {
	DataType             string `json:"dataType"` // 例：PegaDB
	AppID                string `json:"appID"`
	AppDataBackupID      string `json:"appDataBackupID"`
	DownloadUrlExpireSec int64  `json:"downloadUrlExpireSec"` // 默认值：43200
	Marker               int    `json:"marker"`               // 默认值：0
	MaxKeys              int    `json:"maxKeys"`              // 默认值：1000
	DbaasType            string `json:"dbaasType"`
}

type QueryAppBackupDetailResponse struct {
	DataType           string               `json:"dataType"` // 例：PegaDB
	AppDataBackupID    string               `json:"appDataBackupID"`
	AppDataBackupShows []AppDataBackupShow  `json:"appDataBackupShows"`
	Pagination         PaginationDefinition `json:"pagination"`
}

type AppDataBackupShow struct {
	DataBackupID        string   `json:"dataBackupID"`
	AppID               string   `json:"appID"`
	ClusterID           string   `json:"clusterID"`
	Name                string   `json:"name"`
	Status              string   `json:"status"`
	Method              string   `json:"method"`
	Mode                string   `json:"mode"`
	StartDateTime       string   `json:"startDateTime"`
	EndDateTime         string   `json:"endDateTime"`
	Duration            string   `json:"duration"`
	DataDateTime        string   `json:"dataDateTime"`
	BackupSize          string   `json:"backupSize"`
	BackupSizeBytes     int64    `json:"backupSizeBytes"`
	DataSize            string   `json:"dataSize"`
	InnerLinks          []string `json:"innerLinks"`
	OuterLinks          []string `json:"outerLinks"`
	DstStorageAccountID string   `json:"dstStorageAccountID"`
	DstStorageObjectID  string   `json:"dstStorageObjectID"`
}

type QueryShardBackupDetailRequest struct {
	DataType             string `json:"dataType"` // 例：PegaDB
	AppID                string `json:"appID"`
	ClusterID            string `json:"clusterID"` // SCS shard_id
	DatabackupID         string `json:"databackupID"`
	DownloadUrlExpireSec int64  `json:"downloadUrlExpireSec"` // 默认值：43200
	DbaasType            string `json:"dbaasType"`
}

type QueryShardBackupDetailResponse struct {
	DataType        string               `json:"dataType"`
	DataBackupID    string               `json:"dataBackupID"`
	AppID           string               `json:"appID"`
	ClusterID       string               `json:"clusterID"`
	Name            string               `json:"name"`
	Status          string               `json:"status"`
	Method          string               `json:"method"`
	Mode            string               `json:"mode"`
	StartDateTime   string               `json:"startDateTime"`
	EndDateTime     string               `json:"endDateTime"`
	Duration        string               `json:"duration"`
	DataDateTime    string               `json:"dataDateTime"`
	BackupSize      string               `json:"backupSize"`
	BackupSizeBytes int64                `json:"backupSizeBytes"`
	DataSize        string               `json:"dataSize"`
	InnerLinks      []string             `json:"innerLinks"`
	OuterLinks      []string             `json:"outerLinks"`
	Pagination      PaginationDefinition `json:"pagination"`
}

type EncryptStrategy struct {
	EncryptEnable            bool   `json:"encryptEnable"`
	KeyManagementType        string `json:"keyManagementType"` // 密钥管理方式：自托管(self_kms)、百度KMS(baidu_kms)
	KeyManagementServiceName string `json:"keyManagementServiceName,omitempty"`
	KecretKeyID              string `json:"secretKeyID,omitempty"`
}

type ModifyEncryptPolicyRequest struct {
	DataType        string           `json:"dataType"` // 例：PegaDB
	AppID           string           `json:"appID"`
	EncryptStrategy *EncryptStrategy `json:"encryptStrategy"`
}

type DestInstance struct {
	AgentPoolName  string `json:"agentPoolName"`
	Region         string `json:"region"`
	InstanceID     string `json:"instanceID"`
	XagentHost     string `json:"xagentHost"`
	XagentPort     int    `json:"xagentPort"`
	AgentHost      string `json:"agentHost"`
	AgentPort      int    `json:"agentPort"`
	AgentLocalPort int    `json:"agentLocalPort"`
	DatabaseHost   string `json:"databaseHost"`
	DatabasePort   int    `json:"databasePort"`
	DatabasePath   string `json:"databasePath"`
}

type CreateRestoreRequest struct {
	DataType              string         `json:"dataType"`
	AppID                 string         `json:"appID"`
	ClusterID             string         `json:"clusterID"`
	Name                  string         `json:"name"`
	DataBackupID          string         `json:"dataBackupID"`
	DestInstances         []DestInstance `json:"destInstances"`
	PerformanceFlavorID   string         `json:"performanceFlavorID"`
	SpeedLimitBytesPerSec int64          `json:"speedLimitBytesPerSec"`
}

type CreateRestoreResponse struct {
	DataType                   string `json:"dataType"`
	AppID                      string `json:"appID"`
	ClusterID                  string `json:"clusterID"`
	DataBackupRestoreServiceID string `json:"dataBackupRestoreServiceID"`
}

type QueryRestoreRequest struct {
	DataType                   string `json:"dataType"`
	DataBackupRestoreServiceID string `json:"dataBackupRestoreServiceID"`
}

type QueryRestoreResponse struct {
	AppID                      string `json:"appID"`
	ClusterID                  string `json:"clusterID"`
	CreateDateTime             string `json:"createDateTime"`
	DataBackupRestoreServiceID string `json:"dataBackupRestoreServiceID"`
	DataType                   string `json:"dataType"`
	Name                       string `json:"name"`
	Status                     string `json:"status"`
}

// ------------------------------------时间点恢复相关参数
type PrecheckPointInTimeRestoreRequest struct {
	DataType               string `json:"dataType"`
	AppID                  string `json:"appID"`
	ClusterID              string `json:"clusterID"`
	RestorePointInDateTime string `json:"restorePointInDateTime"`
}

type PrecheckPointInTimeRestoreResponse struct {
	DataType                  string `json:"dataType"`
	AppID                     string `json:"appID"`
	ClusterID                 string `json:"clusterID"`
	Recoverable               bool   `json:"recoverable"`
	RecoverableDateTimeBefore string `json:"recoverableDateTimeBefore"`
	Message                   string `json:"message"`
}
type QueryPointInTimeRestoreRangeRequest struct {
	DataType  string `json:"dataType"`
	AppID     string `json:"appID"`
	ClusterID string `json:"clusterID"`
}

type RecoverableDateTime struct {
	StartDateTime string `json:"startDateTime"`
	EndDateTime   string `json:"endDateTime"`
}

type QueryPointInTimeRestoreRangeResponse struct {
	DataType                  string                `json:"dataType"`
	AppID                     string                `json:"appID"`
	ClusterID                 string                `json:"clusterID"`
	LatestRecoverableDateTime string                `json:"latestRecoverableDateTime"`
	RecoverableDateTimes      []RecoverableDateTime `json:"recoverableDateTimes"`
}
type CreatePointInTimeRestoreRequest struct {
	DataType               string         `json:"dataType"`
	AppID                  string         `json:"appID"`
	ClusterID              string         `json:"clusterID"`
	Name                   string         `json:"name"`
	RestorePointInDateTime string         `json:"restorePointInDateTime"`
	DestInstances          []DestInstance `json:"destInstances"`
	PerformanceFlavorID    string         `json:"performanceFlavorID"`
	SpeedLimitBytesPerSec  int64          `json:"speedLimitBytesPerSec"`
}

type CreatePointInTimeRestoreResponse struct {
	DataType                    string `json:"dataType"`
	AppID                       string `json:"appID"`
	ClusterID                   string `json:"clusterID"`
	PointInTimeRestoreServiceID string `json:"pointInTimeRestoreServiceID"`
}

type QueryPointInTimeRestoreRequest struct {
	DataType                    string `json:"dataType"`
	PointInTimeRestoreServiceID string `json:"pointInTimeRestoreServiceID"`
}

type QueryPointInTimeRestoreResponse struct {
	AppID                       string `json:"appID"`
	ClusterID                   string `json:"clusterID"`
	CreateDateTime              string `json:"createDateTime"`
	PointInTimeRestoreServiceID string `json:"pointInTimeRestoreServiceID"`
	DataType                    string `json:"dataType"`
	Name                        string `json:"name"`
	Status                      string `json:"status"`
}

type QueryBackupUsageRequest struct {
	DataType  string `json:"dataType"`
	AppID     string `json:"appID"`
	DbaasType string `json:"dbaasType"`
}

type QueryBackupUsageResponse struct {
	DataType                           string `json:"dataType"`
	AppID                              string `json:"appID"`
	ClusterID                          string `json:"clusterID"`
	LogicalLogBackupSizeBytes          int    `json:"logicalLogBackupSizeBytes"`        // 逻辑日志备份总大小Bytes（备份到bos的rdb/aof）
	LogicalLogBackupBillingSizeBytes   int    `json:"logicalLogBackupBillingSizeBytes"` // 逻辑日志备份计费大小Bytes（备份到bos的rdb/aof）
	PhysicalLogBackupSizeBytes         int    `json:"physicalLogBackupSizeBytes"`
	PhysicalLogBackupBillingSizeBytes  int    `json:"physicalLogBackupBillingSizeBytes"`
	LogicalDataBackupSizeBytes         int    `json:"logicalDataBackupSizeBytes"`
	LogicalDataBackupBillingSizeBytes  int    `json:"logicalDataBackupBillingSizeBytes"`
	PhysicalDataBackupSizeBytes        int    `json:"physicalDataBackupSizeBytes"`        // 物理数据备份总大小Bytes（备份到bos的rdb）
	PhysicalDataBackupBillingSizeBytes int    `json:"physicalDataBackupBillingSizeBytes"` // 物理数据备份计费大小Bytes（备份到bos的rdb）
	SnapshotDataBackupSizeBytes        int    `json:"snapshotDataBackupSizeBytes"`
	SnapshotDataBackupBillingSizeBytes int    `json:"snapshotDataBackupBillingSizeBytes"`
}

type DbrsService interface {
	CreateBackupPolicy(ctx context.Context, req *CreatePolicyRequest) (rsp *CommonPolicyResponse, err error)
	DeleteBackupPolicy(ctx context.Context, req *DeletePolicyRequest) (resp *CommonPolicyResponse, err error)
	QueryBackupPolicy(ctx context.Context, req *QueryPolicyRequest) (resp *QueryPolicyResponse, err error)
	ModifyBackupPolicy(ctx context.Context, req *CommonPolicyRequest) (resp *CommonPolicyResponse, err error)
	QueryBackupList(ctx context.Context, req *QueryBackupListRequest) (resp *QueryBackupListResponse, err error)
	CreateBackupTask(ctx context.Context, req *CreateBackupTaskRequest) (resp *CreateBackupTaskResponse, err error)
	DeleteBackupRecord(ctx context.Context, req *DeleteBackupRecordRequest) (resp *DeleteBackupRecordResponse, err error)
	QueryAppBackupDetailByAppBackupId(ctx context.Context, req *QueryAppBackupDetailRequest) (resp *QueryAppBackupDetailResponse, err error)
	QueryBackupDetailByBackupId(ctx context.Context, req *QueryShardBackupDetailRequest) (resp *QueryShardBackupDetailResponse, err error)
	ModifyBackupEncryptPolicy(ctx context.Context, req *ModifyEncryptPolicyRequest) (resp *CommonPolicyResponse, err error)
	CreateBackupRestore(ctx context.Context, req *CreateRestoreRequest) (rsp *CreateRestoreResponse, err error)
	QueryBackupRestore(ctx context.Context, req *QueryRestoreRequest) (rsp *QueryRestoreResponse, err error)
	PrecheckPointInTimeRestore(ctx context.Context, req *PrecheckPointInTimeRestoreRequest) (rsp *PrecheckPointInTimeRestoreResponse, err error)
	QueryPointInTimeRestoreRange(ctx context.Context, req *QueryPointInTimeRestoreRangeRequest) (rsp *QueryPointInTimeRestoreRangeResponse, err error)
	CreatePointInTimeRestore(ctx context.Context, req *CreatePointInTimeRestoreRequest) (rsp *CreatePointInTimeRestoreResponse, err error)
	QueryPointInTimeRestore(ctx context.Context, req *QueryPointInTimeRestoreRequest) (rsp *QueryPointInTimeRestoreResponse, err error)
	QueryBackupUsage(ctx context.Context, req *QueryBackupUsageRequest) (rsp *QueryBackupUsageResponse, err error)
}
