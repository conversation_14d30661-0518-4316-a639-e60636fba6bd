/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-16
 * File: ral_conf.go
 */

/*
 * DESCRIPTION
 *   ral方法的对象化封装，便于ut
 */

// Package sdk_utils
package sdk_utils

import (
	"context"
)

// RalCaller - ral caller
type RalCaller interface {
	// WithOption - 设置之后ral请求的option
	WithOption(opts ...ROption) RalCaller

	// HttpRequest - 请求ral
	HttpRequest(ctx context.Context, method, path string, queries, posts, headers, result interface{}) error

	// HttpRequestEx - 请求ral，并返回http response
	HttpRequestEx(ctx context.Context, method, path string, queries, posts, headers, result interface{}) (*HttpResponse, error)

	// HttpRequestRal - 直接请求ral
	HttpRequestWithCustomizeHeader(ctx context.Context, method, path string, queries, posts, headers, result interface{}) (*HttpResponse, error)
}

type ralCaller struct {
	ralName string
	opts    []ROption
	frozen  bool
}

func (caller *ralCaller) WithOption(opts ...ROption) RalCaller {
	opts = append(caller.opts, opts...)
	if caller.frozen {
		newCaller := &ralCaller{
			ralName: caller.ralName,
			opts:    opts,
		}
		return newCaller
	}
	caller.opts = opts
	return caller
}

func (caller *ralCaller) HttpRequest(ctx context.Context, method, path string,
	queries, posts, headers, result interface{}) error {
	return RalHttp(ctx, caller.ralName, method, path, queries, posts, headers, result, caller.opts...)
}

func (caller *ralCaller) HttpRequestEx(ctx context.Context, method, path string,
	queries, posts, headers, result interface{}) (*HttpResponse, error) {
	return RalHttpEx(ctx, caller.ralName, method, path, queries, posts, headers, result, caller.opts...)
}

func (caller *ralCaller) HttpRequestWithCustomizeHeader(ctx context.Context, method, path string,
	queries, posts, headers, result interface{}) (*HttpResponse, error) {
	return RalHTTPExWithCustomizeHeader(ctx, caller.ralName, method, path, queries, posts, headers, result, caller.opts...)
}

func NewRalCaller(ralName string, opts ...ROption) RalCaller {
	caller := &ralCaller{
		ralName: ralName,
		opts:    opts,
		frozen:  true,
	}
	return caller
}
