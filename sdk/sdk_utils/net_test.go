/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-07
 * File: net_test.go
 */

/*
 * DESCRIPTION
 *   net test
 */

// Package sdk_utils
package sdk_utils

import (
	"icode.baidu.com/baidu/gdp/net/gaddr"
	"net/http"
	"testing"
)

func TestHttpReqWrapper(t *testing.T) {
	httpReq, _ := http.NewRequest(MethodGet, "http://localhost:8080/", nil)

	req := HttpReqWrapper(httpReq)

	if host := req.GetHost(); host != "localhost:8080" {
		t.Fatalf("get host fail: %s", host)
	}

	req.SetHost("www.baidu.com")
	if host := req.GetHost(); host != "www.baidu.com" {
		t.Fatalf("get host fail: %s", host)
	}

	req.<PERSON>Header("host", "www2.baidu.com")
	if host := req.GetHost(); host != "www2.baidu.com" {
		t.Fatalf("get host fail: %s", host)
	}

}

func TestResolveAddr(t *testing.T) {
	addrs := []interface{}{
		"*******:80",
		gaddr.New("tcp", "*******:80"),
		NewAddr("*******", 80),
		&EndPoint{
			Host: "*******",
			Port: 80,
		},
	}

	for _, addr := range addrs {
		a := ResolveAddr(addr)
		if a.Host() != "*******" || a.Port() != 80 || a.String() != "*******:80" {
			t.Fatalf("resolve addr fail, addr: %+v, host: %s, port: %d, str: %s",
				addr, a.Host(), a.Port(), a.String())
		}
	}
}
