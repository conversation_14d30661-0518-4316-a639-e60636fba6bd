package sdk_utils

//func TestRalHttpEx_Get_RawResult(t *testing.T) {
//	ctx := context.Background()
//	defer TestEnvDefer(ctx)()
//
//	rawResult := ""
//	httpRsp, err := RalHttpEx(ctx, "sts", MethodGet, "/v1/credential",
//		nil, nil, nil, &rawResult,
//	)
//	if err != nil {
//		t.Fatalf("ral error: %s", err)
//	}
//
//	fmt.Println(rawResult)
//	fmt.Println(base_utils.Format(httpRsp))
//}

//func TestRalHttpEx_Get_StructResult(t *testing.T) {
//	ctx := context.Background()
//	defer TestEnvDefer(ctx)()
//
//	stResult := struct {
//		Code    string `json:"code"`
//		Message string `json:"message"`
//	}{}
//	httpRsp, err := RalHttpEx(ctx, "sts", MethodGet, "/v1/credential",
//		nil, nil, nil, &stResult,
//	)
//	if err != nil {
//		t.Fatalf("ral error: %s", err)
//	}
//
//	fmt.Println(base_utils.Format(stResult))
//	fmt.Println(base_utils.Format(httpRsp))
//}

//func TestRalHttpEx_Get_NilResult(t *testing.T) {
//	ctx := context.Background()
//	defer TestEnvDefer(ctx)()
//
//	httpRsp, err := RalHttpEx(ctx, "sts", MethodGet, "/v1/credential",
//		nil, nil, nil, nil,
//	)
//	if err != nil {
//		t.Fatalf("ral error: %s", err)
//	}
//
//	fmt.Println(base_utils.Format(httpRsp))
//}
