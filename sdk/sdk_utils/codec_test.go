package sdk_utils

import (
	"bytes"
	"fmt"
	"io"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func TestJsonEncoder_Encode(t *testing.T) {
	x := struct {
		A int
		B string
	}{
		A: 1,
		B: "2",
	}

	out, _ := JSONEncoder.Encode(x)
	v, _ := io.ReadAll(out)

	fmt.Printf("before: %+v, after: '%s'", x, string(v))
}

func TestNoErrorDecoder_OnCodecError(t *testing.T) {
	x := struct{ A int }{}
	decoder := OnErrorDecoderWrapper(JSONDecoder, func(err error, raw []byte) error {
		fmt.Printf("got codec error: %v, raw: %s\n", err, base_utils.Format(raw))
		return nil
	})
	if err := decoder.Decode(bytes.NewBuffer([]byte(`{A:1}`)), &x); err != nil {
		t.Fatalf("OnErrorDecoderWrapper.Decode invalid bytes, expect success, but got error: %v", err)
	}
}

func TestNoErrorDecoder_DecodeNormal(t *testing.T) {
	x := struct{ A int }{}
	decoder := OnErrorDecoderWrapper(JSONDecoder, nil)
	if err := decoder.Decode(bytes.NewBuffer([]byte(`{"A":1}`)), &x); err != nil {
		t.Fatalf("OnErrorDecoderWrapper.Decode normal bytes, expect success, but got error: %v", err)
	}
	if x.A != 1 {
		t.Fatalf("wrong value, expect: 1, got %v", x.A)
	}
}

func TestIgnoreEmptyDecoder_DecodeEmptyBytes(t *testing.T) {
	x := struct{ A int }{}
	if err := JSONDecoder.Decode(bytes.NewBuffer(nil), &x); err == nil {
		t.Fatalf("JSONDecoder.Decode empty bytes, expect fail, but got nil error")
	} else {
		fmt.Printf("JSONDecoder.Decode empty bytes, got error: %v\n", err)
	}

	if err := IgnoreEmptyDecoderWrapper(JSONDecoder).Decode(bytes.NewBuffer(nil), &x); err != nil {
		t.Fatalf("IgnoreEmptyDecoderWrapper.Decode empty bytes, expect success, but got error: %v", err)
	}
}

func TestIgnoreEmptyDecoder_DecodeNormal(t *testing.T) {
	x := struct{ A int }{}
	if err := IgnoreEmptyDecoderWrapper(JSONDecoder).Decode(bytes.NewBuffer([]byte(`{"A":1}`)), &x); err != nil {
		t.Fatalf("IgnoreEmptyDecoderWrapper.Decode normal bytes, expect success, but got error: %v", err)
	}
	if x.A != 1 {
		t.Fatalf("wrong value, expect: 1, got %v", x.A)
	}
}
