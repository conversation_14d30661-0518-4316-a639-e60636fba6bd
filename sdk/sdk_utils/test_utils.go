/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-07
 * File: debug.go
 */

/*
 * DESCRIPTION
 *   debug
 */

// Package sdk_utils
package sdk_utils

import (
	"context"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"reflect"
	"sync"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/baidu/gdp/net/servicer"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/debug_utils"
)

var initOnce = sync.Once{}

// SetTestEnv - 设置测试环境
func SetTestEnv(ctx context.Context) {
	rootDir := filepath.Join(debug_utils.GetCurFileDir(1), "..", "..")
	rootDir, _ = filepath.Abs(rootDir)

	SetTestEnvWithRootDir(ctx, rootDir)
}

// SetTestEnvWithRootDir - 设置测试环境
func SetTestEnvWithRootDir(ctx context.Context, rootDir string) {
	initOnce.Do(func() {
		// set default env
		env.Default = env.New(env.Option{
			AppName: "test",
			IDC:     "test",
			RunMode: env.RunModeTest,
			RootDir: rootDir,
			DataDir: filepath.Join(rootDir, "data_ut"),
			LogDir:  filepath.Join(rootDir, "log_ut"),
			ConfDir: filepath.Join(rootDir, "conf_ut"),
		})

		// init servicer
		_ = servicer.InitDefault(ctx)

		// load servicer
		pattern := filepath.Join(env.ConfDir(), "servicer", "*.toml")
		servicer.MustLoad(ctx, servicer.LoadOptFilesGlob(pattern, false))

		// init ral
		_ = ral.InitDefault(ctx)
	})
}

// fmt wrapper for ShieldStdout
type formatWrapper struct {
	stdOut *os.File
}

func (f *formatWrapper) Printf(format string, a ...interface{}) (int, error) {
	return fmt.Fprintf(f.stdOut, format, a...)
}

func (f *formatWrapper) Print(a ...interface{}) (int, error) {
	return fmt.Fprint(f.stdOut, a...)
}

func (f *formatWrapper) Println(a ...interface{}) (int, error) {
	return fmt.Fprintln(f.stdOut, a...)
}

// ShieldStdout - 用于测试工具，屏蔽非预期的标准输出
func ShieldStdout() *formatWrapper {
	log.SetOutput(ioutil.Discard)
	fmtWrapper := &formatWrapper{
		stdOut: os.Stdout,
	}
	os.Stdout = nil
	return fmtWrapper
}

// ClearTestLogDir - 清除测试中产生的log
func ClearTestLogDir() {
	clearTestLogDir(env.LogDir())
}

func clearTestLogDir(dirName string) {
	dirEntries, err := os.ReadDir(dirName)
	if err != nil {
		if os.IsNotExist(err) {
			return
		}
		fmt.Printf("read dir fail, err: %s", err.Error())
		return
	}

	for _, dirEntry := range dirEntries {
		fileName := dirEntry.Name()
		if fileName == "." || fileName == ".." {
			continue
		}

		fileName = filepath.Join(dirName, fileName)
		if dirEntry.IsDir() {
			clearTestLogDir(fileName)
			continue
		}

		if err = os.Remove(fileName); err != nil {
			fmt.Printf("remove file fail, file: %s, err: %s\n", fileName, err.Error())
		} else {
			fmt.Printf("remove file: %s\n", fileName)
		}
	}

	if err = os.Remove(dirName); err != nil {
		fmt.Printf("remove dir fail, dir: %s, err: %s\n", dirName, err.Error())
	} else {
		fmt.Printf("remove dir: %s\n", dirName)
	}
}

// TestEnvDefer - defer sdk_utils.TestEnvDefer(ctx)()
func TestEnvDefer(ctx context.Context) func() {
	SetTestEnv(ctx)
	return func() {
		ClearTestLogDir()
	}
}

// testRalCaller - test ral caller
type testRalCaller struct {
	expectResult  interface{}
	expectErr     error
	expectHttpRsp *HttpResponse
	sessionConfig *ralSessionConfig
}

func (t *testRalCaller) WithOption(opts ...ROption) RalCaller {
	for _, opt := range opts {
		opt(t.sessionConfig)
	}
	return t
}

var _ RalCaller = (*testRalCaller)(nil)

func NewTestRalCaller(expectResult interface{}, expectErr error, expectHttpCode int) *testRalCaller {
	if expectResult != nil {
		expectResultType := reflect.TypeOf(expectResult)
		if expectResultType.Kind() != reflect.Ptr {
			panic("expectResult should be a pointer")
		}
	}
	rawBody, _ := base_utils.JSONEncode(expectResult)
	return &testRalCaller{
		expectResult: expectResult,
		expectErr:    expectErr,
		expectHttpRsp: &HttpResponse{
			Status:     fmt.Sprintf("http status %d", expectHttpCode),
			StatusCode: expectHttpCode,
			Header:     http.Header{},
			RawBytes:   rawBody,
		},
		sessionConfig: &ralSessionConfig{},
	}
}

func (t *testRalCaller) HttpRequest(ctx context.Context, method, path string,
	queries, posts, headers, outputResult interface{}) error {
	_, err := t.HttpRequestEx(ctx, method, path, queries, posts, headers, outputResult)
	return err
}

func (t *testRalCaller) HttpRequestEx(ctx context.Context, method, path string,
	queries, posts, headers, outputResult interface{}) (*HttpResponse, error) {

	fmt.Printf("[RAL] method:%s, path:%s, queries:%s, posts:%s, headers:%s\n",
		method, path, base_utils.Format(queries), base_utils.Format(posts), base_utils.Format(headers))

	if t.expectResult != nil {
		outputResultType := reflect.TypeOf(outputResult)
		if outputResultType.Kind() != reflect.Ptr {
			panic("outputResult should be a pointer")
		}
		outputResultValue := reflect.ValueOf(outputResult)
		expectResultValue := reflect.ValueOf(t.expectResult)
		outputResultValue.Elem().Set(expectResultValue.Elem())
	}

	if t.sessionConfig.finalCheckFunc != nil {
		t.expectErr = t.sessionConfig.finalCheckFunc(ctx, t.expectHttpRsp, t.expectResult)
	}

	return t.expectHttpRsp, t.expectErr
}

func (t *testRalCaller) HttpRequestWithCustomizeHeader(ctx context.Context, method, path string,
	queries, posts, headers, outputResult interface{}) (*HttpResponse, error) {

	fmt.Printf("[RAL] method:%s, path:%s, queries:%s, posts:%s, headers:%s\n",
		method, path, base_utils.Format(queries), base_utils.Format(posts), base_utils.Format(headers))

	if t.expectResult != nil {
		outputResultType := reflect.TypeOf(outputResult)
		if outputResultType.Kind() != reflect.Ptr {
			panic("outputResult should be a pointer")
		}
		outputResultValue := reflect.ValueOf(outputResult)
		expectResultValue := reflect.ValueOf(t.expectResult)
		outputResultValue.Elem().Set(expectResultValue.Elem())
	}

	if t.sessionConfig.finalCheckFunc != nil {
		t.expectErr = t.sessionConfig.finalCheckFunc(ctx, t.expectHttpRsp, t.expectResult)
	}

	return t.expectHttpRsp, t.expectErr
}
