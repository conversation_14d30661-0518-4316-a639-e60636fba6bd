/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-07
 * File: ral_conf_test.go
 */

/*
 * DESCRIPTION
 *   ral conf test
 */

// Package sdk_utils
package sdk_utils

import (
	"context"
	"testing"
)

func TestGetRalConf(t *testing.T) {
	ctx := context.Background()
	defer TestEnvDefer(ctx)()

	ralName := "sts"

	conf, err := GetRalConf(ralName)
	if err != nil {
		t.Fatalf("GetRalConf fail, err: %s", err.Error())
	}
	if conf.Name != ralName {
		t.Fatalf("wrong ral name, expect %s, got: %s", ralName, conf.Name)
	}
}
