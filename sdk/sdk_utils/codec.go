/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-01-30
 * File: codec.go
 */

/*
 * DESCRIPTION
 *   codec相关封装
 */

// Package sdk_utils
package sdk_utils

import (
	"bytes"
	"io"
	"io/ioutil"
	"net/url"
	"reflect"

	jsoniter "github.com/json-iterator/go"
	"icode.baidu.com/baidu/gdp/codec"
)

// Encoder gdp Encoder wrapper
type Encoder interface {
	ContentType() string
	IsEnabled(v interface{}) bool
	codec.Encoder
}

var (
	JSONEncoder   Encoder = &jsonEncoder{}
	FORMEncoder   Encoder = &formEncoder{}
	MCPACKEncoder Encoder = &mcpackEncoder{}
	RawEncoder    Encoder = &rawEncoder{}
	NilEncoder    Encoder = &nilEncoder{}
)

// jsonEncoder
type jsonEncoder struct{}

func (e *jsonEncoder) IsEnabled(v interface{}) bool {
	return true
}

func (e *jsonEncoder) Encode(v interface{}) (out io.Reader, err error) {
	return codec.JSONEncode(v)
}

func (e *jsonEncoder) ContentType() string {
	return "application/json"
}

// formEncoder
type formEncoder struct{}

func (e *formEncoder) IsEnabled(v interface{}) bool {
	rv := reflect.ValueOf(v)
	if rv.Kind() == reflect.Ptr {
		rv = rv.Elem()
	}
	switch rv.Kind() {
	case reflect.Map, reflect.Struct:
		return true
	default:
		return false
	}
}

func (e *formEncoder) ContentType() string {
	return "application/x-www-form-urlencoded"
}

func (e *formEncoder) Encode(v interface{}) (out io.Reader, err error) {
	urlValues := url.Values{}
	if err = CastToUrlValues(v, &urlValues); err != nil {
		return
	}
	return codec.FORMEncode(urlValues)
}

// mcpackEncoder
type mcpackEncoder struct{}

func (e *mcpackEncoder) IsEnabled(v interface{}) bool {
	return true
}

func (e *mcpackEncoder) ContentType() string {
	return "application/octet-stream"
}

func (e *mcpackEncoder) Encode(v interface{}) (out io.Reader, err error) {
	return codec.MCPACKEncode(v)
}

// rawEncoder
type rawEncoder struct{}

func (e *rawEncoder) IsEnabled(v interface{}) bool {
	v = deInterfacePtr(v)
	switch v.(type) {
	case string, []byte:
		return true
	default:
		if _, ok := v.(io.Reader); ok {
			return true
		}
		return false
	}
}

func (e *rawEncoder) ContentType() string {
	return ""
}

func (e *rawEncoder) Encode(v interface{}) (out io.Reader, err error) {
	return codec.RawEncode(v)
}

// nilEncoder
type nilEncoder struct{}

func (e *nilEncoder) IsEnabled(v interface{}) bool {
	return true
}

func (e *nilEncoder) ContentType() string {
	return ""
}

func (e *nilEncoder) Encode(v interface{}) (out io.Reader, err error) {
	return nil, nil
}

// Decoder gdp Decoder wrapper
type Decoder interface {
	IsEnabled(v interface{}) bool
	codec.Decoder
}

var (
	JSONDecoder   Decoder = &jsonDecoder{}
	MCPACKDecoder Decoder = &mcpackDecoder{}
	RawDecoder    Decoder = &rawDecoder{}
	NilDecoder    Decoder = &nilDecoder{}
)

// jsonDecoder
type jsonDecoder struct{}

func (d *jsonDecoder) IsEnabled(v interface{}) bool {
	return isValidPtr(v)
}

func (d *jsonDecoder) Decode(in io.Reader, v interface{}) error {
	if in == nil {
		return codec.ErrNilReader
	}
	decoder := jsoniter.NewDecoder(in)
	decoder.UseNumber()
	return decoder.Decode(v)
}

// mcpackDecoder
type mcpackDecoder struct{}

func (d *mcpackDecoder) IsEnabled(v interface{}) bool {
	return isValidPtr(v)
}

func (d *mcpackDecoder) Decode(in io.Reader, v interface{}) error {
	return codec.MCPACKDecode(in, v)
}

// rawDecoder
type rawDecoder struct{}

func (d *rawDecoder) IsEnabled(v interface{}) bool {
	v = deInterfacePtr(v)
	switch v.(type) {
	case *string, *[]byte:
		return true
	default:
		if _, ok := v.(io.Writer); ok {
			return true
		}
		return false
	}
}

func (d *rawDecoder) Decode(in io.Reader, v interface{}) error {
	if in == nil {
		return codec.ErrNilReader
	}

	v = deInterfacePtr(v)
	switch v.(type) {
	case *string, *[]byte:
		b, err := ioutil.ReadAll(in)
		if err != nil {
			return err
		}
		switch v := v.(type) {
		case *string:
			*v = string(b)
		case *[]byte:
			*v = b
		}
		return nil
	default:
		return codec.RawDecode(in, v)
	}
}

// nilDecoder
type nilDecoder struct{}

func (d *nilDecoder) IsEnabled(v interface{}) bool {
	return true
}

func (d *nilDecoder) Decode(in io.Reader, v interface{}) error {
	return nil
}

// DecoderWrapper decoder wrapper
type DecoderWrapper interface {
	SetDecoder(decoder Decoder)
	GetDecoder() Decoder
}

type decoderWrapper struct {
	decoder Decoder
}

func (w *decoderWrapper) SetDecoder(decoder Decoder) {
	w.decoder = decoder
}

func (w *decoderWrapper) GetDecoder() Decoder {
	return w.decoder
}

type OnCodecError func(err error, rawBytes []byte) error

// onErrorDecoderWrapper
type onErrorDecoderWrapper struct {
	*decoderWrapper
	onError OnCodecError
}

func (d *onErrorDecoderWrapper) IsEnabled(v interface{}) bool {
	return d.decoder.IsEnabled(v)
}

func (d *onErrorDecoderWrapper) Decode(in io.Reader, v interface{}) error {
	rawBytes, err := ioutil.ReadAll(in)
	if err != nil {
		return err
	}
	err = d.decoder.Decode(bytes.NewBuffer(rawBytes), v)
	if err != nil && d.onError != nil {
		return d.onError(err, rawBytes)
	}
	return nil
}

// OnErrorDecoderWrapper get a decoder wrapper to do sth on decoding error
func OnErrorDecoderWrapper(decoder Decoder, onError OnCodecError) Decoder {
	return &onErrorDecoderWrapper{
		decoderWrapper: &decoderWrapper{decoder},
		onError:        onError,
	}
}

// ignoreEmptyDecoder
type ignoreEmptyDecoder struct {
	*decoderWrapper
}

func (d *ignoreEmptyDecoder) IsEnabled(v interface{}) bool {
	return d.decoder.IsEnabled(v)
}

func (d *ignoreEmptyDecoder) Decode(in io.Reader, v interface{}) error {
	rawBytes, err := ioutil.ReadAll(in)
	if err != nil {
		return err
	}
	if len(rawBytes) == 0 {
		return nil
	}
	return d.decoder.Decode(bytes.NewBuffer(rawBytes), v)
}

// IgnoreEmptyDecoderWrapper get a decoder wrapper that ignore empty input
func IgnoreEmptyDecoderWrapper(decoder Decoder) Decoder {
	return &ignoreEmptyDecoder{&decoderWrapper{decoder}}
}

// convert *interface{} to interface{}
func deInterfacePtr(v interface{}) interface{} {
	if v, ok := v.(*interface{}); ok {
		return *v
	}
	return v
}

// check if v is a non-nil ptr
func isValidPtr(v interface{}) bool {
	rv := reflect.ValueOf(v)
	return rv.Kind() == reflect.Ptr && !rv.IsNil()
}
