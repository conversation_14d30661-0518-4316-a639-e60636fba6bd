/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-07
 * File: option_test.go
 */

/*
 * DESCRIPTION
 *   option test
 */

// Package sdk_utils
package sdk_utils

import (
	"testing"

	"icode.baidu.com/baidu/gdp/extension/option"
)

func TestOptionWrapper_Get(t *testing.T) {
	opt := option.NewFixed(
		option.NewFixed(
			option.NewFixed(
				nil,
				map[interface{}]interface{}{"a": 1},
			),
			map[interface{}]interface{}{"b": 2},
		),
		map[interface{}]interface{}{"c": 3},
	)

	var c int64
	err := OptionWrapper(opt).Get("c", &c)
	if err != nil {
		t.Fatalf("get key fail")
	}
	if c != 3 {
		t.Fatalf("wrong value %v", c)
	}
}

func TestOptionWrapper_GetAll(t *testing.T) {
	opt := option.NewFixed(nil,
		map[interface{}]interface{}{
			"a": 1,
			"b": 2,
			"c": 3,
			"d": 4,
		},
	)

	var x struct {
		A int `json:"a"`
		B int `json:"b"`
		C int `json:"c"`
	}
	extra, err := OptionWrapper(opt).GetAll(&x)
	if err != nil {
		t.Fatalf("get all fail")
	}
	if x.C != 3 {
		t.Fatalf("wrong value %v", x.C)
	}
	if d, ok := extra["d"]; !ok {
		t.Fatalf("extra key not found")
	} else if d != 4 {
		t.Fatalf("wrong extra value %v", d)
	}
}
