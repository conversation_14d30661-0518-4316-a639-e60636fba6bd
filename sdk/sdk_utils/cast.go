/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-07
 * File: cast.go
 */

/*
 * DESCRIPTION
 *   casts
 */

// Package sdk_utils
package sdk_utils

import (
	"net/http"
	"net/url"

	"github.com/spf13/cast"

	"icode.baidu.com/baidu/scs/x1-base/utils/mapstruct"
)

// Cast - mapstruct decode
// @param input
// 			- sruct with field tag "mapstructure",
// 			- map[interface{}]interface{}
// 			- map[string]interface{}
// 			- map[string][string]
// @param output
// 			- *sruct with field tag "mapstructure",
// 			- *map[interface{}]interface{},
// 			- *map[string]interface,
// 			- *map[string][string]
// @return error
func Cast(input, output interface{}) error {
	return CastWithTag(input, output, "mapstructure")
}

// CastWithTag - 同Cast，指定tag
func CastWithTag(input, output interface{}, tag string) error {
	return mapstruct.DecodeEx(input, output, true, tag)
}

// CastFromUrlValues - url.Values -> interface
// @param input
// 			- url.Values
// @param output
// 			- *sruct with field tag "url"
//          - *map[interface{}]interface{}
// 			- *map[string]interface{}
// 			- *map[string][string]
// @return error
func CastFromUrlValues(input url.Values, output interface{}) error {
	return CastFromUrlValuesWithTag(input, output, "url")
}

// CastFromUrlValuesWithTag - 同CastFromUrlValues，指定tag
func CastFromUrlValuesWithTag(input url.Values, output interface{}, tag string) error {
	if p, ok := output.(*url.Values); ok {
		// 不支持url数组参数，以下同
		for k, _ := range input {
			p.Add(k, input.Get(k))
		}
		return nil
	}

	m := make(map[string]interface{})
	for k, _ := range input {
		m[k] = input.Get(k)
	}

	return CastWithTag(m, output, tag)
}

// CastToUrlValues - interface -> url.Values
// @param input
// 			- struct with file tag "url"
// 			- map[interface{}]interface{}
// 			- map[string]interface{}
// 			- map[string][string]
// @param output
// 			- *url.Values
// @return error
func CastToUrlValues(input interface{}, output *url.Values) error {
	return CastToUrlValuesWithTag(input, output, "url")
}

// CastToUrlValuesWithTag - 同CastToUrlValues，并指定tag
func CastToUrlValuesWithTag(input interface{}, output *url.Values, tag string) error {
	if in, ok := input.(url.Values); ok {
		for k, _ := range in {
			output.Add(k, in.Get(k))
		}
		return nil
	}

	m := make(map[string]interface{})
	if err := CastWithTag(input, &m, tag); err != nil {
		return err
	}
	for k, v := range m {
		output.Add(k, cast.ToString(v))
	}

	return nil
}

// CastFromHttpHeader - http.Header -> interface
// @param input
// 			- http.Header
// @param output
// 			- *sruct with field tag "header"
//          - *map[interface{}]interface{}
// 			- *map[string]interface{}
// 			- *map[string][string]
// @return error
func CastFromHttpHeader(input http.Header, output interface{}) error {
	return CastFromHttpHeaderWithTag(input, output, "header")
}

// CastFromHttpHeaderWithTag - 同CastFromHttpHeader，指定tag
func CastFromHttpHeaderWithTag(input http.Header, output interface{}, tag string) error {
	if p, ok := output.(*http.Header); ok {
		// 不支持header数组参数，以下同
		for k, _ := range input {
			p.Add(k, input.Get(k))
		}
		return nil
	}

	m := make(map[string]interface{})
	for k, _ := range input {
		m[k] = input.Get(k)
	}

	return CastWithTag(m, output, tag)
}

// CastToHttpHeader - interface -> http.Header
// @param input
// 			- struct with file tag "url"
// 			- map[interface{}]interface{}
// 			- map[string]interface{}
// 			- map[string][string]
// @param output
// 			- *http.Header
// @return error
func CastToHttpHeader(input interface{}, output *http.Header) error {
	return CastToHttpHeaderWithTag(input, output, "header")
}

// CastToHttpHeaderWithTag - 同CastToHttpHeader，并指定tag
func CastToHttpHeaderWithTag(input interface{}, output *http.Header, tag string) error {
	if in, ok := input.(http.Header); ok {
		for k, _ := range in {
			output.Add(k, in.Get(k))
		}
		return nil
	}

	m := make(map[string]interface{})
	if err := CastWithTag(input, &m, tag); err != nil {
		return err
	}
	for k, v := range m {
		output.Add(k, cast.ToString(v))
	}
	return nil
}

// CastRequestQueriesToMap
// @param input
// 			- struct with file tag "url"
// 			- url.Values
// @param output
// 			- map[string]interface{}
// @return error
func CastRequestQueriesToMap(reqQueries interface{}) (map[string]interface{}, error) {
	queries := map[string]interface{}{}

	if reqQueries != nil {
		if p, ok := reqQueries.(url.Values); ok {
			if err := CastFromUrlValues(p, &queries); err != nil {
				return nil, err
			}
		} else {
			if err := CastWithTag(reqQueries, &queries, "url"); err != nil {
				return nil, err
			}
		}
	}

	return queries, nil
}

// CastRequestHeadersToMap
// @param input
// 			- struct with file tag "header"
// 			- http.Header
// @return output
// 			- map[string]interface{}
// @return error
func CastRequestHeadersToMap(reqHeaders interface{}) (map[string]interface{}, error) {
	headers := map[string]interface{}{}

	if reqHeaders != nil {
		if p, ok := reqHeaders.(http.Header); ok {
			if err := CastFromHttpHeader(p, &headers); err != nil {
				return nil, err
			}
		} else {
			if err := CastWithTag(reqHeaders, &headers, "header"); err != nil {
				return nil, err
			}
		}
	}

	return headers, nil
}
