/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-02-07
 * File: option.go
 */

/*
 * DESCRIPTION
 *   对option.Option的封装
 */

// Package sdk_utils
package sdk_utils

import (
	"fmt"
	"reflect"

	"icode.baidu.com/baidu/gdp/extension/option"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/utils/mapstruct"
)

type Option interface {
	option.Option
	Get(k, v interface{}) (err error)
	GetAll(v interface{}) (extra map[string]interface{}, err error)
}

type optionWrapper struct {
	option.Option
}

func (opt *optionWrapper) Get(k, v interface{}) (err error) {
	_opt := opt.Option
	for _opt != nil {
		if _v := _opt.Value(k); _v != nil {
			return CastWithTag(_v, v, "json")
		}
		_opt = _opt.Base()
	}
	return cerrs.ErrNotFound
}

func (opt *optionWrapper) GetAll(v interface{}) (extra map[string]interface{}, err error) {
	m := make(map[interface{}]interface{})
	_opt := opt.Option
	for _opt != nil {
		_opt.Range(func(k, v interface{}) bool {
			if reflect.TypeOf(k).Kind() == reflect.String {
				k = fmt.Sprint(k)
			} else {
				panic("not a string kind")
			}
			m[k] = v
			return true
		})
		_opt = _opt.Base()
	}

	meta, err := mapstruct.DecodeExWithMeta(m, v, true, "json")
	if err != nil {
		return nil, err
	}

	extra = map[string]interface{}{}
	for _, k := range meta.Unused {
		extra[k] = m[k]
	}
	return
}

func OptionWrapper(opt option.Option) Option {
	return &optionWrapper{
		Option: opt,
	}
}
