/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-06
 * File: ral_conf.go
 */

/*
 * DESCRIPTION
 *   ral conf utils
 */

// Package sdk_utils
package sdk_utils

import (
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
)

// RalConfInfo -
type RalConfInfo struct {
	Name          string                 `json:"name"`
	Headers       map[string]string      `json:"headers,omitempty"`
	ConnTimeOut   int64                  `json:"conn_timeout"`
	ReadTimeOut   int64                  `json:"read_timeout"`
	WriteTimeOut  int64                  `json:"write_timeout"`
	Retry         int64                  `json:"retry"`
	Strategy      *balanceStrategy       `json:"strategy"`
	SuperStrategy *balanceStrategy       `json:"super_strategy,omitempty"`
	Resource      *ralResource           `json:"resource"`
	Extra         map[string]interface{} `json:"extra"`
}

type balanceStrategy struct {
	Name string `json:"name"`
}

type ralResource struct {
	Manual map[string][]*EndPoint `json:"manual,omitempty"`
	BNS    *bnsInfo               `json:"bns,omitempty"`
}

type bnsInfo struct {
	BnsName string `json:"bns_name"`
}

// GetRalConf - get ral conf by ral name
func GetRalConf(ralName string) (*RalConfInfo, error) {
	opt, err := GetRalOption(ralName)
	if err != nil {
		return nil, err
	}

	conf := &RalConfInfo{Extra: map[string]interface{}{}}
	conf.Extra, err = opt.GetAll(conf)
	return conf, err
}

// GetRalOption - 获取ral的原始配置
func GetRalOption(ralName string) (Option, error) {
	if ral.DefaultRaller == nil {
		panic("ral not init, can not load conf")
	}
	sm := ral.DefaultRaller.ServiceMapper()

	svr := sm.Servicer(ralName)
	if svr == nil {
		return nil, cerrs.ErrNotFound.Errorf("servicer not found")
	}

	opt := svr.Option()
	if opt == nil {
		return nil, cerrs.ErrNotFound.Errorf("servicer option is nil")
	}

	return OptionWrapper(opt), nil
}
