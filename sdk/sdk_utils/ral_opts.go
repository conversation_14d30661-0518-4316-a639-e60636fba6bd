/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-16
 * File: ral_opts.go
 */

/*
 * DESCRIPTION
 *   ral.ROption
 */

// Package sdk_utils
package sdk_utils

import (
	"context"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"strings"

	"icode.baidu.com/baidu/gdp/extension/option"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/net/connector"
	"icode.baidu.com/baidu/gdp/net/ral"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
)

// ral session config
type ralSessionConfig struct {
	ctx              context.Context
	encoder          Encoder
	decoder          Decoder
	ralOpts          []ral.ROption
	prepareCheckFunc prepareCheckFunc
	finalCheckFunc   FinalCheckFunc
	its              []*ral.Interceptor
}

func getRalSessionConfig(ctx context.Context, method string, posts, result interface{},
	opts ...ROption) *ralSessionConfig {

	var defaultEncoder Encoder
	var defaultDecoder Decoder

	if posts == nil {
		defaultEncoder = NilEncoder
	} else {
		switch method {
		case MethodPost, MethodPut, MethodDelete, MethodPatch:
			if RawEncoder.IsEnabled(posts) {
				defaultEncoder = RawEncoder
			} else {
				defaultEncoder = JSONEncoder
			}
		default:
			defaultEncoder = NilEncoder
		}
	}

	if result == nil {
		defaultDecoder = NilDecoder
	} else {
		if RawDecoder.IsEnabled(result) {
			defaultDecoder = RawDecoder
		} else {
			defaultDecoder = JSONDecoder
		}
	}

	conf := &ralSessionConfig{
		ctx:     ctx,
		encoder: defaultEncoder,
		decoder: defaultDecoder,
		ralOpts: make([]ral.ROption, 0),
	}

	for _, opt := range opts {
		opt(conf)
	}

	return conf
}

type ROption func(conf *ralSessionConfig)

// ROptEncoder 指定encoder
func ROptEncoder(encoder Encoder) ROption {
	return func(conf *ralSessionConfig) {
		conf.encoder = encoder
	}
}

// ROptDecoder 指定decoder
func ROptDecoder(decoder Decoder) ROption {
	return func(conf *ralSessionConfig) {
		for d := decoder; ; {
			if w, ok := d.(DecoderWrapper); ok {
				if d = w.GetDecoder(); d != nil {
					continue
				}
				w.SetDecoder(conf.decoder)
			}
			break
		}
		conf.decoder = decoder
	}
}

// ROptSingleAddr - 请求指定addr, 支持string或net.Addr类型
func ROptSingleAddr(addr interface{}) ROption {
	return func(conf *ralSessionConfig) {
		ralOpt := ral.ROptConnector(&connector.Single{
			Addr: ResolveAddr(addr),
		})
		conf.ralOpts = append(conf.ralOpts, ralOpt)
	}
}

// ROptReadTimeOut - 给当前会话设置读超时，单位毫秒
func ROptReadTimeOut(timeoutMs int) ROption {
	return func(conf *ralSessionConfig) {
		ralOpt := ral.ROptReadTimeOut(timeoutMs)
		conf.ralOpts = append(conf.ralOpts, ralOpt)
	}
}

// ROptRetry - 给当前会话设置重试次数
func ROptRetry(retry int) ROption {
	return func(conf *ralSessionConfig) {
		ralOpt := ral.ROptRetry(retry)
		conf.ralOpts = append(conf.ralOpts, ralOpt)
	}
}

// PrepareChecker ral http response prepare checker, 返回err ral将重试
type PrepareChecker func(ctx context.Context, httpRsp *HttpResponse, invoker PrepareCheckFunc) error
type PrepareCheckFunc func(ctx context.Context, httpRsp *HttpResponse) error
type prepareCheckFunc func(httpRsp *http.Response) error

// ROptPrepareChecker - 设置ral PrepareChecker
func ROptPrepareChecker(checker PrepareChecker) ROption {
	return func(conf *ralSessionConfig) {
		conf.prepareCheckFunc = func(lastCheckFunc prepareCheckFunc) prepareCheckFunc {
			return func(httpRsp *http.Response) error {
				return checker(conf.ctx, HttpRspWrapper(httpRsp),
					func(_ context.Context, _ *HttpResponse) error {
						if lastCheckFunc != nil {
							return lastCheckFunc(httpRsp)
						}
						if httpRsp.StatusCode >= http.StatusInternalServerError {
							return cerrs.ErrHttpStatusError.Errorf(httpRsp.Status)
						}
						return nil
					})
			}
		}(conf.prepareCheckFunc)
	}
}

// FinalChecker 检查http response 和 result
type FinalChecker func(ctx context.Context, httpRsp *HttpResponse, result interface{}, invoker FinalCheckFunc) error
type FinalCheckFunc func(ctx context.Context, httpRsp *HttpResponse, result interface{}) error

// ROptFinalChecker - 设置ral FinalChecker
func ROptFinalChecker(checker FinalChecker) ROption {
	return func(conf *ralSessionConfig) {
		conf.finalCheckFunc = func(lastCheckFunc FinalCheckFunc) FinalCheckFunc {
			return func(ctx context.Context, httpRsp *HttpResponse, result interface{}) error {
				return checker(ctx, httpRsp, result,
					func(ctx context.Context, httpRsp *HttpResponse, result interface{}) error {
						if lastCheckFunc != nil {
							return lastCheckFunc(ctx, httpRsp, result)
						}
						return nil
					})
			}
		}(conf.finalCheckFunc)
	}
}

// HttpReqBuilder http request builder, 用于在ral do http request 前中更新request
type HttpReqBuilder func(ctx context.Context, opt Option, addr Addr, httpReq *HttpRequest) error

// ROptHttpReqBuilder - 设置ral HttpReqBuilder
func ROptHttpReqBuilder(builder HttpReqBuilder) ROption {
	return func(conf *ralSessionConfig) {
		conf.its = append(conf.its, &ral.Interceptor{
			BuildRequest: func(ctx context.Context, opt option.Option, addr net.Addr,
				invoker ral.BuildRequestFunc) (interface{}, error) {
				r, err := invoker(ctx, opt, addr)
				if err != nil {
					return nil, err
				}
				httpReq, ok := r.(*http.Request)
				if !ok {
					return r, nil
				}
				err = builder(conf.ctx, OptionWrapper(opt), ResolveAddr(addr), HttpReqWrapper(httpReq))
				return httpReq, err
			},
		})
	}
}

// ROptGenCurlLog 打印请求的等价curl命令
func ROptGenCurlLog(level logit.Level) ROption {
	return func(conf *ralSessionConfig) {
		conf.its = append([]*ral.Interceptor{{
			BuildRequest: func(ctx context.Context, opt option.Option, addr net.Addr,
				invoker ral.BuildRequestFunc) (interface{}, error) {
				r, err := invoker(ctx, opt, addr)
				if err != nil {
					return nil, err
				}
				httpReq, ok := r.(*http.Request)
				if !ok {
					return r, nil
				}
				// curl{ -X method}{ -H 'xxx' -H 'yyy'}{ -d 'zzz'} {'url'}
				method := httpReq.Method
				if strings.EqualFold(method, MethodGet) {
					method = ""
				} else if method != "" {
					method = fmt.Sprintf(" -X %s", strings.ToUpper(method))
				}
				headers := ""
				for k, _ := range httpReq.Header {
					headers += fmt.Sprintf(" -H '%s:%s'", k, httpReq.Header.Get(k))
				}
				payload := ""
				if body, err := httpReq.GetBody(); err == nil {
					defer func() { _ = body.Close() }()
					if b, err := ioutil.ReadAll(body); err == nil {
						// json.Encoder will add a '\n' after marshaling
						payload = strings.TrimSuffix(string(b), "\n")
						if payload != "" {
							payload = fmt.Sprintf(" -d '%s'",
								strings.ReplaceAll(payload, "'", `\'`))
						}
					}
				}
				URL := *httpReq.URL
				URL.Host = strings.TrimSuffix(addr.String(), ":80")

				curl := fmt.Sprintf("curl%s%s%s '%s'", method, headers, payload, URL.String())
				logger.SdkLogger.Output(conf.ctx, level, 0, "%s", curl)
				return httpReq, err
			},
		}}, conf.its...)
	}
}
