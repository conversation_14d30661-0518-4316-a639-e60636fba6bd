/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-06
 * File: ral.go
 */

/*
 * DESCRIPTION
 *   对ral调用的封装
 */

// Package sdk_utils
package sdk_utils

import (
	"context"
	"net"
	"net/http"
	"net/url"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/net/ral"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

// RalHttpGet - ral http get
// @param ctx - context
// @param path - url path
// @param queries - http queries: url.Values, struct with field tag "url", map[string]interface{} ...
// @param headers - http headers: url.Values, struct with field tag "header", map[string]interface{} ...
// @param result - *struct with field tag "json", map[string]interface{}
// @return error
func RalHttpGet(ctx context.Context, ralName, path string, queries, headers, result interface{},
	opts ...ROption) error {

	return RalHttp(ctx, ralName, MethodGet, path, queries, nil, headers, result, opts...)
}

// RalHttpPost - ral http post with "json" content-type
// @param ctx - context
// @param path - url path
// @param queries - http queries: url.Values, struct with field tag "url", map[string]interface{} ...
// @param posts - http posts: url.Values, struct with field tag "url", map[string]interface{} ...
// @param headers - http headers: url.Values, struct with field tag "header", map[string]interface{} ...
// @param result - *struct with field tag "json", map[string]interface{}
// @return error
func RalHttpPost(ctx context.Context, ralName, path string, queries, posts, headers, result interface{},
	opts ...ROption) error {

	return RalHttp(ctx, ralName, MethodPost, path, queries, posts, headers, result, opts...)
}

// RalHttp - ral http call
// @param ctx - context
// @param method - http method
// @param path - url path
// @param queries - url.Values, struct with field tag "url", map[string]interface{} ...
// @param posts - url.Values, struct with field tag "url", map[string]interface{} ...
// @param headers - url.Values, struct with field tag "header", map[string]interface{} ...
// @param result - *struct with field tag "json", map[string]interface{}
// @return error
func RalHttp(ctx context.Context, ralName, method, path string, queries, posts, headers, result interface{},
	opts ...ROption) error {

	_, err := RalHttpEx(ctx, ralName, method, path, queries, posts, headers, result, opts...)
	return err
}

// RalHttpEx - 同RalHttp，并返回http response
func RalHttpEx(ctx context.Context, ralName, method, path string, queries, posts, headers, result interface{},
	opts ...ROption) (*HttpResponse, error) {

	// 获取配置
	conf := getRalSessionConfig(ctx, method, posts, result, opts...)

	// 获取 headers
	httpHeader := http.Header{}
	if headers != nil {
		if err := CastToHttpHeader(headers, &httpHeader); err != nil {
			logger.SdkLogger.Warning(ctx, "parse headers fail, type: %T, value: %+v", headers, headers)
			return nil, cerrs.ErrInvalidParams.Wrap(err)
		}
	}
	if posts != nil && len(httpHeader.Values("Content-Type")) == 0 {
		if t := conf.encoder.ContentType(); t != "" {
			httpHeader.Add("Content-Type", t)
		}
	}

	// 创建 ral req
	ralReq := &ghttp.RalRequest{
		Method: method,
		Path:   path,
		Header: httpHeader,
	}
	if queries != nil {
		ralReq.Query = url.Values{}
		if err := CastToUrlValues(queries, &ralReq.Query); err != nil {
			logger.SdkLogger.Warning(ctx, "parse queries fail, type: %T, value: %+v", queries, queries)
			return nil, cerrs.ErrInvalidParams.Wrap(err)
		}
	}

	// 获取 post body
	if posts != nil {
		if err := ralReq.WithBody(posts, conf.encoder); err != nil {
			logger.SdkLogger.Warning(ctx, "post body encode fail: %s, posts: %s",
				err.Error(), base_utils.Format(posts))
			return nil, cerrs.ErrInvalidParams.Wrap(err)
		}
	}

	// 构造 ral ralHttpRsp
	ralRsp := &ghttp.RalResponse{
		PrepareCheck: conf.prepareCheckFunc,
	}
	if result != nil {
		ralRsp.Data = result
		ralRsp.Decoder = conf.decoder
	}

	// ral call
	if conf.its != nil {
		ctx = ral.ContextWithInterceptor(ctx, conf.its...)
	}
	err := ral.RAL(ctx, ralName, ralReq, ralRsp, conf.ralOpts...)
	httpRsp := HttpRspWrapper(ralRsp.Response())

	if err != nil {
		logger.SdkLogger.Warning(ctx,
			"ral fail: %s, ralName: %s, method: %s, path: %s, queries: %s, posts: %s, headers: %s, httpRsp: %v",
			err.Error(), ralName, method, path, base_utils.Format(queries), base_utils.Format(posts),
			base_utils.Format(headers), httpRsp)
		return httpRsp, cerrs.ErrRalRequestFail.Wrap(err)
	}

	// final check
	if conf.finalCheckFunc != nil {
		err = conf.finalCheckFunc(ctx, httpRsp, result)
	}

	return httpRsp, err
}

// IsConnectFail 判断是否是连接失败
func IsConnectFail(err error) bool {
	netErr := &net.OpError{}
	if cerrs.As(err, &netErr) && netErr.Op == "dial" {
		return true
	}
	return false
}

// GetRalWorkLogger 获取ral worker logger
func GetRalWorkLogger() logit.Logger {
	if ral.DefaultRaller == nil {
		return nil
	}
	return ral.DefaultRaller.WorkLogger()
}

// RalHTTPExWithCustomizeHeader - 同RalHttp，并返回http response
func RalHTTPExWithCustomizeHeader(ctx context.Context, ralName, method, path string, queries, posts, headers, result interface{},
	opts ...ROption) (*HttpResponse, error) {

	// 获取配置
	conf := getRalSessionConfig(ctx, method, posts, result, opts...)

	// 设置header
	httpHeader := make(map[string][]string)
	m := make(map[string]interface{})
	if err := CastWithTag(headers, &m, "header"); err != nil {
	}
	for k, v := range m {
		httpHeader[k] = []string{cast.ToString(v)}
	}

	if _, ok := httpHeader["Content-Type"]; !ok {
		if t := conf.encoder.ContentType(); t != "" {
			httpHeader["Content-Type"] = []string{t}
		}
	}

	// 创建 ral req
	ralReq := &ghttp.RalRequest{
		Method: method,
		Path:   path,
		Header: httpHeader,
	}
	if queries != nil {
		ralReq.Query = url.Values{}
		if err := CastToUrlValues(queries, &ralReq.Query); err != nil {
			logger.SdkLogger.Warning(ctx, "parse queries fail, type: %T, value: %+v", queries, queries)
			return nil, cerrs.ErrInvalidParams.Wrap(err)
		}
	}

	// 获取 post body
	if posts != nil {
		if err := ralReq.WithBody(posts, conf.encoder); err != nil {
			logger.SdkLogger.Warning(ctx, "post body encode fail: %s, posts: %s",
				err.Error(), base_utils.Format(posts))
			return nil, cerrs.ErrInvalidParams.Wrap(err)
		}
	}

	// 构造 ral ralHttpRsp
	ralRsp := &ghttp.RalResponse{
		PrepareCheck: conf.prepareCheckFunc,
	}
	if result != nil {
		ralRsp.Data = result
		ralRsp.Decoder = conf.decoder
	}

	logger.SdkLogger.Trace(ctx,
		"ral start, ralName: %s, method: %s, path: %s, queries: %s, posts: %s, headers: %s",
		ralName, method, path, base_utils.Format(queries), base_utils.Format(posts),
		base_utils.Format(httpHeader))

	// ral call
	if conf.its != nil {
		ctx = ral.ContextWithInterceptor(ctx, conf.its...)
	}
	err := ral.RAL(ctx, ralName, ralReq, ralRsp, conf.ralOpts...)
	httpRsp := HttpRspWrapper(ralRsp.Response())

	if err != nil {
		logger.SdkLogger.Warning(ctx,
			"ral fail: %s, ralName: %s, method: %s, path: %s, queries: %s, posts: %s, headers: %s, httpRsp: %v",
			err.Error(), ralName, method, path, base_utils.Format(queries), base_utils.Format(posts),
			base_utils.Format(httpHeader), httpRsp)
		return httpRsp, cerrs.ErrRalRequestFail.Wrap(err)
	}

	logger.SdkLogger.Trace(ctx,
		"ral success, ralName: %s, method: %s, path: %s, queries: %s, posts: %s, headers: %s, httpRsp: %v",
		ralName, method, path, base_utils.Format(queries), base_utils.Format(posts),
		base_utils.Format(httpHeader), httpRsp)

	// final check
	if conf.finalCheckFunc != nil {
		err = conf.finalCheckFunc(ctx, httpRsp, result)
	}

	return httpRsp, err
}
