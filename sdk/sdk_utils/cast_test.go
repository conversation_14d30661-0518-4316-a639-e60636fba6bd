/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-07
 * File: cast_test.go
 */

/*
 * DESCRIPTION
 *   cast test
 */

// Package sdk_utils
package sdk_utils

import (
	"fmt"
	"net/http"
	"net/url"
	"testing"
)

func TestCast(t *testing.T) {
	var a = 1
	var b int
	if Cast(a, &b) != nil {
		t.Fatalf("cast int fail")
	} else {
		fmt.Printf("%v -> %v\n", a, b)

		if a != b {
			t.Fatalf("cast int value invalid")
		}
	}

	type TestStruct struct {
		A string `mapstructure:"a"`
		B string `mapstructure:"b"`
	}

	inputStruct := TestStruct{
		A: "1",
		B: "2",
	}

	inputMapInterfaceInterface := map[interface{}]interface{}{
		"c": "3",
		4:   4,
	}

	inputMapStringInterface := map[string]interface{}{
		"e": "5",
		"f": 6,
	}

	inputMapStringString := map[string]string{
		"a": "7",
		"b": "8",
	}

	var outUrlValues = TestStruct{}
	var outputMapInterfaceInterface map[interface{}]interface{}
	var outputMapStringInterface map[string]interface{}
	var outputMapStringString map[string]string

	inputs := []interface{}{
		inputStruct,
		inputMapInterfaceInterface,
		inputMapStringInterface,
		inputMapStringString,
	}

	outputs := []interface{}{
		&outUrlValues,
		&outputMapInterfaceInterface,
		&outputMapStringInterface,
		&outputMapStringString,
	}

	for _, input := range inputs {
		for _, output := range outputs {
			if err := Cast(input, output); err != nil {
				t.Errorf("[TestCast] fail, %T -> %T, %s", input, output, err.Error())
			} else {
				fmt.Printf("%+v -> %+v\n", input, output)
			}
		}
	}
}

func TestCastUrlValues(t *testing.T) {
	// urlValues to map[string]interface{}
	urlValues := url.Values{}
	urlValues.Add("a", "1")
	urlValues.Add("b", "2")

	var outputMapStringInterface map[string]interface{}
	if err := CastFromUrlValues(urlValues, &outputMapStringInterface); err != nil {
		t.Errorf("[TestCastUrlValues] CastFromUrlValues fail")
	} else {
		fmt.Printf("%+v -> %+v\n", urlValues, outputMapStringInterface)

		if v, ok := outputMapStringInterface["a"]; !ok {
			t.Errorf("[TestCastUrlValues] CastFromUrlValues to map fail, field a not found")
		} else {
			_v, _ := v.(string)
			if _v != "1" {
				t.Errorf("[TestCastUrlValues] CastFromUrlValues to map fail, value a invalid")
			}
		}
	}

	// urlValues to urlStruct
	type TestStruct struct {
		A string `url:"a"`
		B string `url:"b"`
	}
	var outputStruct TestStruct
	if err := CastFromUrlValues(urlValues, &outputStruct); err != nil {
		t.Errorf("[TestCastUrlValues] CastFromUrlValues to struct fail")
	} else {
		fmt.Printf("%+v -> %+v\n", urlValues, outputStruct)
		if outputStruct.A != "1" {
			t.Errorf("[TestCastUrlValues] CastFromUrlValues to struct fail, value a invalid")
		}
	}
}

func TestCastHttpHeader(t *testing.T) {
	// httpHeader to map[string]interface{}
	httpHeader := http.Header{}
	httpHeader.Add("a", "1")
	httpHeader.Add("b", "2")

	var outputMapStringInterface map[string]interface{}
	if err := CastFromHttpHeader(httpHeader, &outputMapStringInterface); err != nil {
		t.Errorf("[TestCastHttpHeader] CastFromHttpHeader fail")
	} else {
		fmt.Printf("%+v -> %+v\n", httpHeader, outputMapStringInterface)

		// header Key 变成大写了
		if v, ok := outputMapStringInterface["A"]; !ok {
			t.Errorf("[TestCastHttpHeader] CastFromHttpHeader to map fail, field a not found")
		} else {
			_v, _ := v.(string)
			if _v != "1" {
				t.Errorf("[TestCastHttpHeader] CastFromHttpHeader to map fail, value a invalid")
			}
		}
	}

	// httpHeader to urlStruct
	type TestStruct struct {
		A string `header:"a"`
		B string `header:"b"`
	}
	var outputStruct TestStruct
	if err := CastFromHttpHeader(httpHeader, &outputStruct); err != nil {
		t.Errorf("[TestCastHttpHeader] CastFromHttpHeader to struct fail")
	} else {
		fmt.Printf("%+v -> %+v\n", httpHeader, outputStruct)
		if outputStruct.A != "1" {
			t.Errorf("[TestCastHttpHeader] CastFromHttpHeader to struct fail, value a invalid")
		}
	}
}
