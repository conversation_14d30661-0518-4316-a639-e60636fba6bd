/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-02-07
 * File: net.go
 */

/*
 * DESCRIPTION
 *   net相关封装
 */

// Package sdk_utils
package sdk_utils

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"strings"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/net/gaddr"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

// http methods
const (
	MethodHead   = http.MethodHead
	MethodGet    = http.MethodGet
	MethodPost   = http.MethodPost
	MethodPut    = http.MethodPut
	MethodDelete = http.MethodDelete
	MethodPatch  = http.MethodPatch
)

// HttpResponse http.Response wrapper
type HttpResponse struct {
	Status     string // "200 OK"
	StatusCode int    // 200
	Header     http.Header
	RawBytes   []byte
}

func HttpRspWrapper(rsp *http.Response) *HttpResponse {
	if rsp == nil {
		return nil
	}

	var rawBytes []byte
	if rsp.Body != nil {
		defer func() { _ = rsp.Body.Close() }()
		rawBytes, _ = ioutil.ReadAll(rsp.Body)
		rsp.Body = ioutil.NopCloser(bytes.NewBuffer(rawBytes))
	}

	return &HttpResponse{
		Status:     rsp.Status,
		StatusCode: rsp.StatusCode,
		Header:     rsp.Header.Clone(),
		RawBytes:   rawBytes,
	}
}

func (rsp *HttpResponse) String() string {
	return fmt.Sprintf("%s, rawBytes: %s", rsp.Status, base_utils.Format(rsp.RawBytes))
}

// HttpRequest http.Request (client) wrapper
type HttpRequest struct {
	*http.Request
}

func HttpReqWrapper(httpReq *http.Request) *HttpRequest {
	if httpReq == nil {
		return nil
	}
	return &HttpRequest{httpReq}
}

func (req *HttpRequest) SetHeader(k string, v interface{}) {
	_v := cast.ToString(v)
	if strings.EqualFold(k, "Host") {
		req.SetHost(_v)
	} else {
		req.Header.Set(k, _v)
	}
}

func (req *HttpRequest) GetHeader(k string, v interface{}) error {
	var _v string

	if strings.EqualFold(k, "Host") {
		_v = req.GetHost()
	} else if _vs := req.Header.Values(k); len(_vs) == 0 {
		return cerrs.ErrNotFound
	} else {
		_v = _vs[0]
	}

	return Cast(_v, v)
}

func (req *HttpRequest) GetHost() string {
	// For client requests, Host optionally overrides the Host
	// header to send. If empty, the Request.Write method uses
	// the value of URL.Host. Host may contain an international
	// domain name.
	if req.Host != "" {
		return req.Host
	}
	return req.URL.Host
}

func (req *HttpRequest) SetHost(host string) {
	req.Host = host
	req.URL.Host = host
	req.Header.Set("Host", host)
}

// EndPoint host&port
type EndPoint struct {
	Host string `json:"host"`
	Port int    `json:"port"`
}

// Addr net.Addr wrapper
type Addr interface {
	net.Addr
	Host() string
	Port() int
}

type addrWrapper struct {
	net.Addr
	*EndPoint
}

func (a *addrWrapper) Host() string {
	return a.EndPoint.Host
}

func (a *addrWrapper) Port() int {
	return a.EndPoint.Port
}

// ResolveAddr - parse addr -> net.Addr
func ResolveAddr(addr interface{}) Addr {
	if v, ok := addr.(Addr); ok {
		return v
	} else if v, ok := addr.(*EndPoint); ok {
		return NewAddr(v.Host, v.Port)
	} else if v, ok := addr.(net.Addr); ok {
		host, port := SplitHostPort(v.String())
		return newAddr(host, port, v)
	} else if v, ok := addr.(string); ok {
		host, port := SplitHostPort(v)
		return newAddr(host, port, gaddr.New("tcp", v))
	}
	panic(fmt.Sprintf("invalid addr type: %T", addr))
}

func newAddr(host string, port interface{}, addr net.Addr) Addr {
	if addr == nil {
		addr = gaddr.New("tcp", JoinHostPort(host, port))
	}
	return &addrWrapper{
		Addr: addr,
		EndPoint: &EndPoint{
			Host: host,
			Port: cast.ToInt(port),
		},
	}
}

// NewAddr - host+port->Addr
func NewAddr(host string, port interface{}) Addr {
	return newAddr(host, port, nil)
}

// SplitHostPort net.SplitHostPort wrapper
func SplitHostPort(hostport string) (string, string) {
	host, port, err := net.SplitHostPort(hostport)
	if err != nil {
		host = hostport
	}
	return host, port
}

// JoinHostPort net.JoinHostPort wrapper
func JoinHostPort(host string, port interface{}) string {
	if cast.ToInt(port) == 0 {
		port = ""
	}
	return net.JoinHostPort(host, cast.ToString(port))
}
