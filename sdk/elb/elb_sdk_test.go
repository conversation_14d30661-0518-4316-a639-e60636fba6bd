package elb

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func TestElbSdk_QueryEip_NotFound(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newElbSdk(DefaultServiceName)
	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, nil, http.StatusNotFound))

	_, err := s.QueryEip(ctx, &BindQueryRequest{
		Eip: "",
		Auth: &common.Authentication{
			IamUserId:     "mock-user-id",
			TransactionId: "mock-trans-id",
			Credential: &common.Credential{
				Ak:           "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
				Sk:           "bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
				SessionToken: "cccccccccccccccccccccccccccccccc",
			},
		},
	})

	if !cerrs.ErrNotFound.Is(err) {
		t.Fatalf("expect err: %s, got err: %s", cerrs.ErrNotFound, err)
	}

}

func TestElbSdk_CreateELB(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newElbSdk(DefaultServiceName)
	expectResult := &OperationResponse{}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(expectResult, nil, 200))

	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     "204d95b9fa0c4cad8eeea3860b5ad6cf",
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	auth := authRsp.Auth

	createRsp, err := s.CreateELB(ctx, &CreateELBRequest{
		Action:            "create",
		Internal:          "true",
		BandwidthInMbps:   0,
		Name:              "scs-bj-cdsdcsdssjtn",
		Callback:          "",
		Count:             1,
		Source:            "scs",
		VpcId:             "7d3caa41-c268-410d-ae68-97b3baff8423",
		SubnetId:          "a15555b7-ae41-41f2-971b-7babac8caf7d",
		IsVpc:             true,
		Type:              "normal",
		BgwGroupId:        "",
		BgwGroupExclusive: false,
		MasterAZ:          "",
		SlaveAZ:           "",
		BgwGroupMode:      "",
		Auth:              auth,
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(base_utils.Format(createRsp))
}

func TestElbSdk_ListElb(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newElbSdk(DefaultServiceName)

	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     "204d95b9fa0c4cad8eeea3860b5ad6cf",
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	auth := authRsp.Auth

	listRsp, err := s.ListElb(ctx, &ListElbRequest{
		ElbId: "7a346a616a64577931482b693976316b6761746947513d3d",
		Port:  6379,
		Auth:  auth,
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(base_utils.Format(listRsp))
}
