// protofsg -with_context -json_tag=1 elb.proto interface.go
option cc_generic_services = true;
package x1-base.sdk.elb;

import "x1-base/sdk/common/common.proto"
import "google/protobuf/descriptor.proto";

// ElbType -
extend google.protobuf.FileOptions {
	optional string ElbTypeNormal = 1;
	optional string ElbTypeIpV6   = 2;
}
option (ElbTypeNormal)  = "normal";
option (ElbTypeIpV6)    = "ipv6";

message Elb {
    optional string vip = 1;
    optional string id = 2;
    optional string name = 3;
    optional string ovip = 4;
    optional string type = 5;
    optional string ipv6 = 6;
}

message OperationResponse {
    optional string transactionId = 1;
    repeated Elb blbList = 2;
    optional bool success = 3;
    optional string requestId = 4;
    optional string code = 5;
    optional string message = 6;
}

message CreateELBRequest {
    //@inject_tag json:",omitempty"
    optional string action = 1 [default='create'];
    //internal false:内网 true:外网
    //@inject_tag json:",omitempty"
    optional string internal = 2 [default='false'];
    //@inject_tag json:",omitempty"
    optional int32 bandwidthInMbps = 3;
    //@inject_tag json:",omitempty"
    optional string name = 4;
    //@inject_tag json:",omitempty"
    optional string callback = 5;
    //@inject_tag json:",omitempty"
    optional int32 count = 6;
    //@inject_tag json:",omitempty"
    optional string source = 7;
    //@inject_tag json:",omitempty"
    optional string vpcId = 8;
    //@inject_tag json:",omitempty"
    optional string subnetId = 9;
    //@inject_tag json:",omitempty"
    optional bool is_vpc = 10;
    //@inject_tag json:",omitempty"
    optional string type = 11;
    //@inject_tag json:",omitempty"
    optional string bgwGroupId = 12;
    //@inject_tag json:",omitempty"
    optional bool bgwGroupExclusive = 13;
    //@inject_tag json:",omitempty"
    optional string masterAZ = 14;
    //@inject_tag json:",omitempty"
    optional string slaveAZ = 15;
    //@inject_tag json:",omitempty"
    optional string bgwGroupMode = 16;
    //@inject_tag json:"-"
    optional common.Authentication auth = 17;
}

message DeleteELBRequest {
    optional string action = 1 [default='delete'];
    optional string elb_id = 2;
    //@inject_tag json:"-"
    optional common.Authentication auth = 3;
}

message PauseELBRequest {
    optional string action = 1 [default='pause'];
    optional string elb_id = 2;
    //@inject_tag json:"-"
    optional common.Authentication auth = 3;
}

message RecoverELBRequest {
    optional string action = 1 [default='recover'];
    optional string elb_id = 2;
    //@inject_tag json:"-"
    optional common.Authentication auth = 3;
}

message BackendServer {
    optional string instanceId = 1;
    optional int32 port = 2;
    optional int32 weight = 3;
}

message CreateBackendServerRequest {
    optional string action  = 1 [default='create'];
    repeated BackendServer backendServerList = 2;
    //@inject_tag json:",omitempty"
    optional string callback = 3;
    //@inject_tag json:"-"
    optional string elb_id = 4;
    //@inject_tag json:"-"
    optional common.Authentication auth = 5;
}

message DeleteBackendServerRequest {
    optional string action = 1 [default='delete'];
    optional string backend_id = 2;
    //@inject_tag json:"-"
    optional string elb_id = 3;
    //@inject_tag json:"-"
    optional common.Authentication auth = 4;
}

message CreateListenerRequest {
    //@inject_tag json:",omitempty"
    optional string type = 1 [default='TCP'];
    //@inject_tag json:",omitempty"
    optional int32 port = 2;
    //@inject_tag json:",omitempty"
    optional int32 backendPort = 3;
    //@inject_tag json:",omitempty"
    optional string scheduler = 4 [default='RoundRobin'];
    //@inject_tag json:",omitempty"
    optional int32 healthCheckPort = 5;
    //@inject_tag json:",omitempty"
    optional string healthCheck = 6 [default='TCP'];
    //@inject_tag json:",omitempty"
    optional int32 healthCheckTimeoutInSecond = 7;
    //@inject_tag json:",omitempty"
    optional int32 healthCheckUpRetry = 8;
    //@inject_tag json:",omitempty"
    optional int32 healthCheckDownRetry = 9;
    //@inject_tag json:",omitempty"
    optional int32 healthCheckIntervalInSecond = 10;
    //@inject_tag json:",omitempty"
    optional string callback = 11;
    //@inject_tag json:",omitempty"
    optional int32 tcpSessionTimeout = 12;
    //@inject_tag json:"-"
    optional string elb_id = 13;
    //@inject_tag json:"-"
    optional common.Authentication auth = 14;
}

message DeleteListenerRequest {
    optional string action = 1 [default='delete'];
    optional int64 listener_port = 2;
    optional string elb_id = 3;
    //@inject_tag json:"-"
    optional common.Authentication auth = 4;
}

message CluterInfo {
    optional string instanceId  = 1;
    optional int32  clusterId = 2;
} 

message BindArg {
    optional string instanceType = 1 [default='BLB'];
    repeated string instanceIdList = 2;
    repeated CluterInfo clusterInfo = 3;
}

message StartELBRequest {
    optional string action = 1 [default='recover'];
    optional string elb_id = 2;
    //@inject_tag json:",omitempty"
    optional string callback = 3;
    //@inject_tag json:"-"
    optional common.Authentication auth = 4;
}

message StopELBRequest {
    optional string action = 1 [default='pause'];
    repeated string instanceidlist = 2;
    //@inject_tag json:",omitempty"
    optional string callback = 3;
    //@inject_tag json:"-"
    optional string elb_id = 4;
    //@inject_tag json:"-"
    optional common.Authentication auth = 5;
}

message ListElbRequest {
    optional string elb_id = 1;
    optional int32 port = 2;
    //@inject_tag json:"-"
    optional common.Authentication auth = 3;
}

message ListElbResponse {
    optional string status = 1;
    optional string vip = 2;
    optional string createTime = 3;
    optional bool internal = 4;
    optional string name = 5;
    optional string ovip = 6;
    optional string ipv6 = 7;
}

message ListListenerResponse {
    optional int32 backendPort = 1;
    optional string type = 2;
    optional string scheduler = 3;
    optional bool keepSession = 4;
    optional bool xForwardedFor = 5;
    optional string keepSessionParam = 6;
    optional string healthCheck = 7;
    optional string healthCheckPort = 8;
    optional string healthCheckTimeoutInSecond = 9;
    optional string healthCheckDownRetry = 10;
    optional string healthCheckUpRetry = 11;
    optional string healthCheckIntervalInSecond = 12;
    optional string healthCheackUrlPath = 13;
    optional string status = 14;
    optional string certs = 15;
    optional int32 tcpSessionTimeout = 16;
}

message PortList {
    optional int32 listenerPort = 1;
    optional int32 healthCheckPort = 2;
    optional string portType = 3;
    optional string status = 4;
}

message BackendServerList {
    optional string instanceId = 1;
    optional string weight = 2;
    repeated PortList portList = 3;
}

message ListBackendServerResponse {
    repeated BackendServerList backendServerList = 1;
}

message BindEipRequest {
    //@inject_tag json:",omitempty"
    optional string instanceType = 1;
    //@inject_tag json:",omitempty"
    optional string instanceId = 2;
    //@inject_tag json:",omitempty"
    optional string vpcId = 3;
    //@inject_tag json:",omitempty"
    optional string instanceIp = 4;
    //@inject_tag json:",omitempty"
    optional string oldInstanceId = 5;
    //@inject_tag json:",omitempty"
    optional string instanceInternalId = 6;
    //@inject_tag json:",omitempty"
    optional string instanceInternalType = 7;
    //@inject_tag json:",omitempty"
    optional string pnetIp = 8;
    //@inject_tag json:",omitempty"
    optional string callback = 9;
    //@inject_tag json:"-"
    optional string eip = 10;
    //@inject_tag json:"-"
    optional common.Authentication auth = 17;
}

message BindEipResponse {
    optional string transactionId = 1;
    optional bool success = 2;
    optional string code = 3;
    optional string message = 4;
}

message UnbindEipRequest {
    //@inject_tag json:",omitempty"
    optional string callback = 1;
    //@inject_tag json:"-"
    optional string eip = 2;
    //@inject_tag json:"-"
    optional common.Authentication auth = 17;
}

message UnbindEipResponse {
    optional string transactionId = 1;
    optional bool success = 2;
    optional string code = 3;
    optional string message = 4;
}

message BindQueryRequest {
    //@inject_tag json:"-"
    optional string eip = 1;
    //@inject_tag json:"-"
    optional common.Authentication auth = 17;
}

message BindQueryResponse {
    optional string instanceType = 1;
    optional string instanceId = 2;
    optional string pnetIp = 3;
    optional string fixedIp = 4;
    optional string id = 5;
    optional string name = 6;
    optional string eip = 7;
    optional string allocationId = 8;
    optional string status = 9;
    optional int32 pauseMask = 10;
    optional int32 bandwidthInMbps = 11;
    optional int32 inBandwidthInMbps = 12;
    optional string eipType = 13;
    optional string shareGroupId = 14;
    optional string type = 15;
    optional string source = 16;
    optional int32 onlyTcpOut = 17;
    optional int32 isHighDefense = 18;
    optional int32 hDefenseBandwidthInMbps = 19;
    optional string ipv6 = 20;
}


service ELBService {
    rpc CreateELB(CreateELBRequest) returns (OperationResponse);
    rpc DeleteELB(DeleteELBRequest) returns (OperationResponse);
    rpc StartELB(StartELBRequest) returns (OperationResponse);
    rpc StopELB(StopELBRequest) returns (OperationResponse);
    rpc CreateListener(CreateListenerRequest) returns (OperationResponse);
    rpc DeleteListener(DeleteListenerRequest) returns (OperationResponse);
    rpc CreateBackendServer(CreateBackendServerRequest) returns (OperationResponse);
    rpc DeleteBackendServer(DeleteBackendServerRequest) returns (OperationResponse);
    rpc list_elb(ListElbRequest) returns (ListElbResponse);
    rpc list_listener(ListElbRequest) returns (ListListenerResponse);
    rpc list_backend_server(ListElbRequest) returns (ListBackendServerResponse);
    rpc bind_eip(BindEipRequest) returns (BindEipResponse);
    rpc query_eip(BindQueryRequest) returns (BindQueryResponse);
    rpc unbind_eip(UnbindEipRequest) returns (UnbindEipResponse);
};
