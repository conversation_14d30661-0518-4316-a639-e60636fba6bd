/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-28
 * File: elb_sdk.go
 */

/*
 * DESCRIPTION
 *   根据elb.proto生成的implement文件
 */

// Package elb
package elb

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

type elbSdk struct {
	conf *elbConf
	common.OpenApi
}

func NewDefaultElbSdk() ELBService {
	return newElbSdk(DefaultServiceName)
}

func NewElbSdk(serviceName string) ELBService {
	return newElbSdk(serviceName)
}

func newElbSdk(serviceName string) *elbSdk {
	s := &elbSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}

	return s
}

func (s *elbSdk) CreateELB(ctx context.Context, req *CreateELBRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	req.Action = "create"
	params := &common.OpenApiParams{
		ActionName: "CreateELB",
		Auth:       req.Auth,
		HttpMethod: http.MethodPost,
		Uri:        elbUriForCreate,
		Posts:      req,
		Product:    s.conf.Product,
	}

	rsp = &OperationResponse{}
	err = cerrs.ErrELBOperationFail.Wrap(s.DoRequest(ctx, params, rsp))
	return
}

func (s *elbSdk) DeleteELB(ctx context.Context, req *DeleteELBRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	params := &common.OpenApiParams{
		ActionName: "DeleteELB",
		Auth:       req.Auth,
		HttpMethod: http.MethodDelete,
		Uri:        elbClusterUri + "/" + req.ElbId,
		Product:    s.conf.Product,
	}

	rsp = &OperationResponse{}
	err = cerrs.ErrELBOperationFail.Wrap(s.DoRequest(ctx, params, rsp))
	return
}

func (s *elbSdk) StartELB(ctx context.Context, req *StartELBRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	req.Action = "recover"
	params := &common.OpenApiParams{
		ActionName: "StartELB",
		Auth:       req.Auth,
		HttpMethod: http.MethodPut,
		Uri:        elbClusterUri + "/" + req.ElbId,
		Queries: map[string]interface{}{
			"action": "recover",
		},
		Posts: req,
	}

	rsp = &OperationResponse{}
	err = cerrs.ErrELBOperationFail.Wrap(s.DoRequest(ctx, params, rsp))
	return
}

func (s *elbSdk) StopELB(ctx context.Context, req *StopELBRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	req.Action = "pause"
	params := &common.OpenApiParams{
		ActionName: "StopELB",
		Auth:       req.Auth,
		HttpMethod: http.MethodPut,
		Uri:        elbClusterUri + "/" + req.ElbId,
		Queries: map[string]interface{}{
			"action": "pause",
		},
		Posts:   req,
		Product: s.conf.Product,
	}

	rsp = &OperationResponse{}
	err = cerrs.ErrELBOperationFail.Wrap(s.DoRequest(ctx, params, rsp))
	return
}

func (s *elbSdk) CreateListener(ctx context.Context, req *CreateListenerRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	params := &common.OpenApiParams{
		ActionName: "CreateListener",
		Auth:       req.Auth,
		HttpMethod: http.MethodPost,
		Uri:        elbClusterUri + "/" + req.ElbId + "/listener",
		Posts:      req,
		Product:    s.conf.Product,
	}

	rsp = &OperationResponse{}
	err = cerrs.ErrELBOperationFail.Wrap(s.DoRequest(ctx, params, rsp))
	return
}

func (s *elbSdk) DeleteListener(ctx context.Context, req *DeleteListenerRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	params := &common.OpenApiParams{
		ActionName: "DeleteListener",
		Auth:       req.Auth,
		HttpMethod: http.MethodDelete,
		Uri:        elbClusterUri + "/" + req.ElbId + "/listener/" + fmt.Sprintf("%d", req.ListenerPort),
		Product:    s.conf.Product,
	}

	rsp = &OperationResponse{}
	err = cerrs.ErrELBOperationFail.Wrap(s.DoRequest(ctx, params, rsp))
	return
}

func (s *elbSdk) CreateBackendServer(ctx context.Context, req *CreateBackendServerRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	req.Action = "create"
	params := &common.OpenApiParams{
		ActionName: "CreateBackendServer",
		Auth:       req.Auth,
		HttpMethod: http.MethodPost,
		Uri:        elbClusterUri + "/" + req.ElbId + "/backendserver",
		Posts:      req,
		Product:    s.conf.Product,
	}

	rsp = &OperationResponse{}
	err = s.DoRequest(ctx, params, rsp,
		sdk_utils.ROptPrepareChecker(
			func(ctx context.Context, httpRsp *sdk_utils.HttpResponse, invoker sdk_utils.PrepareCheckFunc) error {
				err := invoker(ctx, httpRsp)
				if err != nil && httpRsp.StatusCode == http.StatusConflict {
					time.Sleep(100 * time.Millisecond)
					return cerrs.ErrHttpStatusError.Errorf(httpRsp.Status)
				}
				return err
			}))
	err = cerrs.ErrELBOperationFail.Wrap(err)
	return
}

func (s *elbSdk) DeleteBackendServer(ctx context.Context, req *DeleteBackendServerRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	params := &common.OpenApiParams{
		ActionName: "DeleteBackendServer",
		Auth:       req.Auth,
		HttpMethod: http.MethodDelete,
		Uri:        elbClusterUri + "/" + req.ElbId + "/backendserver/" + req.BackendId,
		Product:    s.conf.Product,
	}

	rsp = &OperationResponse{}
	err = cerrs.ErrELBOperationFail.Wrap(s.DoRequest(ctx, params, rsp))
	return
}

func (s *elbSdk) ListElb(ctx context.Context, req *ListElbRequest) (rsp *ListElbResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	params := &common.OpenApiParams{
		ActionName: "ListElb",
		Auth:       req.Auth,
		HttpMethod: http.MethodGet,
		Uri:        elbClusterUri + "/" + req.ElbId,
		Product:    s.conf.Product,
	}

	rsp = &ListElbResponse{}
	err = cerrs.ErrELBQueryFail.Wrap(s.DoRequest(ctx, params, rsp))
	return
}

func (s *elbSdk) ListListener(ctx context.Context, req *ListElbRequest) (rsp *ListListenerResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	params := &common.OpenApiParams{
		ActionName: "ListListener",
		Auth:       req.Auth,
		HttpMethod: http.MethodGet,
		Uri:        elbClusterUri + "/" + req.ElbId + "/listener/" + fmt.Sprintf("%d", req.Port),
		Product:    s.conf.Product,
	}

	rsp = &ListListenerResponse{}
	err = cerrs.ErrELBQueryFail.Wrap(s.DoRequest(ctx, params, rsp))
	return
}

func (s *elbSdk) ListBackendServer(ctx context.Context, req *ListElbRequest) (rsp *ListBackendServerResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	params := &common.OpenApiParams{
		ActionName: "ListBackendServer",
		Auth:       req.Auth,
		HttpMethod: http.MethodGet,
		Uri:        elbClusterUri + "/" + req.ElbId + "/backendserver",
		Product:    s.conf.Product,
	}

	rsp = &ListBackendServerResponse{}
	err = cerrs.ErrELBQueryFail.Wrap(s.DoRequest(ctx, params, rsp))
	return
}

func (s *elbSdk) BindEip(ctx context.Context, req *BindEipRequest) (rsp *BindEipResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	params := &common.OpenApiParams{
		ActionName: "bind_eip",
		Auth:       req.Auth,
		HttpMethod: http.MethodPut,
		Uri:        eipClusterUri + "/" + req.Eip,
		Queries: map[string]interface{}{
			"action": "bind",
		},
		Posts:   req,
		Product: s.conf.Product,
	}

	rsp = &BindEipResponse{}
	err = cerrs.ErrEIPOpFail.Wrap(s.DoRequest(ctx, params, rsp))
	return
}

func (s *elbSdk) QueryEip(ctx context.Context, req *BindQueryRequest) (rsp *BindQueryResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	params := &common.OpenApiParams{
		ActionName: "bind_query_eip",
		Auth:       req.Auth,
		HttpMethod: http.MethodGet,
		Uri:        eipClusterUri + "/" + req.Eip,
		Product:    s.conf.Product,
	}

	rsp = &BindQueryResponse{}
	err = cerrs.ErrEIPQueryFail.Wrap(s.DoRequest(ctx, params, rsp))
	return
}

func (s *elbSdk) UnbindEip(ctx context.Context, req *UnbindEipRequest) (rsp *UnbindEipResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	params := &common.OpenApiParams{
		ActionName: "unbind_eip",
		Auth:       req.Auth,
		HttpMethod: http.MethodPut,
		Uri:        eipClusterUri + "/" + req.Eip,
		Queries: map[string]interface{}{
			"action": "unbind",
		},
		Posts:   req,
		Product: s.conf.Product,
	}

	rsp = &UnbindEipResponse{}
	err = cerrs.ErrEIPOpFail.Wrap(s.DoRequest(ctx, params, rsp))
	return
}

func (s *elbSdk) UpdateBackendServer(ctx context.Context, req *UpdateBackendServerRequest) (rsp *OperationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	params := &common.OpenApiParams{
		ActionName: "UpdateBackendServer",
		Auth:       req.Auth,
		HttpMethod: http.MethodPut,
		Uri:        elbClusterUri + "/" + req.ElbId + "/backendservers",
		Queries: map[string]interface{}{
			"action": "batchupdate",
		},
		Posts:   req,
		Product: s.conf.Product,
	}

	rsp = &OperationResponse{}
	err = s.DoRequest(ctx, params, rsp,
		sdk_utils.ROptPrepareChecker(
			func(ctx context.Context, httpRsp *sdk_utils.HttpResponse, invoker sdk_utils.PrepareCheckFunc) error {
				err := invoker(ctx, httpRsp)
				if err != nil && httpRsp.StatusCode == http.StatusConflict {
					time.Sleep(100 * time.Millisecond)
					return cerrs.ErrHttpStatusError.Errorf(httpRsp.Status)
				}
				return err
			}))
	err = cerrs.ErrELBOperationFail.Wrap(err)
	return
}
