/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-03-23
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据elb.proto生成的interface文件
 */

// Package elb
package elb

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

// ElbType -
const (
	ElbTypeNormal = "normal"
	ElbTypeIpV6   = "ipv6"
)

type Elb struct {
	Vip  string `json:"vip"`
	Id   string `json:"id"`
	Name string `json:"name"`
	Ovip string `json:"ovip"`
	Type string `json:"type"`
	Ipv6 string `json:"ipv6"`
}

type OperationResponse struct {
	TransactionId string `json:"transactionId"`
	BlbList       []*Elb `json:"blbList"`
	Success       bool   `json:"success"`
	RequestId     string `json:"requestId"`
	Code          string `json:"code"`
	Message       string `json:"message"`
}

type CreateELBRequest struct {
	Action string `json:"action,omitempty"`
	// Internal false:内网 true:外网
	Internal          string                 `json:"internal,omitempty"`
	BandwidthInMbps   int32                  `json:"bandwidthInMbps,omitempty"`
	Name              string                 `json:"name,omitempty"`
	Callback          string                 `json:"callback,omitempty"`
	Count             int32                  `json:"count,omitempty"`
	Source            string                 `json:"source,omitempty"`
	VpcId             string                 `json:"vpcId,omitempty"`
	SubnetId          string                 `json:"subnetId,omitempty"`
	IsVpc             bool                   `json:"is_vpc,omitempty"`
	Type              string                 `json:"type,omitempty"`
	BgwGroupId        string                 `json:"bgwGroupId,omitempty"`
	BgwGroupExclusive bool                   `json:"bgwGroupExclusive,omitempty"`
	MasterAZ          string                 `json:"masterAZ,omitempty"`
	SlaveAZ           string                 `json:"slaveAZ,omitempty"`
	BgwGroupMode      string                 `json:"bgwGroupMode,omitempty"`
	Auth              *common.Authentication `json:"-"`
}

type DeleteELBRequest struct {
	Action string                 `json:"action"`
	ElbId  string                 `json:"elb_id"`
	Auth   *common.Authentication `json:"-"`
}

type PauseELBRequest struct {
	Action string                 `json:"action"`
	ElbId  string                 `json:"elb_id"`
	Auth   *common.Authentication `json:"-"`
}

type RecoverELBRequest struct {
	Action string                 `json:"action"`
	ElbId  string                 `json:"elb_id"`
	Auth   *common.Authentication `json:"-"`
}

type BackendServer struct {
	InstanceId string `json:"instanceId"`
	Port       int32  `json:"port"`
	Weight     int32  `json:"weight"`
}

type CreateBackendServerRequest struct {
	Action            string                 `json:"action"`
	BackendServerList []*BackendServer       `json:"backendServerList"`
	Callback          string                 `json:"callback,omitempty"`
	ElbId             string                 `json:"-"`
	Auth              *common.Authentication `json:"-"`
}

type UpdateBackendServerRequest struct {
	BackendServerList []*CommonBackendServer `json:"backendServerList"`
	Callback          string                 `json:"callback,omitempty"`
	ElbId             string                 `json:"-"`
	Auth              *common.Authentication `json:"-"`
}

// CommonBackendServer definition
type CommonBackendServer struct {
	InstanceId string `json:"instanceId"`
	Weight     int32  `json:"weight"`
}

type DeleteBackendServerRequest struct {
	Action    string                 `json:"action"`
	BackendId string                 `json:"backend_id"`
	ElbId     string                 `json:"-"`
	Auth      *common.Authentication `json:"-"`
}

type CreateListenerRequest struct {
	Type                        string                 `json:"type,omitempty"`
	Port                        int32                  `json:"port,omitempty"`
	BackendPort                 int32                  `json:"backendPort,omitempty"`
	Scheduler                   string                 `json:"scheduler,omitempty"`
	HealthCheckPort             int32                  `json:"healthCheckPort,omitempty"`
	HealthCheck                 string                 `json:"healthCheck,omitempty"`
	HealthCheckTimeoutInSecond  int32                  `json:"healthCheckTimeoutInSecond,omitempty"`
	HealthCheckUpRetry          int32                  `json:"healthCheckUpRetry,omitempty"`
	HealthCheckDownRetry        int32                  `json:"healthCheckDownRetry,omitempty"`
	HealthCheckIntervalInSecond int32                  `json:"healthCheckIntervalInSecond,omitempty"`
	Callback                    string                 `json:"callback,omitempty"`
	TcpSessionTimeout           int32                  `json:"tcpSessionTimeout,omitempty"`
	ElbId                       string                 `json:"-"`
	Auth                        *common.Authentication `json:"-"`
}

type DeleteListenerRequest struct {
	Action       string                 `json:"action"`
	ListenerPort int64                  `json:"listener_port"`
	ElbId        string                 `json:"elb_id"`
	Auth         *common.Authentication `json:"-"`
}

type CluterInfo struct {
	InstanceId string `json:"instanceId"`
	ClusterId  int32  `json:"clusterId"`
}

type BindArg struct {
	InstanceType   string        `json:"instanceType"`
	InstanceIdList []string      `json:"instanceIdList"`
	ClusterInfo    []*CluterInfo `json:"clusterInfo"`
}

type StartELBRequest struct {
	Action   string                 `json:"action"`
	ElbId    string                 `json:"elb_id"`
	Callback string                 `json:"callback,omitempty"`
	Auth     *common.Authentication `json:"-"`
}

type StopELBRequest struct {
	Action         string                 `json:"action"`
	Instanceidlist []string               `json:"instanceidlist"`
	Callback       string                 `json:"callback,omitempty"`
	ElbId          string                 `json:"-"`
	Auth           *common.Authentication `json:"-"`
}

type ListElbRequest struct {
	ElbId string                 `json:"elb_id"`
	Port  int32                  `json:"port"`
	Auth  *common.Authentication `json:"-"`
}

type ListElbResponse struct {
	Status     string `json:"status"`
	Vip        string `json:"vip"`
	CreateTime string `json:"createTime"`
	Internal   bool   `json:"internal"`
	Name       string `json:"name"`
	Ovip       string `json:"ovip"`
	Ipv6       string `json:"ipv6"`
}

type ListListenerResponse struct {
	BackendPort                 int32  `json:"backendPort"`
	Type                        string `json:"type"`
	Scheduler                   string `json:"scheduler"`
	KeepSession                 bool   `json:"keepSession"`
	XForwardedFor               bool   `json:"xForwardedFor"`
	KeepSessionParam            string `json:"keepSessionParam"`
	HealthCheck                 string `json:"healthCheck"`
	HealthCheckPort             int32  `json:"healthCheckPort"`
	HealthCheckTimeoutInSecond  int32  `json:"healthCheckTimeoutInSecond"`
	HealthCheckDownRetry        int32  `json:"healthCheckDownRetry"`
	HealthCheckUpRetry          int32  `json:"healthCheckUpRetry"`
	HealthCheckIntervalInSecond int32  `json:"healthCheckIntervalInSecond"`
	HealthCheackUrlPath         string `json:"healthCheackUrlPath"`
	Status                      string `json:"status"`
	Certs                       string `json:"certs"`
	TcpSessionTimeout           int32  `json:"tcpSessionTimeout"`
}

type PortList struct {
	ListenerPort    int32  `json:"listenerPort"`
	HealthCheckPort int32  `json:"healthCheckPort"`
	PortType        string `json:"portType"`
	Status          string `json:"status"`
}

type BackendServerList struct {
	InstanceId string      `json:"instanceId"`
	Weight     string      `json:"weight"`
	PortList   []*PortList `json:"portList"`
}

type ListBackendServerResponse struct {
	BackendServerList []*BackendServerList `json:"backendServerList"`
}

type BindEipRequest struct {
	InstanceType         string                 `json:"instanceType,omitempty"`
	InstanceId           string                 `json:"instanceId,omitempty"`
	VpcId                string                 `json:"vpcId,omitempty"`
	InstanceIp           string                 `json:"instanceIp,omitempty"`
	OldInstanceId        string                 `json:"oldInstanceId,omitempty"`
	InstanceInternalId   string                 `json:"instanceInternalId,omitempty"`
	InstanceInternalType string                 `json:"instanceInternalType,omitempty"`
	PnetIp               string                 `json:"pnetIp,omitempty"`
	Callback             string                 `json:"callback,omitempty"`
	Eip                  string                 `json:"-"`
	Auth                 *common.Authentication `json:"-"`
}

type BindEipResponse struct {
	TransactionId string `json:"transactionId"`
	Success       bool   `json:"success"`
	Code          string `json:"code"`
	Message       string `json:"message"`
}

type UnbindEipRequest struct {
	Callback string                 `json:"callback,omitempty"`
	Eip      string                 `json:"-"`
	Auth     *common.Authentication `json:"-"`
}

type UnbindEipResponse struct {
	TransactionId string `json:"transactionId"`
	Success       bool   `json:"success"`
	Code          string `json:"code"`
	Message       string `json:"message"`
}

type BindQueryRequest struct {
	Eip  string                 `json:"-"`
	Auth *common.Authentication `json:"-"`
}

type BindQueryResponse struct {
	InstanceType            string `json:"instanceType"`
	InstanceId              string `json:"instanceId"`
	PnetIp                  string `json:"pnetIp"`
	FixedIp                 string `json:"fixedIp"`
	Id                      string `json:"id"`
	Name                    string `json:"name"`
	Eip                     string `json:"eip"`
	AllocationId            string `json:"allocationId"`
	Status                  string `json:"status"`
	PauseMask               int32  `json:"pauseMask"`
	BandwidthInMbps         int32  `json:"bandwidthInMbps"`
	InBandwidthInMbps       int32  `json:"inBandwidthInMbps"`
	EipType                 string `json:"eipType"`
	ShareGroupId            string `json:"shareGroupId"`
	Type                    string `json:"type"`
	Source                  string `json:"source"`
	OnlyTcpOut              int32  `json:"onlyTcpOut"`
	IsHighDefense           int32  `json:"isHighDefense"`
	HDefenseBandwidthInMbps int32  `json:"hDefenseBandwidthInMbps"`
	Ipv6                    string `json:"ipv6"`
}

type ELBService interface {
	CreateELB(ctx context.Context, req *CreateELBRequest) (rsp *OperationResponse, err error)
	DeleteELB(ctx context.Context, req *DeleteELBRequest) (rsp *OperationResponse, err error)
	StartELB(ctx context.Context, req *StartELBRequest) (rsp *OperationResponse, err error)
	StopELB(ctx context.Context, req *StopELBRequest) (rsp *OperationResponse, err error)
	CreateListener(ctx context.Context, req *CreateListenerRequest) (rsp *OperationResponse, err error)
	DeleteListener(ctx context.Context, req *DeleteListenerRequest) (rsp *OperationResponse, err error)
	CreateBackendServer(ctx context.Context, req *CreateBackendServerRequest) (rsp *OperationResponse, err error)
	DeleteBackendServer(ctx context.Context, req *DeleteBackendServerRequest) (rsp *OperationResponse, err error)
	ListElb(ctx context.Context, req *ListElbRequest) (rsp *ListElbResponse, err error)
	ListListener(ctx context.Context, req *ListElbRequest) (rsp *ListListenerResponse, err error)
	ListBackendServer(ctx context.Context, req *ListElbRequest) (rsp *ListBackendServerResponse, err error)
	BindEip(ctx context.Context, req *BindEipRequest) (rsp *BindEipResponse, err error)
	QueryEip(ctx context.Context, req *BindQueryRequest) (rsp *BindQueryResponse, err error)
	UnbindEip(ctx context.Context, req *UnbindEipRequest) (rsp *UnbindEipResponse, err error)
	UpdateBackendServer(ctx context.Context, req *UpdateBackendServerRequest) (rsp *OperationResponse, err error)
}
