package bns

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"net"
	"net/http"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

// 创建bns
func TestBnsSdk_BnsServiceCreate(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&BnsGeneralResponse{
		RetCode: 0,
		Msg:     "success",
	}, nil, http.StatusOK))
	_, err := s.BnsServiceCreate(ctx, &BnsCreateRequest{
		ParentPath:  "TEST",
		<PERSON>th<PERSON><PERSON>:     "TEST",
		NodeName:    "TEST",
		RunUser:     "work",
		ServiceConf: "test",
		IsMatrixSu:  0,
	})
	if err != nil {
		t.Errorf("[%s] test create bns service success fail", t.Name())
		return
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&BnsGeneralResponse{
		RetCode: -1,
		Msg:     "cluster-200000096-proxysandbox.BCE.all already exists in bns",
	}, nil, http.StatusOK))
	_, err = s.BnsServiceCreate(ctx, &BnsCreateRequest{
		ParentPath:  "TEST",
		AuthKey:     "TEST",
		NodeName:    "TEST",
		RunUser:     "work",
		ServiceConf: "test",
		IsMatrixSu:  0,
	})
	if err == nil || !cerrs.Is(err, cerrs.ErrBNSRequestAlreadyExist) {
		t.Errorf("[%s] test create bns service already exist fail", t.Name())
		return
	}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&BnsGeneralResponse{
		RetCode: -1,
		Msg:     "cluster-200000096-proxysandbox.BCE.all ",
	}, nil, http.StatusOK))
	_, err = s.BnsServiceCreate(ctx, &BnsCreateRequest{
		ParentPath:  "TEST",
		AuthKey:     "TEST",
		NodeName:    "TEST",
		RunUser:     "work",
		ServiceConf: "test",
		IsMatrixSu:  0,
	})
	if err == nil || cerrs.Is(err, cerrs.ErrBNSRequestAlreadyExist) {
		t.Errorf("[%s] test create bns service other error fail", t.Name())
		return
	}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&BnsGeneralResponse{
		RetCode: 0,
		Msg:     "",
	}, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	_, err = s.BnsServiceCreate(ctx, &BnsCreateRequest{
		ParentPath:  "TEST",
		AuthKey:     "TEST",
		NodeName:    "TEST",
		RunUser:     "work",
		ServiceConf: "test",
		IsMatrixSu:  0,
	})
	if err == nil {
		fmt.Println(err)
		t.Errorf("[%s] test create bns service network error", t.Name())
		return
	}
}

// 删除bns
func TestBnsSdk_BnsServiceDelete(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&BnsGeneralResponse{
		RetCode: 0,
		Msg:     "success",
	}, nil, http.StatusOK))
	_, err := s.BnsServiceDelete(ctx, &BnsDeleteRequest{
		AuthKey:     "TEST",
		ServiceName: "TEST",
	})
	if err != nil {
		t.Errorf("[%s] test delete bns service success fail", t.Name())
		return
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&BnsGeneralResponse{
		RetCode: -1,
		Msg:     "cluster-2000000-proxysandbox.BCE.all is not exist",
	}, nil, http.StatusOK))
	_, err = s.BnsServiceDelete(ctx, &BnsDeleteRequest{
		AuthKey:     "TEST",
		ServiceName: "TEST",
	})
	if err == nil || !cerrs.Is(err, cerrs.ErrBNSRequestNotFound) {
		t.Errorf("[%s] test delete bns service not found fail", t.Name())
		return
	}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&BnsGeneralResponse{
		RetCode: -1,
		Msg:     "cluster-2000000-proxysandbox.BCE.all is aaa",
	}, nil, http.StatusOK))
	_, err = s.BnsServiceDelete(ctx, &BnsDeleteRequest{
		AuthKey:     "TEST",
		ServiceName: "TEST",
	})
	if err == nil || cerrs.Is(err, cerrs.ErrBNSRequestNotFound) {
		t.Errorf("[%s] test delete bns service other error fail", t.Name())
		return
	}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&BnsGeneralResponse{
		RetCode: 0,
		Msg:     "",
	}, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	_, err = s.BnsServiceDelete(ctx, &BnsDeleteRequest{
		AuthKey:     "TEST",
		ServiceName: "TEST",
	})
	if err == nil {
		t.Errorf("[%s] test delete bns service network error", t.Name())
		return
	}
}

// 创建node
func TestBnsSdk_BnsNodeServiceCreate(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(SpecialServiceName)

	createRsp, err := s.BnsNodeCreate(ctx, &BnsNodeCreateRequest{
		ParentPath: "BAIDU_DBA_DBA-BDRP_redis_raoalong-dev-test",
		Token:      "0tVUYOby5ofzmmuRDx3ZScWTx9lKBLqN",
		NodeName:   "ral-test1",
		Type:       "service",
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("bnsNodeCreate resp:", base_utils.Format(createRsp))
}

// 添加单个实例
func TestBnsSdk_BnsAddInstance(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(DefaultServiceName)
	req := &BnsAddInstanceRequest{
		ServiceName: "zhangxuepeng.redis.all",
		AuthKey:     "5qCNPem7cm23SIoBZTX0pfJkx5HfOjEQ",
		InstanceInfo: &BnsInstanceInfo{
			HostName: "************",
			Port: map[string]string{
				"main": "8500",
				"test": "7787",
			},
			Tag:             nil,
			Disable:         0,
			InstanceId:      1999,
			Status:          0,
			DeployPath:      "/root/xagent",
			RunUser:         "root",
			HealthCheckCmd:  "",
			HealthCheckType: "proc",
			ContainerId:     "",
		},
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&BnsAddInstanceResponse{
		RetCode: 0,
		Msg:     "success",
	}, nil, http.StatusOK))
	_, err := s.BnsServiceAddInstance(ctx, req)
	if err != nil {
		t.Errorf("[%s] test bns service add instance success fail", t.Name())
		return
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&BnsAddInstanceResponse{
		RetCode: -1,
		Msg:     "1993.cluster-200000096-proxysandbox.BCE.all already exists",
	}, nil, http.StatusOK))
	_, err = s.BnsServiceAddInstance(ctx, req)
	if err == nil || !cerrs.Is(err, cerrs.ErrBNSRequestAlreadyExist) {
		t.Errorf("[%s] test bns service add instance already exist fail", t.Name())
		return
	}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&BnsAddInstanceResponse{
		RetCode: -1,
		Msg:     "cluster-2000000-proxysandbox.BCE.all is aaa",
	}, nil, http.StatusOK))
	_, err = s.BnsServiceAddInstance(ctx, req)
	if err == nil || cerrs.Is(err, cerrs.ErrBNSRequestNotFound) {
		t.Errorf("[%s] test bns service add instance other error fail", t.Name())
		return
	}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&BnsAddInstanceResponse{
		RetCode: 0,
		Msg:     "",
	}, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	_, err = s.BnsServiceAddInstance(ctx, req)
	if err == nil {
		t.Errorf("[%s] test bns service add instance network error", t.Name())
		return
	}
}

func TestBnsSdk_BnsAddInstance1(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(DefaultServiceName)

	createRsp, err := s.BnsServiceAddInstance(ctx, &BnsAddInstanceRequest{
		ServiceName: "cluster-200000096-proxysandbox.BCE.all",
		AuthKey:     "********************************",
		InstanceInfo: &BnsInstanceInfo{
			HostName: "************2",
			Port: map[string]string{
				"main": "8500",
			},
			Tag:             nil,
			Disable:         0,
			InstanceId:      1993,
			Status:          0,
			DeployPath:      "/root/xagent",
			RunUser:         "work",
			HealthCheckCmd:  "",
			HealthCheckType: "proc",
			ContainerId:     "",
		},
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("bnsServiceAddInstance resp:", base_utils.Format(createRsp))
}

// 清空服务单元实例
func TestBnsSdk_BnsClearInstance(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(DefaultServiceName)
	rsp, err := s.BnsServiceClearInstance(ctx, &BnsClearInstanceRequest{
		ServiceName: "raoalong-dev-test.redis.gz",
		AuthKey:     "0tVUYOby5ofzmmuRDx3ZScWTx9lKBLqN",
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("bnsServiceClearInstance resp:", base_utils.Format(rsp))
}

// 删除实例
func TestBnsSdk_BnsDeleteInstance(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(DefaultServiceName)
	rsp, err := s.BnsServiceDeleteInstance(ctx, &BnsDeleteInstanceRequest{
		ServiceName: "raoalong-dev-test.redis.gz",
		AuthKey:     "0tVUYOby5ofzmmuRDx3ZScWTx9lKBLqN",
		HostName:    "nj02-bdrp-share59.nj02",
		InstanceId:  57,
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("bnsServiceDeleteInstance resp:", base_utils.Format(rsp))
}

// bns group修改服务组的服务列表
func TestBnsSdk_BnsGroupModifyServices(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(DefaultServiceName)
	rsp, err := s.BnsGroupModifyServices(ctx, &BnsGroupModifyServicesRequest{
		GroupName: "group.bdrp-raoalong-dev-test-proxy.MAP.all",
		ServiceNames: []string{
			"raoalong-dev-test.redis.nj",
			"raoalong-dev-test.redis.gz",
		},
		AuthKey: "0tVUYOby5ofzmmuRDx3ZScWTx9lKBLqN",
		Action:  "remove",
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("bnsGroupModifyServices resp:", base_utils.Format(rsp))
}

// 生成默认idc map
func TestBnsSdk_BnsGenerateDefaultIdcMap(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(DefaultServiceName)
	rsp, err := s.GenerateDefaultIdcMap(&BnsGroupConfList{
		GroupName: "group.bdrp-raoalong-dev-test666-proxy.redis.all",
		IdcMap: map[string]IdcMapConf{
			"test": {
				Prefer: "bj",
				Backup: "nj",
			},
		},
		ProtocolName:              "testProtocol",
		ConverterName:             "testConverter",
		ServiceRtimeout:           10,
		ServiceCtimeout:           5,
		ServiceWtimeout:           30,
		ServiceConnType:           2,
		ServiceRetry:              6,
		ConnectQueueSize:          20,
		HealthyBackupThreshold:    5,
		HealthyQueueSize:          7,
		HealthyCheckTime:          2,
		Hybrid:                    "H",
		HealthyTimeout:            8,
		CrossRoom:                 "Cross",
		ConnectX2:                 7,
		ConnectX1:                 6,
		ConnectY1:                 5,
		ConnectY2:                 4,
		HealthyMinRate:            9,
		Balance:                   "Bl",
		ServiceEnableConnectRetry: "YES",
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("generateDefaultIdcMap resp:", base_utils.Format(rsp))
}

// 创建bns group
func TestBnsSdk_BnsGroupCreate(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(DefaultServiceName)

	rsp, err := s.BnsGroupCreate(ctx, &BnsGroupCreateRequest{
		ServiceNames: []string{
			"raoalong-dev-test.redis.nj",
			"raoalong-dev-test.redis.bj",
		},
		NodePath:         "BAIDU_DBA_DBA-BDRP_redis_raoalong-dev-test",
		AuthKey:          "0tVUYOby5ofzmmuRDx3ZScWTx9lKBLqN",
		Threshold:        0,
		ThresholdPercent: 0,
		GroupConf: &BnsGroupConfList{
			GroupName: "group.bdrp-raoalong-dev-test888-proxy.redis.all",
		},
		ConfIsJson:        1,
		ConstrainGroup:    0,
		OpenDeadhostCheck: 0,
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("bnsGroupCreate resp:", base_utils.Format(rsp))
}

func TestBnsSdk_BnsGroupDelete(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(DefaultServiceName)
	rsp, err := s.BnsGroupDelete(ctx, &BnsGroupDeleteRequest{
		GroupName: "group.bdrp-raoalong-dev-test-proxy.MAP.all",
		AuthKey:   "0tVUYOby5ofzmmuRDx3ZScWTx9lKBLqN",
		Limit:     false,
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("bnsGroupDelete resp:", base_utils.Format(rsp))
}

func TestBnsSdk_BnsServiceInstanceInfo(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(DefaultServiceName)
	rsp, err := s.BnsServiceInstanceInfo(ctx, &BnsServiceInstanceInfoRequest{
		ServiceName: "raoalong-dev-test.redis.nj",
		AuthKey:     "0tVUYOby5ofzmmuRDx3ZScWTx9lKBLqN",
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("bnsServiceInstanceInfo resp:", base_utils.Format(rsp))
}

// 根据container_id屏蔽|解屏蔽节点
func TestBnsSdk_DisableInstance(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(DefaultServiceName)
	rsp, err := s.BatchDisableAndEnableInstancesByContainerId(ctx, &InstanceDisableAndEnableRequest{
		ServiceName:  "bdrp-bdrp-ral-test-proxy.redis.hk",
		AuthKey:      "0tVUYOby5ofzmmuRDx3ZScWTx9lKBLqN",
		Enable:       "enable",
		ContainerIds: "1.opera-online-BDRPA1gnProxyBdrpdiskhdd1000-000-hk.redis.hk,0.opera-online-BDRPA1gnProxyBdrpdiskhdd1000-000-hk.redis.hk",
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("batchDisableAndEnableInstancesByContainerId resp:", base_utils.Format(rsp))
}

func Test_bnsSdk_BnsNodeInfo(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(SpecialServiceName)

	infoResp, err := s.BnsNodeInfo(ctx, &BnsNodeInfoRequest{
		FullPath: "BAIDU_DBA_DBA-BDRP_redis_raoalong-dev-test_ral-test1",
		Token:    "0tVUYOby5ofzmmuRDx3ZScWTx9lKBLqN",
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("bns node info:", base_utils.Format(infoResp))
}

func Test_bnsSdk_BnsNodeDelete(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBnsSdk(SpecialServiceName)

	delResp, err := s.BnsNodeDelete(ctx, &BnsNodeDeleteRequest{
		FullPath: "BAIDU_DBA_DBA-BDRP_redis_raoalong-dev-test_ral-test1",
		Token:    "0tVUYOby5ofzmmuRDx3ZScWTx9lKBLqN",
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("bns node info:", base_utils.Format(delResp))
}
