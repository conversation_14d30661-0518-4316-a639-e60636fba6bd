// Package bns
package bns

import "context"

type BnsGroupIdcMap struct {
	Services map[string]IdcMapBnsGroupConf `json:"services"`
}

type IdcMapBnsGroupConf struct {
	IdcMap   map[string]IdcMapConf `json:"idc_map"`
	Protocol struct {
		Name string `json:"name"`
	} `json:"protocol"`
	Converter struct {
		Name string `json:"name"`
	} `json:"converter"`
	ServiceRtimeout int `json:"service_rtimeout"`
	ServiceCtimeout int `json:"service_ctimeout"`
	ServiceWtimeout int `json:"service_wtimeout"`
	ServiceConnType int `json:"service_conn_type"`
	ServiceRetry    int `json:"service_retry"`
	SuperStrategy   struct {
		ConnectQueueSize       int     `json:"ConnectQueueSize"`
		HealthyBackupThreshold int     `json:"HealthyBackupThreshold"`
		HealthyQueueSize       int     `json:"HealthyQueueSize"`
		HealthyCheckTime       int     `json:"HealthyCheckTime"`
		Hybrid                 string  `json:"Hybrid"`
		HealthyTimeout         int     `json:"HealthyTimeout"`
		CrossRoom              string  `json:"CrossRoom"`
		ConnectX2              int     `json:"ConnectX2"`
		ConnectX1              int     `json:"ConnectX1"`
		ConnectY1              int     `json:"ConnectY1"`
		ConnectY2              int     `json:"ConnectY2"`
		HealthyMinRate         float32 `json:"HealthyMinRate"`
		Balance                string  `json:"Balance"`
	} `json:"SuperStrategy"`
	ServiceEnableConnectRetry string `json:"service_enable_connect_retry"`
}

type IdcMapConf struct {
	Prefer string `json:"prefer"`
	Backup string `json:"backup"`
}

type InstanceInfo struct {
	HostName        string `json:"hostName"`
	Port            string `json:"port"`       // json格式，如：{"main":21,"ctl":20}
	Tag             string `json:"tag"`        // 如：k1:v1,k2:v2
	Disable         int    `json:"disable"`    // 值为1：加入时缺省为bns不可见  值为0：(缺省)表示一旦加入就bns可见
	InstanceId      int    `json:"instanceId"` // 可选；大于等于0的整数；未设置由系统生成
	Status          int    `json:"status"`
	DeployPath      string `json:"deployPath"`
	RunUser         string `json:"runUser"`
	HealthCheckCmd  string `json:"healthCheckCmd"`
	HealthCheckType string `json:"healthCheckType"` // proc：进程方式；script：脚本方式
	ContainerId     string `json:"containerId"`     // 可选
}

type BnsCreateRequest struct {
	ParentPath  string `json:"parentPath"` // 例：BAIDU_OP_OPED_NOAH
	AuthKey     string `json:"authKey"`
	NodeName    string `json:"nodeName"` // 必须符合三段式约定(即服务名.产品线.机房)示例：eip.noah.tc
	RunUser     string `json:"runUser"`
	ServiceConf string `json:"serviceConf"` // 可选；长度须小于4k，否则会截断
	IsMatrixSu  int    `json:"isMatrixSu"`  // 可选；缺省为0
}

type BnsNodeCreateRequest struct {
	ParentPath string `json:"parentPath"` // 例：BAIDU_OP_OPED_NOAH
	Token      string `json:"token"`
	NodeName   string `json:"nodeName"` // 必须符合三段式约定(即服务名.产品线.机房)示例：eip.noah.tc
	Type       string `json:"type"`     // "service"
}

type BnsDeleteRequest struct {
	ServiceName string `json:"serviceName"` // 必须符合三段式约定(即服务名.产品线.机房)示例：eip.noah.tc
	AuthKey     string `json:"authKey"`
}

type BnsNodeDeleteRequest struct {
	FullPath string `json:"fullPath"` // 例：BAIDU_OP_OPED_NOAH.eip.noah.tc
	Token    string `json:"token"`
}

type BnsNodeInfoRequest struct {
	FullPath string `json:"fullPath"` // 例：BAIDU_OP_OPED_NOAH.eip.noah.tc
	Token    string `json:"token"`
}

type BnsInstanceInfo struct {
	HostName   string            `json:"hostName"`
	Port       map[string]string `json:"port"`       // json格式，如：{"main":21,"ctl":20}
	Tag        map[string]string `json:"tag"`        // 如：k1:v1,k2:v2
	Disable    int               `json:"disable"`    // 值为1：加入时缺省为bns不可见  值为0：(缺省)表示一旦加入就bns可见
	InstanceId int               `json:"instanceId"` // 可选；大于等于0的整数；未设置由系统生成
	Status     int               `json:"status"`
	/*
		0:实例正常
		-1:实例异常
		999:实例未配置健康检查时的状态
		888:实例对应的机器死机(无法ping和ssh)
		777:实例对应的机器解析ip失败
		其他数字:用户自定义脚本产生的状态码
	*/
	DeployPath      string `json:"deployPath"`
	RunUser         string `json:"runUser"`
	HealthCheckCmd  string `json:"healthCheckCmd"`
	HealthCheckType string `json:"healthCheckType"` // proc：进程方式；script：脚本方式
	ContainerId     string `json:"containerId"`     // 可选
}

type BnsClearInstanceRequest struct {
	ServiceName string `json:"serviceName"` // 必须符合三段式约定(即服务名.产品线.机房)示例：eip.noah.tc
	AuthKey     string `json:"authKey"`
}

type BnsDeleteInstanceRequest struct {
	ServiceName string `json:"serviceName"` // 必须符合三段式约定(即服务名.产品线.机房)示例：eip.noah.tc
	AuthKey     string `json:"authKey"`
	HostName    string `json:"hostName"`
	InstanceId  int    `json:"instanceid"`
}

type BnsGroupCreateRequest struct {
	ServiceNames      []string          `json:"service_names"`
	NodePath          string            `json:"node_path"`
	AuthKey           string            `json:"authKey"`
	Threshold         int               `json:"threshold"`           // 可选  当此服务组下状态为0的实例数目低于此值时，查询端得到的信息将不会被更新（若不用此特性 请置为0 即可）
	ThresholdPercent  int               `json:"threshold_percent"`   // 可选  0~100之间
	GroupConf         *BnsGroupConfList `json:"group_conf"`          // 可选  //{"services":{"$GroupName":{"idc_map":{"jx":{"prefer":"bj","backup":"bj"},"tc":{"prefer":"bj","backup":"bj"},"nj":{"prefer":"nj","backup":"nj"},"nj03":{"prefer":"nj","backup":"nj"},"hz":{"prefer":"hz","backup":"hz"},"sh":{"prefer":"sh","backup":"sh"},"sz":{"prefer":"sz","backup":"sz"},"gz":{"prefer":"gz","backup":"gz"}},"protocol":{"name":"nshead"},"converter":{"name":"mcpack2"},"service_rtimeout":200,"service_ctimeout":100,"service_wtimeout":200,"service_conn_type":0,"service_retry":0,"SuperStrategy":{"ConnectQueueSize":100,"HealthyBackupThreshold":3,"HealthyQueueSize":100,"HealthyCheckTime":3,"Hybrid":"Off","HealthyTimeout":100,"CrossRoom":"Off","ConnectX2":40,"ConnectX1":10,"ConnectY1":95,"ConnectY2":6,"HealthyMinRate":0.1,"Balance":"Random"},"service_enable_connect_retry":"On"}}}
	ConfIsJson        int               `json:"conf_is_json"`        // 可选  缺省为1，进行json校验
	ConstrainGroup    int               `json:"constrainGroup"`      // 可选  0：表示不限制，缺省值   1：表示限制
	OpenDeadhostCheck int               `json:"open_deadhost_check"` // 可选  0：表示不开启，缺省值   1：表示开启
}

type BnsGroupConfList struct {
	GroupName                 string                // bns group 名称
	IdcMap                    map[string]IdcMapConf // idc map
	ProtocolName              string                // 协议名称,默认值为"nshead"
	ConverterName             string                // 数据打包方式,默认值为"mcpack2"
	ServiceRtimeout           int                   // 读超时(ms)
	ServiceCtimeout           int                   // 连接超时(ms)
	ServiceWtimeout           int                   // 写超时(ms)
	ServiceConnType           int                   // 连接类型,默认值为0
	ServiceRetry              int                   // 重试次数,默认值为0,可自定义配置
	ConnectQueueSize          int
	HealthyBackupThreshold    int
	HealthyQueueSize          int
	HealthyCheckTime          int
	Hybrid                    string
	HealthyTimeout            int
	CrossRoom                 string
	ConnectX2                 int
	ConnectX1                 int
	ConnectY1                 int
	ConnectY2                 int
	HealthyMinRate            float32
	Balance                   string // 负载均衡策略,默认值为"Random"
	ServiceEnableConnectRetry string
}

type BnsGroupDeleteRequest struct {
	GroupName string `json:"groupName"`
	AuthKey   string `json:"authKey"`
	Limit     bool   `json:"limit"` // 服务组含有服务单元时是否限制删除  true：限制，不删除 false：不限制，删除  默认为false

}

type BnsServiceInstanceInfoRequest struct {
	ServiceName string `json:"serviceName"`
	AuthKey     string `json:"authKey"`
}

type BnsGroupModifyServicesRequest struct {
	GroupName    string   `json:"groupName"`
	AuthKey      string   `json:"authKey"`
	ServiceNames []string `json:"services"`
	Action       string   `json:"action"` // add：追加服务列表到服务组 remove：从服务组里移出服务
}

type BnsAddInstanceRequest struct {
	ServiceName  string           `json:"serviceName"` // 必须符合三段式约定(即服务名.产品线.机房)示例：eip.noah.tc
	AuthKey      string           `json:"authKey"`
	InstanceInfo *BnsInstanceInfo `json:"instanceInfo"`
}

type BnsBatchAddInstanceRequest struct {
	ServiceName  string             `json:"serviceName"` // 必须符合三段式约定(即服务名.产品线.机房)示例：eip.noah.tc
	AuthKey      string             `json:"authKey"`
	InstanceInfo []*BnsInstanceInfo `json:"instanceInfo"`
}

type BnsBatchAddInstanceResponse struct {
	RetCode int    `json:"retCode"`
	Msg     string `json:"msg"`
	Data    string `json:"data"`
}

type BnsAddInstanceResponse struct {
	RetCode    int    `json:"retCode"`
	Msg        string `json:"msg"`
	InstanceId string `json:"instanceId"`
}

type InstanceDisableAndEnableRequest struct {
	ServiceName  string `json:"serviceName"`
	AuthKey      string `json:"authKey"`
	Enable       string `json:"enable"`
	ContainerIds string `json:"containerIds"`
}

type BnsGeneralResponse struct {
	RetCode int    `json:"retCode"`
	Msg     string `json:"msg"`
}

type BnsGeneralResponse2 struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type BnsNodeInfoResponse struct {
	Success    bool    `json:"success"`
	Message    string  `json:"message"`
	ID         string  `json:"id"`
	TreeID     string  `json:"treeId"`
	Name       string  `json:"name"`
	Path       string  `json:"path"`
	ParentID   string  `json:"parentId"`
	Type       string  `json:"type"`
	DetailType string  `json:"detailType"`
	RefID      *string `json:"refId"`
	TagType    string  `json:"tagType"`
	TagName    string  `json:"tagName"`
	Status     string  `json:"status"`
}

type BnsInstanceInfoDecode struct {
	Id                       string `json:"id"`
	HostName                 string `json:"hostName"`
	Status                   string `json:"status"`
	InterventionalStatus     string `json:"interventionalStatus"`
	Port                     string `json:"port"`
	InterventionalStatusMean string `json:"interventionalStatusMean"`
	Tag                      string `json:"tag"`
	Offset                   string `json:"offset"`
	DeployPath               string `json:"deployPath"`
	RunUser                  string `json:"runUser"`
	HealthCheckCmd           string `json:"healthCheckCmd"`
	HealthCheckType          string `json:"healthCheckType"`
	ContainerId              string `json:"containerId"`
}

type BnsServiceInstanceInfoResponse struct {
	RetCode      int                     `json:"retCode"`
	Msg          string                  `json:"msg"`
	InstanceInfo []BnsInstanceInfoDecode `json:"instanceInfo"`
	InstanceStr  string                  `json:"instanceStr"`
}

type BnsGroupInfoRequest struct {
	GroupName string `json:"groupName"`
	AuthKey   string `json:"authKey"`
}

type BnsGroupInfoData struct {
	ID                interface{} `json:"id"`
	GroupName         string      `json:"groupName"`
	GroupConf         string      `json:"groupConf"`
	FlowCtlConf       string      `json:"flowCtlConf"`
	ServiceNames      string      `json:"serviceNames"`
	Creator           string      `json:"creator"`
	NodeID            int         `json:"nodeId"`
	NodePath          string      `json:"nodePath"`
	Threshold         int         `json:"threshold"`
	ThresholdPercent  string      `json:"threshold_percent"`
	CustomDefine      string      `json:"custom_define"`
	OpenDeadhostCheck string      `json:"open_deadhost_check"`
	OpenSmartBns      string      `json:"open_smart_bns"`
	Cat               string      `json:"cat"`
}

type BnsGroupInfoResponse struct {
	RetCode int               `json:"retCode"`
	Msg     string            `json:"msg"`
	Data    *BnsGroupInfoData `json:"data"`
}

type BnsService interface {
	BnsServiceCreate(ctx context.Context, req *BnsCreateRequest) (rsp *BnsGeneralResponse, err error)
	BnsServiceDelete(ctx context.Context, req *BnsDeleteRequest) (rsp *BnsGeneralResponse, err error)
	BnsNodeCreate(ctx context.Context, req *BnsNodeCreateRequest) (rsp *BnsGeneralResponse2, err error)
	BnsNodeDelete(ctx context.Context, req *BnsNodeDeleteRequest) (rsp *BnsGeneralResponse2, err error)
	BnsNodeInfo(ctx context.Context, req *BnsNodeInfoRequest) (rsp *BnsNodeInfoResponse, err error)
	BnsServiceAddInstance(ctx context.Context, req *BnsAddInstanceRequest) (rsp *BnsAddInstanceResponse, err error)
	BnsServiceClearInstance(ctx context.Context, req *BnsClearInstanceRequest) (rsp *BnsGeneralResponse, err error)
	BnsServiceDeleteInstance(ctx context.Context, req *BnsDeleteInstanceRequest) (rsp *BnsGeneralResponse, err error)
	BatchDisableAndEnableInstancesByContainerId(ctx context.Context, req *InstanceDisableAndEnableRequest) (rsp *BnsGeneralResponse, err error)
	BnsGroupModifyServices(ctx context.Context, req *BnsGroupModifyServicesRequest) (rsp *BnsGeneralResponse2, err error)
	BnsGroupCreate(ctx context.Context, req *BnsGroupCreateRequest) (rsp *BnsGeneralResponse, err error)
	BnsGroupDelete(ctx context.Context, req *BnsGroupDeleteRequest) (rsp *BnsGeneralResponse, err error)
	BnsGroupInfo(ctx context.Context, req *BnsGroupInfoRequest) (rsp *BnsGroupInfoResponse, err error)
	BnsServiceInstanceInfo(ctx context.Context, req *BnsServiceInstanceInfoRequest) (rsp *BnsServiceInstanceInfoResponse, err error)
	BnsMapToString(mapTag map[string]string) (stringTag string, err error)
	BnsArrayToString(mapTag []string) (stringTag string, err error)
	GenerateDefaultIdcMap(bnsGroupConf *BnsGroupConfList) (idcMap string, err error)
}
