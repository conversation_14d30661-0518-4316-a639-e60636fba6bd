// Package bns
package bns

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

// noah bns Service 的sdk实现
type bnsSdk struct {
	conf *bnsConf
	common.OpenApi
}

func NewBefaultBnsSdk() BnsService {
	return newBnsSdk(DefaultServiceName)
}

func NewBnsSdk(serviceName string) BnsService {
	return newBnsSdk(serviceName)
}

func newBnsSdk(serviceName string) *bnsSdk {
	s := &bnsSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}

	return s
}

// doRequest - 通用openstack服务请求方法
func (s *bnsSdk) doRequest(ctx context.Context, actionName string, token string,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	params := &common.OpenApiParams{
		ActionName: actionName,
		Token:      token,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}

	// 请求 openapi
	err = s.DoRequest(ctx, params, rsp)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return err
	}
	return nil
}

// 【bns 变更api】
// 服务单元——创建   【/webfoot/index.php?r=bns/Create】 频率限制：3000个 / 10小时
func (s *bnsSdk) BnsServiceCreate(ctx context.Context, req *BnsCreateRequest) (rsp *BnsGeneralResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.AuthKey == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param authKey is null")
	}
	if req.NodeName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param nodeName is null")
	}

	rsp = &BnsGeneralResponse{}

	// 设置 query queries
	queries := map[string]interface{}{
		"parentPath": req.ParentPath,
		"authKey":    req.AuthKey,
		"nodeName":   req.NodeName,
		"runUser":    req.RunUser,
	}
	if req.ServiceConf != "" {
		queries["serviceConf"] = req.ServiceConf
	}
	if req.IsMatrixSu != 0 {
		queries["isMatrixSu"] = req.IsMatrixSu
	}

	if err = s.doRequest(ctx, "BnsServiceCreate", req.AuthKey, http.MethodPost,
		bnsServiceCreateUri, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.RetCode != 0 && strings.Contains(rsp.Msg, "already exists") {
		return nil, cerrs.ErrBNSRequestAlreadyExist.Errorf("BnsServiceCreate request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	if rsp.RetCode != 0 {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsServiceCreate request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return
}

// 服务单元——删除    【/webfoot/index.php?r=bns/Delete】
func (s *bnsSdk) BnsServiceDelete(ctx context.Context, req *BnsDeleteRequest) (rsp *BnsGeneralResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.AuthKey == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param authKey is null")
	}
	if req.ServiceName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param serviceName is null")
	}

	rsp = &BnsGeneralResponse{}

	// 设置 query queries
	queries := map[string]interface{}{
		"serviceName": req.ServiceName,
		"authKey":     req.AuthKey,
	}

	if err = s.doRequest(ctx, "BnsServiceDelete", req.AuthKey, http.MethodPost,
		bnsServiceDeleteUri, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.RetCode != 0 && strings.Contains(rsp.Msg, "not exist") {
		return nil, cerrs.ErrBNSRequestNotFound.Errorf("BnsServiceDelete request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	if rsp.RetCode != 0 {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsServiceDelete request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return
}

// 服务节点——创建   【service-tree/index.php?r=Node/AddNode/addNodeApi】
func (s *bnsSdk) BnsNodeCreate(ctx context.Context, req *BnsNodeCreateRequest) (rsp *BnsGeneralResponse2, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Token == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param token is null")
	}
	if req.NodeName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param nodeName is null")
	}

	rsp = &BnsGeneralResponse2{}

	// 设置 query queries
	queries := map[string]interface{}{
		"parentPath": req.ParentPath,
		"token":      req.Token,
		"nodeName":   req.NodeName,
		"type":       req.Type,
	}

	if err = s.doRequest(ctx, "BnsNodeServiceCreate", req.Token, http.MethodPost,
		bnsNodeServiceCreateUri, queries, req, rsp); err != nil {
		return nil, err
	}
	if !rsp.Success {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsNodeServiceCreate request fail,req:%s , BnsError RetCode: %t , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.Success, rsp.Message)
	}
	return
}

// 服务节点——删除   【/service-tree/index.php?r=Node/DelNode/DeleteNodeApi】
func (s *bnsSdk) BnsNodeDelete(ctx context.Context, req *BnsNodeDeleteRequest) (rsp *BnsGeneralResponse2, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Token == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param token is null")
	}
	if req.FullPath == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param nodeName is null")
	}

	rsp = &BnsGeneralResponse2{}

	// 设置 query queries
	queries := map[string]interface{}{
		"node":  req.FullPath,
		"token": req.Token,
	}

	if err = s.doRequest(ctx, "BnsNodeServiceDelete", req.Token, http.MethodPost,
		bnsNodeServiceDeleteUri, queries, req, rsp); err != nil {
		return nil, err
	}
	if !rsp.Success {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsNodeServiceDelete request fail,req:%s , BnsError RetCode: %t , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.Success, rsp.Message)
	}
	return
}

// 服务节点查询   【/service-tree/v1/node/path_{path}】
func (s *bnsSdk) BnsNodeInfo(ctx context.Context, req *BnsNodeInfoRequest) (rsp *BnsNodeInfoResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Token == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param token is null")
	}
	if req.FullPath == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param nodeName is null")
	}

	rsp = &BnsNodeInfoResponse{}

	// 设置 query queries
	queries := map[string]interface{}{
		"token": req.Token,
	}

	if err = s.doRequest(ctx, "BnsNodeServiceQuery", req.Token, http.MethodGet,
		fmt.Sprintf(bnsNodeServiceInfoUri, req.FullPath), queries, req, rsp); err != nil {
		return nil, err
	}
	if !rsp.Success {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsNodeServiceQuery request fail,req:%s , BnsError RetCode: %t , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.Success, rsp.Message)
	}
	return
}

// 服务单元——添加单个实例   【/webfoot/index.php?r=bns/AddInstance】
func (s *bnsSdk) BnsServiceAddInstance(ctx context.Context, req *BnsAddInstanceRequest) (rsp *BnsAddInstanceResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.AuthKey == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param authKey is null")
	}
	if req.ServiceName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param serviceName is null")
	}

	rsp = &BnsAddInstanceResponse{}

	// 设置 query queries
	port, err := json.Marshal(req.InstanceInfo.Port)
	if err != nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param port json marshal failed")
	}
	tag, err := s.BnsMapToString(req.InstanceInfo.Tag)
	if err != nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param tag format failed")
	}
	queries := map[string]interface{}{
		"serviceName":     req.ServiceName,
		"authKey":         req.AuthKey,
		"hostName":        req.InstanceInfo.HostName,
		"port":            string(port),
		"tag":             tag,
		"disable":         req.InstanceInfo.Disable,
		"instanceId":      req.InstanceInfo.InstanceId,
		"status":          req.InstanceInfo.Status,
		"deployPath":      req.InstanceInfo.DeployPath,
		"runUser":         req.InstanceInfo.RunUser,
		"healthCheckCmd":  req.InstanceInfo.HealthCheckCmd,
		"healthCheckType": req.InstanceInfo.HealthCheckType,
		"containerId":     req.InstanceInfo.ContainerId,
	}

	if err = s.doRequest(ctx, "BnsServiceAddInstance", req.AuthKey, http.MethodPost,
		bnsServiceAddInstanceUri, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.RetCode != 0 && strings.Contains(rsp.Msg, "already exists") {
		return nil, cerrs.ErrBNSRequestAlreadyExist.Errorf("BnsServiceAddInstance request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	if rsp.RetCode != 0 {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsServiceAddInstance request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return
}

// 服务单元——批量添加实例   【/webfoot/index.php?r=bns/BatchAddInstance】 TODO
// 服务单元——批量添加机器    【/webfoot/index.php?r=bns/BatchAddHosts】  TODO

// 服务单元——清空服务单元下实例   【/webfoot/index.php?r=bns/ClearInstance】
func (s *bnsSdk) BnsServiceClearInstance(ctx context.Context, req *BnsClearInstanceRequest) (rsp *BnsGeneralResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.AuthKey == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param authKey is null")
	}
	if req.ServiceName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param serviceName is null")
	}

	rsp = &BnsGeneralResponse{}

	queries := map[string]interface{}{
		"serviceName": req.ServiceName,
		"authKey":     req.AuthKey,
	}

	if err = s.doRequest(ctx, "BnsServiceClearInstance", req.AuthKey, http.MethodPost,
		bnsServiceClearInstanceUri, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.RetCode != 0 {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsServiceClearInstance request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return
}

// 服务单元——删除实例       【/webfoot/index.php?r=bns/DeleteInstance】
func (s *bnsSdk) BnsServiceDeleteInstance(ctx context.Context, req *BnsDeleteInstanceRequest) (rsp *BnsGeneralResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.AuthKey == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param authKey is null")
	}
	if req.ServiceName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param serviceName is null")
	}

	rsp = &BnsGeneralResponse{}

	queries := map[string]interface{}{
		"serviceName": req.ServiceName,
		"authKey":     req.AuthKey,
		"hostName":    req.HostName,
		"instanceId":  req.InstanceId,
	}

	if err = s.doRequest(ctx, "BnsServiceDeleteInstance", req.AuthKey, http.MethodPost,
		bnsServiceDeleteInstanceUri, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.RetCode != 0 {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsServiceDeleteInstance request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return
}

// 服务单元——批量删除机器    【/webfoot/index.php?r=bns/BatchDeleteHosts】 TODO
// 服务单元——批量修改实例信息   【/webfoot/index.php?r=webfoot/ModifyInstanceInfo】  TODO
// 服务单元——批量修改实例部署路径  【/webfoot/index.php?r=webfoot/SaveDeployPath】  TODO
// 服务单元——批量修改实例运行账户  【/webfoot/index.php?r=webfoot/SaveAccount】  TODO
// 服务单元——批量修改实例健康检查方式  【/webfoot/index.php?r=webfoot/SaveHealthCheck】  TODO
// 服务单元——批量修改实例端口  【/webfoot/index.php?r=webfoot/SavePort】  TODO
// 服务单元——批量修改实例状态  【/webfoot/index.php?r=webfoot/SaveStatus】  TODO
// 服务单元——批量设置实例tag  【/webfoot/index.php?r=webfoot/SaveTags】  TODO
// 服务单元——新增|更新bns service详情  【/webfoot/index.php?r=webfoot/SaveService】  TODO
// 服务单元——更新bns service配置  【/webfoot/index.php?r=webfoot/UpdateServiceConf】  TODO
// 服务单元——更新bns service阈值  【/webfoot/index.php?r=webfoot/ModifyThreshold】  TODO
// 服务单元——批量屏蔽|解屏蔽实例  【/webfoot/index.php?r=webfoot/EnableServiceInstances】 本接口是按机器屏蔽或生效实例,与平台需求不符 TODO

// 服务单元——根据container_id批量屏蔽|解屏蔽实例  【/webfoot/index.php?r=webfoot/EnableInstancesByContainerId】
func (s *bnsSdk) BatchDisableAndEnableInstancesByContainerId(ctx context.Context, req *InstanceDisableAndEnableRequest) (rsp *BnsGeneralResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.AuthKey == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param authKey is null")
	}
	if req.ServiceName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param serviceName is null")
	}
	var instanceOperate string
	if req.Enable == "enable" {
		instanceOperate = "EnableInstance"
	} else if req.Enable == "disable" {
		instanceOperate = "DisableInstance"
	} else {
		return nil, cerrs.ErrInvalidParams.Errorf("req param enable is invalid")
	}

	rsp = &BnsGeneralResponse{}

	// 设置 query queries
	queries := map[string]interface{}{
		"serviceName":  req.ServiceName,
		"authKey":      req.AuthKey,
		"enable":       req.Enable,
		"containerIds": req.ContainerIds,
	}

	if err = s.doRequest(ctx, instanceOperate, req.AuthKey, http.MethodPost,
		batchDisableAndEnableInstancesByContainerIdUri, queries, req, rsp); err != nil {
		return nil, err
	}

	if rsp.RetCode != 0 {
		return nil, cerrs.ErrBNSRequestFail.Errorf("%s request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			instanceOperate, base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return
}

// 服务单元——批量变更实例tag  【/webfoot/index.php?r=webfoot/ShellModifyInstanceInfo】  TODO
// 服务组——修改服务组的服务列表  【/webfoot/index.php?r=group/ModifyServicesOfGroup】
func (s *bnsSdk) BnsGroupModifyServices(ctx context.Context, req *BnsGroupModifyServicesRequest) (rsp *BnsGeneralResponse2, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.AuthKey == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param authKey is null")
	}
	if req.GroupName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param groupName is null")
	}

	rsp = &BnsGeneralResponse2{}

	serviceNames, err := s.BnsArrayToString(req.ServiceNames)
	if err != nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param serviceNames format failed")
	}
	queries := map[string]interface{}{
		"groupName": req.GroupName,
		"authKey":   req.AuthKey,
		"services":  serviceNames,
		"action":    req.Action,
	}

	if err = s.doRequest(ctx, "BnsGroupModifyServices", req.AuthKey, http.MethodGet,
		bnsGroupModifyServicesUri, queries, nil, rsp); err != nil {
		return nil, err
	}
	if !rsp.Success {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsGroupModifyServices request fail,req:%s , BnsError Success: %t , BnsError ErrorMessage: %s",
			base_utils.Format(req), rsp.Success, rsp.Message)
	}
	return
}

// 服务组——创建  【/webfoot/index.php?r=group/SaveGroup】
func (s *bnsSdk) BnsGroupCreate(ctx context.Context, req *BnsGroupCreateRequest) (rsp *BnsGeneralResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.AuthKey == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param authKey is null")
	}
	if req.GroupConf == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param GroupConf is null")
	}
	if req.GroupConf.GroupName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param groupName is null")
	}

	groupConf, err := s.GenerateDefaultIdcMap(req.GroupConf)
	if err != nil {
		return nil, err
	}

	rsp = &BnsGeneralResponse{}

	serviceNames, err := s.BnsArrayToString(req.ServiceNames)
	if err != nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param serviceNames format failed")
	}

	queries := map[string]interface{}{
		"group_name":          req.GroupConf.GroupName,
		"service_names":       serviceNames,
		"node_path":           req.NodePath,
		"authKey":             req.AuthKey,
		"threshold":           req.Threshold,
		"threshold_percent":   req.ThresholdPercent,
		"group_conf":          groupConf,
		"conf_is_json":        req.ConfIsJson,
		"constrainGroup":      req.ConstrainGroup,
		"open_deadhost_check": req.OpenDeadhostCheck,
	}

	if err = s.doRequest(ctx, "BnsGroupCreate", req.AuthKey, http.MethodPost,
		bnsGroupCreateUri, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.RetCode != 0 {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsGroupCreate request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return
}

// 服务组——删除  【/webfoot/index.php?r=group/DelGroup】
func (s *bnsSdk) BnsGroupDelete(ctx context.Context, req *BnsGroupDeleteRequest) (rsp *BnsGeneralResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.AuthKey == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param authKey is null")
	}
	if req.GroupName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param groupName is null")
	}

	rsp = &BnsGeneralResponse{}

	queries := map[string]interface{}{
		"groupName": req.GroupName,
		"authKey":   req.AuthKey,
		"limit":     req.Limit,
	}

	if err = s.doRequest(ctx, "BnsGroupDelete", req.AuthKey, http.MethodPost,
		bnsGroupDeleteUri, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.RetCode != 0 {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsGroupDelete request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return
}

// 服务组——获取某节点下的服务组列表  【/webfoot/index.php?r=group/GroupsOnNodePath】  TODO
// 服务组——修改服务组所属的节点路径  【/webfoot/index.php?r=group/ChangeNodePath】  TODO
// 服务单元——批量删除实例  【/webfoot/index.php?r=bns/BatchDeleteInstance】  TODO
// 服务单元——修改服务组smartbns路由表  【/webfoot/index.php?r=group/SaveFlowConf】   TODO

// 【bns 查询api】
// 服务单元——服务信息查询  【/webfoot/index.php?r=webfoot/GetServiceInfo】
// 服务单元——服务组信息查询  【/webfoot/index.php?r=Group/GroupInfo_v2】
func (s *bnsSdk) BnsGroupInfo(ctx context.Context, req *BnsGroupInfoRequest) (resp *BnsGroupInfoResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &BnsGroupInfoResponse{}
	queries := map[string]interface{}{
		"groupName": req.GroupName,
		"authKey":   req.AuthKey,
	}
	if err = s.doRequest(ctx, "BnsGroupInfo", req.AuthKey, http.MethodPost, bnsGroupInfoUri, queries, req, resp); err != nil {
		return nil, err
	}
	if resp.RetCode != 0 && strings.Contains(resp.Msg, "group not exists") {
		return nil, cerrs.ErrBNSRequestNotFound.Errorf("BnsGroupInfo request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), resp.RetCode, resp.Msg)
	} else if resp.RetCode != 0 {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsGroupInfo request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), resp.RetCode, resp.Msg)
	}
	return resp, nil
}

// 服务单元——服务实例信息查询  【/webfoot/index.php?r=webfoot/GetInstanceInfo】   需要实现
func (s *bnsSdk) BnsServiceInstanceInfo(ctx context.Context, req *BnsServiceInstanceInfoRequest) (rsp *BnsServiceInstanceInfoResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.AuthKey == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param authKey is null")
	}
	if req.ServiceName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param serviceName is null")
	}

	rsp = &BnsServiceInstanceInfoResponse{}

	queries := map[string]interface{}{
		"serviceName": req.ServiceName,
		"authKey":     req.AuthKey,
	}

	if err = s.doRequest(ctx, "BnsServiceInstanceInfo", req.AuthKey, http.MethodPost,
		bnsServiceInstanceInfoUri, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.RetCode != 0 && strings.Contains(rsp.Msg, "not exists") {
		return nil, cerrs.ErrBNSRequestNotFound.Errorf("BnsServiceInstanceInfo request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	if rsp.RetCode != 0 {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsServiceInstanceInfo request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return
}

// 服务组——服务组实例信息查询  【/webfoot/index.php?r=webfoot/ApiInstanceInfo】
// 服务(组)——验证token对节点路径是否有操作权限  【/webfoot/index.php?r=bns/CheckToken】

//【bns 查询变更历史】
// 服务单元——查询服务单元变更历史  【/webfoot/index.php?r=history/GetServiceChangeHistory】
// 服务单元——查询挂卸载历史  【/webfoot/index.php?r=history/GetMountUnMountHistory】

func (s *bnsSdk) BnsMapToString(mapTag map[string]string) (stringTag string, err error) {
	var ret string
	for k, v := range mapTag {
		if ret != "" {
			ret += ","
		}
		tag := k + ":" + v
		ret += tag
	}
	return ret, nil
}

func (s *bnsSdk) BnsArrayToString(mapTag []string) (stringTag string, err error) {
	var ret string
	for _, v := range mapTag {
		if ret != "" {
			ret += ","
		}
		ret += v
	}
	return ret, nil
}

func (s *bnsSdk) GenerateDefaultIdcMap(bnsGroupConf *BnsGroupConfList) (idcMap string, err error) {

	if bnsGroupConf == nil {
		return "", cerrs.ErrInvalidParams.Errorf("bnsGroupConf param is null")
	}

	if bnsGroupConf.GroupName == "" {
		return "", cerrs.ErrInvalidParams.Errorf("GroupName param is null")
	}

	var DefaultIdcMap BnsGroupIdcMap
	var DefaultIdcMapBnsGroupConf IdcMapBnsGroupConf
	DefaultIdcMapBnsGroupConf.Protocol.Name = bnsGroupConf.ProtocolName
	if DefaultIdcMapBnsGroupConf.Protocol.Name == "" {
		DefaultIdcMapBnsGroupConf.Protocol.Name = "nshead"
	}

	DefaultIdcMapBnsGroupConf.Converter.Name = bnsGroupConf.ConverterName
	if DefaultIdcMapBnsGroupConf.Converter.Name == "" {
		DefaultIdcMapBnsGroupConf.Converter.Name = "mcpack2"
	}

	DefaultIdcMapBnsGroupConf.ServiceRtimeout = bnsGroupConf.ServiceRtimeout
	if DefaultIdcMapBnsGroupConf.ServiceRtimeout == 0 {
		DefaultIdcMapBnsGroupConf.ServiceRtimeout = 200
	}

	DefaultIdcMapBnsGroupConf.ServiceCtimeout = bnsGroupConf.ServiceCtimeout
	if DefaultIdcMapBnsGroupConf.ServiceCtimeout == 0 {
		DefaultIdcMapBnsGroupConf.ServiceCtimeout = 100
	}

	DefaultIdcMapBnsGroupConf.ServiceWtimeout = bnsGroupConf.ServiceWtimeout
	if DefaultIdcMapBnsGroupConf.ServiceWtimeout == 0 {
		DefaultIdcMapBnsGroupConf.ServiceWtimeout = 200
	}

	// DefaultIdcMapBnsGroupConf.ServiceConnType = 0
	DefaultIdcMapBnsGroupConf.ServiceConnType = bnsGroupConf.ServiceConnType

	// DefaultIdcMapBnsGroupConf.ServiceRetry = 0
	DefaultIdcMapBnsGroupConf.ServiceRetry = bnsGroupConf.ServiceRetry

	DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectQueueSize = bnsGroupConf.ConnectQueueSize
	if DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectQueueSize == 0 {
		DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectQueueSize = 100
	}

	DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyBackupThreshold = bnsGroupConf.HealthyBackupThreshold
	if DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyBackupThreshold == 0 {
		DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyBackupThreshold = 3
	}

	DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyQueueSize = bnsGroupConf.HealthyQueueSize
	if DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyQueueSize == 0 {
		DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyQueueSize = 100
	}

	DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyCheckTime = bnsGroupConf.HealthyCheckTime
	if DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyCheckTime == 0 {
		DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyCheckTime = 3
	}

	DefaultIdcMapBnsGroupConf.SuperStrategy.Hybrid = bnsGroupConf.Hybrid
	if DefaultIdcMapBnsGroupConf.SuperStrategy.Hybrid == "" {
		DefaultIdcMapBnsGroupConf.SuperStrategy.Hybrid = "Off"
	}

	DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyTimeout = bnsGroupConf.HealthyTimeout
	if DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyTimeout == 0 {
		DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyTimeout = 100
	}

	DefaultIdcMapBnsGroupConf.SuperStrategy.CrossRoom = bnsGroupConf.CrossRoom
	if DefaultIdcMapBnsGroupConf.SuperStrategy.CrossRoom == "" {
		DefaultIdcMapBnsGroupConf.SuperStrategy.CrossRoom = "Off"
	}

	DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectX2 = bnsGroupConf.ConnectX2
	if DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectX2 == 0 {
		DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectX2 = 40
	}

	DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectX1 = bnsGroupConf.ConnectX1
	if DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectX1 == 0 {
		DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectX1 = 10
	}

	DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectY1 = bnsGroupConf.ConnectY1
	if DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectY1 == 0 {
		DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectY1 = 95
	}

	DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectY2 = bnsGroupConf.ConnectY2
	if DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectY2 == 0 {
		DefaultIdcMapBnsGroupConf.SuperStrategy.ConnectY2 = 6
	}

	DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyMinRate = bnsGroupConf.HealthyMinRate
	if DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyMinRate == 0 {
		DefaultIdcMapBnsGroupConf.SuperStrategy.HealthyMinRate = 0.1
	}

	DefaultIdcMapBnsGroupConf.SuperStrategy.Balance = bnsGroupConf.Balance
	if DefaultIdcMapBnsGroupConf.SuperStrategy.Balance == "" {
		DefaultIdcMapBnsGroupConf.SuperStrategy.Balance = "Random"
	}

	DefaultIdcMapBnsGroupConf.ServiceEnableConnectRetry = bnsGroupConf.ServiceEnableConnectRetry
	if DefaultIdcMapBnsGroupConf.ServiceEnableConnectRetry == "" {
		DefaultIdcMapBnsGroupConf.ServiceEnableConnectRetry = "On"
	}

	DefaultIdcMapBnsGroupConf.IdcMap = bnsGroupConf.IdcMap
	if DefaultIdcMapBnsGroupConf.IdcMap == nil {
		idcMapConf := map[string]IdcMapConf{
			"jx": {
				Prefer: "bj",
				Backup: "bj",
			},
			"tc": {
				Prefer: "bj",
				Backup: "bj",
			},
			"nj": {
				Prefer: "nj",
				Backup: "nj",
			},
			"sz": {
				Prefer: "sz",
				Backup: "sz",
			},
			"gz": {
				Prefer: "gz",
				Backup: "gz",
			},
		}
		DefaultIdcMapBnsGroupConf.IdcMap = idcMapConf
	}

	DefaultIdcMap.Services = make(map[string]IdcMapBnsGroupConf)
	DefaultIdcMap.Services[bnsGroupConf.GroupName] = DefaultIdcMapBnsGroupConf

	jsonIdcMap, err := json.Marshal(DefaultIdcMap)
	if err != nil {
		return "", cerrs.ErrInvalidParams.Errorf("defaultIdcMap json marshal failed")
	}
	return string(jsonIdcMap), nil
}
