/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/24 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file bns_conf.go
 * <AUTHOR>
 * @date 2023/03/24 15:15:47
 * @brief bns conf

 *
 **/

package bns

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "bns"

// bnsConf definition
type bnsConf struct {
	Product string `toml:"Product,omitempty"`
}

var bnsConfMap = &sync.Map{}

func getConf(serviceName string) *bnsConf {
	if conf, ok := bnsConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*bnsConf); ok {
			return conf
		}
	}

	conf := &bnsConf{}
	conf.mustLoad(serviceName)

	bnsConfMap.Store(serviceName, conf)

	return conf
}

func (conf *bnsConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
