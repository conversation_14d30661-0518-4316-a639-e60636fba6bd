package bns

const DefaultServiceName = "bns"

const SpecialServiceName = "noah-bns"

// 各种uri
const (
	bnsServiceCreateUri                            = "/webfoot/index.php?r=bns/Create"
	bnsServiceDeleteUri                            = "/webfoot/index.php?r=bns/Delete"
	bnsNodeServiceCreateUri                        = "service-tree/index.php?r=Node/AddNode/addNodeApi"
	bnsNodeServiceDeleteUri                        = "service-tree/index.php?r=Node/DelNode/DeleteNodeApi"
	bnsNodeServiceInfoUri                          = "/service-tree/v1/node/path_%s"
	bnsServiceAddInstanceUri                       = "/webfoot/index.php?r=bns/AddInstance"
	bnsServiceClearInstanceUri                     = "/webfoot/index.php?r=bns/ClearInstance"
	bnsServiceDeleteInstanceUri                    = "/webfoot/index.php?r=bns/DeleteInstance"
	bnsGroupCreateUri                              = "/webfoot/index.php?r=group/SaveGroup"
	bnsGroupDeleteUri                              = "/webfoot/index.php?r=group/DelGroup"
	bnsGroupInfoUri                                = "/webfoot/index.php?r=Group/GroupInfo_v2"
	bnsServiceInstanceInfoUri                      = "/webfoot/index.php?r=webfoot/GetInstanceInfo"
	bnsGroupModifyServicesUri                      = "/webfoot/index.php?r=group/ModifyServicesOfGroup"
	batchDisableAndEnableInstancesByContainerIdUri = "/webfoot/index.php?r=webfoot/EnableInstancesByContainerId"
)
