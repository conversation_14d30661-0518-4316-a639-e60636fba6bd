package bcm

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const (
	TestIamUserId = "4093ae6b9e48423e89c69916bc6d5d5e"
)

func Test_bcmSdk_CreateResource(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBcmSdk(DefaultServiceName)
	_, err := s.CreateResource(ctx, nil)
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.CreateResource(ctx, &CreateResourceRequest{Auth: nil})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test Auth nil checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.CreateResource(ctx, &CreateResourceRequest{Auth: &common.Authentication{}})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test requestId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	// {"requestId":"787df68f-bade-4664-9225-5afec2c17ec2",
	//	"code":"ResourceNotExistException",
	//	"message":"The monitor resource: abc___bj.BCE_SCS.4093ae6b9e48423e89c69916bc6d5d5e not exists."}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&CreateResourceResponse{
		Message:   "Test Message",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusNotFound))
	rsp, err := s.CreateResource(ctx, &CreateResourceRequest{Auth: &common.Authentication{},
		UserId:      "testUserId",
		Region:      "testRegion",
		ServiceName: "testServiceName",
		ResourceId:  "testResourceId"})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) || rsp.Code != "TestCode" ||
		rsp.RequestID != "787df68f-bade-4664-9225-5afec2c17ec2" ||
		rsp.Message != "Test Message" {
		t.Errorf("[%s] test create resource fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	_, err = s.CreateResource(ctx, &CreateResourceRequest{Auth: &common.Authentication{},
		UserId:      "testUserId",
		Region:      "testRegion",
		ServiceName: "testServiceName",
		ResourceId:  "testResourceId"})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&GetResourceResponse{
		Message:       "",
		Code:          "",
		RequestID:     "",
		UserId:        "testUserId",
		Region:        "testRegion",
		ServiceName:   "testServiceName",
		TypeName:      "testTypeName",
		ResourceId:    "testResourceId",
		ErrUpdateTime: nil,
		Identifiers:   nil,
		Properties:    nil,
		Tags:          nil,
	}, nil, http.StatusOK))
	_, err = s.GetResource(ctx, &GetResourceRequest{Auth: &common.Authentication{},
		UserId:      "testUserId",
		Region:      "testRegion",
		ServiceName: "testServiceName",
		ResourceId:  "testResourceId"})
	if err != nil {
		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
	}
}

//func Test_bcmSdk_CreateResource_Sandbox(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	s := newBcmSdk(DefaultServiceName)
//	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
//		TransactionId: uuid.NewString(),
//		IamUserId:     TestIamUserId,
//	})
//	if err != nil {
//		t.Errorf("[%s] iam auth fail", t.Name())
//		return
//	}
//	auth := authRsp.Auth
//
//	rsp, err := s.CreateResource(ctx, &CreateResourceRequest{Auth: auth,
//		UserId:      TestIamUserId,
//		Region:      "bj",
//		ServiceName: "BCE_SCS",
//		ResourceId:  "sandbox___sandbox_test_0",
//		TypeName:    "Instance",
//		Properties: []Property{
//			{
//				Name:  "propertyName",
//				Value: "propertyValue",
//			},
//		},
//		Identifiers: []Identifier{
//			{
//				Name:  "identifierName",
//				Value: "identifierValue",
//			},
//		},
//		Tags: []Tag{
//			{
//				Name:  "tagName",
//				Value: "tagValue",
//			},
//		}})
//	if err != nil {
//		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
//	}
//	fmt.Printf("%+v", rsp)
//}

func Test_bcmSdk_GetResource(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBcmSdk(DefaultServiceName)
	_, err := s.GetResource(ctx, nil)
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.GetResource(ctx, &GetResourceRequest{Auth: nil})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test Auth nil checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.GetResource(ctx, &GetResourceRequest{Auth: &common.Authentication{}})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test requestId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&GetResourceResponse{
		Message:   "The monitor resource: abc___bj.BCE_SCS.4093ae6b9e48423e89c69916bc6d5d5e not exists.",
		Code:      "ResourceNotExistException",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusNotFound))
	rsp, err := s.GetResource(ctx, &GetResourceRequest{Auth: &common.Authentication{},
		UserId:      "testUserId",
		Region:      "testRegion",
		ServiceName: "testServiceName",
		ResourceId:  "testResourceId"})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) || rsp.Code != "ResourceNotExistException" ||
		rsp.RequestID != "787df68f-bade-4664-9225-5afec2c17ec2" ||
		rsp.Message != "The monitor resource: abc___bj.BCE_SCS.4093ae6b9e48423e89c69916bc6d5d5e not exists." {
		t.Errorf("[%s] test resource not found fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	_, err = s.GetResource(ctx, &GetResourceRequest{Auth: &common.Authentication{},
		UserId:      "testUserId",
		Region:      "testRegion",
		ServiceName: "testServiceName",
		ResourceId:  "testResourceId"})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&GetResourceResponse{
		Message:       "",
		Code:          "",
		RequestID:     "",
		UserId:        "testUserId",
		Region:        "testRegion",
		ServiceName:   "testServiceName",
		TypeName:      "testTypeName",
		ResourceId:    "testResourceId",
		ErrUpdateTime: nil,
		Identifiers:   nil,
		Properties:    nil,
		Tags:          nil,
	}, nil, http.StatusOK))
	_, err = s.GetResource(ctx, &GetResourceRequest{Auth: &common.Authentication{},
		UserId:      "testUserId",
		Region:      "testRegion",
		ServiceName: "testServiceName",
		ResourceId:  "testResourceId"})
	if err != nil {
		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
	}
}

//func Test_bcmSdk_GetResource_Sandbox(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	s := newBcmSdk(DefaultServiceName)
//	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
//		TransactionId: uuid.NewString(),
//		IamUserId:     TestIamUserId,
//	})
//	if err != nil {
//		t.Errorf("[%s] iam auth fail", t.Name())
//		return
//	}
//	auth := authRsp.Auth
//
//	rsp, err := s.GetResource(ctx, &GetResourceRequest{Auth: auth,
//		UserId:      TestIamUserId,
//		Region:      "bj",
//		ServiceName: "BCE_SCS",
//		ResourceId:  "scs-bj-vstcprsvifjd___scs-bj-vstcprsvifjd_proxy_100"})
//	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) || rsp.Code != "ResourceNotExistException" {
//		t.Errorf("[%s] sandbox test resource not found fail", t.Name())
//	} else {
//		fmt.Printf("%s\n", err.Error())
//	}
//	rsp, err = s.GetResource(ctx, &GetResourceRequest{Auth: auth,
//		UserId:      TestIamUserId,
//		Region:      "bj",
//		ServiceName: "BCE_SCS",
//		ResourceId:  "sandbox___sandbox_test_0"})
//	if err != nil {
//		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
//	}
//	fmt.Printf("%+v", rsp)
//}

func Test_bcmSdk_DeleteResource(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBcmSdk(DefaultServiceName)
	_, err := s.DeleteResource(ctx, nil)
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.DeleteResource(ctx, &DeleteResourceRequest{Auth: nil})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test Auth nil checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.DeleteResource(ctx, &DeleteResourceRequest{Auth: &common.Authentication{}})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test requestId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	// {"requestId":"787df68f-bade-4664-9225-5afec2c17ec2",
	//	"code":"ResourceNotExistException",
	//	"message":"The monitor resource: abc___bj.BCE_SCS.4093ae6b9e48423e89c69916bc6d5d5e not exists."}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&DeleteResourceResponse{
		Message:   "Test Message",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusNotFound))
	rsp, err := s.DeleteResource(ctx, &DeleteResourceRequest{Auth: &common.Authentication{},
		UserId:      "testUserId",
		Region:      "testRegion",
		ServiceName: "testServiceName",
		ResourceId:  "testResourceId"})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) || rsp.Code != "TestCode" ||
		rsp.RequestID != "787df68f-bade-4664-9225-5afec2c17ec2" ||
		rsp.Message != "Test Message" {
		t.Errorf("[%s] test create resource fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	_, err = s.DeleteResource(ctx, &DeleteResourceRequest{Auth: &common.Authentication{},
		UserId:      "testUserId",
		Region:      "testRegion",
		ServiceName: "testServiceName",
		ResourceId:  "testResourceId"})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&DeleteResourceResponse{
		Message:   "",
		Code:      "",
		RequestID: "",
	}, nil, http.StatusOK))
	_, err = s.DeleteResource(ctx, &DeleteResourceRequest{Auth: &common.Authentication{},
		UserId:      "testUserId",
		Region:      "testRegion",
		ServiceName: "testServiceName",
		ResourceId:  "testResourceId"})
	if err != nil {
		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
	}
}

//func Test_bcmSdk_DeleteResource_Sandbox(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	s := newBcmSdk(DefaultServiceName)
//	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
//		TransactionId: uuid.NewString(),
//		IamUserId:     TestIamUserId,
//	})
//	if err != nil {
//		t.Errorf("[%s] iam auth fail", t.Name())
//		return
//	}
//	auth := authRsp.Auth
//
//	rsp, err := s.DeleteResource(ctx, &DeleteResourceRequest{Auth: auth,
//		UserId:      TestIamUserId,
//		Region:      "bj",
//		ServiceName: "BCE_SCS",
//		ResourceId:  "sandbox___sandbox_test_not_exist"})
//	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) || rsp.Code != "ResourceNotExistException" {
//		t.Errorf("[%s] sandbox test resource not found fail", t.Name())
//	} else {
//		fmt.Printf("%s\n", err.Error())
//	}
//	rsp, err = s.DeleteResource(ctx, &DeleteResourceRequest{Auth: auth,
//		UserId:      TestIamUserId,
//		Region:      "bj",
//		ServiceName: "BCE_SCS",
//		ResourceId:  "sandbox___sandbox_test_0"})
//	if err != nil {
//		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
//	}
//	fmt.Printf("%+v", rsp)
//}

//func Test_bcmSdk_ListInstanceGroups_offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	s := newBcmSdk(DefaultServiceName)
//	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
//		TransactionId: uuid.NewString(),
//		IamUserId:     TestIamUserId,
//	})
//	if err != nil {
//		t.Errorf("[%s] iam auth fail", t.Name())
//		return
//	}
//	auth := authRsp.Auth
//
//	rsp, err := s.ListInstanceGroups(ctx, &ListInstanceGroupsRequest{Auth: auth,
//		UserId:      TestIamUserId,
//		Region:      "bj",
//		ServiceName: "BCE_SCS",
//		PageSize:    5, PageNo: 1})
//	if err != nil {
//		t.Errorf("[%s] sandbox list instance group fail", t.Name())
//	}
//	fmt.Printf("%+v", rsp)
//	fmt.Printf("%+v", rsp.Result[0])
//}

func Test_bcmSdk_ListInstanceGroups(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBcmSdk(DefaultServiceName)
	_, err := s.ListInstanceGroups(ctx, nil)
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.ListInstanceGroups(ctx, &ListInstanceGroupsRequest{Auth: nil})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test Auth nil checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.ListInstanceGroups(ctx, &ListInstanceGroupsRequest{Auth: &common.Authentication{}})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test requestId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	// {"requestId":"787df68f-bade-4664-9225-5afec2c17ec2",
	//	"code":"ResourceNotExistException",
	//	"message":"The monitor resource: abc___bj.BCE_SCS.4093ae6b9e48423e89c69916bc6d5d5e not exists."}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ListInstanceGroupsResponse{
		Message:   "Test Message",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusNotFound))
	rsp, err := s.ListInstanceGroups(ctx, &ListInstanceGroupsRequest{
		UserId:      "testUserId",
		ServiceName: "testServiceName",
		Region:      "testRegion",
		Auth:        &common.Authentication{},
		PageSize:    500,
		PageNo:      1,
	})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) || rsp.Code != "TestCode" ||
		rsp.RequestID != "787df68f-bade-4664-9225-5afec2c17ec2" ||
		rsp.Message != "Test Message" {
		t.Errorf("[%s] test create resource fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	_, err = s.ListInstanceGroups(ctx, &ListInstanceGroupsRequest{
		UserId:      "testUserId",
		ServiceName: "testServiceName",
		Region:      "testRegion",
		Auth:        &common.Authentication{},
		PageSize:    500,
		PageNo:      1,
	})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	// {"id":7282,"name":"testxuepeng","serviceName":"BCE_SCS","typeName":"RD_ST_INSTANCE","region":"bj",
	//"userId":"4093ae6b9e48423e89c69916bc6d5d5e","uuid":"c6711b68-6181-494e-ad47-e56cca695452","count":1,
	//"serviceNameAlias":"云数据库 Redis","typeNameAlias":"Redis-社区版-实例","regionAlias":"北京",
	//"tagKey":"INSTANCE_GROUP_c6711b68-6181-494e-ad47-e56cca695452:c6711b68-6181-494e-ad47-e56cca695452",
	//"typeTarget":"{\"resourceType\":\"RD_ST_INSTANCE\",\"resourceId\":[\"ClusterId\",\"NodeId\"]}"}]
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ListInstanceGroupsResponse{
		Message:   "T",
		Code:      "T",
		RequestID: "T",
		Result: []*InstanceGroupItem{{
			Id:               7777,
			Name:             "N",
			ServiceName:      "S",
			TypeName:         "T",
			Region:           "R",
			UserId:           "U",
			Uuid:             "UU",
			Count:            0,
			ServiceNameAlias: "SS",
			TypeNameAlias:    "TT",
			RegionAlias:      "RR",
			TagKey:           "TK",
			TypeTarget:       "TTT",
		}},
	}, nil, http.StatusOK))
	_, err = s.ListInstanceGroups(ctx, &ListInstanceGroupsRequest{
		UserId:      "testUserId",
		ServiceName: "testServiceName",
		Region:      "testRegion",
		Auth:        &common.Authentication{},
		PageSize:    500,
		PageNo:      1,
	})
	if err != nil {
		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
	}
}

//func Test_bcmSdk_GetInstanceGroup_Offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	s := newBcmSdk(DefaultServiceName)
//	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
//		TransactionId: uuid.NewString(),
//		IamUserId:     TestIamUserId,
//	})
//	if err != nil {
//		t.Errorf("[%s] iam auth fail", t.Name())
//		return
//	}
//	auth := authRsp.Auth
//
//	rsp, err := s.GetInstanceGroup(ctx, &GetInstanceGroupRequest{Auth: auth,
//		UserId:          TestIamUserId,
//		Region:          "bj",
//		ServiceName:     "BCE_SCS",
//		InstanceGroupID: 7281})
//	if err != nil {
//		t.Errorf("[%s] sandbox list instance group fail", t.Name())
//	}
//	fmt.Printf("%+v", rsp)
//}

func Test_bcmSdk_GetInstanceGroup(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBcmSdk(DefaultServiceName)
	_, err := s.GetInstanceGroup(ctx, nil)
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.GetInstanceGroup(ctx, &GetInstanceGroupRequest{Auth: nil})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test Auth nil checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.GetInstanceGroup(ctx, &GetInstanceGroupRequest{Auth: &common.Authentication{}})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test requestId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	// {"requestId":"787df68f-bade-4664-9225-5afec2c17ec2",
	//	"code":"ResourceNotExistException",
	//	"message":"The monitor resource: abc___bj.BCE_SCS.4093ae6b9e48423e89c69916bc6d5d5e not exists."}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&InstanceGroupResponse{
		Message:   "Test Message",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusNotFound))
	rsp, err := s.GetInstanceGroup(ctx, &GetInstanceGroupRequest{
		UserId:          "testUserId",
		ServiceName:     "testServiceName",
		Region:          "testRegion",
		Auth:            &common.Authentication{},
		InstanceGroupID: 1000,
	})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) || rsp.Code != "TestCode" ||
		rsp.RequestID != "787df68f-bade-4664-9225-5afec2c17ec2" ||
		rsp.Message != "Test Message" {
		t.Errorf("[%s] test create resource fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	_, err = s.GetInstanceGroup(ctx, &GetInstanceGroupRequest{
		UserId:          "testUserId",
		ServiceName:     "testServiceName",
		Region:          "testRegion",
		Auth:            &common.Authentication{},
		InstanceGroupID: 1000,
	})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	// {"id":7282,"name":"testxuepeng","serviceName":"BCE_SCS","typeName":"RD_ST_INSTANCE","region":"bj",
	//"userId":"4093ae6b9e48423e89c69916bc6d5d5e","uuid":"c6711b68-6181-494e-ad47-e56cca695452","count":1,
	//"serviceNameAlias":"云数据库 Redis","typeNameAlias":"Redis-社区版-实例","regionAlias":"北京",
	//"tagKey":"INSTANCE_GROUP_c6711b68-6181-494e-ad47-e56cca695452:c6711b68-6181-494e-ad47-e56cca695452",
	//"typeTarget":"{\"resourceType\":\"RD_ST_INSTANCE\",\"resourceId\":[\"ClusterId\",\"NodeId\"]}"}]
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&InstanceGroupResponse{
		Message:   "T",
		Code:      "T",
		RequestID: "T",
		InstanceGroupItem: InstanceGroupItem{
			Id:               7777,
			Name:             "N",
			ServiceName:      "S",
			TypeName:         "T",
			Region:           "R",
			UserId:           "U",
			Uuid:             "UU",
			Count:            0,
			ServiceNameAlias: "SS",
			TypeNameAlias:    "TT",
			RegionAlias:      "RR",
			TagKey:           "TK",
			TypeTarget:       "TTT",
		},
	}, nil, http.StatusOK))
	_, err = s.GetInstanceGroup(ctx, &GetInstanceGroupRequest{
		UserId:          "testUserId",
		ServiceName:     "testServiceName",
		Region:          "testRegion",
		Auth:            &common.Authentication{},
		InstanceGroupID: 1000,
	})
	if err != nil {
		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
	}
}

//func Test_bcmSdk_ListInstanceInGroup_offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	s := newBcmSdk(DefaultServiceName)
//	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
//		TransactionId: uuid.NewString(),
//		IamUserId:     TestIamUserId,
//	})
//	if err != nil {
//		t.Errorf("[%s] iam auth fail", t.Name())
//		return
//	}
//	auth := authRsp.Auth
//
//	rsp, err := s.ListInstanceInGroup(ctx, &ListInstanceInGroupRequest{
//		UserId:          TestIamUserId,
//		ServiceName:     "BCE_SCS",
//		Region:          "bj",
//		Auth:            auth,
//		InstanceGroupID: 7282,
//		ViewType:        ViewTypeLIST,
//		TypeName:        "RD_ST_INSTANCE",
//		PageNo:          1,
//		PageSize:        5,
//	})
//	if err != nil {
//		t.Errorf("[%s] sandbox list instance group fail", t.Name())
//	}
//	fmt.Printf("%+v", rsp)
//}

//func Test_bcmSdk_AddInstanceToGroup_Offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	s := newBcmSdk(DefaultServiceName)
//	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
//		TransactionId: uuid.NewString(),
//		IamUserId:     TestIamUserId,
//	})
//	if err != nil {
//		t.Errorf("[%s] iam auth fail", t.Name())
//		return
//	}
//	auth := authRsp.Auth
//
//	rsp, err := s.AddInstanceToGroup(ctx, &OperateInstanceInGroupRequest{
//		UserId:          TestIamUserId,
//		ServiceName:     "BCE_SCS",
//		Region:          "bj",
//		Auth:            auth,
//		InstanceGroupID: 7282,
//		Name:            "testxuepeng",
//		TypeName:        "RD_ST_INSTANCE",
//		ResourceIdList: []*MonitorResource{
//			&MonitorResource{
//				UserId:      TestIamUserId,
//				Region:      "bj",
//				ServiceName: "BCE_SCS",
//				TypeName:    "RD_ST_INSTANCE",
//				ResourceId:  "ClusterId:scs-bj-xvpqpmgfidqv;NodeId:scs-bj-xvpqpmgfidqv-0",
//			},
//		},
//	})
//	if err != nil {
//		t.Errorf("[%s] sandbox add to instance group fail", t.Name())
//	}
//	fmt.Printf("%+v", rsp)
//}

func Test_bcmSdk_ListInstanceInGroup(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBcmSdk(DefaultServiceName)
	_, err := s.ListInstanceInGroup(ctx, nil)
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.ListInstanceInGroup(ctx, &ListInstanceInGroupRequest{Auth: nil})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test Auth nil checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.ListInstanceInGroup(ctx, &ListInstanceInGroupRequest{Auth: &common.Authentication{}})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test requestId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	// {"requestId":"787df68f-bade-4664-9225-5afec2c17ec2",
	//	"code":"ResourceNotExistException",
	//	"message":"The monitor resource: abc___bj.BCE_SCS.4093ae6b9e48423e89c69916bc6d5d5e not exists."}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ListInstanceInGroupResponse{
		Message:   "Test Message",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusNotFound))
	rsp, err := s.ListInstanceInGroup(ctx, &ListInstanceInGroupRequest{
		UserId:          "testUserId",
		ServiceName:     "testServiceName",
		Region:          "testRegion",
		Auth:            &common.Authentication{},
		InstanceGroupID: 1000,
		ViewType:        ViewTypeLIST,
		TypeName:        "RD_ST_INSTANCE",
		PageNo:          1,
		PageSize:        5,
	})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) || rsp.Code != "TestCode" ||
		rsp.RequestID != "787df68f-bade-4664-9225-5afec2c17ec2" ||
		rsp.Message != "Test Message" {
		t.Errorf("[%s] test create resource fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	rsp, err = s.ListInstanceInGroup(ctx, &ListInstanceInGroupRequest{
		UserId:          "testUserId",
		ServiceName:     "testServiceName",
		Region:          "testRegion",
		Auth:            &common.Authentication{},
		InstanceGroupID: 1000,
		ViewType:        ViewTypeLIST,
		TypeName:        "RD_ST_INSTANCE",
		PageNo:          1,
		PageSize:        5,
	})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	// {"id":7282,"name":"testxuepeng","serviceName":"BCE_SCS","typeName":"RD_ST_INSTANCE","region":"bj",
	//"userId":"4093ae6b9e48423e89c69916bc6d5d5e","uuid":"c6711b68-6181-494e-ad47-e56cca695452","count":1,
	//"serviceNameAlias":"云数据库 Redis","typeNameAlias":"Redis-社区版-实例","regionAlias":"北京",
	//"tagKey":"INSTANCE_GROUP_c6711b68-6181-494e-ad47-e56cca695452:c6711b68-6181-494e-ad47-e56cca695452",
	//"typeTarget":"{\"resourceType\":\"RD_ST_INSTANCE\",\"resourceId\":[\"ClusterId\",\"NodeId\"]}"}]
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&ListInstanceInGroupResponse{
		Message:    "T",
		Code:       "T",
		RequestID:  "T",
		OrderBy:    "T",
		Order:      "T",
		PageNo:     0,
		PageSize:   0,
		TotalCount: 0,
		Result:     nil,
	}, nil, http.StatusOK))
	rsp, err = s.ListInstanceInGroup(ctx, &ListInstanceInGroupRequest{
		UserId:          "testUserId",
		ServiceName:     "testServiceName",
		Region:          "testRegion",
		Auth:            &common.Authentication{},
		InstanceGroupID: 1000,
		ViewType:        ViewTypeLIST,
		TypeName:        "RD_ST_INSTANCE",
		PageNo:          1,
		PageSize:        5,
	})
	if err != nil {
		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
	}
}

func Test_bcmSdk_AddInstanceToGroup(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBcmSdk(DefaultServiceName)
	_, err := s.AddInstanceToGroup(ctx, nil)
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.AddInstanceToGroup(ctx, &OperateInstanceInGroupRequest{Auth: nil})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test Auth nil checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.AddInstanceToGroup(ctx, &OperateInstanceInGroupRequest{Auth: &common.Authentication{}})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test requestId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	// {"requestId":"787df68f-bade-4664-9225-5afec2c17ec2",
	//	"code":"ResourceNotExistException",
	//	"message":"The monitor resource: abc___bj.BCE_SCS.4093ae6b9e48423e89c69916bc6d5d5e not exists."}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&InstanceGroupResponse{
		Message:   "Test Message",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusNotFound))
	rsp, err := s.AddInstanceToGroup(ctx, &OperateInstanceInGroupRequest{
		UserId:          "testUserId",
		ServiceName:     "testServiceName",
		Region:          "testRegion",
		Auth:            &common.Authentication{},
		InstanceGroupID: 1000,
		Name:            "1000",
		TypeName:        "1000",
		ResourceIdList: []*MonitorResource{{
			UserId:      "testUserId",
			Region:      "testRegion",
			ServiceName: "testServiceName",
			TypeName:    "testType",
			ResourceId:  "testResourceId",
		}},
	})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) || rsp.Code != "TestCode" ||
		rsp.RequestID != "787df68f-bade-4664-9225-5afec2c17ec2" ||
		rsp.Message != "Test Message" {
		t.Errorf("[%s] test create resource fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	_, err = s.AddInstanceToGroup(ctx, &OperateInstanceInGroupRequest{
		UserId:          "testUserId",
		ServiceName:     "testServiceName",
		Region:          "testRegion",
		Auth:            &common.Authentication{},
		InstanceGroupID: 1000,
		Name:            "1000",
		TypeName:        "1000",
		ResourceIdList: []*MonitorResource{{
			UserId:      "testUserId",
			Region:      "testRegion",
			ServiceName: "testServiceName",
			TypeName:    "testType",
			ResourceId:  "testResourceId",
		}},
	})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	// {"id":7282,"name":"testxuepeng","serviceName":"BCE_SCS","typeName":"RD_ST_INSTANCE","region":"bj",
	//"userId":"4093ae6b9e48423e89c69916bc6d5d5e","uuid":"c6711b68-6181-494e-ad47-e56cca695452","count":1,
	//"serviceNameAlias":"云数据库 Redis","typeNameAlias":"Redis-社区版-实例","regionAlias":"北京",
	//"tagKey":"INSTANCE_GROUP_c6711b68-6181-494e-ad47-e56cca695452:c6711b68-6181-494e-ad47-e56cca695452",
	//"typeTarget":"{\"resourceType\":\"RD_ST_INSTANCE\",\"resourceId\":[\"ClusterId\",\"NodeId\"]}"}]
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&InstanceGroupResponse{
		Message:   "T",
		Code:      "T",
		RequestID: "T",
		InstanceGroupItem: InstanceGroupItem{
			Id:               7777,
			Name:             "N",
			ServiceName:      "S",
			TypeName:         "T",
			Region:           "R",
			UserId:           "U",
			Uuid:             "UU",
			Count:            0,
			ServiceNameAlias: "SS",
			TypeNameAlias:    "TT",
			RegionAlias:      "RR",
			TagKey:           "TK",
			TypeTarget:       "TTT",
		},
	}, nil, http.StatusOK))
	_, err = s.AddInstanceToGroup(ctx, &OperateInstanceInGroupRequest{
		UserId:          "testUserId",
		ServiceName:     "testServiceName",
		Region:          "testRegion",
		Auth:            &common.Authentication{},
		InstanceGroupID: 1000,
		Name:            "1000",
		TypeName:        "1000",
		ResourceIdList: []*MonitorResource{{
			UserId:      "testUserId",
			Region:      "testRegion",
			ServiceName: "testServiceName",
			TypeName:    "testType",
			ResourceId:  "testResourceId",
		}},
	})
	if err != nil {
		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
	}
}

//func Test_bcmSdk_RemoveInstanceFromGroup_Offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	s := newBcmSdk(DefaultServiceName)
//	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
//		TransactionId: uuid.NewString(),
//		IamUserId:     TestIamUserId,
//	})
//	if err != nil {
//		t.Errorf("[%s] iam auth fail", t.Name())
//		return
//	}
//	auth := authRsp.Auth
//
//	rsp, err := s.RemoveInstanceFromGroup(ctx, &OperateInstanceInGroupRequest{
//		UserId:          TestIamUserId,
//		ServiceName:     "BCE_SCS",
//		Region:          "bj",
//		Auth:            auth,
//		InstanceGroupID: 7282,
//		Name:            "testxuepeng",
//		TypeName:        "RD_ST_INSTANCE",
//		ResourceIdList: []*MonitorResource{
//			&MonitorResource{
//				UserId:      TestIamUserId,
//				Region:      "bj",
//				ServiceName: "BCE_SCS",
//				TypeName:    "RD_ST_INSTANCE",
//				ResourceId:  "ClusterId:scs-bj-xvpqpmgfidqv;NodeId:scs-bj-xvpqpmgfidqv-0",
//			},
//		},
//	})
//	if err != nil {
//		t.Errorf("[%s] sandbox add to instance group fail", t.Name())
//	}
//	fmt.Printf("%+v", rsp)
//}

func Test_bcmSdk_RemoveInstanceFromGroup(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBcmSdk(DefaultServiceName)
	_, err := s.RemoveInstanceFromGroup(ctx, nil)
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.RemoveInstanceFromGroup(ctx, &OperateInstanceInGroupRequest{Auth: nil})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test Auth nil checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.RemoveInstanceFromGroup(ctx, &OperateInstanceInGroupRequest{Auth: &common.Authentication{}})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test requestId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	// {"requestId":"787df68f-bade-4664-9225-5afec2c17ec2",
	//	"code":"ResourceNotExistException",
	//	"message":"The monitor resource: abc___bj.BCE_SCS.4093ae6b9e48423e89c69916bc6d5d5e not exists."}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&InstanceGroupResponse{
		Message:   "Test Message",
		Code:      "TestCode",
		RequestID: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusNotFound))
	rsp, err := s.RemoveInstanceFromGroup(ctx, &OperateInstanceInGroupRequest{
		UserId:          "testUserId",
		ServiceName:     "testServiceName",
		Region:          "testRegion",
		Auth:            &common.Authentication{},
		InstanceGroupID: 1000,
		Name:            "1000",
		TypeName:        "1000",
		ResourceIdList: []*MonitorResource{{
			UserId:      "testUserId",
			Region:      "testRegion",
			ServiceName: "testServiceName",
			TypeName:    "testType",
			ResourceId:  "testResourceId",
		}},
	})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) || rsp.Code != "TestCode" ||
		rsp.RequestID != "787df68f-bade-4664-9225-5afec2c17ec2" ||
		rsp.Message != "Test Message" {
		t.Errorf("[%s] test create resource fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	_, err = s.RemoveInstanceFromGroup(ctx, &OperateInstanceInGroupRequest{
		UserId:          "testUserId",
		ServiceName:     "testServiceName",
		Region:          "testRegion",
		Auth:            &common.Authentication{},
		InstanceGroupID: 1000,
		Name:            "1000",
		TypeName:        "1000",
		ResourceIdList: []*MonitorResource{{
			UserId:      "testUserId",
			Region:      "testRegion",
			ServiceName: "testServiceName",
			TypeName:    "testType",
			ResourceId:  "testResourceId",
		}},
	})
	if err == nil || !cerrs.ErrBCMRequestFail.Is(err) {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	// {"id":7282,"name":"testxuepeng","serviceName":"BCE_SCS","typeName":"RD_ST_INSTANCE","region":"bj",
	//"userId":"4093ae6b9e48423e89c69916bc6d5d5e","uuid":"c6711b68-6181-494e-ad47-e56cca695452","count":1,
	//"serviceNameAlias":"云数据库 Redis","typeNameAlias":"Redis-社区版-实例","regionAlias":"北京",
	//"tagKey":"INSTANCE_GROUP_c6711b68-6181-494e-ad47-e56cca695452:c6711b68-6181-494e-ad47-e56cca695452",
	//"typeTarget":"{\"resourceType\":\"RD_ST_INSTANCE\",\"resourceId\":[\"ClusterId\",\"NodeId\"]}"}]
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&InstanceGroupResponse{
		Message:   "T",
		Code:      "T",
		RequestID: "T",
		InstanceGroupItem: InstanceGroupItem{
			Id:               7777,
			Name:             "N",
			ServiceName:      "S",
			TypeName:         "T",
			Region:           "R",
			UserId:           "U",
			Uuid:             "UU",
			Count:            0,
			ServiceNameAlias: "SS",
			TypeNameAlias:    "TT",
			RegionAlias:      "RR",
			TagKey:           "TK",
			TypeTarget:       "TTT",
		},
	}, nil, http.StatusOK))
	_, err = s.RemoveInstanceFromGroup(ctx, &OperateInstanceInGroupRequest{
		UserId:          "testUserId",
		ServiceName:     "testServiceName",
		Region:          "testRegion",
		Auth:            &common.Authentication{},
		InstanceGroupID: 1000,
		Name:            "1000",
		TypeName:        "1000",
		ResourceIdList: []*MonitorResource{{
			UserId:      "testUserId",
			Region:      "testRegion",
			ServiceName: "testServiceName",
			TypeName:    "testType",
			ResourceId:  "testResourceId",
		}},
	})
	if err != nil {
		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
	}
}

//func Test_bcmSdk_QueryMetricData_Offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	s := newBcmSdk(DefaultServiceName)
//	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
//		TransactionId: uuid.NewString(),
//		IamUserId:     TestIamUserId,
//	})
//	if err != nil {
//		t.Errorf("[%s] iam auth fail", t.Name())
//		return
//	}
//	auth := authRsp.Auth
//
//	rsp, err := s.QueryMetricData(ctx, &QueryMetricDataRequest{
//		UserId:         TestIamUserId,
//		ServiceName:    "BCE_SCS",
//		Region:         "bj",
//		Auth:           auth,
//		MetricName:     "RejectedConns",
//		Statistics:     []string{"average", "sum", "maximum", "minimum", "sampleCount"},
//		StartTime:      time.Now().UTC().Add(-5 * time.Minute),
//		EndTime:        time.Now().UTC(),
//		PeriodInSecond: 120,
//		Dimensions:     "ClusterId:scs-bj-cwkrodbdhrtu;NodeId:scs-bj-cwkrodbdhrtu_redis_4460_3",
//	})
//	if err != nil {
//		t.Errorf("[%s] query metric data fail", t.Name())
//	}
//	fmt.Printf("%+v", rsp)
//}

func Test_bcmSdk_QueryMetricData(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newBcmSdk(DefaultServiceName)
	_, err := s.QueryMetricData(ctx, nil)
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.QueryMetricData(ctx, &QueryMetricDataRequest{Auth: nil})
	if err == nil || !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test Auth nil checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&QueryMetricDataResponse{
		Message:   "Test Message",
		Code:      "TestCode",
		RequestId: "787df68f-bade-4664-9225-5afec2c17ec2",
	}, nil, http.StatusNotFound))
	rsp, err := s.QueryMetricData(ctx, &QueryMetricDataRequest{
		UserId:      "testUserId",
		ServiceName: "testServiceName",
		Region:      "testRegion",
		Auth:        &common.Authentication{},
	})
	if err == nil || rsp.Code != "TestCode" ||
		rsp.RequestId != "787df68f-bade-4664-9225-5afec2c17ec2" ||
		rsp.Message != "Test Message" {
		t.Errorf("[%s] test create resource fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
		Op:  "dial",
		Err: fmt.Errorf("dial fail"),
	}), 0))
	rsp, err = s.QueryMetricData(ctx, &QueryMetricDataRequest{
		UserId:      "testUserId",
		ServiceName: "testServiceName",
		Region:      "testRegion",
		Auth:        &common.Authentication{},
	})
	fmt.Println(err)
	if err == nil {
		t.Errorf("[%s] test ral request fail fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
	s.SetRalCaller(sdk_utils.NewTestRalCaller(&QueryMetricDataResponse{
		Message:    "T",
		Code:       "T",
		RequestId:  "T",
		DataPoints: []DataPoint{},
	}, nil, http.StatusOK))
	_, err = s.QueryMetricData(ctx, &QueryMetricDataRequest{
		UserId:      "testUserId",
		ServiceName: "testServiceName",
		Region:      "testRegion",
		Auth:        &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
	}
}
