/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/02/20 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file interface.go
 * <AUTHOR>
 * @date 2023/02/20 15:25:40
 * @brief
 *
 **/

package bcm

import (
	"context"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type PushEventRequest struct {
	Result      *Result
	AccountID   string                 `json:"-"`
	ServiceName string                 `json:"-"`
	Auth        *common.Authentication `json:"-"`
}

type Result struct {
	Region       string `json:"region"`
	ResourceID   string `json:"resourceId"`
	ResourceType string `json:"resourceType"`
	EventID      string `json:"eventId"`
	EventType    string `json:"eventType"`
	EventLevel   string `json:"eventLevel"`
	EventAlias   string `json:"eventAlias"`
	Timestamp    string `json:"timestamp"`
	Content      string `json:"content"`
}

type PushEventResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	EventID string `json:"eventId"`
}

type Identifier struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type Property struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type Tag struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type CreateResourceRequest struct {
	UserId      string                 `json:"userId"`
	Region      string                 `json:"region"`
	ResourceId  string                 `json:"resourceId"`
	TypeName    string                 `json:"typeName"`
	ServiceName string                 `json:"serviceName"`
	Identifiers []Identifier           `json:"identifiers"`
	Properties  []Property             `json:"properties"`
	Tags        []Tag                  `json:"tags"`
	Auth        *common.Authentication `json:"-"`
}

type CreateResourceResponse struct {
	Message     string       `json:"message"`
	Code        string       `json:"code"`
	RequestID   string       `json:"requestId"`
	UserId      string       `json:"userId"`
	Region      string       `json:"region"`
	ServiceName string       `json:"serviceName"`
	TypeName    string       `json:"typeName"`
	ResourceId  string       `json:"resourceId"`
	Identifiers []Identifier `json:"identifiers"`
	Properties  []Property   `json:"properties"`
	Tags        []Tag        `json:"tags"`
}

type GetResourceRequest struct {
	UserId      string                 `json:"-"`
	ServiceName string                 `json:"-"`
	Region      string                 `json:"region"`
	ResourceId  string                 `json:"resourceId"`
	Auth        *common.Authentication `json:"-"`
}

type GetResourceResponse struct {
	Message       string       `json:"message"`
	Code          string       `json:"code"`
	RequestID     string       `json:"requestId"`
	UserId        string       `json:"userId"`
	Region        string       `json:"region"`
	ServiceName   string       `json:"serviceName"`
	TypeName      string       `json:"typeName"`
	ResourceId    string       `json:"resourceId"`
	ErrUpdateTime interface{}  `json:"errUpdateTime"`
	Identifiers   []Identifier `json:"identifiers"`
	Properties    []Property   `json:"properties"`
	Tags          []Tag        `json:"tags"`
}

type DeleteResourceRequest struct {
	UserId      string                 `json:"-"`
	ServiceName string                 `json:"-"`
	Region      string                 `json:"region"`
	ResourceId  string                 `json:"resourceId"`
	Auth        *common.Authentication `json:"-"`
}

type DeleteResourceResponse struct {
	Message   string `json:"message"`
	Code      string `json:"code"`
	RequestID string `json:"requestId"`
}

type InstanceGroupItem struct {
	Id               int64  `json:"id"`
	Name             string `json:"name"`
	ServiceName      string `json:"serviceName"`
	TypeName         string `json:"typeName"`
	Region           string `json:"region"`
	UserId           string `json:"userId"`
	Uuid             string `json:"uuid"`
	Count            int    `json:"count"`
	ServiceNameAlias string `json:"serviceNameAlias"`
	TypeNameAlias    string `json:"typeNameAlias"`
	RegionAlias      string `json:"regionAlias"`
	TagKey           string `json:"tagKey"`
	TypeTarget       string `json:"typeTarget"`
}

type ListInstanceGroupsRequest struct {
	UserId      string                 `json:"-"`
	ServiceName string                 `json:"-"`
	Region      string                 `json:"region"`
	PageNo      int                    `json:"pageNo"`
	PageSize    int                    `json:"pageSize"`
	Auth        *common.Authentication `json:"-"`
}

type ListInstanceGroupsResponse struct {
	Message    string               `json:"message"`
	Code       string               `json:"code"`
	RequestID  string               `json:"requestId"`
	OrderBy    string               `json:"orderBy"`
	Order      string               `json:"order"`
	PageNo     int                  `json:"pageNo"`
	PageSize   int                  `json:"pageSize"`
	TotalCount int                  `json:"totalCount"`
	Result     []*InstanceGroupItem `json:"result"`
}

type GetInstanceGroupRequest struct {
	UserId          string                 `json:"-"`
	ServiceName     string                 `json:"-"`
	Region          string                 `json:"region"`
	InstanceGroupID int64                  `json:"instanceGroupID"`
	Auth            *common.Authentication `json:"-"`
}

type InstanceGroupResponse struct {
	Message   string `json:"message"`
	Code      string `json:"code"`
	RequestID string `json:"requestId"`
	InstanceGroupItem
}

type MonitorResource struct {
	UserId      string `json:"userId"`
	Region      string `json:"region"`
	ServiceName string `json:"serviceName"`
	TypeName    string `json:"typeName"`
	ResourceId  string `json:"resourceId"`
}

type OperateInstanceInGroupRequest struct {
	UserId          string                 `json:"-"`
	ServiceName     string                 `json:"-"`
	Region          string                 `json:"region"`
	Auth            *common.Authentication `json:"-"`
	InstanceGroupID int64                  `json:"instanceGroupID"`
	Name            string                 `json:"name"`
	TypeName        string                 `json:"typeName"`
	ResourceIdList  []*MonitorResource     `json:"resourceIdList"`
}

type ListInstanceInGroupRequest struct {
	UserId          string                 `json:"-"`
	ServiceName     string                 `json:"-"`
	Region          string                 `json:"region"`
	Auth            *common.Authentication `json:"-"`
	InstanceGroupID int64                  `json:"instanceGroupID"`
	ViewType        string                 `json:"viewType"`
	TypeName        string                 `json:"typeName"`
	PageNo          int                    `json:"pageNo"`
	PageSize        int                    `json:"pageSize"`
}

type ListInstanceInGroupResponse struct {
	Message    string `json:"message"`
	Code       string `json:"code"`
	RequestID  string `json:"requestId"`
	OrderBy    string `json:"orderBy"`
	Order      string `json:"order"`
	PageNo     int    `json:"pageNo"`
	PageSize   int    `json:"pageSize"`
	TotalCount int    `json:"totalCount"`
	Result     [][]struct {
		ItemName        string `json:"itemName"`
		ItemAlias       string `json:"itemAlias"`
		ItemValue       string `json:"itemValue"`
		ItemSeq         int    `json:"itemSeq"`
		ItemIdentitable bool   `json:"itemIdentitable"`
		ItemDimension   string `json:"itemDimension"`
		ItemIsConn      bool   `json:"itemIsConn"`
	} `json:"result"`
}

type QueryMetricDataRequest struct {
	UserId         string                 `json:"-"`
	ServiceName    string                 `json:"-"`
	Region         string                 `json:"region"`
	Auth           *common.Authentication `json:"-"`
	MetricName     string                 `json:"metricName"`
	Statistics     []string               `json:"statistics"`
	StartTime      time.Time              `json:"startTime"`
	EndTime        time.Time              `json:"endTime"`
	PeriodInSecond int                    `json:"periodInSecond"`
	Dimensions     string                 `json:"dimensions"`
}
type DataPoint struct {
	Average     float64   `json:"average"`
	Maximum     float64   `json:"maximum"`
	Minimum     float64   `json:"minimum"`
	Sum         float64   `json:"sum"`
	SampleCount int       `json:"sampleCount"`
	Timestamp   time.Time `json:"timestamp"`
}
type QueryMetricDataResponse struct {
	RequestId  string      `json:"requestId"`
	Code       string      `json:"code"`
	UserId     string      `json:"userId"`
	Message    string      `json:"message"`
	DataPoints []DataPoint `json:"dataPoints"`
}

type BcmService interface {
	PushEvents(ctx context.Context, req *PushEventRequest) (rsp *PushEventResponse, err error)
	CreateResource(ctx context.Context, req *CreateResourceRequest) (rsp *CreateResourceResponse, err error)
	GetResource(ctx context.Context, req *GetResourceRequest) (rsp *GetResourceResponse, err error)
	DeleteResource(ctx context.Context, req *DeleteResourceRequest) (rsp *DeleteResourceResponse, err error)
	ListInstanceGroups(ctx context.Context, req *ListInstanceGroupsRequest) (rsp *ListInstanceGroupsResponse, err error)
	GetInstanceGroup(ctx context.Context, req *GetInstanceGroupRequest) (rsp *InstanceGroupResponse, err error)
	AddInstanceToGroup(ctx context.Context, req *OperateInstanceInGroupRequest) (rsp *InstanceGroupResponse, err error)
	RemoveInstanceFromGroup(ctx context.Context, req *OperateInstanceInGroupRequest) (rsp *InstanceGroupResponse, err error)
	ListInstanceInGroup(ctx context.Context, req *ListInstanceInGroupRequest) (rsp *ListInstanceInGroupResponse, err error)
	QueryMetricData(ctx context.Context, req *QueryMetricDataRequest) (rsp *QueryMetricDataResponse, err error)
}
