/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/02/20 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file bcm.go
 * <AUTHOR>
 * @date 2023/02/20 15:43:12
 * @brief
 *
 **/

package bcm

import (
	"context"
	"net/http"
	"strings"

	"github.com/spf13/cast"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type bcmSdk struct {
	conf *bcmConf
	common.OpenApi
}

func NewDefaultBCMSdk() BcmService {
	return newBcmSdk(DefaultServiceName)
}

func NewBcmSdk(serviceName string) BcmService {
	return newBcmSdk(serviceName)
}

func newBcmSdk(serviceName string) *bcmSdk {
	s := &bcmSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}

	return s
}

// doRequest - 通用openstack服务请求方法
func (s *bcmSdk) doRequest(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}
	// 请求 openapi
	err = s.DoRequest(ctx, params, rsp)
	if err != nil {
		return err
	}
	return nil
}

func (s *bcmSdk) PushEvents(ctx context.Context, req *PushEventRequest) (rsp *PushEventResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	rsp = &PushEventResponse{}
	pushEventURI := EventPrefix + req.AccountID + "/services/" + req.ServiceName + EventPostfix
	err = s.doRequest(ctx, "PushEvents", req.Auth, http.MethodPost,
		pushEventURI, nil, req.Result, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "push events request fail, code: %d, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.EventID)
		return nil, cerrs.ErrBCMRequestFail.Errorf("push events fail, code: %d, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

func (s *bcmSdk) DeleteResource(ctx context.Context, req *DeleteResourceRequest) (rsp *DeleteResourceResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.ResourceId == "" || req.Region == "" || req.UserId == "" || req.ServiceName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("param is null")
	}
	rsp = &DeleteResourceResponse{}
	deleteResourceURI := ResourceUriPrefix + req.ServiceName + "/resources/" + req.UserId + "/" + req.Region + "/" + req.ResourceId
	err = s.doRequest(ctx, "DeleteResource", req.Auth, http.MethodDelete,
		deleteResourceURI, nil, nil, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "delete resource fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return rsp, cerrs.ErrBCMRequestFail.Errorf("delete resource fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

func (s *bcmSdk) GetResource(ctx context.Context, req *GetResourceRequest) (rsp *GetResourceResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.ResourceId == "" || req.Region == "" || req.UserId == "" || req.ServiceName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("param is null")
	}
	rsp = &GetResourceResponse{}
	getResourceURI := ResourceUriPrefix + req.ServiceName + "/resources/" + req.UserId + "/" + req.Region + "/" + req.ResourceId
	err = s.doRequest(ctx, "GetResource", req.Auth, http.MethodGet,
		getResourceURI, nil, nil, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "get resource fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return rsp, cerrs.ErrBCMRequestFail.Errorf("get resource fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

func (s *bcmSdk) CreateResource(ctx context.Context, req *CreateResourceRequest) (rsp *CreateResourceResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.ResourceId == "" || req.Region == "" || req.UserId == "" || req.ServiceName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("param is null")
	}
	rsp = &CreateResourceResponse{}
	createResourceURI := ResourceUriPrefix + req.ServiceName + "/resources"
	err = s.doRequest(ctx, "CreateResource", req.Auth, http.MethodPost,
		createResourceURI, nil, req, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "create resource fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return rsp, cerrs.ErrBCMRequestFail.Errorf("create resource fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// ListInstanceGroups lists all instance groups in the account
func (s *bcmSdk) ListInstanceGroups(ctx context.Context, req *ListInstanceGroupsRequest) (rsp *ListInstanceGroupsResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.PageNo == 0 || req.PageSize == 0 ||
		req.UserId == "" || req.ServiceName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("param is null")
	}
	rsp = &ListInstanceGroupsResponse{}
	uri := InstanceGroupPrefix + req.UserId + "/instance-group/list"
	querys := map[string]interface{}{
		"pageNo":      req.PageNo,
		"pageSize":    req.PageSize,
		"serviceName": req.ServiceName}
	if req.Region != "" {
		querys["region"] = req.Region
	}
	err = s.doRequest(ctx, "ListInstanceGroups", req.Auth, http.MethodGet,
		uri, querys, nil, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "list instance groups fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return rsp, cerrs.ErrBCMRequestFail.Errorf("list instance groups fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// GetInstanceGroup 获取实例组详情
func (s *bcmSdk) GetInstanceGroup(ctx context.Context, req *GetInstanceGroupRequest) (rsp *InstanceGroupResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.Region == "" || req.InstanceGroupID == 0 ||
		req.UserId == "" || req.ServiceName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("param is null")
	}
	rsp = &InstanceGroupResponse{}
	uri := InstanceGroupPrefix + req.UserId + "/instance-group/" + cast.ToString(req.InstanceGroupID)
	err = s.doRequest(ctx, "GetInstanceGroup", req.Auth, http.MethodGet,
		uri, nil, nil, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "get instance group fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return rsp, cerrs.ErrBCMRequestFail.Errorf("get instance group fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// ListInstanceInGroup lists all instance in the instance group
func (s *bcmSdk) ListInstanceInGroup(ctx context.Context, req *ListInstanceInGroupRequest) (rsp *ListInstanceInGroupResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.Region == "" || req.InstanceGroupID == 0 ||
		req.UserId == "" || req.ServiceName == "" ||
		req.ViewType == "" || req.TypeName == "" ||
		req.PageNo == 0 || req.PageSize == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("param is null")
	}

	querys := map[string]interface{}{
		"id":          req.InstanceGroupID,
		"pageSize":    req.PageSize,
		"pageNo":      req.PageNo,
		"serviceName": req.ServiceName,
		"userId":      req.UserId,
		"typeName":    req.TypeName,
		"region":      req.Region,
		"viewType":    req.ViewType,
	}
	rsp = &ListInstanceInGroupResponse{}
	uri := InstanceGroupPrefix + req.UserId + "/instance-group/instance/list"
	err = s.doRequest(ctx, "ListInstanceInGroup", req.Auth, http.MethodGet,
		uri, querys, nil, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "list instance in group fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return rsp, cerrs.ErrBCMRequestFail.Errorf("list instance in group fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// AddInstanceToGroup 往实例组内添加实例,幂等接口
func (s *bcmSdk) AddInstanceToGroup(ctx context.Context, req *OperateInstanceInGroupRequest) (rsp *InstanceGroupResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.Region == "" || req.InstanceGroupID == 0 ||
		req.UserId == "" || req.ServiceName == "" ||
		req.Name == "" || req.TypeName == "" ||
		len(req.ResourceIdList) == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("param is null")
	}

	rsp = &InstanceGroupResponse{}
	uri := InstanceGroupPrefix + req.UserId + "/instance-group/" + cast.ToString(req.InstanceGroupID) + "/instance/add"
	err = s.doRequest(ctx, "AddInstanceToGroup", req.Auth, http.MethodPost,
		uri, nil, req, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "add instance to group fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return rsp, cerrs.ErrBCMRequestFail.Errorf("add instance to group fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// RemoveInstanceFromGroup 从实例组内删除实例,幂等接口
func (s *bcmSdk) RemoveInstanceFromGroup(ctx context.Context, req *OperateInstanceInGroupRequest) (rsp *InstanceGroupResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.Region == "" || req.InstanceGroupID == 0 ||
		req.UserId == "" || req.ServiceName == "" ||
		req.Name == "" || req.TypeName == "" || req.ResourceIdList == nil ||
		len(req.ResourceIdList) == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("param is null")
	}

	rsp = &InstanceGroupResponse{}
	uri := InstanceGroupPrefix + req.UserId + "/instance-group/" + cast.ToString(req.InstanceGroupID) + "/instance/remove"
	err = s.doRequest(ctx, "RemoveInstanceFromGroup", req.Auth, http.MethodPost,
		uri, nil, req, rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "remove instance from group fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestID)
		return rsp, cerrs.ErrBCMRequestFail.Errorf("remove instance from group fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/KzHUM_sAtc/Daasq-4ic7/8fErh7qXBbV-eC
// QueryMetricData 查询监控数据
func (s *bcmSdk) QueryMetricData(ctx context.Context, req *QueryMetricDataRequest) (rsp *QueryMetricDataResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	rsp = &QueryMetricDataResponse{}
	uri := QueryMetricDataPrefix + req.UserId + "/" + req.ServiceName + "/" + req.MetricName
	querys := map[string]interface{}{
		"statistics[]":   strings.Join(req.Statistics, ","),
		"startTime":      base_utils.FormatWithISO8601(req.StartTime),
		"endTime":        base_utils.FormatWithISO8601(req.EndTime),
		"periodInSecond": req.PeriodInSecond,
		"dimensions":     req.Dimensions,
	}
	if req.Region != "" {
		querys["region"] = req.Region
	}
	err = s.doRequest(ctx, "QueryMetricData", req.Auth, http.MethodGet,
		uri, querys, nil, rsp)
	return
}
