package crm

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const (
	TestIamUserId = "45492ef3f0a74cda8bdb5660c9dc2d0a"
	TestUser      = "45492ef3f0a74cda8bdb5660c9dc2d0a"
	ServiceId     = "5bb35ef26f944f118cf0d569867db929"
	ResourceID    = "dd6080cc7e7544fbbc83c70ef57789d4"
)

func TestCrmSdk_GetCustomerDetailInfo(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newCrmSdk(DefaultServiceName)

	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     TestUser,
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	auth := authRsp.Auth

	Rsp, err := s.GetCustomerDetailInfo(ctx, &GetCustomerDetailReq{
		UserId: TestIamUserId,
		Auth:   auth,
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(base_utils.Format(Rsp))
}
