// Package crm
package crm

import (
	"context"
	"net/http"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

// crm 获取用户身份信息
type crmSdk struct {
	conf *crmConf
	common.OpenApi
}

func NewBefaultCrmSdk() CrmService {
	return newCrmSdk(DefaultServiceName)
}

func NewCrmSdk(serviceName string) CrmService {
	return newCrmSdk(serviceName)
}

func newCrmSdk(serviceName string) *crmSdk {
	s := &crmSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}

	return s
}

func (s *crmSdk) doCrmRequest(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {
	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}
	// 请求 openapi
	return s.DoRequest(ctx, params, rsp)
}

func (s *crmSdk) GetCustomerDetailInfo(ctx context.Context, req *GetCustomerDetailReq) (rsp *CustomerDetailResp, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.UserId == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param userId is null")
	}

	rsp = &CustomerDetailResp{}

	uri := getCustomerDetailUri + "/" + req.UserId
	if err = s.doCrmRequest(ctx, "GetCustomerDetailInfo", req.Auth, http.MethodGet,
		uri, nil, nil, rsp); err != nil {
		return nil, err
	}

	return rsp, nil
}
