// Package crm
package crm

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type GetCustomerDetailReq struct {
	UserId string                 `json:"userId"`
	Auth   *common.Authentication `json:"auth"`
}

type CustomerDetailInfo struct {
	StsError   bool   `json:"stsError"`
	IsInternal bool   `json:"IsInternal"`
	UserType   string `json:"userType"`
	ErrorMsg   string `json:"errorMsg"`
}

type CustomerDetailResp struct {
	Customer *CustomerDetail `json:"customer"`
	TagList  CustomerTagList `json:"tagList"`
}

type CustomerDetail struct {
	AccountId                string                `json:"accountId"`
	Name                     string                `json:"name"`
	Cname                    string                `json:"cname"`
	UserType                 string                `json:"userType"`
	Email                    string                `json:"email"`
	MobilePhone              string                `json:"mobilePhone"`
	Phone                    string                `json:"phone"`
	Company                  string                `json:"company"`
	Address                  string                `json:"address"`
	Industry                 string                `json:"industry"`
	IndustryDetail           string                `json:"industryDetail"`
	IndustryType             []string              `json:"industryType"`
	Business                 string                `json:"business"`
	Website                  string                `json:"website"`
	RegisterTime             string                `json:"registerTime"`
	BaeUser                  string                `json:"baeUser"`
	CodeChecked              bool                  `json:"codeChecked"`
	MobilePhoneVerified      bool                  `json:"mobilePhoneVerified"`
	EmailVerified            bool                  `json:"emailVerified"`
	MobilePhoneLastVerified  string                `json:"mobilePhoneLastVerified"`
	EmailLastVerified        string                `json:"emailLastVerified"`
	Extra                    string                `json:"extra"`
	Fchost                   bool                  `json:"fchost"`
	ServiceAccount           bool                  `json:"serviceAccount"`
	ActivateTime             string                `json:"activateTime"`
	Province                 string                `json:"province"`
	City                     string                `json:"city"`
	LoginName                string                `json:"loginName"`
	AccountManagers          []CustomerAccountInfo `json:"accountManagers"`
	TechniqueAccountManagers string                `json:"techniqueAccountManagers"`
	RealnameBind             string                `json:"realnameBind"`
	StatusFlag               bool                  `json:"statusFlag"`
}

type CustomerAccountInfo struct {
	AccountId           string `json:"accountId"`
	AccountManagerName  string `json:"accountManagerName"`
	AccountManagerPhone string `json:"accountManagerPhone"`
	AccountManagerEmail string `json:"accountManagerEmail"`
}

type CustomerTagList struct {
	TagResponses []CustomerTagResponses `json:"tagResponses"`
}

type CustomerTagResponses struct {
	TagId       string `json:"tagId"`
	TagName     string `json:"tagName"`
	Description string `json:"description"`
	TagTypeId   string `json:"tagTypeId"`
}

type CrmService interface {
	GetCustomerDetailInfo(ctx context.Context, req *GetCustomerDetailReq) (rsp *CustomerDetailResp, err error)
}
