/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/24 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file crm_conf.go
 * <AUTHOR>
 * @date 2023/03/24 15:15:47
 * @brief crm conf

 *
 **/

package crm

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "crm"

// crmConf definition
type crmConf struct {
	Product string `toml:"Product,omitempty"`
}

var crmConfMap = &sync.Map{}

func getConf(serviceName string) *crmConf {
	if conf, ok := crmConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*crmConf); ok {
			return conf
		}
	}

	conf := &crmConf{}
	conf.mustLoad(serviceName)

	crmConfMap.Store(serviceName, conf)

	return conf
}

func (conf *crmConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
