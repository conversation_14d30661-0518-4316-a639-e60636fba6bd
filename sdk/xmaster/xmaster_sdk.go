/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/04/12
 * File: xmaster.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package xmaster TODO package function desc
package xmaster

import (
	"context"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

var (
	ErrParamIsNilptr    = cerrs.ErrInvalidParams.Errorf("params is nil ptr")
	ErrClusterIdIsEmpty = cerrs.ErrInvalidParams.Errorf("clusterid is empty")
)

type xmasterSdk struct {
	conf      *XmasterConf
	ralCaller sdk_utils.RalCaller
}

type doRequestParams struct {
	HttpMethod string
	Uri        string
	Queries    interface{}
	Payload    interface{}
	Headers    interface{}
	RspPtr     interface{}
}

func NewDefaultXmasterSdk() XMasterService {
	return newXmasterSdk(DefaultServiceName)
}

func NewXmasterSdk(serviceName string) XMasterService {
	return newXmasterSdk(serviceName)
}

func newXmasterSdk(serviceName string) *xmasterSdk {
	if serviceName == "" {
		panic("invalid service name")
	}
	x := &xmasterSdk{
		conf: getConf(serviceName),
		ralCaller: sdk_utils.NewRalCaller(
			serviceName,
			sdk_utils.ROptGenCurlLog(logit.TraceLevel),
			sdk_utils.ROptPrepareChecker(
				func(ctx context.Context, httpRsp *sdk_utils.HttpResponse, invoker sdk_utils.PrepareCheckFunc) error {
					logger.SdkLogger.Debug(ctx, "xmaster http rsp: %v", httpRsp)
					return invoker(ctx, httpRsp)
				}),
		),
	}
	return x
}

func (x *xmasterSdk) doRequest(ctx context.Context, params *doRequestParams) error {
	ralCaller := x.ralCaller.WithOption()
	if params.Headers == nil {
		params.Headers = map[string]string{x.conf.XmTokenKey: x.conf.XmTokenValue}
	} else {
		params.Headers.(map[string]interface{})[x.conf.XmTokenKey] = x.conf.XmTokenValue
	}
	err := ralCaller.HttpRequest(ctx, params.HttpMethod, params.Uri, params.Queries,
		params.Payload, params.Headers, params.RspPtr)
	logger.SdkLogger.Debug(ctx, "call xmaster api", logit.String("ral params", base_utils.Format(params)))
	if err != nil {
		logger.SdkLogger.Warning(ctx, "call xmaster fail", logit.String("ral params", base_utils.Format(params)))
	}
	return err
}

func (x *xmasterSdk) QueryClusterHealthStatus(ctx context.Context, req *QueryClusterHealthStatusRequest) (rsp *QueryClusterHealthStatusResponse, err error) {
	if req == nil {
		return nil, ErrParamIsNilptr
	}
	if len(req.ClusterId) == 0 {
		return nil, ErrClusterIdIsEmpty
	}
	rsp = &QueryClusterHealthStatusResponse{}
	err = x.doRequest(ctx, &doRequestParams{
		HttpMethod: QueryClusterHealthStatusMethod,
		Uri:        UriPath + req.ClusterId + QueryClusterHealthStatusAction,
		Queries:    nil,
		Payload:    nil,
		RspPtr:     rsp,
	})
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func (x *xmasterSdk) QueryCluster(ctx context.Context, req *QueryClusterRequest) (rsp *QueryClusterResponse, err error) {
	if req == nil {
		return nil, ErrParamIsNilptr
	}
	if len(req.ClusterId) == 0 {
		return nil, ErrClusterIdIsEmpty
	}
	rsp = &QueryClusterResponse{}
	err = x.doRequest(ctx, &doRequestParams{
		HttpMethod: QueryClusterMethod,
		Uri:        UriPath + req.ClusterId + QueryClusterAction,
		Queries:    nil,
		Payload:    nil,
		RspPtr:     rsp,
	})
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func (x *xmasterSdk) CreateCluster(ctx context.Context, req *CreateClusterRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, ErrParamIsNilptr
	}
	if len(req.ClusterId) == 0 {
		return nil, ErrClusterIdIsEmpty
	}
	rsp = &CommonResponse{}
	err = x.doRequest(ctx, &doRequestParams{
		HttpMethod: CreateClusterMethod,
		Uri:        UriPath + req.ClusterId + CreateClusterAction,
		Queries:    nil,
		Payload:    base_utils.Format(req.Topo),
		RspPtr:     rsp,
	})
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func (x *xmasterSdk) UpdateCluster(ctx context.Context, req *CreateClusterRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, ErrParamIsNilptr
	}
	if len(req.ClusterId) == 0 {
		return nil, ErrClusterIdIsEmpty
	}
	rsp = &CommonResponse{}
	err = x.doRequest(ctx, &doRequestParams{
		HttpMethod: UpdateClusterMethod,
		Uri:        UriPath + req.ClusterId + UpdateClusterAction,
		Queries:    nil,
		Payload:    base_utils.Format(req.Topo),
		RspPtr:     rsp,
	})
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func (x *xmasterSdk) DeleteCluster(ctx context.Context, req *DeleteClusterRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, ErrParamIsNilptr
	}
	if len(req.ClusterId) == 0 {
		return nil, ErrClusterIdIsEmpty
	}
	rsp = &CommonResponse{}
	err = x.doRequest(ctx, &doRequestParams{
		HttpMethod: DeleteClusterMethod,
		Uri:        UriPath + req.ClusterId + DeleteClusterAction,
		Queries:    nil,
		Payload:    nil,
		RspPtr:     rsp,
	})
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func (x *xmasterSdk) SetMonitorStrategy(ctx context.Context, req *SetMonitorStrategyRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, ErrParamIsNilptr
	}
	if len(req.ClusterId) == 0 {
		return nil, ErrClusterIdIsEmpty
	}
	rsp = &CommonResponse{}
	err = x.doRequest(ctx, &doRequestParams{
		HttpMethod: SetMonitorStrategyMethod,
		Uri:        UriPath + req.ClusterId + SetMonitorStrategyAction,
		Queries:    nil,
		Payload:    base_utils.Format(req.Rule),
		RspPtr:     rsp,
	})
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func (x *xmasterSdk) GetMonitorStrategy(ctx context.Context, req *GetMonitorStrategyRequest) (rsp *GetMonitorStrategyResponse, err error) {
	if req == nil {
		return nil, ErrParamIsNilptr
	}
	if len(req.ClusterId) == 0 {
		return nil, ErrClusterIdIsEmpty
	}
	rsp = &GetMonitorStrategyResponse{}
	err = x.doRequest(ctx, &doRequestParams{
		HttpMethod: GetMonitorStrategyMethod,
		Uri:        UriPath + req.ClusterId + GetMonitorStrategyAction,
		Queries:    nil,
		Payload:    nil,
		RspPtr:     rsp,
	})
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func (x *xmasterSdk) SetMonitorSwitch(ctx context.Context, req *SetMonitorSwitchRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, ErrParamIsNilptr
	}
	if len(req.ClusterId) == 0 {
		return nil, ErrClusterIdIsEmpty
	}
	rsp = &CommonResponse{}
	err = x.doRequest(ctx, &doRequestParams{
		HttpMethod: SetMonitorSwitchMethod,
		Uri:        UriPath + req.ClusterId + SetMonitorSwitchAction,
		Queries:    nil,
		Payload:    base_utils.Format(Switch{Enable: req.Enable}),
		RspPtr:     rsp,
	})
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func (x *xmasterSdk) GetMonitorSwitch(ctx context.Context, req *GetMonitorSwitchRequest) (rsp *GetMonitorSwitchResponse, err error) {
	if req == nil {
		return nil, ErrParamIsNilptr
	}
	if len(req.ClusterId) == 0 {
		return nil, ErrClusterIdIsEmpty
	}
	rsp = &GetMonitorSwitchResponse{}
	err = x.doRequest(ctx, &doRequestParams{
		HttpMethod: GetMonitorSwitchMethod,
		Uri:        UriPath + req.ClusterId + GetMonitorSwitchAction,
		Queries:    nil,
		Payload:    nil,
		RspPtr:     rsp,
	})
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func (x *xmasterSdk) SetTaskFakeSwitch(ctx context.Context, req *SetTaskFakeSwitchRequest) (rsp *CommonResponse, err error) {
	if req == nil {
		return nil, ErrParamIsNilptr
	}
	if len(req.ClusterId) == 0 {
		return nil, ErrClusterIdIsEmpty
	}
	rsp = &CommonResponse{}
	err = x.doRequest(ctx, &doRequestParams{
		HttpMethod: SetTaskFakeSwitchMethod,
		Uri:        UriPath + req.ClusterId + SetTaskFakeSwitchAction,
		Queries:    nil,
		Payload:    base_utils.Format(TaskFakeSwitch{Enable: req.Enable}),
		RspPtr:     rsp,
	})
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func (x *xmasterSdk) GetTaskFakeSwitch(ctx context.Context, req *GetTaskFakeSwitchRequest) (rsp *GetTaskFakeSwitchResponse, err error) {
	if req == nil {
		return nil, ErrParamIsNilptr
	}
	if len(req.ClusterId) == 0 {
		return nil, ErrClusterIdIsEmpty
	}
	rsp = &GetTaskFakeSwitchResponse{}
	err = x.doRequest(ctx, &doRequestParams{
		HttpMethod: GetTaskFakeSwitchMethod,
		Uri:        UriPath + req.ClusterId + GetTaskFakeSwitchAction,
		Queries:    nil,
		Payload:    nil,
		RspPtr:     rsp,
	})
	if err != nil {
		return nil, err
	}
	return rsp, nil
}
