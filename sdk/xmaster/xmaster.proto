//protofsg -with_context -json_tag=1 xmaster.proto interface.go
package x1base.sdk.xmaster;

// CommonResponse 通用请求response  HttpResult
message CommonResponse {
    optional int32 success = 1;
    optional string code = 2;
    optional string message = 3;
    optional string requestId = 4;
}

// HttpResult.code定义
extend google.protobuf.FileOptions {
    optional string HttpErrSuccess = 1;
    optional string HttpErrNotFound = 2;
    optional string HttpErrMissingParam = 3;
    optional string HttpErrInternalError = 4;
    optional string HttpErrClusterNotFound = 5;
    optional string HttpErrInstanceNotFound = 6;
    optional string HttpErrInstanceOffline = 7;
    optional string HttpErrInstanceDeleteBusy = 8;
    optional string HttpErrInstanceForceDeleteNotAllowed = 9;
    optional string HttpErrMalformedJson = 10;
    optional string HttpErrInvalidParam = 11;
    optional string HttpErrFlowRateLimit = 12;
    optional string HttpErrClusterExist = 13;
    optional string HttpErrHostExist = 14;
    optional string HttpErrServiceNotExist = 15;
    optional string HttpErrNoAvailableMachine = 16;
    optional string HttpErrMachineAlreadyExist = 17;
    optional string HttpErrMachineServing = 18;
    optional string HttpErrMachineNotExist = 19;
    optional string HttpErrMachineDiskNotExist = 20;
    optional string HttpErrMachineDiskAlreadyExist = 21;
    optional string HttpErrMachineDiskInUse = 22;
    optional string HttpErrMachineDiskTooSmall = 23;
    optional string HttpErrMachineCPUTooSmall = 24;
    optional string HttpErrMachineMemTooSmall = 25;
    optional string HttpErrInstanceNotExist = 26;
    optional string HttpErrInstanceLabelAlreadyExist = 27;
    optional string HttpErrInstanceLabelNotExist = 28;
    optional string HttpErrServiceAlreadyExist = 29;
    optional string HttpErrServiceServing = 30;
    optional string HttpErrResourceNotAvailable = 31;
    optional string HttpErrZoneNotExist = 32;
    optional string HttpErrRegionNotExist = 33;
    optional string HttpErrPoolAlreadyExist = 34;
    optional string HttpErrPoolNotExist = 35;
    optional string HttpErrPoolServing = 36;
    optional string HttpErrProductAlreadyExist = 37;
    optional string HttpErrProductNotExist = 38;
    optional string HttpErrProductServing = 39;
    optional string HttpErrServiceUnavailable = 40;
}
option (HttpErrSuccess) = "Success";
option (HttpErrNotFound) = "NotFound";
option (HttpErrMissingParam) = "MissingParam";
option (HttpErrInternalError) = "InternalError";
option (HttpErrClusterNotFound) = "ClusterNotFound";
option (HttpErrInstanceNotFound) = "InstanceNotFound";
option (HttpErrInstanceOffline) = "InstanceOffline";
option (HttpErrInstanceDeleteBusy) = "InstanceDeleteBusy";
option (HttpErrInstanceForceDeleteNotAllowed) = "InstanceForceDeleteNotAllowed";
option (HttpErrMalformedJson) = "MalformedJSON";
option (HttpErrInvalidParam) = "InvalidParam";
option (HttpErrFlowRateLimit) = "FlowRateLimit";
option (HttpErrClusterExist) = "ClusterExist";
option (HttpErrHostExist) = "HostExist";
option (HttpErrServiceNotExist) = "ServiceNotExist";
option (HttpErrNoAvailableMachine) = "NoAvailableMachine";
option (HttpErrMachineAlreadyExist) = "MachineExist";
option (HttpErrMachineServing) = "MachineServing";
option (HttpErrMachineNotExist) = "MachineNotExist";
option (HttpErrMachineDiskNotExist) = "MachineDiskNotExist";
option (HttpErrMachineDiskAlreadyExist) = "MachineDiskAlreadyExist";
option (HttpErrMachineDiskInUse) = "MachineDiskInUse";
option (HttpErrMachineDiskTooSmall) = "MachineDiskTooSmall";
option (HttpErrMachineCPUTooSmall) = "MachineCPUTooSmall";
option (HttpErrMachineMemTooSmall) = "MachineMemTooSmall";
option (HttpErrInstanceNotExist) = "InstanceNotExist";
option (HttpErrInstanceLabelAlreadyExist) = "InstanceLabelAlreadyExist";
option (HttpErrInstanceLabelNotExist) = "InstanceLabelNotExist";
option (HttpErrServiceAlreadyExist) = "ServiceExist";
option (HttpErrServiceServing) = "ServiceServing";
option (HttpErrResourceNotAvailable) = "ResourceNotAvailable";
option (HttpErrZoneNotExist) = "ZoneNotExist";
option (HttpErrRegionNotExist) = "RegionNotExist";
option (HttpErrPoolAlreadyExist) = "PoolExist";
option (HttpErrPoolNotExist) = "PoolNotExist";
option (HttpErrPoolServing) = "PoolServing";
option (HttpErrProductAlreadyExist) = "ProductExist";
option (HttpErrProductNotExist) = "ProductNotExist";
option (HttpErrProductServing) = "ProductServing";
option (HttpErrServiceUnavailable) = "ServiceUnavailable";


// InnerClusterTopoSetRequest 内部实现request用这个
message InnerClusterTopoSetRequest {
    optional string cluster_id = 1;
    // topo ClusterTopology的json形式
    optional string topo = 2;
}

message Instance {
    optional string instanceId = 1;
    optional int64 LastActiveTime = 2;
    map<string, string> attributes = 3;
}
message ClusterTopology {
    repeated Instance instances = 1;
    map<string, string> attributes = 2;
}

message CreateClusterRequest {
    optional string cluster_id = 1;
    optional ClusterTopology topo = 2;
}

message DeleteClusterRequest {
    optional string cluster_id = 1;
}

message QueryClusterRequest {
    optional string cluster_id = 1;
}

message QueryClusterResponse {
    optional int32 success = 1;
    optional string code = 2;
    optional string message = 3;
    optional string requestId = 4;
    repeated Instance instances = 5;
    map<string, string> attributes = 6;
}

// InnerSetMonitorStrategyRequest 内部实现request用这个
message InnerSetMonitorStrategyRequest {
    optional string cluster_id = 1;
    // rule MonitorConfig的json形式
    optional string rule = 2;
}

message MutexInfo {
    optional string mutexId = 1;
}
message MonitorItem {
    optional string Name = 1;
    optional string Cond = 2;
    optional string Expr = 3;
    optional string Filter = 4;
    repeated MutexInfo MutexList = 5;
    optional int32 Level = 6;
    optional int32 Timeout = 7;
    map<string, string> tag = 8;
}
message MonitorConfig {
    repeated MonitorItem rules = 1;
}
message SetMonitorStrategyRequest {
    optional string cluster_id = 1;
    optional MonitorConfig rule = 2;
}

message GetMonitorStrategyRequest {
    optional string cluster_id = 1;
}
message GetMonitorStrategyResponse {
    optional int32 success = 1;
    optional string code = 2;
    optional string message = 3;
    optional string requestId = 4;
    repeated MonitorItem rules = 5;
}

// InnerSetMonitorSwitchRequest 内部实现request用这个
message InnerSetMonitorSwitchRequest {
    optional string cluster_id = 1;
    // enable Switch的json形式
    optional string enable = 2;
}
message Switch {
    optional bool enable = 1;
}
message SetMonitorSwitchRequest {
    optional string cluster_id = 1;
    optional bool enable = 2;
}
message GetMonitorSwitchRequest {
    optional string cluster_id = 1;
}
message GetMonitorSwitchResponse {
    optional int32 success = 1;
    optional string code = 2;
    optional string message = 3;
    optional string requestId = 4;
    optional bool enable = 5;
}

// InnerSetTaskFakeSwitchRequest 内部实现request用这个
message InnerSetTaskFakeSwitchRequest {
    optional string cluster_id = 1;
    // enable Switch的json形式
    optional string enable = 2;
}
message TaskFakeSwitch {
    optional bool enable = 1;
}
message SetTaskFakeSwitchRequest {
    optional string cluster_id = 1;
    optional bool enable = 2;
}
message GetTaskFakeSwitchRequest {
    optional string cluster_id = 1;
}
message GetTaskFakeSwitchResponse {
    optional int32 success = 1;
    optional string code = 2;
    optional string message = 3;
    optional string requestId = 4;
    optional bool enable = 5;
}

message QueryClusterHealthStatusRequest {
    optional string cluster_id = 1;
}

message IsHealthy {
    optional bool instance_alive = 1;
}

message HealthInfo {
    optional string instanceId = 1;
    optional IsHealthy isHealthy = 2;
    optional int64 LastActiveTime = 3;
    map<string, string> monitorInfo = 4;
}

message QueryClusterHealthStatusResponse {
    optional int32 success = 1;
    optional string code = 2;
    optional string message = 3;
    optional string requestId = 4;
    repeated HealthInfo health_info = 5;
}

//XMasterService xmaster部分api实现
service XMasterService {
    // QueryClusterHealthStatus http_health_status_cmd
    rpc QueryClusterHealthStatus(QueryClusterHealthStatusRequest) returns (QueryClusterHealthStatusResponse);
    // CreateCluster http_topo_set_cmd
    rpc CreateCluster(CreateClusterRequest) returns (CommonResponse);
    // UpdateCluster http_topo_set_cmd
    rpc UpdateCluster(CreateClusterRequest) returns (CommonResponse);
    // DeleteCluster http_topo_delete_cmd
    rpc DeleteCluster(DeleteClusterRequest) returns (CommonResponse);
    // QueryCluster http_topo_list_cmd
    rpc QueryCluster(QueryClusterRequest) returns (QueryClusterResponse);
    // SetMonitorStrategy http_monitor_rule_set_cmd
    rpc SetMonitorStrategy(SetMonitorStrategyRequest) returns (CommonResponse);
    // GetMonitorStrategy http_monitor_rule_list_cmd
    rpc GetMonitorStrategy(GetMonitorStrategyRequest) returns (GetMonitorStrategyResponse);
    // SetMonitorSwitch http_monitor_switch_cmd
    rpc SetMonitorSwitch(SetMonitorSwitchRequest) returns (CommonResponse);
    // GetMonitorSwitch http_monitor_switch_list_cmd
    rpc GetMonitorSwitch(GetMonitorSwitchRequest) returns (GetMonitorSwitchResponse);
    // SetTaskFakeSwitch http_task_fake_switch_cmd
    rpc SetTaskFakeSwitch(SetTaskFakeSwitchRequest) returns (CommonResponse);
    // GetTaskFakeSwitch http_task_fake_switch_list_cmd
    rpc GetTaskFakeSwitch(GetTaskFakeSwitchRequest) returns (GetTaskFakeSwitchResponse);
}

