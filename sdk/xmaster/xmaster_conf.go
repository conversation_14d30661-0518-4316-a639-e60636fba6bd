package xmaster

import (
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"sync"
)

const extraConfName = "XMASTER"

type XmasterConf struct {
	XmTokenKey string `toml:"XmTokenKey"`
	XmTokenValue string `toml:"XmTokenValue"`
}

var xmasterConfMap = &sync.Map{}

func getConf(serviceName string) *XmasterConf {
	if conf, ok := xmasterConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*XmasterConf); ok {
			return conf
		}
	}

	conf := &XmasterConf{}
	conf.mustLoad(serviceName)

	if len(conf.XmTokenValue) <= 0 {
		conf.XmTokenValue = DefaultXmTokenValue
	}
	if len(conf.XmTokenKey) <= 0 {
		conf.XmTokenKey = DefaultXmTokenKey
	}

	xmasterConfMap.Store(serviceName, conf)

	return conf
}

func (conf *XmasterConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
