/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-04-12
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据xmaster.proto生成的interface文件
 */

// Package xmaster
package xmaster

import (
	"context"
)

const (
	HttpErrSuccess                       = "Success"
	HttpErrNotFound                      = "NotFound"
	HttpErrMissingParam                  = "MissingParam"
	HttpErrInternalError                 = "InternalError"
	HttpErrClusterNotFound               = "ClusterNotFound"
	HttpErrInstanceNotFound              = "InstanceNotFound"
	HttpErrInstanceOffline               = "InstanceOffline"
	HttpErrInstanceDeleteBusy            = "InstanceDeleteBusy"
	HttpErrInstanceForceDeleteNotAllowed = "InstanceForceDeleteNotAllowed"
	HttpErrMalformedJson                 = "MalformedJSON"
	HttpErrInvalidParam                  = "InvalidParam"
	HttpErrFlowRateLimit                 = "FlowRateLimit"
	HttpErrClusterExist                  = "ClusterExist"
	HttpErrHostExist                     = "HostExist"
	HttpErrServiceNotExist               = "ServiceNotExist"
	HttpErrNoAvailableMachine            = "NoAvailableMachine"
	HttpErrMachineAlreadyExist           = "MachineExist"
	HttpErrMachineServing                = "MachineServing"
	HttpErrMachineNotExist               = "MachineNotExist"
	HttpErrMachineDiskNotExist           = "MachineDiskNotExist"
	HttpErrMachineDiskAlreadyExist       = "MachineDiskAlreadyExist"
	HttpErrMachineDiskInUse              = "MachineDiskInUse"
	HttpErrMachineDiskTooSmall           = "MachineDiskTooSmall"
	HttpErrMachineCPUTooSmall            = "MachineCPUTooSmall"
	HttpErrMachineMemTooSmall            = "MachineMemTooSmall"
	HttpErrInstanceNotExist              = "InstanceNotExist"
	HttpErrInstanceLabelAlreadyExist     = "InstanceLabelAlreadyExist"
	HttpErrInstanceLabelNotExist         = "InstanceLabelNotExist"
	HttpErrServiceAlreadyExist           = "ServiceExist"
	HttpErrServiceServing                = "ServiceServing"
	HttpErrResourceNotAvailable          = "ResourceNotAvailable"
	HttpErrZoneNotExist                  = "ZoneNotExist"
	HttpErrRegionNotExist                = "RegionNotExist"
	HttpErrPoolAlreadyExist              = "PoolExist"
	HttpErrPoolNotExist                  = "PoolNotExist"
	HttpErrPoolServing                   = "PoolServing"
	HttpErrProductAlreadyExist           = "ProductExist"
	HttpErrProductNotExist               = "ProductNotExist"
	HttpErrProductServing                = "ProductServing"
	HttpErrServiceUnavailable            = "ServiceUnavailable"
)

const (
	UriPath = "/v1/xsr/"

	QueryClusterHealthStatusMethod = "GET"
	QueryClusterHealthStatusAction = "/healthStatus"

	QueryClusterMethod = "GET"
	QueryClusterAction = "/topo"

	CreateClusterMethod = "POST"
	CreateClusterAction = "/topo"

	UpdateClusterMethod = "PUT"
	UpdateClusterAction = "/topo"

	DeleteClusterMethod = "DELETE"
	DeleteClusterAction = "/topo"

	SetMonitorStrategyMethod = "PUT"
	SetMonitorStrategyAction = "/rule"

	GetMonitorStrategyMethod = "GET"
	GetMonitorStrategyAction = "/rule"

	SetMonitorSwitchMethod = "PUT"
	SetMonitorSwitchAction = "/switch"

	GetMonitorSwitchMethod = "GET"
	GetMonitorSwitchAction = "/switch"

	SetTaskFakeSwitchMethod = "PUT"
	SetTaskFakeSwitchAction = "/fakeSwitch"

	GetTaskFakeSwitchMethod = "GET"
	GetTaskFakeSwitchAction = "/fakeSwitch"
)

// CommonResponse 通用请求response  HttpResult
type CommonResponse struct {
	Success   int32  `json:"success"`
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
}

// InnerClusterTopoSetRequest 内部实现request用这个
type InnerClusterTopoSetRequest struct {
	ClusterId string `json:"cluster_id"`
	// Topo ClusterTopology的json形式
	Topo string `json:"topo"`
}

type Instance struct {
	InstanceId     string            `json:"instanceId"`
	LastActiveTime int64             `json:"LastActiveTime"`
	Attributes     map[string]string `json:"attributes"`
}

type ClusterTopology struct {
	Instances  []*Instance       `json:"instances"`
	Attributes map[string]string `json:"attributes"`
}

type CreateClusterRequest struct {
	ClusterId string           `json:"cluster_id"`
	Topo      *ClusterTopology `json:"topo"`
}

type DeleteClusterRequest struct {
	ClusterId string `json:"cluster_id"`
}

type QueryClusterRequest struct {
	ClusterId string `json:"cluster_id"`
}

type QueryClusterResponse struct {
	Success    int32             `json:"success"`
	Code       string            `json:"code"`
	Message    string            `json:"message"`
	RequestId  string            `json:"requestId"`
	Instances  []*Instance       `json:"instances"`
	Attributes map[string]string `json:"attributes"`
}

// InnerSetMonitorStrategyRequest 内部实现request用这个
type InnerSetMonitorStrategyRequest struct {
	ClusterId string `json:"cluster_id"`
	// Rule MonitorConfig的json形式
	Rule string `json:"rule"`
}

type MutexInfo struct {
	MutexId string `json:"mutexId"`
}

type MonitorItem struct {
	Name      string            `json:"Name"`
	Cond      string            `json:"Cond"`
	Expr      string            `json:"Expr"`
	Filter    string            `json:"Filter"`
	MutexList []*MutexInfo      `json:"MutexList"`
	Level     int32             `json:"Level"`
	Timeout   int32             `json:"Timeout"`
	Tag       map[string]string `json:"tag"`
}

type MonitorConfig struct {
	Rules []*MonitorItem `json:"rules"`
}

type SetMonitorStrategyRequest struct {
	ClusterId string         `json:"cluster_id"`
	Rule      *MonitorConfig `json:"rule"`
}

type GetMonitorStrategyRequest struct {
	ClusterId string `json:"cluster_id"`
}

type GetMonitorStrategyResponse struct {
	Success   int32          `json:"success"`
	Code      string         `json:"code"`
	Message   string         `json:"message"`
	RequestId string         `json:"requestId"`
	Rules     []*MonitorItem `json:"rules"`
}

// InnerSetMonitorSwitchRequest 内部实现request用这个
type InnerSetMonitorSwitchRequest struct {
	ClusterId string `json:"cluster_id"`
	// Enable Switch的json形式
	Enable string `json:"enable"`
}

type Switch struct {
	Enable bool `json:"enable"`
}

type SetMonitorSwitchRequest struct {
	ClusterId string `json:"cluster_id"`
	Enable    bool   `json:"enable"`
}

type GetMonitorSwitchRequest struct {
	ClusterId string `json:"cluster_id"`
}

type GetMonitorSwitchResponse struct {
	Success   int32  `json:"success"`
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
	Enable    bool   `json:"enable"`
}

type TaskFakeSwitch struct {
	Enable bool `json:"enable"`
}

type SetTaskFakeSwitchRequest struct {
	ClusterId string `json:"cluster_id"`
	Enable    bool   `json:"enable"`
}

type GetTaskFakeSwitchRequest struct {
	ClusterId string `json:"cluster_id"`
}

type GetTaskFakeSwitchResponse struct {
	Success   int32  `json:"success"`
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
	Enable    bool   `json:"enable"`
}

type QueryClusterHealthStatusRequest struct {
	ClusterId string `json:"cluster_id"`
}

type QueryClusterHealthStatusResponse struct {
	Success    int32         `json:"success"`
	Code       string        `json:"code"`
	Message    string        `json:"message"`
	RequestId  string        `json:"requestId"`
	HealthInfo []*HealthInfo `json:"health_info"`
}

type IsHealthyInfo struct {
	InstanceAlive bool `json:"instance_alive"`
}

type HealthInfo struct {
	InstanceID     string            `json:"instanceId"`
	IsHealthy      IsHealthyInfo     `json:"isHealthy"`
	LastActiveTime int64             `json:"LastActiveTime"`
	MonitorInfo    map[string]string `json:"monitorInfo"`
}

// XMasterService xmaster部分api实现
type XMasterService interface {
	// QueryClusterHealthStatus http_health_status_cmd
	QueryClusterHealthStatus(ctx context.Context, req *QueryClusterHealthStatusRequest) (rsp *QueryClusterHealthStatusResponse, err error)
	// QueryCluster http_topo_list_cmd
	QueryCluster(ctx context.Context, req *QueryClusterRequest) (rsp *QueryClusterResponse, err error)
	// CreateCluster http_topo_set_cmd
	CreateCluster(ctx context.Context, req *CreateClusterRequest) (rsp *CommonResponse, err error)
	// UpdateCluster http_topo_set_cmd
	UpdateCluster(ctx context.Context, req *CreateClusterRequest) (rsp *CommonResponse, err error)
	// DeleteCluster http_topo_delete_cmd
	DeleteCluster(ctx context.Context, req *DeleteClusterRequest) (rsp *CommonResponse, err error)

	// SetMonitorStrategy http_monitor_rule_set_cmd
	SetMonitorStrategy(ctx context.Context, req *SetMonitorStrategyRequest) (rsp *CommonResponse, err error)
	// GetMonitorStrategy http_monitor_rule_list_cmd
	GetMonitorStrategy(ctx context.Context, req *GetMonitorStrategyRequest) (rsp *GetMonitorStrategyResponse, err error)
	// SetMonitorSwitch http_monitor_switch_cmd
	SetMonitorSwitch(ctx context.Context, req *SetMonitorSwitchRequest) (rsp *CommonResponse, err error)
	// GetMonitorSwitch http_monitor_switch_list_cmd
	GetMonitorSwitch(ctx context.Context, req *GetMonitorSwitchRequest) (rsp *GetMonitorSwitchResponse, err error)

	// SetTaskFakeSwitch http_task_fake_switch_cmd
	SetTaskFakeSwitch(ctx context.Context, req *SetTaskFakeSwitchRequest) (rsp *CommonResponse, err error)
	// GetTaskFakeSwitch http_task_fake_switch_list_cmd
	GetTaskFakeSwitch(ctx context.Context, req *GetTaskFakeSwitchRequest) (rsp *GetTaskFakeSwitchResponse, err error)
}
