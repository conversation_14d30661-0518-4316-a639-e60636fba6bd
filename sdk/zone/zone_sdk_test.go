/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/27
 * File: zone_sdk_test.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package sdk TODO package function desc
package zone

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const (
	TestIamUserId = "a318fe1fe9f5464d92478dab0aa4f5ff"
)

func TestZoneSdk_GetZoneMapList(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newZoneSdk(DefaultServiceName)
	_, err := s.GetZoneMapList(ctx, &ZoneMapListRequest{
		Auth: &common.Authentication{
			IamUserId:     TestIamUserId,
			TransactionId: uuid.New().String(),
			ResourceAccount: &common.ResourceAccount{
				ResourceAk:       "ffffffffffffffffffffffffffffffff",
				EncryptAccountId: "ffffffffffffffffffffffffffffffff",
			},
			Credential: &common.Credential{
				Ak:           "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
				Sk:           "bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
				SessionToken: "cccccccccccccccccccccccccccccccccc",
			},
		}})

	fmt.Println(err) // TODO auth fail
	//if err != nil {
	//	t.Fatalf("err! : %s", err.Error())
	//}
}
