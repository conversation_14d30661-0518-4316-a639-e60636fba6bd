/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/24 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file zone_conf.go
 * <AUTHOR>
 * @date 2023/03/24 15:15:47
 * @brief zone conf

 *
 **/

package zone

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "zone"

// zoneConf definition
type zoneConf struct {
	Product string `toml:"Product,omitempty"`
}

var zoneConfMap = &sync.Map{}

func getConf(serviceName string) *zoneConf {
	if conf, ok := zoneConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*zoneConf); ok {
			return conf
		}
	}

	conf := &zoneConf{}
	conf.mustLoad(serviceName)

	zoneConfMap.Store(serviceName, conf)

	return conf
}

func (conf *zoneConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
