/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-28
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据zone.proto生成的interface文件
 */

// Package zone
package zone

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type ZoneMapDetail struct {
	LogicalZone  string `json:"logicalZone"`
	PhysicalZone string `json:"physicalZone"`
}

type ZoneMapListResponse struct {
	ZoneMapDetailList []*ZoneMapDetail `json:"zoneMapDetailList"`
}

type ZoneMapListRequest struct {
	Auth *common.Authentication `json:"-"`
}

type ZoneMapService interface {
	GetZoneMapList(ctx context.Context, req *ZoneMapListRequest) (rsp *ZoneMapListResponse, err error)
}
