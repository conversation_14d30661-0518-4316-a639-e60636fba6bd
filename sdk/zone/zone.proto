// go install icode.baidu.com/baidu/scs/protofsg@latest
// protofsg -with_context -json_tag=1 zone.proto interface.go
package x1-base.sdk.zone;
option cc_generic_services = true;

import "x1-base/sdk/common/common.proto";

message zoneMapDetail {
    optional string logicalZone = 3;
    optional string physicalZone = 4;
}

message zoneMapListResponse {
    repeated zoneMapDetail zoneMapDetailList = 1;
}

message zoneMapListRequest {
    //@inject_tag  json:"-"
    optional common.Authentication auth = 1;
}

service zoneMapService {
    rpc get_zone_map_list(zoneMapListRequest) returns (zoneMapListResponse);
}
