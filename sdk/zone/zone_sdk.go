/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><EMAIL>)
 * Date: 2021/12/27
 * File: zone_sdk.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package sdk TODO package function desc
package zone

import (
	"context"
	"net/http"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

// zoneSdk - ZoneService implement
type zoneSdk struct {
	conf *zoneConf
	common.OpenApi
}

// NewDefaultZoneSdk - new zoneSdk instance with DefaultServiceName
func NewDefaultZoneSdk() ZoneMapService {
	return newZoneSdk(DefaultServiceName)
}

// NewZoneSdk - new zoneSdk instance
func NewZoneSdk(serviceName string) ZoneMapService {
	return newZoneSdk(serviceName)
}

// newZoneSdk - new zoneSdk instance
func newZoneSdk(serviceName string) *zoneSdk {
	s := &zoneSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}

	return s
}

// doZoneRequest - 通用服务请求方法
//   BCCS_CSMASTER_ERROR_NO ret = KeystoneSDK::get_open_api_request_data(
//        transaction_id,
//        credential,
//        "", "",
//        zone_addr,
//        "GET",
//        _zone_map_list_url,
//        std::map<std::string, std::string>{},
//        request_data
//    );

func (s *zoneSdk) doZoneRequest(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {
	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}
	// 请求 openapi
	return s.DoRequest(ctx, params, rsp)
}

func (s *zoneSdk) GetZoneMapList(ctx context.Context, req *ZoneMapListRequest) (rsp *ZoneMapListResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	rsp = &ZoneMapListResponse{}
	if err = s.doZoneRequest(ctx, "GetZoneMapList", req.Auth, http.MethodGet,
		ZoneMapListUrl, nil, req, rsp); err != nil {
		return nil, err
	}

	return
}
