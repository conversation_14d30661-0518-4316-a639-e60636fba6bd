package bce_utils

import (
	"regexp"
	"strings"

	bceAuth "github.com/baidubce/bce-sdk-go/auth"
	bceHttp "github.com/baidubce/bce-sdk-go/http"
	"github.com/spf13/cast"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

type Signer interface {
	GetSignature(req *CredentialRequest) (authString string, err error)
	CheckSignature(req *AuthorizationRequest) (err error)
	ParseAuthorization(authString string) *Authorization
}

type CredentialRequest struct {
	Ak              string      `json:"ak"`
	Sk              string      `json:"sk"`
	SessionToken    string      `json:"session_token"`
	HttpMethod      string      `json:"http_method"`
	Uri             string      `json:"uri"`
	Queries         interface{} `json:"queries"`
	Headers         interface{} `json:"headers"`
	HeadersMustSign []string    `json:"headers_to_sign"`
	Timestamp       int64       `json:"timestamp"`
	DurationSec     int32       `json:"duration_sec"`
}

type AuthorizationRequest struct {
	Authorization string      `json:"authorization"`
	SessionToken  string      `json:"session_token"`
	HttpMethod    string      `json:"http_method"`
	Uri           string      `json:"uri"`
	Queries       interface{} `json:"queries"`
	Headers       interface{} `json:"headers"`
}

type Authorization struct {
	Version       int
	Ak            string
	Timestamp     string
	ExpireSeconds int64
	SignedHeaders []string
	Signature     string
}

// v1Signer - 封装 BceV1Signer
type v1Signer struct{}

func NewV1Signer() Signer {
	return &v1Signer{}
}

// GetSignature - 生成签名
func (s *v1Signer) GetSignature(req *CredentialRequest) (authString string, err error) {
	signer := &bceAuth.BceV1Signer{}

	// build http request
	httpReq := &bceHttp.Request{}
	httpReq.SetMethod(req.HttpMethod)
	httpReq.SetUri(req.Uri)

	if queries, err := sdk_utils.CastRequestQueriesToMap(req.Queries); err != nil {
		return "", err
	} else {
		for k, v := range queries {
			httpReq.SetParam(k, cast.ToString(v))
		}
	}

	if headers, err := sdk_utils.CastRequestHeadersToMap(req.Headers); err != nil {
		return "", err
	} else {
		for k, v := range headers {
			httpReq.SetHeader(k, cast.ToString(v))
		}
	}

	// bce credentials
	var cred *bceAuth.BceCredentials
	if req.SessionToken != "" {
		cred, err = bceAuth.NewSessionBceCredentials(req.Ak, req.Sk, req.SessionToken)
	} else {
		cred, err = bceAuth.NewBceCredentials(req.Ak, req.Sk)
	}
	if err != nil {
		return "", err
	}

	// sign opt
	headersMustSign := make(map[string]struct{})
	if req.HeadersMustSign != nil {
		for _, header := range req.HeadersMustSign {
			header = strings.ToLower(header)
			headersMustSign[header] = struct{}{}
		}
	}

	signOpt := &bceAuth.SignOptions{
		HeadersToSign: headersMustSign,
		Timestamp:     req.Timestamp,
		ExpireSeconds: int(req.DurationSec),
	}

	// calc sign
	signer.Sign(httpReq, cred, signOpt)

	return httpReq.Header(bceHttp.AUTHORIZATION), nil
}

// CheckSignature - 签名校验 TODO 暂未实现
func (s *v1Signer) CheckSignature(req *AuthorizationRequest) (err error) {
	panic("implement me")
}

var authStringRegexp = regexp.MustCompile(`^(?i)bce-auth-v(\d+)/(\S+?)/(\S+?)/(\d+)/(\S+?)/(\S+)$`)

func (s *v1Signer) ParseAuthorization(authString string) *Authorization {
	if m := authStringRegexp.FindStringSubmatch(authString); m != nil {
		auth := &Authorization{
			Version:       cast.ToInt(m[1]),
			Ak:            m[2],
			Timestamp:     m[3],
			ExpireSeconds: cast.ToInt64(m[4]),
			SignedHeaders: strings.Split(m[5], ";"),
			Signature:     m[6],
		}
		return auth
	}

	return nil
}
