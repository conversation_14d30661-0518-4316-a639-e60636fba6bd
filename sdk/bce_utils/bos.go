package bce_utils

import (
	"github.com/baidubce/bce-sdk-go/auth" //导入认证模块
	bceBos "github.com/baidubce/bce-sdk-go/services/bos"
)

type BosClient struct {
	*bceBos.Client
}

// NewBosClient - 创建bos client
func NewBosClient(ak, sk, endpoint string) *BosClient {
	client, err := bceBos.NewClient(ak, sk, endpoint)
	if err != nil {
		panic(err)
	}
	return &BosClient{client}
}

// NewBosClient - 创建bos client
func NewBosClientWithSts(ak, sk, sessionToken, endpoint string) (*BosClient, error) {
	client, err := bceBos.NewClient(ak, sk, endpoint)
	if err != nil {
		return nil, err
	}
	stsCredential, err := auth.NewSessionBceCredentials(
		ak,
		sk,
		sessionToken)
	if err != nil {
		return nil, err
	}
	client.Config.Credentials = stsCredential
	return &BosClient{client}, nil
}
