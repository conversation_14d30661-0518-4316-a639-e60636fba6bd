package bce_utils

import (
	"fmt"
	"testing"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func TestV1Signer_GetSignature(t *testing.T) {
	s := NewV1Signer()

	req := &CredentialRequest{
		Ak:         "********************************",
		Sk:         "22222222222222222222222222222222",
		HttpMethod: "GET",
		Uri:        "/v1/list/clusters",
		Queries:    nil,
		Headers: map[string]interface{}{
			"Host": "scs.baidu.com",
		},
		HeadersMustSign: []string{"Host"},
		Timestamp:       time.Now().Unix(),
		DurationSec:     1000,
	}

	authString, err := s.GetSignature(req)
	if err != nil {
		t.Fatalf("GetSignatureByCredential fail, req: %v, err: %v", base_utils.Format(req), err.Error())
	}

	fmt.Printf("get Authorization: %s\n", authString)
}
