package guardian

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type guardianSDK struct {
	common.OpenApi
}

func (g guardianSDK) TopoCheck(ctx context.Context, req *TopoCheckRequest) (*TopoCheckResponse, error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp := &TopoCheckResponse{}
	queries := map[string]any{
		"appId": req.AppID,
	}
	if err := g.doRequest(ctx, TopoCheckUrl, req.AppID, "POST", TopoCheckUrl, queries, nil, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func (g guardianSDK) NewTopoCheck(ctx context.Context, req *TopoCheckRequest) (*NewTopoCheckResponse, error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp := &NewTopoCheckResponse{}
	queries := map[string]any{
		"appId": req.AppID,
	}
	if err := g.doRequest(ctx, TopoCheckUrl, req.AppID, "POST", TopoCheckUrl, queries, nil, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func NewDefaultGuardianSDK() GuardianService {
	return newGuardianSDK("guardian")
}

func NewGuardianSDK(serviceName string) GuardianService {
	return newGuardianSDK(serviceName)
}

func newGuardianSDK(serviceName string) *guardianSDK {
	s := &guardianSDK{
		OpenApi: common.NewOpenApi(serviceName),
	}
	return s
}

func (s *guardianSDK) doRequest(ctx context.Context, actionName string, token string,
	httpMethod, uri string, queries map[string]any, req, rsp any) (err error) {
	params := &common.OpenApiParams{
		ActionName: actionName,
		Token:      token,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
	}

	// 请求 openapi
	err = s.DoRequest(ctx, params, rsp)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return err
	}
	return nil
}
