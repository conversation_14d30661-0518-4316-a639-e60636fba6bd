package guardian

import "context"

const (
	TopoCheckUrl = "/v1/x1guardian/topocheck"
)

type TopoCheckRequest struct {
	AppID string
}

type TopoCheckResponse struct {
	AppID               string
	Status              int32
	Version             string
	Type                string
	AppShortID          int64
	UserID              string
	GetLocalTopoFail    bool
	GetAllRedisInfoFail bool
	SingleAppMetaData   any
	Topo                any `json:"topo,omitempty"`
	Cnf                 any `json:"cnf,omitempty"`
}

type NewTopoCheckResponse struct {
	AppID               string
	Status              int32
	Version             string
	Type                string
	AppShortID          int64
	UserID              string
	GetLocalTopoFail    bool
	GetAllRedisInfoFail bool
	SingleAppMetaData   *SingleAppMetaData
	Topo                *CheckTopoRet   `json:"topo,omitempty"`
	Cnf                 *MemCnfCheckRet `json:"cnf,omitempty"`
}

type CheckTopoRet struct {
	DbTopo   map[string][]string `json:"dbTopo,omitempty"`
	DbProxy  map[string][]string `json:"dbProxy,omitempty"`
	RedisSer map[string][]string `json:"redisSer,omitempty"`
	ProxySer map[string][]string `json:"proxySer,omitempty"`
	BlbRs    []string            `json:"blbRs,omitempty"`
	Msg      string              `json:"msg,omitempty"`
}

type MemCnfCheckRet struct {
	InnerNotMatch       map[string][]*singleRecordItem `json:"innerNotMatch,omitempty"`
	NotMatch            map[string][]*singleRecordItem `json:"notMatch,omitempty"`
	NotHave             map[string][]*singleRecordItem `json:"notHave,omitempty"`
	DisableCmdMiss      map[string][]*singleRecordItem `json:"disableCmdMiss,omitempty"`
	DisableCmdMore      map[string][]*singleRecordItem `json:"disableCmdMore,omitempty"`
	DisableCmdInnerMiss map[string][]*singleRecordItem `json:"disableCmdInnerMiss,omitempty"`
	DisableCmdInnerMore map[string][]*singleRecordItem `json:"disableCmdInnerMore,omitempty"`
}

type singleRecordItem struct {
	ConfName       string `json:"confName,omitempty"`
	AppID          string `json:"appID,omitempty"`
	NodeID         string `json:"nodeID,omitempty"`
	ComparedValue  string `json:"comparedValue,omitempty"`
	ComparedNodeID string `json:"comparedNodeID,omitempty"`
	ConfValue      string `json:"confValue,omitempty"`
}

type SingleAppMetaData struct {
	LocalTopo        any
	RedisInfoMap     map[string]map[string]map[string]string
	RedisConfMap     map[string][]string
	ProxyConfMap     map[string][]string
	MemConfMap       map[string]map[string]string
	UserConf         any
	UserConfMap      map[string]string
	DisableCmdMap    map[string][]string
	DbDisableCmdList []string
}

type GuardianService interface {
	TopoCheck(ctx context.Context, req *TopoCheckRequest) (*TopoCheckResponse, error)
	NewTopoCheck(ctx context.Context, req *TopoCheckRequest) (*NewTopoCheckResponse, error)
}
