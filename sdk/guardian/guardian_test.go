package guardian

import (
	"context"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func Test_guardianSDK_TopoCheck(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	g := NewDefaultGuardianSDK()
	_, err := g.<PERSON>o<PERSON>(ctx, nil)
	if err == nil {
		t.<PERSON>rrorf("TopoCheck() error = %v, wantErr %v", err, true)
		return
	}
	_, err = g.<PERSON>o<PERSON>ck(ctx, &TopoCheckRequest{
		AppID: "test",
	})
	base_utils.Format(err.Error())
}

func Test_guardianSDK_NewTopoCheck(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	g := NewDefaultGuardianSDK()
	_, err := g.<PERSON><PERSON><PERSON><PERSON>(ctx, nil)
	if err == nil {
		t.<PERSON><PERSON>("NewTopoCheck() error = %v, wantErr %v", err, true)
		return
	}
	_, err = g.NewTopoCheck(ctx, &TopoCheckRequest{
		AppID: "test",
	})
	base_utils.Format(err.Error())
}
