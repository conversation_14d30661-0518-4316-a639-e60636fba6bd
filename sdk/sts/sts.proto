// protofsg -with_context -json_tag=1 sts.proto interface.go
option cc_generic_services = true;
option go_package = "./;sts";
package x1-base.sdk.sts;

import "x1-base/sdk/common/common.proto";

// --- SDK requests & responses data type ---

message Token {
    optional string id = 1;
    optional string expires_at = 2;
    optional string issued_at = 3;
}

message GetAssumeRoleRequest {
    optional string iam_user_id = 1;
    optional string transaction_id = 2;
}

message GetAssumeRoleResponse {
    optional string accessKeyId = 1;
    optional string secretAccessKey = 2;
    optional string sessionToken = 3;
    optional string createTime = 4;
    optional string expiration = 5;
    optional string userId = 6;
    optional string roleId = 7;
    optional Token token = 8;
    optional string code = 9;
    optional string message = 10;
    optional string requestId = 11;
}

message EncryptResourceAccountIdResponse {
    optional string encryptAccountId = 1;
    optional string resourceAk = 2;
}

message GetOpenApiAuthRequest {
    optional string transaction_id = 1;
    optional string iam_user_id = 2;
    optional bool need_resource_account = 3
}

message GetOpenApiAuthResponse {
    optional common.Authentication auth = 1;
}

// --- SDK interface ---
service StsService {
    rpc GetAssumeRole(GetAssumeRoleRequest) returns (GetAssumeRoleResponse);
    rpc GetEncryptResourceAccountId(NullMessage) returns (EncryptResourceAccountIdResponse);
    rpc GetOpenApiAuth(GetOpenApiAuthRequest) returns (GetOpenApiAuthResponse);
}
