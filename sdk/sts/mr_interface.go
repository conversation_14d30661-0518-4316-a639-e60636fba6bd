/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-03-04
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据sts.proto生成的interface文件
 */

// Package sts
package sts

import (
	"context"
)

type MrGetAssumeRoleRequest struct {
	Req    *GetAssumeRoleRequest
	Region string
}

type MrGetOpenApiAuthRequest struct {
	Req    *GetOpenApiAuthRequest
	Region string
}

type MrStsService interface {
	PickStsSdk(ctx context.Context, region string) StsService
	GetAssumeRole(ctx context.Context, req *MrGetAssumeRoleRequest) (rsp *GetAssumeRoleResponse, err error)
	GetEncryptResourceAccountId(ctx context.Context, region string) (rsp *EncryptResourceAccountIdResponse, err error)
	GetOpenApiAuth(ctx context.Context, req *MrGetOpenApiAuthRequest) (rsp *GetOpenApiAuthResponse, err error)
}
