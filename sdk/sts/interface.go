/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-03-04
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据sts.proto生成的interface文件
 */

// Package sts
package sts

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type Token struct {
	Id        string `json:"id"`
	ExpiresAt string `json:"expires_at"`
	IssuedAt  string `json:"issued_at"`
}

type GetAssumeRoleRequest struct {
	IamUserId     string `json:"iam_user_id"`
	TransactionId string `json:"transaction_id"`
}

type GetAssumeRoleResponse struct {
	AccessKeyId     string `json:"accessKeyId"`
	SecretAccessKey string `json:"secretAccessKey"`
	SessionToken    string `json:"sessionToken"`
	CreateTime      string `json:"createTime"`
	Expiration      string `json:"expiration"`
	UserId          string `json:"userId"`
	RoleId          string `json:"roleId"`
	Token           *Token `json:"token"`
	Code            string `json:"code"`
	Message         string `json:"message"`
	RequestId       string `json:"requestId"`
}

type EncryptResourceAccountIdResponse struct {
	EncryptAccountId string `json:"encryptAccountId"`
	ResourceAk       string `json:"resourceAk"`
}

type GetOpenApiAuthRequest struct {
	TransactionId       string `json:"transaction_id"`
	IamUserId           string `json:"iam_user_id"`
	NeedResourceAccount bool   `json:"need_resource_account"`
}

type GetOpenApiAuthResponse struct {
	Auth *common.Authentication `json:"auth"`
}

type StsService interface {
	GetAssumeRole(ctx context.Context, req *GetAssumeRoleRequest) (rsp *GetAssumeRoleResponse, err error)
	GetEncryptResourceAccountId(ctx context.Context) (rsp *EncryptResourceAccountIdResponse, err error)
	GetOpenApiAuth(ctx context.Context, req *GetOpenApiAuthRequest) (rsp *GetOpenApiAuthResponse, err error)
}
