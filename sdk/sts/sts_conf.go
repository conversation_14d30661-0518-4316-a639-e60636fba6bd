package sts

import (
	"fmt"
	"sync"
	"time"

	"icode.baidu.com/baidu/gdp/excache"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "STS"

type stsConf struct {
	// assume role
	ServiceId     string `toml:"ServiceId"`
	ServiceAk     string `toml:"ServiceAk"`
	ServiceSk     string `toml:"ServiceSk"`
	DefaultRole   string `toml:"DefaultRole"`
	DurationSec   int    `toml:"DurationSec"`   //请求的auth过期时间
	AuthCachedSec int    `json:"AuthCachedSec"` //缓存的auth过期时间

	// encrypt resource account id
	ResourceID string `toml:"ResourceID"`
	ResourceAk string `toml:"ResourceAk"`
	ResourceSk string `toml:"ResourceSk"`

	// cache
	AssumeRoleCacheTTL time.Duration     `toml:"-"`
	AssumeRoleCache    *excache.LRUCache `toml:"-"`
	Product            string            `toml:"Product,omitempty"`
}

type assumeRoleCachedItem struct {
	CachedRsp *GetAssumeRoleResponse
	CreateAT  time.Time
	ExpireAT  time.Time
}

var stsConfMap = &sync.Map{}

func getConf(serviceName string) *stsConf {
	if conf, ok := stsConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*stsConf); ok {
			return conf
		}
	}

	conf := &stsConf{}
	conf.mustLoad(serviceName)

	if conf.DurationSec <= 0 {
		conf.DurationSec = cacheDefaultTTLSec
	}

	if conf.AuthCachedSec > 0 {
		cacheTTLSec := conf.AuthCachedSec
		if cacheTTLSec+cacheGuardDurationSec > conf.DurationSec {
			cacheTTLSec = conf.DurationSec - cacheGuardDurationSec
		}
		if cacheTTLSec > 0 {
			d := time.Duration(cacheTTLSec) * time.Second
			conf.AssumeRoleCacheTTL = d
			conf.AssumeRoleCache = excache.NewLRUCache(cacheSize, 0, d)
		}
	}

	stsConfMap.Store(serviceName, conf)

	return conf
}

func (conf *stsConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
