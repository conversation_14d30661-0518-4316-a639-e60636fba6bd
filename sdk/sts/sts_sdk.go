/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-06
 * File: interface_impl.go
 */

/*
 * DESCRIPTION
 *   根据sts.proto生成的implement文件
 */

// Package sts
package sts

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bce_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
)

// stsSdk - StsService implement
type stsSdk struct {
	conf      *stsConf
	signer    bce_utils.Signer
	ralCaller sdk_utils.RalCaller
}

// NewDefaultStsSdk - 用默认serviceName创建StsSdk
func NewDefaultStsSdk() StsService {
	return newStsSdk(DefaultServiceName)
}

// NewStsSdk - new stsSdk instance
func NewStsSdk(serviceName string) StsService {
	return newStsSdk(serviceName)
}

// newStsSdk - 内部new方法
func newStsSdk(serviceName string) *stsSdk {
	if serviceName == "" {
		panic("invalid service name")
	}

	s := &stsSdk{
		conf:   getConf(serviceName),
		signer: bce_utils.NewV1Signer(),
		ralCaller: sdk_utils.NewRalCaller(
			serviceName,
			sdk_utils.ROptGenCurlLog(logit.TraceLevel),
			sdk_utils.ROptPrepareChecker(
				func(ctx context.Context, httpRsp *sdk_utils.HttpResponse, invoker sdk_utils.PrepareCheckFunc) error {
					logger.SdkLogger.Debug(ctx, "sts http rsp: %v", httpRsp)
					return invoker(ctx, httpRsp)
				}),
		),
	}

	return s
}

// SetRalCaller - 用于ut
func (s *stsSdk) SetRalCaller(caller sdk_utils.RalCaller) sdk_utils.RalCaller {
	last := s.ralCaller
	s.ralCaller = caller
	return last
}

// GetAssumeRole - GetAssumeRole implement
func (s *stsSdk) GetAssumeRole(ctx context.Context, req *GetAssumeRoleRequest) (rsp *GetAssumeRoleResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	// 尝试从cache中获取
	if rsp = s.getAssumeRoleFromCache(ctx, req); rsp != nil {
		return
	}

	// 获取当前时间
	nowUtc := time.Now().UTC()

	uri := credentialUri

	// 设置 query queries
	queries := map[string]interface{}{
		"assumeRole":      "",
		"accountId":       req.IamUserId,
		"roleName":        s.conf.DefaultRole,
		"durationSeconds": s.conf.DurationSec,
		"withToken":       "",
	}

	// 设置headers
	date := base_utils.FormatWithISO8601(nowUtc)
	productResource := "scs"
	if s.conf.Product != "" {
		productResource = s.conf.Product
	}
	headers := map[string]interface{}{
		"User-Agent":       productResource,
		"X-Auth-Token":     req.IamUserId,
		"x-bce-request-id": req.TransactionId,
		"x-bce-date":       date,
	}

	ralCaller := s.ralCaller.WithOption(
		sdk_utils.ROptHttpReqBuilder(
			func(_ context.Context, opt sdk_utils.Option, addr sdk_utils.Addr, req *sdk_utils.HttpRequest) error {
				// 获取host
				host := req.GetHost()
				host, _ = sdk_utils.SplitHostPort(host)
				// 更新host header
				req.SetHeader("Host", host)
				headers["Host"] = host

				// 获取authorization string
				credReq := &bce_utils.CredentialRequest{
					Ak:              s.conf.ServiceAk,
					Sk:              s.conf.ServiceSk,
					HttpMethod:      http.MethodPost,
					Uri:             uri,
					Queries:         queries,
					Headers:         headers,
					HeadersMustSign: []string{"Host"},
					Timestamp:       nowUtc.Unix(),
				}
				authString, err := s.signer.GetSignature(credReq)
				if err != nil {
					logger.SdkLogger.Warning(ctx, "get authorization fail, err: %s", err.Error())
					return cerrs.ErrAuthFail.Wrap(err)
				}

				// 添加authorization header
				req.SetHeader("Authorization", authString)
				return nil
			}),
	)

	// 请求sts openapi
	rsp = &GetAssumeRoleResponse{}
	if err = ralCaller.HttpRequest(ctx, http.MethodPost, uri, queries, "", headers, rsp); err != nil {
		logger.SdkLogger.Warning(ctx, "ral sts fail, req: %+v, err: %s", *req, err.Error())
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "credential request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.RequestId)
		return nil, cerrs.ErrSTSGetCredentialFail.Errorf("credential fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}

	// 保存rsp到cache
	s.setAssumeRoleToCache(ctx, req, rsp)

	return
}

func (s *stsSdk) getAssumeRoleFromCache(ctx context.Context, req *GetAssumeRoleRequest) (rsp *GetAssumeRoleResponse) {
	if s.conf.AssumeRoleCache == nil {
		return nil
	}

	v, has := s.conf.AssumeRoleCache.Get(req.IamUserId)
	if !has {
		return nil
	}

	cachedItem := v.(*assumeRoleCachedItem)
	logger.SdkLogger.Trace(ctx, "use cached assume role for user %s, created at %s, expire at %s",
		req.IamUserId, base_utils.FormatWithCST(cachedItem.CreateAT), base_utils.FormatWithCST(cachedItem.ExpireAT))

	return cachedItem.CachedRsp
}

func (s *stsSdk) setAssumeRoleToCache(ctx context.Context, req *GetAssumeRoleRequest, rsp *GetAssumeRoleResponse) {
	if s.conf.AssumeRoleCache == nil {
		return
	}

	nowTime := time.Now()
	cachedItem := &assumeRoleCachedItem{
		CachedRsp: rsp,
		CreateAT:  nowTime,
		ExpireAT:  nowTime.Add(s.conf.AssumeRoleCacheTTL),
	}
	s.conf.AssumeRoleCache.Set(req.IamUserId, cachedItem)
	logger.SdkLogger.Trace(ctx, "the assume role for user %s was cached, and it will expire at %s",
		req.IamUserId, base_utils.FormatWithCST(cachedItem.ExpireAT))

	return
}

// GetEncryptResourceAccountId - GetEncryptResourceAccountId impl
func (s *stsSdk) GetEncryptResourceAccountId(ctx context.Context) (rsp *EncryptResourceAccountIdResponse, err error) {
	now := time.Now().Unix()
	rawContent := fmt.Sprintf("%s;%d", s.conf.ResourceID, now)

	// AES 128 ECB encrypt
	encryptKey := s.conf.ResourceSk[0:16]
	encryptAccountId, err := crypto_utils.AesECBEncryptString(rawContent, encryptKey,
		crypto_utils.PKCS7PaddingType, crypto_utils.HexCodecType)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "aes ecb encrypt fail, content: %s, key: %s", rawContent, encryptKey)
		return nil, cerrs.ErrSTSEncryptAccountFail.Wrap(err)
	}

	rsp = &EncryptResourceAccountIdResponse{
		EncryptAccountId: encryptAccountId,
		ResourceAk:       s.conf.ResourceAk,
	}

	return
}

// GetOpenApiAuth - 获取openapi通用auth信息
func (s *stsSdk) GetOpenApiAuth(ctx context.Context, req *GetOpenApiAuthRequest) (rsp *GetOpenApiAuthResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	// GetAssumeRole
	roleReq := &GetAssumeRoleRequest{
		IamUserId:     req.IamUserId,
		TransactionId: req.TransactionId,
	}
	roleRsp, err := s.GetAssumeRole(ctx, roleReq)
	if err != nil {
		return nil, err
	}

	// build auth
	auth := &common.Authentication{
		IamUserId:     req.IamUserId,
		TransactionId: req.TransactionId,
		Credential: &common.Credential{
			Ak:           roleRsp.AccessKeyId,
			Sk:           roleRsp.SecretAccessKey,
			SessionToken: roleRsp.SessionToken,
		},
	}

	// GetEncryptResourceAccountId
	if req.NeedResourceAccount {
		accountRsp, err := s.GetEncryptResourceAccountId(ctx)
		if err != nil {
			return nil, err
		}
		auth.ResourceAccount = &common.ResourceAccount{
			ResourceAk:       accountRsp.ResourceAk,
			EncryptAccountId: accountRsp.EncryptAccountId,
		}
	}

	rsp = &GetOpenApiAuthResponse{Auth: auth}

	return
}
