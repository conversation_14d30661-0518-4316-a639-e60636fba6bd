/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/04/28
 * File: mr_sts_sdk.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package sts TODO package function desc
package sts

import (
	"context"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	commonUtil "icode.baidu.com/baidu/scs/x1-base/utils/common"
)

var ErrCantPickStsSdk = errors.Errorf("cant pick a valid Sts sdk")

var MrStsSdk *mrStsSdk = &mrStsSdk{SdkMap: make(map[string]StsService, 0)}

type MrStsConf struct {
	common.MultiRegionCnf
}

type mrStsSdk struct {
	SdkMap map[string]StsService
}

func MustInitMrStsSdk(ctx context.Context, conf MrStsConf) {
	for _, region := range conf.RegionList {
		MrStsSdk.SdkMap[region] = NewStsSdk(conf.ServiceName + "_" + region)
	}
}

func (m *mrStsSdk) PickStsSdk(ctx context.Context, region string) StsService {
	innerRegion := commonUtil.GetInnerRegion(ctx, region)
	if StsSdk, ok := m.SdkMap[innerRegion]; ok {
		return StsSdk
	}
	return nil
}

func (m *mrStsSdk) GetAssumeRole(ctx context.Context, req *MrGetAssumeRoleRequest) (rsp *GetAssumeRoleResponse, err error) {
	stsSdk := m.PickStsSdk(ctx, req.Region)
	if stsSdk == nil {
		return nil, ErrCantPickStsSdk
	}
	return stsSdk.GetAssumeRole(ctx, req.Req)
}

func (m *mrStsSdk) GetEncryptResourceAccountId(ctx context.Context, region string) (rsp *EncryptResourceAccountIdResponse, err error) {
	stsSdk := m.PickStsSdk(ctx, region)
	if stsSdk == nil {
		return nil, ErrCantPickStsSdk
	}
	return stsSdk.GetEncryptResourceAccountId(ctx)
}

func (m *mrStsSdk) GetOpenApiAuth(ctx context.Context, req *MrGetOpenApiAuthRequest) (rsp *GetOpenApiAuthResponse, err error) {
	stsSdk := m.PickStsSdk(ctx, req.Region)
	if stsSdk == nil {
		return nil, ErrCantPickStsSdk
	}
	return stsSdk.GetOpenApiAuth(ctx, req.Req)
}
