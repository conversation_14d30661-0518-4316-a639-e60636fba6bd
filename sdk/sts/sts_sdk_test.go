/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-07
 * File: sts_sdk_test.go
 */

/*
 * DESCRIPTION -
 */

// Package sts
package sts

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const (
	TestIamUserId = "a318fe1fe9f5464d92478dab0aa4f5ff"
)

func TestStsSdk_GetAssumeRole(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newStsSdk(DefaultServiceName)

	req := &GetAssumeRoleRequest{
		IamUserId:     TestIamUserId,
		TransactionId: uuid.New().String(),
	}

	rsp, err := s.GetAssumeRole(ctx, req)
	if err != nil {
		t.Fatalf("get assume role fail")
	}

	fmt.Printf("get assume role, req: %s, rsp: %s\n",
		base_utils.Format(req), base_utils.Format(rsp))
}

func TestStsSdk_GetEncryptResourceAccountId(t *testing.T) {
	s := newStsSdk(DefaultServiceName)

	rsp, err := s.GetEncryptResourceAccountId(context.Background())
	if err != nil {
		t.Fatalf("get encrypt recource account fail")
	}

	fmt.Printf("get encrypt recource account: %s\n", base_utils.Format(rsp))
}
