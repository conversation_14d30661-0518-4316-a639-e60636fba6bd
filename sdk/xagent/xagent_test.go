package xagent

import (
	"context"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type testReqFunc func(t *testing.T, ctx context.Context, method, path string,
	queries, posts, headers, result interface{}) error

type dummyCaller struct {
	sdk_utils.RalCaller
	t           *testing.T
	testReqFunc testReqFunc
}

func (c *dummyCaller) WithOption(opts ...sdk_utils.ROption) sdk_utils.RalCaller {
	return c
}

func (c *dummyCaller) HttpRequest(ctx context.Context, method, path string, queries, posts, headers,
	result interface{}) error {
	return c.testReqFunc(c.t, ctx, method, path, queries, posts, headers, result)
}

func newDummyCaller(t *testing.T, httpReqFunc testReqFunc) *dummyCaller {
	return &dummyCaller{
		t:           t,
		testReqFunc: httpReqFunc,
	}
}

func TestXAgentSdk_XagentCmd(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newXAgentSdk(DefaultServiceName)

	req := &ExecCmdRequest{
		XagentHost: "localhost",
		XagentPort: 8080,
		CmdName:    "test",
		Data:       "test",
	}

	_, err := s.ExecCmd(ctx, req)
	if err != nil {
		fmt.Println(err)
	}
}

func TestXAgentSdk_CreateTask(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newXAgentSdk(DefaultServiceName)
	s.SetRalCaller(newDummyCaller(t, func(t *testing.T, ctx context.Context, method, path string,
		queries, posts, headers, result interface{}) error {
		postMap, ok := posts.(map[string]interface{})
		if !ok {
			t.Fatalf("posts format error")
		}
		if taskName, _ := postMap["task_name"]; taskName != "test" {
			t.Fatalf("taskName[%v] error in posts", taskName)
		}
		if param1, _ := postMap["param1"]; param1 != "1" {
			t.Fatalf("param1[%v] error in posts", param1)
		}

		fmt.Printf("posts: %s\n", base_utils.Format(posts))
		return nil
	}))

	type dataType struct {
		Param1 string `json:"param1"`
		Param2 int    `json:"param2"`
	}

	req := &CreateTaskRequest{
		XagentHost: "localhost",
		XagentPort: 8080,
		Data: &dataType{
			Param1: "1",
			Param2: 2,
		},
		TaskName: "test",
	}

	_, err := s.CreateTask(ctx, req)
	if err != nil {
		fmt.Println(err)
	}
}
