/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-01-21
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据xagent.proto生成的interface文件
 */

// Package xagent
package xagent

import (
	"context"
)

type ExecCmdRequest struct {
	XagentHost string      `json:"xagent_host"`
	XagentPort int32       `json:"xagent_port"`
	CmdName    string      `json:"cmd_name"`
	Data       interface{} `json:"data"`
}

type ExecCmdResponse struct {
	Status     int32       `json:"status"`
	Statusinfo string      `json:"statusinfo"`
	Data       interface{} `json:"data"`
	RequestId  string      `json:"request_id"`
}

type QueryCmdRequest struct {
	XagentHost string `json:"xagent_host"`
	XagentPort int32  `json:"xagent_port"`
	CmdName    string `json:"cmd_name"`
}

type QueryCmdResponse struct {
	Data string `json:"data"`
}

type CreateTaskRequest struct {
	XagentHost  string      `json:"-"`
	XagentPort  int32       `json:"-"`
	TaskType    string      `json:"task_type"`
	Data        interface{} `json:"-"`
	TaskName    string      `json:"task_name"`
	CallbackUrl string      `json:"callback_url"`
	Timeout     int32       `json:"timeout"`
}

type CreateTaskResponse struct {
	ErrorInfo string `json:"error_info"`
	ErrorCode int32  `json:"error_code"`
	TaskId    int64  `json:"task_id"`
}

type QueryTaskRequest struct {
	XagentHost string `json:"xagent_host"`
	XagentPort int32  `json:"xagent_port"`
	TaskId     int64  `json:"task_id"`
}

type QueryTaskResponse struct {
	ErrorInfo  string `json:"error_info"`
	ErrorCode  int32  `json:"error_code"`
	TaskId     int64  `json:"task_id"`
	TaskStatus string `json:"task_status"`
	TaskResult string `json:"task_result"`
}

type XAgentService interface {
	ExecCmd(ctx context.Context, req *ExecCmdRequest) (rsp *ExecCmdResponse, err error)
	QueryCmd(ctx context.Context, req *QueryCmdRequest) (rsp *QueryCmdResponse, err error)
	CreateTask(ctx context.Context, req *CreateTaskRequest) (rsp *CreateTaskResponse, err error)
	QueryTask(ctx context.Context, req *QueryTaskRequest) (rsp *QueryTaskResponse, err error)
}
