/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-16
 * File: interface_impl.go
 */

/*
 * DESCRIPTION
 *		本SDK实现参考了下列wiki (其中关于参数和返回值中用户data的说明令人混淆，具体见代码实现)
 * 		同步cmd：http://wiki.baidu.com/pages/viewpage.action?pageId=894098915
 * 		异步task：http://wiki.baidu.com/pages/viewpage.action?pageId=894098923
 */

// Package xagent
package xagent

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/vep"
)

// xAgentSdk - XrmService implement
type xAgentSdk struct {
	ralCaller sdk_utils.RalCaller
}

// NewDefaultXAgentSdk - 使用默认的serviceName创建sdk
func NewDefaultXAgentSdk() XAgentService {
	return newXAgentSdk(DefaultServiceName)
}

// NewXAgentSdk - 使用指定的serviceName创建sdk
func NewXAgentSdk(serviceName string) XAgentService {
	return newXAgentSdk(serviceName)
}

// newXAgentSdk - new xAgentSdk instance
func newXAgentSdk(serviceName string) *xAgentSdk {
	if serviceName == "" {
		panic("invalid service name")
	}
	s := &xAgentSdk{
		ralCaller: sdk_utils.NewRalCaller(
			serviceName,
			sdk_utils.ROptGenCurlLog(logit.TraceLevel),
			sdk_utils.ROptPrepareChecker(
				func(ctx context.Context, httpRsp *sdk_utils.HttpResponse, invoker sdk_utils.PrepareCheckFunc) error {
					logger.SdkLogger.Debug(ctx, "xagent http rsp: %v", httpRsp)
					return invoker(ctx, httpRsp)
				}),
		),
	}
	return s
}

// SetRalCaller - 替换ralCaller(用于ut)
func (s *xAgentSdk) SetRalCaller(caller sdk_utils.RalCaller) sdk_utils.RalCaller {
	last := s.ralCaller
	s.ralCaller = caller
	return last
}

func (s *xAgentSdk) doRequest(ctx context.Context, actionName string, host string, port int32,
	httpMethod, uri string, req, rsp interface{}) (err error) {
	if strings.HasPrefix(host, "vep:") {
		vepHost, vepPort, err := vep.GetVpcEndpointModelServices().ParseVpcEndpoint(host, int(port))
		if err != nil {
			logger.SdkLogger.Error(ctx, "parse vep host failed, err: %s", err.Error())
			return err
		}
		host = vepHost
		port = int32(vepPort)
	}
	ralCaller := s.ralCaller.WithOption(sdk_utils.ROptSingleAddr(sdk_utils.NewAddr(host, port)))
	if err = ralCaller.HttpRequest(ctx, httpMethod, uri, nil, req, nil, rsp); err != nil {
		logger.SdkLogger.Warning(ctx,
			"[%s] ral request fail, host: %s port: %d, httpMethod: %s, uri: %s, req: %v, err: %s",
			actionName, host, port, httpMethod, uri, base_utils.Format(req), err.Error())
		return err
	}

	return
}

// parseStringResult parse string result to dest result
func parseStringResult(rawResult string, result interface{}) error {
	if p, ok := result.(*string); ok {
		*p = rawResult
		return nil
	}
	return base_utils.Unmarshal([]byte(rawResult), result)
}

// ParseResult parse interface{} result to dest result
func ParseResult(rawResult, result interface{}) error {
	if s, ok := rawResult.(string); ok {
		return parseStringResult(s, result)
	}
	return sdk_utils.CastWithTag(rawResult, result, "json")
}

// ParseResult ExecCmdResponse parse data string to dest result
func (rsp *ExecCmdResponse) ParseResult(result interface{}) error {
	return ParseResult(rsp.Data, result)
}

// ParseResult QueryCmdResponse parse data string to dest result
func (rsp *QueryCmdResponse) ParseResult(result interface{}) error {
	return parseStringResult(rsp.Data, result)
}

// ParseResult QueryTaskResponse parse task result string to dest result
func (rsp *QueryTaskResponse) ParseResult(result interface{}) error {
	return parseStringResult(rsp.TaskResult, result)
}

// ExecCmd - 执行命令
func (s *xAgentSdk) ExecCmd(ctx context.Context, req *ExecCmdRequest) (rsp *ExecCmdResponse, err error) {
	uri := fmt.Sprintf("/%s", req.CmdName)

	var rawRsp string
	err = s.doRequest(ctx, "ExecCmd", req.XagentHost, req.XagentPort, http.MethodPost,
		uri, req.Data, &rawRsp)
	if err != nil {
		return nil, err
	}

	rsp = &ExecCmdResponse{}
	err = base_utils.UnmarshalUseNumber([]byte(rawRsp), rsp)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "invalid cmd exec response, cmd: %s, rawResponse: %s",
			req.CmdName, rawRsp)
		return nil, cerrs.ErrInvalidResponse.Wrap(err)
	}

	if rsp.Status != 0 {
		logger.SdkLogger.Warning(ctx, "xagent cmd exec fail, requestId: %s, statusInfo: %s",
			rsp.RequestId, rsp.Statusinfo)
		return nil, cerrs.ErrXAgentCmdFail.Errorf(rsp.Statusinfo)
	}

	return
}

func (s *xAgentSdk) QueryCmd(ctx context.Context, req *QueryCmdRequest) (rsp *QueryCmdResponse, err error) {
	uri := fmt.Sprintf("/%s", req.CmdName)

	var rawData string
	err = s.doRequest(ctx, "ExecCmd", req.XagentHost, req.XagentPort, http.MethodGet,
		uri, nil, &rawData)
	if err != nil {
		return nil, err
	}

	rsp = &QueryCmdResponse{Data: rawData}

	return
}

// CreateTask - 创建task
func (s *xAgentSdk) CreateTask(ctx context.Context, req *CreateTaskRequest) (rsp *CreateTaskResponse, err error) {
	uri := "/task"

	if req.TaskType == "" {
		req.TaskType = TASK_TYPE_ASYNC
	}

	var taskReq map[string]interface{}
	_ = sdk_utils.CastWithTag(req, &taskReq, "json")
	_ = sdk_utils.CastWithTag(req.Data, &taskReq, "json")

	logger.SdkLogger.Debug(ctx, "create xagent task, ctx: %s, request: %s",
		base_utils.Format(ctx), base_utils.Format(req))
	rsp = &CreateTaskResponse{}
	err = s.doRequest(ctx, "CreateTask", req.XagentHost, req.XagentPort, http.MethodPost,
		uri, taskReq, rsp)
	if err != nil {
		return nil, err
	}
	if rsp.ErrorCode != 0 {
		logger.SdkLogger.Warning(ctx, "xagent task query fail, task_name: %s, task_type: %s, err_info: %s",
			req.TaskName, rsp.ErrorInfo)
		return nil, cerrs.ErrXAgentTaskCreateFail.Errorf(rsp.ErrorInfo)
	}

	return
}

// QueryTask - 查询task状态
func (s *xAgentSdk) QueryTask(ctx context.Context, req *QueryTaskRequest) (rsp *QueryTaskResponse, err error) {
	uri := fmt.Sprintf("/task/%d", req.TaskId)

	rsp = &QueryTaskResponse{}
	err = s.doRequest(ctx, "QueryTask", req.XagentHost, req.XagentPort, http.MethodGet,
		uri, nil, rsp)
	if err != nil {
		return nil, err
	}
	if rsp.ErrorCode != 0 {
		logger.SdkLogger.Warning(ctx, "xagent task query fail, task_id: %s, err_info: %s",
			req.TaskId, rsp.ErrorInfo)
		return nil, cerrs.ErrXAgentTaskQueryFail.Errorf(rsp.ErrorInfo)
	}

	return
}
