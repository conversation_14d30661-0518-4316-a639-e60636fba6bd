//protofsg -with_context -json_tag=1 xagent.proto interface.go
package x1base.sdk.xagent;
option cc_generic_services = true;

message Interface {}

message ExecCmdRequest {
    optional string xagent_host = 1;
    optional int32 xagent_port = 2;
    optional string cmd_name = 3;
    optional Interface data = 4;
}

message ExecCmdResponse {
    optional int32 status = 1;
    optional string statusinfo = 2;
    optional Interface data = 3;
    optional string request_id = 4;
}

message QueryCmdRequest {
    optional string xagent_host = 1;
    optional int32 xagent_port = 2;
    optional string cmd_name = 3;
}

message QueryCmdResponse {
    optional string data = 1;
}

message CreateTaskRequest {
    //@inject_tag json:"-"
    optional string xagent_host = 1;
    //@inject_tag json:"-"
    optional int32 xagent_port = 2;
    optional string task_type = 3;
    //@inject_tag json:"-"
    optional Interface data = 4;
    optional string task_name = 5;
    optional string callback_url = 6;
    optional int32 timeout = 7;
}

message CreateTaskResponse {
     optional string error_info = 1;
     optional int32 error_code = 2;
     optional int64 task_id = 3;
}

message QueryTaskRequest {
    optional string xagent_host = 1;
    optional int32 xagent_port = 2;
    optional int64 task_id = 3;
}

message QueryTaskResponse {
    optional string error_info = 1;
    optional int32 error_code = 2;
    optional int64 task_id = 3;
    optional string task_status = 5;
    optional string task_result = 6;
}

service XAgentService {
    rpc exec_cmd(ExecCmdRequest) returns (ExecCmdResponse);
    rpc query_cmd(QueryCmdRequest) returns (QueryCmdResponse);
    rpc create_task(CreateTaskRequest) returns (CreateTaskResponse);
    rpc query_task(QueryTaskRequest) returns (QueryTaskResponse);
}

