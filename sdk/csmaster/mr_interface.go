/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (<EMAIL>)
 * Date: 2022/04/28
 * File: mr_interface.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package csmaster TODO package function desc
package csmaster

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"
)

type MrCsmasterClusterRequest struct {
	Req    *CsmasterClusterRequest
	Region string
}

type MrCsmasterInstancesRequest struct {
	Req    *CsmasterInstancesRequest
	Region string
}

type MrCsmasterAclStatusUpdateRequest struct {
	Req    *CsmasterAclStatusUpdateRequest
	Region string
}

type MrCsmasterAclGetUserListRequest struct {
	Req    *CsmasterAclGetUserListRequest
	Region string
}

type MrCsmasterUpdateBackupRecordsRequest struct {
	Req    *CsmasterUpdateBackupRecordsRequest
	Region string
}

type MrCsmasterGetWhiteIPsRequest struct {
	Req    *CsmasterGetWhiteIPsRequest
	Region string
}

type MrCsmasterAclActionsRequest struct {
	Req    *CsmasterAclActionsRequest
	Region string
}

type MrCsmasterSetExpectVersionRequest struct {
	Req    *CsmasterSetExpectVersionRequest
	Region string
}

type MrCsmasterModifyClientAuthRequest struct {
	Req    *CsmasterModifyClientAuthRequest
	Region string
}

type MrCsmasterAdapter struct {
	Req    ghttp.Request
	Region string
}

type MrSetStaleReadReq struct {
	Req    *SetStaleReadReq
	Region string
}

type MrSetClusterQpsReq struct {
	Req    *SetClusterQpsReq
	Region string
}

type MrModifyIpListRequest struct {
	Req    *ModifyIpListRequest
	Region string
}

type MrUpdateGroupRequest struct {
	Req    *UpdateGroupRequest
	Region string
}

type MrSetClusterSlotRequest struct {
	Req    *SetClusterSlotRequest
	Region string
}

type MrSimpleCacheClusterReq struct {
	Req    *SimpleCacheClusterReq
	Region string
}

type MrSetClusterAsSlaveReq struct {
	Req    *SetClusterAsSlaveReq
	Region string
}

type MrCsmasterGetClusterDetailRequest struct {
	Req    *CsmasterGetClusterDetailRequest
	Region string
}

type MrAddNewTaskReq struct {
	Req    *AddNewTaskReq
	Region string
}
type MrCsmasterSlaveOfMasterRequest struct {
	Req    *SlaveOfMasterRequest
	Region string
}

type MrCsmasterSlaveOfNoOneRequest struct {
	Req    *SlaveOfNoOneRequest
	Region string
}

type MrCsmasterFindNewMaster struct {
	Req    *FindNewMasterRequest
	Region string
}

type MrCsmasterUpdateInnerSecurityRequest struct {
	Req    *UpdateInnerSecurityRequest
	Region string
}

type MrModifyConfigRequest struct {
	Req    *ModifyConfigRequest
	Region string
}

type MrGetSyncGroupDelayRequest struct {
	Req    *GetSyncGroupDelayRequest
	Region string
}

type MrCsmasterModifySyncChannelRequest struct {
	Req    *ModifySyncChannelReq
	Region string
}

type MrCsmasterModifySyncGroupRequest struct {
	Req    *ModifySyncGroupRequest
	Region string
}

type MrSyncGroupBcmResourceRequest struct {
	Req    *SyncGroupBcmResourceRequest
	Region string
}

type MrPutClusterTopoInXmasterRequest struct {
	Req    *PutClusterTopoInXmasterRequest
	Region string
}

type MrCsmasterService interface {
	PickCmSdk(ctx context.Context, region string) CsmasterService
	UpdateClusterModel(ctx context.Context, req *MrCsmasterClusterRequest) (rsp *CsmasterClusterResponse, err error)
	GetClusterModel(ctx context.Context, req *MrCsmasterClusterRequest) (rsp *CsmasterClusterResponse, err error)
	SaveInstanceModels(ctx context.Context, req *MrCsmasterInstancesRequest) (resp *CsmasterInstancesResponse, err error)
	GetInstanceModels(ctx context.Context, req *MrCsmasterInstancesRequest) (resp *CsmasterInstancesResponse, err error)
	DeleteInstanceModels(ctx context.Context, req *MrCsmasterInstancesRequest) (resp *CsmasterInstancesResponse, err error)
	UpdateAclStatus(ctx context.Context, req *MrCsmasterAclStatusUpdateRequest) (resp *CsmasterAclStatusUpdateResponse, err error)
	GetAclUserList(ctx context.Context, req *MrCsmasterAclGetUserListRequest) (resp *CsmasterAclGetUserListResponse, err error)
	UpdateBackupRecords(ctx context.Context, req *MrCsmasterUpdateBackupRecordsRequest) (resp *CsmasterUpdateBackupRecordsResponse, err error)
	GetWhiteIPs(ctx context.Context, req *MrCsmasterGetWhiteIPsRequest) (resp *CsmasterGetWhiteIPsResponse, err error)
	CsmasterAclActions(ctx context.Context, req *MrCsmasterAclActionsRequest) (rsp *CsmasterAclActionsResponse, err error)
	CsmasterSetExpectVersion(ctx context.Context, req *MrCsmasterSetExpectVersionRequest) (rsp *CsmasterSetExpectVersionResponse, err error)
	CsmasterModifyClientAuth(ctx context.Context, req *MrCsmasterModifyClientAuthRequest) (rsp *CsmasterModifyClientAuthResponse, err error)
	CsmasterAdapter(ctx context.Context, req *MrCsmasterAdapter) (*CsmasterAdapterResponse, error)
	SetStaleReadable(ctx context.Context, req *MrSetStaleReadReq) (resp *CommonRes, err error)
	SetClusterQps(ctx context.Context, req *MrSetClusterQpsReq) (resp *CommonRes, err error)
	ModifyClusterIpWhitelist(ctx context.Context, req *MrModifyIpListRequest) (rsp *CommonRes, err error)
	ListClusterIpWhitelist(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *ListWhiteIpResponse, err error)
	UpdateGroupInfo(ctx context.Context, req *MrUpdateGroupRequest) (rsp *CommonRes, err error)
	SetClusterSlot(ctx context.Context, req *MrSetClusterSlotRequest) (rsp *CommonRes, err error)
	GetClusterSlot(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *SlotInfoResponse, err error)
	SetClusterAsSlave(ctx context.Context, req *MrSetClusterAsSlaveReq) (rsp *CommonRes, err error)
	SetClusterAsMaster(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *CommonRes, err error)
	ListCacheCluster(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *ListCacheClusterRes, err error)
	SetClusterForbiddenWrite(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *CommonRes, err error)
	SetClusterWrite(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *CommonRes, err error)
	GetSyncStatus(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *GetSyncStatusResponse, err error)
	GetVpcSgIdsByCluster(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *GetSecurityGroupIdsResponse, err error)
	CsmasterGetClusterDetail(ctx context.Context, req *MrCsmasterGetClusterDetailRequest) (rsp *ClusterDetailResponse, err error)
	GetClusterInfoKeyspace(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *ClusterInfoKeyspaceResponse, err error)
	AddNewTask(ctx context.Context, req *MrAddNewTaskReq) (rsp *X1CommonRes, err error)
	SlaveOfMaster(ctx context.Context, req *MrCsmasterSlaveOfMasterRequest) (rsp *SlaveOfMasterResponse, err error)
	SlaveOfNoOne(ctx context.Context, req *MrCsmasterSlaveOfNoOneRequest) (rsp *X1CommonRes, err error)
	MrCsmasterFindNewMaster(ctx context.Context, req *MrCsmasterFindNewMaster) (rsp *FindNewMasterResponse, err error)
	MrCsmasterUpdateInnerSecurity(ctx context.Context, req *MrCsmasterUpdateInnerSecurityRequest) (rsp *X1CommonRes, err error)
	GetClusterInfoReplication(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *ClusterInfoReplicationResponse, err error)
	GetClusterStatus(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *GetClusterStatusResponse, err error)
	MrCsmasterUpdateSecurity(ctx context.Context, req *MrCsmasterUpdateInnerSecurityRequest) (rsp *X1CommonRes, err error)
	MrCsmasterModifyConfigInfo(ctx context.Context, req *MrModifyConfigRequest) (resp *ModifyConfigResponse, err error)
	MrGetSyncDelay(ctx context.Context, req *MrGetSyncGroupDelayRequest) (resp *GetSyncGroupDelayResponse, err error)
	MrGetSyncDelayTime(ctx context.Context, req *MrGetSyncGroupDelayRequest) (resp *GetSyncGroupDelayResponse, err error)
	MrCsmasterModifySyncChannel(ctx context.Context, req *MrCsmasterModifySyncChannelRequest) (resp *X1CommonRes, err error)
	MrCsmasterModifySyncGroup(ctx context.Context, req *MrCsmasterModifySyncGroupRequest) (resp *X1CommonRes, err error)
	MrOperateSyncGroupBcmResourceRequest(ctx context.Context, req *MrSyncGroupBcmResourceRequest) (resp *X1CommonRes, err error)
	MrPutClusterTopoInXmaster(ctx context.Context, req *MrPutClusterTopoInXmasterRequest) (resp *X1CommonRes, err error)
}
