/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/27
 * File: zone_sdk_test.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package sdk TODO package function desc
package csmaster_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

const (
	IamUserId   = "204d95b9fa0c4cad8eeea3860b5ad6cf"
	AppID       = "scs-bj-tzvnnoasdmjj"
	AppShortID  = 23611
	UserShortID = 44
)

func init() {
	unittest.UnitTestInit(2)
}

//func TestCsmasterUpdateCluster(t *testing.T) {
//	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
//	defer cancel()
//	stsSdk := sts.NewDefaultStsSdk()
//	req := &sts.GetAssumeRoleRequest{
//		TransactionId: base_utils.GetReqID(ctx),
//		IamUserId:     IamUserId,
//	}
//	rsp, err := stsSdk.GetAssumeRole(ctx, req)
//	if err != nil {
//		t.Fatalf("expect no error, actual err %s", err.Error())
//	}
//	token := rsp.Token.Id
//	{
//		cResp, err := csmaster.NewDefaultCsmasterSdk().GetClusterModel(ctx, &csmaster.CsmasterClusterRequest{
//			AppID: AppID,
//			Token: token,
//		})
//		if err != nil {
//			t.Fatalf("expect no error, actual err %s", err.Error())
//		}
//		if cResp.Model.ClusterShowId != AppID {
//			t.Fatalf("expect %s, actual %+v", AppID, cResp.Model)
//		}
//	}
//	{
//		_, err := csmaster.NewDefaultCsmasterSdk().UpdateClusterModel(ctx, &csmaster.CsmasterClusterRequest{
//			Model: &csmaster.CsmasterCluster{
//				Status: 12,
//			},
//			AppID: AppID,
//			Token: token,
//		})
//		if err != nil {
//			t.Fatalf("expect no error, actual err %s", err.Error())
//		}
//	}
//	{
//		cResp, err := csmaster.NewDefaultCsmasterSdk().GetClusterModel(ctx, &csmaster.CsmasterClusterRequest{
//			AppID: AppID,
//			Token: token,
//		})
//		if err != nil {
//			t.Fatalf("expect no error, actual err %s", err.Error())
//		}
//		if cResp.Model.ClusterShowId != AppID {
//			t.Fatalf("expect %s, actual %+v", AppID, cResp.Model)
//		}
//		if cResp.Model.Status != 12 {
//			t.Fatalf("expect status 12, actual %+v", cResp.Model)
//		}
//	}
//	{
//		_, err := csmaster.NewDefaultCsmasterSdk().UpdateClusterModel(ctx, &csmaster.CsmasterClusterRequest{
//			Model: &csmaster.CsmasterCluster{
//				Status: 1,
//			},
//			AppID: AppID,
//			Token: token,
//		})
//		if err != nil {
//			t.Fatalf("expect no error, actual err %s", err.Error())
//		}
//	}
//	{
//		cResp, err := csmaster.NewDefaultCsmasterSdk().GetClusterModel(ctx, &csmaster.CsmasterClusterRequest{
//			AppID: AppID,
//			Token: token,
//		})
//		if err != nil {
//			t.Fatalf("expect no error, actual err %s", err.Error())
//		}
//		if cResp.Model.ClusterShowId != AppID {
//			t.Fatalf("expect %s, actual %+v", AppID, cResp.Model)
//		}
//		if cResp.Model.Status != 1 {
//			t.Fatalf("expect status 1, actual %+v", cResp.Model)
//		}
//	}
//}

//func TestCsmasterSaveInstances(t *testing.T) {
//	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
//	defer cancel()
//	stsSdk := sts.NewDefaultStsSdk()
//	req := &sts.GetAssumeRoleRequest{
//		TransactionId: base_utils.GetReqID(ctx),
//		IamUserId:     IamUserId,
//	}
//	rsp, err := stsSdk.GetAssumeRole(ctx, req)
//	if err != nil {
//		t.Fatalf("expect no error, actual err %s", err.Error())
//	}
//	token := rsp.Token.Id
//
//	insts := []*csmaster.CsmasterInstance{{
//		ClusterId:         AppShortID,
//		UserId:            UserShortID,
//		Port:              6379,
//		CreateTime:        time.Now().Format("2006-01-03 15:04:05"),
//		Flavor:            2,
//		Uuid:              "963643ee-020f-4247-a9c3-dbb8826bd87d",
//		CacheInstanceType: 3,
//		SlaverRedis:       "***********",
//		Persistence:       2,
//		FixIp:             "***********",
//		FloatingIp:        "*********",
//		Password:          "xhadfUisdfhav",
//		HashName:          "bj_recwaijntcbp_0",
//		HostName:          "instance-asdfasfe-01",
//		IamUserId:         IamUserId,
//		ShardId:           12345,
//		HashId:            "963643ee-020f-4247-a9c3-dbb8826bd87d",
//		AvailabilityZone:  "zoneA",
//	}, {
//		ClusterId:         AppShortID,
//		UserId:            UserShortID,
//		Port:              6379,
//		CreateTime:        time.Now().Format("2006-01-03 15:04:05"),
//		Flavor:            2,
//		Uuid:              "d9170349-095d-4c86-94ab-3edf29079892",
//		CacheInstanceType: 3,
//		MasterRedis:       "***********",
//		Persistence:       2,
//		FixIp:             "***********",
//		FloatingIp:        "*********",
//		Password:          "xhadfUisdfhav",
//		HashName:          "bj_recwaijntcbp_0",
//		HostName:          "instance-asdfasfe-02",
//		IamUserId:         IamUserId,
//		ShardId:           12345,
//		HashId:            "963643ee-020f-4247-a9c3-dbb8826bd87d",
//		AvailabilityZone:  "zoneA",
//	}}
//
//	{
//		_, err := csmaster.NewDefaultCsmasterSdk().SaveInstanceModels(ctx, &csmaster.CsmasterInstancesRequest{
//			Models: insts,
//			AppID:  AppID,
//			Token:  token,
//		})
//		if err != nil {
//			t.Fatalf("expect no error, actual err %s", err.Error())
//		}
//	}
//	{
//		cResp, err := csmaster.NewDefaultCsmasterSdk().GetInstanceModels(ctx, &csmaster.CsmasterInstancesRequest{
//			AppID: AppID,
//			Token: token,
//		})
//		if err != nil {
//			t.Fatalf("expect no error, actual err %s", err.Error())
//		}
//		if len(cResp.Models) != 2 {
//			t.Fatalf("expect 2 modes, actual %d", len(cResp.Models))
//		}
//		if cResp.Models[1].Id == 0 {
//			t.Fatalf("expect non zero, actual zero; model: %+v", *(cResp.Models[1]))
//		}
//	}
//	insts = []*csmaster.CsmasterInstance{{
//		Uuid:  "963643ee-020f-4247-a9c3-dbb8826bd87d",
//		FixIp: "***********",
//	}, {
//		Uuid:  "d9170349-095d-4c86-94ab-3edf29079892",
//		FixIp: "***********",
//	}}
//	{
//		_, err := csmaster.NewDefaultCsmasterSdk().SaveInstanceModels(ctx, &csmaster.CsmasterInstancesRequest{
//			Models: insts,
//			AppID:  AppID,
//			Token:  token,
//		})
//		if err != nil {
//			t.Fatalf("expect no error, actual err %s", err.Error())
//		}
//	}
//	{
//		cResp, err := csmaster.NewDefaultCsmasterSdk().GetInstanceModels(ctx, &csmaster.CsmasterInstancesRequest{
//			AppID: AppID,
//			Token: token,
//		})
//		if err != nil {
//			t.Fatalf("expect no error, actual err %s", err.Error())
//		}
//		if len(cResp.Models) != 2 {
//			t.Fatalf("expect 2 modes, actual %d", len(cResp.Models))
//		}
//		for _, model := range cResp.Models {
//			if model.Uuid == "963643ee-020f-4247-a9c3-dbb8826bd87d" && model.FixIp != "***********" {
//				t.Fatalf("expect ***********, actucal %s; model:%+v", model.FixIp, model)
//			}
//		}
//	}
//	{
//		_, err := csmaster.NewDefaultCsmasterSdk().DeleteInstanceModels(ctx, &csmaster.CsmasterInstancesRequest{
//			Models: insts,
//			AppID:  AppID,
//			Token:  token,
//		})
//		if err != nil {
//			t.Fatalf("expect no error, actual err %s", err.Error())
//		}
//	}
//	{
//		cResp, err := csmaster.NewDefaultCsmasterSdk().GetInstanceModels(ctx, &csmaster.CsmasterInstancesRequest{
//			AppID: AppID,
//			Token: token,
//		})
//		if err != nil {
//			t.Fatalf("expect no error, actual err %s", err.Error())
//		}
//		if len(cResp.Models) != 0 {
//			t.Fatalf("expect 0 modes, actual %d", len(cResp.Models))
//		}
//	}
//}

func TestCsmasterBackupAction(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	csmasterOp := csmaster.NewCsmasterSdk("")

	// case 1
	resp, err := csmasterOp.CsmasterBackupAction(ctx, nil)
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")

	// case 2
	resp, err = csmasterOp.CsmasterBackupAction(ctx, &csmaster.BackupActionReq{
		Action: "list_records",
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")
}

func TestCsmasterCreateBigkeyTask(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	csmasterOp := csmaster.NewCsmasterSdk("")

	// case 1
	resp, err := csmasterOp.CsmasterCreateBigkeyTask(ctx, nil)
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")

	// case 2
	resp, err = csmasterOp.CsmasterCreateBigkeyTask(ctx, &csmaster.CreateBigkeyTaskReq{
		CacheClusterShowId: "scs-bj-xxxxx",
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")
}

func TestGetClusterDetailFromCsmaster(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	csmasterOp := csmaster.NewCsmasterSdk("")

	// case 1
	resp, err := csmasterOp.GetClusterDetailFromCsmaster(ctx, nil)
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")

	// case 2
	resp, err = csmasterOp.GetClusterDetailFromCsmaster(ctx, &csmaster.CsmasterGetClusterDetailRequest{
		AppID: "mock",
		Token: "mock",
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")
}

func Test_csmasterSdk_OperateSyncGroupBcmResource(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	csmasterOp := csmaster.NewCsmasterSdk("")

	// case 1
	resp, err := csmasterOp.OperateSyncGroupBcmResource(ctx, nil)
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")

	// case 2
	resp, err = csmasterOp.OperateSyncGroupBcmResource(ctx, &csmaster.SyncGroupBcmResourceRequest{
		Token: "mock",
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")
}

func Test_csmasterSdk_DealTimeWindowTask(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	csmasterOp := csmaster.NewCsmasterSdk("")

	// case 1
	_, err := csmasterOp.DealTimeWindowTask(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Error(err)
	}

	// case 2
	_, err = csmasterOp.DealTimeWindowTask(ctx, &csmaster.DealTimeWindowTaskReq{
		Token:  "",
		TaskID: 0,
		Action: "",
	})
	if err == nil {
		t.Error("no token except error")
	}
}

func Test_csmasterSdk_PutClusterTopoInXmaster(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	csmasterOp := csmaster.NewCsmasterSdk("")

	// case 1
	_, err := csmasterOp.PutClusterTopoInXmaster(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Error(err)
	}

	// case 2
	_, err = csmasterOp.PutClusterTopoInXmaster(ctx, &csmaster.PutClusterTopoInXmasterRequest{
		Token:              "",
		CacheClusterShowID: "",
	})
	if err == nil {
		t.Error("no token except error")
	}
}
