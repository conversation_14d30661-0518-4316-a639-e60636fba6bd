/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/04/28
 * File: mr_csmaster.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package csmaster TODO package function desc
package csmaster

import (
	"context"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	commonUtil "icode.baidu.com/baidu/scs/x1-base/utils/common"
)

var ErrCantPickCsdk = errors.Errorf("cant pick a valid csmaster sdk")

var MrCsdk *mrCsmasterSdk = &mrCsmasterSdk{SdkMap: make(map[string]CsmasterService, 0)}

type MrCmConf struct {
	common.MultiRegionCnf
}

type mrCsmasterSdk struct {
	SdkMap map[string]CsmasterService
}

func MustInitMrCsmasterSdk(ctx context.Context, conf MrCmConf) {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		MrCsdk.SdkMap[privatecloud.DBStackPrefix] = NewCsmasterSdk(conf.ServiceName + "_" + privatecloud.DBStackPrefix)
		return
	}
	for _, region := range conf.RegionList {
		MrCsdk.SdkMap[region] = NewCsmasterSdk(conf.ServiceName + "_" + region)
	}
}

func (m *mrCsmasterSdk) PickCmSdk(ctx context.Context, region string) CsmasterService {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return m.SdkMap[privatecloud.DBStackPrefix]
	}
	innerRegion := commonUtil.GetInnerRegion(ctx, region)
	if csdk, ok := m.SdkMap[innerRegion]; ok {
		return csdk
	}
	return nil
}

func (m *mrCsmasterSdk) UpdateClusterModel(ctx context.Context, req *MrCsmasterClusterRequest) (rsp *CsmasterClusterResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.UpdateClusterModel(ctx, req.Req)
}

func (m *mrCsmasterSdk) GetClusterModel(ctx context.Context, req *MrCsmasterClusterRequest) (rsp *CsmasterClusterResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.GetClusterModel(ctx, req.Req)
}

func (m *mrCsmasterSdk) SaveInstanceModels(ctx context.Context, req *MrCsmasterInstancesRequest) (resp *CsmasterInstancesResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.SaveInstanceModels(ctx, req.Req)
}

func (m *mrCsmasterSdk) GetInstanceModels(ctx context.Context, req *MrCsmasterInstancesRequest) (resp *CsmasterInstancesResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.GetInstanceModels(ctx, req.Req)
}

func (m *mrCsmasterSdk) DeleteInstanceModels(ctx context.Context, req *MrCsmasterInstancesRequest) (resp *CsmasterInstancesResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.DeleteInstanceModels(ctx, req.Req)
}

func (m *mrCsmasterSdk) UpdateAclStatus(ctx context.Context, req *MrCsmasterAclStatusUpdateRequest) (resp *CsmasterAclStatusUpdateResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.UpdateAclStatus(ctx, req.Req)
}

func (m *mrCsmasterSdk) GetAclUserList(ctx context.Context, req *MrCsmasterAclGetUserListRequest) (resp *CsmasterAclGetUserListResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.GetAclUserList(ctx, req.Req)
}

func (m *mrCsmasterSdk) UpdateBackupRecords(ctx context.Context, req *MrCsmasterUpdateBackupRecordsRequest) (resp *CsmasterUpdateBackupRecordsResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.UpdateBackupRecords(ctx, req.Req)
}

func (m *mrCsmasterSdk) GetWhiteIPs(ctx context.Context, req *MrCsmasterGetWhiteIPsRequest) (resp *CsmasterGetWhiteIPsResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.GetWhiteIPs(ctx, req.Req)
}

func (m *mrCsmasterSdk) CsmasterAclActions(ctx context.Context, req *MrCsmasterAclActionsRequest) (rsp *CsmasterAclActionsResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.CsmasterAclActions(ctx, req.Req)
}

func (m *mrCsmasterSdk) CsmasterSetExpectVersion(ctx context.Context, req *MrCsmasterSetExpectVersionRequest) (rsp *CsmasterSetExpectVersionResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.CsmasterSetExpectVersion(ctx, req.Req)
}

func (m *mrCsmasterSdk) CsmasterModifyClientAuth(ctx context.Context, req *MrCsmasterModifyClientAuthRequest) (rsp *CsmasterModifyClientAuthResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.CsmasterModifyClientAuth(ctx, req.Req)
}

func (m *mrCsmasterSdk) CsmasterAdapter(ctx context.Context, req *MrCsmasterAdapter) (*CsmasterAdapterResponse, error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.CsmasterAdapter(ctx, req.Req)
}

func (m *mrCsmasterSdk) SetStaleReadable(ctx context.Context, req *MrSetStaleReadReq) (resp *CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.SetClusterStaleReadable(ctx, req.Req)
}

func (m *mrCsmasterSdk) SetClusterQps(ctx context.Context, req *MrSetClusterQpsReq) (resp *CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.SetClusterQps(ctx, req.Req)
}

func (m *mrCsmasterSdk) ModifyClusterIpWhitelist(ctx context.Context, req *MrModifyIpListRequest) (rsp *CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.ModifyClusterIpWhitelist(ctx, req.Req)
}

func (m *mrCsmasterSdk) UpdateGroupInfo(ctx context.Context, req *MrUpdateGroupRequest) (rsp *CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.UpdateGroupInfo(ctx, req.Req)
}

func (m *mrCsmasterSdk) SetClusterSlot(ctx context.Context, req *MrSetClusterSlotRequest) (rsp *CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.SetClusterSlot(ctx, req.Req)
}

func (m *mrCsmasterSdk) GetClusterSlot(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *SlotInfoResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.GetClusterSlot(ctx, req.Req)
}

func (m *mrCsmasterSdk) SetClusterAsSlave(ctx context.Context, req *MrSetClusterAsSlaveReq) (rsp *CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.SetClusterAsSlave(ctx, req.Req)
}

func (m *mrCsmasterSdk) SetClusterAsMaster(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.SetClusterAsMaster(ctx, req.Req)
}

func (m *mrCsmasterSdk) ListCacheCluster(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *ListCacheClusterRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.ListCacheCluster(ctx, req.Req)
}

func (m *mrCsmasterSdk) SetClusterForbiddenWrite(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.SetClusterForbiddenWrite(ctx, req.Req)
}

func (m *mrCsmasterSdk) SetClusterWrite(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.SetClusterWrite(ctx, req.Req)
}

func (m *mrCsmasterSdk) GetSyncStatus(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *GetSyncStatusResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.GetSyncStatus(ctx, req.Req)
}

func (m *mrCsmasterSdk) ListClusterIpWhitelist(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *ListWhiteIpResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.ListClusterIpWhitelist(ctx, req.Req)
}

func (m *mrCsmasterSdk) GetVpcSgIdsByCluster(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *GetSecurityGroupIdsResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.GetVpcSgIdsByCluster(ctx, req.Req)
}

func (m *mrCsmasterSdk) CsmasterGetClusterDetail(ctx context.Context, req *MrCsmasterGetClusterDetailRequest) (rsp *ClusterDetailResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.CsmasterGetClusterDetail(ctx, req.Req)
}

func (m *mrCsmasterSdk) GetClusterInfoKeyspace(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *ClusterInfoKeyspaceResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.GetClusterInfoKeyspace(ctx, req.Req)
}

func (m *mrCsmasterSdk) AddNewTask(ctx context.Context, req *MrAddNewTaskReq) (rsp *X1CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.AddNewTask(ctx, req.Req)
}

func (m *mrCsmasterSdk) SlaveOfMaster(ctx context.Context, req *MrCsmasterSlaveOfMasterRequest) (rsp *SlaveOfMasterResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.SlaveOfMaster(ctx, req.Req)
}

func (m *mrCsmasterSdk) SlaveOfNoOne(ctx context.Context, req *MrCsmasterSlaveOfNoOneRequest) (rsp *X1CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.SlaveOfNoOne(ctx, req.Req)
}

func (m *mrCsmasterSdk) MrCsmasterFindNewMaster(ctx context.Context, req *MrCsmasterFindNewMaster) (rsp *FindNewMasterResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.FindNewMaster(ctx, req.Req)
}

func (m *mrCsmasterSdk) MrCsmasterUpdateInnerSecurity(ctx context.Context, req *MrCsmasterUpdateInnerSecurityRequest) (rsp *X1CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.UpdateInnerSecurity(ctx, req.Req)
}

func (m *mrCsmasterSdk) GetClusterInfoReplication(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *ClusterInfoReplicationResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.GetClusterInfoReplication(ctx, req.Req)
}

func (m *mrCsmasterSdk) GetClusterStatus(ctx context.Context, req *MrSimpleCacheClusterReq) (rsp *GetClusterStatusResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.GetClusterStatus(ctx, req.Req)
}

func (m *mrCsmasterSdk) MrCsmasterUpdateSecurity(ctx context.Context, req *MrCsmasterUpdateInnerSecurityRequest) (rsp *X1CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.UpdateSecurity(ctx, req.Req)
}

func (m *mrCsmasterSdk) MrCsmasterModifyConfigInfo(ctx context.Context, req *MrModifyConfigRequest) (resp *ModifyConfigResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.CsmasterModifyConfigInfo(ctx, req.Req)
}

func (m *mrCsmasterSdk) MrCsmasterGetClusterConfigInfo(ctx context.Context, req *MrSimpleCacheClusterReq) (resp *GetConfigInfoResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.CsmasterGetClusterConfigInfo(ctx, req.Req)
}

func (m *mrCsmasterSdk) MrCsmasterUpdateRoSecurity(ctx context.Context, req *MrCsmasterUpdateInnerSecurityRequest) (rsp *X1CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.UpdateRoSecurity(ctx, req.Req)
}

func (m *mrCsmasterSdk) MrGetSyncDelay(ctx context.Context, req *MrGetSyncGroupDelayRequest) (resp *GetSyncGroupDelayResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.GetSyncDelay(ctx, req.Req)
}

func (m *mrCsmasterSdk) MrGetSyncDelayTime(ctx context.Context, req *MrGetSyncGroupDelayRequest) (resp *GetSyncGroupDelayResponse, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.GetSyncDelayTime(ctx, req.Req)
}

func (m *mrCsmasterSdk) MrCsmasterModifySyncChannel(ctx context.Context, req *MrCsmasterModifySyncChannelRequest) (resp *X1CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.ModifySyncChannel(ctx, req.Req)
}

func (m *mrCsmasterSdk) MrCsmasterModifySyncGroup(ctx context.Context, req *MrCsmasterModifySyncGroupRequest) (resp *X1CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.ModifySyncGroup(ctx, req.Req)
}

func (m *mrCsmasterSdk) MrOperateSyncGroupBcmResourceRequest(ctx context.Context, req *MrSyncGroupBcmResourceRequest) (resp *X1CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.OperateSyncGroupBcmResource(ctx, req.Req)
}

func (m *mrCsmasterSdk) MrPutClusterTopoInXmaster(ctx context.Context, req *MrPutClusterTopoInXmasterRequest) (resp *X1CommonRes, err error) {
	csdk := m.PickCmSdk(ctx, req.Region)
	if csdk == nil {
		return nil, ErrCantPickCsdk
	}
	return csdk.PutClusterTopoInXmaster(ctx, req.Req)
}
