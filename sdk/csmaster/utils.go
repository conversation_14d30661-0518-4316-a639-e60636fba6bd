/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/03/10
 * File: utils.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package csmaster TODO package function desc
package csmaster

import "context"

type userIdKey string

var iamUserIdKey userIdKey

// NewAuthedContext 把 iam_user_id 存到ctx里
func NewAuthedContext(ctx context.Context, u string) context.Context {
	return context.WithValue(ctx, iamUserIdKey, u)
}

// GetIamUserIdFromCtx 从ctx里拿iam_userid
func GetIamUserIdFromCtx(ctx context.Context) (string, bool) {
	u, ok := ctx.Value(iamUserIdKey).(string)
	return u, ok
}
