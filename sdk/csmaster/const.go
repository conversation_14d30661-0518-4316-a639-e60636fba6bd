/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (<EMAIL>)
 * Date: 2021/12/27
 * File: const.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package sdk TODO package function desc
package csmaster

const DefaultServiceName = "csmaster"

const (
	UpdateClusterModelUrl           = "/v1/scs/updateClusterModel"
	GetClusterModelUrl              = "/v1/scs/getClusterModel"
	SaveInstanceModelsUrl           = "/v1/scs/updateInstancesModel"
	GetInstanceModelsUrl            = "/v1/scs/getInstancesModel"
	DeleteInstanceModelsUrl         = "/v1/scs/deleteInstancesModel"
	AclUrl                          = "/v1/scs/aclUserActions"
	BackupUrl                       = "/v1/scs/backupActions"
	GetWhiteIPs                     = "/v1/scs/getWhiteIpListForX1"
	CsmasterAclActionsUrl           = "/v1/scs/aclUserActions"
	CsmasterSetExpectVersionUrl     = "/v1/scs/setExpectVersion"
	ModifyClientAuthUrl             = "/v1/scs/modifyClientAuth"
	GetClsuterDetailUrl             = "/v1/scs/listInstances"
	GetClusterDetailFromCsmasterUrl = "/v1/scs/csmlistInstances"
	ModifyConfigUrl                 = "/v1/scs/modifyConfigInfo"
	SetStaleReadableUrl             = "/v1/scs/setStaleReadable"
	SetClusterQpsUrl                = "/v1/scs/setClusterQps"
	ModifyClusterIpWhitelistUrl     = "/v1/scs/modifyClusterIpWhitelist"
	ListClusterIpWhiteListUrl       = "/v1/scs/listClusterIpWhitelist"
	GetVpcSgIdsUrl                  = "/v1/scs/getVpcSecurityGroupsIds"
	ListClusterUrl                  = "/v1/scs/list"
	GetClusterInfoKeySpaceUrl       = "/v1/scs/getClusterInfoKeyspace"
	AddTaskUrl                      = "v1/scs/task/add"
	SlaveOfMasterUrl                = "/v1/scs/redisop/slaveofmaster"
	SlaveOfNoOneUrl                 = "/v1/scs/redisop/slaveofnoone"
	FindNewMasterUrl                = "/v1/scs/redisop/findnewmaster"
	UpdateInnerSecurityUrl          = "/v1/scs/redisop/updateinnersecurity"
	CreateOrderUrl                  = "/v1/scs/createOrder"
	ForbiddenWriteUrl               = "/v1/scs/forbiddenWrite"
	EnableWriteUrl                  = "/v1/scs/enableWrite"
	ResetShardFailoverFlagUrl       = "/v1/scs/resetShardFailoverFlag"
	UpdateGroupInfoUrl              = "/v1/scs/updateGroupInfo"
	GetSyncStatusUrl                = "/v1/scs/getSyncStatus"
	GetClusterInfoReplicationUrl    = "/v1/scs/getClusterInfoReplication"
	GetClusterStatusUrl             = "/v1/scs/getClusterStatus"
	UpdateSecurityUrl               = "/v1/scs/redisop/updatesecurity"
	UpdateRoSecurityUrl             = "/v1/scs/redisop/updaterosecurity"
	GetClusterConfigUrl             = "/v1/scs/listUserConfig"
	ModifySyncChannelUrl            = "/v1/scs/modifySyncChannel"
	GetSyncDelayUrl                 = "/v1/scs/getSyncDelay"
	DeleteSyncPointUrl              = "/v1/scs/deleteSyncPoint"
	GetSyncDelayTimeUrl             = "/v1/scs/getSyncDelayTime"
	ModifySyncGroupUrl              = "/v1/scs/modifySyncGroup"
	OperateSyncGroupBcmResourceUrl  = "/v1/scs/operateSyncGroupBcmResource"
	NotEnableReplaceForOP           = "/v1/scs/notEnableReplaceForOP"
	BackupAction                    = "/v1/scs/backupActions"
	CreateBigkeyTask                = "/v1/scs/createBigkeyTask"
	RestartMcProxies                = "/v1/scs/restartMemcacheProxy"
	DealTimeWindowTaskUrl           = "/v1/scs/dealTwTask"
	PutClusterTopoInXmasterUrl      = "/v1/scs/putClusterTopoInXmaster"
)

const (
	INSTANCE_TYPE_PROXY        = 0
	INSTANCE_TYPE_SENTINEL     = 1
	INSTANCE_TYPE_SLAVE_REDIS  = 2
	INSTANCE_TYPE_MASTER_REDIS = 3
	INSTANCE_TYPE_MEMCACHE     = 4
	INSTANCE_TYPE_MASTER_NAS   = 5
)

const (
	GlobalGroupActionUpdate = "update"
	GlobalGroupActionDelete = "delete"

	GlobalGroupRoleUnknow = 0
	GlobalGroupRoleMaster = 1
	GlobalGroupRoleSlave  = 2
)
