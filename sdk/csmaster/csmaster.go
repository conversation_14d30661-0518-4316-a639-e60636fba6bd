/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/27
 * File: zone_sdk.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package sdk TODO package function desc
package csmaster

import (
	"context"
	"encoding/json"
	"net/http"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const AddModifyIpWhitelistAction = "insert"
const DeleteModifyIpWhitelistAction = "delete"

// zoneSdk - ZoneService implement
type csmasterSdk struct {
	common.OpenApi
}

// NewDefaultZoneSdk - new zoneSdk instance with DefaultServiceName
func NewDefaultCsmasterSdk() CsmasterService {
	return newCsmasterSdk(DefaultServiceName)
}

// NewZoneSdk - new zoneSdk instance
func NewCsmasterSdk(serviceName string) CsmasterService {
	return newCsmasterSdk(serviceName)
}

// newZoneSdk - new zoneSdk instance
func newCsmasterSdk(serviceName string) *csmasterSdk {
	s := &csmasterSdk{
		OpenApi: common.NewOpenApi(serviceName),
	}
	return s
}

// doZoneRequest - 通用服务请求方法
//   BCCS_CSMASTER_ERROR_NO ret = KeystoneSDK::get_open_api_request_data(
//        transaction_id,
//        credential,
//        "", "",
//        zone_addr,
//        "GET",
//        _zone_map_list_url,
//        std::map<std::string, std::string>{},
//        request_data
//    );

func (m *csmasterSdk) doCsmasterRequest(ctx context.Context, actionName string, token string,
	httpMethod, uri string, queries, req, rsp any) (err error) {
	params := &common.OpenApiParams{
		ActionName: actionName,
		Token:      token,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
	}

	return m.DoRequest(ctx, params, rsp)
}

func (m *csmasterSdk) UpdateClusterModel(ctx context.Context, req *CsmasterClusterRequest) (rsp *CsmasterClusterResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CsmasterClusterResponse{}
	if err = m.doCsmasterRequest(ctx, "UpdateClusterModel", req.Token, http.MethodPost,
		UpdateClusterModelUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return rsp, err
	}
	return
}

func (m *csmasterSdk) GetClusterModel(ctx context.Context, req *CsmasterClusterRequest) (rsp *CsmasterClusterResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CsmasterClusterResponse{}
	if err = m.doCsmasterRequest(ctx, "GetClusterModel", req.Token, http.MethodPost,
		GetClusterModelUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return rsp, err
	}
	return
}

func (m *csmasterSdk) SaveInstanceModels(ctx context.Context, req *CsmasterInstancesRequest) (rsp *CsmasterInstancesResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CsmasterInstancesResponse{}
	if err = m.doCsmasterRequest(ctx, "SaveInstanceModels", req.Token, http.MethodPost,
		SaveInstanceModelsUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) GetInstanceModels(ctx context.Context, req *CsmasterInstancesRequest) (rsp *CsmasterInstancesResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CsmasterInstancesResponse{}
	if err = m.doCsmasterRequest(ctx, "GetInstanceModels", req.Token, http.MethodPost,
		GetInstanceModelsUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) DeleteInstanceModels(ctx context.Context, req *CsmasterInstancesRequest) (rsp *CsmasterInstancesResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CsmasterInstancesResponse{}
	if err = m.doCsmasterRequest(ctx, "DeleteInstanceModels", req.Token, http.MethodPost,
		DeleteInstanceModelsUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) UpdateAclStatus(ctx context.Context, req *CsmasterAclStatusUpdateRequest) (resp *CsmasterAclStatusUpdateResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	req.AclAction = "force_update_status"
	resp = &CsmasterAclStatusUpdateResponse{}
	if err = m.doCsmasterRequest(ctx, "UpdateAclStatus", req.Token, http.MethodPost,
		AclUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) GetAclUserList(ctx context.Context, req *CsmasterAclGetUserListRequest) (resp *CsmasterAclGetUserListResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	req.AclAction = "getuserlist"
	resp = &CsmasterAclGetUserListResponse{}
	if err = m.doCsmasterRequest(ctx, "GetAclUserList", req.Token, http.MethodPost,
		AclUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) UpdateBackupRecords(ctx context.Context, req *CsmasterUpdateBackupRecordsRequest) (resp *CsmasterUpdateBackupRecordsResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	req.Action = "update_records"
	resp = &CsmasterUpdateBackupRecordsResponse{}
	if err = m.doCsmasterRequest(ctx, "UpdateBackupRecords", req.Token, http.MethodPost,
		BackupUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) GetWhiteIPs(ctx context.Context, req *CsmasterGetWhiteIPsRequest) (resp *CsmasterGetWhiteIPsResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &CsmasterGetWhiteIPsResponse{}
	if err = m.doCsmasterRequest(ctx, "GetWhiteIPs", req.Token, http.MethodPost,
		GetWhiteIPs, map[string]any{"cacheClusterShowId": req.AppID}, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) CsmasterAclActions(ctx context.Context, req *CsmasterAclActionsRequest) (rsp *CsmasterAclActionsResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &CsmasterAclActionsResponse{}
	if err = m.doCsmasterRequest(ctx, "CsmasterAclActions", req.Token, http.MethodPost,
		CsmasterAclActionsUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) CsmasterSetExpectVersion(ctx context.Context, req *CsmasterSetExpectVersionRequest) (rsp *CsmasterSetExpectVersionResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &CsmasterSetExpectVersionResponse{}
	if err = m.doCsmasterRequest(ctx, "CsmasterSetExpectVersion", req.Token, http.MethodPost,
		CsmasterSetExpectVersionUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) CsmasterModifyClientAuth(ctx context.Context, req *CsmasterModifyClientAuthRequest) (rsp *CsmasterModifyClientAuthResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &CsmasterModifyClientAuthResponse{}
	if err = m.doCsmasterRequest(ctx, "CsmasterModifyClientAuth", req.Token, http.MethodPost,
		ModifyClientAuthUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) CsmasterAdapter(ctx context.Context, req ghttp.Request) (*CsmasterAdapterResponse, error) {
	var resp any
	rsp := &resp
	userId, ok := GetIamUserIdFromCtx(ctx)
	if !ok || userId == "" {
		logger.SdkLogger.Error(ctx, "get iam user id from ctx fail,ok:%s , userid: %s",
			base_utils.Format(ok), base_utils.Format(userId))
		return nil, errors.Errorf("get iam user id from ctx fail")
	}

	token, err := compo_utils.GetOpenapiToken(ctx, userId)
	if err != nil {
		logger.SdkLogger.Error(ctx, "get token fail, user id: %s , err: %s",
			base_utils.Format(userId), err.Error())
		return nil, err
	}
	contentType := req.HeaderDefault("Content-Type", "application/x-www-form-urlencoded")
	var postData any
	if contentType == "application/x-www-form-urlencoded" {
		_ = req.HTTPRequest().ParseForm()
		postData = req.HTTPRequest().PostForm
	} else if contentType == "application/json" {
		err = json.NewDecoder(req.Body()).Decode(&postData)
		if err != nil {
			logger.SdkLogger.Warning(ctx, "decode post data fail")
		}
	}

	httprsp, err := m.doCsmasterRequestEx(ctx, req.Path(), token, req.HTTPRequest().Method, req.Path(), req.HTTPRequest().Header, req.HTTPRequest().URL.Query(), postData, rsp)
	// ; err != nil {
	//	return nil, err
	// }
	return &CsmasterAdapterResponse{
		RawResp: httprsp,
		Resp:    rsp,
	}, err
}

func (m *csmasterSdk) doCsmasterRequestEx(ctx context.Context, actionName string, token string,
	httpMethod, uri string, headers, queries, req, rsp any) (*sdk_utils.HttpResponse, error) {
	params := &common.OpenApiParams{
		ActionName: actionName,
		Token:      token,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Headers:    headers,
	}
	logger.SdkLogger.Trace(ctx, "csmaster req ex , params:%s", base_utils.Format(params))

	return m.DoRequestEx(ctx, params, rsp)
}

func (m *csmasterSdk) CsmasterGetClusterDetail(ctx context.Context, req *CsmasterGetClusterDetailRequest) (rsp *ClusterDetailResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &ClusterDetailResponse{}
	if err = m.doCsmasterRequest(ctx, "CsmasterGetClusterDetail", req.Token, http.MethodPost,
		GetClsuterDetailUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) GetClusterDetailFromCsmaster(ctx context.Context, req *CsmasterGetClusterDetailRequest) (rsp *ClusterDetailResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &ClusterDetailResponse{}
	if err = m.doCsmasterRequest(ctx, "CsmasterGetClusterDetail", req.Token, http.MethodPost,
		GetClusterDetailFromCsmasterUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) CsmasterModifyConfigInfo(ctx context.Context, req *ModifyConfigRequest) (resp *ModifyConfigResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &ModifyConfigResponse{}
	if err = m.doCsmasterRequest(ctx, "CsmasterModifyConfigInfo", req.Token, http.MethodPost,
		ModifyConfigUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) SetClusterQps(ctx context.Context, req *SetClusterQpsReq) (resp *CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp := &CommonRes{}
	if err := m.doCsmasterRequest(ctx, "SetClusterQpsUrl", req.Token, http.MethodPost,
		SetClusterQpsUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (m *csmasterSdk) ModifyClusterIpWhitelist(ctx context.Context, req *ModifyIpListRequest) (rsp *CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CommonRes{}
	if err := m.doCsmasterRequest(ctx, "ModifyClusterIpWhitelistUrl", req.Token, http.MethodPost,
		ModifyClusterIpWhitelistUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (m *csmasterSdk) SetClusterStaleReadable(ctx context.Context, req *SetStaleReadReq) (resp *CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp := &CommonRes{}
	if err := m.doCsmasterRequest(ctx, "SetStaleReadalbe", req.Token, http.MethodPost,
		SetStaleReadableUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != 0 {
		return nil, errors.Errorf("set stalereadalbe fail,code:%s", base_utils.Format(rsp.Code))
	}
	return rsp, nil
}

func (m *csmasterSdk) ListClusterIpWhitelist(ctx context.Context, req *SimpleCacheClusterReq) (resp *ListWhiteIpResponse, err error) {
	rsp := &ListWhiteIpResponse{}
	if err := m.doCsmasterRequest(ctx, "ListClusterIpWhitelist", req.Token, http.MethodGet,
		ListClusterIpWhiteListUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (m *csmasterSdk) ListCacheCluster(ctx context.Context, req *SimpleCacheClusterReq) (rsp *ListCacheClusterRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &ListCacheClusterRes{}
	if err = m.doCsmasterRequest(ctx, "ListCacheCluster", req.Token, http.MethodPost,
		ListClusterUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) GetVpcSgIdsByCluster(ctx context.Context, req *SimpleCacheClusterReq) (rsp *GetSecurityGroupIdsResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &GetSecurityGroupIdsResponse{}
	if err = m.doCsmasterRequest(ctx, "GetVpcSgIdsByCluster", req.Token, http.MethodGet,
		GetVpcSgIdsUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) GetClusterInfoKeyspace(ctx context.Context, req *SimpleCacheClusterReq) (rsp *ClusterInfoKeyspaceResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &ClusterInfoKeyspaceResponse{}
	if err = m.doCsmasterRequest(ctx, "GetClusterInfoKeyspace", req.Token, http.MethodGet,
		GetClusterInfoKeySpaceUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) AddNewTask(ctx context.Context, req *AddNewTaskReq) (rsp *X1CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &X1CommonRes{}
	if err = m.doCsmasterRequest(ctx, "AddNewTask", req.Token, http.MethodPost,
		AddTaskUrl, nil, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) SlaveOfMaster(ctx context.Context, req *SlaveOfMasterRequest) (rsp *SlaveOfMasterResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &SlaveOfMasterResponse{}
	if err = m.doCsmasterRequest(ctx, "SlaveOfMaster", req.Token, http.MethodPost,
		SlaveOfMasterUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) SlaveOfNoOne(ctx context.Context, req *SlaveOfNoOneRequest) (rsp *X1CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &X1CommonRes{}
	if err = m.doCsmasterRequest(ctx, "SlaveOfNoOne", req.Token, http.MethodPost,
		SlaveOfNoOneUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) FindNewMaster(ctx context.Context, req *FindNewMasterRequest) (rsp *FindNewMasterResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &FindNewMasterResponse{}
	if err = m.doCsmasterRequest(ctx, "FindNewMaster", req.Token, http.MethodPost,
		FindNewMasterUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

// CsmasterCreateOrder create order
func (m *csmasterSdk) CsmasterCreateOrder(ctx context.Context, req *CreateOrderRequest) (resp *CreateOrderResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &CreateOrderResponse{}
	if err = m.doCsmasterRequest(ctx, "CsmasterCreateOrder", req.Token, http.MethodPost,
		CreateOrderUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) UpdateInnerSecurity(ctx context.Context, req *UpdateInnerSecurityRequest) (rsp *X1CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &X1CommonRes{}
	if err = m.doCsmasterRequest(ctx, "UpdateInnerSecurity", req.Token, http.MethodPost,
		UpdateInnerSecurityUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) UpdateGroupInfo(ctx context.Context, req *UpdateGroupRequest) (rsp *CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &CommonRes{}
	if err = m.doCsmasterRequest(ctx, "UpdateGroupInfo", req.Token, http.MethodPost,
		UpdateGroupInfoUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) SetClusterSlot(ctx context.Context, req *SetClusterSlotRequest) (rsp *CommonRes, err error) {
	// TODO implement me
	panic("implement me")
}

func (m *csmasterSdk) GetClusterSlot(ctx context.Context, req *SimpleCacheClusterReq) (rsp *SlotInfoResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m *csmasterSdk) SetClusterAsSlave(ctx context.Context, req *SetClusterAsSlaveReq) (rsp *CommonRes, err error) {
	// TODO implement me
	panic("implement me")
}

func (m *csmasterSdk) SetClusterAsMaster(ctx context.Context, req *SimpleCacheClusterReq) (rsp *CommonRes, err error) {
	// TODO implement me
	panic("implement me")
}

func (m *csmasterSdk) SetClusterForbiddenWrite(ctx context.Context, req *SimpleCacheClusterReq) (rsp *CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &CommonRes{}
	if err = m.doCsmasterRequest(ctx, "SetClusterForbiddenWrite", req.Token, http.MethodGet,
		ForbiddenWriteUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) SetClusterWrite(ctx context.Context, req *SimpleCacheClusterReq) (rsp *CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &CommonRes{}
	if err = m.doCsmasterRequest(ctx, "SetClusterWrite", req.Token, http.MethodGet,
		EnableWriteUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) GetSyncStatus(ctx context.Context, req *SimpleCacheClusterReq) (rsp *GetSyncStatusResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &GetSyncStatusResponse{}
	if err = m.doCsmasterRequest(ctx, "GetSyncStatus", req.Token, http.MethodGet,
		GetSyncStatusUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) GetClusterInfoReplication(ctx context.Context, req *SimpleCacheClusterReq) (rsp *ClusterInfoReplicationResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &ClusterInfoReplicationResponse{}
	if err = m.doCsmasterRequest(ctx, "GetClusterInfoReplication", req.Token, http.MethodGet,
		GetClusterInfoReplicationUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) ResetShardFailoverFlag(ctx context.Context, req *ResetShardFailoverFlagRequest) (resp *ResetShardFailoverFlagResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &ResetShardFailoverFlagResponse{}
	if err = m.doCsmasterRequest(ctx, "ResetShardFailoverFlag", req.Token, http.MethodPost,
		ResetShardFailoverFlagUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) GetClusterStatus(ctx context.Context, req *SimpleCacheClusterReq) (rsp *GetClusterStatusResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &GetClusterStatusResponse{}
	if err = m.doCsmasterRequest(ctx, "GetClusterStatusUrl", req.Token, http.MethodGet,
		GetClusterStatusUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) UpdateSecurity(ctx context.Context, req *UpdateInnerSecurityRequest) (rsp *X1CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &X1CommonRes{}
	if err = m.doCsmasterRequest(ctx, "UpdateSecurity", req.Token, http.MethodPost,
		UpdateSecurityUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) CsmasterGetClusterConfigInfo(ctx context.Context, req *SimpleCacheClusterReq) (rsp *GetConfigInfoResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &GetConfigInfoResponse{}
	if err = m.doCsmasterRequest(ctx, "getClusterConfig", req.Token, http.MethodPost,
		GetClusterConfigUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) UpdateRoSecurity(ctx context.Context, req *UpdateInnerSecurityRequest) (rsp *X1CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &X1CommonRes{}
	if err = m.doCsmasterRequest(ctx, "UpdateRoSecurity", req.Token, http.MethodPost,
		UpdateRoSecurityUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) ModifySyncChannel(ctx context.Context, req *ModifySyncChannelReq) (rsp *X1CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &X1CommonRes{}
	if err = m.doCsmasterRequest(ctx, "ModifySyncChannel", req.Token, http.MethodPost,
		ModifySyncChannelUrl, map[string]any{"cacheClusterShowId": req.AppID}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) GetSyncDelay(ctx context.Context, req *GetSyncGroupDelayRequest) (resp *GetSyncGroupDelayResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &GetSyncGroupDelayResponse{}
	if err = m.doCsmasterRequest(ctx, "GetSyncDelay", req.Token, http.MethodPost,
		GetSyncDelayUrl, nil, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) DeleteSyncGroupPoint(ctx context.Context, req *SimpleCacheClusterReq) (rsp *X1CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &X1CommonRes{}
	if err = m.doCsmasterRequest(ctx, "deleteSyncGroupPoint", req.Token, http.MethodPost,
		DeleteSyncPointUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) GetSyncDelayTime(ctx context.Context, req *GetSyncGroupDelayRequest) (resp *GetSyncGroupDelayResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &GetSyncGroupDelayResponse{}
	if err = m.doCsmasterRequest(ctx, "GetSyncDelayTime", req.Token, http.MethodPost,
		GetSyncDelayTimeUrl, nil, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) ModifySyncGroup(ctx context.Context, req *ModifySyncGroupRequest) (resp *X1CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &X1CommonRes{}
	if err = m.doCsmasterRequest(ctx, "ModifySyncGroup", req.Token, http.MethodPost,
		ModifySyncGroupUrl, nil, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) OperateSyncGroupBcmResource(ctx context.Context, req *SyncGroupBcmResourceRequest) (resp *X1CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &X1CommonRes{}
	if err = m.doCsmasterRequest(ctx, "OperateSyncGroupBcmResource", req.Token, http.MethodPost,
		OperateSyncGroupBcmResourceUrl, nil, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) CsmasterNotEnableReplace(ctx context.Context, req *NotEnableReplaceRequestForOP) (resp *CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &CommonRes{}
	if err = m.doCsmasterRequest(ctx, "CsmasterNotEnableReplace", req.Token, http.MethodPost,
		NotEnableReplaceForOP, nil, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) CsmasterBackupAction(ctx context.Context, req *BackupActionReq) (resp *BackupActionResp, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &BackupActionResp{}
	if err = m.doCsmasterRequest(ctx, "CsmasterBackupAction", req.Token, http.MethodPost,
		BackupAction, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) CsmasterCreateBigkeyTask(ctx context.Context, req *CreateBigkeyTaskReq) (resp *CreateBigkeyTaskResp, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &CreateBigkeyTaskResp{}
	if err = m.doCsmasterRequest(ctx, "CsmasterCreateBigkeyTask", req.Token, http.MethodPost,
		CreateBigkeyTask, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) RestartMcProxy(ctx context.Context, req *SimpleCacheClusterReq) (rsp *X1CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &X1CommonRes{}
	if err = m.doCsmasterRequest(ctx, "restartMcProxies", req.Token, http.MethodPost,
		RestartMcProxies, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, rsp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) DealTimeWindowTask(ctx context.Context, req *DealTimeWindowTaskReq) (resp *CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	resp = &CommonRes{}
	if err = m.doCsmasterRequest(ctx, "DealTimeWindowTask", req.Token, http.MethodPost,
		DealTimeWindowTaskUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowId}, req, resp); err != nil {
		return nil, err
	}
	return
}

func (m *csmasterSdk) PutClusterTopoInXmaster(ctx context.Context, req *PutClusterTopoInXmasterRequest) (rsp *X1CommonRes, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	rsp = &X1CommonRes{}
	if err = m.doCsmasterRequest(ctx, "PutClusterTopoInXmaster", req.Token, http.MethodPut,
		PutClusterTopoInXmasterUrl, map[string]any{"cacheClusterShowId": req.CacheClusterShowID}, req, rsp); err != nil {
		return nil, err
	}
	return
}
