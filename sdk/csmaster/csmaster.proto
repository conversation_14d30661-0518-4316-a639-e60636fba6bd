// go install icode.baidu.com/baidu/scs/protofsg@latest
// protofsg -with_context -json_tag=1 zone.proto interface.go
package x1-base.sdk.csmaster;
option cc_generic_services = true;

import "x1-base/sdk/common/common.proto";

message CacheClusterModelIface {
    optional int64 id = 1;
    optional int64 user_id = 2;
    optional string cluster_name = 3;
    optional int32 engine_type = 4;
    optional string security_group_id = 5;
    optional string elb_id = 6;
    optional string eip = 7;
    optional string elb_pnetip = 8;
    optional int32 status = 9;
    optional string create_time = 10;
    optional string pool_name = 11;
    optional string security_group_rules_id = 12;
    optional int32 port = 13;
    optional int32 flavor = 14;
    optional int32 persistence = 15;
    optional string domain = 16;
    optional string cluster_show_id = 17;
    optional string transaction_id = 18;
    optional string order_id = 19;
    optional int32 version = 20;
    optional int32 dest_flavor = 21;
    optional int32 tag_type = 22;
    optional int32 instance_num = 23;
    optional string availability_zone = 24;
    optional string subnet_id = 25;
    optional string vpc_id = 26;
    optional string cluster_type = 27;
    optional string master_domain = 28;
    optional int32 master_port = 29;
    optional string master_vpc_id = 30;
    optional string second_subnet_id = 31;
    optional string bcc_callback_flag = 32;
    optional string old_availability_zone = 33;
    optional string cluster_tags = 34;
    optional string cluster_tag_type = 35;
    optional string cluster_flavor_type = 36;
    optional string backup_config = 37;
    optional int32 backup_status = 38;
    optional int32 last_backup_day = 39;
    optional string kernel_version = 40;
    optional int32 conf_version = 41;
    optional int32 whitelist_version = 42;
    optional int32 store_type = 43;
    optional int32 replication_num = 44;
    optional string client_auth = 45;
    optional string meta_auth = 46;
    optional string redis_auth = 47;
    optional string elb_ipv6_id = 48;
    optional string elb_ipv6 = 49;
    optional int32 dest_proxy_num = 50;
    optional string recover_batch_id = 51;
    optional int32 dest_instance_num = 52;
    optional string node_type = 53;
    optional string dest_node_type = 54;
    optional int32 cur_image = 55;
    optional int32 exp_image = 56;
    optional string metaserver_id = 57;
    optional int32 encrypt_flag = 58;
    optional string recycle_time = 59;
    optional int32 recycle_status = 60;
    optional string shard_security_group_id = 61;
    optional string az_deploy_info = 62;
    optional string dest_az_deploy_info = 63;
    optional int32 op_type = 64;
    optional int32 dest_replication_num = 65;
    optional int32 enable_read_only = 66;
    optional int32 proxy_num = 67;
    optional string instance_order_id = 68;
    optional int32 acluser_version = 69;
    optional int32 migration_status = 70;
    optional string alias_name = 71;
    optional int64 timeout = 72;
    optional int32 blb_listener_port = 73;
    optional string public_domain = 74;
    optional int32 isolate_status = 75;
    optional string time_window = 76;
    optional string env_type = 77;
    optional string event_state = 78;
    optional string group_id = 79;
    optional int32 group_role = 80;
    optional string deploy_id_list = 81;
    optional int32 enable_access_log = 82;
    optional string clone_data_cluster_id = 83;
    optional string clone_data_backup_id = 84;
    optional string clone_data_moment = 85;
    optional int32 clone_status = 86;
    optional string restore_time = 87;
    optional int32 enable_restore = 88;
    optional int32 restore_status = 89;
    optional string last_restore_time = 90;
    optional string restore_recover_time = 91;
    optional string restore_batch_id = 92;
    optional string cur_image_ref = 93;
    optional string bgw_group_id = 94;
    optional int32 bgw_group_exclusive = 95;
}

message CacheClusterModelRequest {
    optional string timestamp = 1;
    optional string X_AUTH_TOKEN = 2;
    optional string action = 3;
    optional CommonHead head = 4;
    optional CacheClusterModelIface model = 5;
    optional int64 cacheClusterId = 6;
}

message CacheClusterModelResponse {
    optional string transactionId = 1;
    optional int32 code  = 2;
    optional string message = 3;
    optional string requestId = 4; 
    optional CacheClusterModelIface model = 5;
}

message CacheInstanceModelIface {
    optional int64 id = 1;
    optional int64 cluster_id = 2;
    optional int64 user_id = 3;
    optional int32 port = 4;
    optional string create_time = 5;
    optional int32 flavor = 6;
    optional string uuid = 7;
    optional int32 cache_instance_type = 8;
    optional string master_redis = 9;
    optional string slaver_redis = 10;
    optional int32 status = 11;
    optional int32 persistence = 12;
    optional string fix_ip = 13;
    optional string floating_ip = 14;
    optional string password = 15;
    optional string hash_name = 16;
    optional string host_name = 17;
    optional string iam_user_id = 18;
    optional int64 shard_id = 19;
    optional int32 migrate_status = 20;
    optional string hash_id = 21;
    optional string availability_zone = 22;
    optional string subnet_id = 23;
    optional string ipv6 = 24;
    optional string res_flavor = 25;
    optional string node_id = 26;
    optional int32 stat_port = 27;
    optional int32 xagent_port = 28;
    optional string home_path = 29;
    optional string bbc_id = 30;
}

message CacheInstanceModelsRequest {
    optional string timestamp = 1;
    optional string X_AUTH_TOKEN = 2;
    optional string action = 3;
    optional CommonHead head = 4;
    repeated CacheInstanceModelIface models = 5;
    optional int64 cacheClusterId = 6;
}

message CacheInstanceModelsResponse {
    optional string transactionId = 1;
    optional int32 code  = 2;
    optional string message = 3;
    optional string requestId = 4; 
    repeated CacheInstanceModelIface models = 5;
}

service zoneMapService {
    rpc get_zone_map_list(zoneMapListRequest) returns (zoneMapListResponse);
}
