/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-28
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据zone.proto生成的interface文件
 */

// Package zone
package csmaster

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
)

type CsmasterCluster struct {
	Id                   int64  `json:"id,omitempty"`
	UserId               int64  `json:"user_id,omitempty"`
	ClusterName          string `json:"cluster_name,omitempty"`
	EngineType           int32  `json:"engine_type,omitempty"`
	SecurityGroupId      string `json:"security_group_id,omitempty"`
	ElbId                string `json:"elb_id,omitempty"`
	Eip                  string `json:"eip,omitempty"`
	ElbPnetip            string `json:"elb_pnetip,omitempty"`
	Status               int32  `json:"status,omitempty"`
	CreateTime           string `json:"create_time,omitempty"`
	PoolName             string `json:"pool_name,omitempty"`
	SecurityGroupRulesId string `json:"security_group_rules_id,omitempty"`
	Port                 int32  `json:"port,omitempty"`
	Flavor               int32  `json:"flavor,omitempty"`
	Persistence          int32  `json:"persistence,omitempty"`
	Domain               string `json:"domain,omitempty"`
	ClusterShowId        string `json:"cluster_show_id,omitempty"`
	TransactionId        string `json:"transaction_id,omitempty"`
	OrderId              string `json:"order_id,omitempty"`
	Version              int32  `json:"version,omitempty"`
	DestFlavor           int32  `json:"dest_flavor,omitempty"`
	TagType              int32  `json:"tag_type,omitempty"`
	InstanceNum          int32  `json:"instance_num,omitempty"`
	AvailabilityZone     string `json:"availability_zone,omitempty"`
	SubnetId             string `json:"subnet_id,omitempty"`
	VpcId                string `json:"vpc_id,omitempty"`
	ClusterType          string `json:"cluster_type,omitempty"`
	MasterDomain         string `json:"master_domain,omitempty"`
	MasterPort           int32  `json:"master_port,omitempty"`
	MasterVpcId          string `json:"master_vpc_id,omitempty"`
	SecondSubnetId       string `json:"second_subnet_id,omitempty"`
	BccCallbackFlag      string `json:"bcc_callback_flag,omitempty"`
	OldAvailabilityZone  string `json:"old_availability_zone,omitempty"`
	ClusterTags          string `json:"cluster_tags,omitempty"`
	ClusterTagType       string `json:"cluster_tag_type,omitempty"`
	ClusterFlavorType    string `json:"cluster_flavor_type,omitempty"`
	BackupConfig         string `json:"backup_config,omitempty"`
	BackupStatus         int32  `json:"backup_status,omitempty"`
	LastBackupDay        int32  `json:"last_backup_day,omitempty"`
	KernelVersion        string `json:"kernel_version,omitempty"`
	ConfVersion          int32  `json:"conf_version,omitempty"`
	WhitelistVersion     int32  `json:"whitelist_version,omitempty"`
	StoreType            int32  `json:"store_type,omitempty"`
	ReplicationNum       int32  `json:"replication_num,omitempty"`
	ClientAuth           string `json:"client_auth,omitempty"`
	MetaAuth             string `json:"meta_auth,omitempty"`
	RedisAuth            string `json:"redis_auth,omitempty"`
	ElbIpv6Id            string `json:"elb_ipv6_id,omitempty"`
	ElbIpv6              string `json:"elb_ipv6,omitempty"`
	DestProxyNum         int32  `json:"dest_proxy_num,omitempty"`
	RecoverBatchId       string `json:"recover_batch_id,omitempty"`
	DestInstanceNum      int32  `json:"dest_instance_num,omitempty"`
	NodeType             string `json:"node_type,omitempty"`
	DestNodeType         string `json:"dest_node_type,omitempty"`
	CurImage             int32  `json:"cur_image,omitempty"`
	ExpImage             int32  `json:"exp_image,omitempty"`
	MetaserverId         string `json:"metaserver_id,omitempty"`
	EncryptFlag          int32  `json:"encrypt_flag,omitempty"`
	RecycleTime          string `json:"recycle_time,omitempty"`
	RecycleStatus        int32  `json:"recycle_status,omitempty"`
	ShardSecurityGroupId string `json:"shard_security_group_id,omitempty"`
	AzDeployInfo         string `json:"az_deploy_info,omitempty"`
	DestAzDeployInfo     string `json:"dest_az_deploy_info,omitempty"`
	OpType               int32  `json:"op_type,omitempty"`
	DestReplicationNum   int32  `json:"dest_replication_num,omitempty"`
	EnableReadOnly       int32  `json:"enable_read_only,omitempty"`
	ProxyNum             int32  `json:"proxy_num,omitempty"`
	InstanceOrderId      string `json:"instance_order_id,omitempty"`
	AcluserVersion       int32  `json:"acluser_version,omitempty"`
	MigrationStatus      int32  `json:"migration_status,omitempty"`
	AliasName            string `json:"alias_name,omitempty"`
	Timeout              int64  `json:"timeout,omitempty"`
	BlbListenerPort      int32  `json:"blb_listener_port,omitempty"`
	PublicDomain         string `json:"public_domain,omitempty"`
	IsolateStatus        int32  `json:"isolate_status,omitempty"`
	TimeWindow           string `json:"time_window,omitempty"`
	EnvType              string `json:"env_type,omitempty"`
	EventState           string `json:"event_state,omitempty"`
	GroupId              string `json:"group_id,omitempty"`
	GroupRole            int32  `json:"group_role,omitempty"`
	DeployIdList         string `json:"deploy_id_list,omitempty"`
	EnableAccessLog      int32  `json:"enable_access_log,omitempty"`
	CloneDataClusterId   string `json:"clone_data_cluster_id,omitempty"`
	CloneDataBackupId    string `json:"clone_data_backup_id,omitempty"`
	CloneDataMoment      string `json:"clone_data_moment,omitempty"`
	CloneStatus          int32  `json:"clone_status,omitempty"`
	RestoreTime          string `json:"restore_time,omitempty"`
	EnableRestore        int32  `json:"enable_restore,omitempty"`
	RestoreStatus        int32  `json:"restore_status,omitempty"`
	LastRestoreTime      string `json:"last_restore_time,omitempty"`
	RestoreRecoverTime   string `json:"restore_recover_time,omitempty"`
	RestoreBatchId       string `json:"restore_batch_id,omitempty"`
	CurImageRef          string `json:"cur_image_ref,omitempty"`
	BgwGroupId           string `json:"bgw_group_id,omitempty"`
	BgwGroupExclusive    int32  `json:"bgw_group_exclusive,omitempty"`
	MetaserverAddress    string `json:"metaserver_address,omitempty"`
	BnsService           string `json:"bns_service,omitempty"`
	BnsGroup             string `json:"bns_group,omitempty"`
	BnsNodeName          string `json:"bns_node_name,omitempty"`
	EndpointId           string `json:"endpoint_id,omitempty"`
	EndpointIp           string `json:"endpoint_ip,omitempty"`
	HitX1                int32  `json:"hit_x1,omitempty"`
	SyncGroupId          string `json:"sync_group_id,omitempty"`
	DiskFlavor           int32  `json:"disk_flavor,omitempty"`
	DestDiskFlavor       int32  `json:"dest_disk_flavor,omitempty"`
	SyncGroupName        string `json:"sync_group_name,omitempty"`
	Entrance             string `json:"entrance,omitempty"`
	EnableHotkey         int32  `json:"enable_hotkey,omitempty"`
	NotEnableReplace     int32  `json:"not_enable_replace,omitempty"`
	UseXmaster           int32  `json:"use_xmaster,omitempty"`
	AutoModifyStrategy   string `json:"auto_modify_strategy,omitempty"`
	UseNewBackup         int32  `json:"use_new_backup,omitempty"`
	UseNewAgent          string `json:"use_new_agent,omitempty"`
	UseEncryptBackup     string `json:"use_encrypt_backup,omitempty"`
}

type CsmasterInstance struct {
	Id                int64  `json:"id,omitempty"`
	ClusterId         int64  `json:"cluster_id,omitempty"`
	UserId            int64  `json:"user_id,omitempty"`
	Port              int32  `json:"port,omitempty"`
	CreateTime        string `json:"create_time,omitempty"`
	Flavor            int32  `json:"flavor,omitempty"`
	Uuid              string `json:"uuid,omitempty"`
	CacheInstanceType int32  `json:"cache_instance_type,omitempty"`
	MasterRedis       string `json:"master_redis,omitempty"`
	SlaverRedis       string `json:"slaver_redis,omitempty"`
	Status            int32  `json:"status,omitempty"`
	Persistence       int32  `json:"persistence,omitempty"`
	FixIp             string `json:"fix_ip,omitempty"`
	FloatingIp        string `json:"floating_ip,omitempty"`
	Password          string `json:"password,omitempty"`
	HashName          string `json:"hash_name,omitempty"`
	HostName          string `json:"host_name,omitempty"`
	IamUserId         string `json:"iam_user_id,omitempty"`
	ShardId           int64  `json:"shard_id,omitempty"`
	MigrateStatus     int32  `json:"migrate_status,omitempty"`
	HashId            string `json:"hash_id,omitempty"`
	AvailabilityZone  string `json:"availability_zone,omitempty"`
	SubnetId          string `json:"subnet_id,omitempty"`
	Ipv6              string `json:"ipv6,omitempty"`
	ResFlavor         string `json:"res_flavor,omitempty"`
	NodeId            string `json:"node_id,omitempty"`
	StatPort          int32  `json:"stat_port,omitempty"`
	XagentPort        int32  `json:"xagent_port,omitempty"`
	HomePath          string `json:"home_path,omitempty"`
	BbcId             string `json:"bbc_id,omitempty"`
	NodeShowId        string `json:"node_show_id,omitempty"`
	RoGroupID         int64  `json:"ro_group_id,omitempty"`
	RoGroupWeight     int32  `json:"ro_group_weight,omitempty"`
	RoGroupStatus     int32  `json:"ro_group_status,omitempty"`
	IsReadOnly        int32  `json:"is_readonly,omitempty"`
	ContainerId       string `json:"container_id,omitempty"`
	ContainerName     string `json:"container_name,omitempty"`
	McpackPort        int32  `json:"mcpack_port,omitempty"`
}

type CsmasterClusterRequest struct {
	Model          *CsmasterCluster `json:"model,omitempty"`
	AppID          string           `json:"-"`
	Token          string           `json:"-"`
	RequiredFields []string         `json:"requiredFields"`
}

type CsmasterInstancesRequest struct {
	Models          []*CsmasterInstance `json:"models,omitempty"`
	AppID           string              `json:"-"`
	Token           string              `json:"-"`
	RequiredFields  []string            `json:"requiredFields"`
	StatusForDelete string              `json:"StatusForDelete,omitempty"`
}

type CsmasterClusterResponse struct {
	TransactionId string          `json:"transactionId,omitempty"`
	Code          int             `json:"code,omitempty"`
	Message       string          `json:"message,omitempty"`
	RequestID     string          `json:"requestId,omitempty"`
	Model         CsmasterCluster `json:"model,omitempty"`
}

type CsmasterInstancesResponse struct {
	TransactionId string              `json:"transactionId,omitempty"`
	Code          int                 `json:"code,omitempty"`
	Message       string              `json:"message,omitempty"`
	RequestID     string              `json:"requestId,omitempty"`
	Models        []*CsmasterInstance `json:"models,omitempty"`
}

type CsmasterAclStatusUpdateRequest struct {
	AclAction   string `json:"aclAction,omitempty"`
	UserName    string `json:"userName,omitempty"`
	ForceStatus string `json:"forceStatus,omitempty"`
	AppID       string `json:"-"`
	Token       string `json:"-"`
}

type CsmasterAclStatusUpdateResponse struct {
	TransactionId string `json:"transactionId,omitempty"`
	Code          int    `json:"code,omitempty"`
	Message       string `json:"message,omitempty"`
	RequestID     string `json:"requestId,omitempty"`
}

type CsmasterAclGetUserListRequest struct {
	AclAction string `json:"aclAction,omitempty"`
	AppID     string `json:"-"`
	Token     string `json:"-"`
}

type CsmasterAcl struct {
	UserName     string `json:"userName,omitempty"`
	UserType     int    `json:"userType,omitempty"`
	UpdateStatus int    `json:"updateStatus,omitempty"`
	Extra        string `json:"extra,omitempty"`
}

type CsmasterAclGetUserListResponse struct {
	TransactionId string         `json:"transactionId,omitempty"`
	Code          int            `json:"code,omitempty"`
	Message       string         `json:"message,omitempty"`
	RequestID     string         `json:"requestId,omitempty"`
	Acls          []*CsmasterAcl `json:"aclUser,omitempty"`
}

type CsmasterAclActionsRequest struct {
	AclAction          string `json:"aclAction,omitempty"`
	UserName           string `json:"userName,omitempty"`
	UserType           int    `json:"userType,omitempty"`
	Password           string `json:"password,omitempty"`
	Extra              string `json:"extra,omitempty"`
	AllowedCommands    string `json:"allowedCommands,omitempty"`
	AllowedSubCommands string `json:"allowedSubCommands,omitempty"`
	KeyPatterns        string `json:"keyPatterns,omitempty"`
	AppID              string `json:"-"`
	Token              string `json:"-"`
}

type AclUser struct {
	UserName           string `json:"userName,omitempty"`
	UserType           int    `json:"userType,omitempty"`
	Password           string `json:"password,omitempty"`
	Comment            string `json:"extra,omitempty"`
	AllowedCommands    string `json:"allowedCommands,omitempty"`
	AllowedSubCommands string `json:"allowedSubCommands,omitempty"`
	KeyPatterns        string `json:"keyPatterns,omitempty"`
}

type CsmasterAclActionsResponse struct {
	TransactionId string     `json:"transactionId,omitempty"`
	Code          int        `json:"code,omitempty"`
	Message       string     `json:"message,omitempty"`
	RequestID     string     `json:"requestId,omitempty"`
	AclUsers      []*AclUser `json:"aclUser,omitempty"`
}

type CsmasterBackupRecord struct {
	NodeShortID int64  `json:"inst_id,omitempty"`
	Size        int64  `json:"size,omitempty"`
	Status      string `json:"status,omitempty"`
}

type CsmasterUpdateBackupRecordsRequest struct {
	Action  string                  `json:"action,omitempty"`
	Records []*CsmasterBackupRecord `json:"update_records,omitempty"`
	AppID   string                  `json:"-"`
	Token   string                  `json:"-"`
}

type CsmasterUpdateBackupRecordsResponse struct {
	TransactionId string `json:"transactionId,omitempty"`
	Code          int    `json:"code,omitempty"`
	Message       string `json:"message,omitempty"`
	RequestID     string `json:"requestId,omitempty"`
}

type CsmasterGetWhiteIPsRequest struct {
	Mode  string `json:"mode,omitempty"`
	AppID string `json:"-"`
	Token string `json:"-"`
}

type CsmasterGetWhiteIPsResponse struct {
	TransactionId string   `json:"transactionId,omitempty"`
	Code          int      `json:"code,omitempty"`
	Message       string   `json:"message,omitempty"`
	RequestID     string   `json:"requestId,omitempty"`
	IPs           []string `json:"clusterIpList,omitempty"`
}

type CsmasterSetExpectVersionRequest struct {
	KernelVersion string `json:"kernelVersion,omitempty"`
	UpdateType    string `json:"updateType,omitempty"`
	IsDefer       bool   `json:"isDefer,omitempty"`
	AppID         string `json:"-"`
	Token         string `json:"-"`
}

type CsmasterSetExpectVersionResponse struct {
	TransactionId string `json:"transactionId,omitempty"`
	Code          int    `json:"code,omitempty"`
	Message       string `json:"message,omitempty"`
	RequestID     string `json:"requestId,omitempty"`
}

type CsmasterModifyClientAuthRequest struct {
	ClientAuth string `json:"clientAuth,omitempty"`
	AppID      string `json:"-"`
	Token      string `json:"-"`
}

type CsmasterModifyClientAuthResponse struct {
	TransactionId string `json:"transactionId,omitempty"`
	Code          int    `json:"code,omitempty"`
	Message       string `json:"message,omitempty"`
	RequestID     string `json:"requestId,omitempty"`
}

type CsmasterAdapterResponse struct {
	RawResp *sdk_utils.HttpResponse
	Resp    *any
}

// CsmasterGetClusterDetailRequest define list_cache_cluster_instances request
type CsmasterGetClusterDetailRequest struct {
	AppID string `json:"-"`
	Token string `json:"-"`
}

// ClusterDetailResponse convert to listInstances
type ClusterDetailResponse struct {
	AvailabilityZone        string                  `json:"availabilityZone"`
	BgwGroupExclusive       bool                    `json:"bgwGroupExclusive"`
	BgwGroupID              string                  `json:"bgwGroupId"`
	CacheClusterID          int                     `json:"cacheClusterId"`
	CacheClusterInstanceNum int                     `json:"cacheClusterInstanceNum"`
	CacheClusterInstances   []CacheClusterInstances `json:"cacheClusterInstances"`
	CacheClusterName        string                  `json:"cacheClusterName"`
	CacheClusterShowID      string                  `json:"cacheClusterShowId"`
	ClusterType             string                  `json:"clusterType"`
	CreateTime              time.Time               `json:"createTime"`
	DiskFlavor              int                     `json:"diskFlavor"`
	Domain                  string                  `json:"domain"`
	Eip                     string                  `json:"eip"`
	EnableAccessLog         int                     `json:"enableAccessLog"`
	EnableHotkey            int                     `json:"enableHotkey"`
	EnableReadOnly          int                     `json:"enableReadOnly"`
	EnableReplace           int                     `json:"enableReplace"`
	EnableRestore           int                     `json:"enableRestore"`
	EnableSlowLog           int                     `json:"enableSlowLog"`
	Engine                  string                  `json:"engine"`
	EngineVersion           string                  `json:"engineVersion"`
	EnvType                 string                  `json:"envType"`
	GroupID                 string                  `json:"groupId"`
	GroupRole               int                     `json:"groupRole"`
	HitX1                   int                     `json:"hitX1"`
	InternalPort            int                     `json:"internalPort"`
	Ipv6                    string                  `json:"ipv6"`
	IsolateStatus           int                     `json:"isolateStatus"`
	MaintainTime            MaintainTime            `json:"maintainTime"`
	MasterDomain            string                  `json:"masterDomain"`
	MasterPort              int                     `json:"masterPort"`
	NodeType                string                  `json:"nodeType"`
	PasswordEmpty           int                     `json:"passwordEmpty"`
	Pnetip                  string                  `json:"pnetip"`
	PublicDomain            string                  `json:"publicDomain"`
	RedisList               []RedisList             `json:"redisList"`
	ReplicationInfo         []ReplicationInfo       `json:"replicationInfo"`
	ReplicationNum          int                     `json:"replicationNum"`
	RestoreStatus           int                     `json:"restoreStatus"`
	RestoreTime             string                  `json:"restoreTime"`
	Status                  string                  `json:"status"`
	StoreType               int                     `json:"storeType"`
	SubnetIDList            []string                `json:"subnetIdList"`
	TaskType                string                  `json:"taskType"`
	TotalCapacityInGb       float64                 `json:"totalCapacityInGb"`
	UsedCapacityInGb        float64                 `json:"usedCapacityInGb"`
	UsedMem                 int                     `json:"used_mem"`
	Version                 int                     `json:"version"`
	VpcID                   string                  `json:"vpcId"`
	EnableTde               int                     `json:"enableTde"`
}

// CacheClusterInstances define
type CacheClusterInstances struct {
	FixIP      string `json:"fixIp"`
	FlavorInGB int    `json:"flavorInGB"`
	FloatingIP string `json:"floatingIp"`
	HashName   string `json:"hashName"`
	InstanceID string `json:"instanceId"`
	ShardID    int    `json:"shardId"`
}

// MaintainTime define
type MaintainTime struct {
	Duration  int    `json:"duration"`
	Period    []int  `json:"period"`
	StartTime string `json:"startTime"`
}

// RedisList define
type RedisList struct {
	AvailabilityZone  string `json:"availabilityZone"`
	CacheInstanceType int    `json:"cacheInstanceType"`
	InGroup           int    `json:"inGroup"`
	IsReadOnly        int    `json:"isReadOnly"`
	NodeShowID        string `json:"nodeShowId"`
	UUID              string `json:"uuid"`
}

// ReplicationInfo define
type ReplicationInfo struct {
	AvailabilityZone string `json:"availabilityZone"`
	IsMaster         int    `json:"isMaster"`
	ReplicationNum   int    `json:"replicationNum"`
	SubnetID         string `json:"subnetId"`
}

// ConfItem definision
type ConfItem struct {
	ConfName   string `json:"confName"`
	ConfModule int    `json:"confModule"`
	ConfValue  string `json:"confValue"`
}

// ModifyConfigRequest definition
type ModifyConfigRequest struct {
	Model    *CsmasterCluster `json:"model,omitempty"`
	AppID    string           `json:"-"`
	Token    string           `json:"-"`
	ConfItem *ConfItem        `json:"confItem"`
	From     string           `json:"from,omitempty"`
}

// ModifyConfigResponse definition
type ModifyConfigResponse struct {
	TransactionId string `json:"transactionId,omitempty"`
	Code          int    `json:"code,omitempty"`
	Message       string `json:"message,omitempty"`
	RequestID     string `json:"requestId,omitempty"`
}

type SetStaleReadReq struct {
	Staleread          int32  `json:"staleRead"`
	CacheClusterShowId string `json:"cacheClusterShowId,omitempty"`
	Token              string `json:"-"`
	CacheClusterId     int64  `json:"cacheClusterId,omitempty"`
}

type CommonRes struct {
	RequestId      string `json:"requestId,omitempty"`
	Code           int32  `json:"code,omitempty"`
	Message        string `json:"message,omitempty"`
	CacheClusterId int64  `json:"cacheClusterId,omitempty"`
}

type X1CommonRes struct {
	RequestId      string `json:"requestId,omitempty"`
	Code           string `json:"code,omitempty"`
	Message        string `json:"message,omitempty"`
	CacheClusterId int64  `json:"cacheClusterId,omitempty"`
}

type SetClusterQpsReq struct {
	QpsWrite           int64  `json:"qpsWrite,omitempty"`
	CacheClusterShowId string `json:"cacheClusterShowId,omitempty"`
	Token              string `json:"-"`
	QpsRead            int64  `json:"qpsRead,omitempty"`
}

type ModifyIpListRequest struct {
	ClusterIpList      []string `json:"clusterIpList,omitempty"`
	CacheClusterShowId string   `json:"cacheClusterShowId,omitempty"`
	Token              string   `json:"-"`
	Action             string   `json:"action,omitempty"`
}

type SimpleCacheClusterReq struct {
	Token              string   `json:"-"`
	Action             string   `json:"action,omitempty"`
	CacheClusterName   string   `json:"cacheClusterName,omitempty"`
	CacheInstances     []string `json:"cacheInstances,omitempty"`
	ElbId              string   `json:"elb_id,omitempty"`
	UserId             int64    `json:"userId,omitempty"`
	Eip                string   `json:"eip,omitempty"`
	CacheClusterShowId string   `json:"cacheClusterShowId,omitempty"`
	Domain             string   `json:"domain,omitempty"`
	ClientAuth         string   `json:"clientAuth,omitempty"`
	From               string   `json:"from,omitempty"`
	ExpectVersion      string   `json:"expectVersion,omitempty"`
}

type ListWhiteIpResponse struct {
	ClusterIpList []string `json:"clusterIpList,omitempty"`
	RequestId     string   `json:"requestId,omitempty"`
	Code          int32    `json:"code,omitempty"`
	Message       string   `json:"message,omitempty"`
}

type UpdateGroupRequest struct {
	Action             string `json:"action,omitempty"`
	CacheClusterShowId string `json:"cacheClusterShowId,omitempty"`
	GroupId            string `json:"groupId"`
	GroupRole          int32  `json:"groupRole"`
	CacheClusterId     int64  `json:"cacheClusterId,omitempty"`
	Token              string `json:"-"`
}

type SetClusterSlotRequest struct {
	CacheClusterShowId string  `json:"cacheClusterShowId,omitempty"`
	SlotInfo           []int64 `json:"slotInfo,omitempty"`
	Timestamp          string  `json:"timestamp,omitempty"`
	XAuthToken         string  `json:"XAuthToken,omitempty"`
	CacheClusterId     int64   `json:"cacheClusterId,omitempty"`
}

type SlotInfoResponse struct {
	Slot      []int64 `json:"slot,omitempty"`
	Shard     []int64 `json:"shard,omitempty"`
	RequestId string  `json:"requestId,omitempty"`
	Code      int32   `json:"code,omitempty"`
	Message   string  `json:"message,omitempty"`
}

type SetClusterAsSlaveReq struct {
	CacheClusterShowId string `json:"cacheClusterShowId,omitempty"`
	MasterDomain       string `json:"masterDomain,omitempty"`
	MasterPort         int32  `json:"masterPort,omitempty"`
	MasterVpcId        string `json:"masterVpcId,omitempty"`
	CacheClusterId     int32  `json:"cacheClusterId,omitempty"`
	Timestamp          string `json:"timestamp,omitempty"`
	XAuthToken         string `json:"XAuthToken,omitempty"`
}

type CacheCluster struct {
	CacheClusterId          int64              `json:"cacheClusterId,omitempty"`
	Engine                  string             `json:"engine,omitempty"`
	Pnetip                  string             `json:"pnetip,omitempty"`
	Eip                     string             `json:"eip,omitempty"`
	Status                  string             `json:"status,omitempty"`
	CreateTime              string             `json:"createTime,omitempty"`
	CacheClusterName        string             `json:"cacheClusterName,omitempty"`
	CacheClusterInstanceNum int32              `json:"cacheClusterInstanceNum,omitempty"`
	CacheClusterShowId      string             `json:"cacheClusterShowId,omitempty"`
	TotalCapacityInGb       float64            `json:"totalCapacityInGb,omitempty"`
	UsedCapacityInGb        float64            `json:"usedCapacityInGb,omitempty"`
	Version                 int32              `json:"version,omitempty"`
	AvailabilityZone        string             `json:"availabilityZone,omitempty"`
	SubnetId                string             `json:"subnetId,omitempty"`
	VpcId                   string             `json:"vpcId,omitempty"`
	MasterDomain            string             `json:"masterDomain,omitempty"`
	MasterPort              int32              `json:"masterPort,omitempty"`
	ClusterType             string             `json:"clusterType,omitempty"`
	EngineVersion           string             `json:"engineVersion,omitempty"`
	UsedMem                 int64              `json:"usedMem,omitempty"`
	SubnetIdList            []string           `json:"subnetIdList,omitempty"`
	Domain                  string             `json:"domain,omitempty"`
	Port                    int32              `json:"port,omitempty"`
	StoreType               int32              `json:"storeType,omitempty"`
	ReplicationNum          int32              `json:"replicationNum,omitempty"`
	Ipv6                    string             `json:"ipv6,omitempty"`
	RepairStatus            int64              `json:"repairStatus,omitempty"`
	QuotTime                string             `json:"quotTime,omitempty"`
	RepairStartTime         string             `json:"repairStartTime,omitempty"`
	RepairEndTime           string             `json:"repairEndTime,omitempty"`
	NodeType                string             `json:"nodeType,omitempty"`
	ReplicationInfo         []*ReplicationInfo `json:"replicationInfo,omitempty"`
	EnableReadOnly          int32              `json:"enableReadOnly,omitempty"`
	GroupId                 string             `json:"groupId,omitempty"`
	GroupRole               int32              `json:"groupRole,omitempty"`

	// PasswordEmpty 0:empty 1:no empty
	PasswordEmpty int32 `json:"passwordEmpty,omitempty"`

	IsolateStatus int32  `json:"isolateStatus,omitempty"`
	EventState    string `json:"eventState,omitempty"`
}

type ListCacheClusterRes struct {
	CacheClusterNum int32           `json:"cacheClusterNum,omitempty"`
	CacheClusters   []*CacheCluster `json:"cacheClusters,omitempty"`
	RequestId       string          `json:"requestId,omitempty"`
	Code            int32           `json:"code,omitempty"`
	Message         string          `json:"message,omitempty"`
	CacheClusterId  int64           `json:"cacheClusterId,omitempty"`
}

type RedisSyncInfo struct {
	Ip     string `json:"ip,omitempty"`
	FixIp  string `json:"fixIp,omitempty"`
	Port   int64  `json:"port,omitempty"`
	Status string `json:"status,omitempty"`
}

type GetSyncStatusResponse struct {
	RequestId string           `json:"requestId,omitempty"`
	Code      int32            `json:"code,omitempty"`
	Message   string           `json:"message,omitempty"`
	SyncInfo  []*RedisSyncInfo `json:"syncInfo,omitempty"`
}

type SecurityGroups struct {
	Desc            string `json:"desc,omitempty"`
	Id              string `json:"id,omitempty"`
	Name            string `json:"name,omitempty"`
	SecurityGroupId string `json:"securityGroupId,omitempty"`
	TenantId        string `json:"tenantId,omitempty"`
	VpcId           string `json:"vpcId,omitempty"`
	VpcName         string `json:"vpcName,omitempty"`
}

type GetSecurityGroupIdsResponse struct {
	Code           int32             `json:"code,omitempty"`
	Message        string            `json:"message,omitempty"`
	RequestId      string            `json:"requestId,omitempty"`
	SecurityGroups []*SecurityGroups `json:"securityGroups,omitempty"`
}

type ClusterInfoKeyspaceResponse struct {
	Keys      []int64 `json:"keys,omitempty"`
	RequestId string  `json:"requestId,omitempty"`
	Code      int32   `json:"code,omitempty"`
	Message   string  `json:"message,omitempty"`
}

type AddNewTaskReq struct {
	TaskInfo iface.Task `json:"task_info"`
	Token    string     `json:"-"`
}

type SlaveOfMasterRequest struct {
	ShardGlobalID  string `json:"shardGlobalId"`
	MasterIp       string `json:"masterIp"`
	MasterPort     int    `json:"masterPort"`
	AppID          string `json:"-"`
	Token          string `json:"-"`
	IsManualSwitch bool   `json:"isManualSwitch"`
	UseForbidWrite bool   `json:"useForbidWrite"`
	SyncOffsetDiff int64  `json:"syncOffsetDiff"`
}

type SlaveofMasterErrResult struct {
	NodeID string `json:"nodeId"`
	Msg    string `json:"msg"`
}

type SlaveOfMasterResponse struct {
	Code               string                    `json:"code,omitempty"`
	Message            string                    `json:"message,omitempty"`
	RequestId          string                    `json:"requestId,omitempty"`
	ErrResults         []*SlaveofMasterErrResult `json:"errResults"`
	SwitchMasterErrMsg string                    `json:"switchMasterErrMsg"`
}

type SlaveOfNoOneRequest struct {
	ShardGlobalID string `json:"shardGlobalId"`
	NewMasterID   string `json:"newMasterId"`
	AppID         string `json:"-"`
	Token         string `json:"-"`
}

type FindNewMasterRequest struct {
	ShardGlobalID string `validate:"required" form:"shardGlobalId"`
	AppID         string `json:"-"`
	Token         string `json:"-"`
}

type FindNewMasterResponse struct {
	Code        string `json:"code,omitempty"`
	Message     string `json:"message,omitempty"`
	RequestId   string `json:"requestId,omitempty"`
	NewMasterID string `form:"newMasterId"`
}

type UpdateInnerSecurityRequest struct {
	WhiteIPList []string `form:"whiteIPList"`
	PortList    []int32  `form:"portList"`
	AppID       string   `json:"-"`
	Token       string   `json:"-"`
}

type UpdateInnerSecurityResponse struct {
	Code      int32  `json:"code,omitempty"`
	Message   string `json:"message,omitempty"`
	RequestId string `json:"requestId,omitempty"`
}

// CreateOrderRequest definition
type CreateOrderRequest struct {
	AppID   string `json:"-"`
	Token   string `json:"-"`
	OrderID string `json:"orderId"`
	Action  int    `json:"action"`
}

// CreateOrderResponse definition
type CreateOrderResponse struct {
	TransactionID string `json:"transactionId,omitempty"`
	Code          int    `json:"code,omitempty"`
	Message       string `json:"message,omitempty"`
	RequestID     string `json:"requestId,omitempty"`
}

type ResetShardFailoverFlagRequest struct {
	ClusterID int    `json:"cluster_id"`
	ShardID   int    `json:"shard_id"`
	AppID     string `json:"-"`
	Token     string `json:"-"`
}

type ResetShardFailoverFlagResponse struct {
	Code      int    `json:"code,omitempty"`
	Message   string `json:"message,omitempty"`
	RequestID string `json:"requestId,omitempty"`
}

type ClusterInfoReplicationResponse struct {
	RedisSlaveInfo []*RedisSlaveInfo
	RequestId      string
	Code           int32
	Message        string
}

type GetClusterStatusResponse struct {
	Code    int32  `json:"code,omitempty"`
	Message string `json:"message,omitempty"`
	Status  int32  `json:"status,omitempty"`
}

type RedisSlaveInfo struct {
	Ip     string
	Port   int64
	State  string
	Offset int64
	Lag    int64
}

// GetConfigInfoResponse definition
type GetConfigInfoResponse struct {
	ConfItems []*ConfItem `json:"confItems"`
}

type ModifySyncChannelReq struct {
	Action            string `json:"action,omitempty"`
	AppID             string `json:"-"`
	PeerClusterShowID string `json:"peerClusterShowId,omitempty"`
	PeerIP            string `json:"peerIp,omitempty"`
	PeerPort          int    `json:"peerPort,omitempty"`
	PeerAuth          string `json:"peerAuth,omitempty"`
	Token             string `json:"-"`
}

type DestInfo struct {
	DestCluster string `json:"destCluster"`
	IP          string `json:"ip"`
	Port        int64  `json:"port"`
}

type GetSyncGroupDelayRequest struct {
	SourceCluster string     `json:"sourceCluster"`
	DestClusters  []DestInfo `json:"destClusters"`
	Token         string     `json:"-"`
}

type SyncGroupBcmResourceRequest struct {
	SourceCluster   string     `json:"sourceCluster"`
	NewDestClusters []DestInfo `json:"newDestClusters"`
	Action          string     `json:"action"`
	Token           string     `json:"-"`
}

type GetSyncGroupDelayResponse struct {
	SourceCluster string     `json:"sourceCluster"`
	DestClusters  []SyncInfo `json:"destClusters"`
}

type SyncInfo struct {
	DestCluster string `json:"destCluster"`
	Delay       int64  `json:"delay,omitempty"`
	DelayTime   int64  `json:"delayTime,omitempty"`
}

type ModifySyncGroupRequest struct {
	CacheClusterShowID string `json:"cacheClusterShowId"`
	SyncGroupShowID    string `json:"syncGroupShowId"`
	Token              string `json:"-"`
}

type NotEnableReplaceRequestForOP struct {
	Action    string `json:"action,omitempty"`
	ClusterID int64  `json:"cluster_id,omitempty"`
	PassWD    string `json:"passwd,omitempty"`
	Token     string `json:"-"`
}

type NotEnableReplaceResponseForOP struct {
	TransactionId string `json:"transactionId,omitempty"`
	Code          int    `json:"code,omitempty"`
	Message       string `json:"message,omitempty"`
	RequestID     string `json:"requestId,omitempty"`
}

type BackupActionReq struct {
	Token              string                `json:"-"`
	CacheClusterShowId string                `json:"cacheClusterShowId,omitempty"`
	Action             string                `json:"action,omitempty"`
	AutoBackupConfig   string                `json:"autoBackupConfig,omitempty"`
	BackupId           int                   `json:"backupId,omitempty"`
	BatchId            string                `json:"batchId,omitempty"`
	Comment            string                `json:"comment,omitempty"`
	UrlExpiration      int                   `json:"urlExpiration,omitempty"`
	ListOrder          string                `json:"listOrder,omitempty"`
	Page               int                   `json:"page,omitempty"`
	PageSize           int                   `json:"pageSize,omitempty"`
	UpdateRecords      []*UpdateRecordParams `json:"update_records,omitempty"`
}

type CreateBigkeyTaskReq struct {
	Token              string `json:"-"`
	CacheClusterShowId string `json:"cacheClusterShowId,omitempty"`
}

type CreateBigkeyTaskResp struct {
	Code      int    `json:"code,omitempty"`
	Message   string `json:"message,omitempty"`
	RequestID string `json:"requestId,omitempty"`
}

type UpdateRecordParams struct {
	InstID int    `json:"inst_id,omitempty"`
	Size   int    `json:"size,omitempty"`
	Status string `json:"status,omitempty"`
}

type BackupActionResp struct {
	Code             int                  `json:"code,omitempty"`
	Message          string               `json:"message,omitempty"`
	RequestID        string               `json:"requestId,omitempty"`
	AutoBackupConfig string               `json:"autoBackupConfig,omitempty"`
	Url              string               `json:"url,omitempty"`
	UrlExpiration    int                  `json:"urlExpiration,omitempty"`
	Quota            int                  `json:"quota,omitempty"`
	Total            int                  `json:"total"`
	Records          []*BatchBackupRecord `json:"records,omitempty"`
}

type BackupRecord struct {
	BackupId     int64         `json:"backupId"`             // backup_id
	StartTime    string        `json:"startTime"`            // start_time
	Duration     int64         `json:"duration"`             // duration
	ObjectSize   int64         `json:"objectSize"`           // object_size
	BackupType   string        `json:"backupType"`           // backup_type
	BackupStatus string        `json:"backupStatus"`         // backup_status
	Comment      string        `json:"comment"`              // comment
	Expiration   int64         `json:"expiration,omitempty"` // expiration
	ShardName    string        `json:"shardName"`            // shard_name
	Since        time.Duration `json:"since,omitempty"`      // since
}

type BatchBackupRecord struct {
	BatchId      string          `json:"batchId"`      // batch_id
	BackupType   string          `json:"backupType"`   // backup_type
	Comment      string          `json:"comment"`      // comment
	StartTime    string          `json:"startTime"`    // start_time
	BatchRecords []*BackupRecord `json:"batchRecords"` // batch_records
	Recoverable  string          `json:"recoverable"`  // recoverable
}

type DealTimeWindowTaskReq struct {
	Token              string `json:"-"`
	TaskID             int64  `json:"taskId"`
	Action             string `json:"action"`
	CacheClusterShowId string `json:"cacheClusterShowId"`
}

type PutClusterTopoInXmasterRequest struct {
	CacheClusterShowID string   `json:"cacheClusterShowId"`
	Shards             []string `json:"shards"` // cluster show id lists
	DealAllShards      bool     `json:"deal_all_shards"`
	DealApp            bool     `json:"deal_app"`
	Token              string   `json:"-"`
}

type CsmasterService interface {
	UpdateClusterModel(ctx context.Context, req *CsmasterClusterRequest) (rsp *CsmasterClusterResponse, err error)
	GetClusterModel(ctx context.Context, req *CsmasterClusterRequest) (rsp *CsmasterClusterResponse, err error)
	SaveInstanceModels(ctx context.Context, req *CsmasterInstancesRequest) (resp *CsmasterInstancesResponse, err error)
	GetInstanceModels(ctx context.Context, req *CsmasterInstancesRequest) (resp *CsmasterInstancesResponse, err error)
	DeleteInstanceModels(ctx context.Context, req *CsmasterInstancesRequest) (resp *CsmasterInstancesResponse, err error)
	UpdateAclStatus(ctx context.Context, req *CsmasterAclStatusUpdateRequest) (resp *CsmasterAclStatusUpdateResponse, err error)
	GetAclUserList(ctx context.Context, req *CsmasterAclGetUserListRequest) (resp *CsmasterAclGetUserListResponse, err error)
	UpdateBackupRecords(ctx context.Context, req *CsmasterUpdateBackupRecordsRequest) (resp *CsmasterUpdateBackupRecordsResponse, err error)
	GetWhiteIPs(ctx context.Context, req *CsmasterGetWhiteIPsRequest) (resp *CsmasterGetWhiteIPsResponse, err error)
	CsmasterAclActions(ctx context.Context, req *CsmasterAclActionsRequest) (rsp *CsmasterAclActionsResponse, err error)
	CsmasterSetExpectVersion(ctx context.Context, req *CsmasterSetExpectVersionRequest) (rsp *CsmasterSetExpectVersionResponse, err error)
	CsmasterModifyClientAuth(ctx context.Context, req *CsmasterModifyClientAuthRequest) (rsp *CsmasterModifyClientAuthResponse, err error)
	CsmasterAdapter(ctx context.Context, req ghttp.Request) (*CsmasterAdapterResponse, error)
	CsmasterGetClusterDetail(ctx context.Context, req *CsmasterGetClusterDetailRequest) (rsp *ClusterDetailResponse, err error)
	GetClusterDetailFromCsmaster(ctx context.Context, req *CsmasterGetClusterDetailRequest) (rsp *ClusterDetailResponse, err error)
	CsmasterModifyConfigInfo(ctx context.Context, req *ModifyConfigRequest) (resp *ModifyConfigResponse, err error)
	CsmasterCreateOrder(ctx context.Context, req *CreateOrderRequest) (resp *CreateOrderResponse, err error)
	ResetShardFailoverFlag(ctx context.Context, req *ResetShardFailoverFlagRequest) (resp *ResetShardFailoverFlagResponse, err error)
	CsmasterGetClusterConfigInfo(ctx context.Context, req *SimpleCacheClusterReq) (resp *GetConfigInfoResponse, err error)
	CsmasterNotEnableReplace(ctx context.Context, req *NotEnableReplaceRequestForOP) (resp *CommonRes, err error)

	// SetClusterStaleReadable 等 Global新增区域
	SetClusterStaleReadable(ctx context.Context, req *SetStaleReadReq) (resp *CommonRes, err error)

	SetClusterQps(ctx context.Context, req *SetClusterQpsReq) (resp *CommonRes, err error)
	ModifyClusterIpWhitelist(ctx context.Context, req *ModifyIpListRequest) (rsp *CommonRes, err error)
	ListClusterIpWhitelist(ctx context.Context, req *SimpleCacheClusterReq) (rsp *ListWhiteIpResponse, err error)
	UpdateGroupInfo(ctx context.Context, req *UpdateGroupRequest) (rsp *CommonRes, err error)
	SetClusterSlot(ctx context.Context, req *SetClusterSlotRequest) (rsp *CommonRes, err error)
	GetClusterSlot(ctx context.Context, req *SimpleCacheClusterReq) (rsp *SlotInfoResponse, err error)
	SetClusterAsSlave(ctx context.Context, req *SetClusterAsSlaveReq) (rsp *CommonRes, err error)
	SetClusterAsMaster(ctx context.Context, req *SimpleCacheClusterReq) (rsp *CommonRes, err error)
	ListCacheCluster(ctx context.Context, req *SimpleCacheClusterReq) (rsp *ListCacheClusterRes, err error)
	SetClusterForbiddenWrite(ctx context.Context, req *SimpleCacheClusterReq) (rsp *CommonRes, err error)
	SetClusterWrite(ctx context.Context, req *SimpleCacheClusterReq) (rsp *CommonRes, err error)
	GetSyncStatus(ctx context.Context, req *SimpleCacheClusterReq) (rsp *GetSyncStatusResponse, err error)
	GetVpcSgIdsByCluster(ctx context.Context, req *SimpleCacheClusterReq) (rsp *GetSecurityGroupIdsResponse, err error)
	GetClusterInfoKeyspace(ctx context.Context, req *SimpleCacheClusterReq) (rsp *ClusterInfoKeyspaceResponse, err error)
	AddNewTask(ctx context.Context, req *AddNewTaskReq) (rsp *X1CommonRes, err error)
	SlaveOfMaster(ctx context.Context, req *SlaveOfMasterRequest) (rsp *SlaveOfMasterResponse, err error)
	SlaveOfNoOne(ctx context.Context, req *SlaveOfNoOneRequest) (rsp *X1CommonRes, err error)
	FindNewMaster(ctx context.Context, req *FindNewMasterRequest) (rsp *FindNewMasterResponse, err error)
	UpdateInnerSecurity(ctx context.Context, req *UpdateInnerSecurityRequest) (rsp *X1CommonRes, err error)
	GetClusterInfoReplication(ctx context.Context, req *SimpleCacheClusterReq) (rsp *ClusterInfoReplicationResponse, err error)
	UpdateSecurity(ctx context.Context, req *UpdateInnerSecurityRequest) (rsp *X1CommonRes, err error)
	UpdateRoSecurity(ctx context.Context, req *UpdateInnerSecurityRequest) (rsp *X1CommonRes, err error)
	DeleteSyncGroupPoint(ctx context.Context, req *SimpleCacheClusterReq) (resp *X1CommonRes, err error)
	// Global新增区域

	GetClusterStatus(ctx context.Context, req *SimpleCacheClusterReq) (rsp *GetClusterStatusResponse, err error)
	ModifySyncChannel(ctx context.Context, req *ModifySyncChannelReq) (resp *X1CommonRes, err error)
	GetSyncDelay(ctx context.Context, req *GetSyncGroupDelayRequest) (resp *GetSyncGroupDelayResponse, err error)
	GetSyncDelayTime(ctx context.Context, req *GetSyncGroupDelayRequest) (resp *GetSyncGroupDelayResponse, err error)
	ModifySyncGroup(ctx context.Context, req *ModifySyncGroupRequest) (resp *X1CommonRes, err error)
	OperateSyncGroupBcmResource(ctx context.Context, req *SyncGroupBcmResourceRequest) (resp *X1CommonRes, err error)
	CsmasterBackupAction(ctx context.Context, req *BackupActionReq) (resp *BackupActionResp, err error)
	CsmasterCreateBigkeyTask(ctx context.Context, req *CreateBigkeyTaskReq) (resp *CreateBigkeyTaskResp, err error)
	RestartMcProxy(ctx context.Context, req *SimpleCacheClusterReq) (resp *X1CommonRes, err error)
	DealTimeWindowTask(ctx context.Context, req *DealTimeWindowTaskReq) (resp *CommonRes, err error)
	PutClusterTopoInXmaster(ctx context.Context, req *PutClusterTopoInXmasterRequest) (resp *X1CommonRes, err error)
}
