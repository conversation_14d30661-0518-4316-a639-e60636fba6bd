package csmaster

import (
	"context"
	"fmt"
	"testing"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
)

func Test_mrCsmasterSdk_MrCsmasterModifySyncGroup(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	//MustInitMrCsmasterSdk(ctx, MrCmConf{})
	// case 1
	MrCsdk.SdkMap["test"] = newCsmasterSdk("")

	//case 2
	resp, err := MrCsdk.MrOperateSyncGroupBcmResourceRequest(ctx, &MrSyncGroupBcmResourceRequest{
		Req:    nil,
		Region: "test",
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")
}

func Test_mrCsmasterSdk_MrPutClusterTopoInXmaster(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	//MustInitMrCsmasterSdk(ctx, MrCmConf{})
	// case 1
	MrCsdk.SdkMap["test"] = newCsmasterSdk("")

	//case 2
	resp, err := MrCsdk.MrPutClusterTopoInXmaster(ctx, &MrPutClusterTopoInXmasterRequest{
		Req:    nil,
		Region: "test",
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")
}
