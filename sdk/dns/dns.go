/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/30
 * File: dns.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package dns
package dns

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
)

type dnsSdk struct {
	conf *dnsConf
	common.OpenApi
	ralCaller sdk_utils.RalCaller
}

type PnetResquestParams struct {
	HttpMethod string
	Uri        string
	Queries    interface{}
	Posts      interface{}
	RspPtr     interface{}
}

func NewDefaultDnsSdk() DNSService {
	dnsSDK := newDnsSdk(DefaultServiceName)
	dnsSDK.conf = getConf(DefaultServiceName)
	return dnsSDK
}

func NewDefaultPnetDnsSdk() DNSService {
	return newDnsSdk(DefaultPnetServiceName)
}

func NewDnsSdk(serviceName string) DNSService {
	return newDnsSdk(serviceName)
}

func newDnsSdk(serviceName string) *dnsSdk {
	s := &dnsSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
		ralCaller: sdk_utils.NewRalCaller(
			serviceName,
			sdk_utils.ROptGenCurlLog(logit.TraceLevel),
			sdk_utils.ROptPrepareChecker(
				func(ctx context.Context, httpRsp *sdk_utils.HttpResponse, invoker sdk_utils.PrepareCheckFunc) error {
					logger.SdkLogger.Debug(ctx, "dns http rsp: %v", httpRsp)
					return invoker(ctx, httpRsp)
				}),
		),
	}

	return s
}

// doRequest - 通用openstack服务请求方法
func (s *dnsSdk) doRequest(ctx context.Context, actionName string, token string,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	params := &common.OpenApiParams{
		ActionName: actionName,
		Token:      token,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}

	// 请求 openapi
	err = s.DoRequest(ctx, params, rsp)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return err
	}
	return nil
}

// openapi
func (s *dnsSdk) doRequestWithResourceID(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {
	// 获取当前时间
	tmStr := time.Now().UTC().Format("2006-01-02T15:04:05Z")
	rawContent := fmt.Sprintf("%s/%s", s.conf.ResourceID, tmStr)

	encryptAccountID, err := crypto_utils.AesECBEncryptString(rawContent, s.conf.EncryptKey,
		crypto_utils.PKCS7PaddingType, crypto_utils.HexCodecType)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "aes ecb encrypt fail, content: %s, key: %s", rawContent, s.conf.EncryptKey)
		return cerrs.ErrSTSEncryptAccountFail.Wrap(err)
	}

	productResource := "scs"
	if s.conf.Product != "" {
		productResource = s.conf.Product
	}

	headers := map[string]interface{}{
		"resource-accountId": strings.ToUpper(encryptAccountID),
		"resource-source":    productResource,
	}

	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Headers:    headers,
		Product:    s.conf.Product,
	}

	_, err = s.DoRequestWithCustomizeHeader(ctx, params, rsp)
	return err
}

func (s *dnsSdk) CreateDomain(ctx context.Context, req *DNSCreateRequest) (rsp *DNSCreateResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &DNSCreateResponse{}
	if err = s.doRequestWithResourceID(ctx, "CreateDomain", req.Auth, http.MethodPost,
		vnetUri, nil, req, rsp); err != nil {
		return nil, err
	}

	if rsp.ID == "" {
		return nil, cerrs.ErrDNSRequestFail.Errorf("createDomain request fail, req:%s, rsp:%s",
			base_utils.Format(req), base_utils.Format(rsp))
	}
	return
}

func (s *dnsSdk) DeleteDomain(ctx context.Context, req *DNSDeleteRequest) (err error) {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.ID == "" {
		return cerrs.ErrInvalidParams.Errorf("id param is empty")
	}
	if err = s.doRequestWithResourceID(ctx, "DeleteDomain", req.Auth, http.MethodDelete,
		vnetUri+"/"+req.ID, nil, nil, nil); err != nil {
		return err
	}
	return nil
}

func (s *dnsSdk) UpdateDomain(ctx context.Context, req *DNSUpdateRequest) (rsp *DNSUpdateResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (s *dnsSdk) ListDomain(ctx context.Context, req *DNSListRequest) (rsp *DNSListResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Name == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("domain param is empty")
	}

	rsp = &DNSListResponse{}
	if err = s.doRequestWithResourceID(ctx, "ListDomain", req.Auth, http.MethodGet,
		vnetUri, map[string]interface{}{"vpcId": req.VpcID, "name": req.Name}, req, rsp); err != nil {
		return nil, err
	}

	return
}

func (s *dnsSdk) CreatePnetDomain(ctx context.Context, req *PnetResquest) (err error) {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Domain == "" || req.Rdata == "" {
		return cerrs.ErrInvalidParams.Errorf("domain param is empty")
	}

	err = s.doRequest(ctx, "CreatePnetDomain", req.Token, http.MethodPost,
		pnetUri, nil, req, nil)
	if err != nil {
		return err
	}

	return
}

func (s *dnsSdk) DeletePnetDomain(ctx context.Context, req *PnetResquest) (err error) {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Domain == "" || req.Rdata == "" {
		return cerrs.ErrInvalidParams.Errorf("domain param is empty")
	}

	err = s.doRequest(ctx, "DeletePnetDomain", req.Token, http.MethodDelete,
		pnetUri, nil, req, nil)
	if err != nil {
		return err
	}

	return
}
