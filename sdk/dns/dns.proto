//protofsg -with_context -json_tag=1 dns.proto interface.go
option cc_generic_services = true;
package x1-base.sdk.dns;

message DomainUpdateBackend {
    optional string domain = 1;
    optional string view = 2;
    optional string id = 3;
    optional string origin_rdtype = 4;
    optional int32 origin_ttl = 5;
    optional string origin_class = 6;
    optional string origin_rdata = 7;
    optional string new_rdtype = 8;
    optional int32 new_ttl = 9;
    optional string new_class = 10;
    optional string new_rdata = 11;
}

message DomainBackend {
    optional string domain = 1;
    optional string view = 2;
    optional string id = 3;
    optional string rdtype = 4;
    optional int32 ttl = 5;
    optional string class = 6;
    optional string rdata = 7;
}

message DNSRecord {
    //@inject_tag json:",omitempty"
    optional string rdtype = 1;
    //@inject_tag json:",omitempty"
    optional string rdclass = 2;
    //@inject_tag json:",omitempty"
    optional string network_id = 3;
    //@inject_tag json:",omitempty"
    optional string domain_id = 4;
    //@inject_tag json:",omitempty"
    optional string tenant_id = 5;
    //@inject_tag json:",omitempty"
    optional string rdata = 6;
    //@inject_tag json:",omitempty"
    optional int32  ttl = 7;
    //serial 在response中可能是int或string
    //@inject_tag json:",omitempty"
    optional Interface serial = 8;
    //shared 在request中为int，而在response中为bool
    //@inject_tag json:",omitempty"
    optional Interface shared = 9;
    //@inject_tag json:",omitempty"
    optional string id = 10;
    //@inject_tag json:",omitempty"
    optional string name = 11;
    //@inject_tag json:",omitempty"
    optional string domain = 12;
}

message DNSCreateRequest {
    optional DNSRecord dns_record = 1;
    //@inject_tag json:"-"
    optional string token = 2;
}

message DNSUpdateRequest {
    optional DNSRecord dns_record = 1;
    //@inject_tag json:"-"
    optional string token = 2;
}

message NeutronErrorInfo {
    optional string message = 1;
    optional string type = 2;
    optional string detail = 3;
}

message DNSResponse {
    optional DNSRecord dns_record = 1;
    optional NeutronErrorInfo NeutronError = 2;
}

message DNSListRequest {
    optional string domain = 1;
    //@inject_tag json:"-"
    optional string token = 2;
}

message DNSListResponse {
    repeated DNSRecord dns_records = 1;
    optional NeutronErrorInfo NeutronError = 2;
}

message DNSDeleteRequest {
    optional string id = 1;
    //@inject_tag json:"-"
    optional string token = 2;
}

message DNSDeleteResponse {
    optional NeutronErrorInfo NeutronError = 1;
}

message NullRequest {}
message NullResponse {}

message PnetResquest {
    optional string domain = 1;
    optional string zone = 2;
    optional string type = 3;
    optional int32 ttl = 4;
    optional string rdata = 5;
    optional string view = 6;
}

service DNSService {
    rpc CreateDomain(DNSCreateRequest) returns (DNSResponse);
    rpc DeleteDomain(DNSDeleteRequest) returns (DNSDeleteResponse);
    rpc UpdateDomain(DNSUpdateRequest) returns (DNSResponse);
    rpc ListDomain(DNSListRequest) returns (DNSListResponse);

    rpc CreatePnetDomain(PnetResquest) returns (NullResponse);
    rpc DeletePnetDomain(PnetResquest) returns (NullResponse);
}
