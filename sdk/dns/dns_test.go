package dns

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const (
	TestIamUserID = "ea2c4a2286ca4540afcb7f7d4ba2d199"
	TestVpcID     = "58d53a2e-27fe-4ead-9041-c72ec67ec1c1"
	TestName      = "redis.testname.scs.bj.baidubce.com"
)

func TestDnsListSdk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     TestIamUserID,
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	auth := authRsp.Auth

	s := NewDefaultDnsSdk()
	listReq := DNSListRequest{
		VpcID: TestVpcID,
		Name:  "redis.qycftvvjqtjf.scs.bj.baidubce.com",
		Auth:  auth,
	}
	dnsRecordsRsp, err := s.ListDomain(ctx, &listReq)
	fmt.Println(err)
	fmt.Println(base_utils.Format(dnsRecordsRsp))
}

func TestDnsCreateSdk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     TestIamUserID,
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	auth := authRsp.Auth
	s := NewDefaultDnsSdk()

	// create
	createReq := DNSCreateRequest{
		VpcID:  TestVpcID,
		Name:   TestName,
		Domain: "baidubce.com",
		Type:   "A",
		Value:  "192.168.0.2",
		TTL:    10,
		Auth:   auth,
	}
	createRsp, err := s.CreateDomain(ctx, &createReq)
	if err != nil {
		fmt.Println("------------------------")
		fmt.Println(err)
		return
	}
	fmt.Println(base_utils.Format(createRsp))

	deleteReq := DNSDeleteRequest{
		ID:   createRsp.ID,
		Auth: auth,
	}
	err = s.DeleteDomain(ctx, &deleteReq)
	if err != nil {
		fmt.Println("------------------------")
		fmt.Println(err)
		return
	}
}
