/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-02-23
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据dns.proto生成的interface文件
 */

// Package dns
package dns

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type DNSRecord struct {
	VpcID  string `json:"vpcId,omitempty"`
	Name   string `json:"name,omitempty"`
	Domain string `json:"domain,omitempty"`
	Type   string `json:"type,omitempty"`
	Value  string `json:"value,omitempty"`
	TTL    int32  `json:"ttl,omitempty"`
	ID     string `json:"id,omitempty"`
}

type DNSCreateRequest struct {
	VpcID  string                 `json:"vpcId"`
	Name   string                 `json:"name"`
	Domain string                 `json:"domain"`
	Type   string                 `json:"type"`
	Value  string                 `json:"value"`
	TTL    int32                  `json:"ttl"`
	Auth   *common.Authentication `json:"-"`
}

type DNSCreateResponse struct {
	ID string `json:"id"`
}

type DNSUpdateRequest struct {
	Name  string                 `json:"name"`
	Type  string                 `json:"type"`
	Value string                 `json:"value"`
	TTL   int32                  `json:"ttl"`
	Auth  *common.Authentication `json:"-"`
}

type DNSUpdateResponse struct {
	ID string `json:"id"`
}

type NeutronErrorInfo struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Detail  string `json:"detail"`
}

type DNSListRequest struct {
	VpcID string                 `json:"vpcId"`
	Name  string                 `json:"name,omitempty"`
	Auth  *common.Authentication `json:"-"`
}

type DNSListResponse struct {
	Records    []*DNSRecord `json:"records"`
	NextMarker string       `json:"nextMarker"`
	Marker     string       `json:"marker"`
	MaxKeys    int          `json:"maxKeys"`
}

type DNSDeleteRequest struct {
	ID   string                 `json:"id"`
	Auth *common.Authentication `json:"-"`
}

type PnetResquest struct {
	Domain string `json:"domain"`
	Zone   string `json:"zone"`
	Type   string `json:"type"`
	Ttl    int32  `json:"ttl"`
	Rdata  string `json:"rdata"`
	View   string `json:"view"`
	Token  string `json:"-"`
}

type PnetResponse struct {
	ErrCode      int32  `json:"err_code"`
	Msg          string `json:"msg"`
	ErrNum       int32  `json:"ErrNum"`
	ErrMsg       string `json:"ErrMsg"`
	RetData      string `json:"RetData"`
	UrlManual    string `json:"UrlManual"`
	UrlRedirect  string `json:"UrlRedirect"`
	RespBodyType int32  `json:"RespBodyType"`
}

type DNSService interface {
	CreateDomain(ctx context.Context, req *DNSCreateRequest) (rsp *DNSCreateResponse, err error)
	DeleteDomain(ctx context.Context, req *DNSDeleteRequest) (err error)
	UpdateDomain(ctx context.Context, req *DNSUpdateRequest) (rsp *DNSUpdateResponse, err error)
	ListDomain(ctx context.Context, req *DNSListRequest) (rsp *DNSListResponse, err error)
	CreatePnetDomain(ctx context.Context, req *PnetResquest) (err error)
	DeletePnetDomain(ctx context.Context, req *PnetResquest) (err error)
}
