/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/24 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file dns_conf.go
 * <AUTHOR>
 * @date 2023/03/24 15:15:47
 * @brief dns conf

 *
 **/

package dns

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "DNS"

// dnsConf definition
type dnsConf struct {
	// encrypt resource account id
	ResourceID string `toml:"ResourceID, omitempty"`
	EncryptKey string `toml:"EncryptKey, omitempty"`
	Product    string `toml:"Product, omitempty"`
}

var dnsConfMap = &sync.Map{}

func getConf(serviceName string) *dnsConf {
	if conf, ok := dnsConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*dnsConf); ok {
			return conf
		}
	}

	conf := &dnsConf{}
	conf.mustLoad(serviceName)

	dnsConfMap.Store(serviceName, conf)

	return conf
}

func (conf *dnsConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
