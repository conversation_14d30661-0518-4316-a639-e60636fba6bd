package nova

import (
	"context"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

func TestBccSdk_GetBccSg(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newNovaSdk(DefaultServiceName)

	// TODO: 填入真实的参数 ...
	req := &bcc.GetBccBindSgRequest{
		TenantId: "aaaaaaaa",
		Token:    "bbbbbbbb",
		VmUuid:   "cccccccc",
	}

	rsp, err := s.GetBccSg(ctx, req)
	fmt.Println(rsp, err) // TODO auth fail
	//if err != nil {
	//	t.Fatalf("get bcc sg fail, req: %v, err: %s", base_utils.Format(req), err.Error())
	//}
	//
	//fmt.Printf("get bcc sg fail, req: %v, rsp: %v\n", base_utils.Format(req), base_utils.Format(rsp))
}
