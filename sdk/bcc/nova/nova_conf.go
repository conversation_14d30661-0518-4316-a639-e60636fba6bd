/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/24 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file nova_conf.go
 * <AUTHOR>
 * @date 2023/03/24 15:15:47
 * @brief nova conf

 *
 **/

package nova

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "Nova"

// novaConf definition
type novaConf struct {
	Product string `toml:"Product,omitempty"`
}

var novaConfMap = &sync.Map{}

func getConf(serviceName string) *novaConf {
	if conf, ok := novaConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*novaConf); ok {
			return conf
		}
	}

	conf := &novaConf{}
	conf.mustLoad(serviceName)

	novaConfMap.Store(serviceName, conf)

	return conf
}

func (conf *novaConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
