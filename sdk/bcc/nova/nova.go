package nova

import (
	"context"
	"fmt"
	"net/http"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

// bcc.NovaService 的 sdk实现
type novaSdk struct {
	conf *novaConf
	common.OpenApi
}

// NewDefaultNovaSdk - 创建默认的novaSdk
func NewDefaultNovaSdk() bcc.NovaService {
	return newNovaSdk(DefaultServiceName)
}

// NewNovaSdk - 创建指定serviceName的novaSdk
func NewNovaSdk(serviceName string) bcc.NovaService {
	return newNovaSdk(serviceName)
}

func newNovaSdk(serviceName string) *novaSdk {
	if serviceName == "" {
		panic("invalid service name")
	}

	s := &novaSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}
	return s
}

// doRequest - 通用nova服务请求方法
// @param ctx - context
// @param actionName - action name， 目前仅用于日志
// @param tenantId - 租户id
// @param uuid - vm uuid
// @param token - token: sts_sdk.GetAssumeRole().Token.Id
// @param httpMethod - http.MethodGet, http.MethodPost
// @param shortUri - 指的是{{novaUri}}/{{tenantId}}/servers/{{uuid}}/后面的短uri
// @param req - post内容
// @param rsp - *response
// @return error
func (s *novaSdk) doRequest(ctx context.Context, actionName string, tenantId, uuid, token string,
	httpMethod, shortUri string, req, rsp interface{}) (err error) {

	uri := fmt.Sprintf("%s/%s/servers/%s/%s", Version, tenantId, uuid, shortUri)

	params := &common.OpenApiParams{
		ActionName: actionName,
		Token:      token,
		HttpMethod: httpMethod,
		Uri:        uri,
		Posts:      req,
		Product:    s.conf.Product,
	}

	// 请求 openapi
	return cerrs.ErrBCCNovaFail.Wrap(s.DoRequest(ctx, params, rsp))
}

// StartVm - 启动虚机
func (s *novaSdk) StartVm(ctx context.Context, req *bcc.StartVmRequest) (err error) {
	return s.VmOperation(ctx, &bcc.OperateVmRequest{
		TenantId:  req.TenantId,
		Token:     req.Token,
		VmUuid:    req.VmUuid,
		Operation: VmOpStart,
	})
}

// StopVm - 停止虚机
func (s *novaSdk) StopVm(ctx context.Context, req *bcc.StopVmRequest) (err error) {
	return s.VmOperation(ctx, &bcc.OperateVmRequest{
		TenantId:  req.TenantId,
		Token:     req.Token,
		VmUuid:    req.VmUuid,
		Operation: VmOpStop,
	})
}

// VmOperation - 启停虚机
func (s *novaSdk) VmOperation(ctx context.Context, req *bcc.OperateVmRequest) (err error) {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.TenantId == "" {
		return cerrs.ErrInvalidParams.Errorf("invalid tenant id")
	}
	if req.Token == "" {
		return cerrs.ErrInvalidParams.Errorf("invalid token")
	}
	if req.VmUuid == "" {
		return cerrs.ErrInvalidParams.Errorf("invalid vm uuid")
	}
	if req.Operation != VmOpStart && req.Operation != VmOpStop {
		return cerrs.ErrInvalidParams.Errorf("invalid operation")
	}

	shortUri := "action"

	posts := map[string]interface{}{
		req.Operation: "null",
	}

	err = s.doRequest(ctx, "vm_operation", req.TenantId, req.VmUuid, req.Token,
		http.MethodPost, shortUri, posts, nil)
	return
}

// AddSgToBcc - bind instance to sec group
func (s *novaSdk) AddSgToBcc(ctx context.Context, req *bcc.OperateSecurityGroupRequest) (err error) {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.TenantId == "" {
		return cerrs.ErrInvalidParams.Errorf("invalid tenant id")
	}
	if req.Token == "" {
		return cerrs.ErrInvalidParams.Errorf("invalid token")
	}
	if req.VmUuid == "" {
		return cerrs.ErrInvalidParams.Errorf("invalid vm uuid")
	}
	if req.SecurityGroupId == "" {
		return cerrs.ErrInvalidParams.Errorf("invalid security group id")
	}

	shortUri := "action"

	posts := &bcc.RemoveSecurityGroupRequest{
		RemoveSecurityGroup: &bcc.Group{Name: req.SecurityGroupId},
	}

	err = s.doRequest(ctx, "bind_sg_to_instance", req.TenantId, req.VmUuid, req.Token,
		http.MethodPost, shortUri, posts, nil)
	return
}

// RemoveSgFromBcc - unbind instance from sec group
func (s *novaSdk) RemoveSgFromBcc(ctx context.Context, req *bcc.OperateSecurityGroupRequest) (err error) {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.TenantId == "" {
		return cerrs.ErrInvalidParams.Errorf("invalid tenant id")
	}
	if req.Token == "" {
		return cerrs.ErrInvalidParams.Errorf("invalid token")
	}
	if req.VmUuid == "" {
		return cerrs.ErrInvalidParams.Errorf("invalid vm uuid")
	}
	if req.SecurityGroupId == "" {
		return cerrs.ErrInvalidParams.Errorf("invalid security group id")
	}

	shortUri := "action"

	posts := &bcc.RemoveSecurityGroupRequest{
		RemoveSecurityGroup: &bcc.Group{Name: req.SecurityGroupId},
	}

	err = s.doRequest(ctx, "unbind_sg_from_instance", req.TenantId, req.VmUuid, req.Token,
		http.MethodPost, shortUri, posts, nil)
	return
}

// GetBccSg - get instance 's binding sec groups
func (s *novaSdk) GetBccSg(ctx context.Context, req *bcc.GetBccBindSgRequest) (rsp *bcc.GetBccBindSgResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.TenantId == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("invalid tenant id")
	}
	if req.Token == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("invalid token")
	}
	if req.VmUuid == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("invalid vm uuid")
	}

	shortUri := "os-security-groups"

	rsp = &bcc.GetBccBindSgResponse{}
	err = s.doRequest(ctx, "get_instance_bind_sg", req.TenantId, req.VmUuid, req.Token,
		http.MethodGet, shortUri, nil, rsp)
	return
}
