/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/08/01 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file neutron_conf.go
 * <AUTHOR>
 * @date 2023/08/01 15:15:47
 * @brief neutron conf

 *
 **/

package neutronV2

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "SecurityGroup"

// conf definition
type confModel struct {
	// encrypt resource account id
	ResourceID string `toml:"ResourceID,omitempty"`
	EncryptKey string `toml:"EncryptKey,omitempty"`
	Product    string `toml:"Product,omitempty"`
}

var confMap = &sync.Map{}

// getConf 函数从全局变量 confMap 中获取指定的服务名称对应的配置信息，并返回一个指针指向该配置信息的结构体。
func getConf(serviceName string) *confModel {
	if conf, ok := confMap.Load(serviceName); ok {
		if conf, ok := conf.(*confModel); ok {
			return conf
		}
	}

	conf := &confModel{}
	conf.mustLoad(serviceName)

	confMap.Store(serviceName, conf)

	return conf
}

// mustLoad 方法从指定的服务名加载RAL配置信息。
// 如果加载失败则会触发panic异常。
func (conf *confModel) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
