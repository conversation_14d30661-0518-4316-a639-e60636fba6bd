/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * author: wangbin34
 * Date: 2023-04-21
 * File: interface.go
 */

package neutronV2

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

// CommonNeutronResponse 通用返回
type CommonNeutronResponse struct {
	RequestID string `json:"requestId"`
	Message   string `json:"message"`
	Code      string `json:"code"`
}

type TagModel struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

// openapi securitygroup
type SecurityGroupRuleModel struct {
	SecurityGroupRuleID string `json:"securityGroupRuleId,omitempty"`
	SourceIP            string `json:"sourceIp,omitempty"`
	DestIP              string `json:"destIp,omitempty"`
	Protocol            string `json:"protocol,omitempty"`
	SourceGroupID       string `json:"sourceGroupId,omitempty"`
	Ethertype           string `json:"ethertype,omitempty"`
	PortRange           string `json:"portRange,omitempty"`
	DestGroupID         string `json:"destGroupId,omitempty"`
	SecurityGroupID     string `json:"securityGroupId,omitempty"`
	Remark              string `json:"remark,omitempty"`
	Direction           string `json:"direction"`
}

type SecurityGroupModel struct {
	ID    string                   `json:"id"`
	Name  string                   `json:"name"`
	Desc  string                   `json:"desc"`
	VpcID string                   `json:"vpcId"`
	Rules []SecurityGroupRuleModel `json:"rules"`
	Tags  []TagModel               `json:"tags"`
}

// Create
type CreateSecurityGroupRequest struct {
	ClientToken string                   `json:"-"`
	Name        string                   `json:"name"`
	Desc        string                   `json:"desc,omitempty"`
	VpcID       string                   `json:"vpcId,omitempty"`
	Rules       []SecurityGroupRuleModel `json:"rules"`
	Tags        []TagModel               `json:"tags,omitempty"`
	Auth        *common.Authentication   `json:"-"`
}

type CreateSecurityGroupResponse struct {
	SecurityGroupID string `json:"securityGroupId"`
	RequestID       string `json:"requestId"`
	Message         string `json:"message"`
	Code            string `json:"code"`
}

// Delete
type DeleteSecurityGroupRequest struct {
	SecurityGroupID string                 `json:"securityGroupId"`
	Auth            *common.Authentication `json:"-"`
}

// Show
type ShowSecurityGroupRequest struct {
	SecurityGroupID string                 `json:"securityGroupId"`
	Auth            *common.Authentication `json:"-"`
}

type ShowSecurityGroupResponse struct {
	Name            string                   `json:"name"`
	VpcID           string                   `json:"vpcId"`
	Rules           []SecurityGroupRuleModel `json:"rules"`
	Tags            []TagModel               `json:"tags,omitempty"`
	BindInstanceNum int                      `json:"bindInstanceNum"`
	SgVersion       int                      `json:"sgVersion"`
	ID              string                   `json:"id"`
	Desc            string                   `json:"desc"`
	RequestID       string                   `json:"requestId"`
	Message         string                   `json:"message"`
	Code            string                   `json:"code"`
}

// Create rule
type CreateSecurityGroupRulesRequest struct {
	ClientToken     string                  `json:"-"`
	SecurityGroupID string                  `json:"securityGroupId"`
	Rule            *SecurityGroupRuleModel `json:"rule"`
	Auth            *common.Authentication  `json:"-"`
}

// Delete rule
type DeleteSecurityGroupRulesRequest struct {
	SecurityGroupRulesID string
	Auth                 *common.Authentication `json:"-"`
}

type NeutronService interface {
	CreateSecurityGroup(ctx context.Context, req *CreateSecurityGroupRequest) (rsp *CreateSecurityGroupResponse, err error)
	ShowSecurityGroup(ctx context.Context, req *ShowSecurityGroupRequest) (rsp *ShowSecurityGroupResponse, err error)
	DeleteSecurityGroup(ctx context.Context, req *DeleteSecurityGroupRequest) (rsp *CommonNeutronResponse, err error)
	CreateSecurityGroupRules(ctx context.Context, req *CreateSecurityGroupRulesRequest) (rsp *CommonNeutronResponse, err error)
	DeleteSecurityGroupRules(ctx context.Context, req *DeleteSecurityGroupRulesRequest) (rsp *CommonNeutronResponse, err error)
}
