package neutronV2

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
)

// NeutronService 的sdk实现
type neutronSdk struct {
	conf *confModel
	common.OpenApi
	ralCaller sdk_utils.RalCaller
}

// NewDefaultNeutronSdk - 创建默认的neutronSdk
func NewDefaultNeutronSdk() NeutronService {
	return newNeutronSdk(DefaultSecurityGroupServiceName)
}

// NewNeutronSdk - 创建指定serviceName的neutronSdk
func NewNeutronSdk(serviceName string) NeutronService {
	return newNeutronSdk(serviceName)
}

// newNeutronSdk 函数用于创建一个 neutronSdk 实例
// 参数：
// - serviceName：服务名称，类型为 string
// 返回值：
// - *neutronSdk：返回一个 neutronSdk 实例指针
func newNeutronSdk(serviceName string) *neutronSdk {
	if serviceName == "" {
		panic("invalid service name")
	}

	s := &neutronSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
		ralCaller: sdk_utils.NewRalCaller(
			serviceName,
			sdk_utils.ROptGenCurlLog(logit.TraceLevel),
			sdk_utils.ROptPrepareChecker(
				func(ctx context.Context, httpRsp *sdk_utils.HttpResponse, invoker sdk_utils.PrepareCheckFunc) error {
					logger.SdkLogger.Debug(ctx, "http rsp: %v", httpRsp)
					return invoker(ctx, httpRsp)
				}),
		),
	}
	return s
}

// openapi
func (s *neutronSdk) doRequestWithResourceID(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {
	// 获取当前时间
	tmStr := time.Now().UTC().Format("2006-01-02T15:04:05Z")
	rawContent := fmt.Sprintf("%s/%s", s.conf.ResourceID, tmStr)

	encryptAccountID, err := crypto_utils.AesECBEncryptString(rawContent, s.conf.EncryptKey,
		crypto_utils.PKCS7PaddingType, crypto_utils.HexCodecType)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "aes ecb encrypt fail, content: %s, key: %s", rawContent, s.conf.EncryptKey)
		return cerrs.ErrSTSEncryptAccountFail.Wrap(err)
	}

	productResource := "scs"
	if s.conf.Product != "" {
		productResource = s.conf.Product
	}
	headers := map[string]interface{}{
		"resource-accountId": strings.ToUpper(encryptAccountID),
		"resource-source":    productResource,
	}

	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Headers:    headers,
		Product:    s.conf.Product,
	}

	_, err = s.DoRequestWithCustomizeHeader(ctx, params, rsp)
	return err
}

// openapi 判断 409
func (s *neutronSdk) doRequestWithResourceIDEx(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {
	// 获取当前时间
	tmStr := time.Now().UTC().Format("2006-01-02T15:04:05Z")
	rawContent := fmt.Sprintf("%s/%s", s.conf.ResourceID, tmStr)

	encryptAccountID, err := crypto_utils.AesECBEncryptString(rawContent, s.conf.EncryptKey,
		crypto_utils.PKCS7PaddingType, crypto_utils.HexCodecType)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "aes ecb encrypt fail, content: %s, key: %s", rawContent, s.conf.EncryptKey)
		return cerrs.ErrSTSEncryptAccountFail.Wrap(err)
	}

	productResource := "scs"
	if s.conf.Product != "" {
		productResource = s.conf.Product
	}
	headers := map[string]interface{}{
		"resource-accountId": strings.ToUpper(encryptAccountID),
		"resource-source":    productResource,
	}

	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Headers:    headers,
		Product:    s.conf.Product,
	}

	_, err = s.DoRequestWithCustomizeHeaderEx(ctx, params, rsp,
		sdk_utils.ROptFinalChecker(func(ctx context.Context, httpRsp *sdk_utils.HttpResponse, result interface{}, invoker sdk_utils.FinalCheckFunc) error {
			if httpRsp.StatusCode == 409 {
				return nil
			}
			return invoker(ctx, httpRsp, result)
		}))
	return err
}

// CreateSecurityGroup - CreateSecurityGroup implement
func (s *neutronSdk) CreateSecurityGroup(ctx context.Context, req *CreateSecurityGroupRequest) (rsp *CreateSecurityGroupResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &CreateSecurityGroupResponse{}
	if err = s.doRequestWithResourceID(ctx, "CreateSecurityGroup", req.Auth, http.MethodPost,
		securityGroupsUri, nil, req, rsp); err != nil {
		return nil, err
	}

	return
}

// ShowSecurityGroup - ShowSecurityGroup implement
func (s *neutronSdk) ShowSecurityGroup(ctx context.Context, req *ShowSecurityGroupRequest) (rsp *ShowSecurityGroupResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := securityGroupsUri + "/" + req.SecurityGroupID

	rsp = &ShowSecurityGroupResponse{}
	if err = s.doRequestWithResourceID(ctx, "ShowSecurityGroup", req.Auth, http.MethodGet,
		uri, nil, nil, rsp); err != nil {
		return nil, err
	}

	return
}

// DeleteSecurityGroup - DeleteSecurityGroup implement
func (s *neutronSdk) DeleteSecurityGroup(ctx context.Context, req *DeleteSecurityGroupRequest) (rsp *CommonNeutronResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := securityGroupsUri + "/" + req.SecurityGroupID
	rsp = &CommonNeutronResponse{}

	if err = s.doRequestWithResourceID(ctx, "DeleteSecurityGroup", req.Auth, http.MethodDelete,
		uri, nil, nil, rsp); err != nil {
		return nil, err
	}

	return
}

// CreateSecurityGroupRules - CreateSecurityGroupRules implement
func (s *neutronSdk) CreateSecurityGroupRules(ctx context.Context, req *CreateSecurityGroupRulesRequest) (rsp *CommonNeutronResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := securityGroupsUri + "/" + req.SecurityGroupID
	rsp = &CommonNeutronResponse{}
	queries := map[string]interface{}{
		"authorizeRule": nil,
		// component 调用时会使用相同 auth，故去掉 clientToken
		//"clientToken":   req.Auth.TransactionId,
	}
	if err = s.doRequestWithResourceIDEx(ctx, "CreateSecurityGroupRules", req.Auth, http.MethodPut,
		uri, queries, req, rsp); err != nil {
		return nil, err
	}

	// 当 Code 不为空时，则 HTTP Code 不为 200, 正常情况下不会走到以下逻辑
	// rsp: {"requestId":"xx","message":"Security group rule is duplicated.","code":"SecurityGroup.RuleDuplicated"}
	if rsp.Code != "" {
		if rsp.Code == "SecurityGroup.RuleDuplicated" || rsp.Code == "SecurityGroupRuleExists" {
			return
		}
		logger.SdkLogger.Warning(ctx, "CreateSecurityGroupRules request fail, req:%s, code: %s, message: %s, requestId: %s",
			base_utils.Format(req), rsp.Code, rsp.Message, rsp.RequestID)
		return nil, cerrs.ErrBCCNeutronFail.Errorf("CreateSecurityGroupRules request fail, Code: %s , Message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// DeleteSecurityGroupRules - DeleteSecurityGroupRules implement
func (s *neutronSdk) DeleteSecurityGroupRules(ctx context.Context, req *DeleteSecurityGroupRulesRequest) (rsp *CommonNeutronResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := securityGroupRulesUri + "/" + req.SecurityGroupRulesID
	rsp = &CommonNeutronResponse{}
	if err = s.doRequestWithResourceID(ctx, "DeleteSecurityGroupRules", req.Auth, http.MethodDelete,
		uri, nil, nil, rsp); err != nil {
		return nil, err
	}
	return
}
