package neutronV2

import (
	"context"
	"net/http"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

// bcc.NeutronV2Service 的sdk实现
type neutronV2Sdk struct {
	conf *confModel
	common.OpenApi
}

// NewDefaultNeutronV2Sdk
func NewDefaultNeutronV2Sdk() bcc.NeutronV2Service {
	return newNeutronV2Sdk(DefaultServiceName)
}

// NewNeutronV2Sdk
func NewNeutronV2Sdk(serviceName string) bcc.NeutronV2Service {
	return newNeutronV2Sdk(serviceName)
}

func newNeutronV2Sdk(serviceName string) *neutronV2Sdk {
	if serviceName == "" {
		panic("invalid service name")
	}

	s := &neutronV2Sdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}
	return s
}

// doRequest - 通用openstack服务请求方法
// auth 必要字段:
//
//	  IamUserId
//		 TransactionId
//		 Credential
//	  ResourceAccount
func (s *neutronV2Sdk) doNeutronV2Request(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {
	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}
	// 请求 openapi
	return s.DoRequest(ctx, params, rsp)
}

// 获取子网详情  获取类似   zoneA <===> cn-bj-a 映射信息
func (s *neutronV2Sdk) GetSubnetDetail(ctx context.Context, req *bcc.GetSubnetDetailRequest) (rsp *bcc.GetSubnetDetailResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.SubnetID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("null subnetId")
	}

	uri := getSubnetDetailUri + "/" + req.SubnetID

	rsp = &bcc.GetSubnetDetailResponse{}
	if err = s.doNeutronV2Request(ctx, "get_subnet_detail", req.Auth, http.MethodGet,
		uri, nil, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "get subnet detail request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("get subnet detail request fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}
