package neutronV2

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const (
	TestIamUserID              = "ea2c4a2286ca4540afcb7f7d4ba2d199"
	TestVpcID                  = "58d53a2e-27fe-4ead-9041-c72ec67ec1c1"
	TestVpcIDInvalid           = "58d53a2e-27fe-4ead-9041-c72ec67ec1c1x"
	TestSecurityGroupID        = "g-fq4v7gc8jqes"
	TestSecurityGroupIDInvalid = "g-fq4v7gc8jqes_xx"
	TestSourceIP               = "*************"
)

// CreateSecurityGroupOK 测试函数
func TestCreateSecurityGroupOK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     TestIamUserID,
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	auth := authRsp.Auth
	s := NewDefaultNeutronSdk()

	// create
	createReq := CreateSecurityGroupRequest{
		Name:  TestIamUserID,
		VpcID: TestVpcID,
		Desc:  "test",
		Auth:  auth,
	}
	createRsp, err := s.CreateSecurityGroup(ctx, &createReq)
	if err != nil {
		fmt.Println("------------------------")
		fmt.Println(err)
		return
	}
	fmt.Println(base_utils.Format(createRsp))

	deleteReq := DeleteSecurityGroupRequest{
		SecurityGroupID: createRsp.SecurityGroupID,
		Auth:            auth,
	}
	deleteRsp, err := s.DeleteSecurityGroup(ctx, &deleteReq)
	if err != nil {
		fmt.Println("------------------------")
		fmt.Println(err)
		return
	}
	fmt.Println(base_utils.Format(deleteRsp))
}

// TestCreateSecurityGroupERR is a unit test function for testing CreateSecurityGroup api error scenarios
func TestCreateSecurityGroupERR(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     TestIamUserID,
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	auth := authRsp.Auth
	// use NewNeutronSdk
	s := NewNeutronSdk(DefaultSecurityGroupServiceName)

	// req is nil
	_, err = s.CreateSecurityGroup(ctx, nil)
	if err.Error() != "req param is null" {
		fmt.Println("------------------------")
		fmt.Println(err)
		return
	}
	fmt.Println("check req success")

	// create vpc invalid
	createReq := CreateSecurityGroupRequest{
		Name:  TestIamUserID,
		VpcID: TestVpcIDInvalid,
		Desc:  "test",
		Auth:  auth,
	}
	createRsp, err := s.CreateSecurityGroup(ctx, &createReq)
	if err != nil {
		fmt.Println("------------------------")
		fmt.Println(err)
		return
	}
	fmt.Println(base_utils.Format(createRsp))
}

// TestShowSecurityGroup
func TestShowSecurityGroupOK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     TestIamUserID,
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	auth := authRsp.Auth
	s := NewDefaultNeutronSdk()

	showReq := ShowSecurityGroupRequest{
		SecurityGroupID: TestSecurityGroupID,
		Auth:            auth,
	}
	showRsp, err := s.ShowSecurityGroup(ctx, &showReq)
	if err != nil {
		fmt.Println("------------------------")
		fmt.Println(err)
		return
	}
	fmt.Println(base_utils.Format(showRsp))

}

// TestDeleteSecurityGroupERR 函数是一个测试函数
func TestDeleteSecurityGroupERR(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     TestIamUserID,
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	auth := authRsp.Auth
	s := NewDefaultNeutronSdk()

	// req is nil
	_, err = s.DeleteSecurityGroup(ctx, nil)
	if err.Error() != "req param is null" {
		fmt.Println("------------------------")
		fmt.Println(err)
		return
	}
	fmt.Println("check req success")

	deleteReq := DeleteSecurityGroupRequest{
		SecurityGroupID: TestSecurityGroupIDInvalid,
		Auth:            auth,
	}
	deleteRsp, err := s.DeleteSecurityGroup(ctx, &deleteReq)
	if err != nil {
		fmt.Println("------------------------")
		fmt.Println(err)
		return
	}
	fmt.Println(base_utils.Format(deleteRsp))
}

// 测试函数
func TestCreateSecurityGroupRulesOK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     TestIamUserID,
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	auth := authRsp.Auth
	s := NewDefaultNeutronSdk()

	// create
	createReq := CreateSecurityGroupRulesRequest{
		SecurityGroupID: TestSecurityGroupID,
		Rule: &SecurityGroupRuleModel{
			SourceIP:        TestSourceIP,
			Protocol:        "tcp",
			Ethertype:       "IPv4",
			PortRange:       "6379",
			SecurityGroupID: TestSecurityGroupID,
			Direction:       "ingress",
		},
		Auth: auth,
	}
	createRsp, err := s.CreateSecurityGroupRules(ctx, &createReq)
	if err != nil {
		fmt.Println("------------------------err")
		fmt.Println(err)
		return
	}
	fmt.Println(base_utils.Format(createRsp))

	// delete
	deleteReq := DeleteSecurityGroupRulesRequest{
		SecurityGroupRulesID: "r-uvgi6ut9mp92",
		Auth:                 auth,
	}
	deleteRsp, err := s.DeleteSecurityGroupRules(ctx, &deleteReq)
	if err != nil {
		fmt.Println("------------------------")
		fmt.Println(err)
		return
	}
	fmt.Println(base_utils.Format(deleteRsp))
}

// 测试函数, 重复 rule, 返回创建成功
func TestCreateSecurityGroupRulesERR(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     TestIamUserID,
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	auth := authRsp.Auth
	s := NewDefaultNeutronSdk()

	// req is nil
	_, err = s.CreateSecurityGroupRules(ctx, nil)
	if err.Error() != "req param is null" {
		fmt.Println("------------------------")
		fmt.Println(err)
		return
	}
	fmt.Println("check req success")

	// create
	createReq := CreateSecurityGroupRulesRequest{
		SecurityGroupID: TestSecurityGroupID,
		Rule: &SecurityGroupRuleModel{
			SourceIP:        TestSourceIP,
			Protocol:        "tcp",
			Ethertype:       "IPv4",
			PortRange:       "8000",
			SecurityGroupID: TestSecurityGroupID,
			Direction:       "ingress",
		},
		Auth: auth,
	}

	// rsp: {"requestId":"xx","message":"Security group rule is duplicated.","code":"SecurityGroup.RuleDuplicated"}
	_, err = s.CreateSecurityGroupRules(ctx, &createReq)
	if err != nil {
		fmt.Println("------------------------err")
		fmt.Println(err)
		return
	}
	fmt.Println(base_utils.Format("success"))
}

// 测试删除 rule ERR
func TestDeleteSecurityGroupRulesERR(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := NewDefaultNeutronSdk()

	_, err := s.DeleteSecurityGroupRules(ctx, nil)
	if err.Error() != "req param is null" {
		fmt.Println("------------------------")
		fmt.Println(err)
		return
	}
	fmt.Println("check req success")
}
