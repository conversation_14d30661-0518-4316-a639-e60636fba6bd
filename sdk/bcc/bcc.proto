// protofsg -with_context -json_tag=1 bcc.proto interface.go
option cc_generic_services = true;
option go_package = "./;bcc";
package x1-base.sdk.bcc;

import "x1-base/sdk/common/common.proto";
import "google/protobuf/descriptor.proto";

extend google.protobuf.FileOptions {
	optional string ExchangeIDTypeLong = 1;
	optional string ExchangeIDTypeShort = 2;
}
option (ExchangeIDTypeLong)     = "long";
option (ExchangeIDTypeShort)    = "short";

extend google.protobuf.FileOptions {
	optional string ExchangeIDObjectTypeVM = 1;
	optional string ExchangeIDObjectTypeSubnet = 2;
	optional string ExchangeIDObjectTypeImage = 3;
	optional string ExchangeIDObjectTypeVpc = 4;
	optional string ExchangeIDObjectTypeSecurityGroup = 5;
}
option (ExchangeIDObjectTypeVM)             = "vm";
option (ExchangeIDObjectTypeSubnet)         = "subnet";
option (ExchangeIDObjectTypeImage)          = "image";
option (ExchangeIDObjectTypeVpc)            = "vpc";
option (ExchangeIDObjectTypeSecurityGroup)  = "security_group";

message Networks {
    optional string key = 1;
}

message Metadata {
    optional string instance_type = 1;
    optional string flavor_type = 2;
    optional string image_type = 3;
    optional string reserve = 4;
    optional string master_endpoint = 5;
    optional string env_type = 6;
    optional string replace_uuid = 7;
    optional string deploy_set_id = 8;
    optional string disk_type = 9;
    optional string use_local_disk = 10;
}


message Personality {
    optional string path = 1;
    optional string contents = 2;
}

message Flavor {
    optional int32 cpu = 1;
    optional int32 memory = 2;
    optional int32 disk = 3;
    optional int32 ephemeral = 4;
}

message Group {
    optional string name = 1;
}

message Scheduler {
    repeated string different_host = 1;
    repeated string scheduler_tags = 2;
}

message Server {
    optional Flavor flavor = 1;
    optional string imageRef = 2;
    optional string name = 3;
    optional Metadata metadata = 4;
    optional bool different_host = 5;
    repeated Group security_groups = 6; 
    optional Scheduler osscheduler_hints = 7;
    optional int32 count = 8;
    optional bool create_floatingip = 9;
    optional string subnet_id = 10;
    repeated string deploy_id_list = 11;
}

message CallbackServerInfo {
    optional string fixip = 1;
    optional string floatingip = 2;
    optional string id = 3;
    optional string name = 4;
    optional string adminPass = 5;
    optional Metadata metadata = 6;
}

message BatchServerResponse {
    optional string requestid = 1;
    optional bool succ = 2;
    optional string errmsg = 3;
    repeated CallbackServerInfo servers = 4;
}

message SecurityGroupReq {
    optional string name = 1;
    optional string description = 2;
    optional string creator = 3;
    optional string vpc_id = 4;
}

message CreateSecurityGroupRequest {
    optional SecurityGroupReq security_group = 1;
    //@inject_tag json:"-"
    optional string token = 2;
}

message SecurityGroupRules {
    optional string direction = 1;
    optional string ethertype = 2;
    //@inject_tag json:",omitempty"
    optional string id = 3;
    //@inject_tag json:",omitempty"
    optional int32 port_range_max = 4;
    //@inject_tag json:",omitempty"
    optional int32 port_range_min = 5;
    optional string protocol = 6;
    //@inject_tag json:",omitempty"
    optional string remote_group_id = 7;
    //@inject_tag json:",omitempty"
    optional string remote_ip_prefix = 8;
    optional string security_group_id = 9;
    //@inject_tag json:",omitempty"
    optional string tenant_id = 10;
    optional string creator = 11;
}

message SecurityGroup {
    optional string description = 1;
    optional string id = 2;
    optional string name = 3;
    repeated SecurityGroupRules security_group_rules = 4;
    optional string tenant_id = 5;
}

message NeutronError {
	optional string message = 1;
	optional string type = 2;
	optional string detail = 3;
}

message CreateSecurityGroupResponse {
    optional SecurityGroup security_group = 1;
    optional NeutronError NeutronError = 2;
}

message NullMessage {
}

message ShowSecurityGroupRequest {
    //@inject_tag json:"-"
    optional string token = 1;
    //@inject_tag json:"-"
    optional string security_group_id = 2;
}

message ShowSecurityGroupResponse {
    optional SecurityGroup security_group = 1;
    optional NeutronError NeutronError = 2;
}

message CreateSecurityGroupRulesRequest {
    optional SecurityGroupRules security_group_rule = 1;
    //@inject_tag json:"-"
    optional string token = 2;
}

message CreateSecurityGroupRulesResponse {
    optional SecurityGroupRules security_group_rule = 1;
    optional NeutronError NeutronError = 2;
}

message DeleteSecurityGroupRequest {
    //@inject_tag json:"-"
    optional string security_group_id = 1;
    //@inject_tag json:"-"
    optional string token = 2;
}

message DeleteSecurityGroupResponse {
    optional string security_group_id = 1;
    optional NeutronError NeutronError = 2;
}

message DeleteSecurityGroupRulesRequest {
    //@inject_tag json:"-"
    optional string security_group_rules_id = 1;
    //@inject_tag json:"-"
    optional string token = 2;
}

message DeleteSecurityGroupRulesResponse {
    optional string security_group_rules_id = 1;
    optional NeutronError NeutronError = 2;
}

message IpFormat {
    optional string addr = 1;
    optional int32 version = 2;
    optional string addr_v6 = 3;
}

message IpAddress {
    optional IpFormat fixed = 1;
    optional IpFormat floating = 2;
}

message VmServer {
    optional IpAddress addresses = 1;
    optional string created = 2;
    optional string id = 3;
    optional string name = 4;
    optional string status = 5;
    optional string tenant_id = 6;
    optional string updated = 7;
    optional string user_id = 8;
} 

message ShowServerResponse {
    optional VmServer server = 1;
}

message ShowServerRequest {
    optional string vm_uuid = 1;
    optional string token = 2;
}

message Network {
    optional string status = 1;
    repeated string subnets = 2;
    optional string name = 3;
    optional string id = 4;
}

message ListNetworkRequest {
   //@inject_tag json:"-"
   optional string token = 1;
}

message ListNetworkResponse {
    repeated Network vnetworks = 1;
    optional NeutronError NeutronError = 2;
}

message ShowServer {
    optional string fixip = 1;
    optional string floatingip = 2;// master_redis 
    optional int32 status = 3;
    optional string id = 4; //uuid
    optional string name = 5;//hostname
    optional string adminPass = 6;
    optional Metadata metadata = 7;
    optional string hash_name = 8;
    optional string fixip_v6 = 9;
}
message ShowVolume {
    optional string id = 1;
    optional int32 size = 2;
}
message ShowTransactionResponse {
    optional string transaction_id = 1;
    optional string status = 2;
    optional string action = 3;
    repeated ShowServer servers = 4;
    optional string errmsg = 5;
    repeated ShowVolume volumes = 6;
}

message ShowTransactionRequest {
    optional string transaction_id = 1;
    optional string token = 2;
}

message SetTransactionRequest {
    optional string status = 1;
    //@inject_tag json:"-"
    optional string transaction_id = 2;
    //@inject_tag json:"-"
    optional string token = 3;
}

message SetTransactionResponse {
    optional int32 status = 1;
}

message VolumeAttachment {
    optional string device = 1;
    optional string id = 2;
    optional string serverId = 3;
    optional string volumeId = 4;
}
message VolumeAttachRequest {
    optional VolumeAttachment volumeAttachment = 1;
}
message VolumeAttachResponse {
    optional VolumeAttachment volumeAttachment = 1;
}
message Volumes {
    optional int32 count = 1;
    optional string display_name = 2;
    optional string display_description = 3;
    optional string snapshot_id = 4;
    optional int32 size = 5; //GB
}
message VolumeCreateRequest {
    optional string source = 1;
    optional string action = 2;
    optional string transaction_id = 3;
    optional string callback = 4;
    repeated Volumes volumes = 5;
}
message VolumeCreateResponse {

}
message VolumeDeleteRequest {
    optional string action = 1;
    repeated string volumes = 2;
}
message VolumeDeleteResponse {

}



message FixedIp {
    optional string subnet_id = 1;
    optional string ip_address = 2;
} 
message Port {
    optional string status = 1;
    //@inject_tag json:"binding:host_id"
    optional string bindinghost_id = 2;
    optional string name = 3;
    repeated string allowed_adddress_pairs = 4;
    optional bool   admin_state_up = 5;
    optional string network_id = 6;
    optional string tenant_id = 7;
    repeated string extra_dhcp_opts = 8;
    optional string bindingvnic_type = 9;
    optional string device_owner = 10;
    optional bool   mac_anti_spoofing = 11;
    optional string mac_address = 12;
    repeated FixedIp fixed_ips = 13;
    optional string id = 14;
    repeated string security_groups = 15;
    optional string device_id = 16;
}

message GetDnsMasqRequest {
    //@inject_tag json:"-"
    optional string vpc_id = 1;
    //@inject_tag json:"-"
    optional string token = 2;
}

message GetPortsRequest {
    //@inject_tag json:"-"
    optional string vm_uuid = 1;
    //@inject_tag json:"-"
    optional string token = 2;
}

message PortsResponse {
    repeated Port ports = 1;
    optional NeutronError NeutronError = 2;
}

message ExtraDnsOpt {
    optional string opt_value = 1;
    optional string opt_name = 2;
}
message Vpc {
    repeated ExtraDnsOpt extra_dns_opts = 1;
    optional string status = 2;
    optional string description = 3;
    optional string vpc_name = 4;
    optional string is_default_vpc = 5;
    optional string create_time = 6;
    optional string vpc_cidr = 7;
    optional string tenant_id = 8;
    optional string id = 9;
}

message SetDnsMasqRequest {
    optional Vpc vpc = 1;
    //@inject_tag json:"-"
    optional string vpc_id = 2;
    //@inject_tag json:"-"
    optional string token = 3;
}

message SetDnsMasqResponse {
    optional Vpc vpc = 1;
    optional NeutronError NeutronError = 2;
}

message UnbindSecurityGroupRequest {
    //@inject_tag json:"-"
    optional string port_id = 1;
    //@inject_tag json:"-"
    optional string token = 2;
}

message GetSubnetInfoRequest {
    optional string subnet_id = 1;
    //@inject_tag json:"-"
    optional string token = 2;
}

message SubnetAllocationPools {
    optional string start = 1;
    optional string end = 2;
}

message SubnetInfo {
    optional string subnet_type = 1;
    optional string subnet_region = 2;
    optional string description = 3;
    optional string name = 4;
    optional string network_id = 5;
    optional string az_zone = 6;
    optional Interface dns_nameservers = 7;
    optional string flat_host_cidr = 8;
    optional SubnetAllocationPools allocpool = 9;
    optional string create_time = 10;
    optional Interface host_routes = 11;
    optional string vpc_id = 12;
    optional string tenant_id = 13;
    optional int ip_version = 14;
    optional string gateway_ip = 15;
    optional string cidr = 16;
    optional bool enable_dhcp = 17;
    optional string id = 18;
    optional SubnetAllocationPools dallocpool = 19;
}

message GetSubnetInfoResponse {
    optional SubnetInfo subnet = 1;
    optional NeutronError NeutronError = 2;
}

message GetSubnetIpIdRequest {
    optional string subnet_id = 1;
    //@inject_tag json:"-"
    optional string token = 2;
}

message SubnetIpId {
    optional string id = 1;
}

message GetSubnetIpIdResponse {
    repeated SubnetIpId ports = 1;
    optional NeutronError NeutronError = 2;
}

message OperateVmRequest {
    optional string tenant_id = 1;
    optional string token = 2;
    optional string vm_uuid = 3;
    optional string operation = 4;
}

message StartVmRequest {
    optional string tenant_id = 1;
    optional string token = 2;
    optional string vm_uuid = 3;
}

message StopVmRequest {
    optional string tenant_id = 1;
    optional string token = 2;
    optional string vm_uuid = 3;
}

// http post body
message VmOperationRequest {
    optional string osstop = 1;
    optional string osstart = 2;
}

message OperateSecurityGroupRequest {
    optional string tenant_id = 1;
    optional string token = 2;
    optional string vm_uuid = 3;
    optional string security_group_id = 4;
}

// http post body
message AddSecurityGroupRequest {
    optional Group addSecurityGroup = 1;
}

// http post body
message RemoveSecurityGroupRequest {
    optional Group removeSecurityGroup = 1;
}

message GetBccBindSgRequest {
    optional string tenant_id = 1;
    optional string token = 2;
    optional string vm_uuid = 3;
}

message BccBindSecurityGroup {
    optional string description = 1;
    optional string id = 2;
    optional string name = 3;
    optional string tenant_id = 4;
}

message GetBccBindSgResponse {
    repeated BccBindSecurityGroup security_groups = 1;
}

message CreateCdsList {
    optional string storageType = 1;
    optional int32 cdsSizeInGB = 2;
}

message Billing {
    optional string paymentTiming = 1;
}

message Tags {
    optional string tagKey = 1;
    optional string tagValue = 2;
}

message Configs {
    optional string key = 1;
    optional string configValue = 2;
}

message EphemeralDisks {
    optional string storageType = 1;
    optional int32 sizeInGB = 2;
}

message CreateInstances {
    optional string instanceType = 1;
    optional int32 cpuCount = 2;
    optional string zoneName = 3;
    optional int32 memoryCapacityInGB = 4;
    optional int32 rootDiskSizeInGb = 5;
    optional string rootDiskStorageType = 6;
    //@inject_tag json:",omitempty"
    repeated CreateCdsList createCdsList = 7;
    //@inject_tag json:",omitempty"
    repeated EphemeralDisks ephemeralDisks = 8;
    optional string name = 9;
    //image short id
    optional string imageId = 10;
    optional int32 purchaseCount = 11;
    //subnet short id
    optional string subnetId = 12;
    //sg short id
    optional string securityGroupId = 13;
    optional Billing billing = 14;
    optional int32 relationTag = 15;
    optional int32 rootOnLocal = 16;
    repeated Tags tags = 17;
    optional string deploySetId = 18;
    optional string userData = 19;
    //metadata
    repeated Configs configs = 20;
    //@inject_tag json:",omitempty"
    repeated string deployIdList = 21;
}

message BatchCreateServersRequest {
    optional int32 orderOverTime = 1;
    repeated CreateInstances createInstances = 2;

    // @inject_tag json:"-"
    optional common.Authentication auth = 3;
}

message BatchCreateServersResponse {
    optional string message = 1;
    optional string code = 2;
    optional string requestid = 3;
    optional string orderId = 4;
    repeated string instanceIds = 5;
}

message BatchDeleteServersRequest {
    repeated string instanceIds = 1;
    optional int32 relatedRelease = 2;

    // @inject_tag json:"-"
    optional common.Authentication auth = 3;
}

message BatchDeleteServersResponse {
    optional string message = 1;
    optional string code = 2;
    optional string requestid = 3;
    optional int32 result = 4;
}

message ExchangeIdRequest {
    optional string idType = 1;
    optional string objectType = 2;
    repeated string instanceIds = 3;

    // @inject_tag json:"-"
    optional common.Authentication auth = 4;
}

message Mappings {
    optional string id = 1;
    optional string uuid = 2;
}

message ExchangeIdResponse {
    optional string message = 1;
    optional string code = 2;
    optional string requestid = 3;
    repeated Mappings mappings = 4;
}

message SysVolume {
    optional string status = 1;
    optional int32 bootIndex = 2;
    optional string volumeId = 3;
    optional string name = 4;
    optional string deviceId = 5;
    optional string type = 6;
    optional string createTime = 7;
    optional int32 sizeInGB = 8;
}

message CdsVolume {
    optional string status = 1;
    optional int32 bootIndex = 2;
    optional string volumeId = 3;
    optional string name = 4;
    optional string deviceId = 5;
    optional string type = 6;
    optional string createTime = 7;
    optional int32 sizeInGB = 8;
}

message ShowOrderRequest {
    // @inject_tag json:"-"
    optional string order_id = 1;
    // @inject_tag json:"-"
    optional common.Authentication auth = 2;
}

message Instances {
    optional string status = 1;
    optional string name = 2;
    optional string adminPass = 3;
    //instance short id
    optional string instanceId = 4;
    optional string floatingIp = 5;
    repeated SysVolume sysVolume = 6;
    optional string internalIp = 7;
    repeated CdsVolume cdsVolume = 8;
    optional string flavor = 9;
    optional string createTime = 10;
    optional string metaData = 11;
    optional string ipv6 = 12;
}

message ShowOrderResponse {
    optional string message = 1;
    optional string code = 2;
    optional string requestid = 3;
    optional string orderId = 4;
    optional string status = 5;
    optional string errMsg = 6;
    optional string action = 7;
    optional string zoneName = 8;
    repeated Instances instances = 9;
}

message InstanceVolume {
    optional int32 diskSizeInGB = 1;
    optional string volumeId = 2;
    optional int32 isSystemVolume = 3;
}

message ShowInstanceRequest {
    // @inject_tag json:"-"
    optional string vm_uuid = 1;
    // @inject_tag json:"-"
    optional common.Authentication auth = 2;
}

message InstanceInfo {
    optional string vpcId = 1;
    optional string imageId = 2;
    optional string subnetId = 3;
    optional string id = 4;
    optional int32 networkCapacityInMbps = 5;
    optional string dedicatedHostId = 6;
    optional int32 localDiskSizeInGB = 7;
    optional string instanceType = 8;
    optional string ipv6 = 9;
    optional string desc = 10;
    optional string status = 11;
    optional string placementPolicy = 12;
    optional string paymentTiming = 13;
    optional string publicIp = 14;
    optional string internalIp = 15;
    optional string createTime = 16;
    optional string zoneName = 17;
    optional string name = 18;
    optional int32 memoryCapacityInGB = 19;
    optional int32 cpuCount = 20;
    repeated InstanceVolume volumes = 21;
    //repeated Tags tags = 22;
}

message ShowInstanceResponse {
    optional string message = 1;
    optional string code = 2;
    optional string requestid = 3;
    optional InstanceInfo instance = 4;
}

message ResizeInstanceRequest {
    optional int32 cpuCount = 1;
    optional int32 memoryCapacityInGB = 2;
    optional bool liveResize = 3;

    // @inject_tag json:"-"
    optional string vm_id = 4;
    // @inject_tag json:"-"
    optional common.Authentication auth = 5;
}

message ResizeInstanceResponse {
    optional string message = 1;
    optional string code = 2;
    optional string requestid = 3;
}

message CdsMountListRequest {
    // @inject_tag json:"-"
    optional string vm_id = 1;
    // @inject_tag json:"-"
    optional common.Authentication auth = 2;
}

message CdsVolumeAttachmentInfo {
    optional string volumeId = 1;
    optional string instanceId = 2;
    optional string device = 3;
    optional string serial = 4;
}

message CdsVolumeInfo {
    optional string id = 1;
    optional string createTime = 2;
    optional string expireTime = 3;
    optional string name = 4;
    optional int32 diskSizeInGB = 5;
    optional string status = 6;
    optional string type = 7;
    optional string storageType = 8;
    optional string desc = 9;
    optional string paymentTiming = 10;
    repeated CdsVolumeAttachmentInfo attachments = 11;
    optional string zoneName = 12;
}

message CdsMountListResponse {
    optional string message = 1;
    optional string code = 2;
    optional string requestid = 3;
    optional string nextMarker = 4;
    optional string marker = 5;
    optional int32 maxKeys = 6;
    repeated CdsVolumeInfo volumes = 7;
}

message CdsResizeRequest {
    optional int32 newCdsSizeInGB = 1;
    optional string newVolumeType = 2;

    // @inject_tag json:"-"
    optional string volume_id = 3;
    // @inject_tag json:"-"
    optional common.Authentication auth = 4;
}

message CdsResizeResponse {
    optional string message = 1;
    optional string code = 2;
    optional string requestid = 3;
}

message CreateDeploySetRequest {
    optional string name = 1;
    optional string desc = 2;
    optional string strategy = 3;

    // @inject_tag json:"-"
    optional common.Authentication auth = 4;
}

message DeploySetIdsResponse {
    optional string message = 1;
    optional string code = 2;
    optional string requestid = 3;
    repeated string deploySetIds = 4;
}

message GetSubnetIpTypeRequest {
    optional string subnet_id = 1;
    //@inject_tag json:"-"
    optional string token = 2;
    //@inject_tag json:"-"
    optional string transaction_id = 3;
}

message GetSubnetIpTypeResponse {
    optional Subnet subnet = 1;
    optional NeutronError NeutronError = 2;
}

message Subnet {
    optional string subnet_type = 1;
    optional string subnet_region = 2;
    optional string description = 3;
    optional string name = 4;
    optional bool enable_ipv6 = 5;
    optional string az_zone = 6;
    optional string flat_host_cidr = 7;
    optional string network_id = 8;
    optional string create_time = 9;
    optional string vpc_id = 10;
    optional string tenant_id = 11;
    optional int64 ip_version = 12;
    optional string gateway_ip = 13;
    optional string cidr = 14;
    optional bool enable_dhcp = 15;
    optional string id = 16;
}

service OpenStackService {
    rpc ShowServer(ShowServerRequest) returns (ShowServerResponse);
    rpc show_transaction(ShowTransactionRequest) returns (ShowTransactionResponse);
    rpc set_show_transaction_for_ut(SetTransactionRequest) returns (SetTransactionResponse);
    rpc exchange_id(ExchangeIdRequest) returns (ExchangeIdResponse);
    rpc batch_create_servers(BatchCreateServersRequest) returns (BatchCreateServersResponse);
    rpc batch_delete_servers(BatchDeleteServersRequest) returns (BatchDeleteServersResponse);
    rpc show_order(ShowOrderRequest) returns (ShowOrderResponse);
    rpc show_instance_info(ShowInstanceRequest) returns (ShowInstanceResponse);
    rpc resize_instance(ResizeInstanceRequest) returns (ResizeInstanceResponse);
    rpc instance_attached_cds_list(CdsMountListRequest) returns (CdsMountListResponse);
    rpc resize_cds(CdsResizeRequest) returns (CdsResizeResponse);
    rpc create_deploy_set(CreateDeploySetRequest) returns (DeploySetIdsResponse);
};

service NeutronService {
    rpc CreateSecurityGroup(CreateSecurityGroupRequest) returns (CreateSecurityGroupResponse);
    rpc ShowSecurityGroup(ShowSecurityGroupRequest) returns (ShowSecurityGroupResponse);
    rpc DeleteSecurityGroup(DeleteSecurityGroupRequest) returns (DeleteSecurityGroupResponse);
    rpc CreateSecurityGroupRules(CreateSecurityGroupRulesRequest) returns (CreateSecurityGroupRulesResponse);
    rpc DeleteSecurityGroupRules(DeleteSecurityGroupRulesRequest) returns (DeleteSecurityGroupRulesResponse);
    rpc ListNetwork(ListNetworkRequest) returns (ListNetworkResponse);
    //rpc volume_attach(VolumeAttachRequest) returns (VolumeAttachResponse);
    //rpc volume_create(VolumeCreateRequest) returns (VolumeCreateResponse);
    //rpc volume_delete(VolumeDeleteRequest) returns (VolumeDeleteResponse);
    rpc get_dnsmasq(GetDnsMasqRequest) returns (PortsResponse);
    rpc set_dnsmasq(SetDnsMasqRequest) returns (SetDnsMasqResponse);
    rpc get_ports(GetPortsRequest) returns (PortsResponse);
    rpc unbind_security_group(UnbindSecurityGroupRequest) returns (PortsResponse);
    rpc GetSubnetInfo(GetSubnetInfoRequest) returns (GetSubnetInfoResponse);
    rpc GetSubnetIpId(GetSubnetIpIdRequest) returns (GetSubnetIpIdResponse);
};

service NeutronVpcService {
    rpc get_subnet_ip_type(GetSubnetIpTypeRequest) returns (GetSubnetIpTypeResponse);
}

service NovaService {
    rpc start_vm(StartVmRequest) returns (NullMessage);
    rpc stop_vm(StopVmRequest) returns (NullMessage);
    rpc vm_operation(OperateVmRequest) returns (NullMessage);
    rpc remove_sg_from_bcc(OperateSecurityGroupRequest) returns (NullMessage);
    rpc add_sg_to_bcc(OperateSecurityGroupRequest) returns (NullMessage);
    rpc get_bcc_sg(GetBccBindSgRequest) returns(GetBccBindSgResponse);
};
