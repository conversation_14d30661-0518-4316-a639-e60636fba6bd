/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-02-21
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据bcc.proto生成的interface文件
 */

// Package bcc
package bcc

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

const (
	ExchangeIDTypeLong  = "long"
	ExchangeIDTypeShort = "short"
)

const (
	ExchangeIDObjectTypeVM            = "vm"
	ExchangeIDObjectTypeSubnet        = "subnet"
	ExchangeIDObjectTypeImage         = "image"
	ExchangeIDObjectTypeVpc           = "vpc"
	ExchangeIDObjectTypeSecurityGroup = "security_group"
)

type Networks struct {
	Key string `json:"key"`
}

type Metadata struct {
	InstanceType   string `json:"instance_type"`
	FlavorType     string `json:"flavor_type"`
	ImageType      string `json:"image_type"`
	Reserve        string `json:"reserve"`
	MasterEndpoint string `json:"master_endpoint"`
	EnvType        string `json:"env_type"`
	ReplaceUuid    string `json:"replace_uuid"`
	DeploySetId    string `json:"deploy_set_id"`
	DiskType       string `json:"disk_type"`
	UseLocalDisk   string `json:"use_local_disk"`
}

type Personality struct {
	Path     string `json:"path"`
	Contents string `json:"contents"`
}

type Flavor struct {
	Cpu       int32 `json:"cpu"`
	Memory    int32 `json:"memory"`
	Disk      int32 `json:"disk"`
	Ephemeral int32 `json:"ephemeral"`
}

type Group struct {
	Name string `json:"name"`
}

type Scheduler struct {
	DifferentHost []string `json:"different_host"`
	SchedulerTags []string `json:"scheduler_tags"`
}

type Server struct {
	Flavor           *Flavor    `json:"flavor"`
	ImageRef         string     `json:"imageRef"`
	Name             string     `json:"name"`
	Metadata         *Metadata  `json:"metadata"`
	DifferentHost    bool       `json:"different_host"`
	SecurityGroups   []*Group   `json:"security_groups"`
	OsschedulerHints *Scheduler `json:"osscheduler_hints"`
	Count            int32      `json:"count"`
	CreateFloatingip bool       `json:"create_floatingip"`
	SubnetId         string     `json:"subnet_id"`
	DeployIdList     []string   `json:"deploy_id_list"`
}

type CallbackServerInfo struct {
	Fixip      string    `json:"fixip"`
	Floatingip string    `json:"floatingip"`
	Id         string    `json:"id"`
	Name       string    `json:"name"`
	AdminPass  string    `json:"adminPass"`
	Metadata   *Metadata `json:"metadata"`
}

type BatchServerResponse struct {
	Requestid string                `json:"requestid"`
	Succ      bool                  `json:"succ"`
	Errmsg    string                `json:"errmsg"`
	Servers   []*CallbackServerInfo `json:"servers"`
}

type SecurityGroupReq struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Creator     string `json:"creator"`
	VpcId       string `json:"vpc_id"`
}

type CreateSecurityGroupRequest struct {
	SecurityGroup *SecurityGroupReq `json:"security_group"`
	Token         string            `json:"-"`
}

type SecurityGroupRules struct {
	Direction       string `json:"direction"`
	Ethertype       string `json:"ethertype"`
	Id              string `json:"id,omitempty"`
	PortRangeMax    int32  `json:"port_range_max,omitempty"`
	PortRangeMin    int32  `json:"port_range_min,omitempty"`
	Protocol        string `json:"protocol"`
	RemoteGroupId   string `json:"remote_group_id,omitempty"`
	RemoteIpPrefix  string `json:"remote_ip_prefix,omitempty"`
	SecurityGroupId string `json:"security_group_id"`
	TenantId        string `json:"tenant_id,omitempty"`
	Creator         string `json:"creator"`
}

type SecurityGroup struct {
	Description        string                `json:"description"`
	Id                 string                `json:"id"`
	Name               string                `json:"name"`
	SecurityGroupRules []*SecurityGroupRules `json:"security_group_rules"`
	TenantId           string                `json:"tenant_id"`
}

type NeutronError struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Detail  string `json:"detail"`
}

type CreateSecurityGroupResponse struct {
	SecurityGroup *SecurityGroup `json:"security_group"`
	NeutronError  *NeutronError  `json:"NeutronError"`
}

type ShowSecurityGroupRequest struct {
	Token           string `json:"-"`
	SecurityGroupId string `json:"-"`
}

type ShowSecurityGroupResponse struct {
	SecurityGroup *SecurityGroup `json:"security_group"`
	NeutronError  *NeutronError  `json:"NeutronError"`
}

type CreateSecurityGroupRulesRequest struct {
	SecurityGroupRule *SecurityGroupRules `json:"security_group_rule"`
	Token             string              `json:"-"`
}

type CreateSecurityGroupRulesResponse struct {
	SecurityGroupRule *SecurityGroupRules `json:"security_group_rule"`
	NeutronError      *NeutronError       `json:"NeutronError"`
}

type DeleteSecurityGroupRequest struct {
	SecurityGroupId string `json:"-"`
	Token           string `json:"-"`
}

type DeleteSecurityGroupResponse struct {
	SecurityGroupId string        `json:"security_group_id"`
	NeutronError    *NeutronError `json:"NeutronError"`
}

type DeleteSecurityGroupRulesRequest struct {
	SecurityGroupRulesId string `json:"-"`
	Token                string `json:"-"`
}

type DeleteSecurityGroupRulesResponse struct {
	SecurityGroupRulesId string        `json:"security_group_rules_id"`
	NeutronError         *NeutronError `json:"NeutronError"`
}

type IpFormat struct {
	Addr    string `json:"addr"`
	Version int32  `json:"version"`
	AddrV6  string `json:"addr_v6"`
}

type IpAddress struct {
	Fixed    *IpFormat `json:"fixed"`
	Floating *IpFormat `json:"floating"`
}

type VmServer struct {
	Addresses *IpAddress `json:"addresses"`
	Created   string     `json:"created"`
	Id        string     `json:"id"`
	Name      string     `json:"name"`
	Status    string     `json:"status"`
	TenantId  string     `json:"tenant_id"`
	Updated   string     `json:"updated"`
	UserId    string     `json:"user_id"`
}

type ShowServerResponse struct {
	Server *VmServer `json:"server"`
}

type ShowServerRequest struct {
	VmUuid string `json:"vm_uuid"`
	Token  string `json:"token"`
}

type Network struct {
	Status  string   `json:"status"`
	Subnets []string `json:"subnets"`
	Name    string   `json:"name"`
	Id      string   `json:"id"`
}

type ListNetworkRequest struct {
	Token string `json:"-"`
}

type ListNetworkResponse struct {
	Vnetworks    []*Network    `json:"vnetworks"`
	NeutronError *NeutronError `json:"NeutronError"`
}

type ShowServer struct {
	Fixip string `json:"fixip"`
	// Floatingip master_redis
	Floatingip string `json:"floatingip"`
	Status     int32  `json:"status"`
	// Id uuid
	Id string `json:"id"`
	// Name hostname
	Name      string    `json:"name"`
	AdminPass string    `json:"adminPass"`
	Metadata  *Metadata `json:"metadata"`
	HashName  string    `json:"hash_name"`
	FixipV6   string    `json:"fixip_v6"`
}

type ShowVolume struct {
	Id   string `json:"id"`
	Size int32  `json:"size"`
}

type ShowTransactionResponse struct {
	TransactionId string        `json:"transaction_id"`
	Status        string        `json:"status"`
	Action        string        `json:"action"`
	Servers       []*ShowServer `json:"servers"`
	Errmsg        string        `json:"errmsg"`
	Volumes       []*ShowVolume `json:"volumes"`
}

type ShowTransactionRequest struct {
	TransactionId string `json:"transaction_id"`
	Token         string `json:"token"`
}

type SetTransactionRequest struct {
	Status        string `json:"status"`
	TransactionId string `json:"-"`
	Token         string `json:"-"`
}

type SetTransactionResponse struct {
	Status int32 `json:"status"`
}

type VolumeAttachment struct {
	Device   string `json:"device"`
	Id       string `json:"id"`
	ServerId string `json:"serverId"`
	VolumeId string `json:"volumeId"`
}

type VolumeAttachRequest struct {
	VolumeAttachment *VolumeAttachment `json:"volumeAttachment"`
}

type VolumeAttachResponse struct {
	VolumeAttachment *VolumeAttachment `json:"volumeAttachment"`
}

type Volumes struct {
	Count              int32  `json:"count"`
	DisplayName        string `json:"display_name"`
	DisplayDescription string `json:"display_description"`
	SnapshotId         string `json:"snapshot_id"`
	// Size GB
	Size int32 `json:"size"`
}

type VolumeCreateRequest struct {
	Source        string     `json:"source"`
	Action        string     `json:"action"`
	TransactionId string     `json:"transaction_id"`
	Callback      string     `json:"callback"`
	Volumes       []*Volumes `json:"volumes"`
}

type VolumeCreateResponse struct {
}

type VolumeDeleteRequest struct {
	Action  string   `json:"action"`
	Volumes []string `json:"volumes"`
}

type VolumeDeleteResponse struct {
}

type FixedIp struct {
	SubnetId  string `json:"subnet_id"`
	IpAddress string `json:"ip_address"`
}

type Port struct {
	Status               string     `json:"status"`
	BindinghostId        string     `json:"binding:host_id"`
	Name                 string     `json:"name"`
	AllowedAdddressPairs []string   `json:"allowed_adddress_pairs"`
	AdminStateUp         bool       `json:"admin_state_up"`
	NetworkId            string     `json:"network_id"`
	TenantId             string     `json:"tenant_id"`
	ExtraDhcpOpts        []string   `json:"extra_dhcp_opts"`
	BindingvnicType      string     `json:"bindingvnic_type"`
	DeviceOwner          string     `json:"device_owner"`
	MacAntiSpoofing      bool       `json:"mac_anti_spoofing"`
	MacAddress           string     `json:"mac_address"`
	FixedIps             []*FixedIp `json:"fixed_ips"`
	Id                   string     `json:"id"`
	SecurityGroups       []string   `json:"security_groups"`
	DeviceId             string     `json:"device_id"`
}

type GetDnsMasqRequest struct {
	VpcId string `json:"-"`
	Token string `json:"-"`
}

type GetPortsRequest struct {
	VmUuid string `json:"-"`
	Token  string `json:"-"`
}

type PortsResponse struct {
	Ports        []*Port       `json:"ports"`
	NeutronError *NeutronError `json:"NeutronError"`
}

type ExtraDnsOpt struct {
	OptValue string `json:"opt_value"`
	OptName  string `json:"opt_name"`
}

type Vpc struct {
	ExtraDnsOpts []*ExtraDnsOpt `json:"extra_dns_opts"`
	Status       string         `json:"status"`
	Description  string         `json:"description"`
	VpcName      string         `json:"vpc_name"`
	IsDefaultVpc string         `json:"is_default_vpc"`
	CreateTime   string         `json:"create_time"`
	VpcCidr      string         `json:"vpc_cidr"`
	TenantId     string         `json:"tenant_id"`
	Id           string         `json:"id"`
}

type SetDnsMasqRequest struct {
	Vpc   *Vpc   `json:"vpc"`
	VpcId string `json:"-"`
	Token string `json:"-"`
}

type SetDnsMasqResponse struct {
	Vpc          *Vpc          `json:"vpc"`
	NeutronError *NeutronError `json:"NeutronError"`
}

type UnbindSecurityGroupRequest struct {
	PortId string `json:"-"`
	Token  string `json:"-"`
}

type GetSubnetInfoRequest struct {
	SubnetId string `json:"subnet_id"`
	Token    string `json:"-"`
}

type SubnetAllocationPools struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

type SubnetInfo struct {
	SubnetType     string                 `json:"subnet_type"`
	SubnetRegion   string                 `json:"subnet_region"`
	Description    string                 `json:"description"`
	Name           string                 `json:"name"`
	NetworkId      string                 `json:"network_id"`
	AzZone         string                 `json:"az_zone"`
	DnsNameservers interface{}            `json:"dns_nameservers"`
	FlatHostCidr   string                 `json:"flat_host_cidr"`
	Allocpool      *SubnetAllocationPools `json:"allocpool"`
	CreateTime     string                 `json:"create_time"`
	HostRoutes     interface{}            `json:"host_routes"`
	VpcId          string                 `json:"vpc_id"`
	TenantId       string                 `json:"tenant_id"`
	IpVersion      int                    `json:"ip_version"`
	GatewayIp      string                 `json:"gateway_ip"`
	Cidr           string                 `json:"cidr"`
	EnableDhcp     bool                   `json:"enable_dhcp"`
	Id             string                 `json:"id"`
	Dallocpool     *SubnetAllocationPools `json:"dallocpool"`
}

type GetSubnetInfoResponse struct {
	Subnet       *SubnetInfo   `json:"subnet"`
	NeutronError *NeutronError `json:"NeutronError"`
}

type GetSubnetsRequest struct {
	Token string `json:"-"`
}

type GetSubnetsResponse struct {
	Subnets      []*SubnetInfo `json:"subnets"`
	NeutronError *NeutronError `json:"NeutronError"`
}

type GetSubnetIpIdRequest struct {
	SubnetId string `json:"subnet_id"`
	Token    string `json:"-"`
}

type SubnetIpId struct {
	Id string `json:"id"`
}

type GetSubnetIpIdResponse struct {
	Ports        []*SubnetIpId `json:"ports"`
	NeutronError *NeutronError `json:"NeutronError"`
}

type OperateVmRequest struct {
	TenantId  string `json:"tenant_id"`
	Token     string `json:"token"`
	VmUuid    string `json:"vm_uuid"`
	Operation string `json:"operation"`
}

type StartVmRequest struct {
	TenantId string `json:"tenant_id"`
	Token    string `json:"token"`
	VmUuid   string `json:"vm_uuid"`
}

type StopVmRequest struct {
	TenantId string `json:"tenant_id"`
	Token    string `json:"token"`
	VmUuid   string `json:"vm_uuid"`
}

type VmOperationRequest struct {
	Osstop  string `json:"osstop"`
	Osstart string `json:"osstart"`
}

type OperateSecurityGroupRequest struct {
	TenantId        string `json:"tenant_id"`
	Token           string `json:"token"`
	VmUuid          string `json:"vm_uuid"`
	SecurityGroupId string `json:"security_group_id"`
}

type AddSecurityGroupRequest struct {
	AddSecurityGroup *Group `json:"addSecurityGroup"`
}

type RemoveSecurityGroupRequest struct {
	RemoveSecurityGroup *Group `json:"removeSecurityGroup"`
}

type GetBccBindSgRequest struct {
	TenantId string `json:"tenant_id"`
	Token    string `json:"token"`
	VmUuid   string `json:"vm_uuid"`
}

type BccBindSecurityGroup struct {
	Description string `json:"description"`
	Id          string `json:"id"`
	Name        string `json:"name"`
	TenantId    string `json:"tenant_id"`
}

type GetBccBindSgResponse struct {
	SecurityGroups []*BccBindSecurityGroup `json:"security_groups"`
}

type CreateCdsList struct {
	StorageType string `json:"storageType"`
	CdsSizeInGB int32  `json:"cdsSizeInGB"`
}

type Billing struct {
	PaymentTiming string `json:"paymentTiming"`
}

type Tags struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

type Configs struct {
	Key         string `json:"key"`
	ConfigValue string `json:"configValue"`
}

type EphemeralDisks struct {
	StorageType string `json:"storageType"`
	SizeInGB    int32  `json:"sizeInGB"`
}

// PAASDeploySetStrategy 定义了单个部署策略的结构
type PAASDeploySetStrategy struct {
	Concurrency int    `json:"concurrency"`
	ID          string `json:"id"`
	Policy      string `json:"policy"`
	Quota       int    `json:"quota"`
}

type CreateInstances struct {
	InstanceType            string                   `json:"instanceType,omitempty"`
	Spec                    string                   `json:"spec,omitempty"`
	CpuCount                int32                    `json:"cpuCount"`
	ZoneName                string                   `json:"zoneName"`
	MemoryCapacityInGB      int32                    `json:"memoryCapacityInGB"`
	RootDiskSizeInGb        int32                    `json:"rootDiskSizeInGb"`
	RootDiskStorageType     string                   `json:"rootDiskStorageType"`
	CreateCdsList           []*CreateCdsList         `json:"createCdsList,omitempty"`
	EphemeralDisks          []*EphemeralDisks        `json:"ephemeralDisks,omitempty"`
	Name                    string                   `json:"name"`
	ImageId                 string                   `json:"imageId"`
	PurchaseCount           int32                    `json:"purchaseCount"`
	SubnetId                string                   `json:"subnetId"`
	SecurityGroupId         string                   `json:"securityGroupId"`
	Billing                 *Billing                 `json:"billing"`
	RelationTag             int32                    `json:"relationTag"`
	RootOnLocal             int32                    `json:"rootOnLocal"`
	Tags                    []*Tags                  `json:"tags"`
	DeploySetId             string                   `json:"deploySetId"`
	UserData                string                   `json:"userData"`
	Configs                 []*Configs               `json:"configs"`
	DeployIdList            []string                 `json:"deployIdList,omitempty"`
	IsReplace               bool                     `json:"isReplace,omitempty"`
	ReplacedInstanceIDs     []string                 `json:"replacedInstanceIds,omitempty"`
	PAASDeploySetStrategies []*PAASDeploySetStrategy `json:"paasDeploySetStrategies,omitempty"`
}

type BatchCreateServersRequest struct {
	OrderOverTime   int32                  `json:"orderOverTime"`
	CreateInstances []*CreateInstances     `json:"createInstances"`
	Auth            *common.Authentication `json:"-"`
	ClientToken     string                 `json:"-"`
}

type BatchCreateServersResponse struct {
	Message     string   `json:"message"`
	Code        string   `json:"code"`
	Requestid   string   `json:"requestid"`
	OrderId     string   `json:"orderId"`
	InstanceIds []string `json:"instanceIds"`
}

type BatchDeleteServersRequest struct {
	InstanceIds    []string               `json:"instanceIds"`
	RelatedRelease int32                  `json:"relatedRelease"`
	Auth           *common.Authentication `json:"-"`
}

type BatchDeleteServersResponse struct {
	Message   string `json:"message"`
	Code      string `json:"code"`
	Requestid string `json:"requestid"`
	Result    int32  `json:"result"`
}

type ExchangeIdRequest struct {
	IdType      string                 `json:"idType"`
	ObjectType  string                 `json:"objectType"`
	InstanceIds []string               `json:"instanceIds"`
	Auth        *common.Authentication `json:"-"`
}

type Mappings struct {
	Id   string `json:"id"`
	Uuid string `json:"uuid"`
}

type ExchangeIdResponse struct {
	Message   string      `json:"message"`
	Code      string      `json:"code"`
	Requestid string      `json:"requestid"`
	Mappings  []*Mappings `json:"mappings"`
}

type SysVolume struct {
	Status     string `json:"status"`
	BootIndex  int32  `json:"bootIndex"`
	VolumeId   string `json:"volumeId"`
	Name       string `json:"name"`
	DeviceId   string `json:"deviceId"`
	Type       string `json:"type"`
	CreateTime string `json:"createTime"`
	SizeInGB   int32  `json:"sizeInGB"`
}

type CdsVolume struct {
	Status     string `json:"status"`
	BootIndex  int32  `json:"bootIndex"`
	VolumeId   string `json:"volumeId"`
	Name       string `json:"name"`
	DeviceId   string `json:"deviceId"`
	Type       string `json:"type"`
	CreateTime string `json:"createTime"`
	SizeInGB   int32  `json:"sizeInGB"`
}

type ShowOrderRequest struct {
	OrderId string                 `json:"-"`
	Auth    *common.Authentication `json:"-"`
}

type Instances struct {
	Status     string       `json:"status"`
	Name       string       `json:"name"`
	AdminPass  string       `json:"adminPass"`
	InstanceId string       `json:"instanceId"`
	FloatingIp string       `json:"floatingIp"`
	SysVolume  []*SysVolume `json:"sysVolume"`
	InternalIp string       `json:"internalIp"`
	CdsVolume  []*CdsVolume `json:"cdsVolume"`
	Flavor     string       `json:"flavor"`
	CreateTime string       `json:"createTime"`
	MetaData   string       `json:"metaData"`
	Ipv6       string       `json:"ipv6"`
}

type ShowOrderResponse struct {
	Message   string       `json:"message"`
	Code      string       `json:"code"`
	Requestid string       `json:"requestid"`
	OrderId   string       `json:"orderId"`
	Status    string       `json:"status"`
	ErrMsg    string       `json:"errMsg"`
	Action    string       `json:"action"`
	ZoneName  string       `json:"zoneName"`
	Instances []*Instances `json:"instances"`
}

type InstanceVolume struct {
	DiskSizeInGB   int32  `json:"diskSizeInGB"`
	VolumeId       string `json:"volumeId"`
	IsSystemVolume bool   `json:"isSystemVolume"`
}

type ShowInstanceRequest struct {
	VmUuid string                 `json:"-"`
	Auth   *common.Authentication `json:"-"`
}

type GetCdsStockRequest struct {
	ZoneName string                 `json:"zoneName"`
	Auth     *common.Authentication `json:"-"`
}

type GetCdsStockResponse struct {
	Message     string              `json:"message"`
	Code        string              `json:"code"`
	Requestid   string              `json:"requestid"`
	VolumeStock []VolumeStockDetail `json:"volumeStock"`
}

type VolumeStockDetail struct {
	StorageType string `json:"storageType"`
	Left        int64  `json:"left"`
}

type InstanceInfo struct {
	VpcId                 string            `json:"vpcId"`
	ImageId               string            `json:"imageId"`
	SubnetId              string            `json:"subnetId"`
	Id                    string            `json:"id"`
	NetworkCapacityInMbps int32             `json:"networkCapacityInMbps"`
	DedicatedHostId       string            `json:"dedicatedHostId"`
	LocalDiskSizeInGB     int32             `json:"localDiskSizeInGB"`
	InstanceType          string            `json:"instanceType"`
	Ipv6                  string            `json:"ipv6"`
	Desc                  string            `json:"desc"`
	Status                string            `json:"status"`
	PlacementPolicy       string            `json:"placementPolicy"`
	PaymentTiming         string            `json:"paymentTiming"`
	PublicIp              string            `json:"publicIp"`
	InternalIp            string            `json:"internalIp"`
	CreateTime            string            `json:"createTime"`
	ZoneName              string            `json:"zoneName"`
	Name                  string            `json:"name"`
	MemoryCapacityInGB    int32             `json:"memoryCapacityInGB"`
	CpuCount              int32             `json:"cpuCount"`
	Volumes               []*InstanceVolume `json:"volumes"`
}

type ShowInstanceResponse struct {
	Message   string        `json:"message"`
	Code      string        `json:"code"`
	Requestid string        `json:"requestid"`
	Instance  *InstanceInfo `json:"instance"`
}

type ResizeInstanceRequest struct {
	CpuCount           int32                  `json:"cpuCount"`
	MemoryCapacityInGB int32                  `json:"memoryCapacityInGB"`
	LiveResize         bool                   `json:"liveResize"`
	VmId               string                 `json:"-"`
	Auth               *common.Authentication `json:"-"`
}

type ResizeInstanceResponse struct {
	Message   string `json:"message"`
	Code      string `json:"code"`
	Requestid string `json:"requestid"`
}

type CdsMountListRequest struct {
	VmId string                 `json:"-"`
	Auth *common.Authentication `json:"-"`
}

type CdsVolumeAttachmentInfo struct {
	VolumeId   string `json:"volumeId"`
	InstanceId string `json:"instanceId"`
	Device     string `json:"device"`
	Serial     string `json:"serial"`
}

type CdsVolumeInfo struct {
	Id            string                     `json:"id"`
	CreateTime    string                     `json:"createTime"`
	ExpireTime    string                     `json:"expireTime"`
	Name          string                     `json:"name"`
	DiskSizeInGB  int32                      `json:"diskSizeInGB"`
	Status        string                     `json:"status"`
	Type          string                     `json:"type"`
	StorageType   string                     `json:"storageType"`
	Desc          string                     `json:"desc"`
	PaymentTiming string                     `json:"paymentTiming"`
	Attachments   []*CdsVolumeAttachmentInfo `json:"attachments"`
	ZoneName      string                     `json:"zoneName"`
}

type CdsMountListResponse struct {
	Message    string           `json:"message"`
	Code       string           `json:"code"`
	Requestid  string           `json:"requestid"`
	NextMarker string           `json:"nextMarker"`
	Marker     string           `json:"marker"`
	MaxKeys    int32            `json:"maxKeys"`
	Volumes    []*CdsVolumeInfo `json:"volumes"`
}

type CdsResizeRequest struct {
	NewCdsSizeInGB int32                  `json:"newCdsSizeInGB,omitempty"`
	NewVolumeType  string                 `json:"newVolumeType,omitempty"`
	VolumeId       string                 `json:"-"`
	Auth           *common.Authentication `json:"-"`
}

type CdsResizeResponse struct {
	Message   string `json:"message"`
	Code      string `json:"code"`
	Requestid string `json:"requestid"`
}

type CdsCreateRequest struct {
	ClientToken   string                 `json:"-"`
	PurchaseCount int                    `json:"purchaseCount,omitempty"`
	Name          string                 `json:"name,omitempty"`
	Description   string                 `json:"description,omitempty"`
	CdsSizeInGB   int                    `json:"cdsSizeInGB,omitempty"`
	StorageType   string                 `json:"storageType,omitempty"`
	ChargeType    string                 `json:"chargeType"`
	SnapshotID    string                 `json:"snapshotId,omitempty"`
	ZoneName      string                 `json:"zoneName,omitempty"`
	RenewTimeUnit string                 `json:"renewTimeUnit"`
	RenewTime     int                    `json:"renewTime"`
	InstanceID    string                 `json:"instanceId"`
	EncryptKey    string                 `json:"encryptKey"`
	Tags          []*CdsTag              `json:"tags"`
	ClusterID     string                 `json:"clusterId"`
	ResGroupID    string                 `json:"resGroupId,omitempty"`
	Auth          *common.Authentication `json:"-"`
}

type CdsCreateResponse struct {
	VolumeIDs   []string `json:"volumeIds"`
	WarningList []string `json:"warningList"`
	Code        string   `json:"code"`
	Requestid   string   `json:"requestid"`
	Message     string   `json:"message"`
}

type CdsAttachRequest struct {
	InstanceId string                 `json:"instanceId"`
	VolumeId   string                 `json:"-"`
	Auth       *common.Authentication `json:"-"`
}

type CdsAttachResponse struct {
	Message          string                     `json:"message"`
	Code             string                     `json:"code"`
	Requestid        string                     `json:"requestid"`
	VolumnAttachment []*CdsVolumeAttachmentInfo `json:"volumeAttachment"`
}

type CdsDetachRequest struct {
	InstanceId string                 `json:"instanceId"`
	VolumeId   string                 `json:"-"`
	Auth       *common.Authentication `json:"-"`
}

type CdsDetachResponse struct {
	Message   string `json:"message"`
	Code      string `json:"code"`
	Requestid string `json:"requestid"`
}

type CdsDeleteRequest struct {
	AutoSnapshot   string                 `json:"autoSnapshot"`
	ManualSnapshot string                 `json:"manualSnapshot"`
	Recycle        string                 `json:"recycle"`
	VolumeId       string                 `json:"-"`
	Auth           *common.Authentication `json:"-"`
}

type CdsDeleteResponse struct {
	Message   string `json:"message"`
	Code      string `json:"code"`
	Requestid string `json:"requestid"`
}

type GetCdsDetailRequest struct {
	VolumeId string                 `json:"volumeId"`
	Auth     *common.Authentication `json:"-"`
}

type GetCdsDetailResponse struct {
	Message   string        `json:"message"`
	Code      string        `json:"code"`
	Requestid string        `json:"requestid"`
	Volume    *VolumeDetail `json:"volume"`
}

type VolumeDetail struct {
	Id               string                     `json:"id"`
	CreateTime       string                     `json:"createTime"`
	ExpireTime       string                     `json:"expireTime"`
	Name             string                     `json:"name"`
	DiskSizeInGB     int32                      `json:"diskSizeInGB"`
	Status           string                     `json:"status"`
	Type             string                     `json:"type"`
	StorageType      string                     `json:"storageType"`
	IsSystemVolume   bool                       `json:"isSystemVolume"`
	Desc             string                     `json:"desc"`
	PaymentTiming    string                     `json:"paymentTiming"`
	Attachments      []*CdsVolumeAttachmentInfo `json:"attachments"`
	RegionId         string                     `json:"regionId"`
	SourceSnapshotId string                     `json:"sourceSnapshotId"`
	SnapshotNum      string                     `json:"snapshotNum"`
	Tags             []*CdsTag                  `json:"tags"`
	ZoneName         string                     `json:"zoneName"`
}

type CdsTag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

type CreateDeploySetRequest struct {
	Name     string                 `json:"name"`
	Desc     string                 `json:"desc"`
	Strategy string                 `json:"strategy"`
	Auth     *common.Authentication `json:"-"`
}

type DeploySetIdsResponse struct {
	Message      string   `json:"message"`
	Code         string   `json:"code"`
	Requestid    string   `json:"requestid"`
	DeploySetIds []string `json:"deploySetIds"`
}

type ShowDeploySetRequest struct {
	DeploySetID string                 `json:"-"`
	Auth        *common.Authentication `json:"-"`
}

type ShowDeploySetResponse struct {
	Message              string        `json:"message"`
	Code                 string        `json:"code"`
	Requestid            string        `json:"requestid"`
	InstanceCount        int           `json:"instanceCount"`
	InstanceTotal        *int          `json:"instanceTotal"`  // 使用指针来区分null和0
	BCCInstanceCnt       *int          `json:"bccInstanceCnt"` // 使用指针来区分null和0
	BBCInstanceCnt       *int          `json:"bbcInstanceCnt"` // 使用指针来区分null和0
	UUID                 string        `json:"uuid"`
	Name                 string        `json:"name"`
	Strategy             string        `json:"strategy"`
	ShortID              string        `json:"shortId"`
	Concurrency          int           `json:"concurrency"`           // 主要用此值
	AZInstanceStatisList []interface{} `json:"azIntstanceStatisList"` // 详情中此处返回的是空列表
	Desc                 string        `json:"desc"`
}

type GetSubnetIpTypeRequest struct {
	SubnetId      string `json:"subnet_id"`
	Token         string `json:"-"`
	TransactionId string `json:"-"`
}

type GetSubnetIpTypeResponse struct {
	Subnet       *Subnet       `json:"subnet"`
	NeutronError *NeutronError `json:"NeutronError"`
}

type Subnet struct {
	SubnetType   string `json:"subnet_type"`
	SubnetRegion string `json:"subnet_region"`
	Description  string `json:"description"`
	Name         string `json:"name"`
	EnableIpv6   bool   `json:"enable_ipv6"`
	AzZone       string `json:"az_zone"`
	FlatHostCidr string `json:"flat_host_cidr"`
	NetworkId    string `json:"network_id"`
	CreateTime   string `json:"create_time"`
	VpcId        string `json:"vpc_id"`
	TenantId     string `json:"tenant_id"`
	IpVersion    int64  `json:"ip_version"`
	GatewayIp    string `json:"gateway_ip"`
	Cidr         string `json:"cidr"`
	EnableDhcp   bool   `json:"enable_dhcp"`
	Id           string `json:"id"`
}

// EndpointParams definition
type EndpointParams struct {
	VpcID       string   `json:"vpcId"`
	Name        string   `json:"name"`
	SubnetID    string   `json:"subnetId"`
	Service     string   `json:"service"`
	Description string   `json:"description,omitempty"`
	IPAddress   string   `json:"ipAddress,omitempty"`
	Billing     *Billing `json:"billing"`
}

// CreateEndpointRequest definition
type CreateEndpointRequest struct {
	EndpointParams
	Auth *common.Authentication `json:"-"`
}

// Endpoint definition
type Endpoint struct {
	ID        string `json:"id"`
	IPAddress string `json:"ipAddress"`
}

// CreateEndpointResponse definition
type CreateEndpointResponse struct {
	Endpoint
	Message   string `json:"message"`
	Code      string `json:"code"`
	Requestid string `json:"requestid"`
}

// UpdateEndpointRequest definition
type UpdateEndpointRequest struct {
	EndpointID  string                 `json:"endpointId"`
	Name        string                 `json:"name,omitempty"`
	Description string                 `json:"description,omitempty"`
	Auth        *common.Authentication `json:"-"`
}

// DeleteEndpointRequest definition
type DeleteEndpointRequest struct {
	EndpointID string                 `json:"endpointId"`
	Auth       *common.Authentication `json:"-"`
}

// GetEndpointDetailRequest definition
type GetEndpointDetailRequest struct {
	EndpointID string                 `json:"endpointId"`
	Auth       *common.Authentication `json:"-"`
}

// EndpointInfo definition
type EndpointInfo struct {
	EndpointID  string `json:"endpointId"`
	Name        string `json:"name"`
	IPAddress   string `json:"ipAddress"`
	Status      string `json:"status"`
	Service     string `json:"service"`
	SubnetID    string `json:"subnetId"`
	Description string `json:"description"`
	CreateTime  string `json:"createTime"`
	ProductType string `json:"productType"`
	VpcID       string `json:"vpcId"`
}

// GetEndpointDetailResponse definition
type GetEndpointDetailResponse struct {
	EndpointInfo
	Message   string `json:"message"`
	Code      string `json:"code"`
	Requestid string `json:"requestid"`
}

// GetPublicServiceRequest definition
type GetPublicServiceRequest struct {
	Auth *common.Authentication `json:"-"`
}

// GetPublicServiceResponse definition
type GetPublicServiceResponse struct {
	Services  []string `json:"services"`
	Message   string   `json:"message"`
	Code      string   `json:"code"`
	Requestid string   `json:"requestid"`
}

// GetEndpointListRequest definition
type GetEndpointListRequest struct {
	VpcID     string                 `json:"vpcId"`
	Name      string                 `json:"name,omitempty"`
	IPAddress string                 `json:"ipAddress,omitempty"`
	Status    string                 `json:"status,omitempty"`
	Service   string                 `json:"service,omitempty"`
	SubnetID  string                 `json:"subnetId,omitempty"`
	Marker    string                 `json:"marker,omitempty"`
	MaxKeys   int                    `json:"maxKeys,omitempty"`
	Auth      *common.Authentication `json:"-"`
}

// GetEndpointListResponse definition
type GetEndpointListResponse struct {
	NextMarker string          `json:"nextMarker"`
	Marker     string          `json:"marker"`
	MaxKeys    int             `json:"maxKeys"`
	Endpoints  []*EndpointInfo `json:"endpoints"`
	Message    string          `json:"message"`
	Code       string          `json:"code"`
	Requestid  string          `json:"requestid"`
}

type GetSubnetDetailRequest struct {
	SubnetID string                 `json:"subnetId"`
	Auth     *common.Authentication `json:"-"`
}

type GetSubnetDetailResponse struct {
	Message   string        `json:"message"`
	Code      string        `json:"code"`
	Requestid string        `json:"requestid"`
	Subnet    *SubnetDetail `json:"subnet"`
}

type SubnetDetail struct {
	Name        string       `json:"name"`
	SubnetId    string       `json:"subnetId"`
	ZoneName    string       `json:"zoneName"`
	Cidr        string       `json:"cidr"`
	VpcId       string       `json:"vpcId"`
	SubnetType  string       `json:"subnetType"`
	AvailableIp int64        `json:"availableIp"`
	Description string       `json:"description"`
	Tags        []*SubnetTag `json:"tags"`
	Ipv6Cidr    string       `json:"ipv6Cidr"`
	CreatedTime string       `json:"createdTime"`
}

type SubnetTag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

type OpenStackService interface {
	ShowServer(ctx context.Context, req *ShowServerRequest) (rsp *ShowServerResponse, err error)
	ShowTransaction(ctx context.Context, req *ShowTransactionRequest) (rsp *ShowTransactionResponse, err error)
	SetShowTransactionForUt(ctx context.Context, req *SetTransactionRequest) (rsp *SetTransactionResponse, err error)
	ExchangeId(ctx context.Context, req *ExchangeIdRequest) (rsp *ExchangeIdResponse, err error)
	BatchCreateServers(ctx context.Context, req *BatchCreateServersRequest) (rsp *BatchCreateServersResponse, err error)
	BatchDeleteServers(ctx context.Context, req *BatchDeleteServersRequest) (rsp *BatchDeleteServersResponse, err error)
	ShowOrder(ctx context.Context, req *ShowOrderRequest) (rsp *ShowOrderResponse, err error)
	ShowInstanceInfo(ctx context.Context, req *ShowInstanceRequest) (rsp *ShowInstanceResponse, err error)
	ResizeInstance(ctx context.Context, req *ResizeInstanceRequest) (rsp *ResizeInstanceResponse, err error)
	InstanceAttachedCdsList(ctx context.Context, req *CdsMountListRequest) (rsp *CdsMountListResponse, err error)
	ResizeCds(ctx context.Context, req *CdsResizeRequest) (rsp *CdsResizeResponse, err error)
	CreateDeploySet(ctx context.Context, req *CreateDeploySetRequest) (rsp *DeploySetIdsResponse, err error)
	ShowDeploySet(ctx context.Context, req *ShowDeploySetRequest) (rsp *ShowDeploySetResponse, err error)
	GetCdsStockWithZone(ctx context.Context, req *GetCdsStockRequest) (rsp *GetCdsStockResponse, err error)
	SubnetDetail(ctx context.Context, req *GetSubnetDetailRequest) (rsp *GetSubnetDetailResponse, err error)
	CreateCds(ctx context.Context, req *CdsCreateRequest) (rsp *CdsCreateResponse, err error)
	AttachCds(ctx context.Context, req *CdsAttachRequest) (rsp *CdsAttachResponse, err error)
	DetachCds(ctx context.Context, req *CdsDetachRequest) (rsp *CdsDetachResponse, err error)
	DeleteCds(ctx context.Context, req *CdsDeleteRequest) (rsp *CdsDeleteResponse, err error)
	GetCdsDetail(ctx context.Context, req *GetCdsDetailRequest) (rsp *GetCdsDetailResponse, err error)
}

type NeutronService interface {
	CreateSecurityGroup(ctx context.Context, req *CreateSecurityGroupRequest) (rsp *CreateSecurityGroupResponse, err error)
	ShowSecurityGroup(ctx context.Context, req *ShowSecurityGroupRequest) (rsp *ShowSecurityGroupResponse, err error)
	DeleteSecurityGroup(ctx context.Context, req *DeleteSecurityGroupRequest) (rsp *DeleteSecurityGroupResponse, err error)
	CreateSecurityGroupRules(ctx context.Context, req *CreateSecurityGroupRulesRequest) (rsp *CreateSecurityGroupRulesResponse, err error)
	DeleteSecurityGroupRules(ctx context.Context, req *DeleteSecurityGroupRulesRequest) (rsp *DeleteSecurityGroupRulesResponse, err error)
	ListNetwork(ctx context.Context, req *ListNetworkRequest) (rsp *ListNetworkResponse, err error)
	GetDnsmasq(ctx context.Context, req *GetDnsMasqRequest) (rsp *PortsResponse, err error)
	SetDnsmasq(ctx context.Context, req *SetDnsMasqRequest) (rsp *SetDnsMasqResponse, err error)
	GetPorts(ctx context.Context, req *GetPortsRequest) (rsp *PortsResponse, err error)
	UnbindSecurityGroup(ctx context.Context, req *UnbindSecurityGroupRequest) (rsp *PortsResponse, err error)
	GetSubnetInfo(ctx context.Context, req *GetSubnetInfoRequest) (rsp *GetSubnetInfoResponse, err error)
	GetSubnets(ctx context.Context, req *GetSubnetsRequest) (rsp *GetSubnetsResponse, err error)
	GetSubnetIpId(ctx context.Context, req *GetSubnetIpIdRequest) (rsp *GetSubnetIpIdResponse, err error)
}

type NeutronV2Service interface {
	GetSubnetDetail(ctx context.Context, req *GetSubnetDetailRequest) (rsp *GetSubnetDetailResponse, err error)
}

type NeutronVpcService interface {
	GetSubnetIpType(ctx context.Context, req *GetSubnetIpTypeRequest) (rsp *GetSubnetIpTypeResponse, err error)
}

type NovaService interface {
	StartVm(ctx context.Context, req *StartVmRequest) (err error)
	StopVm(ctx context.Context, req *StopVmRequest) (err error)
	VmOperation(ctx context.Context, req *OperateVmRequest) (err error)
	RemoveSgFromBcc(ctx context.Context, req *OperateSecurityGroupRequest) (err error)
	AddSgToBcc(ctx context.Context, req *OperateSecurityGroupRequest) (err error)
	GetBccSg(ctx context.Context, req *GetBccBindSgRequest) (rsp *GetBccBindSgResponse, err error)
}

// NeutronEndpointService service inferface
type NeutronEndpointService interface {
	CreateEndpoint(ctx context.Context, req *CreateEndpointRequest) (rsp *CreateEndpointResponse, err error)
	UpdateEndpoint(ctx context.Context, req *UpdateEndpointRequest) (err error)
	DeleteEndpoint(ctx context.Context, req *DeleteEndpointRequest) (err error)
	GetEndpointDetail(ctx context.Context, req *GetEndpointDetailRequest) (rsp *GetEndpointDetailResponse, err error)
	GetPublicService(ctx context.Context, req *GetPublicServiceRequest) (rsp *GetPublicServiceResponse, err error)
	GetEndpointList(ctx context.Context, req *GetEndpointListRequest) (rsp *GetEndpointListResponse, err error)
}
