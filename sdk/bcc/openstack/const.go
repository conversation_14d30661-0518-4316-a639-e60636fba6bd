package openstack

const DefaultServiceName = "openstack"

// 各种uri
const (
	/*v1*/
	openStackClusterUri = "/v1/servers"
	showTransactionUri  = "/v1/transactions"
	subnetDetailUri     = "/v1/subnet"

	/*v2*/
	instanceOperationUri  = "/v2/instance/"
	exchangeIdUri         = "/v2/instance/id/mapping"
	batchCreateServersUri = "/v2/instance/mixture"
	batchDeleteServersUri = "/v2/instance/batchDelete"
	showOrderUri          = "/v2/instance/getServersByOrderId"
	volumeOperationUri    = "/v2/volume"
	deploySetCreateUri    = "/v2/instance/deployset/create"
	deploySetUri          = "/v2/deployset"
	getCdsStockUri        = "/v2/volume/getAllStockWithZone"
)
