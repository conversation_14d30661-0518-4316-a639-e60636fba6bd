/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-08
 * File: openstack_test.go
 */

/*
 * DESCRIPTION
 *   -
 */

// Package bcc
package openstack

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const (
	TestIamUserId      = "a318fe1fe9f5464d92478dab0aa4f5ff"
	TestDeploySetID    = "dset-xxxxxxx"
	TestConcurrency    = 2
	TestCodeBadRequest = "BadRequest"
)

func TestOpenstackSdk_BatchCreateServers_InvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newOpenstackSdk(DefaultServiceName)

	_, err := s.BatchCreateServers(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	_, err = s.BatchCreateServers(ctx, &bcc.BatchCreateServersRequest{
		OrderOverTime:   0,
		CreateInstances: nil,
		Auth:            nil,
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test auth params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}

//func TestOpenstackSdk_BatchCreateServers_RalRequestFail(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	s := newOpenstackSdk(DefaultServiceName)
//	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail, 500))
//
//	_, err := s.BatchCreateServers(ctx, &bcc.BatchCreateServersRequest{
//		OrderOverTime: 1000,
//		CreateInstances: []*bcc.CreateInstances{
//			{
//				SecurityGroupId: "g-6xae9n6euz7q",
//			},
//		},
//		Auth: &common.Authentication{
//			IamUserId:     TestIamUserId,
//			TransactionId: uuid.New().String(),
//			ResourceAccount: &common.ResourceAccount{
//				ResourceAk:       "ffffffffffffffffffffffffffffffff",
//				EncryptAccountId: "ffffffffffffffffffffffffffffffff",
//			},
//			Credential: &common.Credential{
//				Ak:           "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
//				Sk:           "bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
//				SessionToken: "cccccccccccccccccccccccccccccccccc",
//			},
//		},
//	})
//	if !cerrs.ErrRalRequestFail.Is(err) {
//		t.Errorf("[%s] fail to test ral failed call", t.Name())
//	} else {
//		fmt.Printf("error occur in expectation: %s\n", err.Error())
//	}
//}

func TestOpenstackSdk_ExchangeId(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newOpenstackSdk(DefaultServiceName)

	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     TestIamUserId,
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	auth := authRsp.Auth

	exchangeIdRsp, err := s.ExchangeId(ctx, &bcc.ExchangeIdRequest{
		IdType:      "short",
		ObjectType:  "vpc",
		InstanceIds: []string{"7d3caa41-c268-410d-ae68-97b3baff8423"},
		Auth:        auth,
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(base_utils.Format(exchangeIdRsp))
}

func TestOpenstackSdk_ShowDeploySet_InvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newOpenstackSdk(DefaultServiceName)

	// req param is null
	_, err := s.ShowDeploySet(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// deployset id is empty
	_, err = s.ShowDeploySet(ctx, &bcc.ShowDeploySetRequest{})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test show deployset fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// auth param is null
	_, err = s.ShowDeploySet(ctx, &bcc.ShowDeploySetRequest{
		DeploySetID: TestDeploySetID,
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test show deployset fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}

func TestOpenstackSdk_ShowDeploySet_OK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newOpenstackSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&bcc.ShowDeploySetResponse{
		Code:        "",
		Concurrency: TestConcurrency,
	}, nil, http.StatusOK))

	deploysetRsp, err := s.ShowDeploySet(ctx, &bcc.ShowDeploySetRequest{
		DeploySetID: TestDeploySetID,
		Auth:        &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test show deployset fail", t.Name())
		return
	}

	if deploysetRsp.Concurrency != TestConcurrency {
		t.Errorf("[%s] test show deployset fail", t.Name())
		return
	}
}

func TestOpenstackSdk_ShowDeploySet_ERR(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newOpenstackSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&bcc.ShowDeploySetResponse{
		Code: TestCodeBadRequest,
	}, nil, http.StatusOK))

	_, err := s.ShowDeploySet(ctx, &bcc.ShowDeploySetRequest{
		DeploySetID: TestDeploySetID,
		Auth:        &common.Authentication{},
	})
	if !cerrs.ErrBCCDeploysetFail.Is(err) {
		t.Errorf("[%s] test show deployset fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}

func TestOpenstackSdk_CreateCds_OK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newOpenstackSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&bcc.CdsCreateResponse{
		Code: "",
	}, nil, http.StatusOK))

	_, err := s.CreateCds(ctx, &bcc.CdsCreateRequest{
		StorageType:   "SSD_Enhanced",
		CdsSizeInGB:   50,
		PurchaseCount: 1,
		Name:          "test",
		Description:   "test",
		ChargeType:    "Postpaid",
		Auth:          &common.Authentication{},
	})
	if err != nil {
		t.Errorf("[%s] test create cds fail", t.Name())
		return
	}
}

func TestOpenstackSdk_CreateCds_InvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newOpenstackSdk(DefaultServiceName)

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&bcc.CdsCreateResponse{
		Code: TestCodeBadRequest,
	}, nil, http.StatusOK))

	// req param is null
	_, err := s.CreateCds(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// auth param is null
	_, err = s.CreateCds(ctx, &bcc.CdsCreateRequest{})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test create cds fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}

func TestOpenstackSdk_CreateCds_ERR(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newOpenstackSdk(DefaultServiceName)

	// create failed
	s.SetRalCaller(sdk_utils.NewTestRalCaller(nil, cerrs.ErrRalRequestFail, 500))
	_, err := s.CreateCds(ctx, &bcc.CdsCreateRequest{
		StorageType:   "SSD_Enhanced",
		CdsSizeInGB:   50,
		PurchaseCount: 1,
		Name:          "test",
		Description:   "test",
		ChargeType:    "Postpaid",
		Auth:          &common.Authentication{},
	})
	if err == nil {
		t.Errorf("[%s] test create cds fail, err is nil", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	s.SetRalCaller(sdk_utils.NewTestRalCaller(&bcc.CdsCreateResponse{
		Code: TestCodeBadRequest,
	}, nil, http.StatusOK))

	_, err = s.CreateCds(ctx, &bcc.CdsCreateRequest{
		StorageType:   "SSD_Enhanced",
		CdsSizeInGB:   50,
		PurchaseCount: 1,
		Name:          "test",
		Description:   "test",
		ChargeType:    "Postpaid",
		Auth:          &common.Authentication{},
	})
	if !cerrs.ErrBCCLogicalFail.Is(err) {
		t.Errorf("[%s] test create cds fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}
