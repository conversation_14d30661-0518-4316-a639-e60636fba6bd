/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-08
 * File: openstack.go
 */

/*
 * DESCRIPTION
 *   根据openstack.proto生成的implement文件
 */

// Package openstack
package openstack

import (
	"context"
	"net/http"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

// bcc.OpenStackService 的sdk实现
type openstackSdk struct {
	conf *openstackConf
	common.OpenApi
}

// NewDefaultOpenstackSdk - 创建默认的OpenstackSdk
func NewDefaultOpenstackSdk() bcc.OpenStackService {
	return newOpenstackSdk(DefaultServiceName)
}

// NewOpenstackSdk - 创建指定serviceName的OpenstackSdk
func NewOpenstackSdk(serviceName string) bcc.OpenStackService {
	return newOpenstackSdk(serviceName)
}

func newOpenstackSdk(serviceName string) *openstackSdk {
	if serviceName == "" {
		panic("invalid service name")
	}

	s := &openstackSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}
	return s
}

// doRequest - 通用openstack服务请求方法
// auth 必要字段:
//
//	  IamUserId
//		 TransactionId
//		 Credential
//	  ResourceAccount
func (s *openstackSdk) doRequest(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {
	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}

	if privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
		params.PrivateCloudEnv = common.PrivateCloudEnvDbStackAdapterBCC
	}
	// 请求 openapi
	return s.DoRequest(ctx, params, rsp)
}

// doRequestV1 - v1版本openstack服务请求方法
func (s *openstackSdk) doRequestV1(ctx context.Context, actionName, token string,
	httpMethod, uri string, queries map[string]interface{}, headers map[string]interface{},
	req, rsp interface{}) (err error) {
	params := &common.OpenApiParams{
		ActionName: actionName,
		Token:      token,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}
	if privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
		params.PrivateCloudEnv = common.PrivateCloudEnvDbStackAdapterBCC
	}
	// 请求 openapi
	return s.DoRequest(ctx, params, rsp)
}

// ShowServer - ShowServer implement
func (s *openstackSdk) ShowServer(ctx context.Context, req *bcc.ShowServerRequest) (rsp *bcc.ShowServerResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Token == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("null token")
	}
	if req.VmUuid == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("null vm uuid")
	}

	uri := openStackClusterUri + "/" + req.VmUuid

	rsp = &bcc.ShowServerResponse{}
	err = s.doRequestV1(ctx, "ShowServer", req.Token,
		http.MethodGet, uri, nil, nil, nil, rsp)
	if err != nil {
		return nil, err
	}

	return
}

// ShowTransaction - ShowTransaction implement
func (s *openstackSdk) ShowTransaction(ctx context.Context, req *bcc.ShowTransactionRequest) (rsp *bcc.ShowTransactionResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Token == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("null token")
	}
	if req.TransactionId == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("null transaction id")
	}

	uri := showTransactionUri + "/" + req.TransactionId

	rsp = &bcc.ShowTransactionResponse{}
	err = s.doRequestV1(ctx, "ShowTransaction", req.Token,
		http.MethodGet, uri, nil, nil, nil, rsp)
	if err != nil {
		return nil, err
	}

	return
}

// SetShowTransactionForUt - SetShowTransactionForUt implement
func (s *openstackSdk) SetShowTransactionForUt(ctx context.Context, req *bcc.SetTransactionRequest) (rsp *bcc.SetTransactionResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Token == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("null token")
	}

	uri := showTransactionUri + "/" + req.TransactionId
	headers := map[string]interface{}{
		"transaction_status": req.Status,
	}

	rsp = &bcc.SetTransactionResponse{}
	err = s.doRequestV1(ctx, "set_show_transaction_for_ut", req.Token,
		http.MethodGet, uri, nil, headers, nil, rsp)
	if err != nil {
		return nil, err
	}

	return
}

func adaptorExchangeId(ctx context.Context, req *bcc.ExchangeIdRequest) (rsp *bcc.ExchangeIdResponse, err error) {
	var mappings []*bcc.Mappings
	for _, ins := range req.InstanceIds {
		mappings = append(mappings, &bcc.Mappings{
			Id:   ins,
			Uuid: ins,
		})
	}
	logger.DefaultLogger.Trace(ctx, "mock exchange id,req:%s,mappings:%s", base_utils.Format(req), base_utils.Format(mappings))

	return &bcc.ExchangeIdResponse{
		Mappings: mappings,
	}, nil
}

// ExchangeId - Exchange id
// @params req.IdType: "short", "long"
// @params req.ObjectType: "vm", "subnet", "image", "vpc", "security_group"
func (s *openstackSdk) ExchangeId(ctx context.Context, req *bcc.ExchangeIdRequest) (rsp *bcc.ExchangeIdResponse, err error) {
	if privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
		return adaptorExchangeId(ctx, req)
	}

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	rsp = &bcc.ExchangeIdResponse{}
	if err = s.doRequest(ctx, "ExchangeId", req.Auth, http.MethodPost,
		exchangeIdUri, nil, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "exchange id request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("exchange id fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}

	return
}

// BatchCreateServers - 批量建bcc实例
func (s *openstackSdk) BatchCreateServers(ctx context.Context, req *bcc.BatchCreateServersRequest) (rsp *bcc.BatchCreateServersResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	if req.OrderOverTime <= 0 {
		logger.SdkLogger.Warning(ctx, "batchcreateserver's order over time:%d must bigger than 0",
			req.OrderOverTime)
		return nil, cerrs.ErrInvalidParams.Errorf("order over time invalid: %d", req.OrderOverTime)
	}

	if req.CreateInstances == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("instances param is null")
	}
	for _, ins := range req.CreateInstances {
		if ins.SecurityGroupId == "" {
			logger.SdkLogger.Warning(ctx, "secure group id invalid, name: %s", ins.Name)
			return nil, cerrs.ErrInvalidParams.Errorf("secure group id invalid, name: %s", ins.Name)
		}
	}

	var query map[string]interface{}
	if req.ClientToken != "" {
		query = map[string]interface{}{
			"clientToken": req.ClientToken,
		}
	}

	rsp = &bcc.BatchCreateServersResponse{}
	if err = s.doRequest(ctx, "BatchCreateServer", req.Auth, http.MethodPost,
		batchCreateServersUri, query, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "batch create servers request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("batch create servers request fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}

	return
}

// BatchDeleteServers - BatchDeleteServers implement
func (s *openstackSdk) BatchDeleteServers(ctx context.Context, req *bcc.BatchDeleteServersRequest) (rsp *bcc.BatchDeleteServersResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	rsp = &bcc.BatchDeleteServersResponse{}
	if err = s.doRequest(ctx, "BatchDeleteServer", req.Auth, http.MethodPost,
		batchDeleteServersUri, nil, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "batch delete servers request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("batch delete servers request fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}

	return
}

// ShowOrder - ShowOrder implement
func (s *openstackSdk) ShowOrder(ctx context.Context, req *bcc.ShowOrderRequest) (rsp *bcc.ShowOrderResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	queries := map[string]interface{}{
		"orderId": req.OrderId,
	}

	rsp = &bcc.ShowOrderResponse{}
	if err = s.doRequest(ctx, "show_order", req.Auth, http.MethodGet,
		showOrderUri, queries, nil, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "show order request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("show order request fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}

	return
}

// ShowInstanceInfo - ShowInstanceInfo implement
func (s *openstackSdk) ShowInstanceInfo(ctx context.Context, req *bcc.ShowInstanceRequest) (rsp *bcc.ShowInstanceResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	uri := instanceOperationUri + req.VmUuid

	rsp = &bcc.ShowInstanceResponse{}
	if err = s.doRequest(ctx, "show_instance_info", req.Auth, http.MethodGet,
		uri, nil, nil, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "show instance info request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("show instance info request fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}

	return
}

// ResizeInstance - ResizeInstance implement
func (s *openstackSdk) ResizeInstance(ctx context.Context, req *bcc.ResizeInstanceRequest) (rsp *bcc.ResizeInstanceResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	uri := instanceOperationUri + req.VmId

	queries := map[string]interface{}{
		"resize": "",
	}

	rsp = &bcc.ResizeInstanceResponse{}
	if err = s.doRequest(ctx, "resize_instance", req.Auth, http.MethodPut,
		uri, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "resize instance info request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("resize instance info request fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}

	return
}

// InstanceAttachedCdsList - InstanceAttachedCdsList implement
func (s *openstackSdk) InstanceAttachedCdsList(ctx context.Context, req *bcc.CdsMountListRequest) (rsp *bcc.CdsMountListResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	uri := volumeOperationUri

	queries := map[string]interface{}{
		"instanceId": req.VmId,
	}

	rsp = &bcc.CdsMountListResponse{}
	if err = s.doRequest(ctx, "instance_attached_cds_list", req.Auth, http.MethodGet,
		uri, queries, nil, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "get instance cds attached list fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("get instance cds attached list fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}

	return
}

// ResizeCds - ResizeCds implement
func (s *openstackSdk) ResizeCds(ctx context.Context, req *bcc.CdsResizeRequest) (rsp *bcc.CdsResizeResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	uri := volumeOperationUri + "/" + req.VolumeId

	queries := map[string]interface{}{
		"resize": "",
	}

	rsp = &bcc.CdsResizeResponse{}
	if err = s.doRequest(ctx, "resize_cds", req.Auth, http.MethodPut,
		uri, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "resize cds %s failed, code: %s, message: %s, requestId: %s",
			req.VolumeId, rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("resize cds %s failed, code: %s, message: %s",
			req.VolumeId, rsp.Code, rsp.Message)
	}

	return
}

// CreateDeploySet - CreateDeploySet implement
func (s *openstackSdk) CreateDeploySet(ctx context.Context, req *bcc.CreateDeploySetRequest) (rsp *bcc.DeploySetIdsResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	rsp = &bcc.DeploySetIdsResponse{}
	if err = s.doRequest(ctx, "create_deploy_set_id", req.Auth, http.MethodPost,
		deploySetCreateUri, nil, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "create deploy set id fail, transId: %s, code: %s, message: %s, requestId: %s",
			req.Auth.TransactionId, rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("create deploy set id fail, transId :%s, code: %s, message: %s",
			req.Auth.TransactionId, rsp.Code, rsp.Message)
	}

	return
}

// ShowDeploySet
func (s *openstackSdk) ShowDeploySet(ctx context.Context, req *bcc.ShowDeploySetRequest) (rsp *bcc.ShowDeploySetResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DeploySetID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("deployset id is empty")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	uri := deploySetUri + "/" + req.DeploySetID
	rsp = &bcc.ShowDeploySetResponse{}
	if err = s.doRequest(ctx, "show_deploy_set", req.Auth, http.MethodGet,
		uri, nil, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "show deploy set id fail, transId: %s, code: %s, message: %s, requestId: %s",
			req.Auth.TransactionId, rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCDeploysetFail.Errorf("show deploy set id fail, transId :%s, code: %s, message: %s",
			req.Auth.TransactionId, rsp.Code, rsp.Message)
	}

	return
}

func (s *openstackSdk) GetCdsStockWithZone(ctx context.Context, req *bcc.GetCdsStockRequest) (rsp *bcc.GetCdsStockResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	uri := getCdsStockUri

	rsp = &bcc.GetCdsStockResponse{}
	if err = s.doRequest(ctx, "get_cds_stock", req.Auth, http.MethodPost,
		uri, nil, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "get cds stock request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("get cds stock request fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// 获取子网详情  获取类似   zoneA <===> cn-bj-a 映射信息
func (s *openstackSdk) SubnetDetail(ctx context.Context, req *bcc.GetSubnetDetailRequest) (rsp *bcc.GetSubnetDetailResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.SubnetID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("null subnetId")
	}

	uri := subnetDetailUri + "/" + req.SubnetID

	rsp = &bcc.GetSubnetDetailResponse{}
	if err = s.doRequest(ctx, "get_subnet_detail", req.Auth, http.MethodGet,
		uri, nil, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "get subnet detail request fail, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("get subnet detail request fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}

// CreateCds - CreateCds implement
func (s *openstackSdk) CreateCds(ctx context.Context, req *bcc.CdsCreateRequest) (rsp *bcc.CdsCreateResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}

	uri := volumeOperationUri
	rsp = &bcc.CdsCreateResponse{}
	if err = s.doRequest(ctx, "create_cds", req.Auth, http.MethodPost,
		uri, nil, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "create cds failed, code: %s, message: %s, requestId: %s",
			rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("create cds failed, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}

	return
}

// AttachCds - AttachCds implement
func (s *openstackSdk) AttachCds(ctx context.Context, req *bcc.CdsAttachRequest) (rsp *bcc.CdsAttachResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.VolumeId == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("null volumeId")
	}

	uri := volumeOperationUri + "/" + req.VolumeId

	queries := map[string]interface{}{
		"attach": "",
	}

	rsp = &bcc.CdsAttachResponse{}
	if err = s.doRequest(ctx, "attach_cds", req.Auth, http.MethodPut,
		uri, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "attach cds %s for instance %s failed, code: %s, message: %s, requestId: %s",
			req.VolumeId, req.InstanceId, rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("attach cds %s for instance %s failed, code: %s, message: %s",
			req.VolumeId, req.InstanceId, rsp.Code, rsp.Message)
	}

	return
}

// DetachCds - DetachCds implement
func (s *openstackSdk) DetachCds(ctx context.Context, req *bcc.CdsDetachRequest) (rsp *bcc.CdsDetachResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.VolumeId == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("null volumeId")
	}

	uri := volumeOperationUri + "/" + req.VolumeId

	queries := map[string]interface{}{
		"detach": "",
	}

	rsp = &bcc.CdsDetachResponse{}
	if err = s.doRequest(ctx, "detach_cds", req.Auth, http.MethodPut,
		uri, queries, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		logger.SdkLogger.Warning(ctx, "detach cds %s for instance %s failed, code: %s, message: %s, requestId: %s",
			req.VolumeId, req.InstanceId, rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("detach cds %s for instance %s failed, code: %s, message: %s",
			req.VolumeId, req.InstanceId, rsp.Code, rsp.Message)
	}

	return
}

// DeleteCds - DeleteCds implement
func (s *openstackSdk) DeleteCds(ctx context.Context, req *bcc.CdsDeleteRequest) (rsp *bcc.CdsDeleteResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.VolumeId == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("null volumeId")
	}

	uri := volumeOperationUri + "/" + req.VolumeId

	rsp = &bcc.CdsDeleteResponse{}
	if err = s.doRequest(ctx, "delete_cds", req.Auth, http.MethodPost,
		uri, nil, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		// 当 Code 不为空时，如果HTTP Code为404时resp Code为"NoSuchObject"，
		// 认为Delete成功，直接返回
		if rsp.Code == "NoSuchObject" {
			return
		}

		logger.SdkLogger.Warning(ctx, "delete cds %s failed, code: %s, message: %s, requestId: %s",
			req.VolumeId, rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("delete cds %s failed, code: %s, message: %s",
			req.VolumeId, rsp.Code, rsp.Message)
	}

	return
}

// 获取cds详情 - CdsDetail implement
func (s *openstackSdk) GetCdsDetail(ctx context.Context, req *bcc.GetCdsDetailRequest) (rsp *bcc.GetCdsDetailResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("auth param is null")
	}
	if req.VolumeId == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("null volumeId")
	}

	uri := volumeOperationUri + "/" + req.VolumeId

	rsp = &bcc.GetCdsDetailResponse{}
	if err = s.doRequest(ctx, "get_cds_detail", req.Auth, http.MethodGet,
		uri, nil, req, rsp); err != nil {
		return nil, err
	}
	if rsp.Code != "" {
		// 当 Code 不为空时，如果HTTP Code为404时resp Code为"NoSuchObject"，
		// 认为get成功，直接返回
		if rsp.Code == "NoSuchObject" {
			return
		}

		logger.SdkLogger.Warning(ctx, "get cds %s detail request fail, code: %s, message: %s, requestId: %s",
			req.VolumeId, rsp.Code, rsp.Message, rsp.Requestid)
		return nil, cerrs.ErrBCCLogicalFail.Errorf("get cds %s detail request fail, code: %s, message: %s",
			req.VolumeId, rsp.Code, rsp.Message)
	}
	return
}
