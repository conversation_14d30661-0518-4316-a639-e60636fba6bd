/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/24 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file openstack_conf.go
 * <AUTHOR>
 * @date 2023/03/24 15:15:47
 * @brief openstack conf

 *
 **/

package openstack

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "Openstack"

// openstackConf definition
type openstackConf struct {
	Product string `toml:"Product,omitempty"`
}

var openstackConfMap = &sync.Map{}

func getConf(serviceName string) *openstackConf {
	if conf, ok := openstackConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*openstackConf); ok {
			return conf
		}
	}

	conf := &openstackConf{}
	conf.mustLoad(serviceName)

	openstackConfMap.Store(serviceName, conf)

	return conf
}

func (conf *openstackConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
