package neutron

import (
	"context"
	"net/http"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

// bcc.NeutronVpcService 的sdk实现
type neutronVpcSdk struct {
	conf *endpointConf
	common.OpenApi
}

// NewDefaultNeutronVpcSdk - 创建默认的neutronVpcSdk
func NewDefaultNeutronVpcSdk() bcc.NeutronVpcService {
	return newNeutronVpcSdk(DefaultVpcServiceName)
}

// NewNeutronVpcSdk - 创建指定serviceName的neutronVpcSdk
func NewNeutronVpcSdk(serviceName string) bcc.NeutronVpcService {
	return newNeutronVpcSdk(serviceName)
}

func newNeutronVpcSdk(serviceName string) *neutronVpcSdk {
	if serviceName == "" {
		panic("invalid service name")
	}

	s := &neutronVpcSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}
	return s
}

// doNeutronVpcRequest - 通用openstack服务请求方法
func (s *neutronVpcSdk) doNeutronVpcRequest(ctx context.Context, actionName string, token string,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	params := &common.OpenApiParams{
		ActionName: actionName,
		Token:      token,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}

	// 请求 openapi
	return s.DoRequest(ctx, params, rsp)
}

// GetSubnetIpType 获取ip类型
// 不存在的subnetIp会报404
func (s neutronVpcSdk) GetSubnetIpType(ctx context.Context, req *bcc.GetSubnetIpTypeRequest) (rsp *bcc.GetSubnetIpTypeResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := SubnetUri + "/" + req.SubnetId + ".json"

	rsp = &bcc.GetSubnetIpTypeResponse{}
	if err = s.doNeutronVpcRequest(ctx, "GetSubnetIpType", req.Token, http.MethodGet,
		uri, nil, nil, rsp); err != nil {
		return nil, err
	}

	if rsp.NeutronError != nil {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("GetSubnetIpType request fail,req:%s , NeutronVpcError Msg: %s , NeutronVpcError Type: %s , NeutronVpcError Detail: %s",
			base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail)
	}

	return
}
