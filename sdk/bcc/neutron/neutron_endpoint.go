/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/06/06 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file neutron_endpoint.go
 * <AUTHOR>
 * @date 2022/06/06 16:10:56
 * @brief neutron endpoint sdk
 *
 **/

package neutron

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bce_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
)

// neutronEndpointSdk sdk definition
type neutronEndpointSdk struct {
	conf *endpointConf
	common.OpenApi
	signer    bce_utils.Signer
	ralCaller sdk_utils.RalCaller
}

// NewDefaultNeutronEndpointSdk - 创建默认neutronEndpintSdk
func NewDefaultNeutronEndpointSdk() bcc.NeutronEndpointService {
	return newNeutronEndpointSdk(DefaultEndpointServiceName)
}

// NewNeutronEndpointSdk - 创建指定serviceName的neutronEndpointSdk
func NewNeutronEndpointSdk(serviceName string) bcc.NeutronEndpointService {
	return newNeutronEndpointSdk(serviceName)
}

func newNeutronEndpointSdk(serviceName string) *neutronEndpointSdk {
	if serviceName == "" {
		panic("invalid service name")
	}

	s := &neutronEndpointSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
		signer:  bce_utils.NewV1Signer(),
		ralCaller: sdk_utils.NewRalCaller(
			serviceName,
			sdk_utils.ROptGenCurlLog(logit.TraceLevel),
		),
	}
	return s
}

// doNeutronVpcRequest - 通用openstack服务请求方法
func (s *neutronEndpointSdk) doNeutronEndpointRequest(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}

	// 请求 openapi
	return s.DoRequest(ctx, params, rsp)
}

// doNeutronVpcRequestWithOption - 通用openstack服务请求方法
func (s *neutronEndpointSdk) doNeutronVpcRequestWithResourceID(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {
	// 获取当前时间
	tmStr := time.Now().UTC().Format("2006-01-02T15:04:05Z")
	rawContent := fmt.Sprintf("%s/%s", s.conf.ResourceID, tmStr)

	encryptAccountID, err := crypto_utils.AesECBEncryptString(rawContent, s.conf.EncryptKey,
		crypto_utils.PKCS7PaddingType, crypto_utils.HexCodecType)
	if err != nil {
		logger.SdkLogger.Warning(ctx, "aes ecb encrypt fail, content: %s, key: %s", rawContent, s.conf.EncryptKey)
		return cerrs.ErrSTSEncryptAccountFail.Wrap(err)
	}

	productResource := "scs"
	if s.conf.Product != "" {
		productResource = s.conf.Product
	}
	headers := map[string]interface{}{
		"resource-accountId": strings.ToUpper(encryptAccountID),
		"resource-source":    productResource,
	}

	params := &common.OpenApiParams{
		ActionName: actionName,
		Auth:       auth,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Headers:    headers,
		Product:    s.conf.Product,
	}

	_, err = s.DoRequestWithCustomizeHeader(ctx, params, rsp)

	return err
}

// CreateEndpoint 创建服务网卡
func (s *neutronEndpointSdk) CreateEndpoint(ctx context.Context, req *bcc.CreateEndpointRequest) (rsp *bcc.CreateEndpointResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := EndpointUri

	rsp = &bcc.CreateEndpointResponse{}
	if err = s.doNeutronVpcRequestWithResourceID(ctx, "CreateEndpoint", req.Auth, http.MethodPost,
		uri, nil, req, rsp); err != nil {
		return nil, err
	}

	if rsp.Code != "" {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("create endpoint request fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}

	return
}

// UpdateEndpoint 更新服务网卡
func (s *neutronEndpointSdk) UpdateEndpoint(ctx context.Context, req *bcc.UpdateEndpointRequest) (err error) {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := EndpointUri + "/" + req.EndpointID

	if err = s.doNeutronEndpointRequest(ctx, "UpdateEndpoint", req.Auth, http.MethodPut,
		uri, nil, req, nil); err != nil {
		return err
	}

	return
}

// DeleteEndpoint 删除服务网卡
func (s *neutronEndpointSdk) DeleteEndpoint(ctx context.Context, req *bcc.DeleteEndpointRequest) (err error) {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := EndpointUri + "/" + req.EndpointID

	if err = s.doNeutronVpcRequestWithResourceID(ctx, "DeleteEndpoint", req.Auth, http.MethodDelete,
		uri, nil, req, nil); err != nil {
		return err
	}

	return
}

// GetEndpointDetail 获取服务网卡详情
func (s *neutronEndpointSdk) GetEndpointDetail(ctx context.Context, req *bcc.GetEndpointDetailRequest) (rsp *bcc.GetEndpointDetailResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := EndpointUri + "/" + req.EndpointID
	rsp = &bcc.GetEndpointDetailResponse{}

	if err = s.doNeutronVpcRequestWithResourceID(ctx, "GetEndpointDetail", req.Auth, http.MethodGet,
		uri, nil, req, rsp); err != nil {
		return nil, err
	}

	if rsp.Code != "" {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("get endpoint detail request fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}

	return
}

// GetEndpointDetail 获取可挂载的公共服务
func (s *neutronEndpointSdk) GetPublicService(ctx context.Context, req *bcc.GetPublicServiceRequest) (rsp *bcc.GetPublicServiceResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := EndpointUri + "/" + "publicService"
	rsp = &bcc.GetPublicServiceResponse{}

	if err = s.doNeutronEndpointRequest(ctx, "GetPublicService", req.Auth, http.MethodGet,
		uri, nil, req, rsp); err != nil {
		return nil, err
	}

	if rsp.Code != "" {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("get public services request fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}

	return
}

// GetEndpointDetail 获取服务网卡详情
func (s *neutronEndpointSdk) GetEndpointList(ctx context.Context, req *bcc.GetEndpointListRequest) (rsp *bcc.GetEndpointListResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	queries := map[string]interface{}{
		"vpcId":     req.VpcID,
		"name":      req.Name,
		"IpAddress": req.IPAddress,
		"status":    req.Status,
		"subnetId":  req.SubnetID,
		"service":   req.Service,
		"marker":    req.Marker,
	}
	if req.MaxKeys != 0 {
		queries["maxKeys"] = req.MaxKeys
	}
	rsp = &bcc.GetEndpointListResponse{}

	if err = s.doNeutronVpcRequestWithResourceID(ctx, "GetEndpointList", req.Auth, http.MethodGet,
		EndpointUri, queries, nil, rsp); err != nil {
		return nil, err
	}

	if rsp.Code != "" {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("get endpoint list request fail, code: %s, message: %s",
			rsp.Code, rsp.Message)
	}
	return
}
