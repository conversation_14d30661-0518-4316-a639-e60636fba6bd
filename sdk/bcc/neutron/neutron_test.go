package neutron

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/spf13/cast"

	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
)

const (
	TestIamUserId = "a318fe1fe9f5464d92478dab0aa4f5ff"
)

func TestBccSdk_CreateSecurityGroup(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newNeutronSdk(DefaultServiceName)

	stsSdk := sts.NewStsSdk(sts.DefaultServiceName)
	req := &sts.GetAssumeRoleRequest{
		IamUserId:     TestIamUserId,
		TransactionId: uuid.New().String(),
	}
	stsRet, err := stsSdk.GetAssumeRole(ctx, req)
	if err != nil {
		t.Fatalf("get assume role fail")
	}

	_, err = s.CreateSecurityGroup(ctx, &bcc.CreateSecurityGroupRequest{SecurityGroup: &bcc.SecurityGroupReq{
		Name:        "FFFFFF",
		Description: stsRet.Token.Id,
		Creator:     "FFFFFF",
		VpcId:       "FFFFFF",
	}, Token: stsRet.Token.Id})
	fmt.Println(err) // TODO FFFFFF is not a valid uuid
	//if err != nil {
	//	t.Fatalf(err.Error())
	//}
}

func TestBccSdk_CreateSecurityGroupRules(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := NewDefaultNeutronSdk()

	_, err := s.CreateSecurityGroupRules(ctx, &bcc.CreateSecurityGroupRulesRequest{
		SecurityGroupRule: &bcc.SecurityGroupRules{
			Direction:       "ingress",
			Ethertype:       "IPv4",
			PortRangeMax:    22,
			PortRangeMin:    22,
			Protocol:        "tcp",
			RemoteIpPrefix:  "*************",
			SecurityGroupId: "7e08bddc-b384-485f-8d2f-7adc9b3bd316",
			Creator:         "scs",
		},
		Token: "97ab503d856947368d994c255f3d8c42",
	})
	fmt.Println(err) // TODO Authentication required
	//if err != nil {
	//	t.Fatalf(err.Error())
	//}
}

func TestVpcSdk_GetSubnetIpType(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	s := newNeutronVpcSdk(DefaultVpcServiceName)

	stsSdk := sts.NewStsSdk(sts.DefaultServiceName)
	req := &sts.GetAssumeRoleRequest{
		IamUserId:     "a318fe1fe9f5464d92478dab0aa4f5ff",
		TransactionId: uuid.New().String(),
	}
	stsRet, err := stsSdk.GetAssumeRole(ctx, req)
	if err != nil {
		t.Fatalf("get assume role fail")
	}

	rsp, err := s.GetSubnetIpType(ctx, &bcc.GetSubnetIpTypeRequest{
		SubnetId:      "2a834926-3d74-4a6b-9db2-c050de50a728",
		Token:         stsRet.Token.Id,
		TransactionId: "3c0b85ac-fff8-11eb-a907-7cd30a6975e0",
	})
	if err != nil {
		t.Fatalf(err.Error())
	}
	bs, err := json.Marshal(rsp)
	fmt.Printf("result:%s\n", cast.ToString(bs))
}
