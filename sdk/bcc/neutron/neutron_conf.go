/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/06/09 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file neutron_conf.go
 * <AUTHOR>
 * @date 2022/06/09 15:15:47
 * @brief neutron endpoint conf

 *
 **/

package neutron

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "Endpoint"

// endpointConf definition
type endpointConf struct {
	// encrypt resource account id
	ResourceID string `toml:"ResourceID,omitempty"`
	EncryptKey string `toml:"EncryptKey,omitempty"`
	Product    string `toml:"Product,omitempty"`
}

var endpointConfMap = &sync.Map{}

func getConf(serviceName string) *endpointConf {
	if conf, ok := endpointConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*endpointConf); ok {
			return conf
		}
	}

	conf := &endpointConf{}
	conf.mustLoad(serviceName)

	endpointConfMap.Store(serviceName, conf)

	return conf
}

func (conf *endpointConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
