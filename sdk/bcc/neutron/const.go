package neutron

const DefaultServiceName = "neutron"
const DefaultVpcServiceName = "neutron_vpc"
const DefaultEndpointServiceName = "neutron_endpoint"

const (
	networkAdminUri       = "/v2.0/"
	securityGroupsUri     = "/v2.0/security-groups"
	securityGroupRulesUri = "/v2.0/security-group-rules"
	SubnetUri             = "/v2.0/subnets"
	EndpointUri           = "/v1/endpoint"
)

// NeutronErrType
const (
	NeutronErrRuleEx = "SecurityGroupRuleExists"
)
