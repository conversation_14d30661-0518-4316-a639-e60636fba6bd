package neutron

import (
	"context"
	"net/http"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

// bcc.NeutronService 的sdk实现
type neutronSdk struct {
	conf *endpointConf
	common.OpenApi
}

// NewDefaultNeutronSdk - 创建默认的neutronSdk
func NewDefaultNeutronSdk() bcc.NeutronService {
	return newNeutronSdk(DefaultServiceName)
}

// NewNeutronSdk - 创建指定serviceName的neutronSdk
func NewNeutronSdk(serviceName string) bcc.NeutronService {
	return newNeutronSdk(serviceName)
}

func newNeutronSdk(serviceName string) *neutronSdk {
	if serviceName == "" {
		panic("invalid service name")
	}

	s := &neutronSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}
	return s
}

// doNeutronRequest - 通用openstack服务请求方法
func (s *neutronSdk) doNeutronRequest(ctx context.Context, actionName string, token string,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	params := &common.OpenApiParams{
		ActionName: actionName,
		Token:      token,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}

	// 请求 openapi
	return s.DoRequest(ctx, params, rsp)
}

func (s *neutronSdk) doNeutronRequestEx(ctx context.Context, actionName string, token string,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	params := &common.OpenApiParams{
		ActionName: actionName,
		Token:      token,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Product:    s.conf.Product,
	}

	// 请求 openapi
	return s.DoRequest(ctx, params, rsp,
		sdk_utils.ROptFinalChecker(func(ctx context.Context, httpRsp *sdk_utils.HttpResponse, result interface{}, invoker sdk_utils.FinalCheckFunc) error {
			if httpRsp.StatusCode == 409 {
				return nil
			}
			return invoker(ctx, httpRsp, result)
		}))
}

// CreateSecurityGroup - CreateSecurityGroup implement
func (s *neutronSdk) CreateSecurityGroup(ctx context.Context, req *bcc.CreateSecurityGroupRequest) (rsp *bcc.CreateSecurityGroupResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &bcc.CreateSecurityGroupResponse{}
	if err = s.doNeutronRequest(ctx, "CreateSecurityGroup", req.Token, http.MethodPost,
		securityGroupsUri, nil, req, rsp); err != nil {
		return nil, err
	}

	if rsp.NeutronError != nil {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("CreateSecurityGroup request fail,req:%s , NeutronError Msg: %s , NeutronError Type: %s , NeutronError Detail: %s",
			base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail)
	}

	return
}

// ShowSecurityGroup - ShowSecurityGroup implement
func (s *neutronSdk) ShowSecurityGroup(ctx context.Context, req *bcc.ShowSecurityGroupRequest) (rsp *bcc.ShowSecurityGroupResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := securityGroupsUri + "/" + req.SecurityGroupId

	rsp = &bcc.ShowSecurityGroupResponse{}
	if err = s.doNeutronRequest(ctx, "ShowSecurityGroup", req.Token, http.MethodGet,
		uri, nil, nil, rsp); err != nil {
		return nil, err
	}

	if rsp.NeutronError != nil {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("ShowSecurityGroup request fail,req:%s , NeutronError Msg: %s , NeutronError Type: %s , NeutronError Detail: %s",
			base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail)
	}

	return
}

// DeleteSecurityGroup - DeleteSecurityGroup implement
func (s *neutronSdk) DeleteSecurityGroup(ctx context.Context, req *bcc.DeleteSecurityGroupRequest) (rsp *bcc.DeleteSecurityGroupResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := securityGroupsUri + "/" + req.SecurityGroupId

	rsp = &bcc.DeleteSecurityGroupResponse{}
	if err = s.doNeutronRequest(ctx, "DeleteSecurityGroup", req.Token, http.MethodDelete,
		uri, nil, nil, rsp); err != nil {
		return nil, err
	}

	if rsp.NeutronError != nil {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("DeleteSecurityGroup request fail,req:%s , NeutronError Msg: %s , NeutronError Type: %s , NeutronError Detail: %s",
			base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail)
	}

	return
}

// CreateSecurityGroupRules - CreateSecurityGroupRules implement
func (s *neutronSdk) CreateSecurityGroupRules(ctx context.Context, req *bcc.CreateSecurityGroupRulesRequest) (rsp *bcc.CreateSecurityGroupRulesResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp = &bcc.CreateSecurityGroupRulesResponse{}
	if err = s.doNeutronRequestEx(ctx, "CreateSecurityGroupRules", req.Token, http.MethodPost,
		securityGroupRulesUri, nil, req, rsp); err != nil {
		return nil, err
	}

	if rsp.NeutronError != nil {
		if rsp.NeutronError.Type == NeutronErrRuleEx {
			logger.SdkLogger.Trace(ctx, "CreateSecurityGroupRules request fail,req:%s , NeutronError Msg: %s , NeutronError Type: %s , NeutronError Detail: %s, Token_Id:%s",
				base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail, req.Token)
			return
		}
		return nil, cerrs.ErrBCCNeutronFail.Errorf("CreateSecurityGroupRules request fail,req:%s , NeutronError Msg: %s , NeutronError Type: %s , NeutronError Detail: %s, Token_Id:%s",
			base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail, req.Token)
	}

	return
}

// DeleteSecurityGroupRules - DeleteSecurityGroupRules implement
func (s *neutronSdk) DeleteSecurityGroupRules(ctx context.Context, req *bcc.DeleteSecurityGroupRulesRequest) (rsp *bcc.DeleteSecurityGroupRulesResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := securityGroupRulesUri + "/" + req.SecurityGroupRulesId

	rsp = &bcc.DeleteSecurityGroupRulesResponse{}
	if err = s.doNeutronRequest(ctx, "DeleteSecurityGroupRules", req.Token, http.MethodDelete,
		uri, nil, nil, rsp); err != nil {
		return nil, err
	}

	if rsp.NeutronError != nil {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("DeleteSecurityGroupRules request fail,req:%s , NeutronError Msg: %s , NeutronError Type: %s , NeutronError Detail: %s",
			base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail)
	}

	return
}

// ListNetwork - ListNetwork implement
func (s *neutronSdk) ListNetwork(ctx context.Context, req *bcc.ListNetworkRequest) (rsp *bcc.ListNetworkResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := networkAdminUri + "vnetworks.json"

	rsp = &bcc.ListNetworkResponse{}
	if err = s.doNeutronRequest(ctx, "ListNetwork", req.Token, http.MethodGet,
		uri, nil, nil, rsp); err != nil {
		return nil, err
	}

	if rsp.NeutronError != nil {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("ListNetwork request fail,req:%s , NeutronError Msg: %s , NeutronError Type: %s , NeutronError Detail: %s",
			base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail)
	}

	return
}

// GetDnsmasq - GetDnsmasq implement
func (s *neutronSdk) GetDnsmasq(ctx context.Context, req *bcc.GetDnsMasqRequest) (rsp *bcc.PortsResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := networkAdminUri + "ports.json"

	queries := map[string]interface{}{
		"network_id":   req.VpcId,
		"device_owner": "network:dhcp",
	}

	rsp = &bcc.PortsResponse{}
	if err = s.doNeutronRequest(ctx, "get_dnsmasq", req.Token, http.MethodGet,
		uri, queries, nil, rsp); err != nil {
		return nil, err
	}

	if rsp.NeutronError != nil {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("get_dnsmasq request fail,req:%s , NeutronError Msg: %s , NeutronError Type: %s , NeutronError Detail: %s",
			base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail)
	}

	return
}

// SetDnsmasq - SetDnsmasq implement
func (s *neutronSdk) SetDnsmasq(ctx context.Context, req *bcc.SetDnsMasqRequest) (rsp *bcc.SetDnsMasqResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := networkAdminUri + "vpcs/" + req.VpcId + ".json"

	rsp = &bcc.SetDnsMasqResponse{}
	if err = s.doNeutronRequest(ctx, "set_dnsmasq", req.Token, http.MethodPut,
		uri, nil, req, rsp); err != nil {
		return nil, err
	}

	if rsp.NeutronError != nil {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("set_dnsmasq request fail,req:%s , NeutronError Msg: %s , NeutronError Type: %s , NeutronError Detail: %s",
			base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail)
	}

	return
}

// GetPorts - GetPorts implement
func (s *neutronSdk) GetPorts(ctx context.Context, req *bcc.GetPortsRequest) (rsp *bcc.PortsResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := networkAdminUri + "ports.json"

	queries := map[string]interface{}{
		"device_id": req.VmUuid,
	}

	rsp = &bcc.PortsResponse{}
	if err = s.doNeutronRequest(ctx, "get_port_id", req.Token, http.MethodGet,
		uri, queries, nil, rsp); err != nil {
		return nil, err
	}

	if rsp.NeutronError != nil {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("get_port_id request fail,req:%s , NeutronError Msg: %s , NeutronError Type: %s , NeutronError Detail: %s",
			base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail)
	}

	return
}

// UnbindSecurityGroup - UnbindSecurityGroup implement
func (s *neutronSdk) UnbindSecurityGroup(ctx context.Context, req *bcc.UnbindSecurityGroupRequest) (rsp *bcc.PortsResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := networkAdminUri + "ports/" + req.PortId + ".json"

	stringPosts := "{\"port\": {\"security_groups\": []}}"

	rsp = &bcc.PortsResponse{}
	if err = s.doNeutronRequest(ctx, "unbind_security_groups", req.Token, http.MethodPut,
		uri, nil, stringPosts, rsp); err != nil {
		return nil, err
	}

	if rsp.NeutronError != nil {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("unbind_security_groups request fail,req:%s , NeutronError Msg: %s , NeutronError Type: %s , NeutronError Detail: %s",
			base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail)
	}

	return
}

// GetSubnetInfo - GetSubnetInfo implement
func (s *neutronSdk) GetSubnetInfo(ctx context.Context, req *bcc.GetSubnetInfoRequest) (rsp *bcc.GetSubnetInfoResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := networkAdminUri + "subnets/" + req.SubnetId

	rsp = &bcc.GetSubnetInfoResponse{}
	if err = s.doNeutronRequest(ctx, "GetSubnetInfo", req.Token, http.MethodGet,
		uri, nil, nil, rsp); err != nil {
		return nil, err
	}

	if rsp.NeutronError != nil {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("GetSubnetInfo request fail,req:%s , NeutronError Msg: %s , NeutronError Type: %s , NeutronError Detail: %s",
			base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail)
	}

	return
}

func (s *neutronSdk) GetSubnets(ctx context.Context, req *bcc.GetSubnetsRequest) (rsp *bcc.GetSubnetsResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := networkAdminUri + "subnets"

	rsp = &bcc.GetSubnetsResponse{}
	if err = s.doNeutronRequest(ctx, "GetSubnets", req.Token, http.MethodGet,
		uri, nil, nil, rsp); err != nil {
		return nil, err
	}

	if rsp.NeutronError != nil {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("GetSubnets request fail,req:%s , NeutronError Msg: %s , NeutronError Type: %s , NeutronError Detail: %s",
			base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail)
	}

	return
}

// GetSubnetIpId - GetSubnetIpId implement
func (s *neutronSdk) GetSubnetIpId(ctx context.Context, req *bcc.GetSubnetIpIdRequest) (rsp *bcc.GetSubnetIpIdResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	uri := networkAdminUri + "ports.json"

	queries := map[string]interface{}{
		"fixed_ips": "subnet_id=" + req.SubnetId,
		"fields":    "id",
	}

	rsp = &bcc.GetSubnetIpIdResponse{}
	if err = s.doNeutronRequest(ctx, "GetSubnetIpId", req.Token, http.MethodGet,
		uri, queries, nil, rsp); err != nil {
		return nil, err
	}

	if rsp.NeutronError != nil {
		return nil, cerrs.ErrBCCNeutronFail.Errorf("GetSubnetIpId request fail,req:%s , NeutronError Msg: %s , NeutronError Type: %s , NeutronError Detail: %s",
			base_utils.Format(req), rsp.NeutronError.Message, rsp.NeutronError.Type, rsp.NeutronError.Detail)
	}

	return
}
