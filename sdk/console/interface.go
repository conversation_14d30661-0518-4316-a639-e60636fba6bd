/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-01-24
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   console为scs封装的接口
 */

// Package console
package console

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type GetSecurityGroupIdsRequest struct {
	InstanceId   string                 `json:"instanceId"`
	InstanceType string                 `json:"instanceType"`
	Auth         *common.Authentication `json:"-"`
}

type Result struct {
	ActiveRules    []*ActiveRules    `json:"activeRules"`
	SecurityGroups []*SecurityGroups `json:"securityGroups"`
}

type ActiveRules struct {
	Direction           string `json:"direction"`
	Ethertype           string `json:"ethertype"`
	Id                  string `json:"id"`
	Name                string `json:"name"`
	PortRange           string `json:"portRange"`
	Protocol            string `json:"protocol"`
	RemoteGroupId       string `json:"remoteGroupId"`
	RemoteGroupName     string `json:"remoteGroupName"`
	RemoteIP            string `json:"remoteIP"`
	SecurityGroupId     string `json:"securityGroupId"`
	SecurityGroupRuleId string `json:"securityGroupRuleId"`
	SecurityGroupUuid   string `json:"securityGroupUuid"`
	TenantId            string `json:"tenantId"`
}

type SecurityGroups struct {
	Desc            string         `json:"desc"`
	Id              string         `json:"id"`
	Name            string         `json:"name"`
	Rules           []*ActiveRules `json:"rules"`
	SecurityGroupId string         `json:"securityGroupId"`
	TenantId        string         `json:"tenantId"`
	VpcId           string         `json:"vpcId"`
	VpcName         string         `json:"vpcName"`
}

type GetSecurityGroupIdsResponse struct {
	Result    *Result `json:"result"`
	Status    int32   `json:"status"`
	Success   bool    `json:"success"`
	Code      string  `json:"code"`
	Message   string  `json:"message"`
	RequestId string  `json:"requestId"`
}

type GetSecurityGroupIdsResp struct {
	Result    *Result `json:"result"`
	Status    int32   `json:"status"`
	Success   bool    `json:"success"`
	Code      string  `json:"code"`
	RequestId string  `json:"requestId"`
}

type UnbindVpcSecurityGroupsRequest struct {
	InstanceId         string                 `json:"instanceId"`
	InstanceType       string                 `json:"instanceType"`
	SecurityGroupUuids []string               `json:"securityGroupUuids"`
	Auth               *common.Authentication `json:"-"`
}

type UnbindVpcSecurityGroupsResponse struct {
	Status    int32  `json:"status"`
	Success   bool   `json:"success"`
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
}

type ConsoleService interface {
	GetSecurityGroupIds(ctx context.Context, req *GetSecurityGroupIdsRequest) (rsp *GetSecurityGroupIdsResponse, err error)
	UnbindVpcSecurityGroups(ctx context.Context, req *UnbindVpcSecurityGroupsRequest) (rsp *UnbindVpcSecurityGroupsResponse, err error)
	GetSecurityGroupIdList(ctx context.Context, req *GetSecurityGroupIdsRequest) (rsp *GetSecurityGroupIdsResp, err error)
}
