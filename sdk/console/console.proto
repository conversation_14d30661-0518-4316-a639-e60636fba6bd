// protofsg -with_context -json_tag=1 console.proto interface.go
option cc_generic_services = true;
package x1-base.sdk.console;

import "x1-base/sdk/common/common.proto"
import "google/protobuf/descriptor.proto";


message GetSecurityGroupIdsRequest {
    optional string instanceId = 1;
    //@inject_tag json:",omitempty"
    optional string instanceType = 2;
    //@inject_tag json:"-"
    optional common.Authentication auth = 3;
}

message Result {
    repeated ActiveRules activeRules = 1;
    repeated SecurityGroups securityGroups = 2;
}

message ActiveRules {
    optional string direction = 1;
    optional string ethertype = 2;
    optional string id = 3;
    optional string name = 4;
    optional string portRange = 5;
    optional string protocol = 6;
    optional string remoteGroupId = 7;
    optional string remoteGroupName = 8;
    optional string remoteIP = 9;
    optional string securityGroupId = 10;
    optional string securityGroupRuleId = 11;
    optional string securityGroupUuid = 12;
    optional string tenantId = 13;
}

message SecurityGroups {
    optional string desc = 1;
    optional string id = 2;
    optional string name = 3;
    repeated ActiveRules rules = 4;
    optional string securityGroupId = 5;
    optional string tenantId = 6;
    optional string vpcId = 7;
    optional string vpcName = 8;
}

message GetSecurityGroupIdsResponse {
    optional Result result = 1;
    optional int32 status = 2;
    optional bool success = 3;
    optional string code = 4;
    optional string message = 5;
    optional string requestId = 6;
}

message UnbindVpcSecurityGroupsRequest {
    optional string instanceId = 1;
    //@inject_tag json:",omitempty"
    optional string instanceType = 2;
    repeated string securityGroupUuids = 3;
    //@inject_tag json:"-"
    optional common.Authentication auth = 4;
}

message UnbindVpcSecurityGroupsResponse {
    optional int32 status = 1;
    optional bool success = 2;
    optional string code = 3;
    optional string message = 4;
    optional string requestId = 5;
}

service ConsoleService {
    rpc get_security_group_ids(GetSecurityGroupIdsRequest) returns (GetSecurityGroupIdsResponse);
    rpc unbind_vpc_security_groups(UnbindVpcSecurityGroupsRequest) returns (UnbindVpcSecurityGroupsResponse);
}

