/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2021/12/27
 * File: zone_sdk_test.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package sdk TODO package function desc
package console_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/console"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

func init() {
	unittest.UnitTestInit(2)
}

func TestGetSecurityGroupIdList(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	consoleSdk := console.NewDefaultConsoleSdk()
	// case 1
	resp, err := consoleSdk.GetSecurityGroupIdList(ctx, nil)
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")

	// case 2
	resp, err = consoleSdk.GetSecurityGroupIdList(ctx, &console.GetSecurityGroupIdsRequest{
		InstanceId:   "",
		InstanceType: "",
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")

	// case 3
	resp, err = consoleSdk.GetSecurityGroupIdList(ctx, &console.GetSecurityGroupIdsRequest{
		InstanceId:   "123",
		InstanceType: "",
		Auth:         nil,
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")
}
