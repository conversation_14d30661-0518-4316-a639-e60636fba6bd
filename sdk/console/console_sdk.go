/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-01-24
 * File: console.go
 */

/*
 * DESCRIPTION
 *   console api for scs
 */

// Package console
package console

import (
	"context"
	"net/http"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type consoleSdk struct {
	conf *consoleConf
	common.OpenApi
}

func NewDefaultConsoleSdk() ConsoleService {
	return newConsoleSdk(DefaultServiceName)
}

func NewConsoleSdk(serviceName string) ConsoleService {
	return newConsoleSdk(serviceName)
}

func newConsoleSdk(serviceName string) *consoleSdk {
	s := &consoleSdk{
		conf:    getConf(serviceName),
		OpenApi: common.NewOpenApi(serviceName),
	}

	return s
}

func (s *consoleSdk) doRequest(ctx context.Context, params *common.OpenApiParams, result interface{}) error {
	err := s.OpenApi.DoRequest(ctx, params, result)
	if err != nil {
		if params.HttpMethod == http.MethodGet {
			return cerrs.ErrConsoleQueryFail.Wrap(err)
		}
		return cerrs.ErrConsoleOpFail.Wrap(err)
	}

	return nil
}

// GetSecurityGroupIds 列举指定app的sg
func (s *consoleSdk) GetSecurityGroupIds(ctx context.Context, req *GetSecurityGroupIdsRequest) (
	rsp *GetSecurityGroupIdsResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req auth is null")
	}

	params := &common.OpenApiParams{
		ActionName: "GetSecurityGroupIds",
		Auth:       req.Auth,
		HttpMethod: http.MethodPost,
		Uri:        scsSecurityUri + "/list",
		Posts:      req,
		Product:    s.conf.Product,
	}

	rsp = &GetSecurityGroupIdsResponse{}
	err = s.doRequest(ctx, params, rsp)
	return
}

// UnbindVpcSecurityGroups 解绑指定app的sg
func (s *consoleSdk) UnbindVpcSecurityGroups(ctx context.Context, req *UnbindVpcSecurityGroupsRequest) (rsp *UnbindVpcSecurityGroupsResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req auth is null")
	}

	params := &common.OpenApiParams{
		ActionName: "UnbindVpcSecurityGroups",
		Auth:       req.Auth,
		HttpMethod: http.MethodPost,
		Uri:        scsSecurityUri + "/unbind",
		Posts:      req,
		Product:    s.conf.Product,
	}

	rsp = &UnbindVpcSecurityGroupsResponse{}
	err = s.doRequest(ctx, params, rsp)
	return
}

func (s *consoleSdk) GetSecurityGroupIdList(ctx context.Context,
	req *GetSecurityGroupIdsRequest) (rsp *GetSecurityGroupIdsResp, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.InstanceId == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req instanceId is null")
	}
	if req.InstanceType == "" {
		logger.SdkLogger.Trace(ctx, "GetSecurityGroupIdList req.InstanceType is null")
	}
	if req.Auth == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req auth is null")
	}

	rsp = &GetSecurityGroupIdsResp{}
	err = s.doRequest(ctx, &common.OpenApiParams{ActionName: "GetSecurityGroupIds", Auth: req.Auth,
		HttpMethod: http.MethodPost, Uri: scsSecurityUri + "/list", Posts: req}, rsp)
	return
}
