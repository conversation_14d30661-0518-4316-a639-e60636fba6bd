/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/24 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file console_conf.go
 * <AUTHOR>
 * @date 2023/03/24 15:15:47
 * @brief nova conf

 *
 **/

package console

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "console"

// consoleConf definition
type consoleConf struct {
	Product string `toml:"Product,omitempty"`
}

var consoleConfMap = &sync.Map{}

func getConf(serviceName string) *consoleConf {
	if conf, ok := consoleConfMap.Load(serviceName); ok {
		if conf, ok := conf.(*consoleConf); ok {
			return conf
		}
	}

	conf := &consoleConf{}
	conf.mustLoad(serviceName)

	consoleConfMap.Store(serviceName, conf)

	return conf
}

func (conf *consoleConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
