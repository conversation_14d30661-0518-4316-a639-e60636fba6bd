/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-06
 * File: logger_file.go
 */

/*
 * DESCRIPTION
 *   file logger
 */

// Package logger
package logger

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
)

// fileLogger - file logger
type fileLogger struct {
	logit.Logger
}

func (logger *fileLogger) Debug(ctx context.Context, format string, a ...interface{}) {
	logger.Output(ctx, logit.DebugLevel, 1, format, a...)
}

func (logger *fileLogger) Trace(ctx context.Context, format string, a ...interface{}) {
	logger.Output(ctx, logit.TraceLevel, 1, format, a...)
}

func (logger *fileLogger) Notice(ctx context.Context, format string, a ...interface{}) {
	logger.Output(ctx, logit.NoticeLevel, 1, format, a...)
}

func (logger *fileLogger) Warning(ctx context.Context, format string, a ...interface{}) {
	logger.Output(ctx, logit.WarningLevel, 1, format, a...)
}

func (logger *fileLogger) Error(ctx context.Context, format string, a ...interface{}) {
	logger.Output(ctx, logit.ErrorLevel, 1, format, a...)
}

func (logger *fileLogger) Fatal(ctx context.Context, format string, a ...interface{}) {
	logger.Output(ctx, logit.FatalLevel, 1, format, a...)
}

func (logger *fileLogger) Output(ctx context.Context, level logit.Level, callDepth int, format string, a ...interface{}) {
	logger.Logger.Output(ctx, level, callDepth+1, fmt.Sprintf(format, a...))
}

// NewLogger - new common logger
func NewLogger(ctx context.Context, opts ...logit.Option) Logger {
	logger, err := logit.NewLogger(ctx, opts...)
	if err != nil {
		panic("logit.NewLogger fail: " + err.Error())
	}

	return &fileLogger{
		Logger: logger,
	}
}

var _ Logger = (*fileLogger)(nil)
