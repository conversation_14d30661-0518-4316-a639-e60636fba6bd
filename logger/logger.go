/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2021/12/01
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package fileLogger TODO package function desc
package logger

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
)

// Logger - Logger interface
type Logger interface {
	Debug(ctx context.Context, format string, a ...interface{})
	Trace(ctx context.Context, format string, a ...interface{})
	Notice(ctx context.Context, format string, a ...interface{})
	Warning(ctx context.Context, format string, a ...interface{})
	Error(ctx context.Context, format string, a ...interface{})
	Fatal(ctx context.Context, format string, a ...interface{})

	Output(ctx context.Context, level logit.Level, callDepth int, format string, a ...interface{})
}

// DefaultLogger 默认logger
var DefaultLogger Logger = NewStdLogger(context.Background())

// SdkLogger sdk logger
var SdkLogger Logger = NewStdLogger(context.Background(), WithModuleName("SDK"))

// ComponentLogger component logger
var ComponentLogger Logger = NewStdLogger(context.Background(), WithModuleName("COMPO"))

// SetDefaultLogger 设置默认logger
func SetDefaultLogger(logger Logger) {
	DefaultLogger = logger
}

// SetSdkLogger 设置sdk logger
func SetSdkLogger(logger Logger) {
	SdkLogger = logger
}

// SetComponentLogger 设置component logger
func SetComponentLogger(logger Logger) {
	ComponentLogger = logger
}
