/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-06
 * File: logger_std.go
 */

/*
 * DESCRIPTION
 *   std logger
 */

// Package logger
package logger

import (
	"context"
	"os"

	"icode.baidu.com/baidu/gdp/logit"
)

// stdLogger - stdout logger
type stdLogger fileLogger

// NewStdLogger - new stdout logger
func NewStdLogger(ctx context.Context, opts ...logit.Option) Logger {
	opts = append(opts, WithWriter(os.Stdout))
	return NewLogger(ctx, opts...)
}
