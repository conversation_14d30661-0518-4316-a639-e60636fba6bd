package logger

import (
	"fmt"
	"os"
	"path/filepath"
)

func readLogFile(fileName string) (content string, err error) {
	bytes, err := os.ReadFile(fileName)
	if err != nil {
		return
	}
	content = string(bytes)
	fmt.Println(content)
	return
}

func deleteLogFiles(fileName string) {
	fileNames, _ := filepath.Glob(fileName + "*")

	if fileNames != nil {
		for _, fileName := range fileNames {
			_ = os.Remove(fileName)
		}
	}
}
