package logger

import (
	"context"
	"regexp"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/conf"
)

func TestAllLevelsForFileLogger(t *testing.T) {
	ctx := context.Background()
	confFileName := "./test_conf.toml"
	var c map[string]interface{}
	err := conf.Parse(confFileName, &c)
	if err != nil {
		t.Fatalf("can not load log conf %s", confFileName)
	}
	logFileName, _ := c["FileName"].(string)

	l := NewLogger(ctx, WithConfigFile(confFileName), WithFlushDuration(1), WithModuleName("TEST"))

	l.Trace(ctx, "this is trace log")
	l.Notice(ctx, "this is notice log")
	l.Warning(ctx, "this is warning log")
	l.Error(ctx, "this is error log")
	l.<PERSON>al(ctx, "this is fatal log")

	time.Sleep(200 * time.Millisecond)

	logFileContent, err := readLogFile(logFileName)
	if err != nil {
		t.Fatalf("read log file fail, %s", err.Error())
	}

	logFileContentPattern := regexp.MustCompile(`(?s)TRACE:.+?NOTICE:`)
	if !logFileContentPattern.MatchString(logFileContent) {
		t.Errorf("log file pattern not matched")
	}

	warningFileContent, err := readLogFile(logFileName + ".wf")
	if err != nil {
		t.Fatalf("read log file fail, %s", err.Error())
	}

	warningFileContentPattern := regexp.MustCompile(`(?s)WARNING:.+?ERROR:.+?FATAL`)
	if !warningFileContentPattern.MatchString(warningFileContent) {
		t.Errorf("log file pattern not matched")
	}

	deleteLogFiles(logFileName)
}
