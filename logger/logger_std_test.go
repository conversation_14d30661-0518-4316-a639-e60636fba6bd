package logger

import (
	"context"
	"os"
	"regexp"
	"testing"
	"time"
)

func TestAllLevelsForStdLogger(t *testing.T) {
	ctx := context.Background()
	logFileName := "./file_test.log"

	file, err := os.Create(logFileName)
	if err != nil {
		t.Fatalf("open file fail")
	}
	oriStdout := os.Stdout
	os.Stdout = file

	l := NewStdLogger(ctx, WithModuleName("TEST"))

	l.Trace(ctx, "this is trace log")
	l.Notice(ctx, "this is notice log")
	l.Warning(ctx, "this is warning log")
	l.Error(ctx, "this is error log")
	l.<PERSON>al(ctx, "this is fatal log")

	time.Sleep(200 * time.Millisecond)

	_ = file.Close()
	os.Stdout = oriStdout

	logFileContent, err := readLogFile(logFileName)
	if err != nil {
		t.Fatalf("read log file fail, %s", err.Error())
	}

	logFileContentPattern := regexp.MustCompile(`(?s)TRACE:.+?NOTICE:.+?WARNING:.+?ERROR:.+?FATAL`)
	if !logFileContentPattern.MatchString(logFileContent) {
		t.Errorf("log file pattern not matched")
	}

	deleteLogFiles(logFileName)
}
