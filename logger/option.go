/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-01-04
 * File: option.go
 */

/*
 * DESCRIPTION
 *   logit option wrapper
 */

// Package logger
package logger

import (
	"context"
	"io"

	"icode.baidu.com/baidu/gdp/logit"
)

// WithLogFields - 设置通用fields
func WithLogFields(fields ...logit.Field) logit.Option {
	return logit.OptSetConfigFn(func(c *logit.Config) {
		beforeOutputFunc := c.BeforeOutputFunc
		c.BeforeOutputFunc = func(ctx context.Context, enc logit.FieldEncoder, level logit.Level, callDepth int) {
			if beforeOutputFunc != nil {
				beforeOutputFunc(ctx, enc, level, callDepth+1)
			}
			for _, field := range fields {
				field.AddTo(enc)
			}
		}
	})
}

// WithModuleName - 设置module名
func WithModuleName(name string) logit.Option {
	return WithLogFields(logit.String("module", name))
}

// WithConfigFile - 设置config文件
func WithConfigFile(confName string) logit.Option {
	return logit.OptConfigFile(confName)
}

// WithWriter - 设置writer
func WithWriter(w io.WriteCloser) logit.Option {
	return logit.OptWriter(w)
}

// WithFlushDuration - 设置flush时长
func WithFlushDuration(ms int) logit.Option {
	return logit.OptFlushDuration(uint(ms))
}
