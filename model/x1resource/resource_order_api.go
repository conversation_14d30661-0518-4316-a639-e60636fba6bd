package x1resource

import (
	"context"
)

func ResourceOrderGetByOrderId(ctx context.Context, orderID string) (*ResourceOrder, error) {
	var resourceOrder ResourceOrder
	err := resource.GetOneByUkey(ctx, orderID, &resourceOrder)
	if err != nil {
		return nil, err
	}
	return &resourceOrder, nil
}

func ResourceOrderGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*ResourceOrder, error) {
	var resourceOrder ResourceOrder
	err := resource.GetOneByCond(ctx, &resourceOrder, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &resourceOrder, nil
}

func ResourceOrderGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*ResourceOrder, error) {
	var resourceOrders []*ResourceOrder
	err := resource.GetAllByCond(ctx, &resourceOrders, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return resourceOrders, nil
}

func ResourceOrderSave(ctx context.Context, dataPtr []*ResourceOrder) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}

func ResourceOrderUpdate(ctx context.Context, orderID string, dataPtr *ResourceOrder) error {
	return resource.UpdateOne(ctx, orderID, dataPtr)
}
