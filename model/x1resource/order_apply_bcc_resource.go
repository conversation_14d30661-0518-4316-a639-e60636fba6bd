package x1resource

import "context"

func OrderApplyBccResourceGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*OrderApplyBccResource, error) {
	var orderApplyBccResource OrderApplyBccResource
	err := resource.GetOneByCond(ctx, &orderApplyBccResource, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &orderApplyBccResource, nil
}

func OrderApplyBccResourceGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*OrderApplyBccResource, error) {
	var orderApplyBccResources []*OrderApplyBccResource
	err := resource.GetAllByCond(ctx, &orderApplyBccResources, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return orderApplyBccResources, nil
}

func OrderApplyBccResourceSave(ctx context.Context, dataPtr []*OrderApplyBccResource) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
