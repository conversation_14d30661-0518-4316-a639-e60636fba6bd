package x1resource

import (
	"context"
	"gorm.io/gorm"
)

func ShardGetByShardId(ctx context.Context, shardID string) (*Shard, error) {
	var shard Shard
	err := resource.GetOneByUkey(ctx, shardID, &shard)
	if err != nil {
		return nil, err
	}
	return &shard, nil
}

func ShardGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*Shard, error) {
	var shard Shard
	err := resource.GetOneByCond(ctx, &shard, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &shard, nil
}

func ShardGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*Shard, error) {
	var shards []*Shard
	err := resource.GetAllByCond(ctx, &shards, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return shards, nil
}

func ShardSave(ctx context.Context, dataPtr []*Shard) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}

func GetDBPortCount(ctx context.Context, deploysetName string, dbPortLeftBracket, dbPortRightBracket int) ([]*DBPortCount, error) {
	var dbPortCounts []*DBPortCount
	db := resource.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: resource.GormLoggerCommon})
	//使用事务，保证访问mysql主库
	err := db.Transaction(func(tx *gorm.DB) error {
		// as后的列名，除了首字母必须都小写
		return tx.Table("shard").Select("count(dbPort) as count", "dbPort as dbport").Where("deployset_name = ? and dbPort >= ? and dbPort <= ?", deploysetName, dbPortLeftBracket, dbPortRightBracket).Group("dbPort").Order("count").Scan(&dbPortCounts).Error
	})
	if err != nil {
		return nil, err
	}
	return dbPortCounts, nil
}
