package x1resource

import (
	"context"
)

func X1ResourceGetByRsId(ctx context.Context, rsID string) (*X1Resource, error) {
	var x1Resource X1Resource
	err := resource.GetOneByUkey(ctx, rsID, &x1Resource)
	if err != nil {
		return nil, err
	}
	return &x1Resource, nil
}

func X1ResourceGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*X1Resource, error) {
	var x1Resource X1Resource
	err := resource.GetOneByCond(ctx, &x1Resource, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &x1Resource, nil
}

func X1ResourceGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*X1Resource, error) {
	var x1Resources []*X1Resource
	err := resource.GetAllByCond(ctx, &x1Resources, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return x1Resources, nil
}

func X1ResourceSave(ctx context.Context, dataPtr []*X1Resource) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
