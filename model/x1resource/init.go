package x1resource

import (
	"context"
	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model"
)

var (
	servicerName                 = "db_x1resource"
	resource     *model.Resource = nil
)

type Conf struct {
	DbLogger     logit.Logger // gdp db logger
	GormLogger   logit.Logger // gorm 操作logger
	ServicerName string       // 自定义x1resource mysql servicer name，传空字符串走默认  "db_x1resource"
}

func Init(ctx context.Context, conf Conf) *model.Resource {
	if conf.ServicerName != "" {
		servicerName = conf.ServicerName
	}
	resourcePtr, err := model.InitModel(ctx, model.ResourceCfg{
		ServicerName: servicerName,
		DbLogger:     conf.DbLogger,
		GormLogger:   conf.GormLogger,
		PreloadConf:  map[string][]string{},
		AutoPreload:  true,
	})
	if err != nil {
		panic("init global_model fail")
	}
	resource = resourcePtr
	return resourcePtr
}

// HasInited 给依赖方判断是否已初始化global master model 用
func HasInited() bool {
	return resource != nil
}

// GetDbAgent 获取Dao原始变量，可以直接操作Dao方法定制化自己的Sql方法
// 具体可以参考 model/x1model/xx_api.go 与 model/dao.go
func GetDbAgent(ctx context.Context) (*model.Resource, error) {
	if !HasInited() {
		return nil, errors.Errorf("x1resource has not init")
	}
	return resource, nil
}
