package x1resource

import "time"

type X1Resource struct {
	Id             int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	ResourceID     string    `gorm:"column:resource_id;NOT NULL" json:"resource_id" ukey:"resource_id"`
	Name           string    `gorm:"column:name;default: ;NOT NULL" json:"name"`
	OrderID        string    `gorm:"column:order_id;NOT NULL" json:"order_id"`
	FixedIP        string    `gorm:"column:fixed_ip;NOT NULL" json:"fixed_ip"`
	Ipv6FixedIP    string    `gorm:"column:ipv6_fixed_ip;NOT NULL" json:"ipv6_fixed_ip"`
	FloatingIP     string    `gorm:"column:floating_ip;NOT NULL" json:"floating_ip"`
	Password       string    `gorm:"column:password;NOT NULL" json:"password"`
	Flavor         string    `gorm:"column:flavor;NOT NULL" json:"flavor"`
	Metadata       string    `gorm:"column:metadata;default: ;NOT NULL" json:"metadata"`
	CreateAt       time.Time `gorm:"column:create_at;NOT NULL" json:"create_at"`
	ShardID        string    `gorm:"column:shard_id;NOT NULL" json:"shard_id"`
	PortData       string    `gorm:"column:port_data;NOT NULL" json:"port_data"`
	PortList       string    `gorm:"column:port_list;NOT NULL" json:"port_list"`
	BccUUID        string    `gorm:"column:bcc_uuid;NOT NULL" json:"bcc_uuid"`
	Type           string    `gorm:"column:type;NOT NULL" json:"type"`
	Status         string    `gorm:"column:status;NOT NULL" json:"status"`
	ParticularInfo string    `gorm:"column:particular_info;NOT NULL" json:"particular_info"`
	CdsID          string    `gorm:"column:cds_id;NOT NULL" json:"cds_id"`
}

func (X1Resource) TableName() string {
	return "resource"
}

type Shard struct {
	Id            int    `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	ShardID       string `gorm:"column:shard_id;NOT NULL" json:"shard_id" ukey:"shard_id"`
	Ports         string `gorm:"column:ports;NOT NULL" json:"ports"`
	PortList      string `gorm:"column:port_list;NOT NULL" json:"port_list"`
	DeploySetName string `gorm:"column:deployset_name;NOT NULL" json:"deployset_name"`
	DbPort        int    `gorm:"column:dbPort;NOT NULL" json:"dbPort" ukey:"dbPort"`
	Entrypoint    string `gorm:"column:entrypoint;NOT NULL" json:"entrypoint"`
	StartParams   string `gorm:"column:start_params;NOT NULL" json:"start_params"`
}

func (Shard) TableName() string {
	return "shard"
}

type ResourceOrder struct {
	Id              int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	OrderID         string    `gorm:"column:order_id;NOT NULL" json:"order_id" ukey:"order_id"`
	ParentID        string    `gorm:"column:parent_id;NOT NULL" json:"parent_id"`
	TaskID          string    `gorm:"column:task_id;NOT NULL" json:"task_id"`
	Status          string    `gorm:"column:status;NOT NULL" json:"status"`
	ErrMsg          string    `gorm:"column:err_msg;NOT NULL;type:longtext" json:"err_msg"`
	Parameters      string    `gorm:"column:parameters;NOT NULL;type:longtext" json:"parameters"`
	IamUserID       string    `gorm:"column:iam_user_id;NOT NULL" json:"iam_user_id"`
	Priority        string    `gorm:"column:priority;NOT NULL" json:"priority"`
	UserType        string    `gorm:"column:user_type;NOT NULL" json:"user_type"`
	ResourceOrderID string    `gorm:"column:resource_order_id;NOT NULL" json:"resource_order_id"`
	Result          string    `gorm:"column:result;NOT NULL;type:longtext" json:"result"`
	Type            string    `gorm:"column:type;NOT NULL" json:"type"`
	Action          string    `gorm:"column:action;NOT NULL" json:"action"`
	StartAt         time.Time `gorm:"column:start_at;NOT NULL" json:"start_at"`
}

func (ResourceOrder) TableName() string {
	return "instance_order"
}

type Bcc struct {
	Id          int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	UUID        string    `gorm:"column:uuid;NOT NULL" json:"uuid" ukey:"uuid"`
	Status      string    `gorm:"column:status;NOT NULL" json:"status"`
	FixedIP     string    `gorm:"column:fixed_ip;NOT NULL" json:"fixed_ip"`
	Ipv6FixedIP string    `gorm:"column:ipv6_fixed_ip;NOT NULL" json:"ipv6_fixed_ip"`
	FloatingIP  string    `gorm:"column:floating_ip;NOT NULL" json:"floating_ip"`
	Password    string    `gorm:"column:password;NOT NULL" json:"password"`
	Hostname    string    `gorm:"column:host_name;NOT NULL;type:longtext" json:"host_name"`
	DeployID    string    `gorm:"column:deploy_id;NOT NULL;type:longtext" json:"deploy_id"`
	ShortID     string    `gorm:"column:short_id;NOT NULL" json:"short_id"`
	Flavor      string    `gorm:"column:flavor;NOT NULL" json:"flavor"`
	Tags        string    `gorm:"column:tags;NOT NULL" json:"tags"`
	Metadata    string    `gorm:"column:metadata;NOT NULL" json:"metadata"`
	Zone        string    `gorm:"column:zone;NOT NULL;type:longtext" json:"zone"`
	IsReserve   string    `gorm:"column:is_reserve;NOT NULL;type:longtext" json:"is_reserve"`
	CreateTime  time.Time `gorm:"column:create_time;NOT NULL" json:"create_time"`
	UpdateTime  time.Time `gorm:"column:update_time;NOT NULL" json:"update_time"`
	UserID      string    `gorm:"column:user_id" json:"user_id"`
}

func (Bcc) TableName() string {
	return "vm_unit"
}

type DBPortCount struct {
	Dbport int
	Count  int
}

type OrderApplyBccResource struct {
	Id                    int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	OrderId               string    `gorm:"column:order_id;NOT NULL" json:"order_id"`
	Zone                  string    `gorm:"column:zone;NOT NULL" json:"zone"`
	DeploysetName         string    `gorm:"column:deployset_name;NOT NULL" json:"deployset_name"`
	Pool                  string    `gorm:"column:pool;NOT NULL" json:"pool"`
	Count                 int       `gorm:"column:count;NOT NULL" json:"count"`
	MaxInstanceMemoryInMB int       `gorm:"column:max_instance_memory_in_mb;NOT NULL" json:"max_instance_memory_in_mb"`
	CreateTime            time.Time `gorm:"column:create_time;NOT NULL" json:"create_time"`
}

func (OrderApplyBccResource) TableName() string {
	return "order_apply_bcc_resource"
}
