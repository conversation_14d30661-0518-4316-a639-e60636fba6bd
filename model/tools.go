package model

import (
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"reflect"
	"strings"
)

type ExtractFieldNameFunc func(v reflect.StructField) string

func GetModelHeaders(m interface{}, f ExtractFieldNameFunc, ignores []string) []string {
	var headers []string
	t := reflect.TypeOf(m)
	for i := 0; i < t.NumField(); i++ {
		n := f(t.Field(i))
		if n == "" {
			continue
		}
		if len(ignores) > 0 {
			if in, _ := base_utils.InArray(n, ignores); in {
				continue
			}
		}
		headers = append(headers, n)
	}
	return headers
}

func ExtractGormColumnName(sv reflect.StructField) string {
	gormTag := sv.Tag.Get("gorm")
	tagParts := strings.Split(gormTag, ";")
	for _, tagPart := range tagParts {
		if strings.HasPrefix(tagPart, "column") {
			if strings.Contains(tagPart, ":") {
				return strings.Split(tagPart, ":")[1]
			}
		}
	}
	return ""
}
