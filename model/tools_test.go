package model

import (
	"reflect"
	"testing"
)

type TestStruct struct {
	Id   int    `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	Name string `gorm:"column:name;NOT NULL" json:"name"`
}

func TestGetModelHeaders(t *testing.T) {
	m := TestStruct{}
	ignores := []string{"id"}

	headers := GetModelHeaders(m, ExtractGormColumnName, ignores)

	if len(headers) != 1 {
		t.Fatalf("Expected 1 header, got %d", len(headers))
	}

	if headers[0] != "name" {
		t.Fatalf("Expected 'name', got '%s'", headers[0])
	}
}

func TestGetModelHeadersWithNoIgnores(t *testing.T) {
	m := TestStruct{}
	ignores := []string{}

	headers := GetModelHeaders(m, ExtractGormColumnName, ignores)

	if len(headers) != 2 {
		t.Fatalf("Expected 2 headers, got %d", len(headers))
	}

	if headers[0] != "id" {
		t.Fatalf("Expected 'id', got '%s'", headers[0])
	}

	if headers[1] != "name" {
		t.Fatalf("Expected 'name', got '%s'", headers[1])
	}
}

func TestExtractGormColumnName(t *testing.T) {
	m := TestStruct{}
	tt := reflect.TypeOf(m)
	field, _ := tt.FieldByName("Id")

	columnName := ExtractGormColumnName(field)

	if columnName != "id" {
		t.Fatalf("Expected 'id', got '%s'", columnName)
	}
}

func TestExtractGormColumnNameWithNoColumn(t *testing.T) {
	type NoColumnStruct struct {
		Id int `gorm:"AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	}

	m := NoColumnStruct{}
	tt := reflect.TypeOf(m)
	field, _ := tt.FieldByName("Id")

	columnName := ExtractGormColumnName(field)

	if columnName != "" {
		t.Fatalf("Expected '', got '%s'", columnName)
	}
}
