package global_model

import "strings"

func GetIntForbidWrite(flags string) int {
	sliceFlag := strings.Split(flags, ",")
	for _, flag := range sliceFlag {
		if flag == FlagForbidWrite {
			return 1
		}
	}
	return 0
}

func IsDirtyReadAllow(flags string) bool {
	sliceFlag := strings.Split(flags, ",")
	for _, flag := range sliceFlag {
		if flag == FlagDirtyReadAllow {
			return true
		}
	}
	return false
}

func SetDirtyReadAllowFlag(member *AppGroupMember) {
	sliceFlag := strings.Split(member.Flags, ",")
	for _, flag := range sliceFlag {
		if flag == FlagDirtyReadAllow {
			return
		}
	}
	sliceFlag = append(sliceFlag, FlagDirtyReadAllow)
	member.Flags = strings.Join(sliceFlag, ",")
	return
}

func UnSetDirtyReadAllowFlag(member *AppGroupMember) {
	sliceFlag := strings.Split(member.Flags, ",")
	var newSliceFlag []string
	for _, flag := range sliceFlag {
		if flag == FlagDirtyReadAllow {
			continue
		}
		newSliceFlag = append(newSliceFlag, flag)
	}
	member.Flags = strings.Join(newSliceFlag, ",")
	return
}

func IsForbidWriteAllow(flags string) bool {
	sliceFlag := strings.Split(flags, ",")
	for _, flag := range sliceFlag {
		if flag == FlagForbidWrite {
			return true
		}
	}
	return false
}

func SetForbidWriteFlag(member *AppGroupMember) {
	sliceFlag := strings.Split(member.Flags, ",")
	for _, flag := range sliceFlag {
		if flag == FlagForbidWrite {
			return
		}
	}
	sliceFlag = append(sliceFlag, FlagForbidWrite)
	member.Flags = strings.Join(sliceFlag, ",")
	return
}

func UnSetForbidWriteFlag(member *AppGroupMember) {
	sliceFlag := strings.Split(member.Flags, ",")
	var newSliceFlag []string
	for _, flag := range sliceFlag {
		if flag == FlagForbidWrite {
			continue
		}
		newSliceFlag = append(newSliceFlag, flag)
	}
	member.Flags = strings.Join(newSliceFlag, ",")
	return
}

func SetAFlag(strFlag string, toAddFlag string) string {
	if toAddFlag == "" {
		return strFlag
	}
	sliceFlag := strings.Split(strFlag, ",")
	for _, flag := range sliceFlag {
		if flag == toAddFlag {
			return strFlag
		}
	}
	sliceFlag = append(sliceFlag, toAddFlag)
	return strings.Join(sliceFlag, ",")
}

func UnSetAFlag(strFlag string, toDelFlag string) string {
	if toDelFlag == "" {
		return strFlag
	}
	sliceFlag := strings.Split(strFlag, ",")
	var newSliceFlag []string
	for _, flag := range sliceFlag {
		if flag == toDelFlag {
			continue
		}
		newSliceFlag = append(newSliceFlag, flag)
	}
	return strings.Join(newSliceFlag, ",")
}

func IsFlagSet(strFlag string, toCheckFlag string) bool {
	sliceFlag := strings.Split(strFlag, ",")
	for _, fg := range sliceFlag {
		if fg == toCheckFlag {
			return true
		}
	}
	return false
}
