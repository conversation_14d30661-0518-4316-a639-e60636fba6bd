/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/04/24
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package global_model TODO package function desc
package global_model

import (
	"time"

	"gorm.io/gorm"
)

const (
	TypeLegacyScs  = "scs"        // 旧热活实例组，不支持弹性扩缩容
	TypeBdrp       = "bdrp"       // 新热活实例组，支持弹性扩缩容
	TypeStandalone = "standalone" // 标准版热活实例组

	StatusRuning                   = "normal"                     // 运行中
	StatusInitail                  = "initial"                    // 初始化中
	StatusLocalFailOver            = "local-failover"             // 本地域切主中
	StatusGlobalFailOver           = "global-failover"            // 跨地域切主中
	StatusAddShardsApplyResource   = "addshards-apply-resource"   // 扩分片，申请资源阶段
	StatusAddShardsMigration       = "addshards-migration"        // 扩分片，迁移数据阶段
	StatusDelShardsReleaseResource = "delshards-release-resource" // 缩分片，释放资源阶段
	StatusDelShardsMigration       = "delshards-migration"        // 缩分片，迁移数据阶段
	StatusModifySpecApplyResource  = "modify-spec-apply-resource" // 变规格，申请资源中
	StatusModifySpecMigration      = "modify-spec-migration"      // 变规格，申请资源中
	StatusAddMember                = "add-member"                 // 增加成员中
	StatusDelMember                = "del-member"                 // 减少成员中
	StatusInnerSelfHeal            = "inner-self-heal"            // 自愈中，用户看不到，用户视角还是normal

	RoleLeaderCluster   = "leader"   // 主实例
	RoleFollowerCluster = "follower" // 从实例

	FlagDirtyReadAllow = "dirty_read_allow" // 允许脏读
	FlagForbidWrite    = "forbid_write"     // 禁写

	ShardStatusRunning        = "normal"           // 运行中
	ShardStatusWaitResource   = "wait-resource"    // 等待创建资源
	ShardStatusWaitMigrateIn  = "wait-migrate-in"  // 等待迁入数据
	ShardStatusWaitMigrateOut = "wait-migrate-out" // 等待迁出数据
	ShardStatusWaitRelease    = "wait-release"     // 等待释放资源
	ShardStatusLocalFailover  = "local-failover"   // 本地域切主中
	ShardStatusGlobalFailover = "global-failover"  // 跨地域切主中

	ModifyTypeAddShards    = "modify-type-add-shards"
	ModifyTypeDelShards    = "modify-type-del-shards"
	ModifyTypeIncrNodeType = "modify-type-incr-node-type"
	ModifyTypeDecrNodeType = "modify-type-decr-node-type"
	ModifyTypeModifySpec   = "modify-type-spec"

	ModifyStageApplyResource   = "modify-stage-apply-resource"
	ModifyStageMigration       = "modify-stage-migration"
	ModifyStageReleaseResource = "modify-stage-release-resource"
	ModifyStageRollback        = "modify-stage-rollback"
	ModifyStageComplete        = "modify-stage-complete"
)

type AppGroup struct {
	Id                 int64                `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	GroupId            string               `gorm:"column:group_id;NOT NULL" json:"group_id" ukey:"group_id"`
	GroupName          string               `gorm:"column:group_name;NOT NULL" json:"group_name"`
	Status             string               `gorm:"column:status;NOT NULL" json:"status"`
	Type               string               `gorm:"column:type;NOT NULL" json:"type"`
	UserId             string               `gorm:"column:user_id;NOT NULL" json:"user_id"`
	CreatedAt          time.Time            `gorm:"column:created_at;default:0000-00-00 00:00:00;NOT NULL" json:"created_at"`
	UpdatedAt          time.Time            `gorm:"column:updated_at;default:0000-00-00 00:00:00;NOT NULL" json:"updated_at"`
	DeletedAt          gorm.DeletedAt       `gorm:"column:deleted_at;default:NULL" json:"deleted_at"` // soft_delete_mark
	Metaserver         string               `gorm:"column:metaserver;NOT NULL" json:"metaserver"`
	BnsGroup           string               `gorm:"column:bns_group;NOT NULL" json:"bns_group"`
	NodeName           string               `gorm:"column:node_name;NOT NULL" json:"node_name"`
	Flags              string               `gorm:"column:flags;NOT NULL" json:"flags"`
	AppGroupMembers    []*AppGroupMember    `gorm:"foreignKey:GroupId;references:GroupId"`
	AppGroupShards     []*AppGroupShard     `gorm:"foreignKey:GroupId;references:GroupId"`
	AppGroupProxys     []*AppGroupProxy     `gorm:"foreignKey:GroupId;references:GroupId"`
	AppGroupRedises    []*AppGroupRedis     `gorm:"foreignKey:GroupId;references:GroupId"`
	AppGroupWhitelists []*AppGroupWhitelist `gorm:"foreignKey:GroupId;references:GroupId"`
	AppGroupAcls       []*AppGroupAcl       `gorm:"foreignKey:GroupId;references:GroupId"`
	AppGroupConfs      []*AppGroupConf      `gorm:"foreignKey:GroupId;references:GroupId"`
}

type AppGroupAcl struct {
	Id                 int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	GroupId            string    `gorm:"column:group_id;NOT NULL" json:"group_id"`
	Account            string    `gorm:"column:account;NOT NULL" json:"account"`
	Password           string    `gorm:"column:password;NOT NULL" json:"password"`
	AllowedCommands    string    `gorm:"column:allowed_commands;NOT NULL" json:"allowed_commands"`
	AllowedSubCommands string    `gorm:"column:allowed_sub_commands;NOT NULL" json:"allowed_sub_commands"`
	KeyPatterns        string    `gorm:"column:key_patterns;NOT NULL" json:"key_patterns"`
	Comment            string    `gorm:"column:comment;NOT NULL" json:"comment"`
	UserType           int       `gorm:"column:user_type;NOT NULL" json:"user_type"`
	Version            int       `gorm:"column:version;default:0;NOT NULL" json:"version"`
	CreatedAt          time.Time `gorm:"column:created_at;default:0000-00-00 00:00:00;NOT NULL" json:"created_at"`
	UpdatedAt          time.Time `gorm:"column:updated_at;default:0000-00-00 00:00:00;NOT NULL" json:"updated_at"`
	Flags              string    `gorm:"column:flags;NOT NULL" json:"flags"`
}

type AppGroupConf struct {
	Id        int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	GroupId   string    `gorm:"column:group_id;NOT NULL" json:"group_id"`
	Name      string    `gorm:"column:name;NOT NULL" json:"name"`
	Module    string    `gorm:"column:module;NOT NULL" json:"module"`
	Value     string    `gorm:"column:value;NOT NULL" json:"value"`
	Effected  string    `gorm:"column:effected;NOT NULL" json:"effected"`
	Version   int       `gorm:"column:version;default:0;NOT NULL" json:"version"`
	CreatedAt time.Time `gorm:"column:created_at;default:0000-00-00 00:00:00;NOT NULL" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;default:0000-00-00 00:00:00;NOT NULL" json:"updated_at"`
	Flags     string    `gorm:"column:flags;NOT NULL" json:"flags"`
}

type AppGroupMember struct {
	Id            int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	MemberId      string    `gorm:"column:member_id;NOT NULL" json:"member_id" ukey:"member_id"`
	MemberName    string    `gorm:"column:member_name;NOT NULL" json:"member_name"`
	Region        string    `gorm:"column:region;NOT NULL" json:"region"`
	Role          string    `gorm:"column:role;NOT NULL" json:"role"`
	GroupId       string    `gorm:"column:group_id;NOT NULL" json:"group_id"`
	Status        string    `gorm:"column:status;NOT NULL" json:"status"`
	CreatedAt     time.Time `gorm:"column:created_at;default:0000-00-00 00:00:00;NOT NULL" json:"created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at;default:0000-00-00 00:00:00;NOT NULL" json:"updated_at"`
	Flags         string    `gorm:"column:flags;NOT NULL" json:"flags"`
	QpsLimitWrite int       `gorm:"column:qps_limit_write;NOT NULL" json:"qps_limit_write"`
	QpsLimitRead  int       `gorm:"column:qps_limit_read;NOT NULL" json:"qps_limit_read"`
	BnsService    string    `gorm:"column:bns_service;NOT NULL" json:"bns_service"`
}

type AppGroupProxy struct {
	Id         int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	ProxyId    string    `gorm:"column:proxy_id;NOT NULL" json:"proxy_id" ukey:"proxy_id"`
	AppId      string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	GroupId    string    `gorm:"column:group_id;NOT NULL" json:"group_id"`
	Region     string    `gorm:"column:region;NOT NULL" json:"region"`
	Status     string    `gorm:"column:status;NOT NULL" json:"status"`
	Ip         string    `gorm:"column:ip;NOT NULL" json:"ip"`
	FloatingIp string    `gorm:"column:floating_ip;NOT NULL" json:"floating_ip"`
	Port       int       `gorm:"column:port;NOT NULL" json:"port"`
	CreatedAt  time.Time `gorm:"column:created_at;default:0000-00-00 00:00:00;NOT NULL" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;default:0000-00-00 00:00:00;NOT NULL" json:"updated_at"`
	Flags      string    `gorm:"column:flags;NOT NULL" json:"flags"`
}

type AppGroupRedis struct {
	Id         int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	RedisId    string    `gorm:"column:redis_id;NOT NULL" json:"redis_id" ukey:"redis_id"`
	AppId      string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	GroupId    string    `gorm:"column:group_id;NOT NULL" json:"group_id"`
	Region     string    `gorm:"column:region;NOT NULL" json:"region"`
	ShardId    string    `gorm:"column:shard_id;NOT NULL" json:"shard_id"`
	Status     string    `gorm:"column:status;NOT NULL" json:"status"`
	Role       string    `gorm:"column:role;NOT NULL" json:"role"`
	Ip         string    `gorm:"column:ip;NOT NULL" json:"ip"`
	FloatingIp string    `gorm:"column:floating_ip;NOT NULL" json:"floating_ip"`
	Port       int       `gorm:"column:port;NOT NULL" json:"port"`
	CreatedAt  time.Time `gorm:"column:created_at;default:0000-00-00 00:00:00;NOT NULL" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;default:0000-00-00 00:00:00;NOT NULL" json:"updated_at"`
	Flags      string    `gorm:"column:flags;NOT NULL" json:"flags"`
}

type AppGroupShard struct {
	Id        int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	ShardId   string    `gorm:"column:shard_id;NOT NULL" json:"shard_id" ukey:"shard_id"`
	GroupId   string    `gorm:"column:group_id;NOT NULL" json:"group_id"`
	Master    string    `gorm:"column:master;NOT NULL" json:"master"`
	Relateds  string    `gorm:"column:relateds;NOT NULL" json:"relateds"` // 存储相关实例，逗号分隔；当实例节点注册时，添加实例id至该字段，当实例节点全部删除时，从改字段删除实例id。
	Status    string    `gorm:"column:status;NOT NULL" json:"status"`
	CreatedAt time.Time `gorm:"column:created_at;default:0000-00-00 00:00:00;NOT NULL" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;default:0000-00-00 00:00:00;NOT NULL" json:"updated_at"`

	Slots string `gorm:"column:slots;NOT NULL" json:"slots"`
	Flags string `gorm:"column:flags;NOT NULL" json:"flags"`
}

type AppGroupWhitelist struct {
	Id        int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	GroupId   string    `gorm:"column:group_id;NOT NULL" json:"group_id"`
	Ip        string    `gorm:"column:ip;NOT NULL" json:"ip"`
	CreatedAt time.Time `gorm:"column:created_at;default:0000-00-00 00:00:00;NOT NULL" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;default:0000-00-00 00:00:00;NOT NULL" json:"updated_at"`
	Flags     string    `gorm:"column:flags;NOT NULL" json:"flags"`
}

type AppGroupModifyStatus struct {
	Id               int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	GroupId          string    `gorm:"column:group_id;NOT NULL" json:"group_id"`
	Type             string    `gorm:"column:type;NOT NULL" json:"type"`
	Stage            string    `gorm:"column:stage;NOT NULL" json:"stage"`
	Progress         string    `gorm:"column:progress;NOT NULL" json:"progress"`
	TargetShardCount int       `gorm:"column:target_shard_count;NOT NULL" json:"target_shard_count"`
	TargetNodeType   string    `gorm:"column:target_node_type;NOT NULL" json:"target_node_type"`
	CreatedAt        time.Time `gorm:"column:created_at;default:0000-00-00 00:00:00;NOT NULL" json:"created_at"`
	UpdatedAt        time.Time `gorm:"column:updated_at;default:0000-00-00 00:00:00;NOT NULL" json:"updated_at"`
	Version          int       `gorm:"column:version;NOT NULL" json:"version"`
}

/*
create table cluster_map
(

	id           int auto_increment
	    primary key,
	s_cluster_id varchar(64) not null,
	t_cluster_id varchar(64) not null,
	created_at   datetime    not null

);
*/
type ClusterMap struct {
	ID         int       `json:"id" gorm:"column:id"`
	SClusterID string    `json:"s_cluster_id" gorm:"column:s_cluster_id"`
	TClusterID string    `json:"t_cluster_id" gorm:"column:t_cluster_id"`
	CreatedAt  time.Time `json:"created_at" gorm:"column:created_at"`
}

func (m *ClusterMap) TableName() string {
	return "cluster_map"
}

func (AppGroup) TableName() string {
	return "app_group"
}

func (AppGroupAcl) TableName() string {
	return "app_group_acl"
}

func (AppGroupConf) TableName() string {
	return "app_group_conf"
}

func (AppGroupMember) TableName() string {
	return "app_group_member"
}

func (AppGroupProxy) TableName() string {
	return "app_group_proxy"
}

func (AppGroupRedis) TableName() string {
	return "app_group_redis"
}

func (AppGroupShard) TableName() string {
	return "app_group_shard"
}

func (AppGroupWhitelist) TableName() string {
	return "app_group_whitelist"
}

func (AppGroupModifyStatus) TableName() string {
	return "app_group_modify_status"
}
