/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/12/15
 * File: dao_ng.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package model TODO package function desc
package model

import (
	"context"
	"fmt"
	"reflect"

	"gorm.io/gorm"
)

func getAnyUniqueKey(s reflect.Value) (string, any) {
	if s.Type().Kind() == reflect.Ptr {
		s = s.Elem()
	}
	for idx := 0; idx < s.NumField(); idx++ {
		if s.Field(idx).CanInterface() {
			keyName, ok := s.Type().Field(idx).Tag.Lookup("ukey")
			if ok {
				// todo 根据情况取值
				var ukeyVal any
				switch s.Field(idx).Kind() {
				case reflect.String:
					ukeyVal = s.Field(idx).String()
				case reflect.Bool:
					ukeyVal = s.Field(idx).Bool()
				case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
					ukeyVal = s.Field(idx).Int()
				case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
					ukeyVal = s.Field(idx).Uint()
				case reflect.Float32, reflect.Float64:
					ukeyVal = s.Field(idx).Float()
				default:
					panic(fmt.Sprintf("unsopport this kind:%s", s.Field(idx).Kind().String()))
				}
				return keyName, ukeyVal
			}
		}
	}
	panic("tag ukey not found in struct " + s.Type().Name())
}

// DeleteMultiForAnyTypeUkey
// 老版本的DeleteMulti只支持string类型的ukey，这里搞一个新的，支持多种类型
func (r Resource) DeleteMultiForAnyTypeUkey(ctx context.Context, datas any) error {
	dvs := reflect.ValueOf(datas)
	if reflect.TypeOf(datas).Kind() == reflect.Ptr {
		dvs = dvs.Elem()
	}
	keyName := ""
	var key any
	var keys []any
	for idx := 0; idx < dvs.Len(); idx++ {
		dv := dvs.Index(idx)
		keyName, key = getAnyUniqueKey(dv)
		keys = append(keys, key)
	}
	return r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon}).
		Where(fmt.Sprintf("%s IN ?", keyName), keys).Delete(datas).Error
}
