/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2021/12/05
 * File: dao.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package model
package model

import (
	"context"
	"fmt"
	"reflect"
	"strings"

	"github.com/spf13/cast"
	"gorm.io/gorm"
)

func getUniqueKey(s reflect.Value) (string, string) {
	if s.Type().Kind() == reflect.Ptr {
		s = s.Elem()
	}
	for idx := 0; idx < s.NumField(); idx++ {
		if s.Field(idx).CanInterface() {
			keyName, ok := s.Type().Field(idx).Tag.Lookup("ukey")
			if ok {
				return keyName, s.Field(idx).String()
			}
		}
	}
	panic("tag ukey not found in struct " + s.Type().Name())
}

func (r Resource) setPreLoad(tx *gorm.DB, target any) *gorm.DB {
	if r.AutoPreload {
		// 如果使用自动preload则转发过去
		return r.autoSetPreload(tx, target)
	}
	targetType := cast.ToString(reflect.TypeOf(target))
	structName := ""
	typeSlice := strings.Split(targetType, ".")
	if len(typeSlice) > 0 {
		structName = typeSlice[len(typeSlice)-1]
	}

	preloadConf := r.PreloadConf[structName]
	for i := 0; i < len(preloadConf); i++ {
		tx = tx.Preload(preloadConf[i])
	}
	return tx
}

func (r Resource) GetOneByUkey(ctx context.Context, ukeyValue string, target any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	tv := reflect.ValueOf(target)
	tv = tv.Elem()
	keyName, _ := getUniqueKey(tv)
	// 使用事务，保证访问mysql主库
	return db.Transaction(func(tx *gorm.DB) error {
		tx = tx.Where(fmt.Sprintf("%s = ?", keyName), ukeyValue)
		tx = r.setPreLoad(tx, target)
		return tx.First(target).Error
	})
}

func (r Resource) GetOneByCond(ctx context.Context, target any, fmtCond string, vals ...any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	// 使用事务，保证访问mysql主库
	return db.Transaction(func(tx *gorm.DB) error {
		tx = tx.Where(fmtCond, vals...)
		tx = r.setPreLoad(tx, target)
		return tx.First(target).Error
	})
}

// 不开启事务
func (r Resource) GetOneByCondNoTx(ctx context.Context, target any, fmtCond string, vals ...any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	db = db.Where(fmtCond, vals...)
	db = r.setPreLoad(db, target)
	return db.First(target).Error
}

func (r Resource) GetAllByCond(ctx context.Context, targets any, fmtCond string, vals ...any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	// 使用事务，保证访问mysql主库
	return db.Transaction(func(tx *gorm.DB) error {
		if fmtCond != "" {
			tx = tx.Where(fmtCond, vals...)
		}
		tx = r.setPreLoad(tx, targets)
		return tx.Find(targets).Error
	})
}

func (r Resource) GetAllByCondNoTx(ctx context.Context, targets any, fmtCond string, vals ...any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	db = db.Where(fmtCond, vals...)
	db = r.setPreLoad(db, targets)
	return db.Find(targets).Error
}

type GetMultiOptions struct {
	FmtCond string
	Vals    []any
	Offset  int
	Limit   int
	Order   []string
	Tx      bool
}

func (r Resource) GetMulti(ctx context.Context, target any, params *GetMultiOptions) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	if params.Tx {
		return db.Transaction(func(tx *gorm.DB) error {
			tx = fillConds(tx, params)
			tx = r.setPreLoad(tx, target)
			return tx.Find(target).Error
		})
	}
	db = fillConds(db, params)
	db = r.setPreLoad(db, target)
	return db.Offset(params.Offset).Limit(params.Limit).Find(target).Error
}

func fillConds(tx *gorm.DB, params *GetMultiOptions) *gorm.DB {
	if params.FmtCond != "" {
		tx = tx.Where(params.FmtCond, params.Vals...)
	}
	for _, order := range params.Order {
		tx = tx.Order(order)
	}
	if params.Limit > 0 {
		tx = tx.Limit(params.Limit)
	}
	if params.Offset > 0 {
		tx = tx.Offset(params.Offset)
	}
	return tx
}

func (r Resource) GetTotalCount(ctx context.Context, m any, params *GetMultiOptions) (int, error) {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	var count int64
	if params.FmtCond != "" {
		db = db.Where(params.FmtCond, params.Vals...)
	}
	err := db.Model(m).Count(&count).Error
	return int(count), err
}

func (r Resource) DB(ctx context.Context) *gorm.DB {
	return r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
}

func getInterface(v reflect.Value) any {
	if v.Type().Kind() == reflect.Ptr {
		return v.Interface()
	}
	p := reflect.New(v.Type())
	p.Elem().Set(v)
	return p.Interface()
}

func (r Resource) CreateOne(ctx context.Context, data any) error {
	return r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon}).Omit("id").
		Create(data).Error
}

func (r Resource) CreateMulti(ctx context.Context, datas any) error {
	return r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon}).Omit("id").
		Create(datas).Error
}

func (r Resource) UpdateOne(ctx context.Context, ukey string, data any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	dv := reflect.ValueOf(data).Elem()
	keyName, key := getUniqueKey(dv)
	if key != "" && key != ukey {
		return fmt.Errorf("can not update %s", keyName)
	}
	return db.Model(data).Where(fmt.Sprintf("%s = ?", keyName), ukey).Updates(data).Error
}

func (r Resource) UpdateMulti(ctx context.Context, datas any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	dvs := reflect.ValueOf(datas)
	if reflect.TypeOf(datas).Kind() == reflect.Ptr {
		dvs = dvs.Elem()
	}
	return db.Transaction(func(tx *gorm.DB) error {
		for idx := 0; idx < dvs.Len(); idx++ {
			dv := dvs.Index(idx)
			keyName, key := getUniqueKey(dv)
			ret := tx.Model(dv.Addr().Interface()).
				Where(fmt.Sprintf("%s = ? ", keyName), key).Updates(getInterface(dv))
			if ret.Error != nil {
				return ret.Error
			}
		}
		return nil
	})
}

func (r Resource) DeleteOneByUkey(ctx context.Context, ukey string, data any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	dv := reflect.ValueOf(data).Elem()
	keyName, _ := getUniqueKey(dv)
	return db.Where(fmt.Sprintf("%s = ?", keyName), ukey).Delete(data).Error
}

func (r Resource) DeleteAllByCond(ctx context.Context, targets any, fmtCond string, vals ...any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	db = db.Where(fmtCond, vals)
	return db.Delete(targets).Error
}

func (r Resource) UnscopedDeleteOneByUkey(ctx context.Context, ukey string, data any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	dv := reflect.ValueOf(data).Elem()
	keyName, _ := getUniqueKey(dv)
	return db.Unscoped().Where(fmt.Sprintf("%s = ?", keyName), ukey).Delete(data).Error
}

func (r Resource) UnscopedDeleteAllByCond(ctx context.Context, targets any, fmtCond string, vals ...any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	db = db.Where(fmtCond, vals)
	return db.Unscoped().Delete(targets).Error
}

func (r Resource) DeleteMulti(ctx context.Context, datas any) error {
	dvs := reflect.ValueOf(datas)
	if reflect.TypeOf(datas).Kind() == reflect.Ptr {
		dvs = dvs.Elem()
	}
	keyName := ""
	key := ""
	var keys []string
	for idx := 0; idx < dvs.Len(); idx++ {
		dv := dvs.Index(idx)
		keyName, key = getUniqueKey(dv)
		keys = append(keys, key)
	}
	return r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon}).
		Where(fmt.Sprintf("%s IN ?", keyName), keys).Delete(datas).Error
}

func (r Resource) FullSaveAssociationsSave(ctx context.Context, target any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon, FullSaveAssociations: true})
	return db.Save(target).Error
}

// UpdateOneByMap 用map会强制更新零值字段
// 如果不更新零值字段，请使用UpdateOne
// itemStructPtr 需要更新的struct的指针，需要更新的struct的指针，需要更新的struct的指针
// mapData 需要更新的数据
func (r Resource) UpdateOneByMap(ctx context.Context, itemStructPtr any, mapData map[string]any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	return db.Model(itemStructPtr).Updates(mapData).Error
}

func (r Resource) UpdateSingleColByUkey(ctx context.Context, itemStructPtr any, ukey string, key string, value any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	dv := reflect.ValueOf(itemStructPtr).Elem()
	keyName, _ := getUniqueKey(dv)
	return db.Model(itemStructPtr).Where(fmt.Sprintf("%s = ?", keyName), ukey).Update(key, value).Error
}

func (r Resource) ProtectFields(v reflect.Value) {
	switch v.Type().Kind() {
	case reflect.Ptr:
		r.ProtectFields(v.Elem())
	case reflect.Slice:
		for i := 0; i < v.Len(); i++ {
			r.ProtectFields(v.Index(i))
		}
	case reflect.Struct:
		fieldInfos, has := r.ProtectFieldParams[v.Type().Name()]
		if has {
			for _, fieldInfo := range fieldInfos {
				if fieldInfo.CondFunc(v) {
					fieldInfo.ValueFunc(v)
				}
			}
		} else {
			for i := 0; i < v.NumField(); i++ {
				if v.Type().Field(i).Type.Kind() == reflect.Slice {
					r.ProtectFields(v.Field(i))
				}
			}
		}
	default:
		return
	}
}

// DeleteAllByCondV2 和v1相比，主要是把where条件的val改成了展开模式
func (r Resource) DeleteAllByCondV2(ctx context.Context, targets any, fmtCond string, vals ...any) error {
	db := r.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: r.GormLoggerCommon})
	db = db.Where(fmtCond, vals...)
	return db.Delete(targets).Error
}
