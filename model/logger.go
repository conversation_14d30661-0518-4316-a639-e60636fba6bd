/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2021/12/06
 * File: logger.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package model TODO package function desc
package model

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/utils"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
)

type GormLogger struct {
	SlowThreshold             time.Duration
	IgnoreRecordNotFoundError bool
	LogLevel                  logger.LogLevel
	Father                    *Resource
}

func (l *GormLogger) LogMode(level logger.LogLevel) logger.Interface {
	newlogger := *l
	newlogger.LogLevel = level
	return &newlogger
}

func (l GormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.Log<PERSON>evel >= logger.Info {
		l.Father.GormLogger.Trace(ctx, fmt.Sprintf(msg, data...))
	}
}

func (l GormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Warn {
		l.Father.GormLogger.Warning(ctx, fmt.Sprintf(msg, data...))
	}
}

func (l GormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Error {
		l.Father.GormLogger.Error(ctx, fmt.Sprintf(msg, data...))
	}
}

func (l GormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	if l.LogLevel <= logger.Silent {
		return
	}
	elapsed := time.Since(begin)
	switch {
	case err != nil && l.LogLevel >= logger.Error && (!errors.Is(err, gorm.ErrRecordNotFound) || !l.IgnoreRecordNotFoundError):
		sql, rows := fc()
		l.Father.GormLogger.Trace(ctx, "ERROR SQL", logit.String("file", utils.FileWithLineNum()),
			logit.Error("error", err), logit.Float64("elapsed", float64(elapsed.Nanoseconds())/1e6),
			logit.String("sql", sql), logit.Int64("row", rows))

	case elapsed > l.SlowThreshold && l.SlowThreshold != 0 && l.LogLevel >= logger.Warn:
		sql, rows := fc()
		slowLog := fmt.Sprintf("SLOW SQL >= %v", l.SlowThreshold)
		l.Father.GormLogger.Trace(ctx, slowLog, logit.String("file", utils.FileWithLineNum()),
			logit.Error("error", err), logit.Float64("elapsed", float64(elapsed.Nanoseconds())/1e6),
			logit.String("sql", sql), logit.Int64("row", rows))
	case l.LogLevel == logger.Info:
		sql, rows := fc()
		l.Father.GormLogger.Trace(ctx, "SQL", logit.String("file", utils.FileWithLineNum()),
			logit.Float64("elapsed", float64(elapsed.Nanoseconds())/1e6),
			logit.String("sql", sql), logit.Int64("row", rows))
	}
}

func (r *Resource) InitGormLogger() {
	{
		tmpLogger := &GormLogger{
			SlowThreshold:             time.Second * 30,
			IgnoreRecordNotFoundError: false,
		}
		if env.Options().RunMode == "test" || env.Options().RunMode == "debug" {
			tmpLogger.LogLevel = logger.Info
		} else {
			tmpLogger.LogLevel = logger.Warn
		}
		tmpLogger.Father = r
		r.GormLoggerCommon = tmpLogger
	}
	{
		tmpLogger := &GormLogger{
			SlowThreshold:             time.Second * 30,
			IgnoreRecordNotFoundError: true,
		}
		if env.Options().RunMode == "test" || env.Options().RunMode == "debug" {
			tmpLogger.LogLevel = logger.Info
		} else {
			tmpLogger.LogLevel = logger.Warn
		}
		tmpLogger.Father = r
		r.GormLoggerIgnoreRecordNotFoundError = tmpLogger
	}
}
