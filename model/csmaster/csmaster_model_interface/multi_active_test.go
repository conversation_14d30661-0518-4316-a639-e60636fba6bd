/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON>uni<PERSON>@baidu.com)
 * Date: 2023/11/15
 * File: multi_active_test.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package csmaster_model_interface TODO package function desc
package csmaster_model_interface

import (
	"testing"
	"time"
)

func TestMultiActiveChannelList_TableName(t *testing.T) {
	obj := &MultiActiveChannelList{
		ID:                0,
		ClusterID:         0,
		PeerClusterShowID: "",
		PeerIP:            "",
		PeerPort:          0,
		PeerAuth:          "",
		Status:            0,
		CreateTime:        time.Now(),
	}
	if obj.TableName() != "multi_active_channel_list" {
		t.<PERSON><PERSON>("table name not right")
	}
}
