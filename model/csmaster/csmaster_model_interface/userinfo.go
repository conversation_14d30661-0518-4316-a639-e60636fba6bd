/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/03/24
 * File: userinfo.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package models TODO package function desc
package csmaster_model_interface

import "time"

type Userinfo struct {
	Id                       int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`                         // id
	IamUserId                string    `gorm:"column:iam_user_id;NOT NULL" json:"iam_user_id"`                                   // iam user id
	SecurityGroupId          string    `gorm:"column:security_group_id;NOT NULL" json:"security_group_id"`                       // security group id
	IcmpSecurityGroupRulesId string    `gorm:"column:icmp_security_group_rules_id;NOT NULL" json:"icmp_security_group_rules_id"` // icmp rules id
	SshSecurityGroupRulesId  string    `gorm:"column:ssh_security_group_rules_id;NOT NULL" json:"ssh_security_group_rules_id"`   // ssh rules id
	SecurityGroupRulesId     string    `gorm:"column:security_group_rules_id;NOT NULL" json:"security_group_rules_id"`           // security rules id
	CreateTime               time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_time"`         // create time
	Type                     int       `gorm:"column:type;default:0;NOT NULL" json:"type"`                                       // user type 0exteranl 1internal 2spinoff
}

func (Userinfo) TableName() string {
	return "userinfo"
}
