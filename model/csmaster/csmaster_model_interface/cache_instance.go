/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/03/14
 * File: cache_instance.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package models TODO package function desc
package csmaster_model_interface

import (
	"database/sql"
	"time"
)

const (
	INSTANCE_TYPE_PROXY        = 0
	INSTANCE_TYPE_SENTINEL     = 1
	INSTANCE_TYPE_SLAVE_REDIS  = 2
	INSTANCE_TYPE_MASTER_REDIS = 3
	INSTANCE_TYPE_MEMCACHE     = 4
	INSTANCE_TYPE_MASTER_NAS   = 5
)

var InstanceStatusMap = map[int32]string{
	0:  "CREATING",
	1:  "RUNNING",
	2:  "STOPED", // deprecated
	3:  "ERROR_RECOVERING",
	4:  "ERROR_RECOVER_FAIL",
	5:  "DELETED",
	6:  "MODIFYING",
	7:  "MIGRATING",
	8:  "MIGRATED",
	9:  "CONFIGURING",
	10: "REPLACING",
}

type CacheInstance struct {
	Id                int64          `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`                           // id
	ClusterId         int64          `gorm:"column:cluster_id;NOT NULL" json:"cluster_id"`                             // cluster id
	UserId            int            `gorm:"column:user_id;NOT NULL" json:"user_id"`                                   // user id
	Port              int            `gorm:"column:port;NOT NULL" json:"port"`                                         // port
	CreateTime        time.Time      `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_time"` // time
	Flavor            int            `gorm:"column:flavor;NOT NULL" json:"flavor"`                                     // flavor
	Uuid              string         `gorm:"column:uuid;NOT NULL" json:"uuid"`                                         // uuid
	CacheInstanceType int            `gorm:"column:cache_instance_type;NOT NULL" json:"cache_instance_type"`           // cache instance type
	MasterRedis       sql.NullString `gorm:"column:master_redis" json:"master_redis"`                                  // master redis
	SlaverRedis       sql.NullString `gorm:"column:slaver_redis" json:"slaver_redis"`                                  // slaver redis
	Status            int            `gorm:"column:status;NOT NULL" json:"status"`                                     // status
	Persistence       int            `gorm:"column:persistence;NOT NULL" json:"persistence"`                           // persistence
	FixIp             string         `gorm:"column:fix_ip;NOT NULL" json:"fix_ip"`                                     // fix ip
	FloatingIp        string         `gorm:"column:floating_ip;NOT NULL" json:"floating_ip"`                           // floating ip
	Password          string         `gorm:"column:password;NOT NULL" json:"password"`                                 // password
	HashName          string         `gorm:"column:hash_name;NOT NULL" json:"hash_name"`                               // hash name
	HostName          string         `gorm:"column:host_name;NOT NULL" json:"host_name"`                               // host name
	IamUserId         string         `gorm:"column:iam_user_id;NOT NULL" json:"iam_user_id"`                           // iamuser id
	ShardId           int            `gorm:"column:shard_id;default:0;NOT NULL" json:"shard_id"`                       // shard id
	MigrateStatus     int            `gorm:"column:migrate_status;default:0;NOT NULL" json:"migrate_status"`           // migrate status
	HashId            string         `gorm:"column:hash_id;NOT NULL" json:"hash_id"`                                   // hash id
	AvailabilityZone  string         `gorm:"column:availability_zone;NOT NULL" json:"availability_zone"`               // availability zone
	SubnetId          string         `gorm:"column:subnet_id;NOT NULL" json:"subnet_id"`                               // subnet id
	Ipv6              sql.NullString `gorm:"column:ipv6" json:"ipv6"`                                                  // ipv6 address
	ResFlavor         sql.NullString `gorm:"column:res_flavor" json:"res_flavor"`                                      // res_flavor
	NodeId            sql.NullString `gorm:"column:node_id" json:"node_id"`                                            // store node id of opensource redis cluster mode
	StatPort          int            `gorm:"column:stat_port;default:0;NOT NULL" json:"stat_port"`                     // stat_port
	XagentPort        int            `gorm:"column:xagent_port;default:0;NOT NULL" json:"xagent_port"`                 // xagent_port
	HomePath          string         `gorm:"column:home_path;NOT NULL" json:"home_path"`                               // home_path
	BbcId             string         `gorm:"column:bbc_id;NOT NULL" json:"bbc_id"`                                     // bbc_id
	NodeShowID        string         `gorm:"column:node_show_id;NOT NULL" json:"node_show_id"`                         // node show id
	RoGroupID         int64          `gorm:"column:ro_group_id;default:0;NOT NULL" json:"ro_group_id"`                 // readonly group id
	RoGroupStatus     int            `gorm:"column:ro_group_status;default:0;NOT NULL" json:"ro_group_status"`         // readonly group status
	RoGroupWeight     int            `gorm:"column:ro_group_weight;default:0;NOT NULL" json:"ro_group_weight"`         // readonly group weight
	IsReadonly        int            `gorm:"column:is_readonly;default:0;NOT NULL" json:"is_readonly"`                 // is readonly or not
	ContainerId       string         `gorm:"column:container_id;NOT NULL" json:"container_id"`                         // container_id
	ContainerName     string         `gorm:"column:container_name;NOT NULL" json:"container_name"`                     // container_name
	McpackPort        int            `gorm:"column:mcpack_port;default:0;NOT NULL" json:"mcpack_port"`                 // mcpack_port
	StatLastWeek      string         `gorm:"column:stat_last_week;NOT NULL" json:"stat_last_week"`                     // stat_last_week
}

func (CacheInstance) TableName() string {
	return "cache_instance"
}
