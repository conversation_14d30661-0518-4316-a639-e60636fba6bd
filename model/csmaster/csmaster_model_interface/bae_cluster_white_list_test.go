package csmaster_model_interface

import (
	"testing"
)

func TestBaeClusterWhiteList_TableName(t *testing.T) {
	type fields struct {
		ID         int64
		ClusterID  int64
		VMUuID     string
		FloatingIP string
		Mode       string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "bae_cluster_white_list",
			fields: fields{
				ID:         1,
				ClusterID:  1,
				VMUuID:     "VMUuID",
				FloatingIP: "FloatingIP",
				Mode:       "Mode",
			},
			want: "bae_cluster_white_list",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ca := BaeClusterWhiteList{
				ID:         tt.fields.ID,
				ClusterID:  tt.fields.ClusterID,
				VMUuID:     tt.fields.VMUuID,
				FloatingIP: tt.fields.FloatingIP,
				Mode:       tt.fields.Mode,
			}
			if got := ca.TableName(); got != tt.want {
				t.Errorf("TableName() = %v, want %v", got, tt.want)
			}
		})
	}
}
