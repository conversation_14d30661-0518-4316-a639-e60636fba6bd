/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/19 zengshangy<PERSON>@baidu.com Exp
 *
 **************************************************************************/

/**
 * @file instance.go
 * <AUTHOR>
 * @date 2022/05/19 14:42:31
 * @brief instance model
 *
 **/

package csmaster_model_interface

import "time"

// define instance_order
type InstanceOrder struct {
	ID              int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	OrderID         string    `gorm:"column:order_id;NOT NULL" json:"order_id"`
	ParentID        string    `gorm:"column:parent_id;NOT NULL" json:"parent_id"`
	TaskID          string    `gorm:"column:task_id;NOT NULL" json:"task_id"`
	Status          string    `gorm:"column:status;NOT NULL" json:"status"`
	StartAT         time.Time `gorm:"column:start_at;NOT NULL;autoCreateTime" json:"start_at"`
	ErrMsg          string    `gorm:"column:err_msg;NOT NULL" json:"err_msg"`
	Parameters      string    `gorm:"column:parameters;NOT NULL" json:"parameters"`
	IamUserId       string    `gorm:"column:iam_user_id;NOT NULL" json:"iam_user_id"`
	Priority        string    `gorm:"column:priority;NOT NULL" json:"priority"`
	UserType        string    `gorm:"column:user_type;NOT NULL" json:"user_type"`
	ResourceOrderId string    `gorm:"column:resource_order_id;NOT NULL" json:"resource_order_id"`
	Result          string    `gorm:"column:result;NOT NULL" json:"result"`
	Type            string    `gorm:"column:type;NOT NULL" json:"type"`
	Action          string    `gorm:"column:action;NOT NULL" json:"action"`
}

// TableName of instance_order
func (InstanceOrder) TableName() string {
	return "instance_order"
}
