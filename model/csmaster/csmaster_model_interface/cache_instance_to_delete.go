package csmaster_model_interface

import (
	"database/sql"
	"time"
)

const (
	BNS_UNCLEAR_STATUS = 0
	BNS_CLEARER_STATUS = 1
)

type CacheInstanceToDelete struct {
	ID                int64         `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`                           // id
	ClusterID         int64         `gorm:"column:cluster_id;NOT NULL" json:"cluster_id"`                             // cluster id
	UserID            int           `gorm:"column:user_id;NOT NULL" json:"user_id"`                                   // user id
	Port              int           `gorm:"column:port;NOT NULL" json:"port"`                                         // port
	CreateTime        time.Time     `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_time"` // time
	Flavor            int           `gorm:"column:flavor;NOT NULL" json:"flavor"`                                     // flavor
	Uuid              string        `gorm:"column:uuid;NOT NULL" json:"uuid"`                                         // uuid
	CacheInstanceType int           `gorm:"column:cache_instance_type;NOT NULL" json:"cache_instance_type"`           // cache instance type
	MasterRedis       string        `gorm:"column:master_redis;NOT NULL" json:"master_redis"`                         // master redis
	SlaverRedis       string        `gorm:"column:slaver_redis;NOT NULL" json:"slaver_redis"`                         // slaver redis
	Persistence       int           `gorm:"column:persistence;NOT NULL" json:"persistence"`                           // persistence
	FixIP             string        `gorm:"column:fix_ip;NOT NULL" json:"fix_ip"`                                     // fix ip
	FloatingIP        string        `gorm:"column:floating_ip;NOT NULL" json:"floating_ip"`                           // floating ip
	Password          string        `gorm:"column:password;NOT NULL" json:"password"`                                 // password
	HashName          string        `gorm:"column:hash_name;NOT NULL" json:"hash_name"`                               // hash name
	HostName          string        `gorm:"column:host_name;NOT NULL" json:"host_name"`                               // host name
	ShardID           int           `gorm:"column:shard_id;default:0;NOT NULL" json:"shard_id"`                       // shard id
	DeleteTime        string        `gorm:"column:delete_time;NOT NULL" json:"delete_time"`                           // delete time
	Ipv6              string        `gorm:"column:ipv6;NOT NULL" json:"ipv6"`                                         // ipv6 address
	Status            string        `gorm:"column:status;NOT NULL" json:"status"`                                     // status
	InstanceID        sql.NullInt32 `gorm:"column:instance_id" json:"instance_id"`                                    // instance_id
	ClearStatus       int           `gorm:"column:clear_status;default:0;NOT NULL" json:"clear_status"`               //instance bns clear status
}

func (CacheInstanceToDelete) TableName() string {
	return "cache_instance_to_delete"
}
