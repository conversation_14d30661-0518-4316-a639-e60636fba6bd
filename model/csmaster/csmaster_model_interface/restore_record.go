package csmaster_model_interface

import "time"

/*
create table restore_record
(
    id           bigint auto_increment comment 'id'
        primary key,
    batch_id     varchar(200) default ''                not null comment 'batch_id',
    instance_id  int          default 0                 not null comment 'instance_id',
    cluster_id   int          default 0                 not null comment 'cluster_id',
    start_time   timestamp    default CURRENT_TIMESTAMP not null comment 'start_time',
    status       tinyint(2)   default 0                 not null comment 'status',
    restore_type tinyint(2)   default 0                 not null comment 'restore_type',
    bucket       varchar(200) default ''                not null comment 'bucket',
    object_key   varchar(200) default ''                not null comment 'object_key',
    shard_name   varchar(200) default ''                not null comment 'shard_name'
)
    comment 'restore_record' engine = InnoDB;
*/

const (
	RestoreInit = iota
	RestoreSuccess
	RestoreFailed
	RestoreBackupInProgress
	RestoreRecoveryInProgress
	RestoreRecoveryFailed
	RestoreWaitForExecute
	RestoreExecuteInProgress
	RestoreExecuteFailed
	RestoreExecuteSuccess
)

type RestoreRecord struct {
	ID          int64     `json:"id" gorm:"column:id"`                     // id
	BatchID     string    `json:"batch_id" gorm:"column:batch_id"`         // batch_id
	InstanceID  int       `json:"instance_id" gorm:"column:instance_id"`   // instance_id
	ClusterID   int       `json:"cluster_id" gorm:"column:cluster_id"`     // cluster_id
	StartTime   time.Time `json:"start_time" gorm:"column:start_time"`     // start_time
	Status      int8      `json:"status" gorm:"column:status"`             // status
	RestoreType int8      `json:"restore_type" gorm:"column:restore_type"` // restore_type
	Bucket      string    `json:"bucket" gorm:"column:bucket"`             // bucket
	ObjectKey   string    `json:"object_key" gorm:"column:object_key"`     // object_key
	ShardName   string    `json:"shard_name" gorm:"column:shard_name"`     // shard_name
}

func (m *RestoreRecord) TableName() string {
	return "restore_record"
}
