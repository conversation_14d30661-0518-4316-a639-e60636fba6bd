package csmaster_model_interface

import (
	"context"
	"testing"
)

func NumberWithinValidRange(t *testing.T) {
	if got := IsNumberInRange(5, "1-10"); !got {
		t.<PERSON>rrorf("IsNumberInRange() = %v, want %v", got, true)
	}
}

func NumberOutsideValidRange(t *testing.T) {
	if got := IsNumberInRange(15, "1-10"); got {
		t.<PERSON><PERSON>("IsNumberInRange() = %v, want %v", got, false)
	}
}

func NegativeNumberWithinRange(t *testing.T) {
	if got := IsNumberInRange(-5, "-10--1"); !got {
		t.<PERSON><PERSON><PERSON>("IsNumberInRange() = %v, want %v", got, true)
	}
}

func InvalidRangeFormat(t *testing.T) {
	if got := IsNumberInRange(5, "1-to-10"); got {
		t.<PERSON>("IsNumberInRange() = %v, want %v", got, false)
	}
}

func InvalidRangeFormat2(t *testing.T) {
	if got := IsNumberInRange(5, "aaaa"); got {
		t.Errorf("IsNumberInRange() = %v, want %v", got, false)
	}
}

func NonNumericRangeValues(t *testing.T) {
	if got := IsNumberInRange(5, "a-b"); got {
		t.Errorf("IsNumberInRange() = %v, want %v", got, false)
	}
}

func EmptyRangeString(t *testing.T) {
	if got := IsNumberInRange(5, ""); got {
		t.Errorf("IsNumberInRange() = %v, want %v", got, false)
	}
}

func ValueLegallySingleChoiceValid(t *testing.T) {
	confDef := UserConfList{ConfName: "testConf", ConfType: 1, ConfRange: "option1|option2", ConfUserVisible: 1}
	err := confDef.IsValueLegally(context.Background(), "testConf", "option1", true)
	if err != nil {
		t.Errorf("IsValueLegally() error = %v, want nil", err)
	}
}

func ValueLegallySingleChoiceInvalid(t *testing.T) {
	confDef := UserConfList{ConfName: "testConf", ConfType: 1, ConfRange: "option1|option2", ConfUserVisible: 1}
	err := confDef.IsValueLegally(context.Background(), "testConf", "invalidOption", true)
	if err == nil {
		t.Errorf("IsValueLegally() error = nil, want error")
	}
}

func ValueLegallyNumericValid(t *testing.T) {
	confDef := UserConfList{ConfName: "testConf", ConfType: 2, ConfRange: "1-10", ConfUserVisible: 1}
	err := confDef.IsValueLegally(context.Background(), "testConf", "5", true)
	if err != nil {
		t.Errorf("IsValueLegally() error = %v, want nil", err)
	}
}

func ValueLegallyNumericInvalid(t *testing.T) {
	confDef := UserConfList{ConfName: "testConf", ConfType: 2, ConfRange: "1-10", ConfUserVisible: 1}
	err := confDef.IsValueLegally(context.Background(), "testConf", "15", true)
	if err == nil {
		t.Errorf("IsValueLegally() error = nil, want error")
	}
}

func ValueLegallyMultiChoiceValid(t *testing.T) {
	confDef := UserConfList{ConfName: "testConf", ConfType: 3, ConfRange: "option1|option2", ConfUserVisible: 1}
	err := confDef.IsValueLegally(context.Background(), "testConf", "option1,option2", true)
	if err != nil {
		t.Errorf("IsValueLegally() error = %v, want nil", err)
	}
}

func ValueLegallyMultiChoiceInvalid(t *testing.T) {
	confDef := UserConfList{ConfName: "testConf", ConfType: 3, ConfRange: "option1|option2", ConfUserVisible: 1}
	err := confDef.IsValueLegally(context.Background(), "testConf", "option1,invalidOption", true)
	if err == nil {
		t.Errorf("IsValueLegally() error = nil, want error")
	}
}

func ValueLegallySpecialMultiChoiceValid(t *testing.T) {
	confDef := UserConfList{ConfName: "testConf", ConfType: 4, ConfRange: "option1|option2", ConfUserVisible: 1}
	err := confDef.IsValueLegally(context.Background(), "testConf", "option1option2", true)
	if err != nil {
		t.Errorf("IsValueLegally() error = %v, want nil", err)
	}
}

func ValueLegallySpecialMultiChoiceInvalid(t *testing.T) {
	confDef := UserConfList{ConfName: "testConf", ConfType: 4, ConfRange: "option1|option2", ConfUserVisible: 1}
	err := confDef.IsValueLegally(context.Background(), "testConf", "option1invalidOption", true)
	if err == nil {
		t.Errorf("IsValueLegally() error = nil, want error")
	}
}

func ValueLegallyConfNameMismatch(t *testing.T) {
	confDef := UserConfList{ConfName: "testConf", ConfType: 1, ConfRange: "option1|option2", ConfUserVisible: 1}
	err := confDef.IsValueLegally(context.Background(), "wrongConf", "option1", true)
	if err == nil {
		t.Errorf("IsValueLegally() error = nil, want error")
	}
}

func ValueLegallyUnvisibleConfig(t *testing.T) {
	confDef := UserConfList{ConfName: "testConf", ConfType: 1, ConfRange: "option1|option2", ConfUserVisible: IsConfUserVisiable}
	err := confDef.IsValueLegally(context.Background(), "testConf", "option1", false)
	if err != nil {
		t.Errorf("IsValueLegally() error = %v, want nil", err)
	}
}
