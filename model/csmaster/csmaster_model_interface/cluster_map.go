package csmaster_model_interface

import "time"

/*
create table cluster_map
(
    id           int auto_increment
        primary key,
    s_cluster_id varchar(64) not null,
    t_cluster_id varchar(64) not null,
    created_at   datetime    not null
);
*/
type ClusterMap struct {
	ID         int       `json:"id" gorm:"column:id"`
	SClusterID string    `json:"s_cluster_id" gorm:"column:s_cluster_id"`
	TClusterID string    `json:"t_cluster_id" gorm:"column:t_cluster_id"`
	CreatedAt  time.Time `json:"created_at" gorm:"column:created_at"`
}

func (m *ClusterMap) TableName() string {
	return "cluster_map"
}
