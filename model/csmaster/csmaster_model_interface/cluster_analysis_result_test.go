/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/23 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file cluster_analysis_result_test.go
 * <AUTHOR>
 * @date 2023/03/23 11:05:27
 * @brief
 *
 **/

package csmaster_model_interface

import (
	"testing"
	"time"
)

func TestClusterAnalysisResult_TableName(t *testing.T) {
	type fields struct {
		ID         int64
		ClusterID  int
		ShardID    int
		ShardName  string
		Type       int
		KeyName    string
		Info       string
		UpdateTime time.Time
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "cluster_analysis_result",
			fields: fields{
				ID:         1,
				ClusterID:  1,
				ShardID:    1,
				ShardName:  "test",
				Type:       1,
				KeyName:    "test",
				Info:       "test",
				UpdateTime: time.Now(),
			},
			want: "cluster_analysis_result",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cl := ClusterAnalysisResult{
				ID:        tt.fields.ID,
				ClusterID: tt.fields.ClusterID,
				// ShardId:    tt.fields.ShardID,
				ShardName:  tt.fields.ShardName,
				Type:       tt.fields.Type,
				KeyName:    tt.fields.KeyName,
				Info:       tt.fields.Info,
				UpdateTime: tt.fields.UpdateTime,
			}
			if got := cl.TableName(); got != tt.want {
				t.Errorf("TableName() = %v, want %v", got, tt.want)
			}
		})
	}
}
