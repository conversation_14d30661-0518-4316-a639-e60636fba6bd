package csmaster_model_interface

const (
	BaeDefaultRwPermission = "rw"
)

type BaeClusterWhiteList struct {
	ID         int64  `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"` // id
	ClusterID  int64  `gorm:"column:cluster_id;NOT NULL" json:"cluster_id"`   // cluster id
	VMUuID     string `gorm:"column:vm_uuid;NOT NULL" json:"vm_uuid"`         // vm uuid
	FloatingIP string `gorm:"column:floating_ip;NOT NULL" json:"floating_ip"` //  floating ip
	Mode       string `gorm:"column:mode;NOT NULL" json:"mode"`               // mode
}

func (BaeClusterWhiteList) TableName() string {
	return "bae_cluster_white_list"
}
