package csmaster_model_interface

import "time"

type EventList struct {
	Id         int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`   // id
	ClusterId  int64     `gorm:"column:cluster_id;NOT NULL" json:"cluster_id"`               // cluster id
	InstanceId int64     `gorm:"column:instance_id;NOT NULL" json:"instance_id"`             // instance id
	Level      int64     `gorm:"column:level;NOT NULL" json:"level"`                         // level
	Type       int64     `gorm:"column:type;NOT NULL" json:"type"`                           // type
	Time       time.Time `gorm:"column:time;default:CURRENT_TIMESTAMP;NOT NULL" json:"time"` // time
	EventDesc  string    `gorm:"column:event_desc;NOT NULL" json:"event_desc"`               // event desc
}

func (EventList) TableName() string {
	return "event_list"
}
