package csmaster_model_interface

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

// IsConfUserVisiable confDef中ConfUserVisible为不可见
const IsConfUserVisiable = 0

// IsValueLegally 判断一个参数值，是否符合这条confDef的规范
// 入参 confName string 想要修改的配置名
// 入参 confVal string 想要修改的配置值
// 入参 checkUnVisiable bool 是否检查不可见参数
//
// 检查成功为 nil
// 检查失败为 error
func (confDef UserConfList) IsValueLegally(ctx context.Context, confName string, confVal string, checkUnVisiable bool) error {
	if confDef.ConfName != confName {
		return errors.New("confName not equal")
	}
	// 用户不可见的配置放过
	if !checkUnVisiable && confDef.ConfUserVisible != IsConfUserVisiable {
		logger.DefaultLogger.Trace(ctx, "no need to check unvisiable conf")
		return nil
	}

	switch confDef.ConfType {
	case 1: // 单选
		subConfRange := strings.Split(confDef.ConfRange, "|")
		find, _ := base_utils.InArray(confVal, subConfRange)
		// 不在列表里返回false
		if !find {
			return errors.New("illegal param value")
		}
	case 2: // 数字
		intVal, err := strconv.Atoi(confVal)
		if err != nil {
			return fmt.Errorf("val:%s not numeric", confVal)
		}
		if !IsNumberInRange(intVal, confDef.ConfRange) {
			return fmt.Errorf("val:%s not in range %s", confVal, confDef.ConfRange)
		}
	case 3: // 多选 可以为空
		if confVal != "" {
			subConfRange := strings.Split(confDef.ConfRange, "|")
			subConfVal := strings.Split(confVal, ",")
			// 有一个子值不在范围内，返回false
			for _, toCheckItem := range subConfVal {
				find, _ := base_utils.InArray(toCheckItem, subConfRange)
				if !find {
					return errors.New("illegal param value")
				}
			}
		}
	case 4: // 特殊多选 可以为空
		if confVal != "" {
			subConfRange := strings.Split(confDef.ConfRange, "|")
			subConfVal := strings.Split(confVal, "")
			for _, toCheckItem := range subConfVal {
				find, _ := base_utils.InArray(toCheckItem, subConfRange)
				if !find {
					return errors.New("illegal param value")
				}
			}
		}
	default:
		return fmt.Errorf("not support this conf type:%d", confDef.ConfType)
	}
	return nil
}

func IsNumberInRange(number int, rangeStr string) bool {
	// 使用正则表达式分割范围字符串
	re := regexp.MustCompile(`(-?\d+)-(-?\d+)`)
	matches := re.FindStringSubmatch(rangeStr)

	if len(matches) != 3 {
		return false
	}

	// 解析范围的起始和结束值
	start, err1 := strconv.Atoi(matches[1])
	end, err2 := strconv.Atoi(matches[2])

	if err1 != nil || err2 != nil {
		return false
	}

	// 检查数字是否在范围内
	return number >= start && number <= end
}
