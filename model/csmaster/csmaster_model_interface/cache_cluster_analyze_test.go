/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/23 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file cache_cluster_analyze_test.go
 * <AUTHOR>
 * @date 2023/03/23 11:05:02
 * @brief
 *
 **/

package csmaster_model_interface

import (
	"testing"
	"time"
)

func TestCacheClusterAnalyze_TableName(t *testing.T) {
	type fields struct {
		ID              int64
		ClusterID       int
		Type            int
		BigkeyStatus    int
		BigkeyTopN      int
		CreateTime      time.Time
		AnalyzeExecutor int
		UpdateTime      time.Time
		TaskVersion     int
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "cache_cluster_analyze",
			fields: fields{
				ID:              1,
				ClusterID:       1,
				Type:            1,
				BigkeyStatus:    1,
				BigkeyTopN:      1,
				CreateTime:      time.Now(),
				AnalyzeExecutor: 1,
				UpdateTime:      time.Now(),
				TaskVersion:     1,
			},
			want: "cache_cluster_analyze",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ca := CacheClusterAnalyze{
				ID:              tt.fields.ID,
				ClusterID:       tt.fields.ClusterID,
				Type:            tt.fields.Type,
				BigkeyStatus:    tt.fields.BigkeyStatus,
				BigkeyTopN:      tt.fields.BigkeyTopN,
				CreateTime:      tt.fields.CreateTime,
				AnalyzeExecutor: tt.fields.AnalyzeExecutor,
				UpdateTime:      tt.fields.UpdateTime,
				TaskVersion:     tt.fields.TaskVersion,
			}
			if got := ca.TableName(); got != tt.want {
				t.Errorf("TableName() = %v, want %v", got, tt.want)
			}
		})
	}
}
