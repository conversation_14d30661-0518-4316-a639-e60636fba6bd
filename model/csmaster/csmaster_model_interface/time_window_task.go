package csmaster_model_interface

import (
	"time"
)

const (
	TW_TASK_STATUS_WAITING   = "waiting"
	TW_TASK_STATUS_EXECUTING = "executing"
	TW_TASK_STATUS_SUCCESS   = "success"
	TW_TASK_STATUS_ERROR     = "error"
	TW_TASK_STATUS_CANCELLED = "cancelled"

	TW_TASK_TYPE_RELAUNCH        = "relaunch"
	TW_TASK_TYPE_UPGRADE         = "upgrade"
	TW_TASK_TYPE_MODIFY          = "modify"
	TW_TASK_TYPE_MODIFY_AZ       = "modify_az"
	TW_TASK_TYPE_MODIFY_ENTRANCE = "modify_default_entrance"

	TW_TASK_FROM_CSMASTER = "csmaster"
	TW_TASK_FROM_X1       = "x1-api"
)

type TimeWindowTask struct {
	ID            int       `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`       // id
	ClusterID     int       `gorm:"column:cluster_id;NOT NULL" json:"cluster_id"`         // cluster id
	TaskStatus    string    `gorm:"column:task_status;NOT NULL" json:"task_status"`       // task status
	CreateTime    time.Time `gorm:"column:create_time;" json:"create_time"`               // create time
	TaskType      string    `gorm:"column:task_type;NOT NULL" json:"task_type"`           // task type
	TaskParams    string    `gorm:"column:task_params;default:NULL" json:"task_params"`   // task params
	TransactionID string    `gorm:"column:transaction_id;NOT NULL" json:"transaction_id"` // transaction id
	ExecuteTime   time.Time `gorm:"column:execute_time;" json:"execute_time"`             // execute time
	TaskFrom      string    `gorm:"column:task_from;NOT NULL" json:"task_from"`           // task from
	TaskID        string    `gorm:"column:task_id;NOT NULL" json:"task_id"`               // task id
	TaskDetail    string    `gorm:"column:task_detail;NOT NULL" json:"task_detail"`       // task detail
}

// TableName of cluster_analysis_result
func (TimeWindowTask) TableName() string {
	return "time_window_task"
}
