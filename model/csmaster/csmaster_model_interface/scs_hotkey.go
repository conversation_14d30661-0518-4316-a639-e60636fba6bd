/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2024/01/23
 * File: scs_hotkey.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package csmaster_model_interface TODO package function desc
package csmaster_model_interface

import "time"

type ScsHotkey struct {
	ID            int       `json:"id" gorm:"column:id" ukey:"id"`                 // id
	ClusterShowID string    `json:"cluster_show_id" gorm:"column:cluster_show_id"` // cluster show id
	ShardID       int       `json:"shard_id" gorm:"column:shard_id"`               // shard id
	NodeID        int       `json:"node_id" gorm:"column:node_id"`                 // node id
	InstanceType  int8      `json:"instance_type" gorm:"column:instance_type"`     // instance type
	TimeStamp     time.Time `json:"time_stamp" gorm:"column:time_stamp"`           // time
	KeyName       string    `json:"key_name" gorm:"column:key_name"`               // key name
	KeyType       string    `json:"key_type" gorm:"column:key_type"`               // key type
	KeyDb         int       `json:"key_db" gorm:"column:key_db"`                   // key db
	KeyFreqCnt    int       `json:"key_freq_cnt" gorm:"column:key_freq_cnt"`       // key freq cnt
}

func (m *ScsHotkey) TableName() string {
	return "scs_hotkey"
}
