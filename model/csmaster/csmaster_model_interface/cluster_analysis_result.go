/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/22 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file cluster_analysis_result.go
 * <AUTHOR>
 * @date 2023/03/22 15:20:35
 * @brief
 *
 **/

package csmaster_model_interface

import "time"

type ClusterAnalysisResult struct {
	ID         int64     `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`                           // id
	ClusterID  int       `gorm:"column:cluster_id;NOT NULL" json:"cluster_id"`                             // cluster id
	ShardID    int       `gorm:"column:shard_id;NOT NULL" json:"shard_id"`                                 // shard id
	ShardName  string    `gorm:"column:shard_name;NOT NULL" json:"shard_name"`                             // shard name
	Type       int       `gorm:"column:type;NOT NULL" json:"type"`                                         // analyze type
	KeyName    string    `gorm:"column:key_name;NOT NULL" json:"key_name"`                                 // key name
	Info       string    `gorm:"column:info;NOT NULL" json:"info"`                                         // key info
	UpdateTime time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;NOT NULL" json:"update_time"` // update time
	TotalKeys  int64     `gorm:"column:total_keys;NOT NULL" json:"total_keys"`                             // total keys
}

// TableName of cluster_analysis_result
func (ClusterAnalysisResult) TableName() string {
	return "cluster_analysis_result"
}
