/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/05 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file readonly_group.go
 * <AUTHOR>
 * @date 2022/05/05 16:14:27
 * @brief readonly group model definiton
 *
 **/

package csmaster_model_interface

import "time"

// ReadonlyGroup definition
type ReadonlyGroup struct {
	ID                  int64            `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`                               // id
	ClusterID           int64            `gorm:"column:cluster_id;NOT NULL" json:"cluster_id"`                                 // cluster id
	RoGroupName         string           `gorm:"column:ro_group_name;NOT NULL" json:"ro_group_name"`                           // ro group name
	RoGroupShowID       string           `gorm:"column:ro_group_show_id;NOT NULL" json:"ro_group_show_id"`                     // ro group show id
	Status              int              `gorm:"column:status;NOT NULL" json:"status"`                                         // status
	Domain              string           `gorm:"column:domain;NOT NULL" json:"domain"`                                         // ro group domain
	EnableDelayOff      int              `gorm:"column:enable_delay_off;default:0;NOT NULL" json:"enable_delay_off"`           // enable delay off
	UserID              int64            `gorm:"column:user_id;NOT NULL" json:"user_id"`                                       // user id
	EnableThreshold     int              `gorm:"column:enable_threshold;default:20;NOT NULL" json:"enable_threshold"`          // delay threshold
	LeastInstanceAmount int              `gorm:"column:least_instance_amount;default:1;NOT NULL" json:"least_instance_amount"` // least instance amount
	IsBalanceReload     int              `gorm:"column:is_balance_reload;default:0;NOT NULL" json:"is_balance_reload"`         // is balance reload
	Eip                 string           `gorm:"column:eip;NOT NULL" json:"eip"`                                               // eip
	EipStatus           string           `gorm:"column:eip_status;NOT NULL" json:"eip_status"`                                 // eip status
	BlbID               string           `gorm:"column:blb_id;NOT NULL" json:"blb_id"`                                         // blb id
	IP                  string           `gorm:"column:ip;NOT NULL" json:"ip"`                                                 // ip
	BgwGroupExclusive   int              `gorm:"column:bgw_group_exclusive;default:0;NOT NULL" json:"bgw_group_exclusive"`     // bgw group exclusive
	BgwGroupID          string           `gorm:"column:bgw_group_id;NOT NULL" json:"bgw_group_id"`                             // bgw group id
	ListenerPort        int              `gorm:"column:listener_port;default:0;NOT NULL" json:"listener_port"`                 // listener port
	VpcID               string           `gorm:"column:vpc_id;NOT NULL" json:"vpc_id"`                                         // vpc id
	SubnetID            string           `gorm:"column:subnet_id;NOT NULL" json:"subnet_id"`                                   // subnet id
	OpType              int              `gorm:"column:op_type;default:0;NOT NULL" json:"op_type"`                             // op type
	CreateTime          time.Time        `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_time"`     // create time
	TransactionID       string           `gorm:"column:transaction_id;NOT NULL" json:"transaction_id"`                         // transaction id
	BlbIpv6ID           string           `gorm:"column:blb_ipv6_id" json:"blb_ipv6_id"`                                        // blb ipv6 id
	BlbIpv6IP           string           `gorm:"column:blb_ipv6_ip" json:"blb_ipv6_ip"`                                        // blb ipv6 address
	PublicDomain        string           `gorm:"column:public_domain" json:"public_domain"`
	CacheInstances      []*CacheInstance `gorm:"foreignKey:RoGroupID;references:ID"`
	UserInfo            *Userinfo        `gorm:"foreignKey:Id;references:UserID"`
	EndpointId          string           `gorm:"column:endpoint_id" json:"endpoint_id"`
	EndpointIp          string           `gorm:"column:endpoint_ip" json:"endpoint_ip"`
	OrderID             string           `gorm:"column:order_id" json:"order_id"`
}

// TableName of ReadonlyGroup
func (ReadonlyGroup) TableName() string {
	return "ro_group"
}
