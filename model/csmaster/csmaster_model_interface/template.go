/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/04/19 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file system_template.go
 * <AUTHOR>
 * @date 2022/04/19 14:42:31
 * @brief system template
 *
 **/

package csmaster_model_interface

import "time"

// SystemTemplate definition
type SystemTemplate struct {
	ID               int32     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	ConfName         string    `gorm:"column:conf_name;NOT NULL" json:"conf_name"`
	ConfModule       int32     `gorm:"column:conf_module;NOT NULL" json:"conf_module"`
	ConfDesc         string    `gorm:"column:conf_desc;NOT NULL" json:"conf_desc"`
	ConfType         int8      `gorm:"column:conf_type;NOT NULL" json:"conf_type"`
	ConfRange        string    `gorm:"column:conf_range;NOT NULL" json:"conf_range"`
	ConfDefault      string    `gorm:"column:conf_default;NOT NULL" json:"conf_default"`
	ConfCacheVersion int32     `gorm:"column:conf_cache_version;NOT NULL" json:"conf_cache_version"`
	ConfRedisVersion string    `gorm:"column:conf_redis_version;NOT NULL" json:"conf_redis_version"`
	ConfUserVisible  int32     `gorm:"column:conf_user_visible;NOT NULL" json:"conf_user_visible"`
	NeedReboot       int32     `gorm:"column:need_reboot;NOT NULL" json:"need_reboot"`
	CreateTime       time.Time `gorm:"column:create_time;NOT NULL" json:"create_time"`
	UpdateTime       time.Time `gorm:"column:update_time;NOT NULL" json:"update_time"`
	DeleteTime       time.Time `gorm:"column:delete_time;NOT NULL" json:"delete_time"`
}

// TableName of systemTemplate
func (SystemTemplate) TableName() string {
	return "user_conf_list"
}

// TemplateRecord records all templates applied to one cluster
type TemplateRecord struct {
	ID            int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	TemplateID    int64     `gorm:"column:template_id;NOT NULL" json:"template_id"`
	ClusterID     int64     `gorm:"column:cluster_id;NOT NULL" json:"cluster_id"`
	ClusterShowID string    `gorm:"column:cluster_show_id; NOT NULL" json:"cluster_show_id"`
	UserID        int64     `gorm:"column:user_id;NOT NULL" json:"user_id"`
	CreateTime    time.Time `gorm:"column:create_time;NOT NULL" json:"create_time"`
	UpdateTime    time.Time `gorm:"column:update_time;NOT NULL" json:"update_time"`
	DeleteTime    time.Time `gorm:"column:delete_time;NOT NULL" json:"delete_time"`
	UserInfo      *Userinfo `gorm:"foreignKey:UserID;references:Id"`
}

// TableName of TemplateRecord
func (TemplateRecord) TableName() string {
	return "template_record"
}

// Template define parameter template
type Template struct {
	ID             int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	TemplateShowID string    `gorm:"column:template_show_id;NOT NULL" json:"template_show_id"`
	UserID         int64     `gorm:"column:user_id;NOT NULL" json:"user_id"`
	Name           string    `gorm:"column:name;NOT NULL" json:"name"`
	ClusterType    string    `gorm:"column:cluster_type;NOT NULL" json:"cluster_type"`
	Engine         string    `gorm:"column:engine;NOT NULL" json:"engine"`
	EngineVersion  string    `gorm:"column:engine_version;NOT NULL" json:"engine_version"`
	TemplateType   int32     `gorm:"column:template_type;NOT NULL" json:"template_type"`
	NeedReboot     int32     `gorm:"column:need_reboot;NOT NULL" json:"need_reboot"`
	Comment        string    `gorm:"column:comment;NOT NULL" json:"comment"`
	Parameters     string    `gorm:"column:parameters;NOT NULL" json:"parameters"`
	ParamNum       int32     `gorm:"column:param_num;NOT NULL" json:"param_num"`
	CreateTime     time.Time `gorm:"column:create_time;NOT NULL;autoCreateTime" json:"create_time"`
	UpdateTime     time.Time `gorm:"column:update_time;NOT NULL;autoUpdateTime" json:"update_time"`
	DeleteTime     time.Time `gorm:"column:delete_time;NOT NULL" json:"delete_time"`
	UserInfo       *Userinfo `gorm:"foreignKey:UserID;references:Id"`
}

// TableName of Template
func (Template) TableName() string {
	return "parameter_template"
}
