/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2024/01/23
 * File: scs_hotkey_test.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package csmaster_model_interface TODO package function desc
package csmaster_model_interface

import (
	"testing"
	"time"
)

func TestScsHotkey_TableName(t *testing.T) {
	obj := &ScsHotkey{
		ID:            0,
		ClusterShowID: "",
		ShardID:       0,
		NodeID:        0,
		InstanceType:  0,
		TimeStamp:     time.Time{},
		KeyName:       "",
		KeyType:       "",
		KeyDb:         0,
		KeyFreqCnt:    0,
	}
	if obj.TableName() != "scs_hotkey" {
		t.<PERSON>("table name not right")
	}
}
