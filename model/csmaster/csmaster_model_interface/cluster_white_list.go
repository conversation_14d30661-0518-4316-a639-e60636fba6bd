/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/03/24
 * File: cluster_white_list.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package models TODO package function desc
package csmaster_model_interface

const (
	DefaultRwPermission = "rw"
)

type ClusterWhiteList struct {
	Id         int64  `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"` // id
	ClusterId  int64  `gorm:"column:cluster_id;NOT NULL" json:"cluster_id"`   // cluster id
	VmUuid     string `gorm:"column:vm_uuid;NOT NULL" json:"vm_uuid"`         // vm uuid
	FloatingIp string `gorm:"column:floating_ip;NOT NULL" json:"floating_ip"` //  floating ip
	Mode       string `gorm:"column:mode;NOT NULL" json:"mode"`               // mode
	InsertMode string `gorm:"column:insert_mode;NOT NULL" json:"insert_mode"` // insert mode
}

func (ClusterWhiteList) TableName() string {
	return "cluster_white_list"
}
