/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2023/11/15
 * File: multi_active.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package csmaster_model_interface TODO package function desc
package csmaster_model_interface

import "time"

// MultiActiveChannelList bce_scs.multi_active_channel_list
type MultiActiveChannelList struct {
	ID                int       `gorm:"column:id;AUTO_INCREMENT;primary_key" ukey:"id"`        // id
	ClusterID         int       `gorm:"column:cluster_id;NOT NULL"`                            // cluster_id
	PeerClusterShowID string    `gorm:"column:peer_cluster_show_id;NOT NULL"`                  // peer_cluster_show_id
	PeerIP            string    `gorm:"column:peer_ip;NOT NULL"`                               // peer_ip
	PeerPort          int       `gorm:"column:peer_port;NOT NULL"`                             // peer_port
	PeerAuth          string    `gorm:"column:peer_auth;NOT NULL"`                             // peer_auth
	Status            int       `gorm:"column:status;NOT NULL"`                                // status
	CreateTime        time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL"` // create_time
}

func (m *MultiActiveChannelList) TableName() string {
	return "multi_active_channel_list"
}
