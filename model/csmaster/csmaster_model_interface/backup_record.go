package csmaster_model_interface

import "time"

/*
create table backup_record
(
    id          bigint auto_increment comment 'id'
        primary key,
    batch_id    varchar(200)  default ''                not null comment 'batch_id',
    instance_id int           default 0                 not null comment 'instance_id',
    cluster_id  int           default 0                 not null comment 'cluster_id',
    start_time  timestamp     default CURRENT_TIMESTAMP not null comment 'start_time',
    duration    int           default 0                 not null comment 'duration',
    status      tinyint(2)    default 0                 not null comment 'status',
    backup_type tinyint(2)    default 0                 not null comment 'backup_type',
    bucket      varchar(200)  default ''                not null comment 'bucket',
    object_key  varchar(200)  default ''                not null comment 'object_key',
    object_size bigint        default 0                 not null comment 'object_size',
    shard_name  varchar(200)  default ''                not null comment 'shard_name',
    comment     varchar(1000) default ''                not null comment 'comment',
    expairation int           default 0                 not null comment 'expairation'
)
    comment 'backup_record' engine = InnoDB;
*/

const (
	BackupInit = iota
	BackupSuccess
	BackupInProgress
	BackupFailed
	BackupExpired
	RecoveryInProgress
	RecoveryFailed
	WaitForRestart
	RestartInProgress
	RestartFailed
	RecoverySuccess
)

type BackupRecord struct {
	ID          int64     `json:"id" gorm:"column:id" ukey:"id"`         // id
	BatchID     string    `json:"batch_id" gorm:"column:batch_id"`       // batch_id
	InstanceID  int       `json:"instance_id" gorm:"column:instance_id"` // instance_id
	ClusterID   int       `json:"cluster_id" gorm:"column:cluster_id"`   // cluster_id
	StartTime   time.Time `json:"start_time" gorm:"column:start_time"`   // start_time
	Duration    int       `json:"duration" gorm:"column:duration"`       // duration
	Status      int8      `json:"status" gorm:"column:status"`           // status
	BackupType  int8      `json:"backup_type" gorm:"column:backup_type"` // backup_type
	Bucket      string    `json:"bucket" gorm:"column:bucket"`           // bucket
	ObjectKey   string    `json:"object_key" gorm:"column:object_key"`   // object_key
	ObjectSize  int64     `json:"object_size" gorm:"column:object_size"` // object_size
	ShardName   string    `json:"shard_name" gorm:"column:shard_name"`   // shard_name
	Comment     string    `json:"comment" gorm:"column:comment"`         // comment
	Expairation int       `json:"expairation" gorm:"column:expairation"` // expairation
}

func (m *BackupRecord) TableName() string {
	return "backup_record"
}
