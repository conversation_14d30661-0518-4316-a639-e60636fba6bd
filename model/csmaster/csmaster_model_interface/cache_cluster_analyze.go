/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/22 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file cache_cluster_analyze.go
 * <AUTHOR>
 * @date 2023/03/22 15:17:09
 * @brief
 *
 **/

package csmaster_model_interface

import "time"

type CacheClusterAnalyze struct {
	ID              int64     `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`                             // id
	ClusterID       int       `gorm:"column:cluster_id;NOT NULL" json:"cluster_id"`                               // cluster id
	Type            int       `gorm:"column:type;NOT NULL" json:"type"`                                           // analyze type
	BigkeyStatus    int       `gorm:"column:bigkey_status;NOT NULL" json:"bigkey_status"`                         // bigkey status
	BigkeyTopN      int       `gorm:"column:bigkey_top_n;NOT NULL" json:"bigkey_top_n"`                           // top n bigkey
	CreateTime      time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_time"`   // create time
	AnalyzeExecutor int       `gorm:"column:analyze_executor;NOT NULL" json:"analyze_executor"`                   // analyze executor
	UpdateTime      time.Time `gorm:"column:update_time;default:0000-00-00 00:00:00;NOT NULL" json:"update_time"` // update time
	TaskVersion     int       `gorm:"column:task_version;default:0;NOT NULL" json:"task_version"`                 // task version
}

// TableName of cache_cluster_analyze
func (CacheClusterAnalyze) TableName() string {
	return "cache_cluster_analyze"
}
