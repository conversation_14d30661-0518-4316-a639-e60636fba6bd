package csmaster_model_interface

import "time"

/*
CREATE TABLE `conf_record_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `cluster_id` int(11) NOT NULL COMMENT 'cluster_id',
  `conf_name` varchar(200) NOT NULL DEFAULT '' COMMENT 'conf_name',
  `conf_module` int(11) NOT NULL DEFAULT '0' COMMENT 'conf_module',
  `value` varchar(128) NOT NULL DEFAULT '' COMMENT 'value',
  `effected` int(11) NOT NULL DEFAULT '0' COMMENT 'effect',
  PRIMARY KEY (`id`),
  KEY `cluster_id` (`cluster_id`)
) ENGINE=InnoDB AUTO_INCREMENT=757 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=8 COMMENT='conf_record_list';
*/

const (
	CONF_MODULE_REDIS = iota + 1
	CONF_MODULE_PROXY
	CONF_MODULE_REDIS_PROXY
	CONF_MODULE_META
)

type ConfRecordList struct {
	ID         int    `json:"id" gorm:"column:id"`                   // id
	ClusterID  int    `json:"cluster_id" gorm:"column:cluster_id"`   // cluster_id
	ConfName   string `json:"conf_name" gorm:"column:conf_name"`     // conf_name
	ConfModule int    `json:"conf_module" gorm:"column:conf_module"` // conf_module
	Value      string `json:"value" gorm:"column:value"`             // value
	Effected   int    `json:"effected" gorm:"column:effected"`       // effect
}

func (m *ConfRecordList) TableName() string {
	return "conf_record_list"
}

type ConfHistoryList struct {
	ID          int64     `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	ClusterID   int       `gorm:"index;column:cluster_id" json:"cluster_id"`
	ConfName    string    `gorm:"type:varchar(200);column:conf_name" json:"conf_name"`
	ValueBefore string    `gorm:"type:varchar(128);column:value_before" json:"value_before"`
	ValueAfter  string    `gorm:"type:varchar(128);column:value_after" json:"value_after"`
	ChangeTime  time.Time `gorm:"type:timestamp;default:CURRENT_TIMESTAMP;column:change_time" json:"change_time"`
}

func (m *ConfHistoryList) TableName() string {
	return "conf_history_list"
}

/*
CREATE TABLE `user_conf_list` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `conf_name` varchar(200) NOT NULL DEFAULT '' COMMENT 'conf_name',
  `conf_module` int(11) NOT NULL DEFAULT '0' COMMENT 'conf_module',
  `conf_desc` varchar(1024) NOT NULL DEFAULT '' COMMENT 'conf_desc',
  `conf_type` tinyint(11) NOT NULL DEFAULT '0' COMMENT 'conf_type',
  `conf_range` varchar(200) NOT NULL DEFAULT '' COMMENT 'conf_range',
  `conf_default` varchar(128) NOT NULL DEFAULT '' COMMENT 'conf_default',
  `conf_cache_version` int(11) NOT NULL DEFAULT '0' COMMENT 'conf_cache_version',
  `conf_redis_version` varchar(32) NOT NULL DEFAULT '0' COMMENT 'conf_redis_version',
  `conf_user_visible` int(11) NOT NULL DEFAULT '0' COMMENT 'conf_user_visible',
  `need_reboot` int(11) NOT NULL DEFAULT '0' COMMENT 'need_reboot',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=8 COMMENT='user_conf_list'
*/

type UserConfList struct {
	ID                int    `json:"id" gorm:"column:id"`                                   // id
	ConfName          string `json:"conf_name" gorm:"column:conf_name"`                     // conf_name
	ConfModule        int    `json:"conf_module" gorm:"column:conf_module"`                 // conf_module
	ConfDesc          string `json:"conf_desc" gorm:"column:conf_desc"`                     // conf_desc
	ConfType          int    `json:"conf_type" gorm:"column:conf_type"`                     // conf_type
	ConfRange         string `json:"conf_range" gorm:"column:conf_range"`                   // conf_range
	ConfDefault       string `json:"conf_default" gorm:"column:conf_default"`               // conf_default
	ConfCacheVersion  int    `json:"conf_cache_version" gorm:"column:conf_cache_version"`   // conf_cache_version
	ConfRedisVersion  string `json:"conf_redis_version" gorm:"column:conf_redis_version"`   // conf_redis_version
	ConfUserVisible   int    `json:"conf_user_visible" gorm:"column:conf_user_visible"`     // conf_user_visible
	NeedReboot        int    `json:"need_reboot" gorm:"column:need_reboot"`                 // need_reboot
	Engine            string `json:"engine" gorm:"column:engine"`                           // engine
	LowestFullVersion string `json:"lowest_full_version" gorm:"column:lowest_full_version"` // lowest_full_version
}

func (m *UserConfList) TableName() string {
	return "user_conf_list"
}
