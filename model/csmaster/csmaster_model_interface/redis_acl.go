package csmaster_model_interface

import "time"

/*
CREATE TABLE `cluster_acl_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `cluster_id` int(11) NOT NULL COMMENT 'cluster id',
  `user_name` varchar(200) NOT NULL COMMENT 'user name',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'time',
  `password` varchar(200) NOT NULL COMMENT 'password',
  `allowed_commands` varchar(1000) DEFAULT '' COMMENT 'allowed commands',
  `allowed_sub_commands` varchar(1000) DEFAULT '' COMMENT 'allowed sub commands',
  `key_patterns` varchar(200) DEFAULT '' COMMENT 'slaver redis',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'time',
  `update_status` tinyint(1) NOT NULL COMMENT 'key patterns',
  `extra` varchar(200) NOT NULL COMMENT 'extra',
  `transaction_id` varchar(50) NOT NULL COMMENT 'transaction id',
  `user_type` int(11) NOT NULL DEFAULT '-1' COMMENT 'user_type',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1370 DEFAULT CHARSET=utf8 COMMENT='cluster acl user info';
*/

const (
	ACLUSER_UNKOWN_STATUS = iota - 1
	ACLUSER_UPDATE_SUCCESS
	ACLUSER_CREATING
	ACLUSER_UPDATING
	ACLUSER_UPDATE_FAILED
	ACLUSER_DELETING
	ACLUSER_DELETED
	ACLUSER_DEL_FAILED
)

const (
	ACLUSER_AUTHORITY_UNKOWN = -1
	ACLUSER_AUTHORITY_ALL    = 1
	ACLUSER_AUTHORITY_READ   = 2
)

type ClusterAclUser struct {
	ID                 int       `json:"id" gorm:"column:id"`                                     // id
	ClusterID          int       `json:"cluster_id" gorm:"column:cluster_id"`                     // cluster id
	UserName           string    `json:"user_name" gorm:"column:user_name"`                       // user name
	CreateTime         time.Time `json:"create_time" gorm:"column:create_time"`                   // time
	Password           string    `json:"password" gorm:"column:password"`                         // password
	AllowedCommands    string    `json:"allowed_commands" gorm:"column:allowed_commands"`         // allowed commands
	AllowedSubCommands string    `json:"allowed_sub_commands" gorm:"column:allowed_sub_commands"` // allowed sub commands
	KeyPatterns        string    `json:"key_patterns" gorm:"column:key_patterns"`                 // slaver redis
	ModifyTime         time.Time `json:"modify_time" gorm:"column:modify_time"`                   // time
	UpdateStatus       int8      `json:"update_status" gorm:"column:update_status"`               // key patterns
	Extra              string    `json:"extra" gorm:"column:extra"`                               // extra
	TransactionID      string    `json:"transaction_id" gorm:"column:transaction_id"`             // transaction id
	UserType           int       `json:"user_type" gorm:"column:user_type"`                       // user_type
}

func (m *ClusterAclUser) TableName() string {
	return "cluster_acl_user"
}
