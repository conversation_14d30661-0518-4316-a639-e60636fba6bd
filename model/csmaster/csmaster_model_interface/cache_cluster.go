/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> <PERSON> (<EMAIL>)
 * Date: 2022/03/14
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package models TODO package function desc
package csmaster_model_interface

import (
	"database/sql"
	"time"
)

const (
	VersionMemcached       = 1001
	VersionRedisStandalone = 7001
)

const (
	CACHE_CLUSTER_CREATING                               = iota // 0
	CACHE_CLUSTER_CREATING_RECV_CALLBACK                        // 1
	CACHE_CLUSTER_CREATING_ELB                                  // 2
	CACHE_CLUSTER_CREATING_LISTENER                             // 3
	CACHE_CLUSTER_CREATING_BACKEND                              // 4
	CACHE_CLUSTER_RUNNING                                       // 5
	CACHE_CLUSTER_REBOOTING                                     // 6
	CACHE_CLUSTER_PAUSING                                       // 7
	CACHE_CLUSTER_PAUSED                                        // 8
	CACHE_CLUSTER_DELETING                                      // 9
	CACHE_CLUSTER_DELETED                                       //10
	CACHE_CLUSTER_DELETE_FAILED                                 // 11
	CACHE_CLUSTER_CREATED_FAILED                                // 12
	CACHE_CLUSTER_MODIFYING                                     // 13
	CACHE_CLUSTER_MODIFYING_RECV_CALLBACK                       // 14
	CACHE_CLUSTER_ERROR                                         // 15
	CACHE_CLUSTER_MODIFIED_FAILED                               // 16
	CACHE_CLUSTER_REPLACING                                     // 17
	CACHE_CLUSTER_REPLACING_RECV_CALLBACK                       // 18
	CACHE_CLUSTER_MODIFYING_RESIZING                            // 19
	CACHE_CLUSTER_MODIFYING_RESIZING_CHECKER                    // 20
	CACHE_CLUSTER_CONFIGURING                                   // 21
	CACHE_CLUSTER_CONFIGURED                                    // 22
	CACHE_CLUSTER_MODIFY_CONFIGURING                            // 23
	CACHE_CLUSTER_MODIFY_CONFIGURED                             // 24
	CACHE_CLUSTER_REPLACE_CONFIGURING                           // 25
	CACHE_CLUSTER_REPLACE_CONFIGURED                            // 26
	CACHE_CLUSTER_CREATING_RECV_HEART_BEAT                      // 27
	CACHE_CLUSTER_CREATING_DNS                                  // 28
	CACHE_CLUSTER_RO_GROUP_CREATING                             // 29
	CACHE_CLUSTER_RO_GROUP_DELETING                             // 30
	CACHE_CLUSTER_RO_INSTANCE_DELETING                          // 31
	CACHE_CLUSTER_RO_GROUP_CREATE_FAIED                         // 32
	CACHE_CLUSTER_RO_GROUP_UPDATING                             // 33
	CACHE_CLUSTER_MODIFYING_RECV_HEART_BEAT                     // 34
	CACHE_CLUSTER_MODIFYING_META_TOPOLOGY1                      // 35
	CACHE_CLUSTER_MODIFYING_META_TOPOLOGY2                      // 36
	CACHE_CLUSTER_MODIFYING_DNS                                 // 37
	CACHE_CLUSTER_REPLACING_BACKEND                             // 38
	CACHE_CLUSTER_MODIFYING_EXPANSION                           // 39
	CACHE_CLUSTER_MODIFYING_REDUCTION                           // 40
	CACHE_CLUSTER_REDUCTION_META                                // 41
	CACHE_CLUSTER_REDUCTION_DELETING                            // 42
	CACHE_CLUSTER_EXPANSION_CALLBACK                            // 43
	CACHE_CLUSTER_EXPANSION_META                                // 44
	CACHE_CLUSTER_EXPANSION_MIGRATING                           // 45
	CACHE_CLUSTER_REPLACING_CHECK_RESOURCE                      // 46
	CACHE_CLUSTER_REPLACING_CHECK_HEARTBEAT                     // 47
	CACHE_CLUSTER_REPLACING_CONFIGURE_TOPOLOGY                  // 48
	CACHE_CLUSTER_CREATING_CHECK_RESOURCE                       // 49
	CACHE_CLUSTER_CREATING_CHECK_HEARTBEAT                      // 50
	CACHE_CLUSTER_FLUSHING                                      // 51
	CACHE_CLUSTER_FLUSH_FAILED                                  // 52
	CACHE_CLUSTER_RECOVERING                                    // 53
	CACHE_CLUSTER_RECOVER_FAILED                                // 54
	CACHE_CLUSTER_CONFIGURING_BCM                               // 55
	CACHE_CLUSTER_EXPANSION_BCM                                 // 56
	CACHE_CLUSTER_AZTRANSFORM_FAILED                            // 57
	CACHE_CLUSTER_PRECREAT                                      // 58
	CACHE_CLUSTER_MODIFYING_META_TOPOLOGY3                      //59
	CACHE_CLUSTER_RESTARTING                                    //60
	CACHE_CLUSTER_MODIFYING_BACKEND                             //61
	CACHE_CLUSTER_CHECKING_REDIS_REPLICATION                    //62
	CACHE_CLUSTER_EXCHANGE_MASTER_SLAVE_CHECK                   //63
	CACHE_CLUSTER_EXCHANGE_MASTER_SLAVE                         //64
	CACHE_CLUSTER_EXCHANGE_ENTRANCE                             //65
	CACHE_CLUSTER_EXCHANGE_RECHECK                              //66
	CACHE_CLUSTER_EXCHANGE_FAILED                               //67
	CACHE_CLUSTER_REPLACING_META                                //68
	CACHE_CLUSTER_REPLICATION_CHANGE_MASTER_CHANGE              //69
	CACHE_CLUSTER_REPLICATION_CHANGE_RESOURCE_REQUEST           //70
	CACHE_CLUSTER_REPLICATION_CHANGE_RESOURCE_QUERY             //71
	CACHE_CLUSTER_REPLICATION_CHANGE_TOPO_CHANGE                //72
	CACHE_CLUSTER_REPLICATION_CHANGE_CLUSTER_TOPO_CHANGE        // 73
	CACHE_CLUSTER_REPLICATION_CHANGE_BACKEND                    //74
	CACHE_CLUSTER_REPLICATION_CHANGE_CLEAR                      //75
	CACHE_CLUSTER_REPLACING_TOPO                                //76
	CACHE_CLUSTER_UPDATE_IMAGE                                  //77
	MEMCACHEDV1_CHECK_INSTANCE_RUNNING                          //78
	CACHE_CLUSTER_RESTORE_RECOVERING                            //79
	CACHE_CLUSTER_RESTORE_EXECUTING                             //80 // warning:forbidden adding state engine
)

const (
	CACHE_CLUSTER_MODIFYING_AZ               = 122
	CACHE_CLUSTER_MODIFYING_DEFAULT_ENTRANCE = 123
)

const (
	RESTORE_INITIAL = iota
	RESTORE_SUCCESS
	RESTORE_FAILED
	RESTORE_BACKUP_BACKUPING
	RESTORE_RECOVER_RECOVERING
	RESTORE_RECOVER_FAILED
	RESTORE_WAIT_EXECUTE
	RESTORE_RECOVER_EXECUTING
	RESTORE_RECOVER_EXECUTE_FAILED
	RESTORE_RECOVER_SUCCESS
	RESTORE_BACKUP_PEGA_ANALYSIS
)

const (
	BACKUP_CLUSTER_SUCCESS = iota
	BACKUP_CLUSTER_BACKUPING
	BACKUP_CLUSTER_FAILED
	BACKUP_CLUSTER_RECOVERING
	BACKUP_CLUSTER_RECOVER_FAILED
	BACKUP_CLUSTER_WAIT_RESTART
	BACKUP_CLUSTER_RESTARTING
	BACKUP_CLUSTER_RESTART_FAILED
	BACKUP_CLUSTER_RESTART_SUCCESS
	BACKUP_CLUSTER_WAIT_RECOVER
)

const (
	ANALYZE_BIGKEY = iota
)

const (
	BIGKEY_INITIAL = iota
	BIGKEY_BACKUP
	BIGKEY_READY
	BIGKEY_RUNNING
	BIGKEY_SUCCESS
	BIGKEY_FAILED
	PEGA_BIGKEY_RUNNING
)

const (
	ClusterBackupOrRecoverySuccess = iota
	ClusterBackupInProgress
	ClusterBackupFailed
	ClusterRecoveryInProgress
	ClusterRecoveryFailed
	ClusterWaitForRestart
	ClusterRestartInProgress
	ClusterRestartFailed
	ClusterRestartSuccess
	ClusterWaitForRecovery
)

const (
	ClusterRestoreSuccess = iota
	ClusterRestoreBackupInProgress
	ClusterRestoreRecoveryInProgress
	ClusterRestoreFailed
)

const (
	ModifyCheckFlagIgnoreCPU    = "ignore_cpu"
	ModifyCheckFlagIgnoreMemory = "ignore_mem"
	ModifyCheckFlagIgnoreDisk   = "ignore_disk"
)

type CacheCluster struct {
	Id                    int64            `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	UserId                int64            `gorm:"column:user_id;NOT NULL" json:"user_id"`
	ClusterName           string           `gorm:"column:cluster_name;NOT NULL" json:"cluster_name"`
	EngineType            int              `gorm:"column:engine_type;NOT NULL" json:"engine_type"`
	SecurityGroupId       string           `gorm:"column:security_group_id;NOT NULL" json:"security_group_id"`
	ElbId                 sql.NullString   `gorm:"column:elb_id" json:"elb_id"`
	Eip                   sql.NullString   `gorm:"column:eip" json:"eip"`
	ElbPnetip             sql.NullString   `gorm:"column:elb_pnetip" json:"elb_pnetip"`
	Status                int              `gorm:"column:status;NOT NULL" json:"status"`
	CreateTime            time.Time        `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_time"`
	PoolName              string           `gorm:"column:pool_name;NOT NULL" json:"pool_name"`
	SecurityGroupRulesId  string           `gorm:"column:security_group_rules_id;NOT NULL" json:"security_group_rules_id"`
	Port                  sql.NullInt32    `gorm:"column:port" json:"port"`
	Flavor                sql.NullInt32    `gorm:"column:flavor" json:"flavor"`
	Persistence           sql.NullInt32    `gorm:"column:persistence" json:"persistence"`
	Domain                string           `gorm:"column:domain;NOT NULL" json:"domain"`
	ClusterShowId         string           `gorm:"column:cluster_show_id;NOT NULL" json:"cluster_show_id"`
	TransactionId         string           `gorm:"column:transaction_id;NOT NULL" json:"transaction_id"`
	OrderId               string           `gorm:"column:order_id;NOT NULL" json:"order_id"`
	Version               int              `gorm:"column:version;default:0;NOT NULL" json:"version"`
	DestFlavor            int              `gorm:"column:dest_flavor;default:0;NOT NULL" json:"dest_flavor"`
	TagType               int              `gorm:"column:tag_type;default:0;NOT NULL" json:"tag_type"`
	InstanceNum           int              `gorm:"column:instance_num;default:0;NOT NULL" json:"instance_num"`
	AvailabilityZone      string           `gorm:"column:availability_zone;NOT NULL" json:"availability_zone"`             // availability zone id
	SubnetId              string           `gorm:"column:subnet_id;NOT NULL" json:"subnet_id"`                             // subnet id
	VpcId                 string           `gorm:"column:vpc_id;NOT NULL" json:"vpc_id"`                                   // vpc id
	ClusterType           string           `gorm:"column:cluster_type;default:default;NOT NULL" json:"cluster_type"`       // cluster type
	MasterDomain          string           `gorm:"column:master_domain;NOT NULL" json:"master_domain"`                     // master domain
	MasterPort            int              `gorm:"column:master_port;default:0;NOT NULL" json:"master_port"`               // master port
	MasterVpcId           string           `gorm:"column:master_vpc_id;NOT NULL" json:"master_vpc_id"`                     // master vpc id
	SecondSubnetId        string           `gorm:"column:second_subnet_id;NOT NULL" json:"second_subnet_id"`               // save the second subnet id
	BccCallbackFlag       string           `gorm:"column:bcc_callback_flag;NOT NULL" json:"bcc_callback_flag"`             // bcc callback flag
	OldAvailabilityZone   string           `gorm:"column:old_availability_zone;NOT NULL" json:"old_availability_zone"`     // availability zone before trans az
	ClusterTags           string           `gorm:"column:cluster_tags;NOT NULL" json:"cluster_tags"`                       // cluster tags
	ClusterTagType        string           `gorm:"column:cluster_tag_type;NOT NULL" json:"cluster_tag_type"`               // cluster_tag_type
	ClusterFlavorType     string           `gorm:"column:cluster_flavor_type;NOT NULL" json:"cluster_flavor_type"`         // cluster_flavor_type
	BackupConfig          string           `gorm:"column:backup_config;NOT NULL" json:"backup_config"`                     // backup_config
	BackupStatus          int              `gorm:"column:backup_status;default:0;NOT NULL" json:"backup_status"`           // backup_status
	LastBackupDay         int              `gorm:"column:last_backup_day;default:0;NOT NULL" json:"last_backup_day"`       // last_backup_day
	KernelVersion         string           `gorm:"column:kernel_version;NOT NULL" json:"kernel_version"`                   // redis&mc kernel version
	ConfVersion           int              `gorm:"column:conf_version;default:0;NOT NULL" json:"conf_version"`             // configue version
	WhitelistVersion      int              `gorm:"column:whitelist_version;default:0;NOT NULL" json:"whitelist_version"`   // whitelist version
	StoreType             int              `gorm:"column:store_type;default:0;NOT NULL" json:"store_type"`                 // store type
	ReplicationNum        int              `gorm:"column:replication_num;default:2;NOT NULL" json:"replication_num"`       // replication num
	ClientAuth            string           `gorm:"column:client_auth;NOT NULL" json:"client_auth"`                         // client auth
	MetaAuth              string           `gorm:"column:meta_auth;NOT NULL" json:"meta_auth"`                             // meta auth
	RedisAuth             string           `gorm:"column:redis_auth;NOT NULL" json:"redis_auth"`                           // redis auth
	ElbIpv6Id             string           `gorm:"column:elb_ipv6_id;NOT NULL" json:"elb_ipv6_id"`                         // elb ipv6 id
	ElbIpv6               string           `gorm:"column:elb_ipv6;NOT NULL" json:"elb_ipv6"`                               // elb ipv6 address
	DestProxyNum          int              `gorm:"column:dest_proxy_num;default:0;NOT NULL" json:"dest_proxy_num"`         // dest proxy num
	RecoverBatchId        string           `gorm:"column:recover_batch_id;NOT NULL" json:"recover_batch_id"`               // recover batch id
	DestInstanceNum       int              `gorm:"column:dest_instance_num;default:0;NOT NULL" json:"dest_instance_num"`   // dest instance num
	NodeType              string           `gorm:"column:node_type;NOT NULL" json:"node_type"`                             // node type
	DestNodeType          string           `gorm:"column:dest_node_type;NOT NULL" json:"dest_node_type"`                   // dest node type
	MetaserverId          string           `gorm:"column:metaserver_id;NOT NULL" json:"metaserver_id"`                     // metaserver id
	ShardSecurityGroupId  string           `gorm:"column:shard_security_group_id;NOT NULL" json:"shard_security_group_id"` // shard_security_group_id
	ExpectVersion         string           `gorm:"column:expect_version;NOT NULL" json:"expect_version"`                   // expect_version
	UpdateUuid            string           `gorm:"column:update_uuid;default:Success;NOT NULL" json:"update_uuid"`         // uuid
	EncryptFlag           int              `gorm:"column:encrypt_flag;default:0;NOT NULL" json:"encrypt_flag"`             // encrypt flag
	RecycleTime           time.Time        `gorm:"column:recycle_time;default:CURRENT_TIMESTAMP;NOT NULL" json:"recycle_time"`
	RecycleStatus         int              `gorm:"column:recycle_status;default:0;NOT NULL" json:"recycle_status"`
	CurImage              int              `gorm:"column:cur_image;default:0;NOT NULL" json:"cur_image"`
	ExpImage              int              `gorm:"column:exp_image;default:0;NOT NULL" json:"exp_image"`
	ProxyNum              int              `gorm:"column:proxy_num;NOT NULL" json:"proxy_num"`                                 // proxy num
	AzDeployInfo          string           `gorm:"column:az_deploy_info;NOT NULL" json:"az_deploy_info"`                       // az deploy info
	DestAzDeployInfo      string           `gorm:"column:dest_az_deploy_info;NOT NULL" json:"dest_az_deploy_info"`             // dest_az_deploy_info
	OpType                int              `gorm:"column:op_type;default:0;NOT NULL" json:"op_type"`                           // op type
	DestReplicationNum    int              `gorm:"column:dest_replication_num;default:0;NOT NULL" json:"dest_replication_num"` // dest_replication_num
	EnableReadOnly        int              `gorm:"column:enable_read_only;default:2;NOT NULL" json:"enable_read_only"`         // enable_read_only
	InstanceOrderId       string           `gorm:"column:instance_order_id;NOT NULL" json:"instance_order_id"`                 // instance order id
	MigrationStatus       int              `gorm:"column:migration_status;default:0;NOT NULL" json:"migration_status"`         // migration_status
	AliasName             string           `gorm:"column:alias_name;NOT NULL" json:"alias_name"`                               // alias_name
	AcluserVersion        int              `gorm:"column:acluser_version;default:0;NOT NULL" json:"acluser_version"`           // acl user
	Timeout               int              `gorm:"column:timeout;NOT NULL" json:"timeout"`                                     // timeout
	BlbListenerPort       sql.NullInt32    `gorm:"column:blb_listener_port;default:0" json:"blb_listener_port"`                // blb_listener_port
	PublicDomain          sql.NullString   `gorm:"column:public_domain" json:"public_domain"`                                  // public_domain
	IsolateStatus         int              `gorm:"column:isolate_status;default:0;NOT NULL" json:"isolate_status"`
	EventState            string           `gorm:"column:event_state;NOT NULL" json:"event_state"` // event_state
	TimeWindow            sql.NullString   `gorm:"column:time_window;default:0,1,2,3,4,5,6;03:00;1" json:"time_window"`
	EnvType               string           `gorm:"column:env_type;default:bcc;NOT NULL" json:"env_type"`                               // env_type
	GroupId               string           `gorm:"column:group_id;NOT NULL" json:"group_id"`                                           // group id
	GroupRole             int              `gorm:"column:group_role;default:0;NOT NULL" json:"group_role"`                             // group role 0:not beloing to group, 1:master 2:slave
	DeployIdList          string           `gorm:"column:deploy_id_list;NOT NULL" json:"deploy_id_list"`                               // deploy id list
	EnableAccessLog       int              `gorm:"column:enable_access_log;default:0;NOT NULL" json:"enable_access_log"`               // enable access log
	CloneDataClusterId    string           `gorm:"column:clone_data_cluster_id;NOT NULL" json:"clone_data_cluster_id"`                 // src cluster show id
	CloneDataBackupId     string           `gorm:"column:clone_data_backup_id;NOT NULL" json:"clone_data_backup_id"`                   // backup batchid
	CloneDataMoment       string           `gorm:"column:clone_data_moment;NOT NULL" json:"clone_data_moment"`                         // recover start time
	CloneStatus           int              `gorm:"column:clone_status;default:0;NOT NULL" json:"clone_status"`                         // clone status
	EnableRestore         int              `gorm:"column:enable_restore;default:0;NOT NULL" json:"enable_restore"`                     // enable restore
	RestoreTime           string           `gorm:"column:restore_time;NOT NULL" json:"restore_time"`                                   // restore start time
	RestoreStatus         int              `gorm:"column:restore_status;default:0;NOT NULL" json:"restore_status"`                     // restore_status
	LastRestoreTime       string           `gorm:"column:last_restore_time;NOT NULL" json:"last_restore_time"`                         // last restore backup time
	RestoreRecoverTime    string           `gorm:"column:restore_recover_time;NOT NULL" json:"restore_recover_time"`                   // restore recover time
	RestoreBatchId        string           `gorm:"column:restore_batch_id;NOT NULL" json:"restore_batch_id"`                           // recover batch id
	CurImageRef           string           `gorm:"column:cur_image_ref;NOT NULL" json:"cur_image_ref"`                                 // current image ref
	BgwGroupId            string           `gorm:"column:bgw_group_id;NOT NULL" json:"bgw_group_id"`                                   // bgw group id
	BgwGroupExclusive     int              `gorm:"column:bgw_group_exclusive;default:0;NOT NULL" json:"bgw_group_exclusive"`           // bgw_group_exclusive
	RestoreType           int              `gorm:"column:restore_type;default:0;NOT NULL" json:"restore_type"`                         // restore type
	NeedUpdateRestoreTime int              `gorm:"column:need_update_restore_time;default:0;NOT NULL" json:"need_update_restore_time"` // update restore_time or not
	DiskFlavor            int              `gorm:"column:disk_flavor;default:0;NOT NULL" json:"disk_flavor"`                           // disk flavor
	DestDiskFlavor        int              `gorm:"column:dest_disk_flavor;default:0;NOT NULL" json:"dest_disk_flavor"`                 // dest disk flavor
	UserInfo              *Userinfo        `gorm:"foreignKey:UserId;references:Id"`
	CacheInscances        []*CacheInstance `gorm:"foreignKey:ClusterId;references:Id"`
	ReadonlyGroups        []*ReadonlyGroup `gorm:"foreignKey:ClusterID;references:Id"`
	Resource_type         string           `gorm:"column:resource_type;NOT NULL" json:"resource_type"` //container or bcc
	EndpointId            string           `gorm:"column:endpoint_id;NOT NULL" json:"endpoint_id"`
	EndpointIp            string           `gorm:"column:endpoint_ip;NOT NULL" json:"endpoint_ip"`
	ModifyChkFlag         string           `gorm:"column:modify_chk_flag;NOT NULL" json:"modify_chk_flag"`
	Entrance              string           `gorm:"column:entrance;NOT NULL" json:"entrance"`
	NotEnableReplace      int              `gorm:"column:not_enable_replace;default:0;NOT NULL" json:"not_enable_replace"`
	UseXmaster            int              `gorm:"column:use_xmaster;default:0;NOT NULL" json:"use_xmaster"`
	AutoModifyStrategy    string           `gorm:"column:auto_modify_strategy" json:"auto_modify_strategy"` // auto modify strategy
	UseNewBackup          int              `gorm:"column:use_new_backup;default:0;NOT NULL" json:"use_new_backup"`
	UseNewAgent           string           `gorm:"column:use_new_agent;NOT NULL" json:"use_new_agent"`
	UseEncryptBackup      string           `gorm:"column:use_encrypt_backup;NOT NULL" json:"use_encrypt_backup"`

	// 补全缺少的字段
	EnableSlowLog      int    `gorm:"column:enable_slow_log;default:0;NOT NULL" json:"enable_slow_log"`
	EnableHotkey       int    `gorm:"column:enable_hotkey;default:0;NOT NULL" json:"enable_hotkey"`
	EnableSwitchDomain int    `gorm:"column:enable_switch_domain;default:0;NOT NULL" json:"enable_switch_domain"`
	RecordMaxNodeId    int    `gorm:"column:record_max_node_id;default:0;NOT NULL" json:"record_max_node_id"`
	CbUsernameAdmin    string `gorm:"column:cb_username_admin;NOT NULL" json:"cb_username_admin"`
	CbPasswdAdmin      string `gorm:"column:cb_passwd_admin;NOT NULL" json:"cb_passwd_admin"`
	CbUsernameRo       string `gorm:"column:cb_username_ro;NOT NULL" json:"cb_username_ro"`
	CbPasswdRo         string `gorm:"column:cb_passwd_ro;NOT NULL" json:"cb_passwd_ro"`
	CbMemoryQuota      int    `gorm:"column:cb_memory_quota;default:0;NOT NULL" json:"cb_memory_quota"`
	CbServices         string `gorm:"column:cb_services;NOT NULL" json:"cb_services"`
	RoGroupType        int    `gorm:"column:ro_group_type;default:0;NOT NULL" json:"ro_group_type"`
	PushedFlag         int    `gorm:"column:pushed_flag;default:0;NOT NULL" json:"pushed_flag"`
	DiskType           string `gorm:"column:disk_type;NOT NULL" json:"disk_type"`
	MetaserverAddress  string `gorm:"column:metaserver_address;NOT NULL" json:"metaserver_address"`
	BnsService         string `gorm:"column:bns_service;NOT NULL" json:"bns_service"`
	BnsGroup           string `gorm:"column:bns_group;NOT NULL" json:"bns_group"`
	BnsNodeName        string `gorm:"column:bns_node_name;NOT NULL" json:"bns_node_name"`
	HitX1              int    `gorm:"column:hit_x1;default:0;NOT NULL" json:"hit_x1"`
	SyncGroupId        string `gorm:"column:sync_group_id;NOT NULL" json:"sync_group_id"`
	SyncGroupName      string `gorm:"column:sync_group_name;NOT NULL" json:"sync_group_name"`
	CanUseHotkey       int    `gorm:"column:can_use_hotkey;default:1;NOT NULL" json:"can_use_hotkey"`

	LogicalRegion string `gorm:"column:logical_region;NOT NULL" json:"logical_region"`
}

func (CacheCluster) TableName() string {
	return "cache_cluster"
}
