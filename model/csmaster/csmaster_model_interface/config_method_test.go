package csmaster_model_interface

import (
	"context"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type testIsValueLegallyData struct {
	UserConfList    *UserConfList
	TestConfName    string
	TestConfVal     string
	CheckUnVisiable bool
	CanPass         bool
}

var testUserConfListFake1 = &UserConfList{
	ConfName:        "fake",
	ConfRange:       "6",
	ConfType:        2,
	ConfUserVisible: 0,
}
var testUserConfListFake2 = &UserConfList{
	ConfName:        "fake",
	ConfRange:       "test",
	ConfType:        6,
	ConfUserVisible: 1,
}

var testUserConfListSupportMultiActive = &UserConfList{
	ConfName:        "support_multi_active",
	ConfRange:       "yes|no",
	ConfType:        1,
	ConfUserVisible: 1,
}
var testUserConfListAppendonly = &UserConfList{
	ConfName:        "appendonly",
	ConfRange:       "yes|no|partial",
	ConfType:        1,
	ConfUserVisible: 0,
}
var testUserConfListSlowLog = &UserConfList{
	ConfName:        "slowlog-log-slower-than",
	ConfRange:       "10000-10000000",
	ConfType:        2,
	ConfUserVisible: 0,
}
var testUserConfListDisableCommands = &UserConfList{
	ConfName:        "disable_commands",
	ConfRange:       "flushall|flushdb|keys|hgetall|scan",
	ConfType:        3,
	ConfUserVisible: 0,
}
var testUserConfNotifyKeyspaceEvents = &UserConfList{
	ConfName:        "notify-keyspace-events",
	ConfRange:       "K|E|g|$|l|s|h|z|x|e|A",
	ConfType:        4,
	ConfUserVisible: 0,
}

var testIsValueLegallyDatas = []*testIsValueLegallyData{
	{
		UserConfList:    testUserConfListFake1,
		TestConfName:    "fake",
		TestConfVal:     "hello",
		CheckUnVisiable: true,
		CanPass:         false,
	},

	{
		UserConfList:    testUserConfListFake2,
		TestConfName:    "fake",
		TestConfVal:     "hello",
		CheckUnVisiable: true,
		CanPass:         false,
	},

	// 1不可见配置检查
	// 【无法通过】非法的不可见参数 + 需要检查不可见
	{
		UserConfList:    testUserConfListSupportMultiActive,
		TestConfName:    "support_multi_active",
		TestConfVal:     "hello",
		CheckUnVisiable: true,
		CanPass:         false,
	},
	// 【通过】非法的不可见参数 + 不需要检查不可见
	{
		UserConfList:    testUserConfListSupportMultiActive,
		TestConfName:    "support_multi_active",
		TestConfVal:     "hello",
		CheckUnVisiable: false,
		CanPass:         true,
	},
	// 2 单选
	//
	{
		UserConfList:    testUserConfListAppendonly,
		TestConfName:    "appendonly1",
		TestConfVal:     "yes",
		CheckUnVisiable: false,
		CanPass:         false,
	},
	{
		UserConfList:    testUserConfListAppendonly,
		TestConfName:    "appendonly",
		TestConfVal:     "yes",
		CheckUnVisiable: false,
		CanPass:         true,
	},
	{
		UserConfList:    testUserConfListAppendonly,
		TestConfName:    "appendonly",
		TestConfVal:     "hello",
		CheckUnVisiable: false,
		CanPass:         false,
	},

	// 符合规定的数字类型
	{
		UserConfList:    testUserConfListSlowLog,
		TestConfName:    "slowlog-log-slower-than",
		TestConfVal:     "10000",
		CheckUnVisiable: false,
		CanPass:         true,
	},
	// 非法数字
	{
		UserConfList:    testUserConfListSlowLog,
		TestConfName:    "slowlog-log-slower-than",
		TestConfVal:     "test",
		CheckUnVisiable: false,
		CanPass:         false,
	},
	// 超过量程
	{
		UserConfList:    testUserConfListSlowLog,
		TestConfName:    "slowlog-log-slower-than",
		TestConfVal:     "9",
		CheckUnVisiable: false,
		CanPass:         false,
	},
	// 多选
	{
		UserConfList:    testUserConfListDisableCommands,
		TestConfName:    "disable_commands",
		TestConfVal:     "get",
		CheckUnVisiable: false,
		CanPass:         false,
	},
	{
		UserConfList:    testUserConfListDisableCommands,
		TestConfName:    "disable_commands",
		TestConfVal:     "flushall",
		CheckUnVisiable: false,
		CanPass:         true,
	},
	{
		UserConfList:    testUserConfListDisableCommands,
		TestConfName:    "disable_commands",
		TestConfVal:     "get,flushall",
		CheckUnVisiable: false,
		CanPass:         false,
	},
	{
		UserConfList:    testUserConfListDisableCommands,
		TestConfName:    "disable_commands",
		TestConfVal:     "",
		CheckUnVisiable: false,
		CanPass:         true,
	},
	{
		UserConfList:    testUserConfListDisableCommands,
		TestConfName:    "disable_commands",
		TestConfVal:     "keys,flushall",
		CheckUnVisiable: false,
		CanPass:         true,
	},
	// 特殊多选
	{
		UserConfList:    testUserConfNotifyKeyspaceEvents,
		TestConfName:    "notify-keyspace-events",
		TestConfVal:     "get",
		CheckUnVisiable: false,
		CanPass:         false,
	},
	{
		UserConfList:    testUserConfNotifyKeyspaceEvents,
		TestConfName:    "notify-keyspace-events",
		TestConfVal:     "K",
		CheckUnVisiable: false,
		CanPass:         true,
	},
	{
		UserConfList:    testUserConfNotifyKeyspaceEvents,
		TestConfName:    "notify-keyspace-events",
		TestConfVal:     "",
		CheckUnVisiable: false,
		CanPass:         true,
	},
	{
		UserConfList:    testUserConfNotifyKeyspaceEvents,
		TestConfName:    "notify-keyspace-events",
		TestConfVal:     "K0",
		CheckUnVisiable: false,
		CanPass:         false,
	},
	{
		UserConfList:    testUserConfNotifyKeyspaceEvents,
		TestConfName:    "notify-keyspace-events",
		TestConfVal:     "KEg",
		CheckUnVisiable: false,
		CanPass:         true,
	},
}

func TestIsValueLegally(t *testing.T) {
	ctx := context.Background()
	for _, testCase := range testIsValueLegallyDatas {
		err := testCase.UserConfList.IsValueLegally(ctx, testCase.TestConfName, testCase.TestConfVal, testCase.CheckUnVisiable)
		if testCase.CanPass && (err != nil) {
			t.Fatalf("case:%s,expected to pass but failed:%v", base_utils.Format(testCase), err)
		}
		if !testCase.CanPass && (err == nil) {
			t.Fatal("expected to fail but passed")
		}
	}
}
