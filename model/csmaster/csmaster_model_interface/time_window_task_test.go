package csmaster_model_interface

import (
	"testing"
	"time"
)

func TestTimeWindowTask_TableName(t *testing.T) {
	type fields struct {
		ID            int
		ClusterID     int
		TaskStatus    string
		CreateTime    time.Time
		TaskType      string
		TaskParams    string
		TransactionID string
		ExecuteTime   time.Time
		TaskFrom      string
		TaskID        string
		TaskDetail    string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "time_window_task",
			fields: fields{
				ID:        1,
				ClusterID: 1,
			},
			want: "time_window_task",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cl := TimeWindowTask{
				ID:        tt.fields.ID,
				ClusterID: tt.fields.ClusterID,
			}
			if got := cl.TableName(); got != tt.want {
				t.<PERSON>rf("TableName() = %v, want %v", got, tt.want)
			}
		})
	}
}
