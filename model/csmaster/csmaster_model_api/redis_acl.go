package csmaster_model_api

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func (c CsmasterModelOpInstance) GetRedisAclsByAppShortID(ctx context.Context, appShortID int) ([]*csmaster_model_interface.ClusterAclUser, error) {
	var redisAcls []*csmaster_model_interface.ClusterAclUser
	err := c.resource.GetAllByCond(ctx, &redisAcls, "cluster_id = ?", appShortID)
	if err != nil {
		return nil, err
	}
	return redisAcls, nil
}
