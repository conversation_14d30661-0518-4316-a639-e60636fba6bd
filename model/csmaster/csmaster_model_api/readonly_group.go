/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/06 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file readonly_group.go
 * <AUTHOR>
 * @date 2022/05/06 11:30:29
 * @brief reaonly group api
 *
 **/

package csmaster_model_api

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

// GetReadonlyGroupByID return readonly group filter by id
func (c CsmasterModelOpInstance) GetReadonlyGroupByID(ctx context.Context, roGroupID int64) (*csmaster_model_interface.ReadonlyGroup, error) {
	var readonlyGroup csmaster_model_interface.ReadonlyGroup
	err := c.resource.GetOneByCond(ctx, &readonlyGroup, "id=?", roGroupID)
	if err != nil {
		return nil, errors.Errorf("get readonly group record fail, error:%s", err)
	}
	return &readonlyGroup, nil
}

// GetReadonlyGroupByShowID return template filter by showid
func (c CsmasterModelOpInstance) GetReadonlyGroupByShowID(ctx context.Context, roGroupShowID string) (*csmaster_model_interface.ReadonlyGroup, error) {
	var readonlyGroup csmaster_model_interface.ReadonlyGroup
	err := c.resource.GetOneByCond(ctx, &readonlyGroup, "ro_group_show_id=?", roGroupShowID)
	if err != nil {
		return nil, errors.Errorf("get readonly group record fail, error:%s", err)
	}
	return &readonlyGroup, nil
}

// UpdateReadonlyGroupModel update readonly group model
func (c CsmasterModelOpInstance) UpdateReadonlyGroupModel(ctx context.Context, params *csmaster_model_interface.ReadonlyGroup) error {
	return c.resource.FullSaveAssociationsSave(ctx, params)
}

// GetReadonlyGroupByName update readonly group model
func (c CsmasterModelOpInstance) GetReadonlyGroupByName(ctx context.Context, roGroupName string, userID int64) (*csmaster_model_interface.ReadonlyGroup, error) {
	var readonlyGroup csmaster_model_interface.ReadonlyGroup
	err := c.resource.GetOneByCond(ctx, &readonlyGroup, "ro_group_name=? and user_id=?", roGroupName, userID)
	if err != nil {
		return nil, errors.Errorf("get readonly group record fail, error:%s", err)
	}
	return &readonlyGroup, nil
}

// CreateReadonlyGroupModel create one readonly group record
func (c CsmasterModelOpInstance) CreateReadonlyGroupModel(ctx context.Context, params *csmaster_model_interface.ReadonlyGroup) error {
	err := c.resource.CreateOne(ctx, params)
	if err != nil {
		return err
	}
	return nil
}

// GetReadonlyGroupsByClusterID return template filter by cluster_show_id
func (c CsmasterModelOpInstance) GetReadonlyGroupsByClusterID(ctx context.Context, clusterShowID string) ([]*csmaster_model_interface.ReadonlyGroup, error) {
	var readonlyGroups []*csmaster_model_interface.ReadonlyGroup
	cluster, err := c.GetClusterModelByAppId(ctx, clusterShowID)
	if err != nil {
		return nil, err
	}
	err = c.resource.GetAllByCond(ctx, &readonlyGroups, "cluster_id=?", cluster.Id)
	if err != nil {
		return nil, errors.Errorf("get readonly group records fail, error:%s", err)
	}
	return readonlyGroups, nil
}
