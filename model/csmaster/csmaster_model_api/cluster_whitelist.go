package csmaster_model_api

import (
	"context"

	"github.com/pkg/errors"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func (c CsmasterModelOpInstance) GetClusterWhiteListByClusterId(ctx context.Context, clusterId int64) ([]*csmaster_model_interface.ClusterWhiteList, error) {
	var whitelists []*csmaster_model_interface.ClusterWhiteList
	err := c.resource.GetAllByCond(ctx, &whitelists, "cluster_id = ?", clusterId)
	if err != nil {
		return nil, errors.Errorf("get cluster whitelist by cluster_id fail,clusterid:%d, error:%s", clusterId, err.Error())
	}
	return whitelists, nil
}

func (c CsmasterModelOpInstance) RemoveClusterWhiteList(ctx context.Context, clusterId int64, floatIp string) error {
	err := c.resource.DeleteAllByCondV2(ctx, &csmaster_model_interface.ClusterWhiteList{}, "cluster_id = ? AND floating_ip = ?", clusterId, floatIp)
	if err != nil {
		return err
	}
	return nil
}

func (c CsmasterModelOpInstance) UpdateClusterWhiteList(ctx context.Context, data []*csmaster_model_interface.ClusterWhiteList) error {
	return c.resource.FullSaveAssociationsSave(ctx, data)
}

func (c CsmasterModelOpInstance) GetClusterWhiteListByClusterIdAndGroupName(ctx context.Context, clusterId int64,
	groupName string) ([]*csmaster_model_interface.ClusterWhiteList, error) {
	var whitelists []*csmaster_model_interface.ClusterWhiteList
	err := c.resource.GetAllByCond(ctx, &whitelists, "cluster_id = ? AND group_name = ?", clusterId, groupName)
	if err != nil {
		return nil, errors.Errorf("get cluster whitelist group fail,clusterid:%d, group_name:%s, error:%s", clusterId, groupName, err.Error())
	}
	return whitelists, nil
}

func (c CsmasterModelOpInstance) RemoveClusterWhiteListGroup(ctx context.Context, clusterId int64, groupName string) error {
	if err := c.resource.DeleteAllByCondV2(ctx, &csmaster_model_interface.ClusterWhiteList{}, "cluster_id = ? AND group_name = ?", clusterId, groupName); err != nil {
		return errors.Errorf("delete cluster whitelist group fail, cluster_id:%d, group_name:%s, error:%s", clusterId, groupName, err.Error())
	}
	return nil
}

// 启用事务，先删除再插入
func (c CsmasterModelOpInstance) ModifyClusterWhiteListGroup(ctx context.Context, clusterId int64, oldGroupName string,
	newGroupName string, data []*csmaster_model_interface.ClusterWhiteList) error {
	db := c.resource.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: c.resource.GormLoggerCommon})
	return db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Where("cluster_id = ? AND group_name = ?", clusterId, oldGroupName).Delete(&csmaster_model_interface.ClusterWhiteList{}).Error; err != nil {
			return errors.Errorf("delete cluster whitelist group fail, cluster_id:%d, old_group_name:%s, error:%s", clusterId, oldGroupName, err.Error())
		}
		if err := tx.Save(data).Error; err != nil {
			return errors.Errorf("insert cluster whitelist group fail, cluster_id:%d, new_group_name:%s, error:%s", clusterId, newGroupName, err.Error())
		}
		return nil
	})
}
