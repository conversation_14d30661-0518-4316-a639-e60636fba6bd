/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/19 zeng<PERSON><PERSON><PERSON>@baidu.com Exp
 *
 **************************************************************************/

/**
 * @file instance.go
 * <AUTHOR>
 * @date 2022/05/19 14:55:14
 * @brief instance api
 *
 **/

package csmaster_model_api

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

// GetInstanceOrderByID return InstanceOrder filter by order_id
func (c CsmasterModelOpInstance) GetInstanceOrderByID(ctx context.Context, orderID string) (*csmaster_model_interface.InstanceOrder, error) {
	var order csmaster_model_interface.InstanceOrder
	err := c.resource.GetOneByCond(ctx, &order, "order_id=?", orderID)
	if err != nil {
		return nil, errors.Errorf("get instance order fail, error:%s", err)
	}
	return &order, nil
}

// UpdateInstanceOrderModel modify InstanceOrder
func (c CsmasterModelOpInstance) UpdateInstanceOrderModel(ctx context.Context, params *csmaster_model_interface.InstanceOrder) error {
	return c.resource.FullSaveAssociationsSave(ctx, params)
}

// CreateInstanceOrderModel create InstanceOrder model
func (c CsmasterModelOpInstance) CreateInstanceOrderModel(ctx context.Context, params *csmaster_model_interface.InstanceOrder) error {
	err := c.resource.CreateOne(ctx, params)
	if err != nil {
		return err
	}
	return nil
}

// GetInstanceOrderByParentID return InstanceOrder array filter by parent_order_id
func (c CsmasterModelOpInstance) GetInstanceOrderByParentID(ctx context.Context, ParentID string) ([]*csmaster_model_interface.InstanceOrder, error) {
	var orders []*csmaster_model_interface.InstanceOrder
	err := c.resource.GetAllByCond(ctx, &orders, "parent_id=?", ParentID)
	if err != nil {
		return nil, errors.Errorf("get instance order by parent_id fail, error:%s", err)
	}
	return orders, nil
}
