package csmaster_model_api

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func (c CsmasterModelOpInstance) GetBaeClusterWhiteListByClusterId(ctx context.Context,
	clusterId int64) ([]*csmaster_model_interface.BaeClusterWhiteList, error) {

	var baeWhitelists []*csmaster_model_interface.BaeClusterWhiteList
	err := c.resource.GetAllByCond(ctx, &baeWhitelists, "cluster_id = ?", clusterId)
	if err != nil {
		return nil, errors.Errorf("get bae cluster whitelist by cluster_id fail, clusterid:%d, error:%s", clusterId, err.Error())
	}
	return baeWhitelists, nil
}
