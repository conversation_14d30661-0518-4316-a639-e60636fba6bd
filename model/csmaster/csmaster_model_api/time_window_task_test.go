package csmaster_model_api

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func TestTimeWindowTaskGetAllByCond(t *testing.T) {

	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").
		WillReturnRows(sqlmock.NewRows([]string{"id", "cluster_id", "task_status", "create_time", "task_type",
			"task_params", "transaction_id", "execute_time", "task_from"}).
			AddRow(1, 1, "task_status", time.Now(), "task_type", "task_params", "transaction_id",
				time.Now(), "task_from"))
	mock.ExpectCommit()
	tmp := testResource.csmasterModel.ModelGorm
	testResource.csmasterModel.ModelGorm = gormDB
	defer func() {
		testResource.csmasterModel.ModelGorm = tmp
	}()
	ret, err := testResource.CsmasterOpAgent.TimeWindowTaskGetAllByCond(context.Background(), "cluster_id = ?", 1)
	if err != nil {
		t.Fatalf("error TimeWindowTaskGetAllByCond: %s", err)
	}
	if len(ret) != 1 {
		t.Fatalf("error TimeWindowTaskGetAllByCond: %s", err)
	}
	ret, err = testResource.CsmasterOpAgent.TimeWindowTaskGetAllByCond(context.Background(), "cluster_id = ?", 1, 1)
	if err == nil {
		t.Fatalf("error TimeWindowTaskGetAllByCond: %s", err)
	}
}

func TestTimeWindowTaskSave(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `time_window_task`").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	tmp := testResource.csmasterModel.ModelGorm
	testResource.csmasterModel.ModelGorm = gormDB
	defer func() {
		testResource.csmasterModel.ModelGorm = tmp
	}()
	data := []*csmaster_model_interface.TimeWindowTask{
		{
			ID:            1,
			ClusterID:     1,
			TaskStatus:    "",
			CreateTime:    time.Now(),
			TaskType:      "",
			TaskParams:    "",
			TransactionID: "",
			ExecuteTime:   time.Now(),
			TaskFrom:      "",
			TaskID:        "",
			TaskDetail:    "",
		},
	}
	if err := testResource.CsmasterOpAgent.TimeWindowTaskSave(context.Background(), data); err != nil {
		t.Fatalf("error TimeWindowTaskSave: %s", err)
	}
	data = []*csmaster_model_interface.TimeWindowTask{
		{
			ID:            2,
			ClusterID:     2,
			TaskStatus:    "",
			CreateTime:    time.Time{},
			TaskType:      "",
			TaskParams:    "",
			TransactionID: "",
			ExecuteTime:   time.Time{},
			TaskFrom:      "",
			TaskID:        "",
			TaskDetail:    "",
		},
	}
	if err := testResource.CsmasterOpAgent.TimeWindowTaskSave(context.Background(), data); err == nil {
		t.Fatalf("error TimeWindowTaskSave: %s", err)
	}
}

//func TestTimeWindowTaskGetAllByCondOffline(t *testing.T) {
//
//	table, err := testResource.CsmasterOpAgent.TimeWindowTaskGetAllByCond(context.Background(), "cluster_id = ?", 1)
//	if err != nil {
//		fmt.Println(err)
//		return
//	}
//	for _, row := range table {
//		fmt.Printf("%+v\n", row)
//	}
//	t.Log(table)
//}
//func TestTimeWindowTaskSaveOffline(t *testing.T) {
//
//	data := []*csmaster_model_interface.TimeWindowTask{
//		{0, 1, "TS", time.Time{}, "TT", "", "T", time.Time{}, "F"},
//	}
//	if err := testResource.CsmasterOpAgent.TimeWindowTaskSave(context.Background(), data); err != nil {
//		t.Fatalf("error TimeWindowTaskSave: %s", err)
//	}
//}
