package csmaster_model_api

import (
	"context"
	"strings"
)

type ExpRule struct {
	Name     string `gorm:"column:name" json:"name"`
	Features string `gorm:"column:features" json:"features"`
	Rule     string `gorm:"column:rule" json:"rule"`
	Flag     string `gorm:"column:flag" json:"flag"`
	Priority int    `gorm:"column:priority" json:"priority"`
}

type Rule map[string]*RuleElement

type RuleElement struct {
	Value     any    `json:"value"`
	MatchType string `json:"type"`
}

func (c CsmasterModelOpInstance) GetExpRules(ctx context.Context, name string) ([]*ExpRule, error) {
	expRules := make([]*ExpRule, 0)
	rawSql := `select name, features, coalesce(rule, '-') as rule, coalesce(flag, default_flag) as flag, coalesce(priority, -1) as priority
		from exp_strategy left join exp_rule
			on exp_strategy.id = exp_rule.exp_id
		where name = ?
		order by name, priority desc;`
	if err := c.resource.DB(ctx).Raw(rawSql, name).Scan(&expRules).Error; err != nil {
		return nil, err
	}
	return expRules, nil
}

func (c CsmasterModelOpInstance) GetFlag(ctx context.Context, name string, features map[string]string, df string) (string, error) {
	expRules, err := c.GetExpRules(ctx, name)
	if err != nil {
		return "", err
	}
	for _, expRule := range expRules {
		if matchRule(features, parseRule(expRule.Features, expRule.Rule)) {
			return expRule.Flag, nil
		}
	}
	return df, nil
}

func parseRule(feature string, rule string) Rule {
	ret := make(Rule)
	var featureList []string
	for _, f := range strings.Split(feature, ";") {
		featureList = append(featureList, strings.Split(f, ".")[1])
	}
	if rule == "-" {
		for _, f := range featureList {
			ret[f] = &RuleElement{Value: "*", MatchType: "all"}
		}
		return ret
	}
	rawRules := strings.Split(rule, ";")
	for i, f := range featureList {
		if i >= len(rawRules) {
			ret[f] = &RuleElement{Value: "*", MatchType: "all"}
			continue
		}
		rawRule := rawRules[i]
		if rawRule == "*" {
			ret[f] = &RuleElement{Value: "*", MatchType: "all"}
			continue
		}
		if strings.HasPrefix(rawRule, "*") {
			ret[f] = &RuleElement{Value: strings.TrimPrefix(rawRule, "*"), MatchType: "suffix"}
			continue
		}
		if strings.HasSuffix(rawRule, "*") {
			ret[f] = &RuleElement{Value: strings.TrimSuffix(rawRule, "*"), MatchType: "prefix"}
			continue
		}
		if strings.HasPrefix(rawRule, ">=") {
			ret[f] = &RuleElement{Value: strings.TrimPrefix(rawRule, ">="), MatchType: "ge"}
			continue
		}
		if strings.HasPrefix(rawRule, "<=") {
			ret[f] = &RuleElement{Value: strings.TrimPrefix(rawRule, "<="), MatchType: "le"}
			continue
		}
		if strings.HasPrefix(rawRule, ">") {
			ret[f] = &RuleElement{Value: strings.TrimPrefix(rawRule, ">"), MatchType: "gt"}
			continue
		}
		if strings.HasPrefix(rawRule, "<") {
			ret[f] = &RuleElement{Value: strings.TrimPrefix(rawRule, "<"), MatchType: "lt"}
			continue
		}
		if strings.Contains(rawRule, "|") {
			ret[f] = &RuleElement{Value: strings.Split(rawRule, "|"), MatchType: "in"}
			continue
		}
		ret[f] = &RuleElement{Value: rawRule, MatchType: "eq"}
	}
	return ret
}

func matchRule(features map[string]string, rule Rule) bool {
	for k, v := range rule {
		if v.MatchType == "all" {
			continue
		}
		if v.MatchType == "suffix" {
			if !strings.HasSuffix(features[k], v.Value.(string)) {
				return false
			}
		}
		if v.MatchType == "prefix" {
			if !strings.HasPrefix(features[k], v.Value.(string)) {
				return false
			}
		}
		if v.MatchType == "gt" {
			if features[k] <= v.Value.(string) {
				return false
			}
		}
		if v.MatchType == "lt" {
			if features[k] >= v.Value.(string) {
				return false
			}
		}
		if v.MatchType == "ge" {
			if features[k] < v.Value.(string) {
				return false
			}
		}
		if v.MatchType == "le" {
			if features[k] > v.Value.(string) {
				return false
			}
		}
		if v.MatchType == "in" {
			if !inSlice(features[k], v.Value.([]string)) {
				return false
			}
		}
		if v.MatchType == "eq" {
			if features[k] != v.Value.(string) {
				return false
			}
		}
	}
	return true
}

func inSlice(s string, ss []string) bool {
	for _, v := range ss {
		if s == v {
			return true
		}
	}
	return false
}
