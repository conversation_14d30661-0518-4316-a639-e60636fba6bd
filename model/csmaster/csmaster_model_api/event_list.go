package csmaster_model_api

import (
	"context"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func (c CsmasterModelOpInstance) GetEventListByClusterID(ctx context.Context, clusterShowId string) ([]*csmaster_model_interface.EventList, error) {
	var cacheCluster *csmaster_model_interface.CacheCluster
	if err := c.resource.GetOneByCond(ctx, &cacheCluster, "cluster_show_id = ?", clusterShowId); err != nil {
		return nil, err
	}
	clusterId := cacheCluster.Id

	var eventLists []*csmaster_model_interface.EventList
	if err := c.resource.GetAllByCond(ctx, &eventLists, "cluster_id = ?", clusterId); err != nil {
		return nil, err
	}
	return eventLists, nil
}

func (c CsmasterModelOpInstance) AddEventStart(ctx context.Context, clusterShowId string, eventState string) error {

	var cacheCluster *csmaster_model_interface.CacheCluster
	if err := c.resource.GetOneByCond(ctx, &cacheCluster, "cluster_show_id = ?", clusterShowId); err != nil {
		return err
	}
	clusterId := cacheCluster.Id

	timeS := time.Now().Format("2006-01-02 15:04:05")
	eventDesc := eventState + "|start|" + timeS

	return c.resource.CreateOne(ctx, &csmaster_model_interface.EventList{
		ClusterId:  clusterId,
		InstanceId: 0,
		Level:      1,
		Type:       1,
		Time:       time.Now(),
		EventDesc:  eventDesc,
	})
}

// +-------+-----------------+---------------------+--------+---------------------------------------+
// | id    | cluster_name    | cluster_show_id     | status | event_state                           |
// +-------+-----------------+---------------------+--------+---------------------------------------+
// | 25546 | bj_wyiolvxggund | scs-bj-seisomqtaafl |      8 |                                       |
// | 25545 | bj_hwnsvovvbfsu | scs-bj-dlhimqoavmim |     10 | modifying_cluster|2022-09-30 15:01:35 |
// | 25544 | bj_zykfmhgygrwr | scs-bj-dpvftjoqhdhg |     10 | modifying_cluster|2022-09-29 16:47:23 |
// | 25543 | bj_tslfqjdkdicb | scs-bj-dtmiwnxebnqp |     10 | modifying_cluster|2022-09-29 15:43:30 |
// | 25542 | bj_hnbgvtxwudtp | scs-bj-tiwjdmlksori |      8 |                                       |
// +-------+-----------------+---------------------+--------+---------------------------------------+
func (c CsmasterModelOpInstance) AddEventEnd(ctx context.Context, clusterShowId string, eventType string, eventResult string) error {

	var cacheCluster *csmaster_model_interface.CacheCluster
	if err := c.resource.GetOneByCond(ctx, &cacheCluster, "cluster_show_id = ?", clusterShowId); err != nil {
		return err
	}
	clusterId := cacheCluster.Id

	// 获取开始事件记录，可以通过event_state进行过滤
	var eventList []*csmaster_model_interface.EventList
	if err := c.resource.GetAllByCond(ctx, &eventList, "cluster_id = ? and event_desc like ? order by id desc", clusterId, "%"+eventType+"%"); err != nil {
		return err
	}

	// 未查询到指定事件类型的有效记录
	if len(eventList) == 0 {
		return errors.New("clusterId:" + cast.ToString(clusterId) + " has no record with eventType:" + eventType)
	}

	// 对eventList数据进行匹配&配对
	var targetEventRecord *csmaster_model_interface.EventList
	var incompletedStartEventList []*csmaster_model_interface.EventList
	eventListSummary := make(map[string][]*csmaster_model_interface.EventList)
	for _, eventRecord := range eventList {
		if eventRecord.Type == 2 {
			endEventDesc := eventRecord.EventDesc
			endEventDescList := strings.Split(endEventDesc, "|end|")
			eventListSummary[endEventDescList[0]] = append(eventListSummary[endEventDescList[0]], eventRecord)
		} else if eventRecord.Type == 1 {
			endEventDesc := eventRecord.EventDesc
			eventListSummary[endEventDesc] = append(eventListSummary[endEventDesc], eventRecord)
		}
	}

	for _, eventLists := range eventListSummary {
		if len(eventLists) == 1 {
			if eventLists[0].Type == 1 {
				incompletedStartEventList = append(incompletedStartEventList, eventLists[0])
			}
		} else if len(eventLists)%2 == 1 {
			count := 0
			for _, event := range eventLists {
				if event.Type == 2 {
					count++
				}
			}
			for _, event := range eventLists {
				if len(incompletedStartEventList) == len(eventLists)-count*2 {
					break
				} else if event.Type == 1 {
					incompletedStartEventList = append(incompletedStartEventList, event)
				}
			}

		} else if len(eventLists)%2 == 0 {
			// 结束事件的数量是否为总数量的二分之一
			count := 0
			for _, eventList := range eventLists {
				if eventList.Type == 2 {
					count++
				}
			}
			if count != len(eventLists)/2 {
				for _, event := range eventLists {
					if len(incompletedStartEventList) == len(eventLists)-count*2 {
						break
					} else if event.Type == 1 {
						incompletedStartEventList = append(incompletedStartEventList, event)
					}
				}
			}
		}
	}

	if len(incompletedStartEventList) == 0 {
		return errors.New("clusterId:" + cast.ToString(clusterId) + " has no incompleted start event record with eventType:" + eventType)
	} else {
		targetEventRecord = incompletedStartEventList[0]
		for _, eventRecord := range incompletedStartEventList {
			if eventRecord.Id > targetEventRecord.Id {
				targetEventRecord = eventRecord
			}
		}
	}

	timeS := time.Now().Format("2006-01-02 15:04:05")
	eventDesc := targetEventRecord.EventDesc
	endEventDesc := eventDesc + "|end|" + timeS + "|" + eventResult

	return c.resource.CreateOne(ctx, &csmaster_model_interface.EventList{
		ClusterId:  clusterId,
		InstanceId: 0,
		Level:      1,
		Type:       2,
		Time:       time.Now(),
		EventDesc:  endEventDesc,
	})
}
