/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/04/20 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file userinfo.go
 * <AUTHOR>
 * @date 2022/04/20 16:06:59
 * @brief userinfo interface
 *
 **/

package csmaster_model_api

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

// GetUserIDByIamUserID returns userid in database
func (c CsmasterModelOpInstance) GetUserIDByIamUserID(ctx context.Context, iamUserID string) (int64, error) {
	var userInfo csmaster_model_interface.Userinfo
	err := c.resource.GetOneByCond(ctx, &userInfo, "iam_user_id = ?", iamUserID)
	if err != nil {
		return 0, errors.Errorf("get user id by iam_user_id:%s failed, error:%s", iamUserID, err.Error())
	}
	return userInfo.Id, nil
}

// GetIamUserIDByUserID returns iam user id in database
func (c CsmasterModelOpInstance) GetIamUserIDByUserID(ctx context.Context, UserID int64) (string, error) {
	var userInfo csmaster_model_interface.Userinfo
	err := c.resource.GetOneByCond(ctx, &userInfo, "id = ?", UserID)
	if err != nil {
		return "", errors.Errorf("get user iam_user_id by id:%d failed, error:%s", UserID, err.Error())
	}
	return userInfo.IamUserId, nil
}
