package csmaster_model_api

import (
	"context"
	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"testing"
	"time"
)

func TestGetIamUserIDByUserID(t *testing.T) {

	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").
		WillReturnRows(sqlmock.NewRows([]string{"id", "iam_user_id", "security_group_id", "icmp_security_group_rules_id", "ssh_security_group_rules_id",
			"security_group_rules_id", "create_time", "type"}).
			AddRow(1, "iam_user_id", "security_group_id", "icmp_security_group_rules_id", "ssh_security_group_rules_id",
				"security_group_rules_id", time.Now(), 1))
	mock.ExpectCommit()
	tmp := testResource.csmasterModel.ModelGorm
	testResource.csmasterModel.ModelGorm = gormDB
	defer func() {
		testResource.csmasterModel.ModelGorm = tmp
	}()
	ret, err := testResource.CsmasterOpAgent.GetIamUserIDByUserID(context.Background(), 1)
	if err != nil {
		t.Fatalf("error GetIamUserIDByUserID: %s", err)
	}
	if ret != "iam_user_id" {
		t.Fatalf("error GetIamUserIDByUserID: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("Sfafa").
		WillReturnRows(sqlmock.NewRows([]string{"id", "iam_user_id", "security_group_id", "icmp_security_group_rules_id", "ssh_security_group_rules_id",
			"security_group_rules_id", "create_time", "type"}).
			AddRow(1, "iam_user_id", "security_group_id", "icmp_security_group_rules_id", "ssh_security_group_rules_id",
				"security_group_rules_id", time.Now(), 1))
	mock.ExpectCommit()
	ret, err = testResource.CsmasterOpAgent.GetIamUserIDByUserID(context.Background(), 2)
	if err == nil {
		t.Fatalf("error GetIamUserIDByUserID: %s", err)
	}
}
