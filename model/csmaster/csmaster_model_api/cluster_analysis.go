/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/22 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file cluster_analysis.go
 * <AUTHOR>
 * @date 2023/03/22 15:29:47
 * @brief
 *
 **/

package csmaster_model_api

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func (c CsmasterModelOpInstance) GetAnalysisTaskByID(ctx context.Context, taskID int64) (*csmaster_model_interface.CacheClusterAnalyze, error) {
	var analysisTask csmaster_model_interface.CacheClusterAnalyze
	err := c.resource.GetOneByCond(ctx, &analysisTask, "id = ?", taskID)
	if err != nil {
		return nil, err
	}
	return &analysisTask, nil
}

func (c CsmasterModelOpInstance) UpdateAnalysisTaskResult(ctx context.Context, params []*csmaster_model_interface.ClusterAnalysisResult) error {
	return c.resource.FullSaveAssociationsSave(ctx, params)
}

func (c CsmasterModelOpInstance) UpdateAnalysisTask(ctx context.Context, params *csmaster_model_interface.CacheClusterAnalyze) error {
	return c.resource.FullSaveAssociationsSave(ctx, params)
}

func (c CsmasterModelOpInstance) GetAnalysisTaskByClusterID(ctx context.Context, clusterID int64) (*csmaster_model_interface.CacheClusterAnalyze, error) {
	var analysisTask csmaster_model_interface.CacheClusterAnalyze
	err := c.resource.GetOneByCond(ctx, &analysisTask, "cluster_id = ?", clusterID)
	if err != nil {
		return nil, err
	}
	return &analysisTask, nil
}

func (c CsmasterModelOpInstance) DeleteAnalysisTaskResult(ctx context.Context, clusterID int64) error {
	return c.resource.DeleteAllByCond(ctx, &csmaster_model_interface.ClusterAnalysisResult{}, "cluster_id = ?", clusterID)
}
