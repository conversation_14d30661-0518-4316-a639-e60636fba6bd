package csmaster_model_api

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func (c CsmasterModelOpInstance) GetBackupRecordsMapByClusterID(ctx context.Context, clusterID int64) (map[string][]*csmaster_model_interface.BackupRecord, error) {
	ret := make(map[string][]*csmaster_model_interface.BackupRecord)
	var backupRecords []*csmaster_model_interface.BackupRecord
	if err := c.resource.GetAllByCond(ctx, &backupRecords, "cluster_id = ?", clusterID); err != nil {
		return nil, err
	}
	for _, backupRecord := range backupRecords {
		if _, ok := ret[backupRecord.BatchID]; !ok {
			ret[backupRecord.BatchID] = make([]*csmaster_model_interface.BackupRecord, 0)
		}
		ret[backupRecord.BatchID] = append(ret[backupRecord.BatchID], backupRecord)
	}
	return ret, nil
}
