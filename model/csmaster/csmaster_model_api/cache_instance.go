/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/03/15
 * File: cache_instance.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package apis TODO package function desc
package csmaster_model_api

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func (c CsmasterModelOpInstance) GetAllClusterInstance(ctx context.Context, clusterId int64) ([]*csmaster_model_interface.CacheInstance, error) {
	var cacheInstances []*csmaster_model_interface.CacheInstance
	err := c.resource.GetAllByCond(ctx, &cacheInstances, "id = ?", clusterId)
	if err != nil {
		return nil, err
	}
	return cacheInstances, nil
}

func (c CsmasterModelOpInstance) GetInstanceByID(ctx context.Context, instanceId int64) (*csmaster_model_interface.CacheInstance, error) {
	var cacheInstance csmaster_model_interface.CacheInstance
	err := c.resource.GetOneByCond(ctx, &cacheInstance, "id = ?", instanceId)
	if err != nil {
		return nil, err
	}
	return &cacheInstance, nil
}

func (c CsmasterModelOpInstance) GetInstancesByClusterID(ctx context.Context, clusterId int64) ([]*csmaster_model_interface.CacheInstance, error) {
	var cacheInstances []*csmaster_model_interface.CacheInstance
	err := c.resource.GetAllByCond(ctx, &cacheInstances, "cluster_id = ?", clusterId)
	if err != nil {
		return nil, err
	}
	return cacheInstances, nil
}
