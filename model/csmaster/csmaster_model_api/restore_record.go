package csmaster_model_api

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func (c CsmasterModelOpInstance) GetRestoreRecordsMapByClusterID(ctx context.Context, clusterID int64) (map[string][]*csmaster_model_interface.RestoreRecord, error) {
	ret := make(map[string][]*csmaster_model_interface.RestoreRecord)
	var restoreRecords []*csmaster_model_interface.RestoreRecord
	if err := c.resource.GetAllByCond(ctx, &restoreRecords, "cluster_id = ?", clusterID); err != nil {
		return nil, err
	}
	for _, restoreRecord := range restoreRecords {
		if _, ok := ret[restoreRecord.BatchID]; !ok {
			ret[restoreRecord.BatchID] = make([]*csmaster_model_interface.RestoreRecord, 0)
		}
		ret[restoreRecord.BatchID] = append(ret[restoreRecord.BatchID], restoreRecord)
	}
	return ret, nil
}
