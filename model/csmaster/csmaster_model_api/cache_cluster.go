/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/03/14
 * File: cache_cluster.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package apis TODO package function desc
package csmaster_model_api

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func (c CsmasterModelOpInstance) UpdateClusterModel(ctx context.Context, params *csmaster_model_interface.CacheCluster) error {
	return c.resource.FullSaveAssociationsSave(ctx, params)
}

func (c CsmasterModelOpInstance) GetClusterModel(ctx context.Context, clusterId int64) (*csmaster_model_interface.CacheCluster, error) {
	var cachecluster csmaster_model_interface.CacheCluster
	err := c.resource.GetOneByCond(ctx, &cachecluster, "id = ?", clusterId)
	if err != nil {
		return nil, err
	}
	return &cachecluster, nil
}

func (c CsmasterModelOpInstance) GetClusterModelNoTx(ctx context.Context, clusterId int64) (*csmaster_model_interface.CacheCluster, error) {
	var cachecluster csmaster_model_interface.CacheCluster
	err := c.resource.GetOneByCondNoTx(ctx, &cachecluster, "id = ?", clusterId)
	if err != nil {
		return nil, err
	}
	return &cachecluster, nil
}

func (c CsmasterModelOpInstance) GetClusterModelByAppId(ctx context.Context, clusterShowName string) (*csmaster_model_interface.CacheCluster, error) {
	var cachecluster csmaster_model_interface.CacheCluster
	err := c.resource.GetOneByCond(ctx, &cachecluster, "cluster_show_id = ?", clusterShowName)
	if err != nil {
		return nil, err
	}
	return &cachecluster, nil
}

func (c CsmasterModelOpInstance) GetClusterModelByAppIdNoTx(ctx context.Context, clusterShowName string) (*csmaster_model_interface.CacheCluster, error) {
	var cachecluster csmaster_model_interface.CacheCluster
	err := c.resource.GetOneByCondNoTx(ctx, &cachecluster, "cluster_show_id = ?", clusterShowName)
	if err != nil {
		return nil, err
	}
	return &cachecluster, nil
}

func (c CsmasterModelOpInstance) ClusterIdToAppid(ctx context.Context, clusterId int64) (appid string, err error) {
	var cachecluster csmaster_model_interface.CacheCluster
	err = c.resource.GetOneByCond(ctx, &cachecluster, "id = ?", clusterId)
	if err != nil {
		return "", err
	}
	return cachecluster.ClusterShowId, nil
}

func (c CsmasterModelOpInstance) GetClusterModelByStatus(ctx context.Context, status int64) ([]*csmaster_model_interface.CacheCluster, error) {
	var cacheclusters []*csmaster_model_interface.CacheCluster
	err := c.resource.GetAllByCond(ctx, &cacheclusters, "status = ?", status)
	if err != nil {
		return nil, err
	}
	return cacheclusters, nil
}

func (c CsmasterModelOpInstance) GetClusterModelByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*csmaster_model_interface.CacheCluster, error) {
	var cacheclusters []*csmaster_model_interface.CacheCluster
	err := c.resource.GetAllByCond(ctx, &cacheclusters, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return cacheclusters, nil
}
