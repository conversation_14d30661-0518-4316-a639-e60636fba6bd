package csmaster_model_api

import (
	"context"
	"reflect"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/scs/x1-base/model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func Test_parseRule(t *testing.T) {
	type args struct {
		feature string
		rule    string
	}
	tests := []struct {
		name string
		args args
		want Rule
	}{
		{
			name: "test1",
			args: args{
				feature: "environment.all;environment.prefix;environment.suffix;environment.gt;environment.lt;environment.ge;environment.le;environment.in;environment.eq",
				rule:    "*;prefix*;*suffix;>abc;<abc;>=abc;<=abc;a|b|c;abc",
			},
			want: Rule{
				"all":    &RuleElement{Value: "*", MatchType: "all"},
				"prefix": &RuleElement{Value: "prefix", MatchType: "prefix"},
				"suffix": &RuleElement{Value: "suffix", MatchType: "suffix"},
				"gt":     &RuleElement{Value: "abc", MatchType: "gt"},
				"lt":     &RuleElement{Value: "abc", MatchType: "lt"},
				"ge":     &RuleElement{Value: "abc", MatchType: "ge"},
				"le":     &RuleElement{Value: "abc", MatchType: "le"},
				"in":     &RuleElement{Value: []string{"a", "b", "c"}, MatchType: "in"},
				"eq":     &RuleElement{Value: "abc", MatchType: "eq"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := parseRule(tt.args.feature, tt.args.rule); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("parseRule() = %s, want %s", base_utils.Format(got), base_utils.Format(tt.want))
			}
		})
	}
}

func Test_matchRule(t *testing.T) {
	type args struct {
		features map[string]string
		rule     Rule
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "test1",
			args: args{
				features: map[string]string{
					"all":    "abc",
					"prefix": "abcxxx",
					"suffix": "xxxabc",
					"gt":     "abd",
					"lt":     "abb",
					"ge":     "abc",
					"le":     "abc",
					"in":     "a",
					"eq":     "abc",
				},
				rule: Rule{
					"all":    &RuleElement{Value: "*", MatchType: "all"},
					"prefix": &RuleElement{Value: "abc", MatchType: "prefix"},
					"suffix": &RuleElement{Value: "abc", MatchType: "suffix"},
					"gt":     &RuleElement{Value: "abc", MatchType: "gt"},
					"lt":     &RuleElement{Value: "abc", MatchType: "lt"},
					"ge":     &RuleElement{Value: "abc", MatchType: "ge"},
					"le":     &RuleElement{Value: "abc", MatchType: "le"},
					"in":     &RuleElement{Value: []string{"a", "b", "c"}, MatchType: "in"},
					"eq":     &RuleElement{Value: "abc", MatchType: "eq"},
				},
			},
			want: true,
		},
		{
			name: "test1",
			args: args{
				features: map[string]string{
					"all":    "abc",
					"prefix": "xxxxxx",
					"suffix": "xxxabc",
					"gt":     "abd",
					"lt":     "abb",
					"ge":     "abc",
					"le":     "abc",
					"in":     "a",
					"eq":     "abc",
				},
				rule: Rule{
					"all":    &RuleElement{Value: "*", MatchType: "all"},
					"prefix": &RuleElement{Value: "abc", MatchType: "prefix"},
					"suffix": &RuleElement{Value: "abc", MatchType: "suffix"},
					"gt":     &RuleElement{Value: "abc", MatchType: "gt"},
					"lt":     &RuleElement{Value: "abc", MatchType: "lt"},
					"ge":     &RuleElement{Value: "abc", MatchType: "ge"},
					"le":     &RuleElement{Value: "abc", MatchType: "le"},
					"in":     &RuleElement{Value: []string{"a", "b", "c"}, MatchType: "in"},
					"eq":     &RuleElement{Value: "abc", MatchType: "eq"},
				},
			},
			want: false,
		},
		{
			name: "test1",
			args: args{
				features: map[string]string{
					"all":    "abc",
					"prefix": "abcxxx",
					"suffix": "xxxxxx",
					"gt":     "abd",
					"lt":     "abb",
					"ge":     "abc",
					"le":     "abc",
					"in":     "a",
					"eq":     "abc",
				},
				rule: Rule{
					"all":    &RuleElement{Value: "*", MatchType: "all"},
					"prefix": &RuleElement{Value: "abc", MatchType: "prefix"},
					"suffix": &RuleElement{Value: "abc", MatchType: "suffix"},
					"gt":     &RuleElement{Value: "abc", MatchType: "gt"},
					"lt":     &RuleElement{Value: "abc", MatchType: "lt"},
					"ge":     &RuleElement{Value: "abc", MatchType: "ge"},
					"le":     &RuleElement{Value: "abc", MatchType: "le"},
					"in":     &RuleElement{Value: []string{"a", "b", "c"}, MatchType: "in"},
					"eq":     &RuleElement{Value: "abc", MatchType: "eq"},
				},
			},
			want: false,
		},
		{
			name: "test1",
			args: args{
				features: map[string]string{
					"all":    "abc",
					"prefix": "abcxxx",
					"suffix": "xxxabc",
					"gt":     "abc",
					"lt":     "abb",
					"ge":     "abc",
					"le":     "abc",
					"in":     "a",
					"eq":     "abc",
				},
				rule: Rule{
					"all":    &RuleElement{Value: "*", MatchType: "all"},
					"prefix": &RuleElement{Value: "abc", MatchType: "prefix"},
					"suffix": &RuleElement{Value: "abc", MatchType: "suffix"},
					"gt":     &RuleElement{Value: "abc", MatchType: "gt"},
					"lt":     &RuleElement{Value: "abc", MatchType: "lt"},
					"ge":     &RuleElement{Value: "abc", MatchType: "ge"},
					"le":     &RuleElement{Value: "abc", MatchType: "le"},
					"in":     &RuleElement{Value: []string{"a", "b", "c"}, MatchType: "in"},
					"eq":     &RuleElement{Value: "abc", MatchType: "eq"},
				},
			},
			want: false,
		},
		{
			name: "test1",
			args: args{
				features: map[string]string{
					"all":    "abc",
					"prefix": "abcxxx",
					"suffix": "xxxabc",
					"gt":     "abd",
					"lt":     "abc",
					"ge":     "abc",
					"le":     "abc",
					"in":     "a",
					"eq":     "abc",
				},
				rule: Rule{
					"all":    &RuleElement{Value: "*", MatchType: "all"},
					"prefix": &RuleElement{Value: "abc", MatchType: "prefix"},
					"suffix": &RuleElement{Value: "abc", MatchType: "suffix"},
					"gt":     &RuleElement{Value: "abc", MatchType: "gt"},
					"lt":     &RuleElement{Value: "abc", MatchType: "lt"},
					"ge":     &RuleElement{Value: "abc", MatchType: "ge"},
					"le":     &RuleElement{Value: "abc", MatchType: "le"},
					"in":     &RuleElement{Value: []string{"a", "b", "c"}, MatchType: "in"},
					"eq":     &RuleElement{Value: "abc", MatchType: "eq"},
				},
			},
			want: false,
		},
		{
			name: "test1",
			args: args{
				features: map[string]string{
					"all":    "abc",
					"prefix": "abcxxx",
					"suffix": "xxxabc",
					"gt":     "abd",
					"lt":     "abb",
					"ge":     "abb",
					"le":     "abc",
					"in":     "a",
					"eq":     "abc",
				},
				rule: Rule{
					"all":    &RuleElement{Value: "*", MatchType: "all"},
					"prefix": &RuleElement{Value: "abc", MatchType: "prefix"},
					"suffix": &RuleElement{Value: "abc", MatchType: "suffix"},
					"gt":     &RuleElement{Value: "abc", MatchType: "gt"},
					"lt":     &RuleElement{Value: "abc", MatchType: "lt"},
					"ge":     &RuleElement{Value: "abc", MatchType: "ge"},
					"le":     &RuleElement{Value: "abc", MatchType: "le"},
					"in":     &RuleElement{Value: []string{"a", "b", "c"}, MatchType: "in"},
					"eq":     &RuleElement{Value: "abc", MatchType: "eq"},
				},
			},
			want: false,
		},
		{
			name: "test1",
			args: args{
				features: map[string]string{
					"all":    "abc",
					"prefix": "abcxxx",
					"suffix": "xxxabc",
					"gt":     "abd",
					"lt":     "abb",
					"ge":     "abc",
					"le":     "abd",
					"in":     "a",
					"eq":     "abc",
				},
				rule: Rule{
					"all":    &RuleElement{Value: "*", MatchType: "all"},
					"prefix": &RuleElement{Value: "abc", MatchType: "prefix"},
					"suffix": &RuleElement{Value: "abc", MatchType: "suffix"},
					"gt":     &RuleElement{Value: "abc", MatchType: "gt"},
					"lt":     &RuleElement{Value: "abc", MatchType: "lt"},
					"ge":     &RuleElement{Value: "abc", MatchType: "ge"},
					"le":     &RuleElement{Value: "abc", MatchType: "le"},
					"in":     &RuleElement{Value: []string{"a", "b", "c"}, MatchType: "in"},
					"eq":     &RuleElement{Value: "abc", MatchType: "eq"},
				},
			},
			want: false,
		},
		{
			name: "test1",
			args: args{
				features: map[string]string{
					"all":    "abc",
					"prefix": "abcxxx",
					"suffix": "xxxabc",
					"gt":     "abd",
					"lt":     "abb",
					"ge":     "abc",
					"le":     "abc",
					"in":     "d",
					"eq":     "abc",
				},
				rule: Rule{
					"all":    &RuleElement{Value: "*", MatchType: "all"},
					"prefix": &RuleElement{Value: "abc", MatchType: "prefix"},
					"suffix": &RuleElement{Value: "abc", MatchType: "suffix"},
					"gt":     &RuleElement{Value: "abc", MatchType: "gt"},
					"lt":     &RuleElement{Value: "abc", MatchType: "lt"},
					"ge":     &RuleElement{Value: "abc", MatchType: "ge"},
					"le":     &RuleElement{Value: "abc", MatchType: "le"},
					"in":     &RuleElement{Value: []string{"a", "b", "c"}, MatchType: "in"},
					"eq":     &RuleElement{Value: "abc", MatchType: "eq"},
				},
			},
			want: false,
		},
		{
			name: "test1",
			args: args{
				features: map[string]string{
					"all":    "abc",
					"prefix": "abcxxx",
					"suffix": "xxxabc",
					"gt":     "abd",
					"lt":     "abb",
					"ge":     "abc",
					"le":     "abc",
					"in":     "a",
					"eq":     "abcd",
				},
				rule: Rule{
					"all":    &RuleElement{Value: "*", MatchType: "all"},
					"prefix": &RuleElement{Value: "abc", MatchType: "prefix"},
					"suffix": &RuleElement{Value: "abc", MatchType: "suffix"},
					"gt":     &RuleElement{Value: "abc", MatchType: "gt"},
					"lt":     &RuleElement{Value: "abc", MatchType: "lt"},
					"ge":     &RuleElement{Value: "abc", MatchType: "ge"},
					"le":     &RuleElement{Value: "abc", MatchType: "le"},
					"in":     &RuleElement{Value: []string{"a", "b", "c"}, MatchType: "in"},
					"eq":     &RuleElement{Value: "abc", MatchType: "eq"},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := matchRule(tt.args.features, tt.args.rule); got != tt.want {
				t.Errorf("matchRule() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCsmasterModelOpInstance_GetFlag(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	c := &CsmasterModelOpInstance{
		resource: &model.Resource{
			ModelGorm: gormDB,
		},
	}
	rows := sqlmock.NewRows([]string{"name", "features", "rule", "flag", "priority"}).
		AddRow("test", "environment.iam_user_id;environment.vpc_id", "5567229c43784b779999ef8fbcab87ba;*", "yes", 10).
		AddRow("test", "environment.iam_user_id;environment.vpc_id", "-", "no", -1)
	mock.ExpectQuery("^select .*").WillReturnRows(rows)
	flag, err := c.GetFlag(context.Background(), "test", map[string]string{"iam_user_id": "5567229c43784b779999ef8fbcab87ba", "vpc_id": "vpc-123456"}, "test")
	if err != nil {
		t.Errorf("GetFlag() error = %v", err)
		return
	}
	if flag != "yes" {
		t.Errorf("GetFlag() flag = %v, want %v", flag, "yes")
	}
	rows2 := sqlmock.NewRows([]string{"name", "features", "rule", "flag", "priority"}).
		AddRow("test", "environment.iam_user_id;environment.vpc_id", "5567229c43784b779999ef8fbcab87ba;*", "yes", 10).
		AddRow("test", "environment.iam_user_id;environment.vpc_id", "-", "no", -1)
	mock.ExpectQuery("^select .*").WillReturnRows(rows2)
	flag, err = c.GetFlag(context.Background(), "test", map[string]string{"iam_user_id": "xxx", "vpc_id": "vpc-123456"}, "test")
	if err != nil {
		t.Errorf("GetFlag() error = %v", err)
		return
	}
	if flag != "no" {
		t.Errorf("GetFlag() flag = %v, want %v", flag, "yes")
	}
}
