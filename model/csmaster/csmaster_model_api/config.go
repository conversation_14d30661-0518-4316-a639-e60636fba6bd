package csmaster_model_api

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func (c CsmasterModelOpInstance) GetConfigByAppShortID(ctx context.Context, appShortID int) ([]*csmaster_model_interface.ConfRecordList, error) {
	var confs []*csmaster_model_interface.ConfRecordList
	err := c.resource.GetAllByCond(ctx, &confs, "cluster_id = ?", appShortID)
	if err != nil {
		return nil, err
	}
	return confs, nil
}
