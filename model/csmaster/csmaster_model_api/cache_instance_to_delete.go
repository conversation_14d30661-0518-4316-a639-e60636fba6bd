package csmaster_model_api

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func (c CsmasterModelOpInstance) GetInstanceToDeleteByClearStatus(ctx context.Context, clearStatus int) (
	[]*csmaster_model_interface.CacheInstanceToDelete, error) {
	var cacheInstances []*csmaster_model_interface.CacheInstanceToDelete
	err := c.resource.GetAllByCond(ctx, &cacheInstances, "clear_status =?", clearStatus)
	if err != nil {
		return nil, err
	}
	return cacheInstances, nil
}

func (c CsmasterModelOpInstance) UpdateCacheInstanceToDelete(ctx context.Context, params []*csmaster_model_interface.CacheInstanceToDelete) error {
	return c.resource.FullSaveAssociationsSave(ctx, params)
}
