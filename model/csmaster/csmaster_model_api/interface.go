/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/03/17
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package apis TODO package function desc
package csmaster_model_api

import (
	"context"

	cc "icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
)

type CsmasterModelOp interface {
	UpdateClusterModel(ctx context.Context, params *csmaster_model_interface.CacheCluster) error
	GetClusterModel(ctx context.Context, clusterId int64) (*csmaster_model_interface.CacheCluster, error)
	GetClusterModelNoTx(ctx context.Context, clusterId int64) (*csmaster_model_interface.CacheCluster, error)
	SaveInstanceModels(ctx context.Context, params *csmaster_model_interface.CacheInstance) error
	GetInstanceModels(ctx context.Context, userId string, appId string) ([]*csdk.CsmasterInstance, error)
	DeleteInstanceModels(ctx context.Context, params *csmaster_model_interface.CacheInstance) error
	GetAllClusterWhiteListByClusterId(ctx context.Context, clusterId int64) ([]*csmaster_model_interface.ClusterWhiteList, error)
	ClusterIdToAppid(ctx context.Context, clusterId int64) (appid string, err error)
	RemoveClusterWhiteListBatch(ctx context.Context, clusterId int64, floatIps []string) error
	GetClusterModelByAppId(ctx context.Context, clusterShowName string) (*csmaster_model_interface.CacheCluster, error)
	GetClusterModelByAppIdNoTx(ctx context.Context, clusterShowName string) (*csmaster_model_interface.CacheCluster, error)
	UpdateClusterWhiteLists(ctx context.Context, data []*csmaster_model_interface.ClusterWhiteList) error
	GetSystemTemplate(ctx context.Context) ([]*csmaster_model_interface.SystemTemplate, error)
	GetUserIDByIamUserID(ctx context.Context, iamUserID string) (int64, error)
	GetIamUserIDByUserID(ctx context.Context, UserID int64) (string, error)
	GetTemplateRecordByID(ctx context.Context, templateID int64) ([]*csmaster_model_interface.TemplateRecord, error)
	GetTemplateByID(ctx context.Context, templateID int64) (*csmaster_model_interface.Template, error)
	GetTemplateByShowID(ctx context.Context, templateShowID string) (*csmaster_model_interface.Template, error)
	GetTemplateByUserID(ctx context.Context, userID int64) ([]*csmaster_model_interface.Template, error)
	UpdateTemplateModel(ctx context.Context, params *csmaster_model_interface.Template) error
	DeleteTemplateByID(ctx context.Context, templateID int64) error
	CreateTemplateModel(ctx context.Context, params *csmaster_model_interface.Template) error
	CreateTemplateRecordModel(ctx context.Context, params *csmaster_model_interface.TemplateRecord) error
	GetTemplateCountByCond(ctx context.Context, userID int64, clusterType, engineVersion string) (int, error)
	GetReadonlyGroupByID(ctx context.Context, roGroupID int64) (*csmaster_model_interface.ReadonlyGroup, error)
	GetReadonlyGroupByShowID(ctx context.Context, roGroupShowID string) (*csmaster_model_interface.ReadonlyGroup, error)
	UpdateReadonlyGroupModel(ctx context.Context, params *csmaster_model_interface.ReadonlyGroup) error
	GetReadonlyGroupByName(ctx context.Context, roGroupName string, userID int64) (*csmaster_model_interface.ReadonlyGroup, error)
	CreateReadonlyGroupModel(ctx context.Context, params *csmaster_model_interface.ReadonlyGroup) error
	GetReadonlyGroupsByClusterID(ctx context.Context, clusterShowID string) ([]*csmaster_model_interface.ReadonlyGroup, error)
	GetInstanceOrderByID(ctx context.Context, orderID string) (*csmaster_model_interface.InstanceOrder, error)
	UpdateInstanceOrderModel(ctx context.Context, params *csmaster_model_interface.InstanceOrder) error
	CreateInstanceOrderModel(ctx context.Context, params *csmaster_model_interface.InstanceOrder) error
	GetInstanceOrderByParentID(ctx context.Context, ParentID string) ([]*csmaster_model_interface.InstanceOrder, error)
	GetRedisAclsByAppShortID(ctx context.Context, appShortID int) ([]*csmaster_model_interface.ClusterAclUser, error)
	GetConfigByAppShortID(ctx context.Context, appShortID int) ([]*csmaster_model_interface.ConfRecordList, error)
	GetClusterMap(ctx context.Context) ([]*csmaster_model_interface.ClusterMap, error)
	GetInstanceByID(ctx context.Context, instanceId int64) (*csmaster_model_interface.CacheInstance, error)
	GetInstancesByClusterID(ctx context.Context, clusterId int64) ([]*csmaster_model_interface.CacheInstance, error)
	GetBackupRecordsMapByClusterID(ctx context.Context, clusterID int64) (map[string][]*csmaster_model_interface.BackupRecord, error)
	GetRestoreRecordsMapByClusterID(ctx context.Context, clusterID int64) (map[string][]*csmaster_model_interface.RestoreRecord, error)
	GetEventListByClusterID(ctx context.Context, clusterShowId string) ([]*csmaster_model_interface.EventList, error)
	AddEventStart(ctx context.Context, clusterShowId string, eventState string) error
	AddEventEnd(ctx context.Context, clusterShowId string, eventType string, eventResult string) error
	GetAnalysisTaskByID(ctx context.Context, taskID int64) (*csmaster_model_interface.CacheClusterAnalyze, error)
	GetAnalysisTaskByClusterID(ctx context.Context, clusterID int64) (*csmaster_model_interface.CacheClusterAnalyze, error)
	UpdateAnalysisTask(ctx context.Context, params *csmaster_model_interface.CacheClusterAnalyze) error
	UpdateAnalysisTaskResult(ctx context.Context, params []*csmaster_model_interface.ClusterAnalysisResult) error
	DeleteAnalysisTaskResult(ctx context.Context, clusterID int64) error
	GetExpRules(ctx context.Context, name string) ([]*ExpRule, error)
	GetFlag(ctx context.Context, name string, features map[string]string, df string) (string, error)
	TimeWindowTaskGetAllByCond(ctx context.Context, fmtCond string, vals ...any) ([]*csmaster_model_interface.TimeWindowTask, error)
	TimeWindowTaskSave(ctx context.Context, dataPtr []*csmaster_model_interface.TimeWindowTask) error
	GetInstanceToDeleteByClearStatus(ctx context.Context, clearStatus int) ([]*csmaster_model_interface.CacheInstanceToDelete, error)
	UpdateCacheInstanceToDelete(ctx context.Context, params []*csmaster_model_interface.CacheInstanceToDelete) error
	GetClusterModelByStatus(ctx context.Context, status int64) ([]*csmaster_model_interface.CacheCluster, error)
	GetBaeClusterWhiteListByClusterId(ctx context.Context, clusterId int64) ([]*csmaster_model_interface.BaeClusterWhiteList, error)
	GetClusterWhiteListByClusterId(ctx context.Context, clusterId int64) ([]*csmaster_model_interface.ClusterWhiteList, error)
	RemoveClusterWhiteList(ctx context.Context, clusterId int64, floatIp string) error
	UpdateClusterWhiteList(ctx context.Context, data []*csmaster_model_interface.ClusterWhiteList) error
	GetClusterModelByCond(ctx context.Context, fmtCond string, vals ...any) ([]*csmaster_model_interface.CacheCluster, error)
	GetClusterWhiteListByClusterIdAndGroupName(ctx context.Context, clusterId int64, groupName string) ([]*csmaster_model_interface.ClusterWhiteList, error)
	RemoveClusterWhiteListGroup(ctx context.Context, clusterId int64, groupName string) error
	ModifyClusterWhiteListGroup(ctx context.Context, clusterId int64, oldGroupName string, newGroupName string, data []*csmaster_model_interface.ClusterWhiteList) error
}

type CsmasterModelOpInstance struct {
	resource   *model.Resource
	csmasterOp cc.CsmasterOpIface
}

func (c CsmasterModelOpInstance) SaveInstanceModels(ctx context.Context, params *csmaster_model_interface.CacheInstance) error {
	// TODO implement me
	panic("implement me")
}

func (c CsmasterModelOpInstance) GetInstanceModels(ctx context.Context, userId string, appId string) ([]*csdk.CsmasterInstance, error) {
	// TODO implement me
	panic("implement me")
}

func (c CsmasterModelOpInstance) DeleteInstanceModels(ctx context.Context, params *csmaster_model_interface.CacheInstance) error {
	// TODO implement me
	panic("implement me")
}

func NewCsmasterModelOp(ctx context.Context) (CsmasterModelOp, error) {
	dbInstance, err := csmaster.GetDbAgent(ctx)
	if err != nil {
		return nil, err
	}
	op := &CsmasterModelOpInstance{
		resource:   dbInstance,
		csmasterOp: cc.CsmasterOp(),
	}
	return op, nil
}
