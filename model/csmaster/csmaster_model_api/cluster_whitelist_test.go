package csmaster_model_api

import (
	"context"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func TestCsmasterModelOpInstance_GetClusterWhiteListByClusterId(t *testing.T) {
	table, err := testResource.CsmasterOpAgent.GetClusterWhiteListByClusterId(context.Background(), 0)
	if err != nil {
		fmt.Println(err)
		return
	}
	for _, row := range table {
		fmt.Printf("%+v\n", row)
	}
	t.Log(table)
}

func TestCsmasterModelOpInstance_RemoveClusterWhiteList(t *testing.T) {
	ips := "123"
	err := testResource.CsmasterOpAgent.RemoveClusterWhiteList(context.Background(), 12, ips)
	if err != nil {
		fmt.Println("test")
		fmt.Println(err)
		return
	}
}

func TestCsmasterModelOpInstance_UpdateClusterWhiteList(t *testing.T) {
	data := []*csmaster_model_interface.ClusterWhiteList{}
	err := testResource.CsmasterOpAgent.UpdateClusterWhiteList(context.Background(), data)
	if err != nil {
		fmt.Println(err)
		return
	}
}
