package csmaster_model_api

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func (c CsmasterModelOpInstance) GetClusterMap(ctx context.Context) ([]*csmaster_model_interface.ClusterMap, error) {
	clusterMaps := []*csmaster_model_interface.ClusterMap{}
	if err := c.resource.GetAllByCond(ctx, &clusterMaps, ""); err != nil {
		return nil, err
	}
	return clusterMaps, nil
}
