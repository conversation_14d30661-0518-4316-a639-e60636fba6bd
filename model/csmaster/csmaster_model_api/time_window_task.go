package csmaster_model_api

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func (c CsmasterModelOpInstance) TimeWindowTaskGetAllByCond(ctx context.Context, fmtCond string,
	vals ...interface{}) ([]*csmaster_model_interface.TimeWindowTask, error) {
	var tasks []*csmaster_model_interface.TimeWindowTask
	err := c.resource.GetAllByCond(ctx, &tasks, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (c CsmasterModelOpInstance) TimeWindowTaskSave(ctx context.Context, dataPtr []*csmaster_model_interface.TimeWindowTask) error {
	return c.resource.FullSaveAssociationsSave(ctx, dataPtr)
}
