package csmaster_model_api

import (
	"context"
	"fmt"
	"testing"
)

func TestCsmasterModelOpInstance_GetClusterModelByCond_offline(t *testing.T) {
	table, err := testResource.CsmasterOpAgent.GetClusterModelByCond(context.Background(), "status not in (?,?,?) and use_new_agent=?", 0, 10, 12, "yes")
	if err != nil {
		fmt.Println(err)
		return
	}
	for _, row := range table {
		fmt.Printf("%+v\n", row)
	}
	t.Log(table)
	_, err = testResource.CsmasterOpAgent.GetClusterModelByCond(context.Background(), "status not in (?,?,?) and use_new_agent=?", 0, 10, 12)
	if err != nil {
		fmt.Println(err)
		return
	}
}
