/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/23 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file cluster_analysis_test.go
 * <AUTHOR>
 * @date 2023/03/23 11:03:24
 * @brief
 *
 **/

package csmaster_model_api

import (
	"context"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model"
	cc "icode.baidu.com/baidu/scs/x1-base/model/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

var testResource struct {
	modelLogger     logit.Logger
	gormLogger      logit.Logger
	csmasterModel   *model.Resource
	CsmasterOpAgent CsmasterModelOp
}

func init() {
	unittest.UnitTestInit(3)
	initCsmasterModel(context.Background())
}

func initCsmasterModel(ctx context.Context) {
	modelLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("logit/model.toml"))
	if err != nil {
		panic(err.Error())
	}
	gormLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("logit/gorm.toml"))
	if err != nil {
		panic(err.Error())
	}
	testResource.modelLogger = modelLogger
	testResource.gormLogger = gormLogger
	cc.Init(ctx, cc.Conf{
		DbLogger:     testResource.modelLogger,
		GormLogger:   testResource.gormLogger,
		ServicerName: "csmaster-model",
	})
	csmasterModel, err := cc.GetDbAgent(ctx)
	if err != nil {
		panic(err.Error())
	}
	testResource.csmasterModel = csmasterModel
	csmasterOpAgent, err := NewCsmasterModelOp(ctx)
	if err != nil {
		panic(err.Error())
	}
	testResource.CsmasterOpAgent = csmasterOpAgent
}

func TestCsmasterModelOpInstance_GetAnalysisTaskByID(t *testing.T) {
	table, err := testResource.CsmasterOpAgent.GetAnalysisTaskByID(context.Background(), 1)
	if err != nil {
		fmt.Println(err)
	}
	t.Log(table)
}

func TestCsmasterModelOpInstance_UpdateAnalysisTaskResult(t *testing.T) {
	params := make([]*csmaster_model_interface.ClusterAnalysisResult, 1)
	params[0] = &csmaster_model_interface.ClusterAnalysisResult{
		ClusterID: 100000923,
		ShardID:   1,
		ShardName: "scs-bj-jintao01",
		Type:      0,
		KeyName:   "jintao901",
		Info:      "jintao901",
	}
	err := testResource.CsmasterOpAgent.UpdateAnalysisTaskResult(context.Background(), params)
	if err != nil {
		fmt.Println(err)
	}
}

func TestCsmasterModelOpInstance_UpdateAnalysisTask(t *testing.T) {
	table, err := testResource.CsmasterOpAgent.GetAnalysisTaskByID(context.Background(), 1)
	if err != nil {
		fmt.Println(err)
	}
	if table == nil {
		fmt.Println("table is null")
		return
	}
	table.BigkeyTopN = 100
	err = testResource.CsmasterOpAgent.UpdateAnalysisTask(context.Background(), table)
	if err != nil {
		fmt.Println(err)
	}
}

func TestCsmasterModelOpInstance_GetAnalysisTaskByClusterID(t *testing.T) {
	table, err := testResource.CsmasterOpAgent.GetAnalysisTaskByClusterID(context.Background(), 100000922)
	if err != nil {
		fmt.Println(err)
	}
	t.Log(table)
}

func TestCsmasterModelOpInstance_DeleteAnalysisTaskResult(t *testing.T) {
	params := make([]*csmaster_model_interface.ClusterAnalysisResult, 1)
	params[0] = &csmaster_model_interface.ClusterAnalysisResult{
		ClusterID: 100000924,
		ShardID:   1,
		ShardName: "scs-bj-jintao01",
		Type:      0,
		KeyName:   "jintao901",
		Info:      "jintao901",
	}
	err := testResource.CsmasterOpAgent.UpdateAnalysisTaskResult(context.Background(), params)
	if err != nil {
		fmt.Println(err)
	}

	err = testResource.CsmasterOpAgent.DeleteAnalysisTaskResult(context.Background(), 100000924)
	if err != nil {
		fmt.Println(err)
	}
}
