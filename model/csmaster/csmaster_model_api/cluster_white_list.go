package csmaster_model_api

import (
	"context"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

func (c CsmasterModelOpInstance) GetAllClusterWhiteListByClusterId(ctx context.Context, clusterId int64) ([]*csmaster_model_interface.ClusterWhiteList, error) {
	var whitelists []*csmaster_model_interface.ClusterWhiteList
	err := c.resource.GetAllByCond(ctx, &whitelists, "cluster_id = ?", clusterId)
	if err != nil {
		return nil, errors.Errorf("get all cluster whitelist by cluster fail,clusterid:%d, error:%s", clusterId, err.Error())
	}
	return whitelists, nil
}

func (c CsmasterModelOpInstance) RemoveClusterWhiteListBatch(ctx context.Context, clusterId int64, floatIps []string) error {
	err := c.resource.DeleteAllByCondV2(ctx, &csmaster_model_interface.ClusterWhiteList{}, "cluster_id = ? AND floating_ip IN ?", clusterId, floatIps)
	if err != nil {
		return err
	}
	return nil
}

func (c CsmasterModelOpInstance) UpdateClusterWhiteLists(ctx context.Context, data []*csmaster_model_interface.ClusterWhiteList) error {
	return c.resource.FullSaveAssociationsSave(ctx, data)
}
