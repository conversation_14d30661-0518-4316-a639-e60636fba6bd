/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/04/19 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file system_template.go
 * <AUTHOR>
 * @date 2022/04/19 14:55:14
 * @brief system_template api
 *
 **/

package csmaster_model_api

import (
	"context"
	"strconv"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
)

// GetSystemTemplate returns all system template
func (c CsmasterModelOpInstance) GetSystemTemplate(ctx context.Context) ([]*csmaster_model_interface.SystemTemplate, error) {
	var sysTemplates []*csmaster_model_interface.SystemTemplate
	result := c.resource.ModelGorm.Find(&sysTemplates)
	if result.Error != nil {
		return nil, errors.Errorf("get all system templates fail, error:%s", result.Error)
	}
	return sysTemplates, nil
}

// GetTemplateRecordByID returns records filter by template id
func (c CsmasterModelOpInstance) GetTemplateRecordByID(ctx context.Context, templateID int64) ([]*csmaster_model_interface.TemplateRecord, error) {
	var templateRecords []*csmaster_model_interface.TemplateRecord
	err := c.resource.GetAllByCond(ctx, &templateRecords, "template_id=?", templateID)
	if err != nil {
		return nil, errors.Errorf("get all template record fail, error:%s", err)
	}
	return templateRecords, nil
}

// GetTemplateByID return template filter by id
func (c CsmasterModelOpInstance) GetTemplateByID(ctx context.Context, templateID int64) (*csmaster_model_interface.Template, error) {
	var template csmaster_model_interface.Template
	err := c.resource.GetOneByCond(ctx, &template, "id=?", templateID)
	if err != nil {
		return nil, errors.Errorf("get all template record fail, error:%s", err)
	}
	return &template, nil
}

// GetTemplateByShowID return template filter by showid
func (c CsmasterModelOpInstance) GetTemplateByShowID(ctx context.Context, templateShowID string) (*csmaster_model_interface.Template, error) {
	var template csmaster_model_interface.Template
	err := c.resource.GetOneByCond(ctx, &template, "template_show_id=?", templateShowID)
	if err != nil {
		return nil, errors.Errorf("get all template record fail, error:%s", err)
	}
	return &template, nil
}

// GetTemplateByUserID return template filter by iamuserid
func (c CsmasterModelOpInstance) GetTemplateByUserID(ctx context.Context, userID int64) ([]*csmaster_model_interface.Template, error) {
	var template []*csmaster_model_interface.Template
	err := c.resource.GetAllByCond(ctx, &template, "user_id=?", userID)
	if err != nil {
		return nil, errors.Errorf("get all template fail, error:%s", err)
	}
	return template, nil
}

// UpdateTemplateModel modify template name and return nil
func (c CsmasterModelOpInstance) UpdateTemplateModel(ctx context.Context, params *csmaster_model_interface.Template) error {
	return c.resource.FullSaveAssociationsSave(ctx, params)
}

// DeleteTemplateByID delete template record by id
func (c CsmasterModelOpInstance) DeleteTemplateByID(ctx context.Context, templateID int64) error {
	ukey := strconv.FormatInt(templateID, 10)
	var param *csmaster_model_interface.Template = &csmaster_model_interface.Template{}
	return c.resource.DeleteOneByUkey(ctx, ukey, param)
}

// CreateTemplateModel create template model
func (c CsmasterModelOpInstance) CreateTemplateModel(ctx context.Context, params *csmaster_model_interface.Template) error {
	err := c.resource.CreateOne(ctx, params)
	if err != nil {
		return err
	}
	return nil
}

// CreateTemplateRecordModel create template record
func (c CsmasterModelOpInstance) CreateTemplateRecordModel(ctx context.Context, params *csmaster_model_interface.TemplateRecord) error {
	err := c.resource.CreateOne(ctx, params)
	if err != nil {
		return err
	}
	return nil
}

// GetTemplateCountByCond get template count by condition
func (c CsmasterModelOpInstance) GetTemplateCountByCond(ctx context.Context, userID int64, clusterType, engineVersion string) (int, error) {
	var tmpl []*csmaster_model_interface.Template
	err := c.resource.GetAllByCond(ctx, &tmpl, "cluster_type=? and engine_version=? and user_id = ?", clusterType, engineVersion, userID)
	if err != nil {
		return 0, err
	}
	return len(tmpl), nil
}
