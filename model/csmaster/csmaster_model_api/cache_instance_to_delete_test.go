package csmaster_model_api

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"testing"
)

func TestCsmasterModelOpInstance_GetInstanceToDeleteByClearStatus_offline(t *testing.T) {
	table, err := testResource.CsmasterOpAgent.GetInstanceToDeleteByClearStatus(context.Background(), 0)
	if err != nil {
		fmt.Println(err)
		return
	}
	for _, row := range table {
		fmt.Printf("%+v\n", row)
	}
	t.Log(table)
}

func TestCsmasterModelOpInstance_UpdateCacheInstanceToDelete_offline(t *testing.T) {
	table, err := testResource.CsmasterOpAgent.GetInstanceToDeleteByClearStatus(context.Background(), 0)
	if err != nil {
		fmt.Println(err)
		return
	}
	var target []*csmaster_model_interface.CacheInstanceToDelete
	for _, row := range table {
		if row.ID == 765 {
			row.ClearStatus = 1
			target = append(target, row)
		}
	}
	err = testResource.CsmasterOpAgent.UpdateCacheInstanceToDelete(context.Background(), target)
	t.Log(err)
}
