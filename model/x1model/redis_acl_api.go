/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/03
 * File: application_api.go
 */

/*
 * DESCRIPTION
 *   redisAcl操作方法
 */

// Package x1model
package x1model

import (
	"context"
)

func RedisAclGetByAppId(ctx context.Context, appId string) (*RedisAcl, error) {
	var acl RedisAcl
	err := resource.GetAllByCond(ctx, &acl, "app_id = ?", appId)
	if err != nil {
		return nil, err
	}
	return &acl, nil
}

func RedisAclGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*RedisAcl, error) {
	var acl RedisAcl
	err := resource.GetOneByCond(ctx, &acl, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &acl, nil
}

func RedisAclAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*RedisAcl, error) {
	var acls []*RedisAcl
	err := resource.GetAllByCond(ctx, &acls, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return acls, nil
}

func RedisAclSave(ctx context.Context, dataPtr []*RedisAcl) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}

func RedisAclGetInUse(ctx context.Context, appId, accountName string) (*RedisAcl, error) {
	return RedisAclGetByCond(ctx, "app_id = ? AND account_name = ? AND status = ?",
		appId, accountName, ACLStatusInUse)
}
