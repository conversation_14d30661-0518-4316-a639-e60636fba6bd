package x1model

import (
	"context"
	"fmt"
	"testing"
)

func TestAppBack(t *testing.T) {
	ctx := context.Background()
	// 新建

	appID := "test_app"
	if _, err := GetBackupByAppId(ctx, appID); err != nil {
		fmt.Println(err)
	}
	fmt.Println("success")

	appBackupID := "test_appBackupId"
	if _, err := GetBackupByAppBackupId(ctx, appBackupID); err != nil {
		fmt.Println(err)
	}
	fmt.Println("success")

	ID := "test_Id"
	if _, err := GetBackupItemById(ctx, ID); err != nil {
		fmt.Println(err)
	}
	fmt.Println("success")

	appID = "test_app"
	if _, err := GetValidBackupByAppId(ctx, appID); err != nil {
		fmt.Println(err)
	}
	fmt.Println("success")

	appBackupID = "test_appBackupId"
	if _, err := GetValidBackupByAppBackupId(ctx, appBackupID); err != nil {
		fmt.Println(err)
	}
	fmt.Println("success")

	if err := SaveBackup(ctx, []*AppBackup{}); err != nil {
		fmt.Println(err)
	}
	fmt.Println("success")

	fmt.Println(GetBackupStatus(""))
	fmt.Println(GetBackupStatus("success"))
	fmt.Println(GetBackupStatus("doing"))
	fmt.Println(GetBackupStatus("failed"))
	fmt.Println(GetBackupStatus("expired"))
	fmt.Println(GetBackupStatus("delete"))

	fmt.Println(GetBackupType(""))
	fmt.Println(GetBackupType("auto"))
	fmt.Println(GetBackupType("manual"))
	fmt.Println(GetBackupType("analyze"))
}
