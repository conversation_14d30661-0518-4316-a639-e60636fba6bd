package x1model

import (
	"context"
	"time"
)

type PortAliveRatio struct {
	ID       int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	AppID    string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	UserID   string    `gorm:"column:user_id;NOT NULL" json:"user_id"`
	Status   string    `gorm:"column:status;NOT NULL" json:"status"`
	Ratio    float64   `gorm:"column:ratio;NOT NULL" json:"ratio"`
	CreateAt time.Time `gorm:"column:create_at;NOT NULL" json:"create_at"`
}

func (PortAliveRatio) TableName() string {
	return "port_alive_ratio"
}

// PortAliveRatioGetAllByCond 获取实例的SLA数据
func PortAliveRatioGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*PortAliveRatio, error) {
	var portAliveRatios []*PortAliveRatio
	err := resource.GetAllByCond(ctx, &portAliveRatios, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return portAliveRatios, nil
}
