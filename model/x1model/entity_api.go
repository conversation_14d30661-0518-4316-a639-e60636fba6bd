/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (<EMAIL>)
 * Date: 2021/12/06
 * File: entity_api.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package x1model TODO package function desc
package x1model

import (
	"context"
)

func EntityGetByEntityId(ctx context.Context, entityId string) (*Entity, error) {
	var entity Entity
	err := resource.GetOneByUkey(ctx, entityId, &entity)
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

func EntityGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*Entity, error) {
	var entity Entity
	err := resource.GetOneByCond(ctx, &entity, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

func EntityGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*Entity, error) {
	var entitys []*Entity
	err := resource.GetAllByCond(ctx, &entitys, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return entitys, nil
}

func EntitysSave(ctx context.Context, dataPtr []*Entity) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
