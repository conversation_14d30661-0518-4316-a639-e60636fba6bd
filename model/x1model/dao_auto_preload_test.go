/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON>uni<PERSON>@baidu.com)
 * Date: 2022/03/14
 * File: dao_auto_preload_test.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package model TODO package function desc
package x1model

import (
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/model"
	"reflect"
	"testing"
)

func TestAutoPreload(t *testing.T) {
	r := new(model.Resource)
	app := new(Application)
	ty := reflect.TypeOf(app).Elem()
	for i := 0; i < ty.NumField(); i++ {
		field := ty.Field(i)
		ret := r.SearchAutoPreloadConf(field)
		fmt.Println(ret)
	}

}
