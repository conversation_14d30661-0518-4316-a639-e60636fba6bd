/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/01/11
 * File: application_api.go
 */

/*
 * DESCRIPTION
 *   backup 操作方法
 */

// Package x1model
package x1model

import (
	"context"
)

func BackupGetByBackupID(ctx context.Context, backupId string) (*Backup, error) {
	var backup Backup
	err := resource.GetOneByUkey(ctx, backupId, &backup)
	if err != nil {
		return nil, err
	}
	return &backup, nil
}

func BackupGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*Backup, error) {
	var backup Backup
	err := resource.GetOneByCond(ctx, &backup, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &backup, nil
}

func BackupGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*Backup, error) {
	var backups []*Backup
	err := resource.GetAllByCond(ctx, &backups, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return backups, nil
}

func BackupsSave(ctx context.Context, dataPtr []*Backup) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
