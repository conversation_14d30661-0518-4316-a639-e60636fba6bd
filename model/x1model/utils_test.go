package x1model

import (
	"context"
	"testing"
)

func TestFetchAllNodesOfCluster(t *testing.T) {
	type args struct {
		cluster *Cluster
	}
	tests := []struct {
		name    string
		args    args
		want    []*Node
		wantErr bool
	}{
		{
			name: "normal",
			args: args{
				cluster: &Cluster{
					Nodes: []*Node{
						{
							Id: 1,
						},
					},
					RoNodes: []*RoNode{
						{
							Id: 1,
						},
					},
				},
			},
			want: []*Node{
				{
					Id: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := FetchAllNodesOfCluster(context.Background(), tt.args.cluster)
			if (len(got) != len(tt.want)) && !tt.wantErr {
			}
		})
	}

}

func TestTransProxyToNode(t *testing.T) {
	type args struct {
		ctx   context.Context
		proxy *Proxy
	}
	tests := []struct {
		name    string
		args    args
		want    *Node
		wantErr bool
	}{
		{
			name: "transProxyToNode",
			args: args{
				ctx: context.Background(),
				proxy: &Proxy{
					Id:              1,
					AppId:           "1",
					InterfaceId:     "1",
					ProxyId:         "1",
					ContainerId:     "1",
					Engine:          "InnoDB",
					EngineVersion:   "5.6.7",
					Port:            3306,
					Basedir:         "/var/lib/mysql",
					Ip:              "127.0.0.1",
					XagentPort:      3306,
					Region:          "default",
					LogicZone:       "default",
					Azone:           "default",
					VpcId:           "1",
					SubnetId:        "1",
					Status:          "1",
					DestStatus:      "1",
					Properties:      "",
					ResourceOrderId: "default",
					ResourceId:      "default",
					FloatingIP:      "",
					IPv6:            "",
					RootPassword:    "",
					HostName:        "",
					NodeFixID:       "",
					TempFlags:       "",
					GlobalID:        "",
				},
			},
			want: &Node{
				Id:              1,
				AppId:           "1",
				ClusterId:       "1",
				NodeId:          "1",
				ContainerId:     "1",
				Engine:          "InnoDB",
				EngineVersion:   "5.6.7",
				Port:            3306,
				Basedir:         "/var/lib/mysql",
				Ip:              "127.0.0.1",
				XagentPort:      3306,
				Region:          "default",
				LogicZone:       "default",
				Azone:           "default",
				VpcId:           "1",
				SubnetId:        "1",
				Status:          "1",
				DestStatus:      "1",
				Properties:      "",
				ResourceOrderId: "default",
				ResourceId:      "default",
				FloatingIP:      "",
				IPv6:            "",
				RootPassword:    "",
				HostName:        "",
				NodeFixID:       "",
				TempFlags:       "",
				GlobalID:        "",
			},
		},
		{
			name: "TransProxyToNode nil case",
			args: args{
				ctx:   context.Background(),
				proxy: nil,
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_ = TransProxyToNode(tt.args.ctx, tt.args.proxy)
		})
	}
}
