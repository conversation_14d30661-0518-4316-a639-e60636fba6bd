package x1model

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"icode.baidu.com/baidu/scs/x1-base/model"
)

func TestNamespaceGetAllByAppID(t *testing.T) {

	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").
		WillReturnRows(sqlmock.NewRows([]string{"id", "app_id", "namespace", "bos_bucket", "status",
			"bos_object_prefix", "ingest_keys_num", "ingest_sst_size_mb", "create_at", "update_at"}).
			AddRow(1, "app_id", "namespace", "bos_bucket", "status", "bos_object_prefix",
				0, 0, time.Now(), time.Now()))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	ret, err := NamespaceGetAllByAppID(context.Background(), "app_id")
	if err != nil {
		t.Fatalf("error NamespaceGetAllByAppID: %s", err)
	}
	if len(ret) != 1 {
		t.Fatalf("error NamespaceGetAllByAppID: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").WillReturnError(fmt.Errorf("test error"))

	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	_, err = NamespaceGetAllByAppID(context.Background(), "app_id")
	if err == nil {
		t.Fatalf("error NamespaceGetAllByAppIDAndNamespace: %s", err)
	}
}

func TestNamespaceGetAllByAppIDAndNamespace(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").
		WillReturnRows(sqlmock.NewRows([]string{"id", "app_id", "namespace", "bos_bucket", "status",
			"bos_object_prefix", "ingest_keys_num", "ingest_sst_size_mb", "create_at", "update_at"}).
			AddRow(1, "app_id", "namespace", "bos_bucket", "status", "bos_object_prefix",
				0, 0, time.Now(), time.Now()))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	ret, err := NamespaceGetAllByAppIDAndNamespace(context.Background(), "app_id", "namespace")
	if err != nil {
		t.Fatalf("error NamespaceGetAllByAppIDAndNamespace: %s", err)
	}
	if len(ret) != 1 {
		t.Fatalf("error NamespaceGetAllByAppIDAndNamespace: %s", err)
	}

	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").WillReturnError(fmt.Errorf("test error"))

	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	_, err = NamespaceGetAllByAppIDAndNamespace(context.Background(), "app_id", "namespace")
	if err == nil {
		t.Fatalf("error NamespaceGetAllByAppIDAndNamespace: %s", err)
	}
}

func TestNamespaceGetAllByAppIDAndStatus(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").
		WillReturnRows(sqlmock.NewRows([]string{"id", "app_id", "namespace", "bos_bucket", "status",
			"bos_object_prefix", "ingest_keys_num", "ingest_sst_size_mb", "create_at", "update_at"}).
			AddRow(1, "app_id", "namespace", "bos_bucket", "status", "bos_object_prefix",
				0, 0, time.Now(), time.Now()))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	ret, err := NamespaceGetAllByAppIDAndStatus(context.Background(), "app_id", "status")
	if err != nil {
		t.Fatalf("error TestNamespaceGetAllByAppIDAndStatus: %s", err)
	}
	if len(ret) != 1 {
		t.Fatalf("error TestNamespaceGetAllByAppIDAndStatus: %s", err)
	}

	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").WillReturnError(fmt.Errorf("test error"))

	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	_, err = NamespaceGetAllByAppIDAndStatus(context.Background(), "app_id", "status")
	if err == nil {
		t.Fatalf("error NamespaceGetAllByAppIDAndNamespace: %s", err)
	}
}
func TestNamespaceGetByAppIDAndNamespace(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").
		WillReturnRows(sqlmock.NewRows([]string{"id", "app_id", "namespace", "bos_bucket", "status",
			"bos_object_prefix", "ingest_keys_num", "ingest_sst_size_mb", "create_at", "update_at"}).
			AddRow(1, "app_id", "namespace", "bos_bucket", "status", "bos_object_prefix",
				0, 0, time.Now(), time.Now()))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	ret, err := NamespaceGetByAppIDAndNamespace(context.Background(), "app_id", "namespace")
	if err != nil {
		t.Fatalf("error TestNamespaceGetAllByCond: %s", err)
	}
	if ret.AppID != "app_id" {
		t.Fatalf("error TestNamespaceGetAllByCond: %s", err)
	}

	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").WillReturnError(fmt.Errorf("test error"))

	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	_, err = NamespaceGetByAppIDAndNamespace(context.Background(), "app_id", "namespace")
	if err == nil {
		t.Fatalf("error NamespaceGetAllByAppIDAndNamespace: %s", err)
	}
}

func TestNamespaceGetByCond(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").
		WillReturnRows(sqlmock.NewRows([]string{"id", "app_id", "namespace", "bos_bucket", "status",
			"bos_object_prefix", "ingest_keys_num", "ingest_sst_size_mb", "create_at", "update_at"}).
			AddRow(1, "app_id", "namespace", "bos_bucket", "status", "bos_object_prefix",
				0, 0, time.Now(), time.Now()))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	ret, err := NamespaceGetByCond(context.Background(), "app_id = ?", "app_id")
	if err != nil {
		t.Fatalf("error TestNamespaceGetAllByCond: %s", err)
	}
	if ret.AppID != "app_id" {
		t.Fatalf("error TestNamespaceGetAllByCond: %s", err)
	}

	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").WillReturnError(fmt.Errorf("test error"))

	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	_, err = NamespaceGetByCond(context.Background(), "app_id = ?", "app_id")
	if err == nil {
		t.Fatalf("error NamespaceGetAllByAppIDAndNamespace: %s", err)
	}
}

func TestNamespaceGetAllByCond(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").
		WillReturnRows(sqlmock.NewRows([]string{"id", "app_id", "namespace", "bos_bucket", "status",
			"bos_object_prefix", "ingest_keys_num", "ingest_sst_size_mb", "create_at", "update_at"}).
			AddRow(1, "app_id", "namespace", "bos_bucket", "status", "bos_object_prefix",
				0, 0, time.Now(), time.Now()))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	ret, err := NamespaceGetAllByCond(context.Background(), "app_id = ?", "app_id")
	if err != nil {
		t.Fatalf("error TestNamespaceGetAllByCond: %s", err)
	}
	if len(ret) != 1 {
		t.Fatalf("error TestNamespaceGetAllByCond: %s", err)
	}

	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").WillReturnError(fmt.Errorf("test error"))

	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	_, err = NamespaceGetAllByCond(context.Background(), "app_id = ?", "app_id")
	if err == nil {
		t.Fatalf("error NamespaceGetAllByAppIDAndNamespace: %s", err)
	}
}

func TestSaveNamespaceAll(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `namespace`").
		WithArgs("appid", "namespace", "bos_bucket", sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	data := []*Namespace{
		{
			ID:        0,
			AppID:     "appid",
			Namespace: "namespace",
			BosBucket: "bos_bucket"},
	}
	if err := NamespaceSaveAll(context.Background(), data); err != nil {
		t.Fatalf("error NamespaceSaveAll: %s", err)
	}
}

func TestNamespaceSave(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `namespace`").
		WithArgs("appid", "namespace", "bos_bucket", sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	data := &Namespace{
		ID:        0,
		AppID:     "appid",
		Namespace: "namespace",
		BosBucket: "bos_bucket",
	}
	if err := NamespaceSave(context.Background(), data); err != nil {
		t.Fatalf("error NamespaceSaveAll: %s", err)
	}
}

//func TestGetNamespaceByAppID2(t *testing.T) {
//
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	type args struct {
//		ctx   context.Context
//		appID string
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    []*Namespace
//		wantErr bool
//	}{
//		{"test no appID", args{ctx, "test"}, make([]*Namespace, 0), false},
//		{"test appID", args{ctx, "test_app_id"}, []*Namespace{&Namespace{
//			Id:              1,
//			AppID:           "test_app_id",
//			Namespace:       "test_namespace",
//			BosBucket:       "test_bos_bucket",
//			Status:          "test_status",
//			BosObjectPrefix: "test_bos_object_prefix",
//			CreateAt:        time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC),
//			UpdateAt:        time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC),
//		}, &Namespace{
//			Id:              3,
//			AppID:           "test_app_id",
//			Namespace:       "test_namespace1",
//			BosBucket:       "test_bos_bucket",
//			Status:          "test_status",
//			BosObjectPrefix: "test_bos_object_prefix",
//			CreateAt:        time.Date(2023, 06, 15, 19, 40, 17, 0, time.UTC),
//			UpdateAt:        time.Date(2023, 06, 15, 19, 40, 17, 0, time.UTC),
//		}}, false},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := NamespaceGetAllByAppID(tt.args.ctx, tt.args.appID)
//			if len(got) > 0 {
//				fmt.Printf(" got %+v, err %+v", got[0], err)
//			}
//			if (err != nil) != tt.wantErr {
//				t.Errorf("NamespaceGetAllByAppID() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("NamespaceGetAllByAppID() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//func TestGetNamespaceByAppIdAndNamespaceId2(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	type args struct {
//		ctx       context.Context
//		appID     string
//		namespace string
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    []*Namespace
//		wantErr bool
//	}{
//		{"test no appID", args{ctx, "test_app_id", "1"}, make([]*Namespace, 0), false},
//		{"test appID", args{ctx, "test_app_id", "test_namespace"}, []*Namespace{&Namespace{
//			Id:              1,
//			AppID:           "test_app_id",
//			Namespace:       "test_namespace",
//			BosBucket:       "test_bos_bucket",
//			Status:          "test_status",
//			BosObjectPrefix: "test_bos_object_prefix",
//			CreateAt:        time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC),
//			UpdateAt:        time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC),
//		}}, false},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := NamespaceGetByAppIDAndNamespace(tt.args.ctx, tt.args.appID, tt.args.namespace)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("NamespaceGetAllByAppID() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("NamespaceGetAllByAppID() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//func TestSaveNamespace2(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	type args struct {
//		ctx     context.Context
//		dataPtr []*Namespace
//	}
//	tests := []struct {
//		name    string
//		args    args
//		wantErr bool
//	}{
//		{"test no dataPtr", args{ctx, make([]*Namespace, 0)}, true},
//		{"test dataPtr", args{ctx, []*Namespace{&Namespace{
//			AppID:           "scs-bj-vstcprsvifjd",
//			Namespace:       "test_namespace6",
//			BosBucket:       "test_bos_bucket",
//			Status:          "test_status12",
//			BosObjectPrefix: "test_bos_object_prefix",
//			IngestKeysNum:   1000,
//			IngestSstSizeMB: 100,
//			CreateAt:        time.Now(),
//			UpdateAt:        time.Now(),
//		}}}, false},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if err := NamespaceSaveAll(tt.args.ctx, tt.args.dataPtr); (err != nil) != tt.wantErr {
//
//				t.Errorf("SaveIngestNamespace() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}
//
//func TestCreateOneNamespace2(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	type args struct {
//		ctx     context.Context
//		dataPtr *Namespace
//	}
//	tests := []struct {
//		name    string
//		args    args
//		wantErr bool
//	}{
//		{"test no dataPtr", args{ctx, nil}, true},
//		{"test dataPtr", args{ctx, &Namespace{
//			Id:              5,
//			AppID:           "test_app_id",
//			Namespace:       "test_namespace6",
//			BosBucket:       "test_bos_bucket",
//			Status:          "test_status12",
//			BosObjectPrefix: "test_bos_object_prefix",
//			CreateAt:        time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC),
//			UpdateAt:        time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC),
//		}}, true},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if err := NamespaceSave(tt.args.ctx, tt.args.dataPtr); (err != nil) != tt.wantErr {
//				t.Errorf("CreateOneIngestNamespace() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//		fmt.Println(NamespaceSave(tt.args.ctx, tt.args.dataPtr))
//	}
//}
//
//func TestCreateOneNamespace3(t *testing.T) {
//	fmt.Println(time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC).Local())
//}
