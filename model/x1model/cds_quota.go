// Package x1model
package x1model

import (
	"context"
	"time"
)

const CdsQuotaDDL = `
-- auto-generated definition
  CREATE TABLE cds_quota (
	id int(11) NOT NULL AUTO_INCREMENT,
	app_id varchar(64) NOT NULL DEFAULT '',
	reserve_percentage int(11) NOT NULL DEFAULT 50,
	reserve_max_quota int(11) NOT NULL DEFAULT 80,
	create_at datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
	PRIMARY KEY (id),
	KEY idx_app_id (app_id)
  ) ENGINE=InnoDB AUTO_INCREMENT=178 DEFAULT CHARSET=utf8 COMMENT='cds quota info'

`

// cds quota表
type CdsQuota struct {
	Id                int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	AppID             string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	ReservePercentage int       `gorm:"column:reserve_percentage;NOT NULL" json:"reserve_percentage"`
	ReserveMaxQuota   int       `gorm:"column:reserve_max_quota;NOT NULL" json:"reserve_max_quota"`
	CreateAt          time.Time `gorm:"column:create_at;NOT NULL" json:"create_at"`
}

func (CdsQuota) TableName() string {
	return "cds_quota"
}

func CdsQuotaGetByAppId(ctx context.Context, appId string) ([]*CdsQuota, error) {
	var cdsQuota []*CdsQuota
	err := resource.GetAllByCond(ctx, &cdsQuota, "app_id = ?", appId)
	if err != nil {
		return nil, err
	}
	return cdsQuota, nil
}

func CdsQuotaSave(ctx context.Context, dataPtr []*CdsQuota) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
