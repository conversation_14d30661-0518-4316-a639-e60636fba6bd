/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/06
 * File: proxy_api.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package x1model TODO package function desc
package x1model

import (
	"context"
)

func ProxyGetByProxyId(ctx context.Context, proxyId string) (*Proxy, error) {
	var proxy Proxy
	err := resource.GetOneByUkey(ctx, proxyId, &proxy)
	if err != nil {
		return nil, err
	}
	return &proxy, nil
}

func ProxyGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*Proxy, error) {
	var proxy Proxy
	err := resource.GetOneByCond(ctx, &proxy, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &proxy, nil
}

func ProxyGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*Proxy, error) {
	var proxys []*Proxy
	err := resource.GetAllByCond(ctx, &proxys, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return proxys, nil
}

func ProxysSave(ctx context.Context, dataPtr []*Proxy) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}

func ProxyDeleteMulti(ctx context.Context, proxies []*Proxy) error {
	return resource.DeleteMulti(ctx, proxies)
}
