// Package x1model
package x1model

import (
	"context"
)

// Get
func DeploySetGet(ctx context.Context, userID string, deploySetID string) (*DeploySet, error) {
	var dset DeploySet
	err := resource.GetOneByCond(ctx, &dset, "user_id = ? AND deployset_id = ? AND status = 'inuse'", userID, deploySetID)
	if err != nil {
		return nil, err
	}
	return &dset, nil
}

// List
func DeploySetListByUserID(ctx context.Context, userID string) ([]*DeploySet, error) {
	var dsets []*DeploySet
	err := resource.GetAllByCond(ctx, &dsets, "user_id = ? AND status = 'inuse' order by id desc", userID)
	if err != nil {
		return nil, err
	}
	return dsets, nil
}

// Create or update
func DeploySetSave(ctx context.Context, dataPtr []*DeploySet) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
