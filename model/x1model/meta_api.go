/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/06
 * File: interface_api.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package x1model TODO package function desc
package x1model

import (
	"context"
)

func MetaClusterGetByIMetaClusterId(ctx context.Context, metaClusterId string) (*MetaCluster, error) {
	var metaCluster MetaCluster
	err := resource.GetOneByUkey(ctx, metaClusterId, &metaCluster)
	if err != nil {
		return nil, err
	}
	return &metaCluster, nil
}

func MetaClusterGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*MetaCluster, error) {
	var metaCluster MetaCluster
	err := resource.GetOneByCond(ctx, &metaCluster, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &metaCluster, nil
}

func MetaClustersGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*MetaCluster, error) {
	var metaClusters []*MetaCluster
	err := resource.GetAllByCond(ctx, &metaClusters, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return metaClusters, nil
}

func MetaClustersSave(ctx context.Context, dataPtr []*MetaCluster) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
