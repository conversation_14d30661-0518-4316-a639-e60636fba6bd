package x1model

import "context"

func ConfTplGetAllByCond(ctx context.Context, cond string, vals ...any) ([]*ConfTpl, error) {
	var confTpls []*ConfTpl
	err := resource.GetAllByCondNoTx(ctx, &confTpls, cond, vals...)
	if err != nil {
		return nil, err
	}
	return confTpls, nil
}

func ConfTplSave(ctx context.Context, dataPtr []*ConfTpl) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
