package x1model

import (
	"context"
	"database/sql/driver"
	"github.com/DATA-DOG/go-sqlmock"
	"testing"
	"time"
)

func deploySetHeaders() []string {
	return []string{
		"id",
		"name",
		"deployset_id",
		"user_id",
		"status",
		"strategy",
		"concurrency",
		"description",
		"create_at",
		"update_at",
	}
}

func deploySetData() [][]driver.Value {
	return [][]driver.Value{
		{
			1,
			"name_xx",
			"dset-2b5d91d8-42ab-4ba4-a8d1-bea16437d10c",
			"user_xx",
			"inuse",
			"HOST_HA",
			1,
			"desc_xx",
			time.Now(),
			time.Now(),
		},
	}
}

func TestDeploySetGet(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	rows := mock.NewRows(deploySetHeaders()).AddRow(deploySetData()[0]...)
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	mock.ExpectCommit()
	dset, err := DeploySetGet(context.Background(), "user_xx", "dset-2b5d91d8-42ab-4ba4-a8d1-bea16437d10c")
	if err != nil {
		t.Errorf("DeploySetGet() error = %v", err)
	}
	if dset == nil {
		t.Errorf("DeploySetGet() get data failed")
	}
}

func TestDeploySetListByUserID(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	rows := mock.NewRows(deploySetHeaders()).AddRow(deploySetData()[0]...)
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	mock.ExpectCommit()
	dsets, err := DeploySetListByUserID(context.Background(), "user_xx")
	if err != nil {
		t.Errorf("DeploySetListByUserID() error = %v", err)
	}
	if len(dsets) != 1 {
		t.Errorf("DeploySetListByUserID() got = %v, want = 1", len(dsets))
	}
}

func TestDeploySetSave(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO `deployset` .*").WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	deploySet := &DeploySet{
		ID:          1,
		Name:        "name_xx",
		DeploySetID: "dest-xx",
		UserID:      "xxx",
		Status:      "inuse",
		Strategy:    "HOST_HA",
		Concurrency: 1,
		Desc:        "desc_xx",
		CreateAt:    time.Now(),
		UpdateAt:    time.Now(),
	}
	err := DeploySetSave(context.Background(), []*DeploySet{deploySet})
	if err != nil {
		t.Errorf("DeploySave() error = %v", err)
	}
}
