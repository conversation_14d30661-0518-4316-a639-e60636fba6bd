/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/03/08
 * File: exp_api.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package x1model TODO package function desc
package x1model

import (
	"context"
)

type RuleItem struct {
	Key      string        `json:"key"`
	Value    []interface{} `json:"value"`
	Relation string        `json:"relation"`
}

func ExpGetByStrategyId(ctx context.Context, strategyId string) (*Exp, error) {
	var exp Exp
	err := resource.GetOneByUkey(ctx, strategyId, &exp)
	if err != nil {
		return nil, err
	}
	return &exp, nil
}

func ExpGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*Exp, error) {
	var exp Exp
	err := resource.GetOneByCond(ctx, &exp, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &exp, nil
}

func ExpGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*Exp, error) {
	var explist []*Exp
	err := resource.GetAllByCond(ctx, &explist, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return explist, nil
}

func ExpsSave(ctx context.Context, expList []*Exp) error {
	return resource.FullSaveAssociationsSave(ctx, expList)
}

func FlushExpRules(ctx context.Context, exp *Exp) error {
	return resource.DeleteMulti(ctx, exp.Rules)
}
