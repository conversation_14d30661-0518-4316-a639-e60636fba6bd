package x1model

import (
	"context"
	"strconv"
	"time"

	"gorm.io/gorm"

	"icode.baidu.com/baidu/scs/x1-base/task/iface"
)

type Namespace struct {
	ID              int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	AppID           string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	Namespace       string    `gorm:"column:namespace;NOT NULL" json:"namespace"`
	BosBucket       string    `gorm:"column:bos_bucket;NOT NULL" json:"bos_bucket"`
	MirrorBosBucket string    `gorm:"column:mirror_bos_bucket;NOT NULL" json:"mirror_bos_bucket"`
	Status          string    `gorm:"column:status;NOT NULL" json:"status"`
	BosObjectPrefix string    `gorm:"column:bos_object_prefix;NOT NULL" json:"bos_object_prefix"`
	IngestKeysNum   int64     `gorm:"column:ingest_keys_num;NOT NULL" json:"ingest_keys_num"`
	IngestSstSizeMB int64     `gorm:"column:ingest_sst_size_mb;NOT NULL" json:"ingest_sst_size_mb"`
	CreateAt        time.Time `gorm:"column:create_at;NOT NULL" json:"create_at"`
	UpdateAt        time.Time `gorm:"column:update_at;NOT NULL" json:"update_at"`
}

func (Namespace) TableName() string {
	return "namespace"
}

// NamespaceGetAllByAppID 根据appId获取所有的namespace
func NamespaceGetAllByAppID(ctx context.Context, appID string) ([]*Namespace, error) {
	var namespace []*Namespace
	err := resource.GetAllByCond(ctx, &namespace, "app_id = ?", appID)
	if err != nil {
		return nil, err
	}
	return namespace, nil
}

// NamespaceGetByAppIDAndNamespace 根据appId和namespace获取1个namespace
func NamespaceGetByAppIDAndNamespace(ctx context.Context, appID string, namespaceName string) (*Namespace, error) {
	var namespace Namespace
	err := resource.GetOneByCond(ctx, &namespace, "app_id = ? and namespace = ?", appID, namespaceName)
	if err != nil {
		return nil, err
	}
	return &namespace, nil
}

// NamespaceGetAllByAppIDAndNamespace 根据appId和namespace获取所有namespace
func NamespaceGetAllByAppIDAndNamespace(ctx context.Context, appID string, namespaceName string) ([]*Namespace, error) {
	var namespace []*Namespace
	err := resource.GetAllByCond(ctx, &namespace, "app_id = ? and namespace = ?", appID, namespaceName)
	if err != nil {
		return nil, err
	}
	return namespace, nil
}

// NamespaceGetAllByAppIDAndStatus 根据appId和状态获取所有namespace
func NamespaceGetAllByAppIDAndStatus(ctx context.Context, appID string, status string) ([]*Namespace, error) {
	var namespace []*Namespace
	err := resource.GetAllByCond(ctx, &namespace, "app_id = ? and status = ?", appID, status)
	if err != nil {
		return nil, err
	}
	return namespace, nil
}

// NamespaceGetByCond
func NamespaceGetByCond(ctx context.Context, fmtCond string, vals ...any) (*Namespace, error) {
	var namespace Namespace
	err := resource.GetOneByCond(ctx, &namespace, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &namespace, nil
}

// NamespaceGetAllByCond
func NamespaceGetAllByCond(ctx context.Context, fmtCond string, vals ...any) ([]*Namespace, error) {
	var namespaces []*Namespace
	err := resource.GetAllByCond(ctx, &namespaces, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return namespaces, nil
}

// NamespaceSaveAll 保存所有的namespace
func NamespaceSaveAll(ctx context.Context, dataPtr []*Namespace) error {
	// 遇到唯一键是更新而不是报错
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}

// NamespaceSave 保存一个namespace
func NamespaceSave(ctx context.Context, dataPtr *Namespace) error {
	return resource.CreateOne(ctx, dataPtr)
}

// NamespaceDeleteOne 删除1个namespace
func NamespaceDeleteOne(ctx context.Context, nsID int64) error {
	var param *Namespace = &Namespace{}
	return resource.DeleteOneByUkey(ctx, strconv.FormatInt(nsID, 10), param)
}

// NamespaceAndTaskSave 保存namespace、一个namespaceTask和一个task，构成事务
func NamespaceAndTaskSave(ctx context.Context, reuseNs []*Namespace, newNs *Namespace, nsTask *NamespaceTask, task *iface.Task) error {
	db := resource.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: resource.GormLoggerCommon})
	return db.Transaction(func(tx *gorm.DB) error {
		if reuseNs != nil {
			if err := tx.Save(reuseNs).Error; err != nil {
				return err
			}
		}
		if newNs != nil {
			if err := tx.Create(newNs).Error; err != nil {
				return err
			}
		}
		// 可加可不加
		if err := tx.Create(nsTask).Error; err != nil {
			return err
		}
		if err := tx.WithContext(ctx).Create(task).Error; err != nil {
			return err
		}
		return nil
	})
}
