package x1model

import (
	"context"
	"database/sql/driver"
	"github.com/DATA-DOG/go-sqlmock"
	"testing"
	"time"
)

func confTplHeaders() []string {
	return []string{"id", "tpl_id", "created_at", "updated_at", "status", "desc"}
}

func confTplItemHeaders() []string {
	return []string{"id", "tpl_id", "deploy_path", "render_type", "created_at", "updated_at", "content"}
}

func confTplValues() [][]driver.Value {
	return [][]driver.Value{
		{1, "conf-tpl-000", time.Now(), time.Now(), "inuse", "desc"},
		{2, "conf-tpl-001", time.Now(), time.Now(), "inuse", "desc"},
	}
}

func confTplItemValues() [][]driver.Value {
	return [][]driver.Value{
		{1, "conf-tpl-000", "/tmp", "text", time.Now(), time.Now(), "content"},
		{2, "conf-tpl-001", "/tmp", "text", time.Now(), time.Now(), "content"},
	}
}

func TestConfTplGetAllByCond(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	rows := mock.NewRows(confTplHeaders()).AddRow(confTplValues()[0]...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	confTpls, err := ConfTplGetAllByCond(context.Background(), "tpl_id = ?", "conf-tpl-000")
	if err != nil {
		t.Errorf("ConfTplGetAllByCond() error = %v", err)
	}
	if len(confTpls) != 1 {
		t.Errorf("ConfTplGetAllByCond() got = %v, want = 1", len(confTpls))
	}
}

func TestConfTplSave(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO `conf_tpl` .*").WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectExec("^INSERT INTO `conf_tpl_item` .*").WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	confTpl := &ConfTpl{
		Id:        1,
		TplID:     "conf-tpl-000",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Status:    "inuse",
		Desc:      "desc",
		Items: []*ConfTplItem{
			{
				Id:         1,
				TplID:      "conf-tpl-000",
				DeployPath: "/tmp",
				RenderType: "text",
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
				Content:    "content",
			},
		},
	}
	err := ConfTplSave(context.Background(), []*ConfTpl{confTpl})
	if err != nil {
		t.Errorf("ConfTplSave() error = %v", err)
	}
}
