/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/03
 * File: application_api.go
 */

/*
 * DESCRIPTION
 *   application操作方法
 */

// Package x1model
package x1model

import (
	"context"
	"strings"
)

func ApplicationGetByAppId(ctx context.Context, appId string) (*Application, error) {
	var application Application
	err := resource.GetOneByUkey(ctx, appId, &application)
	if err != nil {
		return nil, err
	}
	return &application, nil
}

func ApplicationGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*Application, error) {
	var application Application
	err := resource.GetOneByCond(ctx, &application, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &application, nil
}

func ApplicationGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*Application, error) {
	var applications []*Application
	err := resource.GetAllByCond(ctx, &applications, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return applications, nil
}

func ApplicationsSave(ctx context.Context, dataPtr []*Application) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}

func (app *Application) SetAPIFlag(flag string) {
	if app.ApiFlag == "" {
		app.ApiFlag = flag
		return
	}
	appFlagSp := strings.Split(app.ApiFlag, ",")
	for _, v := range appFlagSp {
		if v == flag {
			return
		}
	}
	app.ApiFlag = app.ApiFlag + "," + flag
}

func (app *Application) IsAPIFlagSet(flag string) bool {
	if app.ApiFlag == "" {
		return false
	}
	appFlagSp := strings.Split(app.ApiFlag, ",")
	for _, v := range appFlagSp {
		if v == flag || v == "all" {
			return true
		}
	}
	return false
}
