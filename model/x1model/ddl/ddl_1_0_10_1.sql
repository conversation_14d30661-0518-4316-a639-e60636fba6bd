use bce_scs_x1_task;

alter table `application`
    add column `mcpack_port` int(11) NOT NULL DEFAULT '0',
    add column `inner_port` int(11) NOT NULL DEFAULT '0',
    add column `ports_info` varchar(1024) NOT NULL DEFAULT '',
    add column `app_group_id` varchar(64) NOT NULL DEFAULT '',
    add column `app_group_seq_id` int(11) NOT NULL DEFAULT '0',
    add column `local_metaserver` varchar(64) NOT NULL DEFAULT '',
    add column `global_metaserver` varchar(64) NOT NULL DEFAULT '',
    add column `region` varchar(32) NOT NULL DEFAULT '',
    add column `bns_service` varchar(256) NOT NULL DEFAULT '';

alter table `blb` add column `ro_group_id` varchar(64) NOT NULL DEFAULT '';

alter table `cluster` add column `global_id` varchar(64) COLLATE utf8_bin NOT NULL DEFAULT '',
                      add column `global_seq_id` int(11) NOT NULL DEFAULT '0';

alter table `interface` add column `mcpack_port` varchar(64) COLLATE utf8_bin NOT NULL DEFAULT '';

alter table `node` add column `global_id` varchar(64) COLLATE utf8_bin NOT NULL DEFAULT '',
                   add column `global_seq_id` int(11) NOT NULL DEFAULT '0';

alter table `proxy` add column `mcpack_port` varchar(64) NOT NULL DEFAULT '',
                    add column `global_id` varchar(64) NOT NULL DEFAULT '',
                    add column `global_seq_id` int(11) NOT NULL DEFAULT '0';

alter table `tasks` add column `updated_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
                    add column `version` int(11) NOT NULL DEFAULT '0';

CREATE TABLE `ro_node` (
                           `id` bigint(20) NOT NULL AUTO_INCREMENT,
                           `app_id` varchar(64) NOT NULL DEFAULT '',
                           `cluster_id` varchar(64) NOT NULL DEFAULT '',
                           `node_id` varchar(64) NOT NULL DEFAULT '',
                           `container_id` varchar(256) NOT NULL DEFAULT '',
                           `engine` varchar(32) NOT NULL DEFAULT '',
                           `engine_version` varchar(64) NOT NULL DEFAULT '',
                           `port` int(11) NOT NULL DEFAULT '0',
                           `basedir` varchar(256) NOT NULL DEFAULT '',
                           `datadir` varchar(256) NOT NULL DEFAULT '',
                           `innodb_buffer_size` int(11) NOT NULL DEFAULT '0',
                           `ip` varchar(64) NOT NULL DEFAULT '',
                           `xagent_port` int(11) NOT NULL DEFAULT '0',
                           `region` varchar(32) NOT NULL DEFAULT '',
                           `logic_zone` varchar(32) NOT NULL DEFAULT '',
                           `azone` varchar(32) NOT NULL DEFAULT '',
                           `vpc_id` varchar(64) NOT NULL DEFAULT '',
                           `subnet_id` varchar(64) NOT NULL DEFAULT '',
                           `pool` varchar(256) NOT NULL DEFAULT '',
                           `tags` varchar(256) NOT NULL DEFAULT '',
                           `role` varchar(32) NOT NULL DEFAULT '',
                           `dest_role` varchar(32) NOT NULL DEFAULT '',
                           `status` varchar(32) NOT NULL DEFAULT '',
                           `dest_status` varchar(32) NOT NULL DEFAULT '',
                           `task_id` int(11) NOT NULL DEFAULT '0',
                           `properties` varchar(1024) NOT NULL DEFAULT '',
                           `resource_order_id` varchar(64) NOT NULL DEFAULT '',
                           `resource_id` varchar(64) NOT NULL DEFAULT '',
                           `floating_ip` varchar(64) NOT NULL DEFAULT '',
                           `ipv6` varchar(64) NOT NULL DEFAULT '',
                           `root_password` varchar(256) NOT NULL DEFAULT '',
                           `node_short_id` bigint(20) NOT NULL DEFAULT '0',
                           `host_name` varchar(256) NOT NULL DEFAULT '',
                           `node_fix_id` varchar(256) NOT NULL DEFAULT '',
                           `temp_flags` varchar(512) NOT NULL DEFAULT '',
                           `ro_group_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'readonly group id',
                           `ro_group_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'readonly group status',
                           `ro_group_weight` int(11) NOT NULL DEFAULT '0' COMMENT 'readonly group weight',
                           `global_id` varchar(64) NOT NULL DEFAULT '',
                           `global_seq_id` int(11) NOT NULL DEFAULT '0',
                           PRIMARY KEY (`id`),
                           UNIQUE KEY `uniq_node_node_id` (`node_id`),
                           KEY `idx_node_app_id` (`app_id`),
                           KEY `idx_node_cluster_id` (`cluster_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ro node infos';



