use bce_scs_x1_task;

ALTER TABLE `proxy` ADD COLUMN `stat_port` int(11) NOT NULL DEFAULT '0';

CREATE TABLE `meta_cluster` (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                `meta_cluster_id` varchar(64) NOT NULL DEFAULT '',
                                `name` varchar(256) NOT NULL DEFAULT '',
                                `desc` varchar(256) NOT NULL DEFAULT '',
                                `status` varchar(32) NOT NULL DEFAULT '',
                                `type` varchar(32) NOT NULL DEFAULT '',
                                `user_id` varchar(64) NOT NULL DEFAULT '',
                                `engine` varchar(32) NOT NULL DEFAULT '',
                                `region` varchar(32) NOT NULL DEFAULT '',
                                `engine_version` varchar(32) NOT NULL DEFAULT '',
                                `entrance` varchar(64) NOT NULL DEFAULT '',
                                `cur_master` varchar(64) NOT NULL DEFAULT '',
                                `quorum` int(11) NOT NULL DEFAULT '0',
                                `created_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
                                `updated_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
                                `password` varchar(256) NOT NULL DEFAULT '',
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `meta_cluster_id` (`meta_cluster_id`),
                                KEY `idx_meta_cluster_status` (`status`),
                                KEY `idx_meta_cluster_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1006 DEFAULT CHARSET=utf8;

CREATE TABLE `meta_node` (
                             `id` bigint(20) NOT NULL AUTO_INCREMENT,
                             `meta_node_id` varchar(64) NOT NULL DEFAULT '',
                             `meta_cluster_id` varchar(64) NOT NULL DEFAULT '',
                             `status` varchar(32) NOT NULL DEFAULT '',
                             `role` varchar(32) NOT NULL DEFAULT '',
                             `engine` varchar(32) NOT NULL DEFAULT '',
                             `engine_version` varchar(32) NOT NULL DEFAULT '',
                             `port` int(11) NOT NULL DEFAULT '0',
                             `region` varchar(32) NOT NULL DEFAULT '',
                             `logical_zone` varchar(32) NOT NULL DEFAULT '',
                             `azone` varchar(32) NOT NULL DEFAULT '',
                             `vpc_id` varchar(64) NOT NULL DEFAULT '',
                             `subnet_id` varchar(64) NOT NULL DEFAULT '',
                             `base_dir` varchar(64) NOT NULL DEFAULT '',
                             `data_dir` varchar(64) NOT NULL DEFAULT '',
                             `ip` varchar(64) NOT NULL DEFAULT '',
                             `floating_ip` varchar(64) NOT NULL DEFAULT '',
                             `ipv6` varchar(64) NOT NULL DEFAULT '',
                             `resource_order_id` varchar(64) NOT NULL DEFAULT '',
                             `resource_id` varchar(64) NOT NULL DEFAULT '',
                             `created_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
                             `updated_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
                             PRIMARY KEY (`id`),
                             UNIQUE KEY `meta_node_id` (`meta_node_id`),
                             KEY `idx_meta_node_meta_cluster_id` (`meta_cluster_id`),
                             KEY `idx_meta_node_status` (`status`),
                             KEY `idx_meta_node_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1006 DEFAULT CHARSET=utf8;