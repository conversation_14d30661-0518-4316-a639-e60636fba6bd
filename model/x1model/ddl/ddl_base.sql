use bce_scs_x1_task;

CREATE TABLE `application` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_id` varchar(64) NOT NULL DEFAULT '',
  `app_name` varchar(256) NOT NULL DEFAULT '',
  `product` varchar(32) NOT NULL DEFAULT '',
  `type` varchar(32) NOT NULL DEFAULT '',
  `app_mode` varchar(32) NOT NULL DEFAULT '',
  `pool` varchar(256) NOT NULL DEFAULT '',
  `port` int(11) NOT NULL DEFAULT '0',
  `azone` varchar(32) NOT NULL DEFAULT '',
  `rzone` varchar(32) NOT NULL DEFAULT '',
  `vpc_id` varchar(64) NOT NULL DEFAULT '',
  `user_id` varchar(64) NOT NULL DEFAULT '',
  `zk_host` varchar(256) NOT NULL DEFAULT '',
  `replicas` varchar(1024) NOT NULL DEFAULT '',
  `status` varchar(32) NOT NULL DEFAULT '',
  `properties` varchar(1024) NOT NULL DEFAULT '',
  `ip_type` varchar(32) NOT NULL DEFAULT '',
  `security_group_id` varchar(64) NOT NULL DEFAULT '',
  `internal_security_group_id` varchar(64) NOT NULL DEFAULT '',
  `image_id` varchar(256) NOT NULL DEFAULT '',
  `deploy_set_ids` varchar(1024) NOT NULL DEFAULT '',
  `blb_subnet_id` varchar(64) NOT NULL DEFAULT '',
  `app_short_id` bigint(20) NOT NULL DEFAULT '0',
  `user_short_id` bigint(20) NOT NULL DEFAULT '0',
  `domain` varchar(256) NOT NULL DEFAULT '',
  `elb_pnetip` varchar(64) NOT NULL DEFAULT '',
  `elb_ipv6` varchar(64) NOT NULL DEFAULT '',
  `create_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `update_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `delete_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `semi_status` varchar(32) NOT NULL DEFAULT '',
  `clone_data_access` varchar(256) NOT NULL DEFAULT '',
  `dest_replicas` varchar(1024) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_application_app_id` (`app_id`)
) ENGINE=InnoDB AUTO_INCREMENT=161 DEFAULT CHARSET=utf8 COMMENT='application info';

CREATE TABLE `backup` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_id` varchar(64) NOT NULL DEFAULT '',
  `backup_id` varchar(64) NOT NULL DEFAULT '',
  `create_at` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `update_at` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `end_at` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `expire_at` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `status` varchar(32) NOT NULL DEFAULT '',
  `type` varchar(32) NOT NULL DEFAULT '',
  `comment` varchar(1024) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_backup_backup_id` (`backup_id`),
  KEY `idx_backup_app_id` (`app_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1255 DEFAULT CHARSET=utf8 COMMENT='backup info';

CREATE TABLE `backup_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_id` varchar(64) NOT NULL DEFAULT '',
  `cluster_id` varchar(64) NOT NULL DEFAULT '',
  `node_id` varchar(64) NOT NULL DEFAULT '',
  `backup_id` varchar(64) NOT NULL DEFAULT '',
  `access` varchar(512) NOT NULL DEFAULT '',
  `size` bigint(20) NOT NULL DEFAULT '0',
  `create_at` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `update_at` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `status` varchar(32) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `idx_backup_app_id` (`app_id`),
  KEY `idx_backup_cluster_id` (`cluster_id`),
  KEY `idx_backup_node_id` (`node_id`),
  KEY `idx_backup_backup_id` (`backup_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11255 DEFAULT CHARSET=utf8 COMMENT='backup info';

CREATE TABLE `blb` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_id` varchar(64) NOT NULL DEFAULT '',
  `name` varchar(256) NOT NULL DEFAULT '',
  `type` varchar(32) NOT NULL DEFAULT '',
  `vpc_id` varchar(64) NOT NULL DEFAULT '',
  `subnet_id` varchar(64) NOT NULL DEFAULT '',
  `bgw_group_id` varchar(64) NOT NULL DEFAULT '',
  `bgw_group_exclusive` int(11) NOT NULL DEFAULT '0',
  `bgw_group_mode` varchar(32) DEFAULT '',
  `master_az` varchar(32) DEFAULT '',
  `slave_az` varchar(32) DEFAULT '',
  `status` varchar(32) NOT NULL DEFAULT '',
  `blb_id` varchar(64) NOT NULL DEFAULT '',
  `vip` varchar(64) NOT NULL DEFAULT '',
  `ovip` varchar(64) NOT NULL DEFAULT '',
  `ipv6` varchar(64) NOT NULL DEFAULT '',
  `create_at` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `update_at` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `ip_type` varchar(32) DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `idx_blb_app_id` (`app_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1187 DEFAULT CHARSET=utf8 COMMENT='blb info';

CREATE TABLE `cluster` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cluster_id` varchar(64) NOT NULL DEFAULT '',
  `app_id` varchar(64) NOT NULL DEFAULT '',
  `engine` varchar(32) NOT NULL DEFAULT '',
  `engine_version` varchar(64) NOT NULL DEFAULT '',
  `engine_minor_version` varchar(64) NOT NULL DEFAULT '',
  `port` int(11) NOT NULL DEFAULT '0',
  `xagent_sync_port` int(11) NOT NULL DEFAULT '0',
  `node_relation` varchar(64) NOT NULL DEFAULT '',
  `mem_size` bigint(20) NOT NULL DEFAULT '0',
  `actual_mem_size` bigint(20) NOT NULL DEFAULT '0',
  `disk_size` bigint(20) NOT NULL DEFAULT '0',
  `actual_disk_size` bigint(20) NOT NULL DEFAULT '0',
  `disk_used` bigint(20) NOT NULL DEFAULT '0',
  `cpu` int(11) NOT NULL DEFAULT '0',
  `actual_cpu` int(11) NOT NULL DEFAULT '0',
  `status` varchar(64) NOT NULL DEFAULT '',
  `dest_status` varchar(64) NOT NULL DEFAULT '',
  `remark` text,
  `properties` varchar(1024) NOT NULL DEFAULT '',
  `store_type` varchar(32) NOT NULL DEFAULT '',
  `available_volume` int(11) NOT NULL DEFAULT '0',
  `sys_disk_size` bigint(20) NOT NULL DEFAULT '0',
  `sys_disk_used` bigint(20) NOT NULL DEFAULT '0',
  `spec` varchar(64) NOT NULL DEFAULT '',
  `dest_spec` varchar(64) NOT NULL DEFAULT '',
  `cluster_short_id` bigint(11) NOT NULL DEFAULT '0',
  `expire_time` datetime DEFAULT '0000-00-00 00:00:00',
  `create_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `update_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `delete_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `bns_path` varchar(256) NOT NULL DEFAULT '',
  `bns` varchar(256) NOT NULL DEFAULT '',
  `max_node_index` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_cluster_cluster_id` (`cluster_id`),
  KEY `idx_cluster_app_id` (`app_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1161 DEFAULT CHARSET=utf8 COMMENT='cluster info';

CREATE TABLE `config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_id` varchar(64) NOT NULL DEFAULT '',
  `name` varchar(256) NOT NULL DEFAULT '',
  `type` varchar(32) NOT NULL DEFAULT '',
  `value` longtext,
  PRIMARY KEY (`id`),
  KEY `idx_config_app_id` (`app_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1101 DEFAULT CHARSET=utf8 COMMENT='config info';

CREATE TABLE `entity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(256) NOT NULL DEFAULT '',
  `app_id` varchar(64) NOT NULL DEFAULT '',
  `parent_name` varchar(256) NOT NULL DEFAULT '',
  `type` varchar(128) NOT NULL DEFAULT '',
  `status` varchar(32) NOT NULL DEFAULT '',
  `remark` varchar(256) NOT NULL DEFAULT '',
  `etag` int(11) NOT NULL DEFAULT '0',
  `create_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `update_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `properties` longtext,
  `charset` varchar(64) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `idx_entity_app_id` (`app_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1101 DEFAULT CHARSET=utf8 COMMENT='entity info';

CREATE TABLE `exp` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `strategy_id` varchar(64) NOT NULL DEFAULT '',
  `default_flag` varchar(64) NOT NULL DEFAULT '',
  `create_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `update_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `delete_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_exp_strategy_id` (`strategy_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1101 DEFAULT CHARSET=utf8 COMMENT='exp info';

CREATE TABLE `exp_rules` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `strategy_id` varchar(64) NOT NULL DEFAULT '',
  `ranking` int(11) NOT NULL DEFAULT '0',
  `rules` longtext,
  `flag` varchar(64) NOT NULL DEFAULT '',
  `create_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `update_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `delete_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`id`),
  KEY `idx_exp_rules_strategy_id` (`strategy_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1101 DEFAULT CHARSET=utf8 COMMENT='exp rules';

CREATE TABLE `interface` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `interface_id` varchar(64) NOT NULL DEFAULT '',
  `access_type` varchar(32) NOT NULL DEFAULT '',
  `engine` varchar(32) NOT NULL DEFAULT '',
  `engine_version` varchar(64) NOT NULL DEFAULT '',
  `port` int(11) NOT NULL DEFAULT '0',
  `app_id` varchar(64) NOT NULL DEFAULT '',
  `proxy_relation` varchar(64) NOT NULL DEFAULT '',
  `mem_size` bigint(20) NOT NULL DEFAULT '0',
  `actual_mem_size` bigint(20) NOT NULL DEFAULT '0',
  `disk_size` bigint(20) NOT NULL DEFAULT '0',
  `actual_disk_size` bigint(20) NOT NULL DEFAULT '0',
  `disk_used` bigint(20) NOT NULL DEFAULT '0',
  `cpu` int(11) NOT NULL DEFAULT '0',
  `actual_cpu` int(11) NOT NULL DEFAULT '0',
  `status` varchar(32) NOT NULL DEFAULT '',
  `remark` text,
  `properties` varchar(1024) NOT NULL DEFAULT '',
  `store_type` varchar(32) NOT NULL DEFAULT '',
  `available_volume` int(10) NOT NULL DEFAULT '0',
  `sys_disk_size` bigint(20) NOT NULL DEFAULT '0',
  `sys_disk_used` bigint(20) NOT NULL DEFAULT '0',
  `spec` varchar(64) NOT NULL DEFAULT '',
  `dest_spec` varchar(64) NOT NULL DEFAULT '',
  `create_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `update_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `delete_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `bns_path` varchar(256) NOT NULL DEFAULT '',
  `bns` varchar(256) NOT NULL DEFAULT '',
  `access` varchar(1024) NOT NULL DEFAULT '',
  `resource_id` varchar(64) NOT NULL DEFAULT '',
  `interface_short_id` bigint(20) NOT NULL DEFAULT '0',
  `max_node_index` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_interface_interface_id` (`interface_id`),
  KEY `idx_interface_app_id` (`app_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1101 DEFAULT CHARSET=utf8 COMMENT='interface';

CREATE TABLE `node` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_id` varchar(64) NOT NULL DEFAULT '',
  `cluster_id` varchar(64) NOT NULL DEFAULT '',
  `node_id` varchar(64) NOT NULL DEFAULT '',
  `container_id` varchar(256) NOT NULL DEFAULT '',
  `engine` varchar(32) NOT NULL DEFAULT '',
  `engine_version` varchar(64) NOT NULL DEFAULT '',
  `port` int(11) NOT NULL DEFAULT '0',
  `basedir` varchar(256) NOT NULL DEFAULT '',
  `datadir` varchar(256) NOT NULL DEFAULT '',
  `innodb_buffer_size` int(11) NOT NULL DEFAULT '0',
  `ip` varchar(64) NOT NULL DEFAULT '',
  `xagent_port` int(11) NOT NULL DEFAULT '0',
  `region` varchar(32) NOT NULL DEFAULT '',
  `logic_zone` varchar(32) NOT NULL DEFAULT '',
  `azone` varchar(32) NOT NULL DEFAULT '',
  `vpc_id` varchar(64) NOT NULL DEFAULT '',
  `subnet_id` varchar(64) NOT NULL DEFAULT '',
  `pool` varchar(256) NOT NULL DEFAULT '',
  `tags` varchar(256) NOT NULL DEFAULT '',
  `role` varchar(32) NOT NULL DEFAULT '',
  `dest_role` varchar(32) NOT NULL DEFAULT '',
  `status` varchar(32) NOT NULL DEFAULT '',
  `dest_status` varchar(32) NOT NULL DEFAULT '',
  `task_id` int(11) NOT NULL DEFAULT '0',
  `properties` varchar(1024) NOT NULL DEFAULT '',
  `resource_order_id` varchar(64) NOT NULL DEFAULT '',
  `resource_id` varchar(64) NOT NULL DEFAULT '',
  `floating_ip` varchar(64) NOT NULL DEFAULT '',
  `ipv6` varchar(64) NOT NULL DEFAULT '',
  `root_password` varchar(256) NOT NULL DEFAULT '',
  `node_short_id` bigint(20) NOT NULL DEFAULT '0',
  `host_name` varchar(256) NOT NULL DEFAULT '',
  `node_fix_id` varchar(256) NOT NULL DEFAULT '',
  `temp_flags` varchar(512) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_node_node_id` (`node_id`),
  KEY `idx_node_app_id` (`app_id`),
  KEY `idx_node_cluster_id` (`cluster_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1238 DEFAULT CHARSET=utf8 COMMENT='node infos';

CREATE TABLE `proxy` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `proxy_id` varchar(64) NOT NULL DEFAULT '',
  `ip` varchar(64) NOT NULL DEFAULT '',
  `port` int(11) NOT NULL DEFAULT '0',
  `region` varchar(32) NOT NULL DEFAULT '',
  `logic_zone` varchar(32) NOT NULL DEFAULT '',
  `azone` varchar(32) NOT NULL DEFAULT '',
  `vpc_id` varchar(64) NOT NULL DEFAULT '',
  `xagent_port` int(11) NOT NULL DEFAULT '0',
  `app_id` varchar(64) NOT NULL DEFAULT '',
  `properties` varchar(1024) NOT NULL DEFAULT '',
  `basedir` varchar(256) NOT NULL DEFAULT '',
  `status` varchar(32) NOT NULL DEFAULT '',
  `dest_status` varchar(32) NOT NULL DEFAULT '',
  `container_id` varchar(256) NOT NULL DEFAULT '',
  `interface_id` varchar(64) NOT NULL DEFAULT '',
  `subnet_id` varchar(64) NOT NULL DEFAULT '',
  `resource_order_id` varchar(64) NOT NULL DEFAULT '',
  `resource_id` varchar(64) NOT NULL DEFAULT '',
  `floating_ip` varchar(64) NOT NULL DEFAULT '',
  `ipv6` varchar(64) NOT NULL DEFAULT '',
  `root_password` varchar(256) NOT NULL DEFAULT '',
  `engine` varchar(32) NOT NULL DEFAULT '',
  `engine_version` varchar(64) NOT NULL DEFAULT '',
  `proxy_short_id` bigint(20) NOT NULL DEFAULT '0',
  `host_name` varchar(256) NOT NULL DEFAULT '',
  `node_fix_id` varchar(256) NOT NULL DEFAULT '',
  `temp_flags` varchar(512) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_proxy_proxy_id` (`proxy_id`),
  KEY `idx_proxy_app_id` (`app_id`),
  KEY `idx_proxy_interface_id` (`interface_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1101 DEFAULT CHARSET=utf8 COMMENT='proxy infos';

CREATE TABLE `redis_acl` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app_id` varchar(64) NOT NULL DEFAULT '',
  `account_name` varchar(256) NOT NULL DEFAULT '',
  `create_at` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `update_at` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `version` int(11) NOT NULL DEFAULT '0',
  `engine` varchar(32) NOT NULL DEFAULT '',
  `password` varchar(256) NOT NULL DEFAULT '',
  `allowed_cmds` varchar(1024) NOT NULL DEFAULT '',
  `allowed_sub_cmds` varchar(1024) NOT NULL DEFAULT '',
  `key_patterns` varchar(1024) NOT NULL DEFAULT '',
  `properties` varchar(1024) NOT NULL DEFAULT '',
  `status` varchar(32) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `idx_redis_acl_app_id` (`app_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1101 DEFAULT CHARSET=utf8 COMMENT='redis acl infos';

CREATE TABLE `tasks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deleted_at` int(11) DEFAULT '0',
  `task_id` varchar(64) NOT NULL DEFAULT '',
  `task_batch_id` varchar(256) NOT NULL DEFAULT '',
  `last_task_batch_id` varchar(256) NOT NULL DEFAULT '',
  `work_flow` varchar(64) NOT NULL DEFAULT '',
  `entity` varchar(64) NOT NULL DEFAULT '',
  `entity_dim` varchar(64) NOT NULL DEFAULT '',
  `status` varchar(64) NOT NULL DEFAULT '',
  `step` varchar(256) NOT NULL,
  `step_start_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `step_deadline` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `step_teu_count` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `schedule` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `cron` varchar(64) NOT NULL DEFAULT '',
  `deadline` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `start_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `completed_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `mutex` varchar(64) NOT NULL DEFAULT '',
  `priority` varchar(64) NOT NULL DEFAULT '',
  `parameters` longtext,
  `error_step` varchar(256) NOT NULL DEFAULT '',
  `err_msg` longtext,
  PRIMARY KEY (`id`),
  UNIQUE KEY `task_id` (`task_id`),
  KEY `idx_tasks_status` (`status`),
  KEY `idx_tasks_created_at` (`created_at`),
  KEY `idx_tasks_delete_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1006 DEFAULT CHARSET=utf8;