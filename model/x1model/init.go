/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2021/12/02
 * File: init.go
 */

/*
 * DESCRIPTION
 *   使用入口
 */

// Package x1model x1数据库对应的ORM模块
package x1model

import (
	"context"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/model"
)

var (
	servicerName = "db_x1"
	preloadConf  = map[string][]string{
		"Application": {"Clusters.Nodes", "Interfaces.Proxys", "Entitys", "BLBs", "Clusters.RoNodes"},
		"Cluster":     {"Nodes", "RoNodes"},
		"Interfaces":  {"Proxys"},
		"Backup":      {"Items"},
		"Exp":         {"Rules"},
		"MetaCluster": {"MetaNodes"},
		"AppBackup":   {"AppBackupItems"},
		"ConfTpl":     {"Items"},
	}
	resource *model.Resource = nil
)

type Conf struct {
	DbLogger     logit.Logger // gdp db logger
	GormLogger   logit.Logger // gorm 操作logger
	ServicerName string       // 自定义x1 mysql servicer name，传空字符串走默认  "db_x1"
}

// Init 使用前需要先初始化
//
// Param:
//
//	    @ctx - context.Context
//		   @dbLogger - logit.Logger 想要使用的dblogger
func Init(ctx context.Context, conf Conf) {
	if conf.ServicerName != "" {
		servicerName = conf.ServicerName
	}

	resourcePtr, err := model.InitModel(ctx, model.ResourceCfg{
		ServicerName: servicerName,
		DbLogger:     conf.DbLogger,
		GormLogger:   conf.GormLogger,
		PreloadConf:  preloadConf,
		AutoPreload:  false,
	})
	if err != nil {
		panic("init X1model fail")
	}
	resource = resourcePtr
}

// HasInited 给依赖方判断是否已初始化X1model 用
func HasInited() bool {
	return resource != nil
}

// GetDbAgent 获取Dao原始变量，可以直接操作Dao方法定制化自己的Sql方法
// 具体可以参考 model/x1model/xx_api.go 与 model/dao.go
func GetDbAgent(ctx context.Context) (*model.Resource, error) {
	if !HasInited() {
		return nil, errors.Errorf("x1model has not init")
	}
	return resource, nil
}
