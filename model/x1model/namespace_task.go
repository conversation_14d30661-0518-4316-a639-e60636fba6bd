package x1model

import (
	"context"
	"time"
)

type NamespaceTask struct {
	ID              int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" `
	AppID           string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	TaskID          string    `gorm:"column:task_id;NOT NULL" json:"task_id" ukey:"task_id"`
	Action          string    `gorm:"column:action;NOT NULL" json:"action"`
	Namespace       string    `gorm:"column:namespace;NOT NULL" json:"namespace"`
	BosBucket       string    `gorm:"column:bos_bucket;NOT NULL" json:"bos_bucket"`
	BosObjectPrefix string    `gorm:"column:bos_object_prefix;NOT NULL" json:"bos_object_prefix"`
	MirrorBosBucket string    `gorm:"column:mirror_bos_bucket;NOT NULL" json:"mirror_bos_bucket"`
	Progress        string    `gorm:"column:progress;NOT NULL" json:"progress"`
	ProgressPercent float64   `gorm:"column:progress_percent;NOT NULL" json:"progress_percent"`
	ErrorMessage    string    `gorm:"column:error_message;NOT NULL" json:"error_message"`
	CreateAt        time.Time `gorm:"column:create_at;NOT NULL" json:"create_at"`
	UpdateAt        time.Time `gorm:"column:update_at;NOT NULL" json:"update_at"`
	Status          string    `gorm:"column:status;NOT NULL" json:"status"`
	Comment         string    `gorm:"column:comment;NOT NULL" json:"comment"`
	SstFileNum      int       `gorm:"column:sst_file_num;NOT NULL" json:"sst_file_num"`
}

func (NamespaceTask) TableName() string {
	return "namespace_task"
}

// NamespaceTaskGetAllByAppID 通过appId获取namespace任务
func NamespaceTaskGetAllByAppID(ctx context.Context, appID string) ([]*NamespaceTask, error) {
	var namespaceTask []*NamespaceTask
	err := resource.GetAllByCond(ctx, &namespaceTask, "app_id = ?", appID)
	if err != nil {
		return nil, err
	}
	return namespaceTask, nil
}

// NamespaceTaskGetByTaskID 通过任务id获取
func NamespaceTaskGetByTaskID(ctx context.Context, taskID string) (*NamespaceTask, error) {
	var namespaceTask NamespaceTask
	err := resource.GetOneByUkey(ctx, taskID, &namespaceTask)
	if err != nil {
		return nil, err
	}
	return &namespaceTask, nil
}

// NamespaceTaskSaveAll 保存所有NamespaceTask
func NamespaceTaskSaveAll(ctx context.Context, dataPtr []*NamespaceTask) error {
	// 遇到唯一键是更新而不是报错
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}

// NamespaceTaskSave 保存一个NamespaceTask
func NamespaceTaskSave(ctx context.Context, dataPtr *NamespaceTask) error {
	return resource.CreateOne(ctx, dataPtr)
}
