package x1model

import "testing"

func TestApplication_SetAPIFlag(t *testing.T) {
	app := &Application{}
	app.SetAPIFlag("test")
	if app.ApiFlag != "test" {
		t.<PERSON>rror("SetAPIFlag failed")
	}
	app.SetAPIFlag("test")
	if app.ApiFlag != "test" {
		t.Error("SetAPIFlag failed")
	}
	app.SetAPIFlag("test2")
	if app.ApiFlag != "test,test2" {
		t.<PERSON><PERSON><PERSON>("SetAPIFlag failed")
	}
}

func TestApplication_IsAPIFlagSet(t *testing.T) {
	app := &Application{}
	if app.IsAPIFlagSet("test") {
		t.Error("IsAPIFlagSet failed")
	}
	app.SetAPIFlag("test")
	if !app.IsAPIFlagSet("test") {
		t.<PERSON>rror("IsAPIFlagSet failed")
	}
	if app.IsAPIFlagSet("test2") {
		t.Error("IsAPIFlagSet failed")
	}
	app.SetAPIFlag("test2")
	if !app.IsAPIFlagSet("test2") {
		t.Error("IsAPIFlagSet failed")
	}
}

func TestApplication_IsAPIFlagSetAll(t *testing.T) {
	app := &Application{}
	if app.IsAPIFlagSet("test") {
		t.Error("IsAPIFlagSet failed")
	}
	app.SetAPIFlag("all")
	if !app.IsAPIFlagSet("test") {
		t.Error("IsAPIFlagSet failed")
	}
}
