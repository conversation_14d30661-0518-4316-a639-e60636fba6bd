package x1model

import "context"

const (
	InjectionStatusNormal        = "normal"
	InjectionStatusNotAvailable  = "notavailable"
	InjectionStatusWaiting       = "waiting"
	InjectionStatusInjected      = "injected"
	InjectionStatusInFailover    = "infailover"
	InjectionStatusInSelfHealing = "inselfhealing"
)

func InjectionGetAllByCond(ctx context.Context, cond string, vals ...any) ([]*Injection, error) {
	var injections []*Injection
	err := resource.GetAllByCond(ctx, &injections, cond, vals...)
	if err != nil {
		return nil, err
	}
	return injections, nil
}

func InjectionSave(ctx context.Context, dataPtr []*Injection) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
