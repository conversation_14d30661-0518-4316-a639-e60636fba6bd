/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/06
 * File: node_api.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package x1model TODO package function desc
package x1model

import "context"

func NodeGetByNodeId(ctx context.Context, nodeId string) (*Node, error) {
	var node Node
	err := resource.GetOneByUkey(ctx, nodeId, &node)
	if err != nil {
		return nil, err
	}
	return &node, nil
}

func NodeGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*Node, error) {
	var node Node
	err := resource.GetOneByCond(ctx, &node, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &node, nil
}

func NodeGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*Node, error) {
	var nodes []*Node
	err := resource.GetAllByCond(ctx, &nodes, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return nodes, nil
}

func NodeDeleteMulti(ctx context.Context, datas interface{}) error {
	return resource.DeleteMulti(ctx, datas)
}

func NodesSave(ctx context.Context, dataPtr []*Node) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
