package x1model

import (
	"context"
	"database/sql/driver"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/pkg/errors"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/scs/x1-base/model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func initMock(t *testing.T) (sqlmock.Sqlmock, func()) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	return mock, func() {
		_ = db.Close()
	}
}

func packageHeaders() []string {
	return []string{
		"id",
		"name",
		"package_id",
		"full_version",
		"major_version",
		"release_at",
		"access",
		"status",
		"tags",
		"description",
		"owner",
		"md5",
	}
}

func packageData() [][]driver.Value {
	return [][]driver.Value{
		{3, "proxy", "proxy_*******", "*******", "6.0", "2023-03-14 14:32:32", "https://imperturbable-cursor.name", "inuse", nil, "nothing", "cuiyi01", "aaa"},
		{1, "redis", "redis_*******", "*******", "7.0", "2023-03-14 14:28:12", "https://imperturbable-cursor.name", "inuse", nil, "nothing", "cuiyi01", "bbb"},
		{2, "redis", "redis_*******", "*******", "7.0", "2023-03-14 14:28:12", "https://imperturbable-cursor.name", "inuse", nil, "nothing", "cuiyi01", "ccc"},
		{4, "agent", "agent_*******", "*******", "7.0", "2023-03-14 14:28:12", "https://imperturbable-cursor.name", "inuse",
			"bloomfilter", "nothing", "cuiyi01", "ccc"},
		{5, "agent", "agent_*******", "*******", "7.0", "2023-03-14 14:28:12", "https://imperturbable-cursor.name", "inuse",
			"bloomfilter", "nothing", "cuiyi01", "ccc"},
	}
}

func packageRecordHeaders() []string {
	return []string{
		"id",
		"entity",
		"name",
		"type",
		"package_id",
		"created_at",
		"owner",
		"deploytime",
	}
}

func packageRecordData() [][]driver.Value {
	return [][]driver.Value{
		{1, "xxx", "redis", "app", "redis_*******", time.Now().Add(-time.Hour), "cuiyi01", 0},
		{2, "xxx", "redis", "app", "redis_*******", time.Now(), "cuiyi01", 0},
		{3, "xxx", "proxy", "app", "proxy_*******", time.Now(), "cuiyi01", 0},
		{4, "scs-bj-addlirlmppns-0.1", "slot-redis", "redis", "slot-redis-6.2.35.1", time.Now(), "cuiyi01", 1729670000},
		{5, "scs-bj-addlirlmppns-0.1", "slot-redis", "redis", "slot-redis-6.2.36.1", time.Now(), "cuiyi01", 1729671976},
		{5, "scs-bj-addlirlmppns-0.2", "slot-redis", "redis", "slot-redis-6.2.32.1", time.Now(), "cuiyi01", 1729670000},
		{6, "scs-bj-addlirlmppns-0.2", "slot-redis", "redis", "slot-redis-6.2.34.1", time.Now(), "cuiyi01", 1729671976},
	}
}

func TestPackageLatestList(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	rows := sqlmock.NewRows(packageHeaders()).AddRow(packageData()[0]...).AddRow(packageData()[1]...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	pkgs, err := PackageLatestList(context.Background())
	if err != nil {
		t.Fatalf("error: %s", err)
	}
	if len(pkgs) != 2 {
		t.Fatalf("expect 2, got %d", len(pkgs))
	}
}

func TestPackageLatestRelease(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	rows := sqlmock.NewRows(packageHeaders()).AddRow(packageData()[0]...).AddRow(packageData()[1]...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	pkgs, err := GetLatestPackages(context.Background(), []string{"redis", "proxy"}, map[string]string{"redis": "7.0"})
	if err != nil {
		t.Fatalf("error: %s", err)
	}
	if len(pkgs) != 2 {
		t.Fatalf("expect 2, got %d", len(pkgs))
	}
	rows2 := sqlmock.NewRows(packageHeaders()).AddRow(packageData()[1]...).AddRow(packageData()[2]...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows2)
	pkgs, err = GetLatestPackages(context.Background(), []string{"redis", "proxy"}, map[string]string{"redis": "7.0"})
	if err == nil {
		t.Fatalf("error: %s", err)
	} else {
		fmt.Println(err.Error())
	}
}

func TestPackageLatestReleaseEmpty(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	rows := sqlmock.NewRows(nil)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	_, err := GetLatestPackages(context.Background(), []string{"redis", "proxy"}, map[string]string{"fakepkgs": "7.0"})
	if err == nil {
		t.Fatalf("error: %s", err)
	}
	if !strings.Contains(err.Error(), "not found") {
		t.Fatalf("wrong")
	}
}

func TestPackageAddRelease(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	params := &Package{
		Name:         "redis",
		FullVersion:  "*******",
		MajorVersion: "7.0",
		ReleasedAt:   time.Now(),
		Access:       "xxxx",
		Tags:         "ttt",
		MD5:          "aaa",
		CoreBinMD5:   "bbb",
	}
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO `package`").WithArgs(
		"redis",
		"redis-*******",
		"*******",
		"7.0",
		sqlmock.AnyArg(),
		"xxxx",
		"inuse",
		"ttt",
		"no description",
		"unknown",
		"aaa",
		"bbb",
		sqlmock.AnyArg(),
	).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	err := PackageAddRelease(context.Background(), params)
	if err != nil {
		t.Fatalf("error: %s", err)
	}
}

func TestPackageRecordGetCurrent(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	rows := sqlmock.NewRows(packageRecordHeaders()).AddRow(packageRecordData()[1]...).AddRow(packageRecordData()[2]...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	rows2 := sqlmock.NewRows(packageHeaders()).AddRow(packageData()[1]...).AddRow(packageData()[0]...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows2)
	pkgs, err := GetCurrentPackageRecord(context.Background(), "xxx", []string{"redis", "proxy"}, "")
	if err != nil {
		t.Fatalf("error: %s", err)
	}
	if len(pkgs) != 2 {
		t.Fatalf("expect 2, got %d", len(pkgs))
	}
}

func TestGetRequirePkgNameAndVersionFilter(t *testing.T) {
	type getRequirePkgNamesCase struct {
		kernel             string
		kernelMajorVersion string
		appType            string
		needSmartDBA       bool
		needSyncAgent      bool
		rightPkgs          []string
		rightVersionFilter map[string]string
		moreParams         *GetRequirePkgNameMoreParams
	}
	cases := []getRequirePkgNamesCase{
		{
			kernel:             EngineRedis,
			kernelMajorVersion: "2.8",
			appType:            AppTypeStandalone,
			needSmartDBA:       true,
			needSyncAgent:      true,
			rightPkgs:          append(DefaultPkgs, OpensourceRedisPkg, SmartDBApkg, SyncAgentPkg),
			rightVersionFilter: map[string]string{OpensourceRedisPkg: "2"},
		},
		{
			kernel:             EngineRedis,
			kernelMajorVersion: "2.8",
			appType:            AppTypeCluster,
			needSmartDBA:       false,
			needSyncAgent:      false,
			rightPkgs:          append(DefaultPkgs, BaiduRedisPkg),
			rightVersionFilter: map[string]string{BaiduRedisPkg: "2"},
		},
		{
			kernel:             EngineRedis,
			kernelMajorVersion: "3.2",
			appType:            AppTypeStandalone,
			needSmartDBA:       true,
			needSyncAgent:      true,
			rightPkgs:          append(DefaultPkgs, OpensourceRedisPkg, SmartDBApkg, SyncAgentPkg),
			rightVersionFilter: map[string]string{OpensourceRedisPkg: "3"},
		},
		{
			kernel:             EngineRedis,
			kernelMajorVersion: "3.2",
			appType:            AppTypeCluster,
			needSmartDBA:       true,
			needSyncAgent:      true,
			rightPkgs:          append(DefaultPkgs, BaiduRedisPkg, SmartDBApkg, SyncAgentPkg),
			rightVersionFilter: map[string]string{BaiduRedisPkg: "3"},
		},
		{
			kernel:             EngineRedis,
			kernelMajorVersion: "3.2",
			appType:            AppTypeCluster,
			needSmartDBA:       true,
			needSyncAgent:      true,
			rightPkgs:          append(DefaultPkgs, BaiduRedisPkg, SmartDBApkg, SyncAgentPkg),
			rightVersionFilter: map[string]string{BaiduRedisPkg: "3"},
		},
		{
			kernel:             EngineRedis,
			kernelMajorVersion: "4.0",
			appType:            AppTypeCluster,
			needSmartDBA:       true,
			needSyncAgent:      true,
			rightPkgs:          append(DefaultPkgs, BaiduRedisPkg, SmartDBApkg, SyncAgentPkg),
			rightVersionFilter: map[string]string{BaiduRedisPkg: "4"},
		},
		{
			kernel:             EngineRedis,
			kernelMajorVersion: "5.0",
			appType:            AppTypeCluster,
			needSmartDBA:       true,
			needSyncAgent:      true,
			rightPkgs:          append(DefaultPkgs, OpensourceRedisPkg, SmartDBApkg, SyncAgentPkg),
			rightVersionFilter: map[string]string{OpensourceRedisPkg: "5"},
		},
		{
			kernel:             EngineRedis,
			kernelMajorVersion: "6.0",
			appType:            AppTypeCluster,
			needSmartDBA:       true,
			needSyncAgent:      true,
			rightPkgs:          append(DefaultPkgs, BaiduRedisPkg, SmartDBApkg, SyncAgentPkg, JsonModulePkg, CascadModulePkg, BloomfilterModulePkg),
			rightVersionFilter: map[string]string{BaiduRedisPkg: "6"},
		},
		{
			kernel:             EngineRedis,
			kernelMajorVersion: "7.0",
			appType:            AppTypeCluster,
			needSmartDBA:       true,
			needSyncAgent:      true,
			rightPkgs:          append(DefaultPkgs, BaiduRedisPkg, SmartDBApkg, SyncAgentPkg, JsonModulePkg, CascadModulePkg, BloomfilterModulePkg),
			rightVersionFilter: map[string]string{BaiduRedisPkg: "7"},
		},
		{
			kernel:             EngineRedis,
			kernelMajorVersion: "7.0",
			appType:            AppTypeCluster,
			needSmartDBA:       true,
			needSyncAgent:      true,
			rightPkgs:          append(DefaultPkgs, BaiduRedisPkg, SmartDBApkg, SyncAgentPkg, JsonModulePkg, CascadModulePkg, BloomfilterModulePkg),
			rightVersionFilter: map[string]string{BaiduRedisPkg: "7"},
		},
		{
			kernel:             EnginePegaDB,
			kernelMajorVersion: "2.0",
			appType:            AppTypeCluster,
			needSmartDBA:       true,
			needSyncAgent:      true,
			rightPkgs:          append(DefaultPkgs, PegaDBPkg, SmartDBApkg, SyncAgentPkg, SstDumpBin),
			rightVersionFilter: map[string]string{PegaDBPkg: "2"},
		},
		{
			kernel:             EnginePegaDB,
			kernelMajorVersion: "3.0",
			appType:            AppTypeCluster,
			needSmartDBA:       true,
			needSyncAgent:      true,
			rightPkgs:          append(DefaultPkgs, PegaDBPkg, SmartDBApkg, SyncAgentPkg, SstDumpBin),
			rightVersionFilter: map[string]string{PegaDBPkg: "3"},
		},
		{
			kernel:             EnginePegaDB,
			kernelMajorVersion: "",
			appType:            AppTypeCluster,
			needSmartDBA:       true,
			needSyncAgent:      true,
			rightPkgs:          append(DefaultPkgs, PegaDBPkg, SmartDBApkg, SyncAgentPkg, SstDumpBin),
			rightVersionFilter: map[string]string{PegaDBPkg: "2"},
		},
		{
			kernel:             EngineBDRPProxy,
			kernelMajorVersion: "2.0",
			appType:            AppTypeCluster,
			needSmartDBA:       true,
			needSyncAgent:      false,
			rightPkgs:          append(DefaultPkgs, ProxyPkg, SmartDBApkg),
			rightVersionFilter: nil,
		},
		{
			kernel:             EngineBDRPProxy,
			kernelMajorVersion: "4",
			appType:            AppTypeCluster,
			needSmartDBA:       true,
			needSyncAgent:      false,
			rightPkgs:          append(DefaultPkgs, NewProxyPkg, SmartDBApkg),
			rightVersionFilter: nil,
		},
		{
			kernel:             EngineRedis,
			kernelMajorVersion: "4.0",
			appType:            AppTypeCluster,
			needSmartDBA:       true,
			needSyncAgent:      true,
			moreParams:         &GetRequirePkgNameMoreParams{SyncAgentBigVersion: "2.0"},
			rightPkgs:          append(DefaultPkgs, BaiduRedisPkg, SmartDBApkg, NewSyncAgentPkg),
			rightVersionFilter: map[string]string{BaiduRedisPkg: "4"},
		},
		{
			kernel:             EngineRedis,
			kernelMajorVersion: "6.0",
			appType:            AppTypeCluster,
			needSmartDBA:       true,
			needSyncAgent:      true,
			moreParams:         &GetRequirePkgNameMoreParams{SyncAgentBigVersion: "2.0"},
			rightPkgs:          append(DefaultPkgs, BaiduRedisPkg, SmartDBApkg, NewSyncAgentPkg, JsonModulePkg, CascadModulePkg, BloomfilterModulePkg),
			rightVersionFilter: map[string]string{BaiduRedisPkg: "6"},
		},
	}

	for _, caseIns := range cases {
		pkgNames, versionFilter := GetRequirePkgNameAndVersionFilter(caseIns.kernel,
			caseIns.kernelMajorVersion, caseIns.appType, caseIns.needSmartDBA, caseIns.needSyncAgent, caseIns.moreParams)
		fmt.Println("pkgNames:", pkgNames)
		fmt.Println("versionFilter:", versionFilter)
		if len(pkgNames) != len(caseIns.rightPkgs) {
			fmt.Println(caseIns)
			fmt.Println(pkgNames)
			t.Fatalf("not match case :%s, actual pkgs:%s", base_utils.Format(caseIns), base_utils.Format(pkgNames))
		} else {
			// 数量一样，每个都有，就是ok
			for _, wantPkg := range caseIns.rightPkgs {
				has := false
				for _, actualPkg := range pkgNames {
					if actualPkg == wantPkg {
						has = true
						break
					}
				}
				if !has {
					t.Fatalf("not match case :%s, actual pkgs:%s", base_utils.Format(caseIns), base_utils.Format(pkgNames))
				}
			}
		}
		for wantPkg, wantVer := range caseIns.rightVersionFilter {
			if versionFilter == nil {
				t.Fatalf("not match case :%s, actual version filter:%s", base_utils.Format(caseIns), base_utils.Format(versionFilter))
			}
			if actualVer, ok := versionFilter[wantPkg]; !ok {
				t.Fatalf("not match case :%s, actual version filter:%s", base_utils.Format(caseIns), base_utils.Format(versionFilter))
			} else {
				if actualVer != wantVer {
					t.Fatalf("not match case :%s, actual version filter:%s", base_utils.Format(caseIns), base_utils.Format(versionFilter))
				}
			}
		}
		for actualPkg := range versionFilter {
			if caseIns.rightVersionFilter == nil {
				t.Fatalf("not match case :%s, actual version filter:%s", base_utils.Format(caseIns), base_utils.Format(versionFilter))
			}
			if _, ok := caseIns.rightVersionFilter[actualPkg]; !ok {
				t.Fatalf("not match case :%s, actual version filter:%s", base_utils.Format(caseIns), base_utils.Format(versionFilter))
			}
		}
	}
}

func TestGetGreyBoxPackages(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	rows := sqlmock.NewRows(packageHeaders()).AddRow(packageData()[3]...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	pkgs, err := GetGreyBoxPackages(context.Background(), "agent", "")
	if err != nil {
		t.Fatalf("error: %s", err)
	}
	if len(pkgs) != 0 {
		t.Fatalf("expect 0, got %d", len(pkgs))
	}
	rows2 := sqlmock.NewRows(packageHeaders()).AddRow(packageData()[2]...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows2)
	pkgs, err = GetGreyBoxPackages(context.Background(), "agent", "bloomfilter")
	if err != nil {
		t.Fatalf("error: %s", err)
	} else {
		if len(pkgs) == 0 {
			t.Fatalf("expect 1, got %d", len(pkgs))
		} else {
			fmt.Println(base_utils.Format(pkgs))
		}
	}
}

//	func TestPackageRecordUpdateCurrent(t *testing.T) {
//		mock, toDefer := initMock(t)
//		defer toDefer()
//		rows := sqlmock.NewRows(packageRecordHeaders()).AddRow(packageRecordData()[0]...).AddRow(packageRecordData()[2]...)
//		mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
//		rows2 := sqlmock.NewRows(packageHeaders()).AddRow(packageData()[2]...).AddRow(packageData()[0]...)
//		mock.ExpectQuery("^SELECT .*").WillReturnRows(rows2)
//		rows3 := sqlmock.NewRows(packageHeaders()).AddRow(packageData()[0]...).AddRow(packageData()[1]...)
//		mock.ExpectQuery("^SELECT .*").WillReturnRows(rows3)
//		mock.ExpectBegin()
//		mock.ExpectExec("^INSERT INTO `package_record`").WithArgs(
//			"xxx",
//			"redis",
//			"app",
//			"redis_*******",
//			"inuse",
//			sqlmock.AnyArg(),
//			sqlmock.AnyArg(),
//		).WillReturnResult(sqlmock.NewResult(1, 1))
//		mock.ExpectCommit()
//		pkgs, err := PackageRecordUpdateCurrent(context.Background(), "xxx", []string{"redis", "proxy"}, nil, "xx", "")
//		if err != nil {
//			t.Fatalf("error: %s", err)
//		}
//		if len(pkgs) != 2 {
//			t.Fatalf("expect 2, got %d", len(pkgs))
//		}
//	}
//
//	func TestPackageRecordGetForDeploy(t *testing.T) {
//		mock, toDefer := initMock(t)
//		defer toDefer()
//		rows := sqlmock.NewRows(packageRecordHeaders()).AddRow(packageRecordData()[0]...).AddRow(packageRecordData()[2]...)
//		mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
//		rows2 := sqlmock.NewRows(packageHeaders()).AddRow(packageData()[2]...).AddRow(packageData()[0]...)
//		mock.ExpectQuery("^SELECT .*").WillReturnRows(rows2)
//		rows3 := sqlmock.NewRows(packageHeaders()).AddRow(packageData()[0]...).AddRow(packageData()[1]...)
//		mock.ExpectQuery("^SELECT .*").WillReturnRows(rows3)
//		pkgs, err := GetPackageInfosForDeploy(context.Background(), "xxx", []string{"redis", "proxy"}, nil, "xx", "")
//		if err != nil {
//			t.Fatalf("error: %s", err)
//		}
//		if len(pkgs) != 2 {
//			t.Fatalf("expect 2, got %d", len(pkgs))
//		}
//	}
func TestGetRealVersion(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	rows := sqlmock.NewRows(packageRecordHeaders()).AddRow(packageRecordData()[1]...).AddRow(packageRecordData()[2]...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	rows2 := sqlmock.NewRows(packageHeaders()).AddRow(packageData()[1]...).AddRow(packageData()[0]...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows2)
	pkgs, err := GetRealVersion(context.Background(), "xxx", []string{"redis", "proxy"}, "")
	if err != nil {
		t.Fatalf("error: %s", err)
	}
	if len(pkgs) != 2 {
		t.Fatalf("expect 2, got %d", len(pkgs))
	}
}

func TestGetLatestPkgRecord(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	mock.ExpectQuery("^SELECT .*").WillReturnError(errors.New("err!"))
	rows := sqlmock.NewRows(packageRecordHeaders()).AddRow(packageRecordData()[1]...).AddRow(packageRecordData()[2]...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	rows2 := sqlmock.NewRows(packageHeaders()).AddRow(packageData()[1]...).AddRow(packageData()[0]...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows2)
	record, err := GetLatestPkgRecord(context.Background(), "xxx", []string{"redis", "proxy"}, "")
	if err == nil {
		t.Fatalf("error: %s", err)
	}
	record, err = GetLatestPkgRecord(context.Background(), "xxx", []string{"redis", "proxy"}, "")
	if err != nil {
		t.Fatalf("error: %s", err)
	}
	if len(record) != 2 {
		t.Fatalf("expect 2, got %d", len(record))
	}
}

func TestSaveDeployTime(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	mock.ExpectQuery("^SELECT .*").WillReturnError(errors.New("err!"))
	err := SaveDeployTime(context.Background(), "xxx", []string{"redis", "proxy"}, []*Package{{PackageID: "redis_*******"}})
	if err == nil {
		t.Fatalf("error: %s", err)
	}
	mock.ExpectQuery("^SELECT .*").
		WillReturnRows(sqlmock.NewRows(packageRecordHeaders()).AddRow(packageRecordData()[1]...).AddRow(packageRecordData()[2]...))
	mock.ExpectBegin()
	mock.ExpectExec(".* ON DUPLICATE KEY UPDATE .*").WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	err = SaveDeployTime(context.Background(), "xxx", []string{"redis", "proxy"}, []*Package{{PackageID: "redis_*******"}})
	if err != nil {
		t.Fatalf("error: %s", err)
	}

	mock.ExpectQuery("^SELECT .*").
		WillReturnRows(sqlmock.NewRows(packageRecordHeaders()).AddRow(packageRecordData()[1]...).AddRow(packageRecordData()[2]...))
	mock.ExpectBegin()
	mock.ExpectExec(".* ON DUPLICATE KEY UPDATE .*").WillReturnError(errors.New("err!"))
	mock.ExpectRollback()
	err = SaveDeployTime(context.Background(), "xxx", []string{"redis", "proxy"}, []*Package{{PackageID: "redis_*******"}})
	if err == nil {
		t.Fatalf("error: %s", err)
	}
}

func TestGetOldestPackageRealVersion(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	mock.ExpectQuery("^SELECT *").WillReturnError(errors.New("err!"))
	rows := sqlmock.NewRows(packageRecordHeaders()).AddRow(packageRecordData()[3]...).AddRow(packageRecordData()[4]...).
		AddRow(packageRecordData()[5]...).AddRow(packageRecordData()[6]...)
	mock.ExpectQuery("^SELECT *").WillReturnRows(rows)
	oldestVersion, err := GetOldestPackageRealVersion(context.Background(), "redis-mock", []string{"scs-bj-addlirlmppns-0.1"})
	if err == nil {
		t.Fatalf("error: %s", err)
	}
	oldestVersion, err = GetOldestPackageRealVersion(context.Background(), "redis", []string{"scs-bj-addlirlmppns-0.1"})
	if err != nil {
		t.Fatalf("error: %s", err)
	}
	if oldestVersion != "6.2.34.1" {
		t.Fatalf("expect '6.2.34.1', got %s", oldestVersion)
	}
	fmt.Println(fmt.Printf("oldest ver:%s\n",oldestVersion))
}