/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/01/20
 * File: utils.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package x1model TODO package function desc
package x1model

import (
	"context"
	"errors"

	"gorm.io/gorm"
)

func IsNodeCanBeMaster(ctx context.Context, nodeInfo *Node) bool {
	if nodeInfo.Status == NodeOrProxyStatusToCreate || nodeInfo.Status == NodeOrProxyStatusInUse {
		return true
	}
	return false
}

func IsNotFound(err error) bool {
	return errors.Is(err, gorm.ErrRecordNotFound)
}

func GetIpList(ctx context.Context, app *Application) []string {
	iplist := make([]string, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			iplist = append(iplist, node.Ip)
		}
		for _, node := range cluster.RoNodes {
			iplist = append(iplist, node.Ip)
		}
	}
	return iplist
}

// FetchAllNodesOfCluster return all nodes of cluster include roNode
func FetchAllNodesOfCluster(ctx context.Context, cluster *Cluster) []*Node {
	nodes := make([]*Node, 0)
	for _, node := range cluster.Nodes {
		nodes = append(nodes, node)
	}
	for _, node := range cluster.RoNodes {
		n := &Node{
			Id:               node.Id,
			AppId:            node.AppId,
			ClusterId:        node.ClusterId,
			NodeId:           node.NodeId,
			ContainerId:      node.ContainerId,
			Engine:           node.Engine,
			EngineVersion:    node.EngineVersion,
			Port:             node.Port,
			Basedir:          node.Basedir,
			Datadir:          node.Datadir,
			InnodbBufferSize: node.InnodbBufferSize,
			Ip:               node.Ip,
			XagentPort:       node.XagentPort,
			Region:           node.Region,
			LogicZone:        node.LogicZone,
			Azone:            node.Azone,
			VpcId:            node.VpcId,
			SubnetId:         node.SubnetId,
			Pool:             node.Pool,
			Tags:             node.Tags,
			Role:             node.Role,
			DestRole:         node.DestRole,
			Status:           node.Status,
			DestStatus:       node.DestStatus,
			TaskId:           node.TaskId,
			Properties:       node.Properties,
			ResourceOrderId:  node.ResourceOrderId,
			ResourceId:       node.ResourceId,
			FloatingIP:       node.FloatingIP,
			IPv6:             node.IPv6,
			RootPassword:     node.RootPassword,
			NodeShortID:      node.NodeShortID,
			HostName:         node.HostName,
			NodeFixID:        node.NodeFixID,
			TempFlags:        node.TempFlags,
			GlobalID:         node.GlobalID,
			GlobalSeqID:      node.GlobalSeqID,
		}
		nodes = append(nodes, n)
	}
	return nodes
}

// TransProxyToNode transform proxy to node
func TransProxyToNode(ctx context.Context, proxy *Proxy) *Node {
	if proxy == nil {
		return nil
	}
	return &Node{
		Id:              proxy.Id,
		AppId:           proxy.AppId,
		ClusterId:       proxy.InterfaceId,
		NodeId:          proxy.ProxyId,
		ContainerId:     proxy.ContainerId,
		Engine:          proxy.Engine,
		EngineVersion:   proxy.EngineVersion,
		Port:            proxy.Port,
		Basedir:         proxy.Basedir,
		Ip:              proxy.Ip,
		XagentPort:      proxy.XagentPort,
		Region:          proxy.Region,
		LogicZone:       proxy.LogicZone,
		Azone:           proxy.Azone,
		VpcId:           proxy.VpcId,
		SubnetId:        proxy.SubnetId,
		Status:          proxy.Status,
		DestStatus:      proxy.DestStatus,
		Properties:      proxy.Properties,
		ResourceOrderId: proxy.ResourceOrderId,
		ResourceId:      proxy.ResourceId,
		FloatingIP:      proxy.FloatingIP,
		IPv6:            proxy.IPv6,
		RootPassword:    proxy.RootPassword,
		NodeShortID:     proxy.ProxyShortID,
		HostName:        proxy.HostName,
		NodeFixID:       proxy.NodeFixID,
		TempFlags:       proxy.TempFlags,
		GlobalID:        proxy.GlobalID,
		GlobalSeqID:     proxy.GlobalSeqID,
		Role:            "proxy",
		//DestRole:         proxy.DestRole,
		//Datadir:          proxy.Datadir,
		//InnodbBufferSize: proxy.InnodbBufferSize,
		//Pool:             proxy.Pool,
		//Tags:             proxy.Tags,
		//TaskId:           proxy.TaskId,
	}
}
