package x1model

import "context"

func ProxyAclGetAllByCond(ctx context.Context, cond string, vals ...any) ([]*ProxyAcl, error) {
	var proxyAcls []*ProxyAcl
	err := resource.GetAllByCond(ctx, &proxyAcls, cond, vals...)
	if err != nil {
		return nil, err
	}
	return proxyAcls, nil
}

func ProxyAclSave(ctx context.Context, dataPtr []*ProxyAcl) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
