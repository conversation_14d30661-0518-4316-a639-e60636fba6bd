package x1model

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"icode.baidu.com/baidu/scs/x1-base/model"
)

func TestPortAliveRatioGetAllByCond(t *testing.T) {

	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").
		WillReturnRows(sqlmock.NewRows([]string{"id", "app_id", "user_id", "status", "ratio", "create_at"}).
			AddRow(1, "app_id", "user_id", "status", 99.99, time.Now()))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	ret, err := PortAliveRatioGetAllByCond(context.Background(), "app_id = ? and status = ?", "app_id", "status")
	if err != nil {
		t.Fatalf("error PortAliveRatioGetAllByCond: %s", err)
	}
	if len(ret) != 1 {
		t.Fatalf("error PortAliveRatioGetAllByCond: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").WillReturnError(errors.New("a"))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	_, err = PortAliveRatioGetAllByCond(context.Background(), "app_id = ? and status = ?", "app_id", "status")
	if err == nil {
		t.Fatalf("no error PortAliveRatioGetAllByCond: %s", err)
	}
}

//func TestPortAliveRatioGetAllByCondOffline(t *testing.T) {
//	//ctx := context.Background()
//	//defer sdk_utils.TestEnvDefer(ctx)()
//	type args struct {
//		ctx     context.Context
//		fmtCond string
//		vals    string
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    []*PortAliveRatio
//		wantErr bool
//	}{
//		{"no app", args{ctx: context.Background(), fmtCond: "app_id = ?", vals: "no app"}, make([]*PortAliveRatio, 0), false},
//		{"app with task", args{ctx: context.Background(), fmtCond: "app_id = ?", vals: "app_id"}, []*PortAliveRatio{&PortAliveRatio{
//			ID:       1,
//			AppID:    "app_id",
//			UserID:   "user_id",
//			Ratio:    99.99,
//			CreateAt: time.Date(2023, 12, 05, 10, 31, 48, 0, time.UTC),
//			Status:   "status",
//		}}, false},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := PortAliveRatioGetAllByCond(tt.args.ctx, tt.args.fmtCond, tt.args.vals)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("PortAliveRatioGetAllByCond() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if len(got) > 0 {
//				fmt.Printf(" got %+v, err %+v", got[0], err)
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("PortAliveRatioGetAllByCond() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
