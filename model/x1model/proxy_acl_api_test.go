package x1model

import (
	"context"
	"database/sql/driver"
	"github.com/DATA-DOG/go-sqlmock"
	"testing"
	"time"
)

func proxyAclHeaders() []string {
	return []string{
		"id",
		"app_id",
		"account_name",
		"create_at",
		"update_at",
		"version",
		"engine",
		"password",
		"allowed_cmds",
		"allowed_sub_cmds",
		"key_patterns",
		"properties",
		"status",
	}
}

func proxyAclData() [][]driver.Value {
	return [][]driver.Value{
		{
			1,
			"1",
			"test",
			time.Now(),
			time.Now(),
			"1",
			"redis",
			"test",
			"test",
			"test",
			"test",
			"test",
			"1",
		},
	}
}

func TestProxyAclGetAllByCond(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	rows := mock.NewRows(proxyAclHeaders()).AddRow(proxyAclData()[0]...)
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	mock.ExpectCommit()
	proxyAcls, err := ProxyAclGetAllByCond(context.Background(), "app_id = ?", "1")
	if err != nil {
		t.Errorf("ProxyAclGetAllByCond() error = %v", err)
	}
	if len(proxyAcls) != 1 {
		t.Errorf("ProxyAclGetAllByCond() got = %v, want = 1", len(proxyAcls))
	}
}

func TestProxyAclSave(t *testing.T) {
	mock, toDefer := initMock(t)
	defer toDefer()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO `proxy_acl` .*").WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	proxyAcl := &ProxyAcl{
		Id:             1,
		AppID:          "1",
		AccountName:    "test",
		CreateAt:       time.Now(),
		UpdateAt:       time.Now(),
		Version:        1,
		Engine:         "redis",
		Password:       "test",
		AllowedCmds:    "test",
		AllowedSubCmds: "test",
		KeyPatterns:    "test",
		Properties:     "test",
		Status:         "1",
	}
	err := ProxyAclSave(context.Background(), []*ProxyAcl{proxyAcl})
	if err != nil {
		t.Errorf("ProxyAclSave() error = %v", err)
	}
}
