/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/10
 * File: application_api_test.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package x1model TODO package function desc
package x1model

import (
	"context"
	"fmt"
	"math/rand"
	"reflect"
	"testing"
	"time"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

const (
	mockAppNum       = 1
	mockClusterNum   = 5
	mockNodeNum      = 5
	mockInterfaceNum = 5
	mockProxyNum     = 5
	mockEntityNum    = 5

	mockAppIdPre       = "mock_appid_"
	mockClusterIdPre   = "mock_clusterid_"
	mockNodeIdPre      = "mock_nodeid_"
	mockInterfaceIdPre = "mock_interfaceid_"
	mockProxyIdPre     = "mock_proxyid_"
)

var (
	testResource struct {
		modelLogger logit.Logger
		gormLogger  logit.Logger
	}
	mockData struct {
		Applications []*Application
		Clusters     []*Cluster
		Nodes        []*Node
		Interfaces   []*Interface
		Proxys       []*Proxy
		Entitys      []*Entity
	}
	r *rand.Rand
)

func init() {
	ctx := context.Background()
	unittest.UnitTestInit(2)
	modelLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("logit/model.toml"))
	if err != nil {
		panic(err.Error())
	}
	gormLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("logit/gorm.toml"))
	if err != nil {
		panic(err.Error())
	}
	testResource.modelLogger = modelLogger
	testResource.gormLogger = gormLogger

	x1conf := Conf{
		DbLogger:     testResource.modelLogger,
		GormLogger:   testResource.gormLogger,
		ServicerName: "",
	}
	Init(ctx, x1conf)
	cleardb(ctx)
	getTestData()
}

func cleardb(ctx context.Context) {
	v := reflect.ValueOf(mockData)
	for k := 0; k < v.NumField(); k++ {
		err := resource.DeleteAllByCond(ctx, v.Field(k).Interface(), "app_id = ?", mockAppIdPre+"1")
		if err != nil {
			fmt.Println("delete fail ,err:" + err.Error())
		}
	}

}

//func TestApplication(t *testing.T) {
//	ctx := context.Background()
//	// 新建
//	process(ctx, t)
//	// 修改
//	mockData.Applications[0].AppName = "CHANGED"
//	mockData.Applications[0].Clusters[0].Nodes[0].Engine = "CHANGED"
//	process(ctx, t)
//	// 增加
//	mockData.Applications[0].Clusters[0].Nodes = append(mockData.Applications[0].Clusters[0].Nodes, &Node{
//		AppId:            mockAppIdPre + "1",
//		ClusterId:        randString(10),
//		NodeId:           randString(10),
//		ContainerId:      randString(10),
//		Engine:           randString(10),
//		EngineVersion:    randString(10),
//		Port:             0,
//		Basedir:          randString(10),
//		Datadir:          randString(10),
//		InnodbBufferSize: 0,
//		Ip:               randString(10),
//		XagentPort:       0,
//		Region:           randString(10),
//		Azone:            randString(10),
//		VpcId:            randString(10),
//		SubnetId:         randString(10),
//		Pool:             randString(10),
//		Tags:             randString(10),
//		Role:             randString(10),
//		DestRole:         randString(10),
//		Status:           randString(10),
//		DestStatus:       randString(10),
//		TaskId:           0,
//		Properties:       randString(10),
//	})
//	process(ctx, t)
//}

func process(ctx context.Context, t *testing.T) {
	if err := ApplicationsSave(ctx, mockData.Applications); err != nil {
		t.Fatalf("save failed; err: %s", err.Error())
	}
	ret, err := ApplicationGetAllByCond(ctx, "app_id = ?", mockAppIdPre+"1")
	if err != nil {
		t.Fatalf("ApplicationGetAllByCond failed; err: %s", err.Error())
	}
	if !reflect.DeepEqual(ret, mockData.Applications) {
		notEqual(t, ret, mockData.Applications)
	}

	retItem, err := ApplicationGetByAppId(ctx, mockAppIdPre+"1")
	if err != nil {
		t.Fatalf("ApplicationGetByAppId failed; err: %s", err.Error())
	}
	if !reflect.DeepEqual(retItem, mockData.Applications[0]) {
		notEqual(t, []*Application{retItem}, mockData.Applications)
	}

	retItem, err = ApplicationGetByCond(ctx, "app_id = ?", mockAppIdPre+"1")
	if err != nil {
		t.Fatalf("ApplicationGetByCond failed; err: %s", err.Error())
	}
	if !reflect.DeepEqual(retItem, mockData.Applications[0]) {
		notEqual(t, []*Application{retItem}, mockData.Applications)
	}
}

func notEqual(t *testing.T, ret []*Application, mock []*Application) {
	jsonRet, _ := base_utils.Marshal(ret)
	fmt.Println("retJson:" + cast.ToString(jsonRet))
	mockRet, _ := base_utils.Marshal(mock)
	fmt.Println("mockJson" + cast.ToString(mockRet))
	t.Fatalf("save or get data confused")
}

// 		"Application": -> app_id {"Clusters.Nodes", "Interfaces.Proxys", "Entitys"},
// 		"Cluster": -> cluster_id    {"Nodes"},
// 		"Interfaces": -> interface_id {"Proxys"},

func getTestData() {
	r = rand.New(rand.NewSource(time.Now().Unix()))
	var i int

	mockData.Applications = make([]*Application, 0)
	mockData.Clusters = make([]*Cluster, 0)
	mockData.Proxys = make([]*Proxy, 0)
	mockData.Interfaces = make([]*Interface, 0)
	mockData.Nodes = make([]*Node, 0)
	mockData.Entitys = make([]*Entity, 0)

	for i = 1; i <= mockAppNum; i++ {
		mockData.Applications = append(mockData.Applications,
			&Application{
				AppId:      mockAppIdPre + cast.ToString(i),
				AppName:    randString(10),
				Product:    randString(10),
				Type:       randString(10),
				AppMode:    randString(10),
				Pool:       randString(10),
				Azone:      randString(10),
				Rzone:      randString(10),
				VpcId:      randString(10),
				UserId:     randString(10),
				ZkHost:     randString(10),
				Status:     randString(10),
				Properties: randString(10),
				CreateTime: time.Time{},
				UpdateTime: time.Time{},
				DeleteTime: time.Time{},
				SemiStatus: randString(10),
				Clusters:   make([]*Cluster, 0),
				Interfaces: make([]*Interface, 0),
				Entitys:    make([]*Entity, 0),
			})
	}

	for i = 1; i <= mockClusterNum; i++ {
		mockData.Clusters = append(mockData.Clusters, &Cluster{
			ClusterId:          mockClusterIdPre + cast.ToString(i),
			AppId:              mockAppIdPre + "1",
			Engine:             randString(10),
			EngineVersion:      randString(10),
			EngineMinorVersion: randString(10),
			Port:               0,
			XagentSyncPort:     0,
			NodeRelation:       randString(10),
			MemSize:            0,
			ActualMemSize:      0,
			DiskSize:           0,
			ActualDiskSize:     0,
			DiskUsed:           0,
			Cpu:                0,
			ActualCpu:          0,
			Status:             randString(10),
			DestStatus:         randString(10),
			Remark:             randString(10),
			Properties:         randString(10),
			ExpireTime:         time.Time{},
			CreateTime:         time.Time{},
			UpdateTime:         time.Time{},
			DeleteTime:         time.Time{},
			BnsPath:            randString(10),
			Bns:                randString(10),
			Nodes:              make([]*Node, 0),
		})
	}

	for i = 1; i <= mockNodeNum; i++ {
		mockData.Nodes = append(mockData.Nodes, &Node{
			AppId:            mockAppIdPre + "1",
			ClusterId:        mockClusterIdPre + "1",
			NodeId:           mockNodeIdPre + cast.ToString(i),
			ContainerId:      randString(10),
			Engine:           randString(10),
			EngineVersion:    randString(10),
			Port:             0,
			Basedir:          randString(10),
			Datadir:          randString(10),
			InnodbBufferSize: 0,
			Ip:               randString(10),
			XagentPort:       0,
			Region:           randString(10),
			Azone:            randString(10),
			VpcId:            randString(10),
			SubnetId:         randString(10),
			Pool:             randString(10),
			Tags:             randString(10),
			Role:             randString(10),
			DestRole:         randString(10),
			Status:           randString(10),
			DestStatus:       randString(10),
			TaskId:           0,
			Properties:       randString(10),
		})
	}

	for i = 1; i <= mockInterfaceNum; i++ {
		mockData.Interfaces = append(mockData.Interfaces, &Interface{
			InterfaceId:    mockInterfaceIdPre + cast.ToString(i),
			AccessType:     randString(10),
			Engine:         randString(10),
			EngineVersion:  randString(10),
			Port:           0,
			AppId:          mockAppIdPre + "1",
			ProxyRelation:  randString(10),
			MemSize:        0,
			ActualMemSize:  0,
			DiskSize:       0,
			ActualDiskSize: 0,
			DiskUsed:       0,
			Cpu:            0,
			ActualCpu:      0,
			Status:         randString(10),
			Remark:         randString(10),
			Properties:     randString(10),
			CreateTime:     time.Time{},
			UpdateTime:     time.Time{},
			DeleteTime:     time.Time{},
			BnsPath:        randString(10),
			Bns:            randString(10),
			Access:         randString(10),
			Proxys:         make([]*Proxy, 0),
		})
	}

	for i = 1; i <= mockProxyNum; i++ {
		mockData.Proxys = append(mockData.Proxys, &Proxy{
			ProxyId:     mockProxyIdPre + cast.ToString(i),
			Ip:          randString(10),
			Port:        0,
			Region:      randString(10),
			Azone:       randString(10),
			VpcId:       randString(10),
			XagentPort:  0,
			AppId:       mockAppIdPre + "1",
			Properties:  randString(10),
			Basedir:     randString(10),
			Status:      randString(10),
			DestStatus:  randString(10),
			ContainerId: randString(10),
			InterfaceId: randString(10),
		})
	}

	for i = 1; i <= mockEntityNum; i++ {
		mockData.Entitys = append(mockData.Entitys,
			&Entity{
				Name:       randString(10),
				AppId:      mockAppIdPre + "1",
				ParentName: randString(10),
				Type:       randString(10),
				Status:     randString(10),
				Remark:     randString(10),
				Etag:       0,
				CreateTime: time.Time{},
				UpdateTime: time.Time{},
				Properties: randString(10),
				Charset:    randString(10),
			})
	}

	mockData.Clusters[0].Nodes = mockData.Nodes
	mockData.Applications[0].Clusters = mockData.Clusters

	mockData.Interfaces[0].Proxys = mockData.Proxys
	mockData.Applications[0].Interfaces = mockData.Interfaces

	mockData.Applications[0].Entitys = mockData.Entitys

}

func randString(len int) string {
	bytes := make([]byte, len)
	for i := 0; i < len; i++ {
		b := r.Intn(26) + 65
		bytes[i] = byte(b)
	}
	return string(bytes)
}
