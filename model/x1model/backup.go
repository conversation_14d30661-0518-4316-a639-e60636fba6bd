// Package x1model
package x1model

import (
	"context"
	"time"
)

const AppBackupDDL = `
-- auto-generated definition
CREATE TABLE app_backup (
	id bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
	app_id varchar(64) NOT NULL DEFAULT '' COMMENT 'app_id',
	app_backup_id varchar(200) NOT NULL DEFAULT '' COMMENT 'app_backup_id',
	status varchar(64) NOT NULL DEFAULT '' COMMENT 'status',
	backup_type varchar(64) NOT NULL DEFAULT '' COMMENT 'backup_type',
	comment varchar(1000) NOT NULL DEFAULT '' COMMENT 'comment',
	expairation int(11) NOT NULL DEFAULT '0' COMMENT 'expairation',
	start_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'start_time',
	PRIMARY KEY (id)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='app_backup'
`

const AppBackupItemDDL = `
-- auto-generated definition
CREATE TABLE app_backup_item (
	id bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
	app_backup_id varchar(200) NOT NULL DEFAULT '' COMMENT 'app_backup_id',
	backup_id varchar(200) NOT NULL DEFAULT '' COMMENT 'backup_id',
	shard_id varchar(64) NOT NULL DEFAULT '' COMMENT 'shard_id',
	start_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'start_time',
	duration int(11) NOT NULL DEFAULT '0' COMMENT 'duration',
	status varchar(64) NOT NULL DEFAULT '' COMMENT 'status',
	bucket varchar(200) NOT NULL DEFAULT '' COMMENT 'bucket',
	object_key varchar(200) NOT NULL DEFAULT '' COMMENT 'object_key',
	access varchar(2000) NOT NULL DEFAULT '' COMMENT 'access',
	object_size bigint(20) NOT NULL DEFAULT '0' COMMENT 'object_size',
	PRIMARY KEY (id)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='app_backup_item'
`

// 备份状态、备份类型预定义
const (
	BackupUnknown = "unknown" // 未知
	BackupSuccess = "success" // 备份成功
	BackupDoing   = "doing"   // 备份中
	BackupFailed  = "failed"  // 备份失败
	BackupExpired = "expired" // 备份过期
	BackupDeleted = "delete"  // 备份已删除
)

const (
	BackupModeAutomatic  = "auto"    // 例行备份      csmaster:0
	BackupModeManual     = "manual"  // 手动备份      csmaster:1
	BackupModeForAnalyze = "analyze" // 大key分析备份  csmaster:2
	BackupModeUnknown    = "unknown" // 手动备份       csmaster:3
)

type AppBackup struct {
	Id             int64            `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	AppID          string           `gorm:"column:app_id;NOT NULL" json:"app_id"`
	AppBackupID    string           `gorm:"column:app_backup_id;NOT NULL" json:"app_backup_id"`
	Status         string           `gorm:"column:status;NOT NULL" json:"status"`
	BackupType     string           `gorm:"column:backup_type;NOT NULL" json:"backup_type"`
	Comment        string           `gorm:"column:comment;NOT NULL" json:"comment"`
	Expairation    int64            `gorm:"column:expairation;NOT NULL" json:"expairation"`
	StartTime      time.Time        `gorm:"column:start_time;NOT NULL" json:"start_time"`
	AppBackupItems []*AppBackupItem `gorm:"foreignKey:AppBackupID;references:AppBackupID"`
}

func (AppBackup) TableName() string {
	return "app_backup"
}

type AppBackupItem struct {
	Id          int64     `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	AppBackupID string    `gorm:"column:app_backup_id;NOT NULL" json:"app_backup_id"`
	BackupID    string    `gorm:"column:backup_id;NOT NULL" json:"backup_id"`
	ShardID     string    `gorm:"column:shard_id;NOT NULL" json:"shard_id"`
	StartTime   time.Time `gorm:"column:start_time;NOT NULL" json:"start_time"`
	Duration    int64     `gorm:"column:duration;NOT NULL" json:"duration"`
	Status      string    `gorm:"column:status;NOT NULL" json:"status"`
	Bucket      string    `gorm:"column:bucket;NOT NULL" json:"bucket"`
	ObjectKey   string    `gorm:"column:object_key;NOT NULL" json:"object_key"`
	Access      string    `gorm:"column:access;NOT NULL" json:"access"`
	ObjectSize  int64     `gorm:"column:object_size;NOT NULL" json:"object_size"`
}

func (AppBackupItem) TableName() string {
	return "app_backup_item"
}

func GetBackupByAppId(ctx context.Context, appId string) ([]*AppBackup, error) {
	var appBackup []*AppBackup
	err := resource.GetAllByCond(ctx, &appBackup, "app_id = ?", appId)
	if err != nil {
		return nil, err
	}
	return appBackup, nil
}

func GetBackupByAppBackupId(ctx context.Context, appBackupID string) ([]*AppBackup, error) {
	var appBackup []*AppBackup
	err := resource.GetAllByCond(ctx, &appBackup, "app_backup_id = ?", appBackupID)
	if err != nil {
		return nil, err
	}
	return appBackup, nil
}

func GetBackupItemById(ctx context.Context, ID string) ([]*AppBackupItem, error) {
	var appBackupItem []*AppBackupItem
	err := resource.GetAllByCond(ctx, &appBackupItem, "id = ?", ID)
	if err != nil {
		return nil, err
	}
	return appBackupItem, nil
}

func GetBackupItemByObjectKey(ctx context.Context, objectKey string) ([]*AppBackupItem, error) {
	var appBackupItem []*AppBackupItem
	err := resource.GetAllByCond(ctx, &appBackupItem, "object_key = ?", objectKey)
	if err != nil {
		return nil, err
	}
	return appBackupItem, nil
}

func GetValidBackupByAppId(ctx context.Context, appID string) ([]*AppBackup, error) {
	var appBackup []*AppBackup
	err := resource.GetAllByCond(ctx, &appBackup, "app_id = ? and status in ('success','doing','failed')", appID)
	if err != nil {
		return nil, err
	}
	return appBackup, nil
}

func GetValidBackupByAppBackupId(ctx context.Context, appBackupID string) ([]*AppBackup, error) {
	var appBackup []*AppBackup
	err := resource.GetAllByCond(ctx, &appBackup, "app_backup_id = ? and status in ('success','doing','failed')", appBackupID)
	if err != nil {
		return nil, err
	}
	return appBackup, nil
}

func SaveBackup(ctx context.Context, dataPtr []*AppBackup) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}

func GetBackupStatus(backupModelStatus string) int {
	switch backupModelStatus {
	case "success":
		return 1
	case "doing":
		return 2
	case "failed":
		return 3
	case "expired":
		return 4
	case "delete":
		return 4
	default:
		return 1
	}

}

func GetBackupType(backupType string) int {
	switch backupType {
	case "auto":
		return 0
	case "manual":
		return 1
	case "analyze":
		return 2
	default:
		return 4
	}
}
