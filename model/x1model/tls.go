// Package x1model
package x1model

import (
	"context"
	"time"
)

const RedisTlsDDL = `
-- auto-generated definition
CREATE TABLE redis_tls (
	id int(11) NOT NULL AUTO_INCREMENT,
	app_id varchar(64) NOT NULL DEFAULT '',
	cert_pem varchar(1024) NOT NULL DEFAULT '',
	cert_pem_md5 varchar(1024) NOT NULL DEFAULT '',
	cert_key_pem varchar(1024) NOT NULL DEFAULT '',
	cert_key_pem_md5 varchar(1024) NOT NULL DEFAULT '',
	create_at datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
	update_at datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
	status varchar(32) NOT NULL DEFAULT '',
	PRIMARY KEY (id),
	KEY idx_redis_tls_app_id (app_id)
  ) ENGINE=InnoDB AUTO_INCREMENT=219 DEFAULT CHARSET=utf8 COMMENT='redis tls infos'
`

const (
	TlsStatusToCreate = "tocreate" // 生效中
	TlsStatusInUse    = "inuse"    // 使用中
	TlsStatusToDelete = "todelete" // 即将删除
	TlsStatusDeleted  = "deleted"  // 失效
)

// redis tls加密信息表
type RedisTls struct {
	Id            int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	AppID         string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	CertPem       string    `gorm:"column:cert_pem;NOT NULL" json:"cert_pem"`
	CertPemMd5    string    `gorm:"column:cert_pem_md5;NOT NULL" json:"cert_pem_md5"`
	CertKeyPem    string    `gorm:"column:cert_key_pem;NOT NULL" json:"cert_key_pem"`
	CertKeyPemMd5 string    `gorm:"column:cert_key_pem_md5;NOT NULL" json:"cert_key_pem_md5"`
	CreateAt      time.Time `gorm:"column:create_at;NOT NULL" json:"create_at"`
	UpdateAt      time.Time `gorm:"column:update_at;NOT NULL" json:"update_at"`
	CertExpireAt  time.Time `gorm:"column:cert_expire_time;NOT NULL" json:"cert_expire_at"`
	Status        string    `gorm:"column:status;NOT NULL" json:"status"`
}

func (RedisTls) TableName() string {
	return "redis_tls"
}

func RedisTlsGetByAppId(ctx context.Context, appId string) ([]*RedisTls, error) {
	var tls []*RedisTls
	err := resource.GetAllByCond(ctx, &tls, "app_id = ?", appId)
	if err != nil {
		return nil, err
	}
	return tls, nil
}

func RedisTlsSave(ctx context.Context, dataPtr []*RedisTls) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
