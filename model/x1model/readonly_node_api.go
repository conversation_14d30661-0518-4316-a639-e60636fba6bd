/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/18 <EMAIL> Exp
 *
 **************************************************************************/

package x1model

import "context"

// RoNodeGetByNodeID get one ro node
func RoNodeGetByNodeID(ctx context.Context, nodeID string) (*RoNode, error) {
	var node RoNode
	err := resource.GetOneByUkey(ctx, nodeID, &node)
	if err != nil {
		return nil, err
	}
	return &node, nil
}

// RoNodeGetByCond get readonly by condition
func RoNodeGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*RoNode, error) {
	var node RoNode
	err := resource.GetOneByCond(ctx, &node, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &node, nil
}

// RoNodeGetAllByCond get all readonly ro by condition
func RoNodeGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*RoNode, error) {
	var nodes []*RoNode
	err := resource.GetAllByCond(ctx, &nodes, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return nodes, nil
}

// RoNodeDeleteMulti delte ro instance
func RoNodeDeleteMulti(ctx context.Context, datas interface{}) error {
	return resource.DeleteMulti(ctx, datas)
}

// RoNodesSave save
func RoNodesSave(ctx context.Context, dataPtr []*RoNode) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
