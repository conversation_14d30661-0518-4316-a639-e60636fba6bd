/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/06
 * File: cluster_api.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package x1model TODO package function desc
package x1model

import "context"

func ClusterGetByClusterId(ctx context.Context, clusterId string) (*Cluster, error) {
	var cluster Cluster
	err := resource.GetOneByUkey(ctx, clusterId, &cluster)
	if err != nil {
		return nil, err
	}
	return &cluster, nil
}

func ClusterGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*Cluster, error) {
	var cluster Cluster
	err := resource.GetOneByCond(ctx, &cluster, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &cluster, nil
}

func ClusterGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*Cluster, error) {
	var clusters []*Cluster
	err := resource.GetAllByCond(ctx, &clusters, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return clusters, nil
}

func ClustersGetByAppId(ctx context.Context, appId string) ([]*Cluster, error) {
	var clusters []*Cluster
	err := resource.GetAllByCond(ctx, &clusters, "app_id = ?", appId)
	if err != nil {
		return nil, err
	}
	return clusters, nil
}

func ClusterSave(ctx context.Context, dataPtr []*Cluster) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}

func ClusterDeleteMulti(ctx context.Context, clusters []*Cluster) error {
	return resource.DeleteMulti(ctx, clusters)
}
