/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (<EMAIL>)
 * Date: 2021/12/03
 * File: application_api.go
 */

/*
 * DESCRIPTION
 *   Config操作方法
 */

// Package x1model
package x1model

import (
	"context"
)

func ConfigGetByAppId(ctx context.Context, appId string) (*Config, error) {
	var config Config
	err := resource.GetOneByUkey(ctx, appId, &config)
	if err != nil {
		return nil, err
	}
	return &config, nil
}

func ConfigGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*Config, error) {
	var config Config
	err := resource.GetOneByCond(ctx, &config, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &config, nil
}

func ConfigAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*Config, error) {
	var configs []*Config
	err := resource.GetAllByCond(ctx, &configs, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return configs, nil
}

func ConfigSave(ctx context.Context, dataPtr []*Config) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
