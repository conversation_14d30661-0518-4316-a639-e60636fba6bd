/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/01
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   定义枚举
 */

package x1model

const (
	Ipv6 = "ipv6"
	Ipv4 = "ipv4"
)

const (
	EngineRedis       = "redis"
	EngineBDRPRedis   = "bdrpredis"
	EngineBDRPProxy   = "bdrpproxy"
	EnginePegaDB      = "pegadb"
	EngineMc          = "memcache"
	EngineMcProxy     = "mcproxy"
	EngineVDBProxy    = "proxy"
	EngineVDBMaster   = "master"
	EngineVDBDataNode = "datanode"
)

const (
	ProductSCS = "scs"
	ProductVDB = "vdb"
)

const (
	StoreTypeDRAM = "DRAM"
	StoreTypeAEP  = "AEP"
	StoreTypeSSD  = "SSD"
	// StoreTypeLOCALDISK pegadb本地盘
	StoreTypeLOCALDISK = "LOCALDISK"
)

const (
	AppStatusInUse        = "inuse"
	AppStatusToDelete     = "todelete"
	AppStatusDeleted      = "deleted"
	AppStatusLeaderJoin   = "gleaderjoin"
	AppStatusFollowerJoin = "gfollowerjoin"
	AppStatusDeleteQuit   = "gdeletequit"
	AppStatusFollowerQuit = "gfollowerquit"
	AppStatusExchange     = "gexchange"
)

const (
	AppTypeStandalone     = "standalone"
	AppTypeCluster        = "cluster"
	AppTypeNoPorxyCluster = "native-cluster"
)

const (
	NodeOrProxyStatusToCreate      = "tocreate"     // 需要创建资源、绑定网络等
	NodeOrProxyStatusInUse         = "inuse"        // 使用中
	NodeOrProxyStatusToDelete      = "todelete"     // 需删除
	NodeOrProxyStatusToFakeDelete  = "tofakedelete" // 需延迟释放资源，用于故障的节点
	NodeOrProxyStatusInFault       = "infault"      // 故障中
	NodeOrProxyStatusToRestart     = "torestart"    // 待重启
	NodeOrProxyStatusToUpgrade     = "toupgrade"    // 待更新
	NodeOrProxyStatusToJoinGroup   = "gtojoingroup"
	NodeOrPorxyStatusToQuitGroup   = "gtoquitgroup"
	NodeOrPorxyStatusRestarted     = "restarted"
	NodeOrProxyStatusToAnalyze     = "toanalyze"
	NodeOrProxyStatusAnalyzed      = "analyzed"
	NodeOrProxyStatusAnalyzeFailed = "analyzefailed"
	NodeOrProxyStatusTlsOpening    = "tlsopening"
	NodeOrProxyStatusTlsClosing    = "tlsclosing"
	NodeOrProxyStatusTdeOpening    = "tdeopening"
	NodeOrProxyStatusTdeClosing    = "tdeclosing"
)

const (
	ClusterStatusInUse = "inuse" // 使用中
)

const (
	RoleTypeMaster = "master"
	RoleTypeSlave  = "slave"
	RoleTypeNone   = "none"
)

const (
	DefaultXagentPort = 7042
)

const (
	BLBTypeNormal         = "normal"
	BLBTypeReadOnly       = "readonly"
	BLBTypeApp            = "application"
	BLBTypeAppReadOnly    = "app_readonly"
	BLBTypeAppEntrance    = "app_entrance"
	BLBTypeAppToExchange  = "application_to_exchange"
	BLBTypeNormalToDelete = "normal_to_delete"
	BLBTypeAppToDelete    = "application_to_delete"
)

const (
	BLBStatusAvailable = "available"
	BLBStatusPaused    = "paused"
	BLBStatusDeleted   = "deleted"
	BLBStatusToDelete  = "deleting"
	BLBStatusToCreate  = "creating"
)

const (
	ACLStatusToCreate = "tocreate" // 生效中
	ACLStatusInUse    = "inuse"    // 使用中
	ACLStatusToDelete = "todelete" // 即将删除
	ACLStatusDeleted  = "deleted"  // 失效
	ACLStatusToUpdate = "toupdate" // 修改中
)

const DefaultACLUser = "default"

const (
	ConfigTypeRedis = "redis"
	ConfigTypeProxy = "proxy"
)

const (
	BackupStatusDoing = "doing"
	BackupStatusSucc  = "succ"
	BackupStatusError = "error"
)

// readonly instance status definiton
const (
	RoInstNoOperation = iota
	RoInstCreating
	RoInstAvailable
	RoInstCreateFail
	RoInstDeleting
	RoInstDeleted
	RoInstUpdating
)

// ReadonlyGroup status definition
const (
	RoGroupNoOperation = iota
	RoGroupCreating
	RoGroupAvailable
	RoGroupCreateFail
	RoGroupDeleting
	RoGroupDeleted
	RoGroupUpdating
	RoGroupAddMemberFail
)

// Readonly rebalance switch flag
const (
	RebalanceClose = iota
	RebalanceOpen
)

const (
	AppPropertiesNoSyncBlockFailover = "NoSyncBlockFailover"
)

const (
	EipBlbInternalType  = "BLB"
	EipSnicInternalType = "SNIC"
)

const (
	NamespaceStatusOnline      = "online"
	NamespaceStatusOnlining    = "onlining"
	NamespaceStatusOfflining   = "offlining"
	NamespaceStatusOffline     = "offline"
	NamespaceStatusPurged      = "purged"
	NamespaceStatusIngesting   = "ingesting"
	NamespaceStatusReIngesting = "reingesting"
)

const (
	NamespaceTaskStatusWaiting = "waiting"
	NamespaceTaskStatusRunning = "running"
	NamespaceTaskStatusSuccess = "success"
	NamespaceTaskStatusFail    = "fail"
	NamespaceTaskStatusCancel  = "cancel"
)

const (
	PortAliveRatioStatusInUse   = "inuse"
	PortAliveRationStatusDelete = "deleted"
	PortAliveRatioStatusNew     = "new"
)

const (
	APIFlagAll    = "all"
	APIFlagConfig = "config"
	APIFlagAcl    = "acl"
)

const (
	GlobalForCrossAzNearest = "global"
)
