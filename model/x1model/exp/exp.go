/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/03/08
 * File: exp.go
 */

/*
 * DESCRIPTION
 *   对x1model.Exp 进行封装，封装成方便使用的形态
 */

// Package exp TODO package function desc
package exp

import (
	"context"
	"encoding/json"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"reflect"
	"sort"
)

// Agent 实验平台操作实例
// 也可以绕过agent直接用x1model开发自己需要的功能或者扩展Agent接口
type Agent interface {
	SaveExps(ctx context.Context, explist []*x1model.Exp, forceUpdate bool) ([]string, error)
	GetDefaultFlag(ctx context.Context, strategyId string) (string, error)
	GetFlag(ctx context.Context, strategyId string, params map[string]interface{}) (string, error)
	getFlag(ctx context.Context, params map[string]interface{}, expRule *sortedRules) (string, error)
	isHitRule(ctx context.Context, params map[string]interface{}, rule string) (bool, error)
	getSortedRules(ctx context.Context, strategyId string) (*sortedRules, error)
	expCheck(ctx context.Context, exp *x1model.Exp) error
	NewRule(ctx context.Context, ruleItemList []RuleItem) (string, error) // 用于构造 x1model.ExpRules.Rules
}

var (
	errX1modelNotInit = errors.Errorf("Exp need x1model init first")  //需要先实例化x1model
	errMissLogger     = errors.Errorf("need logger")                  //必须要logger
	errInvalidRanking = errors.Errorf("invalid ranking")              //rule中设置的ranking不合法
	errRuleNotFound   = errors.Errorf("rule not found in map")        //通过ranking找不到对应的Rule
	errRuleDecodeFail = errors.Errorf("unmarshal rule into map fail") //解析rule失败

	errParamConvertFail    = errors.Errorf("convert param type fail") //用户传入的参数转换类型错误
	errRuleItemCheckFail   = errors.Errorf("rule item check fail")    //检查是否符合规则内部错误
	errConvertRuleItemFail = errors.Errorf("convert rule item fail")  //转换规则的value到指定格式失败

	errExpCheckFail = errors.Errorf("exp is invalid") //实验规则格式有误
)

// NewExpAgent 新建实验平台实例
func NewExpAgent(ctx context.Context, logger logit.Logger) (Agent, error) {
	if !x1model.HasInited() {
		return nil, errX1modelNotInit
	}
	if logger == nil {
		return nil, errMissLogger
	}
	a := agent{logger: logger}
	return a, nil
}

type agent struct {
	logger logit.Logger
}

func (a agent) GetDefaultFlag(ctx context.Context, strategyId string) (string, error) {
	exp, err := x1model.ExpGetByStrategyId(ctx, strategyId)
	if err != nil {
		a.logger.Error(ctx, "get rules by strategy id fail",
			logit.String("strategyid", strategyId),
			logit.Error("err", err))
		return "", err
	}
	a.logger.Trace(ctx, "get default flag success",
		logit.String("strategyid", strategyId),
		logit.String("strategy_record", base_utils.Format(exp)))
	return exp.DefaultFlag, nil
}

func (a agent) GetFlag(ctx context.Context, strategyId string, params map[string]interface{}) (string, error) {
	expRule, err := a.getSortedRules(ctx, strategyId)
	if err != nil {
		a.logger.Error(ctx, "get rules by strategy id fail",
			logit.String("strategyid", strategyId),
			logit.Error("err", err))
		return "", err
	}

	flag, err := a.getFlag(ctx, params, expRule)
	if err != nil {
		a.logger.Error(ctx, "check is hit rule fail",
			logit.String("strategyid", strategyId),
			logit.String("params", base_utils.Format(params)),
			logit.String("rules", base_utils.Format(expRule.Exp.Rules)),
			logit.Error("err", err))
		return "", err
	}
	return flag, nil
}

type sortedRules struct {
	Exp   *x1model.Exp
	Rules []*x1model.ExpRules
}

func (a agent) getSortedRules(ctx context.Context, strategyId string) (*sortedRules, error) {
	// todo 先尝试从redis取
	exp, err := x1model.ExpGetByStrategyId(ctx, strategyId)
	if err != nil {
		a.logger.Error(ctx, "get exp from db fail",
			logit.String("strategyid", strategyId),
			logit.Error("err", err))
		return nil, err
	}

	mapRule := make(map[int]*x1model.ExpRules, 0)
	rankings := make([]int, 0)

	for _, rule := range exp.Rules {
		// 因为要根据ranking排序，不允许相同的ranking，直接报错
		if _, ok := mapRule[rule.Ranking]; ok {
			a.logger.Error(ctx, "ranking should not same",
				logit.String("strategyId", strategyId),
				logit.String("rules", base_utils.Format(exp.Rules)))
			return nil, errInvalidRanking
		}
		rankings = append(rankings, rule.Ranking)
	}
	sort.Ints(rankings)

	rules := make([]*x1model.ExpRules, 0)
	for _, ranking := range rankings {
		rule, ok := mapRule[ranking]
		if !ok {
			a.logger.Error(ctx, "can find this ranking in map",
				logit.String("ranking", cast.ToString(ranking)),
				logit.String("rule map", base_utils.Format(mapRule)))
			return nil, errRuleNotFound
		}
		rules = append(rules, rule)
	}

	// todo 写回redis

	ret := sortedRules{
		Exp:   exp,
		Rules: rules,
	}
	a.logger.Trace(ctx, "get sorted exp rule success",
		logit.String("strategyId", strategyId),
		logit.String("sorted rules", base_utils.Format(ret)))
	return &ret, nil
}

// getFlag 比对 Rules和用户传入的params
func (a agent) getFlag(ctx context.Context, params map[string]interface{}, expRule *sortedRules) (string, error) {
	for _, rule := range expRule.Rules {
		isHit, err := a.isHitRule(ctx, params, rule.Rules)
		if err != nil {
			a.logger.Error(ctx, "check is hit single rule fail",
				logit.String("params", base_utils.Format(params)),
				logit.String("rule", base_utils.Format(rule)),
				logit.Error("err", err))
			return "", err
		}
		if isHit {
			a.logger.Trace(ctx, "hit rule",
				logit.String("params", base_utils.Format(params)),
				logit.String("hited rule", base_utils.Format(rule)))
			return rule.Flag, nil
		}
	}
	a.logger.Trace(ctx, "hit no rule,return default flag",
		logit.String("params", base_utils.Format(params)),
		logit.String("default flag", expRule.Exp.DefaultFlag))
	return expRule.Exp.DefaultFlag, nil
}

// isHitRule 比对某一条规则是否命中
func (a agent) isHitRule(ctx context.Context, params map[string]interface{}, rule string) (bool, error) {
	sliceRules := make([]RuleItem, 0)
	err := json.Unmarshal([]byte(rule), &sliceRules)
	if err != nil {
		a.logger.Error(ctx, "unmarshal rule into map fail",
			logit.String("rule", rule),
			logit.Error("err", err))
		return false, errRuleDecodeFail
	}
	// 逐条规则检查，某一条失败就返回false
	for _, val := range sliceRules {
		paramVal, ok := params[val.Key]
		// 在用户用来比对的参数中，压根没有这个key，直接返回，不需要继续比对了
		if !ok {
			return false, nil
		}

		// 第一类，数字，全都转成double处理
		if base_utils.IsNumeric(paramVal) {
			f64PV, err := cast.ToFloat64E(paramVal)
			if err != nil {
				a.logger.Error(ctx, "convert param to double fail",
					logit.String("paramVal", cast.ToString(paramVal)))
				return false, errParamConvertFail
			}
			f64Rule, err := val.ConvertToF64Item()
			if err != nil {
				a.logger.Error(ctx, "convert rule into f64 fail",
					logit.String("rule", base_utils.Format(val)),
					logit.Error("err", err))
				return false, errConvertRuleItemFail
			}
			ruleChecker := &f64RuleChecker{
				Params: f64PV,
				Rule:   f64Rule,
			}
			isHit, err := ruleChecker.Check(ctx)
			if err != nil {
				a.logger.Error(ctx, "check rule fail",
					logit.String("param", cast.ToString(paramVal)),
					logit.String("rule", base_utils.Format(val)))
				return false, errRuleItemCheckFail
			}
			if !isHit {
				return false, nil
			}
			continue
		}

		// 第二类，布尔
		if reflect.ValueOf(paramVal).Kind() == reflect.Bool {
			boolPV, err := cast.ToBoolE(paramVal)
			if err != nil {
				a.logger.Error(ctx, "convert param to bool fail",
					logit.String("paramVal", cast.ToString(paramVal)))
				return false, errParamConvertFail
			}
			bRule, err := val.ConvertToBoolItem()
			if err != nil {
				a.logger.Error(ctx, "convert rule into bool fail",
					logit.String("rule", base_utils.Format(val)),
					logit.Error("err", err))
				return false, errConvertRuleItemFail
			}
			ruleChecker := &boolRuleChecker{
				Params: boolPV,
				Rule:   bRule,
			}
			isHit, err := ruleChecker.Check(ctx)
			if err != nil {
				a.logger.Error(ctx, "check rule fail",
					logit.String("param", cast.ToString(paramVal)),
					logit.String("rule", base_utils.Format(val)))
				return false, errRuleItemCheckFail
			}
			if !isHit {
				return false, nil
			}
			continue
		}
		// 第三类，字符串
		strPV, err := cast.ToStringE(paramVal)
		if err != nil {
			a.logger.Error(ctx, "convert param to string fail",
				logit.String("paramVal", cast.ToString(paramVal)),
				logit.Error("err", err))
			return false, errParamConvertFail
		}
		sRule, err := val.ConvertToStringItem()
		if err != nil {
			a.logger.Error(ctx, "convert rule into string fail",
				logit.String("rule", base_utils.Format(val)),
				logit.Error("err", err))
			return false, errConvertRuleItemFail
		}
		ruleChecker := &stringRuleChecker{
			Params: strPV,
			Rule:   sRule,
		}
		isHit, err := ruleChecker.Check(ctx)
		if err != nil {
			a.logger.Error(ctx, "check rule fail",
				logit.String("param", cast.ToString(paramVal)),
				logit.String("rule", base_utils.Format(val)))
			return false, errRuleItemCheckFail
		}
		if !isHit {
			return false, nil
		}

	}
	//所有检查都通过就返回true
	return true, nil
}

// SaveExps 新增/更新实验规则
// explist 想新增/更新的数据
// forceUpdate 是否要强制覆盖
// 第一个返回值为成功存储的strategyId，供业务层做结果判断
//
// 若strategyId不存在，直接存储
// 若存在且 forceUpdate==False,不允许修改
// 若存在且 forceUpdate==Ture,删除所有老规则，存储新的
func (a agent) SaveExps(ctx context.Context, explist []*x1model.Exp, forceUpdate bool) ([]string, error) {
	successStrategyId := make([]string, 0)
	for _, exp := range explist {
		err := a.expCheck(ctx, exp)
		if err != nil {
			a.logger.Error(ctx, "exp to save is invalid",
				logit.String("exp", base_utils.Format(exp)),
				logit.Error("err", err))
			continue
		}

		recordsExp, err := x1model.ExpGetByStrategyId(ctx, exp.StrategyId)
		if err != nil {
			// 没找到不算问题
			if errors.Is(err, gorm.ErrRecordNotFound) {
				recordsExp = nil
			} else {
				// 除了没找到的都是问题，报错continue
				a.logger.Error(ctx, "get rules by strategy id fail",
					logit.String("strategyid", exp.StrategyId),
					logit.Error("err", err))
				continue
			}
		}
		if recordsExp != nil {
			// 原来有这个策略了
			if !forceUpdate {
				//非硬塞，不可以添加，报错continue
				a.logger.Error(ctx, "strategyId is already exist",
					logit.String("strategyid", exp.StrategyId),
					logit.String("exp in db", base_utils.Format(recordsExp)),
					logit.String("exp want to add", base_utils.Format(exp)))
				continue
			}
			// 先把老的Rules都删了，再Save新的，删除失败，报错continue
			err := x1model.FlushExpRules(ctx, recordsExp)
			if err != nil {
				a.logger.Error(ctx, "flush rules fail",
					logit.String("strategyid", exp.StrategyId),
					logit.Error("err", err))
				continue
			}
		}
		// 添加失败，报错continue
		err = x1model.ExpsSave(ctx, []*x1model.Exp{exp})
		if err != nil {
			a.logger.Error(ctx, "save rules fail",
				logit.String("strategyid", exp.StrategyId),
				logit.String("exp", base_utils.Format(exp)),
				logit.Error("err", err))
			continue
		}

		a.logger.Trace(ctx, "save exp success",
			logit.String("strategyid", exp.StrategyId),
			logit.String("exp", base_utils.Format(exp)))

		successStrategyId = append(successStrategyId, exp.StrategyId)
	}

	// todo 删除redis
	return successStrategyId, nil
}

// 规则合法性校验，合法则返回nil
func (a agent) expCheck(ctx context.Context, exp *x1model.Exp) error {
	strategyId := exp.StrategyId
	// strategyId 不能为空
	if len(strategyId) == 0 {
		a.logger.Error(ctx, "exp checkFail, strategyId cant be empty",
			logit.String("exp", base_utils.Format(exp)))
		return errExpCheckFail
	}
	// 至少需要有一个rule
	if len(exp.Rules) == 0 {
		a.logger.Error(ctx, "exp checkFail, at least need one rule",
			logit.String("strategyid", exp.StrategyId),
			logit.String("exp", base_utils.Format(exp)))
		return errExpCheckFail
	}
	for _, rule := range exp.Rules {
		// rule 的 strategyId 必须和exp一致
		if exp.StrategyId != strategyId {
			a.logger.Error(ctx, "strategyId must equal",
				logit.String("strategyid", exp.StrategyId),
				logit.String("rule", base_utils.Format(rule)))
			return errExpCheckFail
		}
		// 需要可以decode成RuleItem
		sliceRules := make([]RuleItem, 0)
		err := json.Unmarshal([]byte(rule.Rules), &sliceRules)
		if err != nil {
			a.logger.Error(ctx, "cant decode rule into RuleItem",
				logit.String("strategyid", exp.StrategyId),
				logit.String("exp", base_utils.Format(exp)))
			return errExpCheckFail
		}
		// 至少需要有一个item
		if len(sliceRules) == 0 {
			a.logger.Error(ctx, "exp checkFail, at least need one rule item",
				logit.String("strategyid", exp.StrategyId),
				logit.String("rule", base_utils.Format(rule)))
			return errExpCheckFail
		}
		for _, item := range sliceRules {
			switch item.Relation {
			// 运算符为 eq ne gt lt ge le 时必须有且仅有一个元素
			// 布尔型只支持eq和ne，其他类型没意义
			// in notin，必须有元素，否则没意义
			// 其他运算符不支持
			case "eq", "ne":
				if len(item.Value) != 1 {
					a.logger.Error(ctx, "exp checkFail, only can have one value",
						logit.String("strategyid", exp.StrategyId),
						logit.String("rule", base_utils.Format(rule)))
					return errExpCheckFail
				}
			case "gt", "lt", "ge", "le":
				if len(item.Value) != 1 {
					a.logger.Error(ctx, "exp checkFail, only can have one value",
						logit.String("strategyid", exp.StrategyId),
						logit.String("rule", base_utils.Format(rule)))
					return errExpCheckFail
				}
				if reflect.ValueOf(item.Value[0]).Kind() == reflect.Bool {
					a.logger.Error(ctx, "exp checkFail, bool not support this relation",
						logit.String("strategyid", exp.StrategyId),
						logit.String("rule", base_utils.Format(rule)))
					return errExpCheckFail
				}
			case "in", "notin":
				if len(item.Value) == 0 {
					a.logger.Error(ctx, "exp checkFail, at least need one val in this relation",
						logit.String("strategyid", exp.StrategyId),
						logit.String("rule", base_utils.Format(rule)))
					return errExpCheckFail
				}
				if reflect.ValueOf(item.Value[0]).Kind() == reflect.Bool {
					a.logger.Error(ctx, "exp checkFail, bool not support this relation",
						logit.String("strategyid", exp.StrategyId),
						logit.String("rule", base_utils.Format(rule)))
					return errExpCheckFail
				}
			default:
				a.logger.Error(ctx, "exp checkFail, not support this relation",
					logit.String("strategyid", exp.StrategyId),
					logit.String("rule", base_utils.Format(rule)))
				return errExpCheckFail
			}
		}
	}
	return nil
}

func (a agent) NewRule(ctx context.Context, ruleItemList []RuleItem) (string, error) {
	bl, err := json.Marshal(ruleItemList)
	if err != nil {
		return "", err
	}
	return string(bl), err
}
