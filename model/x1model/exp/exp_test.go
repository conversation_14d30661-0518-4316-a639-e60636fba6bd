/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/03/09
 * File: exp_test.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package exp TODO package function desc
package exp

import (
	"context"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
	"testing"
)

var rules = []*RuleItem{
	{
		Key:      "region",
		Value:    []interface{}{"bj"},
		Relation: "eq",
	},
	{
		Key:      "cluster_id",
		Value:    []interface{}{1, 2, 3, 4, 5},
		Relation: "in",
	},
	{
		Key:      "ipv6",
		Value:    []interface{}{true},
		Relation: "eq",
	},
}

var (
	testResource struct {
		modelLogger logit.Logger
		gormLogger  logit.Logger
	}
)

func TestExp(t *testing.T) {
	ctx := context.Background()
	unittest.UnitTestInit(3)
	modelLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("logit/model.toml"))
	if err != nil {
		panic(err.Error())
	}
	gormLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("logit/gorm.toml"))
	if err != nil {
		panic(err.Error())
	}
	testResource.modelLogger = modelLogger
	testResource.gormLogger = gormLogger

	x1conf := x1model.Conf{
		DbLogger:     testResource.modelLogger,
		GormLogger:   testResource.gormLogger,
		ServicerName: "",
	}
	x1model.Init(ctx, x1conf)
	expLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("logit/exp.toml"))
	if err != nil {
		panic(err.Error())
	}
	exper, err := NewExpAgent(ctx, expLogger)
	if err != nil {
		panic(err.Error())
	}
	_ = exper
}
