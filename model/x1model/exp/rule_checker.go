/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/03/09
 * File: rule_checker.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package exp TODO package function desc
package exp

import (
	"context"
	"github.com/pkg/errors"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"strings"
)

var (
	errNotImplThisRelation = errors.Errorf("not impl this relation") //这种运算符未支持
	errValNumberInvalid    = errors.Errorf("val num invalid")        //规则中设置的val数量不合法
)

// RuleChecker 规则检查器
type RuleChecker interface {
	Check(ctx context.Context) (bool, error)          //唯一暴露方法，符合本条规则返回true 反之false
	equal(ctx context.Context) (bool, error)          //eq
	notEqual(ctx context.Context) (bool, error)       //ne
	greaterThan(ctx context.Context) (bool, error)    //gt
	lessThan(ctx context.Context) (bool, error)       //lt
	greaterOrEqual(ctx context.Context) (bool, error) //ge
	lessOrEqual(ctx context.Context) (bool, error)    //le
	in(ctx context.Context) (bool, error)             //in
	notIn(ctx context.Context) (bool, error)          //not in
	valCount(ctx context.Context) int                 //获取规则中list长度
	relation(ctx context.Context) string              //获取规则中设置的运算符
}

// rule.value为[]string的规则检查器
type stringRuleChecker struct {
	Params string
	Rule   *StringRuleItem
}

func (s stringRuleChecker) relation(ctx context.Context) string {
	return s.Rule.Relation
}

func (s stringRuleChecker) valCount(ctx context.Context) int {
	return len(s.Rule.Value)
}

// Check统一入口
// 运算符为 eq ne gt lt ge le 时检查必须有且仅有一个元素，其他情况报错
func check(ctx context.Context, checker RuleChecker) (bool, error) {
	switch strings.ToLower(checker.relation(ctx)) {
	case "eq":
		if checker.valCount(ctx) != 1 {
			return false, errValNumberInvalid
		}
		return checker.equal(ctx)
	case "ne":
		if checker.valCount(ctx) != 1 {
			return false, errValNumberInvalid
		}
		return checker.notEqual(ctx)
	case "gt":
		if checker.valCount(ctx) != 1 {
			return false, errValNumberInvalid
		}
		return checker.greaterThan(ctx)
	case "lt":
		if checker.valCount(ctx) != 1 {
			return false, errValNumberInvalid
		}
		return checker.lessThan(ctx)
	case "ge":
		if checker.valCount(ctx) != 1 {
			return false, errValNumberInvalid
		}
		return checker.greaterOrEqual(ctx)
	case "le":
		if checker.valCount(ctx) != 1 {
			return false, errValNumberInvalid
		}
		return checker.lessOrEqual(ctx)
	case "in":
		return checker.in(ctx)
	case "notin":
		return checker.notIn(ctx)

	default:
		return false, errNotImplThisRelation
	}
}

func (s stringRuleChecker) Check(ctx context.Context) (bool, error) {
	return check(ctx, s)
}

func (s stringRuleChecker) equal(ctx context.Context) (bool, error) {
	return s.Params == s.Rule.Value[0], nil
}

func (s stringRuleChecker) notEqual(ctx context.Context) (bool, error) {
	return s.Params != s.Rule.Value[0], nil
}

func (s stringRuleChecker) greaterThan(ctx context.Context) (bool, error) {
	return s.Params > s.Rule.Value[0], nil
}

func (s stringRuleChecker) lessThan(ctx context.Context) (bool, error) {
	return s.Params < s.Rule.Value[0], nil
}

func (s stringRuleChecker) greaterOrEqual(ctx context.Context) (bool, error) {
	return s.Params >= s.Rule.Value[0], nil
}

func (s stringRuleChecker) lessOrEqual(ctx context.Context) (bool, error) {
	return s.Params <= s.Rule.Value[0], nil
}

func (s stringRuleChecker) in(ctx context.Context) (bool, error) {
	return base_utils.InArray(s.Params, s.Rule.Value)
}

func (s stringRuleChecker) notIn(ctx context.Context) (bool, error) {
	isIn, err := base_utils.InArray(s.Params, s.Rule.Value)
	if err != nil {
		return isIn, err
	}
	return !isIn, err
}

// rule.value为[]bool的规则检查器
type boolRuleChecker struct {
	Params bool
	Rule   *BoolRuleItem
}

func (b boolRuleChecker) relation(ctx context.Context) string {
	return b.Rule.Relation
}

func (b boolRuleChecker) valCount(ctx context.Context) int {
	return len(b.Rule.Value)
}

func (b boolRuleChecker) Check(ctx context.Context) (bool, error) {
	return check(ctx, b)
}

func (b boolRuleChecker) equal(ctx context.Context) (bool, error) {
	return b.Params == b.Rule.Value[0], nil
}

func (b boolRuleChecker) notEqual(ctx context.Context) (bool, error) {
	return b.Params != b.Rule.Value[0], nil
}

func (b boolRuleChecker) greaterThan(ctx context.Context) (bool, error) {
	return false, errNotImplThisRelation
}

func (b boolRuleChecker) lessThan(ctx context.Context) (bool, error) {
	return false, errNotImplThisRelation
}

func (b boolRuleChecker) greaterOrEqual(ctx context.Context) (bool, error) {
	return false, errNotImplThisRelation
}

func (b boolRuleChecker) lessOrEqual(ctx context.Context) (bool, error) {
	return false, errNotImplThisRelation
}

func (b boolRuleChecker) in(ctx context.Context) (bool, error) {
	return false, errNotImplThisRelation
}

func (b boolRuleChecker) notIn(ctx context.Context) (bool, error) {
	return false, errNotImplThisRelation
}

// rule.value为[]float64的规则检查器
type f64RuleChecker struct {
	Params float64
	Rule   *F64RuleItem
}

func (f f64RuleChecker) relation(ctx context.Context) string {
	return f.Rule.Relation
}

func (f f64RuleChecker) valCount(ctx context.Context) int {
	return len(f.Rule.Value)
}

func (f f64RuleChecker) Check(ctx context.Context) (bool, error) {
	return check(ctx, f)
}

func (f f64RuleChecker) equal(ctx context.Context) (bool, error) {
	return f.Params == f.Rule.Value[0], nil
}

func (f f64RuleChecker) notEqual(ctx context.Context) (bool, error) {
	return f.Params != f.Rule.Value[0], nil
}

func (f f64RuleChecker) greaterThan(ctx context.Context) (bool, error) {
	return f.Params > f.Rule.Value[0], nil
}

func (f f64RuleChecker) lessThan(ctx context.Context) (bool, error) {
	return f.Params < f.Rule.Value[0], nil
}

func (f f64RuleChecker) greaterOrEqual(ctx context.Context) (bool, error) {
	return f.Params >= f.Rule.Value[0], nil
}

func (f f64RuleChecker) lessOrEqual(ctx context.Context) (bool, error) {
	return f.Params <= f.Rule.Value[0], nil
}

func (f f64RuleChecker) in(ctx context.Context) (bool, error) {
	return base_utils.InArray(f.Params, f.Rule.Value)
}

func (f f64RuleChecker) notIn(ctx context.Context) (bool, error) {
	isIn, err := base_utils.InArray(f.Params, f.Rule.Value)
	if err != nil {
		return isIn, err
	}
	return !isIn, err
}
