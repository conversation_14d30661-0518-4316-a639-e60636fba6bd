/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/03/09
 * File: rule_item.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package exp TODO package function desc
package exp

import (
	"github.com/pkg/errors"
	"github.com/spf13/cast"
)

var (
	errConvertItemFail = errors.Errorf("convert rule item type fail")
)

type RuleItemInterface interface {
	ConvertToStringItem() (*StringRuleItem, error)
	ConvertToBoolItem() (*BoolRuleItem, error)
	ConvertToF64Item() (*F64RuleItem, error)
}

type RuleItem struct {
	Key      string        `json:"key"`
	Value    []interface{} `json:"value"`
	Relation string        `json:"relation"`
}

type StringRuleItem struct {
	Key      string   `json:"key"`
	Value    []string `json:"value"`
	Relation string   `json:"relation"`
}

type BoolRuleItem struct {
	Key      string `json:"key"`
	Value    []bool `json:"value"`
	Relation string `json:"relation"`
}

type F64RuleItem struct {
	Key      string    `json:"key"`
	Value    []float64 `json:"value"`
	Relation string    `json:"relation"`
}

func (i *RuleItem) ConvertToStringItem() (*StringRuleItem, error) {
	if i == nil {
		return nil, errors.Errorf("nil ptr")
	}
	ret := StringRuleItem{
		Key:      i.Key,
		Relation: i.Relation,
	}
	val := make([]string, 0)
	for _, v := range i.Value {
		sv, err := cast.ToStringE(v)
		if err != nil {
			return nil, errConvertItemFail
		}
		val = append(val, sv)
	}
	ret.Value = val
	return &ret, nil
}

func (i *RuleItem) ConvertToBoolItem() (*BoolRuleItem, error) {
	if i == nil {
		return nil, errors.Errorf("nil ptr")
	}
	ret := BoolRuleItem{
		Key:      i.Key,
		Relation: i.Relation,
	}
	val := make([]bool, 0)
	for _, v := range i.Value {
		bv, err := cast.ToBoolE(v)
		if err != nil {
			return nil, errConvertItemFail
		}
		val = append(val, bv)
	}
	ret.Value = val
	return &ret, nil
}

func (i *RuleItem) ConvertToF64Item() (*F64RuleItem, error) {
	if i == nil {
		return nil, errors.Errorf("nil ptr")
	}
	ret := F64RuleItem{
		Key:      i.Key,
		Relation: i.Relation,
	}
	val := make([]float64, 0)
	for _, v := range i.Value {
		fv, err := cast.ToFloat64E(v)
		if err != nil {
			return nil, errConvertItemFail
		}
		val = append(val, fv)
	}
	ret.Value = val
	return &ret, nil
}
