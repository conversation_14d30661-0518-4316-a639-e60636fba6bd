package x1model

import (
	"context"
	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"icode.baidu.com/baidu/scs/x1-base/model"
	"testing"
	"time"
)

func TestInjectionGetAllByCond(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").
		WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "app_id", "status", "last_injection"}).
			AddRow(1, "user1", "app1", "status1", time.Now()))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	ret, err := InjectionGetAllByCond(context.Background(), "user_id = ?", "user1")
	if err != nil {
		t.Fatalf("error GetAllByCond: %s", err)
	}
	if len(ret) != 1 {
		t.Fatalf("error GetAllByCond: %s", err)
	}
}

func TestInjectionSave(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `injection`").
		WithArgs("user1", "app1", "active", sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	data := []*Injection{
		{
			Id:            0,
			UserID:        "user1",
			AppID:         "app1",
			Status:        "active",
			LastInjection: time.Time{},
		},
	}
	if err := InjectionSave(context.Background(), data); err != nil {
		t.Fatalf("error InjectionSave: %s", err)
	}
}
