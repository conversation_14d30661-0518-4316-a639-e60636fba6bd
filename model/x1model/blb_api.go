/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (<EMAIL>)
 * Date: 2021/12/06
 * File: blb_api.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package x1model TODO package function desc
package x1model

import (
	"context"
	"time"

	"gorm.io/gorm"
)

func BLBGetByBLBId(ctx context.Context, blbId string) (*BLB, error) {
	var blb BLB
	err := resource.GetOneByUkey(ctx, blbId, &blb)
	if err != nil {
		return nil, err
	}
	return &blb, nil
}

func BLBGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*BLB, error) {
	var blb BLB
	err := resource.GetOneByCond(ctx, &blb, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &blb, nil
}

func BLBGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*BLB, error) {
	var blbList []*BLB
	err := resource.GetAllByCond(ctx, &blbList, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return blbList, nil
}

func BLBsSave(ctx context.Context, blbList []*BLB) error {
	return resource.FullSaveAssociationsSave(ctx, blbList)
}

func BlbDelete(ctx context.Context, blbs []*BLB) error {
	var toDelete []*BLBToDelete
	for _, b := range blbs {
		toDelete = append(toDelete, &BLBToDelete{
			AppId:                  b.AppId,
			Name:                   b.Name,
			Type:                   b.Type,
			VpcId:                  b.VpcId,
			SubnetId:               b.SubnetId,
			IpType:                 b.IpType,
			BgwGroupId:             b.BgwGroupId,
			BgwGroupExclusive:      b.BgwGroupExclusive,
			BgwGroupMode:           b.BgwGroupMode,
			MasterAZ:               b.MasterAZ,
			SlaveAZ:                b.SlaveAZ,
			BlbId:                  b.BlbId,
			Vip:                    b.Vip,
			Ovip:                   b.Ovip,
			Ipv6:                   b.Ipv6,
			Status:                 b.Status,
			CreateAt:               b.CreateAt,
			UpdateAt:               b.UpdateAt,
			RoGroupID:              b.RoGroupID,
			IPGroupID:              b.IPGroupID,
			ResourceUserId:         b.ResourceUserId,
			ResourceVpcId:          b.ResourceVpcId,
			ResourceSubnetId:       b.ResourceSubnetId,
			ServicePublishEndpoint: b.ServicePublishEndpoint,
			EndpointId:             b.EndpointId,
			EndpointIp:             b.EndpointIp,
			McpackIPGroupID:        b.McpackIPGroupID,
			AzoneForCrossAzNearest: b.AzoneForCrossAzNearest,
			DeleteAt:               time.Now(),
		})
	}
	return resource.ModelGorm.Session(&gorm.Session{Context: ctx, Logger: resource.GormLoggerCommon}).Transaction(func(tx *gorm.DB) error {
		var blbIds []string
		for _, b := range blbs {
			blbIds = append(blbIds, b.BlbId)
		}
		if err := tx.Where("blb_id IN ?", blbIds).Delete(blbs).Error; err != nil {
			return err
		}
		if err := tx.Save(toDelete).Error; err != nil {
			return err
		}
		return nil
	})
}
