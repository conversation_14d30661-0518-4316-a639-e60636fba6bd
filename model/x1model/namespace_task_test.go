package x1model

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"icode.baidu.com/baidu/scs/x1-base/model"
)

func TestNamespaceTaskGetAllByAppID(t *testing.T) {

	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").
		WillReturnRows(sqlmock.NewRows([]string{"id", "app_id", "task_id", "action", "namespace",
			"bos_bucket", "bos_object_prefix", "progress", "error_message", "create_at", "update_at",
			"status", "comment", "sst_file_num", "progress_percent"}).
			AddRow(1, "app_id", "task_id", "action", "namespace", "bos_bucket", "bos_object_prefix",
				"progress", "error_message", time.Now(), time.Now(), "status", "comment", 0, 0))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	ret, err := NamespaceTaskGetAllByAppID(context.Background(), "app_id")
	if err != nil {
		t.Fatalf("error NamespaceTaskGetAllByAppID: %s", err)
	}
	if len(ret) != 1 {
		t.Fatalf("error NamespaceTaskGetAllByAppID: %s", err)
	}
}

func TestNamespaceTaskGetByTaskID(t *testing.T) {

	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectQuery("SELECT .*").
		WillReturnRows(sqlmock.NewRows([]string{"id", "app_id", "task_id", "action", "namespace",
			"bos_bucket", "bos_object_prefix", "progress", "error_message", "create_at", "update_at",
			"status", "comment", "sst_file_num", "progress_percent"}).
			AddRow(1, "app_id", "task_id", "action", "namespace", "bos_bucket", "bos_object_prefix",
				"progress", "error_message", time.Now(), time.Now(), "status", "comment", 0, 0))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	ret, err := NamespaceTaskGetByTaskID(context.Background(), "task_id")
	if err != nil {
		t.Fatalf("error NamespaceTaskGetByTaskID: %s", err)
	}
	if ret.TaskID != "task_id" {
		t.Fatalf("error NamespaceTaskGetByTaskID: %s", err)
	}
}

func TestSaveNamespaceTaskAll(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `namespace_task`").
		WithArgs("appid", "task_id", sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	data := []*NamespaceTask{
		{
			AppID:  "appid",
			TaskID: "task_id",
		},
	}
	if err := NamespaceTaskSaveAll(context.Background(), data); err != nil {
		t.Fatalf("error NamespaceSaveAll: %s", err)
	}
}

func TestNamespaceTaskSave(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `namespace_task`").
		WithArgs("appid", "task_id", sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	resource = &model.Resource{
		ModelGorm: gormDB,
	}
	data := &NamespaceTask{
		AppID:  "appid",
		TaskID: "task_id",
	}
	if err := NamespaceTaskSave(context.Background(), data); err != nil {
		t.Fatalf("error NamespaceSaveAll: %s", err)
	}
}

//func TestCreateOneIngestTask(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	type args struct {
//		ctx     context.Context
//		dataPtr *NamespaceTask
//	}
//	tests := []struct {
//		name    string
//		args    args
//		wantErr bool
//	}{
//		{"no data", args{ctx: context.Background(), dataPtr: &NamespaceTask{}}, true},
//		{"nil data", args{ctx: context.Background(), dataPtr: &NamespaceTask{
//			AppID:           "test_app_id",
//			TaskID:          "test_task_id",
//			Action:          "test_action",
//			Namespace:       "test_namespace",
//			BosBucket:       "test_bos_bucket",
//			BosObjectPrefix: "test_bos_object",
//			Progress:        "test_progress",
//			ErrorMessage:    "test_error_message",
//			CreateAt:        time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC),
//			UpdateAt:        time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC),
//			Status:          NamespaceTaskStatusWaiting,
//		}}, true},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if err := NamespaceTaskSave(tt.args.ctx, tt.args.dataPtr); (err != nil) != tt.wantErr {
//				t.Errorf("NamespaceTaskSave() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}
//
//func TestGetIngestTaskByAppID(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	type args struct {
//		ctx   context.Context
//		appID string
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    []*NamespaceTask
//		wantErr bool
//	}{
//		{"no app", args{ctx: context.Background(), appID: "no app"}, make([]*NamespaceTask, 0), false},
//		{"app with task", args{ctx: context.Background(), appID: "test_app_id"}, []*NamespaceTask{&NamespaceTask{
//			ID:              6,
//			AppID:           "scs-bj-vstcprsvifjd",
//			TaskID:          "test_task_id",
//			Action:          "test_action",
//			Namespace:       "test_namespace",
//			BosBucket:       "test_bos_bucket",
//			BosObjectPrefix: "test_bos_object",
//			Progress:        "test_progress",
//			ErrorMessage:    "test_error_message1",
//			CreateAt:        time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC),
//			UpdateAt:        time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC),
//			Status:          NamespaceTaskStatusWaiting,
//		}}, false},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := NamespaceTaskGetAllByAppID(tt.args.ctx, tt.args.appID)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("NamespaceTaskGetAllByAppID() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if len(got) > 0 {
//				fmt.Printf(" got %+v, err %+v", got[0], err)
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("NamespaceTaskGetAllByAppID() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//func TestGetIngestTaskByTaskID(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	type args struct {
//		ctx    context.Context
//		taskID string
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    []*NamespaceTask
//		wantErr bool
//	}{
//		{"no app", args{ctx: context.Background(), taskID: "no app"}, make([]*NamespaceTask, 0), false},
//		{"app with task", args{ctx: context.Background(), taskID: "test_task_id"}, []*NamespaceTask{&NamespaceTask{
//			ID:              6,
//			AppID:           "test_app_id",
//			TaskID:          "test_task_id",
//			Action:          "test_action",
//			Namespace:       "test_namespace",
//			BosBucket:       "test_bos_bucket",
//			BosObjectPrefix: "test_bos_object",
//			Progress:        "test_progress",
//			ErrorMessage:    "test_error_message1",
//			CreateAt:        time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC),
//			UpdateAt:        time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC),
//			Status:          NamespaceTaskStatusWaiting,
//		}}, false},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := NamespaceTaskGetByTaskID(tt.args.ctx, tt.args.taskID)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("NamespaceTaskGetAllByAppID() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("NamespaceTaskGetAllByAppID() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//func TestSaveIngestTask(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	type args struct {
//		ctx     context.Context
//		dataPtr []*NamespaceTask
//	}
//	tests := []struct {
//		name    string
//		args    args
//		wantErr bool
//	}{
//		{"nil data", args{ctx: context.Background(), dataPtr: []*NamespaceTask{&NamespaceTask{
//			AppID:           "test_app_id",
//			TaskID:          "test_task_id",
//			Action:          "test_action",
//			Namespace:       "test_namespace",
//			BosBucket:       "test_bos_bucket",
//			BosObjectPrefix: "test_bos_object",
//			Progress:        "test_progress",
//			ErrorMessage:    "test_error_message1",
//			CreateAt:        time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC),
//			UpdateAt:        time.Date(2023, 06, 15, 18, 41, 17, 0, time.UTC),
//			Status:          NamespaceTaskStatusWaiting,
//		}}}, false},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if err := NamespaceTaskSaveAll(tt.args.ctx, tt.args.dataPtr); (err != nil) != tt.wantErr {
//				t.Errorf("NamespaceTaskSaveAll() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}
