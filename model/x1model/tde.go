// Package x1model
package x1model

import (
	"context"
	"time"
)

const RedisTdeDDL = `
-- auto-generated definition
CREATE TABLE redis_tde (
	id int(11) NOT NULL AUTO_INCREMENT,
	app_id varchar(64) NOT NULL DEFAULT '',
	tde_key varchar(500) NOT NULL DEFAULT '',
	create_at datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
	update_at datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
	status varchar(32) NOT NULL DEFAULT '',
	PRIMARY KEY (id),
	KEY idx_redis_tde_app_id (app_id)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='redis tde infos'
`

const (
	TdeStatusToCreate = "tocreate" // 生效中
	TdeStatusInUse    = "inuse"    // 使用中
)

// redis tde加密信息表
type RedisTde struct {
	Id       int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	AppID    string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	TdeKey   string    `gorm:"column:tde_key;NOT NULL" json:"tde_key"`
	CreateAt time.Time `gorm:"column:create_at;NOT NULL" json:"create_at"`
	UpdateAt time.Time `gorm:"column:update_at;NOT NULL" json:"update_at"`
	Status   string    `gorm:"column:status;NOT NULL" json:"status"`
}

func (RedisTde) TableName() string {
	return "redis_tde"
}

func RedisTdeGetByAppId(ctx context.Context, appId string) ([]*RedisTde, error) {
	var tde []*RedisTde
	err := resource.GetAllByCond(ctx, &tde, "app_id = ?", appId)
	if err != nil {
		return nil, err
	}
	return tde, nil
}

func RedisTdeSave(ctx context.Context, dataPtr []*RedisTde) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}
