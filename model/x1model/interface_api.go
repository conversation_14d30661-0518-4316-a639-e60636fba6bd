/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/06
 * File: interface_api.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package x1model TODO package function desc
package x1model

import "context"

func InterfaceGetByInterfaceId(ctx context.Context, interfaceId string) (*Interface, error) {
	var interfaceData Interface
	err := resource.GetOneByUkey(ctx, interfaceId, &interfaceData)
	if err != nil {
		return nil, err
	}
	return &interfaceData, nil
}

func InterfaceGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*Interface, error) {
	var interfaceData Interface
	err := resource.GetOneByCond(ctx, &interfaceData, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &interfaceData, nil
}

func InterfaceGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*Interface, error) {
	var interfaces []*Interface
	err := resource.GetAllByCond(ctx, &interfaces, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return interfaces, nil
}

func InterfacesSave(ctx context.Context, dataPtr []*Interface) error {
	return resource.FullSaveAssociationsSave(ctx, dataPtr)
}

func InterfaceDeleteMulti(ctx context.Context, itfs []*Interface) error {
	return resource.DeleteMulti(ctx, itfs)
}
