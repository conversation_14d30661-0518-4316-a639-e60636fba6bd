package x1model

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const (
	PkgRecordStatusInuse     = "inuse"
	PkgRecordStatusToMigrate = "tomigrate"
)

var (
	DefaultPkgs          = []string{"agent", "csagent", "cron", "monitor-agent", "xagent", "opbin"}
	RedisModules         = []string{JsonModulePkg, CascadModulePkg, BloomfilterModulePkg}
	SmartDBApkg          = "smartdba"
	PegaDBPkg            = "PegaDB2"
	BaiduRedisPkg        = "slot-redis"
	OpensourceRedisPkg   = "redis"
	ProxyPkg             = "proxy-slot"
	NewProxyPkg          = "proxy"
	CascadModulePkg      = "cascad-module"
	BloomfilterModulePkg = "bloomfilter-module"
	JsonModulePkg        = "json-module"
	SyncAgentPkg         = "sync-agent"
	NewSyncAgentPkg      = "sync-agent2"
	SstDumpBin           = "sst_dump"
)

func PackageLatestList(ctx context.Context) ([]*Package, error) {
	var dbRets []*Package
	sql := `SELECT t1.*
		FROM package t1
				 INNER JOIN (
			SELECT name, major_version, MAX(released_at) AS latest_releasetime
			FROM package
			WHERE status = 'inuse' 
			GROUP BY name, major_version
		) t2 ON t1.name = t2.name AND t1.major_version = t2.major_version AND t1.released_at = t2.latest_releasetime
		ORDER BY t1.name, t1.released_at desc;`
	if err := resource.DB(ctx).Raw(sql).Scan(&dbRets).Error; err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return dbRets, nil
}

// GetLatestPackages 从DB查询 所需包的最新发版记录
// 若majorVersionFilter不为空，则包name==majorVersionFilter的Key的包，只返回满足majorVersionFilter的Value的发版记录
// 如majorVersionFilter是 map[string]string{"redis": "2"},则Redis这个包只返回2系最新的发版记录，其他包不影响。
func GetLatestPackages(ctx context.Context, requiredPkgNames []string, majorVersionFilter map[string]string) ([]*Package, error) {
	result := make(map[string]*Package) // 处理后的记录
	var latestPackageInfo []*Package
	// 把每个包的大版本最新发版记录查出来
	sql := `SELECT t1.*
			FROM package t1
					 INNER JOIN (SELECT name, major_version, MAX(released_at) AS latest_releasetime
								 FROM package
								 WHERE status = 'inuse'
								   AND name in ?
								 GROUP BY name, major_version) t2 ON t1.name = t2.name AND t1.major_version = t2.major_version AND
																	 t1.released_at = t2.latest_releasetime
			ORDER BY t1.name, t1.released_at desc;`
	if err := resource.DB(ctx).Raw(sql, requiredPkgNames).Scan(&latestPackageInfo).Error; err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	if len(majorVersionFilter) == 0 {
		majorVersionFilter = make(map[string]string)
	}
	for _, pkgInfo := range latestPackageInfo {
		// 若majorVersionFilter有这个包名的key，则用大版本号来匹配，没有则直接放入
		if majorVersion, ok := majorVersionFilter[pkgInfo.Name]; ok {
			if _, ok := result[pkgInfo.Name]; !ok {
				if pkgInfo.MajorVersion == majorVersion {
					result[pkgInfo.Name] = pkgInfo
				}
			} else {
				if pkgInfo.MajorVersion == majorVersion {
					// 满足大版本要求的最新版本，只应该有一个，如果有多个，先报错
					return nil, fmt.Errorf("packege %s ,major_version:%s,select multiple latest release,raw result:%s",
						pkgInfo.Name, pkgInfo.MajorVersion, base_utils.Format(latestPackageInfo))
				}
			}
		} else {
			// 没有大版本限制的包，直接放进去，因为sql里用了order by，所以这里不用考虑被老版本覆盖的问题
			if _, ok := result[pkgInfo.Name]; !ok {
				result[pkgInfo.Name] = pkgInfo
			}
		}
	}
	for filterPkgName, filterMajorVersion := range majorVersionFilter {
		if _, ok := result[filterPkgName]; !ok {
			return nil, fmt.Errorf("pkg:%s, majorverison:%s not found", filterPkgName, filterMajorVersion)
		}
	}
	// 所有要求的包都要查到才算成功
	for _, needPkg := range requiredPkgNames {
		if _, ok := result[needPkg]; !ok {
			return nil, fmt.Errorf("required pkg:%s,nod found", needPkg)
		}
	}

	var rets []*Package
	for _, ret := range result {
		rets = append(rets, ret)
	}
	return rets, nil
}

func GetLatestPkgRecord(ctx context.Context, entity string, packageNames []string, status string) ([]*PackageRecord, error) {
	if status == "" {
		status = PkgRecordStatusInuse
	}
	var recordRets []*PackageRecord
	sql := `SELECT t1.*
			FROM package_record t1
					 INNER JOIN (SELECT name,entity, MAX(created_at) AS max_created_at
								 FROM package_record
								 WHERE status = ?
								   AND name in ?
								   AND entity = ?
								 GROUP BY name) t2 ON t1.name = t2.name AND t1.created_at = t2.max_created_at AND t1.entity = t2.entity;`
	if err := resource.DB(ctx).Raw(sql, status, packageNames, entity).Scan(&recordRets).Error; err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return recordRets, nil
}

// GetCurrentPackageRecord 获取 某个entity对应的最新一组包应用记录
// 根据created_at子段排序出来的最新一条
func GetCurrentPackageRecord(ctx context.Context, entity string, packageNames []string, status string) ([]*Package, error) {
	recordRets, err := GetLatestPkgRecord(ctx, entity, packageNames, status)
	if err != nil {
		return nil, err
	}
	var packageIds []string
	for _, recordRet := range recordRets {
		packageIds = append(packageIds, recordRet.PackageID)
	}
	var rets []*Package
	if err := resource.GetAllByCondNoTx(ctx, &rets, "package_id in ?", packageIds); err != nil {
		return nil, err
	}
	return rets, nil
}

// UpgradeEntityPackegeRecord 维护这个节点的包版本记录
func UpgradeEntityPackegeRecord(ctx context.Context, entity string, packageNames []string,
	toDeployPkgs []*Package, ownerTaskID string) error {
	currentRecords, err := GetCurrentPackageRecord(ctx, entity, packageNames, "")
	if err != nil {
		return err
	}

	var newRecords []*Package
	for _, toDeployPkg := range toDeployPkgs {
		var found bool
		for _, currentRecord := range currentRecords {
			if currentRecord.PackageID == toDeployPkg.PackageID {
				found = true
				break
			}
		}
		if !found {
			newRecords = append(newRecords, toDeployPkg)
		}
	}
	var toAddRecords []*PackageRecord
	for _, pkg := range newRecords {
		toAddRecords = append(toAddRecords, &PackageRecord{
			Entity:    entity,
			Name:      pkg.Name,
			Type:      getEntityType(entity),
			PackageID: pkg.PackageID,
			Status:    "inuse",
			CreatedAt: time.Time{},
			Owner:     ownerTaskID,
		})
	}
	if len(toAddRecords) > 0 {
		if err := resource.FullSaveAssociationsSave(ctx, &toAddRecords); err != nil {
			return err
		}
	}
	return nil
}

func getEntityType(entity string) string {
	if strings.Contains(entity, ".") {
		if strings.Contains(entity, "-itf-") || strings.Contains(entity, "-itfop-") {
			return "proxy"
		}
		return "redis"
	}
	return "app"
}

func PackageAddRelease(ctx context.Context, dataPtr *Package) error {
	if dataPtr.Status == "" {
		dataPtr.Status = "inuse"
	}
	if dataPtr.Description == "" {
		dataPtr.Description = "no description"
	}
	if dataPtr.Owner == "" {
		dataPtr.Owner = "unknown"
	}
	if dataPtr.PackageID == "" {
		dataPtr.PackageID = dataPtr.Name + "-" + dataPtr.FullVersion
	}
	return resource.FullSaveAssociationsSave(ctx, &[]*Package{dataPtr})
}


type GetRequirePkgNameMoreParams struct {
	SyncAgentBigVersion string
}

// GetRequirePkgNameAndVersionFilter 获取所需的包名列表 与 版本限制条件
// kernel 内核类型 redis / pegadb / bdrpproxy
// appType 实例类型 cluster / standalone
func GetRequirePkgNameAndVersionFilter(kernel string, kernelMajorVersion string,
	appType string, needSmartDBA bool, needSyncAgent bool,moreParams *GetRequirePkgNameMoreParams) ([]string, map[string]string) {
	pkgsRequire := DefaultPkgs
	if needSmartDBA {
		pkgsRequire = append(pkgsRequire, SmartDBApkg)
	}
	var versionFilter map[string]string
	switch kernel {
	case EngineRedis:
		if kernelMajorVersion == "2.6" || kernelMajorVersion == "2.8" {
			if appType == AppTypeCluster {
				pkgsRequire = append(pkgsRequire, BaiduRedisPkg)
				versionFilter = map[string]string{BaiduRedisPkg: "2"}
			} else {
				pkgsRequire = append(pkgsRequire, OpensourceRedisPkg)
				versionFilter = map[string]string{OpensourceRedisPkg: "2"}
			}
		} else if kernelMajorVersion == "3.2" {
			if appType == AppTypeCluster {
				pkgsRequire = append(pkgsRequire, BaiduRedisPkg)
				versionFilter = map[string]string{BaiduRedisPkg: "3"}
			} else {
				pkgsRequire = append(pkgsRequire, OpensourceRedisPkg)
				versionFilter = map[string]string{OpensourceRedisPkg: "3"}
			}
		} else if kernelMajorVersion == "4.0" {
			pkgsRequire = append(pkgsRequire, BaiduRedisPkg)
			versionFilter = map[string]string{BaiduRedisPkg: "4"}
		} else if kernelMajorVersion == "5.0" {
			pkgsRequire = append(pkgsRequire, OpensourceRedisPkg)
			versionFilter = map[string]string{OpensourceRedisPkg: "5"}
		} else if kernelMajorVersion == "6.0" {
			pkgsRequire = append(pkgsRequire, BaiduRedisPkg)
			pkgsRequire = append(pkgsRequire, RedisModules...)
			versionFilter = map[string]string{BaiduRedisPkg: "6"}
		} else if kernelMajorVersion == "7.0" {
			pkgsRequire = append(pkgsRequire, BaiduRedisPkg)
			pkgsRequire = append(pkgsRequire, RedisModules...)
			versionFilter = map[string]string{BaiduRedisPkg: "7"}
		} else {
			pkgsRequire = append(pkgsRequire, BaiduRedisPkg)
			versionSlice := strings.Split(kernelMajorVersion, ".")
			kMVersion := kernelMajorVersion
			if len(versionSlice) > 0 {
				kMVersion = versionSlice[0]
			}
			versionFilter = map[string]string{BaiduRedisPkg: kMVersion}
		}
		if needSyncAgent {
			if moreParams != nil && moreParams.SyncAgentBigVersion == "2.0" {
				pkgsRequire = append(pkgsRequire, NewSyncAgentPkg)
			} else {
				pkgsRequire = append(pkgsRequire, SyncAgentPkg)
			}
		}
	case EnginePegaDB:
		pkgsRequire = append(pkgsRequire, PegaDBPkg, SstDumpBin)
		if strings.HasPrefix(kernelMajorVersion, "3") {
			versionFilter = map[string]string{PegaDBPkg: "3"}
		} else {
			versionFilter = map[string]string{PegaDBPkg: "2"}
		}
		if needSyncAgent {
			if moreParams != nil && moreParams.SyncAgentBigVersion == "2.0" {
				pkgsRequire = append(pkgsRequire, NewSyncAgentPkg)
			} else {
				pkgsRequire = append(pkgsRequire, SyncAgentPkg)
			}
		}
	case EngineBDRPProxy:
		if kernelMajorVersion == "4" {
			pkgsRequire = append(pkgsRequire, NewProxyPkg)
		} else {
			pkgsRequire = append(pkgsRequire, ProxyPkg)
		}
	}
	return pkgsRequire, versionFilter
}

// 因为需要版本固定，这两个老方法可能存在问题，这个注释只做存档，有需要请看之前的commit
// PackageRecordGetForDeploy 部署全新节点使用的方法，并内部维护这个节点的版本记录信息
// PackageRecordUpdateCurrent 升级节点并维护这个节点的，并内部维护这个节点的版本记录信息

// GetGreyBoxPackages 获得灰度版本包记录
func GetGreyBoxPackages(ctx context.Context, packageName string, greyboxTag string) ([]*Package, error) {
	var greyboxPackageInfo []*Package
	if len(greyboxTag) == 0 {
		return greyboxPackageInfo, nil
	}
	sql := `SELECT t1.*
			FROM package t1
					 INNER JOIN (SELECT name, major_version,tags, MAX(id) AS latest_id
								 FROM package
								 WHERE status = 'inuse'
								   AND name = ?
                                   AND tags = ?
								 GROUP BY name, major_version) t2 ON t1.name = t2.name AND t1.major_version = t2.major_version AND
																	 t1.id = t2.latest_id AND t1.tags = t2.tags
			ORDER BY t1.name, t1.id desc;`
	if err := resource.DB(ctx).Raw(sql, packageName, greyboxTag).Scan(&greyboxPackageInfo).Error; err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return greyboxPackageInfo, nil
}

// GetRealVersion
// 获取DeployTime最大的记录
func GetRealVersion(ctx context.Context, entity string, packageNames []string, status string) ([]*Package, error) {
	if status == "" {
		status = PkgRecordStatusInuse
	}
	var recordRets []*PackageRecord
	sql := `SELECT t1.*
			FROM package_record t1
					 INNER JOIN (SELECT name,entity, MAX(deploytime) AS max_deploytime
								 FROM package_record
								 WHERE status = ?
								   AND name in ?
								   AND entity = ?
								   AND deploytime != 0
								 GROUP BY name) t2 
					     ON t1.name = t2.name AND t1.deploytime = t2.max_deploytime and t1.entity= t2.entity ;`
	if err := resource.DB(ctx).Raw(sql, status, packageNames, entity).
		Scan(&recordRets).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	var packageIds []string
	for _, recordRet := range recordRets {
		packageIds = append(packageIds, recordRet.PackageID)
	}
	var rets []*Package
	if err := resource.GetAllByCondNoTx(ctx, &rets, "package_id in ?", packageIds); err != nil {
		return nil, err
	}
	return rets, nil
}

// SaveDeployTime 维护这个节点的包最新部署时间
func SaveDeployTime(ctx context.Context, entity string, packageNames []string,
	toDeployPkgs []*Package) error {
	currentRecords, err := GetLatestPkgRecord(ctx, entity, packageNames, "")
	if err != nil {
		return err
	}
	deployTime := time.Now().Unix()
	var toUpdateRecords []*PackageRecord
	for _, record := range currentRecords {
		for _, deployedPkg := range toDeployPkgs {
			if deployedPkg.PackageID == record.PackageID {
				record.Deploytime = deployTime
				toUpdateRecords = append(toUpdateRecords, record)
			}
		}
	}

	if len(toUpdateRecords) > 0 {
		if err := resource.FullSaveAssociationsSave(ctx, &toUpdateRecords); err != nil {
			return err
		}
	}

	return nil
}
