/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><EMAIL>)
 * Date: 2023/12/18
 * File: dao_ng_test.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package model TODO package function desc
package model

import (
	"context"
	"reflect"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type daoUtStruct struct {
	mock sqlmock.Sqlmock
	r    *Resource
}

func initMock(t *testing.T) (*daoUtStruct, func()) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	resource := &Resource{
		ModelGorm: gormDB,
	}
	return &daoUtStruct{
			mock: mock,
			r:    resource,
		}, func() {
			_ = db.Close()
		}
}

type fakeGormStruct struct {
	Id int `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
}

func (fakeGormStruct) TableName() string {
	return "fake_for_unittest"
}

func TestDeleteMultiForAnyTypeUkey(t *testing.T) {
	ctx := context.Background()
	utStruct, closeFn := initMock(t)
	defer closeFn()
	mock := utStruct.mock
	mock.ExpectBegin()
	utStruct.mock.ExpectExec("^DELETE .*").WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	//
	err := utStruct.r.DeleteMultiForAnyTypeUkey(ctx, []*fakeGormStruct{{Id: 1}})
	if err != nil {
		t.Fatalf(err.Error())
	}
}

func TestGetAnyUniqueKey(t *testing.T) {
	type intFakeStruct struct {
		Id int `ukey:"id"`
	}
	k, v := getAnyUniqueKey(reflect.ValueOf(&intFakeStruct{Id: 1}))
	if k != "id" {
		t.Fatalf("wrong")
	}
	if val, ok := v.(int64); !ok {
		t.Fatalf("wrong")
	} else {
		if val != 1 {
			t.Fatalf("wrong")
		}
	}

	type stringFakeStruct struct {
		Id string `ukey:"id"`
	}
	k, v = getAnyUniqueKey(reflect.ValueOf(&stringFakeStruct{Id: "1"}))
	if k != "id" {
		t.Fatalf("wrong")
	}
	if val, ok := v.(string); !ok {
		t.Fatalf("wrong")
	} else {
		if val != "1" {
			t.Fatalf("wrong")
		}
	}

	type boolFakeStruct struct {
		Id bool `ukey:"id"`
	}
	k, v = getAnyUniqueKey(reflect.ValueOf(&boolFakeStruct{Id: true}))
	if k != "id" {
		t.Fatalf("wrong")
	}
	if val, ok := v.(bool); !ok {
		t.Fatalf("wrong")
	} else {
		if !val {
			t.Fatalf("wrong")
		}
	}
	type floatFakeStruct struct {
		Id float64 `ukey:"id"`
	}
	k, v = getAnyUniqueKey(reflect.ValueOf(&floatFakeStruct{Id: 1.1}))
	if k != "id" {
		t.Fatalf("wrong")
	}
	if val, ok := v.(float64); !ok {
		t.Fatalf("wrong")
	} else {
		if val != 1.1 {
			t.Fatalf("wrong")
		}
	}

	type uintFakeStruct struct {
		Id uint32 `ukey:"id"`
	}
	k, v = getAnyUniqueKey(reflect.ValueOf(&uintFakeStruct{Id: 1}))
	if k != "id" {
		t.Fatalf("wrong")
	}
	if val, ok := v.(uint64); !ok {
		t.Fatalf("wrong")
	} else {
		if val != 1 {
			t.Fatalf("wrong")
		}
	}
}
