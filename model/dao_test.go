package model

import (
	"reflect"
	"testing"
)

func TestResource_ProtectFields(t *testing.T) {
	type Node struct {
		Id        int
		AppId     string
		ClusterId string
		NodeId    string
		Role      string
		Status    string
	}

	type Cluster struct {
		Id        int
		ClusterId string
		AppId     string
		Role      string
		Status    string
		Nodes     []*Node
	}

	type Entity struct {
		Id     int
		Name   string
		AppId  string
		Role   string
		Status string
	}

	type Application struct {
		Id       int
		AppId    string
		Clusters []*Cluster
		Entitys  []*Entity
	}

	r := Resource{
		ProtectFieldParams: GetProtectFieldParamsForRole(),
	}

	app := &Application{
		Id:    0,
		AppId: "app1",
		Clusters: []*Cluster{
			&Cluster{
				Id:        0,
				ClusterId: "cluster1",
				AppId:     "app1",
				Role:      "master",
				Status:    "tocreate",
				Nodes: []*Node{
					&Node{
						Id:        0,
						AppId:     "app1",
						ClusterId: "cluster1",
						NodeId:    "node1",
						Role:      "master",
						Status:    "tocreate",
					},
					&Node{
						Id:        0,
						AppId:     "app1",
						ClusterId: "cluster1",
						NodeId:    "node2",
						Role:      "master",
						Status:    "inuse",
					},
				},
			},
			&Cluster{
				Id:        0,
				ClusterId: "cluster2",
				AppId:     "app1",
				Role:      "master",
				Status:    "inuse",
				Nodes: []*Node{
					&Node{
						Id:        0,
						AppId:     "app1",
						ClusterId: "cluster2",
						NodeId:    "node3",
						Role:      "master",
						Status:    "tocreate",
					},
					&Node{
						Id:        0,
						AppId:     "app1",
						ClusterId: "cluster2",
						NodeId:    "node4",
						Role:      "master",
						Status:    "inuse",
					},
				},
			},
		},
		Entitys: []*Entity{
			&Entity{
				Id:     0,
				Name:   "e1",
				AppId:  "app1",
				Role:   "master",
				Status: "tocreate",
			},
			&Entity{
				Id:     0,
				Name:   "e2",
				AppId:  "app1",
				Role:   "master",
				Status: "inuse",
			},
		},
	}
	r.ProtectFields(reflect.ValueOf(app))
	if app.Clusters[0].Nodes[0].Role != "" {
		t.Errorf("expect role is empty, but got %s", app.Clusters[0].Nodes[0].Role)
	}
	if app.Clusters[0].Nodes[1].Role != "master" {
		t.Errorf("expect role is master, but got %s", app.Clusters[0].Nodes[1].Role)
	}
	if app.Clusters[1].Nodes[0].Role != "" {
		t.Errorf("expect role is empty, but got %s", app.Clusters[0].Nodes[0].Role)
	}
	if app.Clusters[1].Nodes[1].Role != "master" {
		t.Errorf("expect role is master, but got %s", app.Clusters[0].Nodes[1].Role)
	}
	if app.Clusters[0].Role != "master" {
		t.Errorf("expect role is master, but got %s", app.Clusters[0].Role)
	}
	if app.Clusters[1].Role != "master" {
		t.Errorf("expect role is master, but got %s", app.Clusters[1].Role)
	}
	if app.Entitys[0].Role != "master" {
		t.Errorf("expect role is master, but got %s", app.Entitys[0].Role)
	}
	if app.Entitys[1].Role != "master" {
		t.Errorf("expect role is master, but got %s", app.Entitys[1].Role)
	}
}
