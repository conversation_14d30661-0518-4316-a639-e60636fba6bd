/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2021/12/06
 * File: conditions.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package model TODO package function desc
package model

import (
	"fmt"

	"gorm.io/gorm"
)

type Condition struct {
	WhereMap  map[string]interface{}
	PageIndex int
	PageSize  int
}

func (cond *Condition) Where(tx *gorm.DB) *gorm.DB {
	for k, v := range cond.WhereMap {
		switch v := v.(type) {
		case string, int:
			tx = tx.Where(fmt.Sprintf("%s = ?", k), v)
		case []int, []string:
			tx = tx.Where(fmt.Sprintf("%s IN ?", k), v)
		}
	}
	tx = tx.Offset(cond.PageSize * (cond.PageIndex - 1))
	tx = tx.Limit(cond.PageSize)
	return tx
}

func (cond *Condition) Add(k string, v interface{}) {
	if k == "PageIndex" || k == "pageIndex" || k == "page_index" {
		cond.PageIndex = v.(int)
	} else if k == "PageSize" || k == "pageSize" || k == "page_size" {
		cond.PageSize = v.(int)
	} else {
		cond.WhereMap[k] = v
	}
}
