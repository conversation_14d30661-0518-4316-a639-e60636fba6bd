/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2021/12/06
 * File: resource.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package model TODO package function desc
package model

import (
	"context"
	"reflect"

	"gorm.io/gorm/logger"
	"icode.baidu.com/baidu/gdp/gorm_adapter"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/mysql"
)

type ProtectFieldParams map[string][]*ProtectFieldInfo

type ProtectFieldInfo struct {
	ValueFunc func(v reflect.Value)
	CondFunc  func(v reflect.Value) bool
}

type Resource struct {
	ModelClient                         mysql.Client
	ModelGorm                           *gorm_adapter.GormDB
	GormLoggerCommon                    logger.Interface
	GormLoggerIgnoreRecordNotFoundError logger.Interface
	PreloadConf                         map[string][]string
	DbLogger                            logit.Logger
	GormLogger                          logit.Logger
	CloseFns                            []func() error
	AutoPreload                         bool //是否自动preload,true的话PreloadConf根据Gorm Tag构造，false的话需要指定PreloadConf。
	ProtectFieldParams                  ProtectFieldParams
}

type ResourceCfg struct {
	ServicerName       string
	DbLogger           logit.Logger // gdp mysql库的logger 可以不传，若不设置，默认日志会打印到ral/ral-worker.log
	GormLogger         logit.Logger // gorm操作的logger 必传
	PreloadConf        map[string][]string
	AutoPreload        bool // 是否自动preload，为true则忽略PreloadConf参数，根据Gorm Tag构造
	ProtectFieldParams ProtectFieldParams
}

func InitModel(ctx context.Context, conf ResourceCfg) (*Resource, error) {
	checkResourceCnf(conf)
	var r Resource
	r.DbLogger = conf.DbLogger
	r.GormLogger = conf.GormLogger
	r.AutoPreload = conf.AutoPreload
	r.ModelClient = initOneDB(conf.ServicerName, r.DbLogger)
	r.ProtectFieldParams = conf.ProtectFieldParams
	var err error
	r.ModelGorm, err = gorm_adapter.NewGorm(r.ModelClient)
	if err != nil {
		panic(err.Error())
	}
	if !r.AutoPreload {
		// 如果不是自动加载，则手动指定PreloadConf
		r.setPreloadConf(conf.PreloadConf)
	}
	r.InitGormLogger()
	return &r, nil
}

func GetProtectFieldParamsForRole() ProtectFieldParams {
	protectFieldParams := make(ProtectFieldParams, 0)
	protectFieldParams["Node"] = []*ProtectFieldInfo{
		&ProtectFieldInfo{
			ValueFunc: func(v reflect.Value) {
				for i := 0; i < v.NumField(); i++ {
					if v.Type().Field(i).Name == "Role" {
						v.Field(i).SetString("")
					}
				}
			},
			CondFunc: func(v reflect.Value) bool {
				for i := 0; i < v.NumField(); i++ {
					if v.Type().Field(i).Name == "Status" && v.Field(i).String() == "tocreate" {
						return true
					}
				}
				return false
			},
		},
	}
	return protectFieldParams
}

func checkResourceCnf(conf ResourceCfg) {
	if conf.ServicerName == "" {
		panic("miss servicer name")
	}
	if conf.GormLogger == nil {
		panic("miss gorm logger")
	}
}

func initOneDB(name string, dbLogger logit.Logger) mysql.Client {
	opts := make([]mysql.ClientOption, 0)
	// 设置日志logger，可选的
	// 若不设置，默认日志会打印到ral/ral-worker.log

	if dbLogger != nil {
		opts = append(opts)
	}
	client, errClient := mysql.NewClient(name, opts...)
	if errClient != nil {
		panic(errClient.Error())
	}
	return client
}

func (r *Resource) setPreloadConf(preloadConf map[string][]string) {
	if len(preloadConf) <= 0 {
		r.PreloadConf = make(map[string][]string, 0)
	} else {
		r.PreloadConf = preloadConf
	}
}
