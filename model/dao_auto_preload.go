/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2022/03/14
 * File: dao_auto_preload.go
 */

/*
 * DESCRIPTION
 *   通过tag自动preload，实验阶段，先别用
 */

// Package model
package model

import (
	"gorm.io/gorm"
	"reflect"
	"strings"
)

func (r Resource) SearchAutoPreloadConf(field reflect.StructField) []string {
	ret := make([]string, 0)
	if field.Type.Kind() == reflect.Slice ||
		(field.Type.Kind() == reflect.Ptr && field.Type.Elem().Kind() == reflect.Struct) {
		// 是slice或者struct指针的话才有可能是我们需要preload的
		// 读取gorm tag，例子：foreignKey:AppId;references:AppId
		gormTag := field.Tag.Get("gorm")
		gormTags := strings.Split(gormTag, ";")
		gormTagsMap := make(map[string]string, 0)
		for _, tag := range gormTags {
			tagKV := strings.Split(tag, ":")
			if len(tagKV) == 2 {
				gormTagsMap[tagKV[0]] = tagKV[1]
			}
		}
		// 探测有没有需要的两个子tag，如果不对劲则不需要preload，直接返回空数组
		foreignKey, ok := gormTagsMap["foreignKey"]
		if !ok {
			// gorm tag没有正确的外键和refrences
			return ret
		}
		references, ok := gormTagsMap["references"]
		if !ok {
			// gorm tag没有正确的外键和refrences
			return ret
		}
		// 两个所需子tag都不为空说明是个要preload的字段
		if foreignKey != "" && references != "" {
			// 获取这个slice字段的元素类型
			elem := field.Type
			if elem.Kind() == reflect.Slice {
				elem = elem.Elem()
			}
			// 如果这个元素是个struct则需要preload且需要继续递归处理
			if elem.Kind() == reflect.Ptr && elem.Elem().Kind() == reflect.Struct {
				childConfs := make([]string, 0) // 缓存集中处理
				// 为了遍历子元素的内容，实例化一个子元素
				elemInstance := reflect.New(elem.Elem()).Elem().Type()
				for i := 0; i < elemInstance.NumField(); i++ {
					// 遍历子元素的字段
					childField := elemInstance.Field(i)
					// 递归获得子元素的递归结果
					childItems := r.SearchAutoPreloadConf(childField)
					// 先加到缓存里，等所有字段遍历完统一处理
					for _, v := range childItems {
						childConfs = append(childConfs, v)
					}
				}
				// 如果为零，说明这个elem是最后一层了，那么把这个elem直接放在conf数组内，然后返回
				if len(childConfs) == 0 {
					ret = append(ret, field.Name)
				} else {
					// 说明elem不是最后一层，需要拼好了塞进去
					for _, v := range childConfs {
						conf := field.Name + "." + v
						ret = append(ret, conf)
					}
				}
				return ret
			} else {
				// slice内部不是struct，不需要preload
				return ret
			}
		}
		// gorm tag没有正确的外键和refrences
		return ret
	}
	// 非slice类型，递归终止条件，返回空
	return ret
}

func (r Resource) autoSetPreload(tx *gorm.DB, target interface{}) *gorm.DB {
	t := reflect.TypeOf(target)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	// 针对slice单独处理，使getAll这类方法也可以auto preload
	if t.Kind() == reflect.Slice {
		t = t.Elem()
		if t.Kind() == reflect.Ptr {
			t = t.Elem()
		}
	}
	if t.Kind() == reflect.Struct {
		if r.PreloadConf == nil {
			r.PreloadConf = make(map[string][]string, 0)
		}
		preloadConf, ok := r.PreloadConf[t.Name()]
		if !ok {
			preloadConf = make([]string, 0)
			for i := 0; i < t.NumField(); i++ {
				field := t.Field(i)
				preloadItems := r.SearchAutoPreloadConf(field)
				for _, preloadItem := range preloadItems {
					preloadConf = append(preloadConf, preloadItem)
				}
			}
			r.PreloadConf[t.Name()] = preloadConf
		}
		for _, confItem := range preloadConf {
			tx = tx.Preload(confItem)
		}
	}
	return tx
}
