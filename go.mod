module icode.baidu.com/baidu/scs/x1-base

go 1.19

require (
	github.com/DATA-DOG/go-sqlmock v1.5.0
	github.com/agiledragon/gomonkey/v2 v2.11.0
	github.com/alicebob/miniredis/v2 v2.31.0
	github.com/baidubce/bce-sdk-go v0.9.151
	github.com/gin-gonic/gin v1.8.1
	github.com/go-cmd/cmd v1.4.1
	github.com/go-redis/redis/v8 v8.11.5
	github.com/go-redis/redismock/v8 v8.11.5
	github.com/goccy/go-json v0.10.2
	github.com/golang/mock v1.4.4
	github.com/google/uuid v1.6.0
	github.com/jlaffaye/ftp v0.0.0-20211117213618-11820403398b
	github.com/json-iterator/go v1.1.12
	github.com/minio/minio-go/v7 v7.0.66
	github.com/mitchellh/mapstructure v1.5.0
	github.com/pkg/errors v0.9.1
	github.com/smartystreets/goconvey v1.6.4
	github.com/spf13/cast v1.5.0
	github.com/stretchr/testify v1.8.3
	github.com/tidwall/redcon v1.6.2
	gorm.io/driver/mysql v1.4.1
	gorm.io/gorm v1.24.0
	gorm.io/plugin/soft_delete v1.1.0
	icode.baidu.com/baidu/bce-iam/sdk-go v0.0.0-20210514102101-36e6ea769be6
	icode.baidu.com/baidu/gdp/codec v1.21.6
	icode.baidu.com/baidu/gdp/conf v1.21.0
	icode.baidu.com/baidu/gdp/env v1.21.0
	icode.baidu.com/baidu/gdp/excache v0.2.0
	icode.baidu.com/baidu/gdp/extension v1.23.0
	icode.baidu.com/baidu/gdp/ghttp v1.24.2
	icode.baidu.com/baidu/gdp/gorm_adapter v1.20.2
	icode.baidu.com/baidu/gdp/logit v1.23.0
	icode.baidu.com/baidu/gdp/mysql v1.24.7
	icode.baidu.com/baidu/gdp/net v1.30.1
	icode.baidu.com/baidu/gdp/redis v1.27.1
	icode.baidu.com/baidu/scs/thirdparty-private-sdks v0.0.0-20240423095638-4b74cebd0551
)

require (
	github.com/BurntSushi/toml v1.2.0 // indirect
	github.com/alicebob/gopher-json v0.0.0-20200520072559-a9ecdc9d1d3a // indirect
	github.com/andybalholm/brotli v1.0.3 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/boltdb/bolt v1.3.1 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/decred/dcrd/dcrec/secp256k1/v4 v4.1.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/didi/gendry v1.6.0 // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/emirpasic/gods v1.18.1 // indirect
	github.com/envoyproxy/protoc-gen-validate v0.6.7 // indirect
	github.com/fsnotify/fsnotify v1.6.0 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/go-logr/logr v1.2.3 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-playground/locales v0.14.0 // indirect
	github.com/go-playground/universal-translator v0.18.0 // indirect
	github.com/go-playground/validator/v10 v10.10.0 // indirect
	github.com/go-resty/resty/v2 v2.7.0 // indirect
	github.com/go-sql-driver/mysql v1.6.0 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/protobuf v1.5.2 // indirect
	github.com/google/go-cmp v0.5.9 // indirect
	github.com/gopherjs/gopherjs v0.0.0-20181017120253-0766667cb4d1 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/klauspost/compress v1.17.6 // indirect
	github.com/klauspost/cpuid/v2 v2.2.6 // indirect
	github.com/leodido/go-urn v1.2.1 // indirect
	github.com/lestrrat-go/blackmagic v1.0.1 // indirect
	github.com/lestrrat-go/httpcc v1.0.1 // indirect
	github.com/lestrrat-go/httprc v1.0.4 // indirect
	github.com/lestrrat-go/iter v1.0.2 // indirect
	github.com/lestrrat-go/jwx/v2 v2.0.8 // indirect
	github.com/lestrrat-go/option v1.0.0 // indirect
	github.com/mattn/go-isatty v0.0.14 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.1 // indirect
	github.com/minio/md5-simd v1.1.2 // indirect
	github.com/minio/sha256-simd v1.0.1 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/onsi/gomega v1.25.0 // indirect
	github.com/orcaman/concurrent-map v1.0.0 // indirect
	github.com/pelletier/go-toml/v2 v2.0.1 // indirect
	github.com/pierrec/lz4/v4 v4.1.14 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_golang v1.13.0 // indirect
	github.com/prometheus/client_model v0.2.0 // indirect
	github.com/prometheus/common v0.37.0 // indirect
	github.com/prometheus/procfs v0.8.0 // indirect
	github.com/rs/xid v1.5.0 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/skip2/go-qrcode v0.0.0-20200617195104-da1b6568686e // indirect
	github.com/smartystreets/assertions v0.0.0-20180927180507-b2de0cb4f26d // indirect
	github.com/tidwall/btree v1.1.0 // indirect
	github.com/tidwall/gjson v1.14.3 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/ugorji/go/codec v1.2.7 // indirect
	github.com/vmihailenco/msgpack v4.0.4+incompatible // indirect
	github.com/yuin/gopher-lua v1.1.0 // indirect
	go.opentelemetry.io/otel v1.6.3 // indirect
	go.opentelemetry.io/otel/sdk v1.6.3 // indirect
	go.opentelemetry.io/otel/trace v1.6.3 // indirect
	go.uber.org/atomic v1.7.0 // indirect
	go.uber.org/multierr v1.6.0 // indirect
	go.uber.org/zap v1.23.0 // indirect
	golang.org/x/crypto v0.21.0 // indirect
	golang.org/x/net v0.23.0 // indirect
	golang.org/x/sys v0.18.0 // indirect
	golang.org/x/text v0.14.0 // indirect
	golang.org/x/time v0.0.0-20220609170525-579cf78fd858 // indirect
	google.golang.org/appengine v1.6.6 // indirect
	google.golang.org/genproto v0.0.0-20200825200019-8632dd797987 // indirect
	google.golang.org/grpc v1.36.0 // indirect
	google.golang.org/protobuf v1.28.1 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	icode.baidu.com/baidu/gdp/bns v1.23.2 // indirect
	icode.baidu.com/baidu/gdp/exjson v1.0.1 // indirect
	icode.baidu.com/baidu/gdp/mcpack v1.0.0 // indirect
	icode.baidu.com/baidu/gdp/xds v0.1.0 // indirect
	icode.baidu.com/baidu/third-party/go-control-plane v0.8.7-0.20220531025328-39658c5e033e // indirect
	istio.io/gogo-genproto v0.0.0-20190731221249-06e20ada0df2 // indirect
)
