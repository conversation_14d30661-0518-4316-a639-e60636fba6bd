/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-03
 * File: cerrs_test.go
 */

/*
 * DESCRIPTION
 *   cerrs unittest
 */

// Package cerrs
package cerrs

import (
	"errors"
	"fmt"
	"testing"
)

// TestNewError - 测试正常创建Error
func TestNewError(t *testing.T) {
	const errCode = 123454321
	const errMessage = "123454321"
	const isFatal = true
	errCause := errors.New("123454321")

	err := New(errCode, errMessage, isFatal, errCause)

	if err == nil {
		t.Fatalf("[TestNewError] new fail")
	}

	if err.Code() != errCode {
		t.Errorf("[TestNewError] code unmatched")
	}

	if err.Message() != errMessage {
		t.<PERSON>("[TestNewError] message unmatched")
	}

	if err.IsFatal() != isFatal {
		t.Errorf("[TestNewError] isFatal unmatched")
	}

	if !errors.Is(err.Cause(), errCause) {
		t.Errorf("[TestNewError] errCause unmatched")
	}
}

// TestNewConst - 测试创建const Error
func TestNewConst(t *testing.T) {
	err1 := NewConst(1, "1", true, nil)

	if err1 == nil {
		t.Fatalf("[TestNewConst] new fail")
	}

	err2 := err1.Fatal(false)

	if err1 == err2 {
		t.Errorf("[TestNewConst] not const")
	}

	if err1.Code() != err2.Code() {
		t.Errorf("[TestNewConst] code unmatch")
	}
}

// TestErrorDetail - 测试err.Error
func TestErrorDetail(t *testing.T) {
	const errCode = 123454321
	const errMessage = "123454321"
	const errDetail = "123456787654321"

	// no wrap error
	err1 := New(errCode, errMessage, false, nil)
	if msg := err1.Error(); msg != errMessage {
		t.Fatalf("[TestErrorDetail] no wrap error detail not matched, expect %s, got %s", errMessage, msg)
	}

	// wrap error
	err2 := New(errCode, errMessage, false, errors.New(errDetail))
	if msg := err2.Error(); msg != errDetail {
		t.Fatalf("[TestErrorDetail] wrap error detail not matched, expect %s, got %s", errDetail, msg)
	}
}

// TestWrapError - 测试wrap error
func TestWrapError(t *testing.T) {
	errDetail := "params is null"

	// Errorf
	err1 := ErrInvalidParams.Errorf(errDetail)

	if !Is(err1, ErrInvalidParams) {
		t.Fatalf("[TestWrapError] wrap result is not an ErrInvalidParams")
	}

	if detail := err1.Error(); detail != errDetail {
		t.Fatalf("[TestWrapError] wrap error detail not matched, expect %s, got %s", errDetail, detail)
	}

	// Wrap
	err2 := ErrUnknown.Wrap(errors.New(errDetail))
	if !Is(err2, ErrUnknown) {
		t.Fatalf("[TestWrapError] wrap result is not an ErrUnknown")
	}

	if detail := err2.Error(); detail != errDetail {
		t.Fatalf("[TestWrapError] wrap error detail not matched, expect %s, got %s", errDetail, detail)
	}
}

// TestCodeWrapper - 测试获取Code的Wrapper函数
func TestCodeWrapper(t *testing.T) {
	// normal errModel
	errCode := 123454321
	err1 := New(errCode, "", false, nil)
	if code := Code(err1); code != errCode {
		t.Errorf("[TestCodeWrapper] normal errModel test: code unmatched, expect %d, got %d", errCode, code)
	}

	// nil
	errCode = CODE_SUCCESS
	var err2 error
	if code := Code(err2); code != errCode {
		t.Errorf("[TestCodeWrapper] nil test: code unmatched, expect %d, got %d", errCode, code)
	}

	// non-nil basic error
	err3 := errors.New("")
	errCode = CODE_UNKNOWN
	if code := Code(err3); code != errCode {
		t.Errorf("[TestCodeWrapper] non-nil basic error test: code unmatched, expect %d, got %d", errCode, code)
	}
}

// TestMessageWrapper - 测试获取Message的Wrapper函数
func TestMessageWrapper(t *testing.T) {
	// normal errModel
	errMessage := "123454321"
	err1 := New(123454321, errMessage, false, nil)
	if msg := Message(err1); msg != errMessage {
		t.Errorf("[TestMessageWrapper] normal errModel test: msg unmatched, expect %s, got %s", errMessage, msg)
	}

	// nil
	var err2 error
	if msg := Message(err2); msg != succMessage {
		t.Errorf("[TestMessageWrapper] nil test: msg unmatched, expect %s, got %s", succMessage, msg)
	}

	// non-nil basic error
	errMessage = "543212345"
	err3 := errors.New(errMessage)
	if msg := Message(err3); msg != errMessage {
		t.Errorf("[TestMessageWrapper] non-nil basic error test: msg unmatched, expect %s, got %s",
			errMessage, msg)
	}
}

// TestErrorWrapper - 测试Error()的Wrapper函数
func TestErrorWrapper(t *testing.T) {
	// normal errModel with nil errCause
	errCode := 123454321
	errMessage := "123454321"
	err1 := New(123454321, errMessage, false, nil)
	if detail := Error(err1); detail != errMessage {
		t.Errorf("[TestMessageWrapper] normal errModel test: detail unmatched, expect %s, got %s",
			errMessage, detail)
	}

	errDetail := errMessage + errMessage
	err2 := New(errCode, errMessage, false, errors.New(errDetail))
	if detail := Error(err2); detail != errDetail {
		t.Errorf("[TestMessageWrapper] normal errModel test: detail unmatched, expect %s, got %s",
			errDetail, detail)
	}

	// nil
	var err3 error
	if detail := Error(err3); detail != "" {
		t.Errorf("[TestMessageWrapper] nil test: detail unmatched, expect %s, got %s", "\"\"", detail)
	}

	// non-nil basic error
	errMessage = "543212345"
	err4 := errors.New(errMessage)
	if detail := Message(err4); detail != errMessage {
		t.Errorf("[TestMessageWrapper] non-nil basic error test: detail unmatched, expect %s, got %s",
			errMessage, detail)
	}
}

// TestFatalWrapper - 测试获取IsFatal的Wrapper函数
func TestFatalWrapper(t *testing.T) {
	// normal errModel
	const isFatal = true
	err1 := New(123454321, "", isFatal, nil)
	if fatal := IsFatal(err1); fatal != isFatal {
		t.Errorf("[TestFatalWrapper] normal errModel test: isFatal unmatched, expect %t, got %t",
			isFatal, fatal)
	}

	// nil
	var err2 error
	if fatal := IsFatal(err2); fatal != false {
		t.Errorf("[TestFatalWrapper] nil test: msg unmatched, expect %t, got %t", false, fatal)
	}

	// non-nil basic error
	err3 := errors.New("123454321")
	if fatal := IsFatal(err3); fatal != true {
		t.Errorf("[TestFatalWrapper] non-nil basic error test: msg unmatched, expect %t, got %t",
			true, fatal)
	}
}

// TestIsWrapper - 测试IsA的Wrapper函数
func TestIsWrapper(t *testing.T) {
	const errCode = 123454321
	const errMessage = "123454321"

	// err is itself
	err1 := errors.New("123454321")
	err2 := New(errCode, errMessage, false, nil)
	if !Is(err1, err1) || !Is(err2, err2) {
		t.Errorf("[TestIsWrapper] self test fail")
	}

	// same code errModel
	err3 := New(errCode, errMessage, false, nil)
	if !Is(err2, err3) {
		t.Errorf("[TestIsWrapper] same code test fail")
	}

	// different errors
	err4 := New(errCode+1, errMessage, false, nil)
	if Is(err1, err2) || Is(err2, err1) || Is(err2, err4) {
		t.Errorf("[TestIsWrapper] different test fail")
	}

	// nil test
	var err5 error
	var err6 *errModel
	if !Is(err5, err5) || !Is(err6, err6) || !Is(err5, err6) || !Is(err6, err5) {
		t.Errorf("[TestIsWrapper] nil test fail")
	}
}

// TestCauseWrapper - Cause函数封装测试
func TestCauseWrapper(t *testing.T) {
	const errCode = 123454321
	const errMessage = "123454321"

	// nil error
	var err1 error
	if Cause(err1) != err1 {
		t.Errorf("[TestCauseWrapper] nil error changed after unwrap")
	}

	// non-nil normal error
	err2 := errors.New(errMessage)
	err3 := Cause(err2)
	if err3 != nil {
		t.Errorf("[TestCauseWrapper] non-nil normal error should have a nil cause")
	}

	// errModel
	err4 := New(errCode, errMessage, false, err2)
	err5 := Cause(err4)
	if err2 != err5 {
		t.Errorf("[TestCauseWrapper] errModel changed after unwrap")
	}
}

// TestErrorfWrapper - Errorf函数封装测试
func TestErrorfWrapper(t *testing.T) {
	format := "12345%s"
	errMessage := fmt.Sprintf(format, format)

	err := Errorf(format, format)
	if !Is(err, ErrUnknown) || err.Error() != errMessage {
		t.Errorf("[TestErrorfWrapper] Errorf test fail")
	}
}

// TestUnFatalWrapper - 测试UnFatal封装函数
func TestUnFatalWrapper(t *testing.T) {
	const errCode = 123454321
	const errMessage = "123454321"
	errCause := errors.New("123454321")

	// nil to nil
	if Fatal(nil, false) != nil {
		t.Errorf("[TestErrorfWrapper] nil test fail")
	}

	// un-fatal error stay it self
	err1 := NewConst(errCode, errMessage, false, errCause)
	err2 := Fatal(err1, false)
	if err1 != err2 {
		t.Errorf("[TestErrorfWrapper] un-fatal error changed after do UnFatal")
	}

	// fatal errModel
	err3 := NewConst(errCode, errMessage, true, errCause)
	err4 := Fatal(err3, false)
	if err3.Error() != err4.Error() || err3.Code() != Code(err4) ||
		err3.Message() != Message(err4) || IsFatal(err4) {
		t.Errorf("[TestErrorfWrapper] fatal errModel UnFatal test fail")
	}

	// fatal basic error
	err5 := errors.New(errMessage)
	err6 := Fatal(err5, false)
	if err5.Error() != err6.Error() || Code(err5) != Code(err6) ||
		!Is(err6, ErrUnknown) || IsFatal(err6) {
		t.Errorf("[TestErrorfWrapper] fatal basic error UnFatal test fail")
	}
}
