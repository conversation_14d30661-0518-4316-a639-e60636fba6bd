/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-03
 * File: codes.go
 */

/*
 * DESCRIPTION
 *   cerrs codes 定义
 */

// Package cerrs
package cerrs

const (
	/*
	 * 基础错误码
	 */
	CODE_SUCCESS           = iota // success
	CODE_UNKNOWN                  // unknown error
	CODE_AUTH_FAIL                // auth fail
	CODE_INVALID_PARAMS           // params error
	CODE_RAL_REQUEST_FAIL         // ral req fail
	CODE_CONF_ERROR               // conf error
	CODE_INVALID_RESPONSE         // invalid response
	CODE_DB_QUERY_FAIL            // db query fail
	CODE_TIMEOUT                  // timeout
	CODE_CANCELED                 // canceled
	CODE_NOT_FOUND                // not found
	CODE_PANIC                    // panic
	CODE_HTTP_STATUS_ERROR        // http status error

	CODE_TASK_MANUAL = 13
	// TODO ...

	/*
	 * SDK错误码基线
	 */
	CODE_IAM_BASE       = 10000
	CODE_STS_BASE       = 20000
	CODE_BCC_BASE       = 30000
	CODE_XAGENT_BASE    = 40000
	CODE_REDIS_BASE     = 50000
	CODE_ELB_BASE       = 60000
	CODE_DNS_BASE       = 70000
	CODE_CONSOLE_BASE   = 80000
	CODE_BNS_BASE       = 90000
	CODE_X1_RES         = 100000
	CODE_XRM_BASE       = 110000
	CODE_BLB_BASE       = 120000
	CODE_BCM_BASE       = 130000
	CODE_NAMESPACE_BASE = 140000
	CODE_DTS_BASE       = 150000
	CODE_VPC_BASE       = 160000

	// TODO ...

	/*
	 * component错误码基线
	 */
	CODE_DEPLOY_COMPO_BASE   = 1000000 // deploy error
	CODE_XAGENT_COMPO_BASE   = 2000000 // xagent error
	CODE_META_COMPO_BASE     = 3000000
	CODE_RESOURCE_COMPO_BASE = 4000000
	// TODO ...

	/*
	 * 用户扩展错误码基线
	 */
	CODE_USER_BASE = 100000000
)
