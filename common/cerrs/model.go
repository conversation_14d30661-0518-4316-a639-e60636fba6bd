/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-03
 * File: model.go
 */

/*
 * DESCRIPTION
 *   cerrs model 定义
 */

// Package cerrs
package cerrs

import (
	"errors"
	"fmt"
)

// errWithCode - errWithCode interface
type errWithCode interface {
	Code() int
}

// errWithMessage - errWithMessage interface
type errWithMessage interface {
	Message() string
}

// errWithFatal - errWithFatal interface
type errWithFatal interface {
	IsFatal() bool
}

// errWithComparer - errWithComparer interface, for errors.Is()
type errWithComparer interface {
	Is(target error) bool
}

// errWithCause - errWithCause interface，for causer
type errWithCause interface {
	Cause() error
}

// errWithWrapper - errWithWrapper interface, for errors.As()
type errWithWrapper interface {
	Unwrap() error
}

// errModel - Err model
type errModel struct {
	errCode    int
	errMessage string
	isFatal    bool
	errCause   error
	isConst    bool
}

var _ error = (*errModel)(nil)

const succMessage = "success"

// Error - 实现 error interface
func (err *errModel) Error() string {
	if err != nil {
		// 优先取errCause.Error()
		if err.errCause != nil {
			return err.errCause.Error()
		}

		return err.errMessage
	}

	return succMessage
}

var _ errWithCode = (*errModel)(nil)

// Code - 实现ErrWithCode interface
func (err *errModel) Code() int {
	if err != nil {
		return err.errCode
	}

	return 0
}

var _ errWithMessage = (*errModel)(nil)

// Message - 实现ErrWithMessage interface
func (err *errModel) Message() string {
	if err != nil {
		return err.errMessage
	}

	return "success"
}

var _ errWithFatal = (*errModel)(nil)

// IsFatal - 实现ErrWithFatal interface
func (err *errModel) IsFatal() bool {
	if err != nil {
		return err.isFatal
	}

	return false
}

var _ fmt.Stringer = (*errModel)(nil)

// String - for format %v
func (err *errModel) String() string {
	return fmt.Sprintf("[Error %d] %s", err.Code(), err.Message())
}

var _ errWithComparer = (*errModel)(nil)

// Is - for errors.Is()
func (err *errModel) Is(target error) bool {
	if err != nil {
		if target == nil {
			return false
		}

		if e, ok := target.(errWithCode); ok {
			if err.errCode == e.Code() {
				return true
			}

			return err.Is(Cause(target))
		}

		return errors.Is(err.errCause, target)
	}

	return target == err || target == nil
}

var _ errWithCause = (*errModel)(nil)

// Cause - 实现ErrWithCause interface
func (err *errModel) Cause() error {
	if err != nil {
		return err.errCause
	}

	return err
}

var _ errWithWrapper = (*errModel)(nil)

// Unwrap - 实现ErrWithWrapper interface
func (err *errModel) Unwrap() error {
	return err.Cause()
}

// Wrap - wrap an error
func (err *errModel) Wrap(errCause error) error {
	if errCause == nil {
		return nil
	}

	if err != nil {
		if err.isConst {
			return New(err.errCode, err.errMessage, err.isFatal, errCause)
		}

		err.errCause = errCause
		return err
	}

	return ErrUnknown.Wrap(errCause)
}

// Errorf - created an error by fmt.Errorf(), then wrap it
func (err *errModel) Errorf(format string, a ...interface{}) error {
	errCause := fmt.Errorf(format, a...)
	return err.Wrap(errCause)
}

// Fatal - make the errModel fatal or not
func (err *errModel) Fatal(fatal bool) *errModel {
	if err != nil {
		if err.isFatal == fatal {
			return err
		}

		if err.isConst {
			return New(err.errCode, err.errMessage, fatal, err.errCause)
		}

		err.isFatal = fatal
		return err
	}

	return err
}

// New - new Error
func New(errCode int, errMessage string, isFatal bool, errCause error) *errModel {
	if errCode == CODE_SUCCESS {
		errCode = CODE_UNKNOWN
	}

	err := &errModel{
		errCode:    errCode,
		errMessage: errMessage,
		isFatal:    isFatal,
		errCause:   errCause,
	}

	return err
}

// NewConst - new const Error
func NewConst(errCode int, errMessage string, isFatal bool, errCause error) *errModel {
	err := New(errCode, errMessage, isFatal, errCause)
	if err != nil {
		err.isConst = true
	}

	return err
}

// Code - Get err code
func Code(err error) int {
	if err != nil {
		if e, ok := err.(errWithCode); ok {
			return e.Code()
		}

		return CODE_UNKNOWN
	}

	return CODE_SUCCESS
}

// Message - Get err message
func Message(err error) string {
	if err != nil {
		if e, ok := err.(errWithMessage); ok {
			return e.Message()
		}

		return err.Error()
	}

	return succMessage
}

// Error - wrapper for err.Error()
func Error(err error) string {
	if err != nil {
		return err.Error()
	}

	return ""
}

// IsFatal - 检查是否是fatal错误
func IsFatal(err error) bool {
	if err != nil {
		if e, ok := err.(errWithFatal); ok {
			return e.IsFatal()
		}

		return true
	}

	return false
}

// Is - 优先使用errWithComparer接口，再使用errors.Is()
func Is(err, target error) bool {
	if err != nil {
		if e, ok := err.(errWithComparer); ok {
			return e.Is(target)
		}
	}

	if target != nil {
		if e, ok := target.(errWithComparer); ok {
			return e.Is(err)
		}
	}

	return errors.Is(err, target)
}

// As - errors.As()
func As(err error, target interface{}) bool {
	//goland:noinspection GoErrorsAs
	return errors.As(err, target)
}

// Cause - 获取Cause
func Cause(err error) error {
	if err != nil {
		if e, ok := err.(errWithCause); ok {
			return e.Cause()
		}

		if e, ok := err.(errWithWrapper); ok {
			return e.Unwrap()
		}
	}

	return nil
}

// Errorf - wrap ErrUnknown.Errorf()
func Errorf(format string, a ...interface{}) error {
	return ErrUnknown.Errorf(format, a...)
}

// Fatal - make an error fatal or not
func Fatal(err error, fatal bool) error {
	if IsFatal(err) == fatal {
		return err
	}

	if err != nil {
		if e, ok := err.(*errModel); ok {
			return e.Fatal(fatal)
		}

		return ErrUnknown.Fatal(fatal).Wrap(err)
	}

	return err
}
