/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-03
 * File: errors.go
 */

/*
 * DESCRIPTION
 *   cerrs errors 定义
 */

// Package cerrs
package cerrs

var (
	/*
	 * Err 基础错误
	 */
	ErrUnknown         = NewConst(CODE_UNKNOWN, "unknown error", true, nil)
	ErrAuthFail        = NewConst(CODE_AUTH_FAIL, "auth fail", false, nil)
	ErrInvalidParams   = NewConst(CODE_INVALID_PARAMS, "invalid params", false, nil)
	ErrRalRequestFail  = NewConst(CODE_RAL_REQUEST_FAIL, "ral request fail", true, nil)
	ErrConfError       = NewConst(CODE_CONF_ERROR, "conf error", true, nil)
	ErrInvalidResponse = NewConst(CODE_INVALID_RESPONSE, "invalid response", true, nil)
	ErrDbQueryFail     = NewConst(CODE_DB_QUERY_FAIL, "db query fail", true, nil)
	ErrTimeout         = NewConst(CODE_TIMEOUT, "timeout", true, nil)
	ErrCanceled        = NewConst(CODE_CANCELED, "canceled", false, nil)
	ErrNotFound        = NewConst(CODE_NOT_FOUND, "not found", false, nil)
	ErrPanic           = NewConst(CODE_PANIC, "panic", true, nil)
	ErrHttpStatusError = NewConst(CODE_HTTP_STATUS_ERROR, "http status error", true, nil)
	ErrorTaskManual    = NewConst(CODE_TASK_MANUAL, "task need manual", false, nil)

	/*
	 * ErrIam IAM sdk 错误
	 */
	ErrIAMRequestFail     = NewConst(CODE_IAM_BASE+1, "iam request fail", true, nil)
	ErrIAMResponseInvalid = NewConst(CODE_IAM_BASE+2, "iam response invalid", true, nil)
	ErrIAMGetTokenFail    = NewConst(CODE_IAM_BASE+3, "iam get token fail", true, nil)
	ErrIAMDecryptFail     = NewConst(CODE_IAM_BASE+4, "iam decrypt cipher fail", true, nil)

	/*
	 * ErrSTS STS sdk 错误
	 */
	ErrSTSGetCredentialFail  = NewConst(CODE_STS_BASE+1, "sts get credential fail", true, nil)
	ErrSTSEncryptAccountFail = NewConst(CODE_STS_BASE+2, "sts encrypt account fail", true, nil)

	/*
	 * ErrBCC BCC sdk/comp 错误
	 */
	ErrBCCLogicalFail       = NewConst(CODE_BCC_BASE+1, "bcc logical request fail", true, nil)
	ErrBCCNovaFail          = NewConst(CODE_BCC_BASE+2, "bcc nova request fail", true, nil)
	ErrBCCNeutronFail       = NewConst(CODE_BCC_BASE+3, "bcc neutron request fail", true, nil)
	ErrBCCTransShortIdFail  = NewConst(CODE_BCC_BASE+4, "trans id to short id fail", true, nil)
	ErrBCCResizeNotSupport  = NewConst(CODE_BCC_BASE+5, "bcc resize not support", true, nil)
	ErrBCCResizeInOperation = NewConst(CODE_BCC_BASE+6, "bcc resize in operation", true, nil)
	ErrBCCHasResized        = NewConst(CODE_BCC_BASE+7, "bcc has resized", true, nil)
	ErrBCCDeploysetFail     = NewConst(CODE_BCC_BASE+7, "bcc deployset request fail", true, nil)

	/*
	 * ErrXAgent XAgent sdk 错误
	 */
	ErrXAgentCmdFail        = NewConst(CODE_XAGENT_BASE+1, "xagent cmd exec fail", true, nil)
	ErrXAgentTaskCreateFail = NewConst(CODE_XAGENT_BASE+2, "xagent task create fail", true, nil)
	ErrXAgentTaskQueryFail  = NewConst(CODE_XAGENT_BASE+3, "xagent task query fail", true, nil)

	/*
	 * ErrRedis Redis sdk 错误
	 */
	ErrRedisCallFail = NewConst(CODE_REDIS_BASE+1, "redis call fail", true, nil)

	/*
	 * ErrELB ELB sdk 错误
	 */
	ErrELBOperationFail = NewConst(CODE_ELB_BASE+1, "elb operation fail", true, nil)
	ErrELBQueryFail     = NewConst(CODE_ELB_BASE+2, "elb query fail", true, nil)
	ErrEIPQueryFail     = NewConst(CODE_ELB_BASE+3, "eip query fail", true, nil)
	ErrEIPOpFail        = NewConst(CODE_ELB_BASE+4, "eip op fail", true, nil)

	/*
	 * ErrDNS DNS sdk 错误
	 */
	ErrDNSRequestFail = NewConst(CODE_DNS_BASE+1, "dns request fail", true, nil)

	/*
	 * ErrConsole sdk 错误
	 */
	ErrConsoleQueryFail = NewConst(CODE_CONSOLE_BASE+1, "console list fail", true, nil)
	ErrConsoleOpFail    = NewConst(CODE_CONSOLE_BASE+2, "console op fail", true, nil)

	/*
	 * Deploy component 错误
	 */
	ErrDeployFail            = NewConst(CODE_DEPLOY_COMPO_BASE+1, "deploy fail", true, nil)
	ErrDeployWorkDirExists   = NewConst(CODE_DEPLOY_COMPO_BASE+2, "deploy work dir exists", false, nil)
	ErrDeployPortAlreadyUsed = NewConst(CODE_DEPLOY_COMPO_BASE+3, "deploy port already used", false, nil)

	/*
	 * ErrXAgent component 错误
	 */
	ErrXAgentTaskFailed = NewConst(CODE_XAGENT_COMPO_BASE+1, "xagent task failed", true, nil)

	/*
	 * ErrMeta bdrp metaserver 错误
	 */
	ErrMetaFailed = NewConst(CODE_META_COMPO_BASE+1, "metaserver failed", false, nil)

	/*
	 * ErrBNS BNS sdk 错误
	 */
	ErrBNSRequestFail         = NewConst(CODE_BNS_BASE+1, "bns request fail", true, nil)
	ErrBNSRequestNotFound     = NewConst(CODE_BNS_BASE+2, "bns resource not found", false, nil)
	ErrBNSRequestAlreadyExist = NewConst(CODE_BNS_BASE+3, "bns resource already exist", false, nil)

	/*
	 * ErrX1 RES sdk 错误
	 */
	ErrX1ResRequestFail = NewConst(CODE_X1_RES+1, "x1 resource request fail", true, nil)

	/*
	 * ErrXRM XRM sdk 错误
	 */
	ErrXRMCreateServerFail            = NewConst(CODE_XRM_BASE+1, "xrm create servers fail", true, nil)
	ErrXRMQueryServerCreationTaskFail = NewConst(CODE_XRM_BASE+2, "xrm query server creation task fail", true, nil)
	ErrXRMDeleteServerFail            = NewConst(CODE_XRM_BASE+3, "xrm delete servers fail", true, nil)
	ErrXRMUpdateServerFail            = NewConst(CODE_XRM_BASE+4, "xrm update servers fail", true, nil)
	ErrXRMResourceNotAvailable        = NewConst(CODE_XRM_BASE+5, "xrm create servers fail, resource not enough to create", true, nil)
	ErrXRMResourceNotFound            = NewConst(CODE_XRM_BASE+6, "xrm cat not find container to delete", true, nil)
	ErrXRMShowServerFail              = NewConst(CODE_XRM_BASE+7, "xrm show server fail", true, nil)
	/*
	 * ErrBLB BLB sdk 错误
	 */
	ErrBLBLogicalFail = NewConst(CODE_BLB_BASE+1, "blb logical request fail", true, nil)

	/*
	 * ErrBCM bcm sdk 错误
	 */
	ErrBCMRequestFail = NewConst(CODE_BCM_BASE+1, "bcm request fail", true, nil)

	/*
	 * ErrVPC vpc sdk 错误
	 */
	ErrVPCRequestFail = NewConst(CODE_VPC_BASE+1, "vpc request fail", true, nil)

	/*
	 * ErrNamespace component 错误
	 */
	ErrNamespaceStorageNoSuchFile = NewConst(CODE_NAMESPACE_BASE+10000+1, "No Such File", true, nil)

	ErrNamespaceError              = NewConst(CODE_NAMESPACE_BASE+1, "namespace error", true, nil)
	ErrNamespaceBosCheckFail       = NewConst(CODE_NAMESPACE_BASE+2, "bos bucket or prefix not exist", true, nil)
	ErrNamespaceDiskCheckFail      = NewConst(CODE_NAMESPACE_BASE+3, "disk is not enough", true, nil)
	ErrNamespaceNamespaceCheckFail = NewConst(CODE_NAMESPACE_BASE+4, "disk is not enough", true, nil)
	ErrNamespaceInvalidParam       = NewConst(CODE_NAMESPACE_BASE+5, "invalid param", true, nil)
	/*
	 * ErrDTS DTS sdk 错误
	 */
	ErrDTSRequestFail = NewConst(CODE_DTS_BASE+1, "dts request fail", true, nil)
)
