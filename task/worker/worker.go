/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON>yi<PERSON> (<EMAIL>)
 * Date: 2022/04/19
 * File: task.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

package worker

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const ReportInterval = 10 * time.Second

const (
	idleStatus    = "idle"
	runningStatus = "running"
)

type runningContext struct {
	done       chan error
	teu        *iface.TaskExecUnit
	ter        *iface.TaskExecResult
	process    workflow.StepProcess
	ctx        context.Context
	parentCtx  context.Context
	cancel     context.CancelFunc
	lastReport time.Time
}

type workerCtx struct {
	status    string
	startTime time.Time
	deadline  time.Time
	rc        *runningContext
}

var (
	ctxPool        = make(map[string][]*workerCtx)
	idleCount      = make(map[string]int)
	idleCountMutex = &sync.Mutex{}
)

func startProcess(rc *runningContext) {
	processFunc := func() {
		var deadLineStr string
		deadline, ok := rc.ctx.Deadline()
		if !ok {
			deadLineStr = "no deadline"
		} else {
			deadLineStr = deadline.Format("2006-01-02 15:04:05")
		}
		start := time.Now()
		resource.EngineLogger.Notice(rc.ctx, "process start record", logit.Time("start", start), logit.String("deadline", deadLineStr))
		err := gtask.NoPanic(func() error {
			return rc.process(rc.ctx, rc.teu)
		})
		end := time.Now()
		cost := end.Sub(start)
		resource.EngineLogger.Notice(rc.ctx, "process end record", logit.Error("error", err), logit.Time("start", start),
			logit.Time("end", end), logit.Int64("cost", int64(cost/time.Millisecond)))
		record := &iface.ProcessRecord{
			TaskID:      rc.teu.TaskID,
			TaskBatchID: rc.teu.TaskBatchID,
			WorkFlow:    "",
			Entity:      rc.teu.Entity,
			Step: func() string {
				if strings.Contains(rc.teu.TaskBatchID, "|") {
					return strings.Split(rc.teu.TaskBatchID, "|")[1]
				}
				return rc.teu.TaskBatchID
			}(),
			Operation:  "",
			CreatedAt:  time.Now(),
			StartedAt:  start,
			Cost:       int(cost / time.Millisecond),
			Parameters: rc.teu.Parameters,
			ErrMsg: func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}(),
		}
		_ = resource.TaskOperator.CreateRecords(rc.parentCtx, []*iface.ProcessRecord{record})
		rc.done <- err
	}

	go processFunc()
}

func findProcess(processStr string) (workflow.StepProcess, error) {
	chunks := strings.Split(processStr, "__")
	if len(chunks) != 2 {
		return nil, fmt.Errorf("invalid process %s", processStr)
	}
	return workflow.FindProcess(chunks[0], chunks[1])
}

func newRunningCtx(ctx context.Context, teu *iface.TaskExecUnit) *runningContext {
	var err error
	rc := &runningContext{
		done: make(chan error),
		teu:  teu,
		ter: &iface.TaskExecResult{
			TaskExecUnitID: teu.TaskExecUnitID,
			TaskID:         teu.TaskID,
			TaskBatchID:    teu.TaskBatchID,
			Entity:         teu.Entity,
			StartAt:        time.Now(),
			Status:         iface.TaskExecResultStatusRunning,
			LastUpdate:     time.Now(),
		},
		lastReport: time.Now(),
	}
	rc.parentCtx = ctx
	rc.ctx, rc.cancel = context.WithDeadline(ctx, rc.teu.ExpireAt)

	rc.ctx = logit.ForkContext(rc.ctx)
	logit.SetLogID(rc.ctx, rc.teu.TaskID)
	logit.AddAllLevel(rc.ctx, logit.String("task_id", rc.teu.TaskID))
	logit.AddAllLevel(rc.ctx, logit.String("task_batch_id", rc.teu.TaskBatchID))
	logit.AddAllLevel(rc.ctx, logit.String("unit_id", rc.teu.TaskExecUnitID))
	logit.AddAllLevel(rc.ctx, logit.String("entity", rc.teu.Entity))
	logit.AddAllLevel(rc.ctx, logit.String("processor", rc.teu.Processor))

	if rc.process, err = findProcess(rc.teu.Processor); err != nil {
		resource.EngineLogger.Warning(ctx, "processor not found", logit.Error("error", err))
		rc.ter.Status = iface.TaskExecResultStatusError
		rc.ter.Message = fmt.Sprintf("processor not found: %s", err.Error())
	}

	// 如果在创建runningContext的过程中，ctx已经被cancel了，那么直接设置状态为error
	if rc.ctx.Err() != nil {
		resource.EngineLogger.Warning(ctx, "context error before start", logit.Error("error", rc.ctx.Err()))
		rc.ter.Status = iface.TaskExecResultStatusError
		rc.ter.Message = fmt.Sprintf("context error before start: %s", rc.ctx.Err().Error())
	}
	return rc
}

func checkRunning(ctx context.Context, rc *runningContext) {
	if rc.ter.Status != iface.TaskExecResultStatusRunning {
		return
	}
	select {
	case done, ok := <-rc.done:
		if !ok {
			rc.ter.Status = iface.TaskExecResultStatusError
			rc.ter.Message = "task done channel has been closed"
		} else {
			if done != nil {
				if cerrs.Code(done) == cerrs.CODE_TASK_MANUAL {
					rc.ter.Status = iface.TaskExecResultStatusManual
				} else {
					rc.ter.Status = iface.TaskExecResultStatusError
				}
				rc.ter.Message = done.Error()
			} else {
				rc.ter.Status = iface.TaskExecResultStatusSuccess
			}
		}
	default:
		rc.ter.Status = iface.TaskExecResultStatusRunning
	}
}

func checkCancel(ctx context.Context, rc *runningContext) {
	if rc.ter.Status != iface.TaskExecResultStatusRunning {
		return
	}
	cmd, err := resource.Broker.ConsumeCmd(ctx, rc.teu.TaskBatchID, "")
	if err != nil {
		resource.EngineLogger.Warning(ctx, "consume worker cmd error", logit.Error("error", err))
	}

	// 当收到取消命令, 或者超过最长的上报时间, 则取消任务
	// 超过最长的上报时间, engine会主动走错误流程, 为了避免重试任务与当前任务冲突, 这里需要取消
	// resource.EngineLogger.Trace(ctx, fmt.Sprintf("consume worker cmd %s", cmd))
	needCancel := cmd == iface.CmdCancel
	if needCancel {
		resource.EngineLogger.Warning(ctx, "receive cancel command, cancel task",
			logit.String("task_batch_id", rc.teu.TaskBatchID))
	}
	if !needCancel {
		needCancel = time.Since(rc.lastReport) > resource.Config.WorkerMaxReportErrorTime()
	}
	if needCancel {
		resource.EngineLogger.Warning(ctx, "lastReport reach timeout, cancel task",
			logit.String("task_batch_id", rc.teu.TaskBatchID), logit.Time("lastReport", rc.lastReport))
	}

	if needCancel {
		rc.ter.Status = iface.TaskExecResultStatusError
		rc.ter.Message = "task has been cancelled"
	}
}

func reportTaskResult(ctx context.Context, rc *runningContext) error {
	if rc.ter.Status == iface.TaskStatusRunning && time.Since(rc.lastReport) < ReportInterval {
		return nil
	}
	rc.ter.LastUpdate = time.Now()
	if err := resource.Backend.Report(ctx, rc.ter); err != nil {
		resource.EngineLogger.Warning(ctx, "report to backend failed", logit.Error("error", err))
		return err
	}
	rc.lastReport = time.Now()
	return nil
}

func consume(ctx context.Context, queue string) error {
	// 每个x1-task只有一个消费者, 消费者id使用engineID即可
	// engineID每次x1-task启动时生成一个
	// 每次预期消费数量为idleCount[queue]
	if getIdleCount(queue) == 0 {
		resource.EngineLogger.Warning(ctx, "no idle worker", logit.String("queue", queue))
		return errors.New("no idle worker")
	}
	teus, err := resource.Broker.Consume(ctx, queue, resource.Config.EngineID(), getIdleCount(queue))
	if err != nil {
		resource.EngineLogger.Error(ctx, "consume task unit error", logit.Error("error", err))
		return err
	}
	if len(teus) == 0 {
		return nil
	}
	resource.EngineLogger.Notice(ctx, fmt.Sprintf("cosume %s queue message", queue), logit.Int("count", len(teus)))
	for _, teu := range teus {
		c := pickIdleWorker(queue)
		if c == nil { // 理论上这种情况不会出现, 出现说明代码有bug
			resource.EngineLogger.Error(ctx,
				"no idle worker, messages lost",
				logit.String("queue", queue), logit.String("teu", teu.TaskExecUnitID))
			setIdleCount(queue, 0)
			continue
		}
		resource.EngineLogger.Trace(ctx, "begin to process task",
			logit.String("batch_id", teu.TaskBatchID), logit.String("teu_id", teu.TaskExecUnitID), logit.String("entity", teu.Entity))
		c.status = runningStatus
		c.startTime = time.Now()
		c.deadline = teu.ExpireAt.Add(time.Second)
		c.rc = newRunningCtx(ctx, teu)
		subIdleCount(queue)
		if c.rc.ter.Status == iface.TaskStatusRunning {
			startProcess(c.rc)
		}
	}
	return nil
}

func check(ctx context.Context, queue string) error {
	for _, c := range ctxPool[queue] {
		if c.status != runningStatus {
			continue
		}
		if time.Now().After(c.deadline) {
			c.rc.ter.Status = iface.TaskExecResultStatusError
			c.rc.ter.Message = "task is already expired"
		}
		checkRunning(ctx, c.rc)
		checkCancel(ctx, c.rc)
		if err := reportTaskResult(ctx, c.rc); err != nil {
			resource.EngineLogger.Warning(ctx, "report to backend failed", logit.Error("error", err))
			continue
		}
		if c.rc.ter.Status != iface.TaskExecResultStatusRunning {
			if c.rc != nil && c.rc.cancel != nil {
				c.rc.cancel()
			}
			c.status = idleStatus
			c.rc = nil
			addIdleCount(queue)
		}
	}
	return nil
}

func pickIdleWorker(queue string) *workerCtx {
	for _, c := range ctxPool[queue] {
		if c.status == idleStatus {
			return c
		}
	}
	return nil
}

func execute(ctx context.Context, queue string) error {
	timer := time.NewTimer(resource.Config.WorkerRunInterval())
	ctx = logit.ForkContext(ctx)
	logit.AddAllLevel(ctx, logit.String("worker_id", resource.Config.EngineID()))
	resource.EngineLogger.Notice(ctx, "worker pool start to process...", logit.Time("start_time", time.Now()))
	for {
		timer.Reset(resource.Config.WorkerRunInterval())
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-timer.C:
			var realIdleCount int
			for _, c := range ctxPool[queue] {
				if c.status == idleStatus {
					realIdleCount++
				}
				if c.rc != nil {
					resource.EngineLogger.Trace(ctx, fmt.Sprintf("running works, status %s, batch_id %s, terStatus %s",
						c.status, c.rc.teu.TaskBatchID, c.rc.ter.Status))
				}
			}
			resource.EngineLogger.Trace(ctx, fmt.Sprintf("worker pool start to process %s, idle %d, realIdle %d", queue, getIdleCount(queue), realIdleCount))
			_ = consume(ctx, queue)
			_ = check(ctx, queue)
		}
	}
}

func initCtxPools() {
	for i := 0; i < resource.Config.WorkerHighPriorityCount(); i++ {
		ctxPool[iface.TaskExecUnitQueueHighPriority] = append(
			ctxPool[iface.TaskExecUnitQueueHighPriority], &workerCtx{status: idleStatus})
	}
	idleCount[iface.TaskExecUnitQueueHighPriority] = resource.Config.WorkerHighPriorityCount()
	for i := 0; i < resource.Config.WorkerMediumPriorityCount(); i++ {
		ctxPool[iface.TaskExecUnitQueueMediumPriority] = append(
			ctxPool[iface.TaskExecUnitQueueMediumPriority], &workerCtx{status: idleStatus})
	}
	idleCount[iface.TaskExecUnitQueueMediumPriority] = resource.Config.WorkerMediumPriorityCount()
	for i := 0; i < resource.Config.WorkerLowPriorityCount(); i++ {
		ctxPool[iface.TaskExecUnitQueueLowPriority] = append(
			ctxPool[iface.TaskExecUnitQueueLowPriority], &workerCtx{status: idleStatus})
	}
	idleCount[iface.TaskExecUnitQueueLowPriority] = resource.Config.WorkerLowPriorityCount()
}

func addIdleCount(queue string) {
	idleCountMutex.Lock()
	defer idleCountMutex.Unlock()
	idleCount[queue]++
}

func subIdleCount(queue string) {
	idleCountMutex.Lock()
	defer idleCountMutex.Unlock()
	idleCount[queue]--
}

func setIdleCount(queue string, count int) {
	idleCountMutex.Lock()
	defer idleCountMutex.Unlock()
	idleCount[queue] = count
}

func getIdleCount(queue string) int {
	idleCountMutex.Lock()
	defer idleCountMutex.Unlock()
	return idleCount[queue]
}

func StartWorkerPool(ctx context.Context) error {
	initCtxPools()
	fmt.Printf("ctxPool: %s\n", base_utils.Format(ctxPool))
	g := gtask.Group{}
	for queue := range ctxPool {
		queue := queue
		g.Go(func() error {
			return execute(ctx, queue)
		})
	}
	_, err := g.Wait()
	return err
}
