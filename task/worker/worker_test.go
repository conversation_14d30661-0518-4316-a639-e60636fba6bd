package worker

import (
	"context"
	"fmt"
	"sync/atomic"
	"testing"
	"time"

	"github.com/google/uuid"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

type mockBroker struct {
}

var callTimes atomic.Int64

func (m mockBroker) Publish(ctx context.Context, queue string, teus []*iface.TaskExecUnit) error {
	panic("implement me")
}

func (m mockBroker) Consume(ctx context.Context, queue string, consumer string, count int) ([]*iface.TaskExecUnit, error) {
	if callTimes.Load() == 0 {
		callTimes.Add(1)
		return []*iface.TaskExecUnit{
			{
				TaskExecUnitID: uuid.NewString(),
				TaskID:         "tttttaskid",
				TaskBatchID:    "run_correct_batch|run_correct__0",
				SendAt:         time.Now(),
				ExpireAt:       time.Now().Add(time.Second * 300),
				Entity:         "scs-bj-sssssssss",
				Dim:            "",
				Processor:      "run_correct__workflow",
				Parameters:     "",
			},
			{
				TaskExecUnitID: uuid.NewString(),
				TaskID:         "tttttaskid",
				TaskBatchID:    "run_error_batch|run_error__0",
				SendAt:         time.Now(),
				ExpireAt:       time.Now().Add(time.Second * 300),
				Entity:         "scs-bj-sssssssss",
				Dim:            "",
				Processor:      "run_error__workflow",
				Parameters:     "",
			},
			{
				TaskExecUnitID: uuid.NewString(),
				TaskID:         "tttttaskid",
				TaskBatchID:    "run_cancel_batch|run_cancel__0",
				SendAt:         time.Now(),
				ExpireAt:       time.Now().Add(time.Second * 300),
				Entity:         "scs-bj-sssssssss",
				Dim:            "",
				Processor:      "run_cancel__workflow",
				Parameters:     "",
			},
			{
				TaskExecUnitID: uuid.NewString(),
				TaskID:         "tttttaskid",
				TaskBatchID:    "run_not_exist_batch|run_not_exist__0",
				SendAt:         time.Now(),
				ExpireAt:       time.Now().Add(time.Second * 300),
				Entity:         "scs-bj-sssssssss",
				Dim:            "",
				Processor:      "run_not_exist__workflow",
				Parameters:     "",
			},
			{
				TaskExecUnitID: uuid.NewString(),
				TaskID:         "tttttaskid",
				TaskBatchID:    "run_already_timeout_batch|run_already_timeout__0",
				SendAt:         time.Now(),
				ExpireAt:       time.Now().Add(-time.Second * 300),
				Entity:         "scs-bj-sssssssss",
				Dim:            "",
				Processor:      "run_already_timeout__workflow",
				Parameters:     "",
			},
		}, nil
	}
	return nil, nil
}

func (m mockBroker) Pendings(ctx context.Context, queue string) ([]*iface.TaskExecUnit, error) {
	//TODO implement me
	panic("implement me")
}

func (m mockBroker) PurgePendings(ctx context.Context, queue string) error {
	//TODO implement me
	panic("implement me")
}

func (m mockBroker) PublishCmd(ctx context.Context, taskBatchId string, workerIDs []string, cmd string) error {
	//TODO implement me
	panic("implement me")
}

func (m mockBroker) ConsumeCmd(ctx context.Context, taskBatchId string, workerID string) (string, error) {
	// fmt.Printf("received task_batch_id %s\n", taskBatchId)
	if taskBatchId == "run_cancel_batch|run_cancel__0" {
		return iface.CmdCancel, nil
	}
	return "", nil
}

type mockConfig struct {
}

func (m mockConfig) FailoverMaxPrerunWaitingTime() time.Duration {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) FailoverPauseOtherTasks() bool {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) EngineRunInterval() time.Duration {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) EngineRunningTimeout() time.Duration {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) EngineLockTTL() time.Duration {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) EngineID() string {
	return "xxxxxxxxx"
}

func (m mockConfig) ScheduleRunInterval() time.Duration {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) ScheduleRunningTimeout() time.Duration {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) ScheduleLockTTL() time.Duration {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) ScheduleID() string {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) WorkerMaxReportErrorTime() time.Duration {
	return time.Second * 10
}

func (m mockConfig) WorkerRunInterval() time.Duration {
	return 300 * time.Millisecond
}

func (m mockConfig) WorkerHighPriorityCount() int {
	return 0
}

func (m mockConfig) WorkerMediumPriorityCount() int {
	return 10
}

func (m mockConfig) WorkerLowPriorityCount() int {
	return 0
}

func (m mockConfig) Level1RetryCount() int {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) Level1RetryInterval() time.Duration {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) Level2RetryCount() int {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) Level2RetryInterval() time.Duration {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) Level3RetryCount() int {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) Level3RetryInterval() time.Duration {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) ManualRetryCount() int {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) HTTPServerRunMode() string {
	//TODO implement me
	panic("implement me")
}

func (m mockConfig) HTTPServerPort() int {
	//TODO implement me
	panic("implement me")
}

type mockBackend struct {
}

var reports map[string]*iface.TaskExecResult

func (m mockBackend) Report(ctx context.Context, taskExecRet *iface.TaskExecResult) error {
	reports[taskExecRet.TaskBatchID] = taskExecRet
	return nil
}

func (m mockBackend) GetReports(ctx context.Context, batchID string) ([]*iface.TaskExecResult, error) {
	if r, ok := reports[batchID]; ok {
		return []*iface.TaskExecResult{r}, nil
	}
	return nil, nil
}

func (m mockBackend) DeleteReports(ctx context.Context, batchID string) error {
	//TODO implement me
	panic("implement me")
}

func (m mockBackend) DeleteReport(ctx context.Context, taskExecRet *iface.TaskExecResult) error {
	//TODO implement me
	panic("implement me")
}

func (m mockBackend) ReportWorkerStatus(ctx context.Context, workerStatus *iface.WorkerStatus) error {
	//TODO implement me
	panic("implement me")
}

func (m mockBackend) GetWorkerStatus(ctx context.Context) ([]*iface.WorkerStatus, error) {
	//TODO implement me
	panic("implement me")
}

type mockLogger struct {
}

func (m mockLogger) Debug(ctx context.Context, message string, fields ...logit.Field) {
	fmt.Printf("%s %s\n", "[DEBUG]", message)
}

func (m mockLogger) Trace(ctx context.Context, message string, fields ...logit.Field) {
	fmt.Printf("%s %s\n", "[TRACE]", message)
}

func (m mockLogger) Notice(ctx context.Context, message string, fields ...logit.Field) {
	fmt.Printf("%s %s\n", "[NOTICE]", message)
}

func (m mockLogger) Warning(ctx context.Context, message string, fields ...logit.Field) {
	fmt.Printf("%s %s\n", "[WARN]", message)
}

func (m mockLogger) Error(ctx context.Context, message string, fields ...logit.Field) {
	fmt.Printf("%s %s\n", "[ERROR]", message)
}

func (m mockLogger) Fatal(ctx context.Context, message string, fields ...logit.Field) {
	//TODO implement me
	panic(fmt.Sprintf("%s %s", "[FATAL]", message))
}

func (m mockLogger) Output(ctx context.Context, level logit.Level, callDepth int, message string, fields ...logit.Field) {
	//TODO implement me
	panic("implement me")
}

type mockTaskOp struct {
}

func (m mockTaskOp) CreateTask(ctx context.Context, task *iface.Task) error {
	//TODO implement me
	panic("implement me")
}

func (m mockTaskOp) CreateTasks(ctx context.Context, tasks []*iface.Task) error {
	//TODO implement me
	panic("implement me")
}

func (m mockTaskOp) UpdateTask(ctx context.Context, task *iface.Task) error {
	//TODO implement me
	panic("implement me")
}

func (m mockTaskOp) UpdateTasks(ctx context.Context, tasks []*iface.Task) error {
	//TODO implement me
	panic("implement me")
}

func (m mockTaskOp) RetrieveTasks(ctx context.Context, condFmt string, vals ...interface{}) ([]*iface.Task, error) {
	//TODO implement me
	panic("implement me")
}

func (m mockTaskOp) UpdateTaskToManual(ctx context.Context, taskID string, errStep string, errMsg string) error {
	//TODO implement me
	panic("implement me")
}

func (m mockTaskOp) CreateRecords(ctx context.Context, records []*iface.ProcessRecord) error {
	return nil
}

func (m mockTaskOp) RetrieveRecords(ctx context.Context, condFmt string, vals ...interface{}) ([]*iface.ProcessRecord, error) {
	//TODO implement me
	panic("implement me")
}

func (m mockTaskOp) CreateSubTasks(ctx context.Context, pTaskID string, tasks []*iface.CreateTaskParams) error {
	//TODO implement me
	panic("implement me")
}

func (m mockTaskOp) CheckSubTasksStatus(ctx context.Context, pTaskID string) (string, error) {
	//TODO implement me
	panic("implement me")
}

func (m mockTaskOp) GetTaskDetail(ctx context.Context, TaskID string) (*iface.Task, error) {
	//TODO implement me
	panic("implement me")
}

func initTest() {
	unittest.UnitTestInit(2)
	resource.Broker = &mockBroker{}
	resource.Config = &mockConfig{}
	resource.Backend = &mockBackend{}
	resource.EngineLogger = &mockLogger{}
	resource.TaskOperator = &mockTaskOp{}
	reports = make(map[string]*iface.TaskExecResult)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     "run_correct",
		Workflow: "workflow",
		StepProcess: func(ctx context.Context, teu *workflow.TaskExecUnit) error {
			fmt.Printf("run_correct: %s\n", base_utils.Format(teu))
			return nil
		},
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     "run_error",
		Workflow: "workflow",
		StepProcess: func(ctx context.Context, teu *workflow.TaskExecUnit) error {
			fmt.Printf("run_error: %s\n", base_utils.Format(teu))
			return fmt.Errorf("error")
		},
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     "run_cancel",
		Workflow: "workflow",
		StepProcess: func(ctx context.Context, teu *workflow.TaskExecUnit) error {
			fmt.Printf("run_cancel: %s\n", base_utils.Format(teu))
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(time.Millisecond * 300):
				return nil
			}
		},
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     "run_already_timeout",
		Workflow: "workflow",
		StepProcess: func(ctx context.Context, teu *workflow.TaskExecUnit) error {
			return nil
		},
	})
}

func Test_startProcess(t *testing.T) {
	initTest()
	g := gtask.Group{}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	g.Go(func() error {
		return StartWorkerPool(ctx)
	})
	_, _ = g.Wait()
	if reports["run_cancel_batch|run_cancel__0"].Status != "error" {
		t.Errorf("expect cancel, but got %s", reports["run_cancel_batch|run_cancel__0"].Status)
	}
	if reports["run_error_batch|run_error__0"].Status != "error" {
		t.Errorf("expect error, but got %s", reports["run_error_batch|run_error__0"].Status)
	}
	if reports["run_correct_batch|run_correct__0"].Status != "success" {
		t.Errorf("expect success, but got %s", reports["run_correct_batch|run_correct__0"].Status)
	}
	if reports["run_already_timeout_batch|run_already_timeout__0"].Status != "error" {
		t.Errorf("expect timeout, but got %s", reports["run_already_timeout_batch|run_already_timeout__0"].Status)
	}
	if reports["run_not_exist_batch|run_not_exist__0"].Status != "error" {
		t.Errorf("expect not exist, but got %s", reports["run_not_exist_batch|run_not_exist__0"].Status)
	}
}

func Test_setIdleCount(t *testing.T) {
	idleCount = make(map[string]int)
	setIdleCount("test", 1)
	if idleCount["test"] != 1 {
		t.Errorf("expect 1, but got %d", idleCount["test"])
	}
}
