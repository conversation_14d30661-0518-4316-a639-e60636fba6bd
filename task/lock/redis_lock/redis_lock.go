/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: C<PERSON>yi01 (<EMAIL>)
 * Date: 2022/04/19
 * File: task.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

package redis_lock

import (
	"context"
	"time"

	"github.com/google/uuid"
	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/utils/lock"
)

type redisLock struct {
	redisCli redis.Client
}

func (rl *redisLock) Lock(ctx context.Context, id string, ttl time.Duration) (unlock func(), err error) {
	owner := uuid.NewString()
	if err = rl.redisCli.Do(ctx, "eval", lock.LockLuaScript, "1", id, owner, int(ttl/time.Millisecond)).Err(); err != nil {
		return
	}
	unlock = func() { 
		rl.redisCli.Do(ctx, "eval", lock.UnLockLuaScript, "1", id, owner)
	}
	return
}

func NewRedisLock(redisCli redis.Client) iface.LockOperator {
	return &redisLock{redisCli: redisCli}
}