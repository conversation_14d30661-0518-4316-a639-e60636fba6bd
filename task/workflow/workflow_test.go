package workflow

import (
	"testing"
)

func TestAddStep(t *testing.T) {
	type args struct {
		stepParam *AddStepParam
		opts      []func(option *StepOption)
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: t.Name(),
			args: args{
				stepParam: &AddStepParam{
					Name:            "step-a",
					Workflow:        "a",
					StepProcess:     nil,
					SuccessNextStep: "step-b",
					ErrorNextStep:   "step-a",
				},
				opts: []func(option *StepOption){
					WithCancellable(true),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := AddStep(tt.args.stepParam, tt.args.opts...); (err != nil) != tt.wantErr {
				t.Errorf("AddStep() error = %v, wantErr %v", err, tt.wantErr)
			}
			sc, err := NewStepContextFromString("step-a__0", "a")
			if err != nil {
				t.Errorf("NewStepContextFromString() error = %v", err)
			}
			if sc.Step.Name != tt.args.stepParam.Name {
				t.Errorf("NewStepContextFromString() error = %v, want %v", sc.Step, tt.args.stepParam.Name)
			}
			if sc.Step.Workflow != tt.args.stepParam.Workflow {
				t.Errorf("NewStepContextFromString() error = %v, want %v", sc.Step, tt.args.stepParam.Workflow)
			}
			if sc.Step.SuccessNextStep != tt.args.stepParam.SuccessNextStep {
				t.Errorf("NewStepContextFromString() error = %v, want %v", sc.Step, tt.args.stepParam.SuccessNextStep)
			}
			if sc.Step.ErrorNextStep != tt.args.stepParam.ErrorNextStep {
				t.Errorf("NewStepContextFromString() error = %v, want %v", sc.Step, tt.args.stepParam.ErrorNextStep)
			}
			if sc.Step.Option.Cancellable != true {
				t.Errorf("NewStepContextFromString() error = %v, want %v", sc.Step, true)
			}
		})
	}
}
