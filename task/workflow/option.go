/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: C<PERSON>yi<PERSON> (<EMAIL>)
 * Date: 2022/04/19
 * File: task.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

package workflow

import "time"

func WithStepSplitHandler(stepSplit StepSplitHandler) func(option *StepOption) {
	return func(option *StepOption) {
		option.StepSplitHandler = stepSplit
	}
}

func WithNextStepHandler(nextStep NextStepHandler) func(option *StepOption) {
	return func(option *StepOption) {
		option.NextStepHandler = nextStep
	}
}

func WithMaxReentry(maxReentry int, maxReentryNextStep string) func(option *StepOption) {
	return func(option *StepOption) {
		option.MaxReentry = maxReentry
		option.MaxReentryNextStep = maxReentryNextStep
	}
}

func WithStepTimeout(stepTimeout time.Duration) func(option *StepOption) {
	return func(option *StepOption) {
		option.StepTimeout = stepTimeout
	}
}

func WithCancellable(cancellable bool) func(option *StepOption) {
	return func(option *StepOption) {
		option.Cancellable = cancellable
	}
}
