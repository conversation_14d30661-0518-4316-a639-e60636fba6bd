/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: Cuiyi01 (<EMAIL>)
 * Date: 2022/04/19
 * File: task.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

package workflow

import (
	"fmt"
	"strconv"
	"strings"
)

func (sc *StepContext) ToString() string {
	return sc.Step.Name + "__" + strconv.Itoa(sc.Reentry)
}

func NewStepContextFromString(stepStr string, workflow string) (*StepContext, error) {
	if stepStr == "" {
		steps, ok := workflowMap[workflow]
		if !ok {
			return nil, fmt.Errorf("workflow %s not found", workflow)
		}
		return &StepContext{
			Step:            steps[0],
			Reentry:         0,
			IsPromoted:      false,
			IsErrorPromoted: false,
		}, nil
	}
	
	chunks := strings.Split(stepStr, "__")
	if len(chunks) != 2 {
		return nil, fmt.<PERSON><PERSON><PERSON>("invalid step string format %s for split failed", stepStr)
	}
	reentry, err := strconv.Atoi(chunks[1])
	if err != nil {
		return nil, fmt.Errorf("invalid step string format %s for %s", stepStr, err.Error())
	}
	steps, ok := workflowMap[workflow]
	if !ok {
		return nil, fmt.Errorf("workflow %s not found", workflow)
	}
	for _, step := range steps {
		if step.Name == chunks[0] {
			return &StepContext{
				Step:            step,
				Reentry:         reentry,
				IsPromoted:      false,
				IsErrorPromoted: false,
			}, nil
		}
	}
	return nil, fmt.Errorf("step %s not found in workflow %s", chunks[0], workflow)
}
