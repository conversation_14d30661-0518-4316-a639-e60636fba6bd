/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON>yi<PERSON> (<EMAIL>)
 * Date: 2022/04/19
 * File: task.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

package workflow

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/iface"
)

const (
	MaxReentry = 15
)

func DefaultStepSplitHandler(ctx context.Context, task *iface.Task) ([]string, error) {
	return []string{task.Entity}, nil
}

func getNextStep(ctx context.Context, sc *StepContext, isSuccess bool, needManual bool) (*StepContext, error) {
	var nextStep string
	if needManual {
		sc.IsPromoted = true
		sc.IsErrorPromoted = true
		sc.NeedManual = true
		return sc, nil
	}
	if isSuccess {
		nextStep = sc.Step.SuccessNextStep
	} else {
		nextStep = sc.Step.ErrorNextStep
	}

	switch nextStep {
	case FinalStepError, FinalStepSuccess:
		return &StepContext{
			Step: &Step{
				Name:     nextStep,
				Workflow: sc.Step.Workflow,
			},
		}, nil
	}

	if nextStep == sc.Step.Name && (sc.Step.Option.MaxReentry <= 0 || sc.Reentry <= sc.Step.Option.MaxReentry) {
		sc.Reentry++
		sc.IsPromoted = true
		sc.IsErrorPromoted = !isSuccess
		return sc, nil
	}

	if sc.Step.Option.MaxReentry > 0 && sc.Reentry > sc.Step.Option.MaxReentry {
		nextStep = sc.Step.Option.MaxReentryNextStep
	}

	steps, ok := workflowMap[sc.Step.Workflow]
	if !ok {
		return nil, fmt.Errorf("workflow %s not found", sc.Step.Workflow)
	}
	for _, step := range steps {
		if step.Name == nextStep {
			return &StepContext{
				Step:            step,
				Reentry:         0,
				IsPromoted:      true,
				IsErrorPromoted: !isSuccess,
			}, nil
		}
	}
	if nextStep == FinalStepSuccess || nextStep == FinalStepError {
		return &StepContext{
			Step: &Step{
				Name:     nextStep,
				Workflow: sc.Step.Workflow,
			},
		}, nil
	}
	return nil, fmt.Errorf("step %s not found in workflow %s", nextStep, sc.Step.Workflow)
}

func DefaultNextStepHandler(ctx context.Context, task *iface.Task, ters []*iface.TaskExecResult) (*StepContext, error) {
	const (
		updateTolerance = 120 * time.Second
	)

	sc, err := NewStepContextFromString(task.Step, task.WorkFlow)
	if err != nil {
		return nil, err
	}

	var succCount, errorCount, runningCount, totalCount, manualCount int = 0, 0, 0, task.StepTEUCount, 0
	for _, ter := range ters {
		switch ter.Status {
		case iface.TaskExecResultStatusSuccess:
			succCount++
		case iface.TaskExecResultStatusError:
			errorCount++
		case iface.TaskExecResultStatusManual:
			manualCount++
		case iface.TaskStatusRunning:
			if time.Since(ter.LastUpdate) > updateTolerance {
				errorCount++
			} else {
				runningCount++
			}
		default:
			return nil, fmt.Errorf("invalid task result status %s", ter.Status)
		}
	}

	if runningCount > 0 {
		return sc, nil
	}

	if succCount+errorCount+manualCount < totalCount && time.Since(task.StepStartAt) < updateTolerance {
		return sc, nil
	}

	return getNextStep(ctx, sc, succCount == totalCount, manualCount > 0)
}
