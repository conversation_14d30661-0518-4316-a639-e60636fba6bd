/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON>yi<PERSON> (<EMAIL>)
 * Date: 2022/04/19
 * File: task.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

package workflow

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/iface"
)

const (
	FinalStepError   = "final_step_error"
	FinalStepSuccess = "final_step_success"
)

type (
	StepProcess func(ctx context.Context, teu *TaskExecUnit) error

	StepSplitHandler func(ctx context.Context, task *Task) ([]string, error)

	NextStepHandler func(ctx context.Context, task *Task, ters []*TaskExecResult) (*StepContext, error)
)

type AddStepParam struct {
	Name            string
	Workflow        string
	StepProcess     StepProcess
	SuccessNextStep string
	ErrorNextStep   string
}

type Step struct {
	Name            string
	Workflow        string
	StepProcess     StepProcess
	SuccessNextStep string
	ErrorNextStep   string
	Option          *StepOption
}

type StepContext struct {
	Step            *Step
	Reentry         int
	IsPromoted      bool
	IsErrorPromoted bool
	NeedManual      bool
}

type StepOption struct {
	StepSplitHandler   StepSplitHandler
	NextStepHandler    NextStepHandler
	StepTimeout        time.Duration
	MaxReentry         int
	MaxReentryNextStep string
	Cancellable        bool
}

type (
	TaskExecUnit = iface.TaskExecUnit

	Task = iface.Task

	TaskExecResult = iface.TaskExecResult
)

var (
	workflowMap map[string][]*Step = make(map[string][]*Step)
)

var (
	FinalErrorStepContext   *StepContext = &StepContext{IsPromoted: true, IsErrorPromoted: true}
	FinalSuccessStepContext *StepContext = &StepContext{IsPromoted: true, IsErrorPromoted: false}
)

func FindProcess(name string, workflow string) (StepProcess, error) {
	steps, ok := workflowMap[workflow]
	if !ok {
		return nil, fmt.Errorf("workflow %s not found", workflow)
	}
	for _, step := range steps {
		if step.Name == name {
			return step.StepProcess, nil
		}
	}
	return nil, fmt.Errorf("step %s not found in workflow %s", name, workflow)
}

func AddStep(stepParam *AddStepParam, opts ...func(option *StepOption)) error {
	steps, ok := workflowMap[stepParam.Workflow]
	if !ok {
		steps = []*Step{}
	}
	for _, step := range steps {
		if step.Name == stepParam.Name {
			return fmt.Errorf("duplicated step name %s", stepParam.Name)
		}
	}
	step := &Step{
		Name:            stepParam.Name,
		Workflow:        stepParam.Workflow,
		StepProcess:     stepParam.StepProcess,
		SuccessNextStep: stepParam.SuccessNextStep,
		ErrorNextStep:   stepParam.ErrorNextStep,
		Option: &StepOption{
			StepSplitHandler:   DefaultStepSplitHandler,
			NextStepHandler:    DefaultNextStepHandler,
			StepTimeout:        3 * time.Minute,
			MaxReentry:         0,
			MaxReentryNextStep: "",
			Cancellable:        false,
		},
	}
	for _, opt := range opts {
		opt(step.Option)
	}
	steps = append(steps, step)
	workflowMap[stepParam.Workflow] = steps
	return nil
}

func GetWorkFlows() map[string][]*Step {
	return workflowMap
}

func SetWorkFlows(wm map[string][]*Step) {
	workflowMap = wm
}
