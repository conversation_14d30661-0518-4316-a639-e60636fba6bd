package schedule

import (
	"context"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

type mockTaskOperator struct {
	retrieveTasksCount int
}

func (m *mockTaskOperator) CreateTask(ctx context.Context, task *iface.Task) error {
	//TODO implement me
	panic("implement me")
}

func (m *mockTaskOperator) CreateTasks(ctx context.Context, tasks []*iface.Task) error {
	//TODO implement me
	panic("implement me")
}

func (m *mockTaskOperator) UpdateTask(ctx context.Context, task *iface.Task) error {
	//TODO implement me
	panic("implement me")
}

func (m *mockTaskOperator) UpdateTasks(ctx context.Context, tasks []*iface.Task) error {
	//TODO implement me
	panic("implement me")
}

func (m *mockTaskOperator) RetrieveTasks(ctx context.Context, condFmt string, vals ...interface{}) ([]*iface.Task, error) {
	if m.retrieveTasksCount == 0 {
		m.retrieveTasksCount++
		return []*iface.Task{
			{
				TaskID: "201",
				Status: iface.TaskStatusPreRun,
				Mutex:  "n_app1",
			},
			{
				TaskID: "301",
				Status: iface.TaskStatusManual,
				Mutex:  "n_app2",
			},
			{
				TaskID: "401",
				Status: iface.TaskStatusManual,
				Mutex:  "n_app5",
			},
			{
				TaskID: "501",
				Status: iface.TaskStatusPreRun,
				Mutex:  "set_config_app6",
			},
		}, nil
	} else if m.retrieveTasksCount == 1 {
		m.retrieveTasksCount++
		return []*iface.Task{
			{
				TaskID: "501",
				Status: iface.TaskStatusPreRun,
				Mutex:  "migrate_1",
			},
		}, nil
	}
	return nil, fmt.Errorf("expect two times call")
}

func (m *mockTaskOperator) UpdateTaskToManual(ctx context.Context, taskID string, errStep string, errMsg string) error {
	//TODO implement me
	panic("implement me")
}

func (m *mockTaskOperator) CreateRecords(ctx context.Context, records []*iface.ProcessRecord) error {
	//TODO implement me
	panic("implement me")
}

func (m *mockTaskOperator) RetrieveRecords(ctx context.Context, condFmt string, vals ...interface{}) ([]*iface.ProcessRecord, error) {
	//TODO implement me
	panic("implement me")
}

func (m *mockTaskOperator) CreateSubTasks(ctx context.Context, pTaskID string, tasks []*iface.CreateTaskParams) error {
	//TODO implement me
	panic("implement me")
}

func (m *mockTaskOperator) CheckSubTasksStatus(ctx context.Context, pTaskID string) (string, error) {
	//TODO implement me
	panic("implement me")
}

func (m *mockTaskOperator) GetTaskDetail(ctx context.Context, TaskID string) (*iface.Task, error) {
	//TODO implement me
	panic("implement me")
}

func initMock() {
	resource.TaskOperator = &mockTaskOperator{}
}

func getTaskData() []*iface.Task {
	return []*iface.Task{
		&iface.Task{
			TaskID: "1",
			Status: iface.TaskStatusQueuing,
			Mutex:  "",
		},
		&iface.Task{
			TaskID: "2",
			Status: iface.TaskStatusQueuing,
			Mutex:  "n_app1",
		},
		&iface.Task{
			TaskID: "3",
			Status: iface.TaskStatusQueuing,
			Mutex:  "n_app2",
		},
		&iface.Task{
			TaskID: "4",
			Status: iface.TaskStatusQueuing,
			Mutex:  "n_app3",
		},
		&iface.Task{
			TaskID: "5",
			Status: iface.TaskStatusQueuing,
			Mutex:  "migrate_1",
		},
		&iface.Task{
			TaskID: "6",
			Status: iface.TaskStatusQueuing,
			Mutex:  "migrate_2",
		},
		&iface.Task{
			TaskID: "7",
			Status: iface.TaskStatusQueuing,
			Mutex:  "n_app5,set_config_app5",
		},
		&iface.Task{
			TaskID: "8",
			Status: iface.TaskStatusQueuing,
			Mutex:  "n_app6,set_config_app6",
		},
		&iface.Task{
			TaskID: "9",
			Status: iface.TaskStatusQueuing,
			Mutex:  "n_app7",
		},
		&iface.Task{
			TaskID: "10",
			Status: iface.TaskStatusQueuing,
			Mutex:  "set_config_app7",
		},
		&iface.Task{
			TaskID: "11",
			Status: iface.TaskStatusQueuing,
			Mutex:  "set_config_app8",
		},
	}
}

func runningTasks() []*iface.Task {
	return []*iface.Task{
		&iface.Task{
			TaskID: "101",
			Status: iface.TaskStatusPreRun,
			Mutex:  "n_app1",
		},
		&iface.Task{
			TaskID: "201",
			Status: iface.TaskStatusManual,
			Mutex:  "n_app2",
		},
		&iface.Task{
			TaskID: "301",
			Status: iface.TaskStatusManual,
			Mutex:  "n_app5",
		},
		&iface.Task{
			TaskID: "401",
			Status: iface.TaskStatusPreRun,
			Mutex:  "set_config_app6",
		},
		&iface.Task{
			TaskID: "501",
			Status: iface.TaskStatusPreRun,
			Mutex:  "migrate_1",
		},
		&iface.Task{
			TaskID: "601",
			Status: iface.TaskStatusPreRun,
			Mutex:  "n_app7,set_config_app7",
		},
		&iface.Task{
			TaskID: "701",
			Status: iface.TaskExecResultStatusManual,
			Mutex:  "set_config_app8",
		},
	}
}

func Test_schedualQueuing(t *testing.T) {
	unittest.UnitTestInit(2)
	initMock()
	tasks := getTaskData()
	_, err := schedualQueuing(context.Background(), tasks, runningTasks())
	if err != nil {
		t.Error(err)
	}
	if tasks[0].Status != iface.TaskStatusPreRun {
		t.Error("task 1 status not pre run")
	}
	if tasks[1].Status != iface.TaskStatusQueuing {
		t.Error("task 2 status not queuing")
	}
	if tasks[2].Status != iface.TaskStatusQueuing {
		t.Error("task 3 status not queuing")
	}
	if tasks[3].Status != iface.TaskStatusPreRun {
		t.Error("task 4 status not pre run")
	}
	if tasks[4].Status != iface.TaskStatusQueuing {
		t.Error("task 5 status not queuing")
	}
	if tasks[5].Status != iface.TaskStatusPreRun {
		t.Error("task 6 status not pre run")
	}
	if tasks[6].Status != iface.TaskStatusQueuing {
		t.Error("task 7 status not queuing")
	}
	if tasks[7].Status != iface.TaskStatusQueuing {
		t.Error("task 8 status not queuing")
	}
	if tasks[8].Status != iface.TaskStatusQueuing {
		t.Error("task 9 status not queuing")
	}
	if tasks[9].Status != iface.TaskStatusQueuing {
		t.Error("task 10 status not queuing")
	}
	if tasks[10].Status != iface.TaskStatusPreRun {
		t.Error("task 11 status not pre run")
	}
}
