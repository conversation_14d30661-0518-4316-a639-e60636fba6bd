/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: C<PERSON>yi01 (<EMAIL>)
 * Date: 2022/04/19
 * File: task.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

package schedule

import (
	"context"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/resource"
)

const (
	scheduleLockId = "x1_task_schedule_lock_d9bdc4d4"
)

type ScheduleHook struct {
	BeforeSchedule func(ctx context.Context, tasks []*iface.Task) error
	AfterSchedule  func(ctx context.Context, tasks []*iface.Task) error
}

var (
	hooks []ScheduleHook
)

func scheduleWaiting(ctx context.Context, tasks []*iface.Task) error {
	for _, task := range tasks {
		if task.Status == iface.TaskStatusWaiting && task.Schedule.Before(time.Now()) {
			resource.EngineLogger.Trace(ctx, "task status from waiting to queuing, not saved yet", logit.String("task_id", task.TaskID))
			task.Status = iface.TaskStatusQueuing
		}
	}
	return nil
}

func schedualQueuing(ctx context.Context, tasks []*iface.Task, runningTasks []*iface.Task) ([]func(ctx context.Context), error) {
	var noticeList []func(ctx context.Context)
	mutexRunningMap := make(map[string]bool)
	for _, runningTask := range runningTasks {
		for _, mutex := range strings.Split(runningTask.Mutex, ",") {
			// manual task only block n_ mutex task
			if !strings.HasPrefix(mutex, "n_") && runningTask.Status == iface.TaskExecResultStatusManual {
				continue
			}
			mutexRunningMap[mutex] = true
		}

	}

	for _, task := range tasks {
		if task.Status != iface.TaskStatusQueuing {
			continue
		}
		if task.Mutex == "" {
			// resource.EngineLogger.Trace(ctx, "task status from queuing to prerun, not saved yet", logit.String("task_id", task.TaskID))
			task.Status = iface.TaskStatusPreRun
			continue
		}
		mutexFound := false
		for _, mutex := range strings.Split(task.Mutex, ",") {
			if mutexRunningMap[mutex] {
				mutexFound = true
				break
			}
		}
		if !mutexFound {
			noticeList = append(noticeList, func(ctx context.Context) {
				resource.EngineLogger.Notice(ctx, "task scheduled to execute", logit.String("task_id", tasks[0].TaskID))
			})
			task.Status = iface.TaskStatusPreRun
			for _, mutex := range strings.Split(task.Mutex, ",") {
				mutexRunningMap[mutex] = true
			}
		}
	}

	return noticeList, nil
}

func scheduleOnce(ctx context.Context) error {
	unlock, err := resource.LockOperator.Lock(ctx, scheduleLockId, resource.Config.ScheduleLockTTL())
	if err != nil {
		resource.EngineLogger.Notice(ctx, "lock for scheduler failed", logit.String("lock_id", scheduleLockId))
		return err
	}
	defer unlock()

	tasks, err := resource.TaskOperator.RetrieveTasks(ctx, "status IN ?", []string{iface.TaskStatusWaiting, iface.TaskStatusQueuing})
	if err != nil {
		resource.EngineLogger.Warning(ctx, "get to schedual tasks failed failed", logit.Error("error", err))
	}
	resource.EngineLogger.Notice(ctx, fmt.Sprintf("get %d waiting or queuing tasks", len(tasks)))

	runningTasks, err := resource.TaskOperator.RetrieveTasks(ctx, "status IN ?",
		[]string{iface.TaskStatusPreRun, iface.TaskStatusRunning, iface.TaskStatusPostRun, iface.TaskStatusManual})
	if err != nil {
		resource.EngineLogger.Warning(ctx, "get running tasks failed failed", logit.Error("error", err))
	}

	for _, hook := range hooks {
		if err := hook.BeforeSchedule(ctx, tasks); err != nil {
			resource.EngineLogger.Warning(ctx, "execute before schedual hook failed", logit.Error("error", err))
		}
	}

	if err := scheduleWaiting(ctx, tasks); err != nil {
		resource.EngineLogger.Warning(ctx, "execute schedual waiting failed", logit.Error("error", err))
		return err
	}

	noticeList, err := schedualQueuing(ctx, tasks, runningTasks)
	if err != nil {
		resource.EngineLogger.Warning(ctx, "execute schedual queuing failed", logit.Error("error", err))
		return err
	}

	if err := resource.TaskOperator.UpdateTasks(ctx, tasks); err != nil {
		resource.EngineLogger.Warning(ctx, "update tasks failed", logit.Error("error", err))
		return err
	}

	for _, notice := range noticeList {
		notice(ctx)
	}

	return nil
}

func AddScheduleHook(hook ScheduleHook) {
	hooks = append(hooks, hook)
}

func StartSchedule(ctx context.Context) error {
	ctx = logit.ForkContext(ctx)
	logit.AddAllLevel(ctx, logit.String("schedule_id", resource.Config.ScheduleID()))
	resource.EngineLogger.Notice(ctx, "schedule start to process...")
	scheduleTimer := time.NewTimer(resource.Config.ScheduleRunInterval())
	for {
		scheduleTimer.Reset(resource.Config.ScheduleRunInterval())
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-scheduleTimer.C:
			{
				execOnce(ctx)
			}
		}
	}
}

func execOnce(ctx context.Context) {
	ctx, cancel := context.WithTimeout(ctx, resource.Config.ScheduleRunningTimeout())
	defer cancel()
	_ = scheduleOnce(ctx)
}
