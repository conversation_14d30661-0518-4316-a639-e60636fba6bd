/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: Cuiyi01 (<EMAIL>)
 * Date: 2022/04/19
 * File: task.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

package iface

import (
	"time"
)

type Config interface {
	EngineRunInterval() time.Duration
	EngineRunningTimeout() time.Duration
	EngineLockTTL() time.Duration
	EngineID() string
	ScheduleRunInterval() time.Duration
	ScheduleRunningTimeout() time.Duration
	ScheduleLockTTL() time.Duration
	ScheduleID() string
	WorkerMaxReportErrorTime() time.Duration
	WorkerRunInterval() time.Duration
	WorkerHighPriorityCount() int
	WorkerMediumPriorityCount() int
	WorkerLowPriorityCount() int
	Level1RetryCount() int
	Level1RetryInterval() time.Duration
	Level2RetryCount() int
	Level2RetryInterval() time.Duration
	Level3RetryCount() int
	Level3RetryInterval() time.Duration
	ManualRetryCount() int
	HTTPServerRunMode() string
	HTTPServerPort() int
	FailoverMaxPrerunWaitingTime() time.Duration
	FailoverPauseOtherTasks() bool
}
