/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: Cuiyi01 (<EMAIL>)
 * Date: 2022/04/19
 * File: task.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

package iface

import (
	"context"
	"time"
)

// Worker的执行状态的定义
const (
	// WorkerStatusIdle 空闲状态
	WorkerStatusIdle string = "idle"
	// WorkerStatusRunning 运行状态
	WorkerStatusRunning string = "running"
	// WorkerStatusOffline 离线状态，worker超过一定时间没有心跳就会进入离线状态，进入离线状态超过一定时间会被删除worker信息
	WorkerStatusOffline string = "offline"
)

// TaskExecResult Status的定义
const (
	TaskExecResultStatusSuccess = "success"
	TaskExecResultStatusError   = "error"
	TaskExecResultStatusRunning = "running"
	TaskExecResultStatusManual  = "manual"
)

const (
	CmdCancel = "cancel"
)

// TaskExecResult 框架Worker执行完成后，生成的执行结果
type TaskExecResult struct {
	TaskExecUnitID string
	TaskID         string
	TaskBatchID    string
	Entity         string
	StartAt        time.Time
	EndAt          time.Time
	Status         string
	Message        string
	LastUpdate     time.Time
}

// WorkerStatus Worker的状态信息
type WorkerStatus struct {
	WorkerID      string
	Status        string
	LastHeartBeat time.Time
}

// Backend 接口定义
type Backend interface {
	// Report 报告TaskExecUnit的执行结果
	Report(ctx context.Context, taskExecRet *TaskExecResult) error
	// GetReports 获取TaskExecUnit的执行结果
	GetReports(ctx context.Context, batchID string) ([]*TaskExecResult, error)
	// DeleteReports 清除Batch的执行结果
	DeleteReports(ctx context.Context, batchID string) error
	// DeleteReport 清除某个TaskExecResult
	DeleteReport(ctx context.Context, taskExecRet *TaskExecResult) error
	// ReportWorkerStatus 报告Worker的状态，由Worker的主线程定时执行
	ReportWorkerStatus(ctx context.Context, workerStatus *WorkerStatus) error
	// GetWorkerStatus 获取Workers的状态
	GetWorkerStatus(ctx context.Context) ([]*WorkerStatus, error)
}
