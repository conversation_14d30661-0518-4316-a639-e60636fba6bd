/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: Cuiyi01 (<EMAIL>)
 * Date: 2022/04/19
 * File: task.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

package iface

import (
	"context"
	"time"

	"gorm.io/plugin/soft_delete"
)

// Task.Status的枚举定义
const (
	TaskStatusWaiting string = "waiting"
	TaskStatusQueuing string = "queuing"
	TaskStatusPreRun  string = "prerun"
	TaskStatusRunning string = "running"
	TaskStatusPostRun string = "postrun"
	TaskStatusSuccess string = "success"
	TaskStatusError   string = "error"
	TaskStatusManual  string = "manual"
)

// Task.Priority的枚举定义
const (
	TaskPriorityHigh   string = "high"
	TaskPriorityMedium string = "medium"
	TaskPriorityLow    string = "low"
)

const (
	SubTasksStatusWaiting string = "waiting"
	SubTasksStatusRunning string = "running"
	SubTasksStatusSuccess string = "success"
	SubTasksStatusError   string = "error"
)

type Task struct {
	ID              uint64                `json:"id" gorm:"column:id;primaryKey;autoIncrement;not null;type:bigint(20)"`
	DeleteAt        soft_delete.DeletedAt `json:"-" gorm:"column:deleted_at;index;type:int(11)"`
	TaskID          string                `json:"taskId" gorm:"column:task_id;unique;not null;type:varchar(64)"`
	TaskBatchID     string                `json:"taskBatchId" gorm:"column:task_batch_id;index;not null;type:varchar(256)"`
	LastTaskBatchID string                `json:"lastTaskBatchId" gorm:"column:last_task_batch_id;index;not null;type:varchar(256)"`
	WorkFlow        string                `json:"workFlow" gorm:"column:work_flow;not null;type:varchar(64)"`
	Entity          string                `json:"entity" gorm:"column:entity;not null;type:varchar(64)"`
	EntityDim       string                `json:"entityDim" gorm:"column:entity_dim;not null;type:varchar(64)"`
	Status          string                `json:"status" gorm:"column:status;index;not null;type:varchar(64)"`
	Step            string                `json:"step" gorm:"column:step;not null;type:varchar(64)"`
	StepStartAt     time.Time             `json:"stepStartAt" gorm:"column:step_start_at;autoCreateTime;not null;type:timestamp"`
	StepDeadline    time.Time             `json:"stepDeadline" gorm:"column:step_deadline;autoCreateTime;not null;type:timestamp"`
	StepTEUCount    int                   `json:"stepTeuCount" gorm:"column:step_teu_count;not null;type:int(11)"`
	CreatedAt       time.Time             `json:"createdAt" gorm:"column:created_at;autoCreateTime;index;not null;type:timestamp"`
	UpdatedAt       time.Time             `json:"updatedAt" gorm:"column:updated_at;autoCreateTime;index;not null;type:timestamp"`
	Schedule        time.Time             `json:"schedule" gorm:"column:schedule;autoCreateTime;not null;type:timestamp"`
	Cron            string                `json:"cron" gorm:"column:cron;not null;type:varchar(64)"`
	Deadline        time.Time             `json:"deadline" gorm:"column:deadline;autoCreateTime;not null;type:timestamp"`
	StartedAt       time.Time             `json:"startAt" gorm:"column:start_at;autoCreateTime;not null;type:timestamp"`
	CompletedAt     time.Time             `json:"completedAt" gorm:"column:completed_at;autoCreateTime;not null;type:timestamp"`
	Mutex           string                `json:"mutex" gorm:"column:mutex;not null;type:varchar(64)"`
	Priority        string                `json:"priority" gorm:"column:priority;not null;type:varchar(64)"`
	Parameters      string                `json:"parameters" gorm:"column:parameters;not null;type:longtext"`
	ErrorStep       string                `json:"errorStep" gorm:"column:error_step;not null;type:varchar(256)"`
	ErrMsg          string                `json:"errMsg" gorm:"column:err_msg;not null;type:longtext"`
	Version         int                   `json:"version" gorm:"column:version;not null;type:int(11)"`
	PTaskID         string                `json:"pTaskId" gorm:"column:p_task_id;not null;type:varchar(64)"`
}

type ProcessRecord struct {
	ID          uint64    `json:"id" gorm:"column:id;primaryKey;autoIncrement;not null;type:bigint(20)"`
	TaskID      string    `json:"taskId" gorm:"column:task_id;unique;not null;type:varchar(64)"`
	TaskBatchID string    `json:"taskBatchId" gorm:"column:task_batch_id;index;not null;type:varchar(256)"`
	WorkFlow    string    `json:"workFlow" gorm:"column:work_flow;not null;type:varchar(64)"`
	Entity      string    `json:"entity" gorm:"column:entity;not null;type:varchar(64)"`
	Step        string    `json:"step" gorm:"column:step;not null;type:varchar(64)"`
	Operation   string    `json:"operation" gorm:"column:operation;not null;type:varchar(64)"`
	CreatedAt   time.Time `json:"createdAt" gorm:"column:created_at;autoCreateTime;index;not null;type:timestamp"`
	StartedAt   time.Time `json:"startedAt" gorm:"column:start_at;autoCreateTime;not null;type:timestamp"`
	Cost        int       `json:"cost" gorm:"column:cost;not null;type:int(11)"`
	Parameters  string    `json:"parameters" gorm:"column:parameters;not null;type:longtext"`
	ErrMsg      string    `json:"errMsg" gorm:"column:err_msg;not null;type:longtext"`
}

type CreateTaskParams struct {
	WorkFlow   string        `json:"workFlow" validate:"required"`
	Schedule   time.Time     `json:"schedule" validate:"required"`
	Timeout    time.Duration `json:"timeout" validate:"required"`
	Mutex      string        `json:"mutex" validate:"required"`
	Entity     string        `json:"entity" validate:"required"`
	Parameters interface{}   `json:"parameters" validate:"required"`
}

func (ProcessRecord) TableName() string {
	return "process_records"
}

type TaskOperator interface {
	// GetInitSql(ctx context.Context) string
	CreateTask(ctx context.Context, task *Task) error
	CreateTasks(ctx context.Context, tasks []*Task) error
	UpdateTask(ctx context.Context, task *Task) error
	UpdateTasks(ctx context.Context, tasks []*Task) error
	RetrieveTasks(ctx context.Context, condFmt string, vals ...interface{}) ([]*Task, error)
	UpdateTaskToManual(ctx context.Context, taskID string, errStep string, errMsg string) error
	CreateRecords(ctx context.Context, records []*ProcessRecord) error
	RetrieveRecords(ctx context.Context, condFmt string, vals ...interface{}) ([]*ProcessRecord, error)
	CreateSubTasks(ctx context.Context, pTaskID string, tasks []*CreateTaskParams) error
	CheckSubTasksStatus(ctx context.Context, pTaskID string) (string, error)
	GetTaskDetail(ctx context.Context, TaskID string) (*Task, error)
}

func GetQueue(priority string) string {
	switch priority {
	case TaskPriorityHigh:
		return TaskExecUnitQueueHighPriority
	case TaskPriorityMedium:
		return TaskExecUnitQueueMediumPriority
	case TaskPriorityLow:
		return TaskExecUnitQueueLowPriority
	default:
		return TaskExecUnitQueueMediumPriority
	}
}
