/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: Cuiyi01 (<EMAIL>)
 * Date: 2022/04/19
 * File: task.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

package iface

import (
	"context"
	"time"
)

const (
	TaskExecUnitQueueHighPriority   string = "high-priority-queue"
	TaskExecUnitQueueMediumPriority string = "medium-priority-queue"
	TaskExecUnitQueueLowPriority    string = "low-priority-queue"
)

// TaskExecUnit Worker执行任务的Message定义
type TaskExecUnit struct {
	TaskExecUnitID string
	TaskID         string
	TaskBatchID    string
	SendAt         time.Time
	ExpireAt       time.Time
	Entity         string
	Dim            string
	Processor      string
	Parameters     string
	PTaskID        string
}

// Broker 向Worker发送任务、命令；Worker接收任务、命令的接口
type Broker interface {
	Publish(ctx context.Context, queue string, teus []*TaskExecUnit) error
	Consume(ctx context.Context, queue string, consumer string, count int) ([]*TaskExecUnit, error)
	Pendings(ctx context.Context, queue string) ([]*TaskExecUnit, error)
	PurgePendings(ctx context.Context, queue string) error
	PublishCmd(ctx context.Context, taskBatchId string, workerIDs []string, cmd string) error
	ConsumeCmd(ctx context.Context, taskBatchId string, workerID string) (string, error)
}
