package gorm_model

import (
	"database/sql/driver"
	"fmt"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
)

func Test_gormTaskOp_CreateSubTasks(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	createTaskParams := iface.CreateTaskParams{
		WorkFlow: "test-workflow",
		Schedule: time.Now(),
		Timeout:  0,
		Mutex:    "test-mutex",
		Entity:   "test-entity",
	}
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `tasks`").WithArgs(
		sqlmock.AnyArg(),
		sqlmock.AnyArg(), // TaskID
		sqlmock.AnyArg(), // TaskBatchID
		sqlmock.AnyArg(), // LastTaskBatchID
		createTaskParams.WorkFlow,
		createTaskParams.Entity,
		sqlmock.AnyArg(), // EntityDim
		"waiting",
		sqlmock.AnyArg(), // Step
		sqlmock.AnyArg(), // StepStartAt
		sqlmock.AnyArg(), // StepDeadline
		0,                // StepTEUCount
		sqlmock.AnyArg(), // CreatedAt
		sqlmock.AnyArg(), // UpdatedAt
		createTaskParams.Schedule,
		sqlmock.AnyArg(), // Cron
		createTaskParams.Schedule.Add(24*time.Hour), // Deadline
		sqlmock.AnyArg(), // StartedAt
		sqlmock.AnyArg(), // CompletedAt
		createTaskParams.Mutex,
		sqlmock.AnyArg(), // Priority
		sqlmock.AnyArg(), // Parameters
		sqlmock.AnyArg(), // ErrorStep
		sqlmock.AnyArg(), // ErrMsg
		0,                // Version
		"test-task-id",   // PTaskID
	).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	gto := &gormTaskOp{
		db: gormDB,
	}
	if err := gto.CreateSubTasks(nil, "test-task-id", []*iface.CreateTaskParams{&createTaskParams}); err != nil {
		t.Fatalf("error creating sub tasks: %s", err)
	}
	err = mock.ExpectationsWereMet()
	if err != nil {
		t.Errorf("unfulfilled expectations: %s", err)
	}
}

func Test_gormTaskOp_CheckSubTasksStatusRunning(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	headers := []string{"id", "deleted_at", "task_id", "task_batch_id", "last_task_batch_id", "work_flow", "entity", "entity_dim",
		"status", "step", "step_start_at", "step_deadline", "step_teu_count", "created_at", "updated_at", "schedule", "cron", "deadline",
		"start_at", "completed_at", "mutex", "priority", "parameters", "error_step", "err_msg", "version", "p_task_id"}
	date_1 := []driver.Value{0, "0", "c1b3f8db-5909-4e23-ba9a-bec4a420caa2", "", "", "test-workflow", "test-entity", "",
		iface.TaskStatusRunning, "", time.Now(), time.Now(), 0, time.Now(), time.Now(), time.Now(), "", time.Now(),
		time.Now(), time.Now(), "test-mutex", "", "null", "", "", 0, "test-task-id"}
	date_2 := []driver.Value{0, "0", "c1b3f8db-5909-4e23-ba9a-bec4a420caa2", "", "", "test-workflow", "test-entity", "",
		iface.TaskStatusError, "", time.Now(), time.Now(), 0, time.Now(), time.Now(), time.Now(), "", time.Now(),
		time.Now(), time.Now(), "test-mutex", "", "null", "", "", 0, "test-task-id"}
	date_3 := []driver.Value{0, "0", "c1b3f8db-5909-4e23-ba9a-bec4a420caa2", "", "", "test-workflow", "test-entity", "",
		iface.TaskStatusSuccess, "", time.Now(), time.Now(), 0, time.Now(), time.Now(), time.Now(), "", time.Now(),
		time.Now(), time.Now(), "test-mutex", "", "null", "", "", 0, "test-task-id"}
	rows := sqlmock.NewRows(headers).AddRow(date_1...).AddRow(date_2...).AddRow(date_3...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	gto := &gormTaskOp{
		db: gormDB,
	}
	status, err := gto.CheckSubTasksStatus(nil, "test-task-id")
	if err != nil {
		t.Fatalf("error checking sub tasks status: %s", err)
	}
	if status != iface.SubTasksStatusRunning {
		t.Fatalf("expecting status running, got %s", status)
	}
}

func Test_gormTaskOp_CheckSubTasksStatusError(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	headers := []string{"id", "deleted_at", "task_id", "task_batch_id", "last_task_batch_id", "work_flow", "entity", "entity_dim",
		"status", "step", "step_start_at", "step_deadline", "step_teu_count", "created_at", "updated_at", "schedule", "cron", "deadline",
		"start_at", "completed_at", "mutex", "priority", "parameters", "error_step", "err_msg", "version", "p_task_id"}
	date_1 := []driver.Value{0, "0", "c1b3f8db-5909-4e23-ba9a-bec4a420caa2", "", "", "test-workflow", "test-entity", "",
		iface.TaskStatusError, "", time.Now(), time.Now(), 0, time.Now(), time.Now(), time.Now(), "", time.Now(),
		time.Now(), time.Now(), "test-mutex", "", "null", "", "", 0, "test-task-id"}
	date_2 := []driver.Value{0, "0", "c1b3f8db-5909-4e23-ba9a-bec4a420caa2", "", "", "test-workflow", "test-entity", "",
		iface.TaskStatusError, "", time.Now(), time.Now(), 0, time.Now(), time.Now(), time.Now(), "", time.Now(),
		time.Now(), time.Now(), "test-mutex", "", "null", "", "", 0, "test-task-id"}
	date_3 := []driver.Value{0, "0", "c1b3f8db-5909-4e23-ba9a-bec4a420caa2", "", "", "test-workflow", "test-entity", "",
		iface.TaskStatusSuccess, "", time.Now(), time.Now(), 0, time.Now(), time.Now(), time.Now(), "", time.Now(),
		time.Now(), time.Now(), "test-mutex", "", "null", "", "", 0, "test-task-id"}
	rows := sqlmock.NewRows(headers).AddRow(date_1...).AddRow(date_2...).AddRow(date_3...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	gto := &gormTaskOp{
		db: gormDB,
	}
	status, err := gto.CheckSubTasksStatus(nil, "test-task-id")
	if err != nil {
		t.Fatalf("error checking sub tasks status: %s", err)
	}
	if status != iface.SubTasksStatusError {
		t.Fatalf("expecting status running, got %s", status)
	}
}

func Test_gormTaskOp_CheckSubTasksStatusSuccess(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	headers := []string{"id", "deleted_at", "task_id", "task_batch_id", "last_task_batch_id", "work_flow", "entity", "entity_dim",
		"status", "step", "step_start_at", "step_deadline", "step_teu_count", "created_at", "updated_at", "schedule", "cron", "deadline",
		"start_at", "completed_at", "mutex", "priority", "parameters", "error_step", "err_msg", "version", "p_task_id"}
	date_1 := []driver.Value{0, "0", "c1b3f8db-5909-4e23-ba9a-bec4a420caa2", "", "", "test-workflow", "test-entity", "",
		iface.TaskStatusSuccess, "", time.Now(), time.Now(), 0, time.Now(), time.Now(), time.Now(), "", time.Now(),
		time.Now(), time.Now(), "test-mutex", "", "null", "", "", 0, "test-task-id"}
	date_2 := []driver.Value{0, "0", "c1b3f8db-5909-4e23-ba9a-bec4a420caa2", "", "", "test-workflow", "test-entity", "",
		iface.TaskStatusSuccess, "", time.Now(), time.Now(), 0, time.Now(), time.Now(), time.Now(), "", time.Now(),
		time.Now(), time.Now(), "test-mutex", "", "null", "", "", 0, "test-task-id"}
	date_3 := []driver.Value{0, "0", "c1b3f8db-5909-4e23-ba9a-bec4a420caa2", "", "", "test-workflow", "test-entity", "",
		iface.TaskStatusSuccess, "", time.Now(), time.Now(), 0, time.Now(), time.Now(), time.Now(), "", time.Now(),
		time.Now(), time.Now(), "test-mutex", "", "null", "", "", 0, "test-task-id"}
	rows := sqlmock.NewRows(headers).AddRow(date_1...).AddRow(date_2...).AddRow(date_3...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	gto := &gormTaskOp{
		db: gormDB,
	}
	status, err := gto.CheckSubTasksStatus(nil, "test-task-id")
	if err != nil {
		t.Fatalf("error checking sub tasks status: %s", err)
	}
	if status != iface.SubTasksStatusSuccess {
		t.Fatalf("expecting status running, got %s", status)
	}
}

func Test_gormTaskOp_CheckSubTasksStatusEmpty(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	headers := []string{"id", "deleted_at", "task_id", "task_batch_id", "last_task_batch_id", "work_flow", "entity", "entity_dim",
		"status", "step", "step_start_at", "step_deadline", "step_teu_count", "created_at", "updated_at", "schedule", "cron", "deadline",
		"start_at", "completed_at", "mutex", "priority", "parameters", "error_step", "err_msg", "version", "p_task_id"}
	rows := sqlmock.NewRows(headers)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	gto := &gormTaskOp{
		db: gormDB,
	}
	status, err := gto.CheckSubTasksStatus(nil, "test-task-id")
	if err != nil {
		t.Fatalf("error checking sub tasks status: %s", err)
	}
	if status != iface.SubTasksStatusSuccess {
		t.Fatalf("expecting status running, got %s", status)
	}
}

func Test_gormTaskOp_GetTaskDetail(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	headers := []string{"id", "deleted_at", "task_id", "task_batch_id", "last_task_batch_id", "work_flow", "entity", "entity_dim",
		"status", "step", "step_start_at", "step_deadline", "step_teu_count", "created_at", "updated_at", "schedule", "cron", "deadline",
		"start_at", "completed_at", "mutex", "priority", "parameters", "error_step", "err_msg", "version", "p_task_id"}
	rows := sqlmock.NewRows(headers)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	gto := &gormTaskOp{
		db: gormDB,
	}
	record, err := gto.GetTaskDetail(nil, "test")
	if err != nil {
		fmt.Printf("error get task detail: %s", err)
	}
	if record != nil {
		fmt.Println(record)
	}
}

func Test_gormTaskOp_GetTaskDetailSuccess(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	headers := []string{"id", "deleted_at", "task_id", "task_batch_id", "last_task_batch_id", "work_flow", "entity", "entity_dim",
		"status", "step", "step_start_at", "step_deadline", "step_teu_count", "created_at", "updated_at", "schedule", "cron", "deadline",
		"start_at", "completed_at", "mutex", "priority", "parameters", "error_step", "err_msg", "version", "p_task_id"}
	date_1 := []driver.Value{1, "0", "c1b3f8db-5909-4e23-ba9a-bec4a420caa1", "", "", "test-workflow", "test-entity", "",
		iface.TaskStatusSuccess, "", time.Now(), time.Now(), 0, time.Now(), time.Now(), time.Now(), "", time.Now(),
		time.Now(), time.Now(), "test-mutex", "", "null", "", "", 0, "test-task-id"}
	rows := sqlmock.NewRows(headers).AddRow(date_1...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	gto := &gormTaskOp{
		db: gormDB,
	}
	record, err := gto.GetTaskDetail(nil, "test")
	if err != nil {
		fmt.Printf("error get task detail: %s", err)
	}
	if record != nil {
		fmt.Println(record)
	}
}

func Test_gormTaskOp_GetTaskDetailFail(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	defer db.Close()
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	headers := []string{"id", "deleted_at", "task_id", "task_batch_id", "last_task_batch_id", "work_flow", "entity", "entity_dim",
		"status", "step", "step_start_at", "step_deadline", "step_teu_count", "created_at", "updated_at", "schedule", "cron", "deadline",
		"start_at", "completed_at", "mutex", "priority", "parameters", "error_step", "err_msg", "version", "p_task_id"}
	date_1 := []driver.Value{1, "0", "c1b3f8db-5909-4e23-ba9a-bec4a420caa1", "", "", "test-workflow", "test-entity", "",
		iface.TaskStatusSuccess, "", time.Now(), time.Now(), 0, time.Now(), time.Now(), time.Now(), "", time.Now(),
		time.Now(), time.Now(), "test-mutex", "", "null", "", "", 0, "test-task-id"}
	date_2 := []driver.Value{2, "0", "c1b3f8db-5909-4e23-ba9a-bec4a420caa2", "", "", "test-workflow", "test-entity", "",
		iface.TaskStatusSuccess, "", time.Now(), time.Now(), 0, time.Now(), time.Now(), time.Now(), "", time.Now(),
		time.Now(), time.Now(), "test-mutex", "", "null", "", "", 0, "test-task-id"}
	date_3 := []driver.Value{3, "0", "c1b3f8db-5909-4e23-ba9a-bec4a420caa3", "", "", "test-workflow", "test-entity", "",
		iface.TaskStatusSuccess, "", time.Now(), time.Now(), 0, time.Now(), time.Now(), time.Now(), "", time.Now(),
		time.Now(), time.Now(), "test-mutex", "", "null", "", "", 0, "test-task-id"}
	rows := sqlmock.NewRows(headers).AddRow(date_1...).AddRow(date_2...).AddRow(date_3...)
	mock.ExpectQuery("^SELECT .*").WillReturnRows(rows)
	gto := &gormTaskOp{
		db: gormDB,
	}
	record, err := gto.GetTaskDetail(nil, "test")
	if err != nil {
		fmt.Printf("error get task detail: %s", err)
	}
	if record != nil {
		fmt.Println(record)
	}
}
