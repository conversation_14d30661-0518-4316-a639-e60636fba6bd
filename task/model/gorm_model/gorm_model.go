/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/11/30, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
使用Redis实现的的Broker
*/

package gorm_model

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"gorm.io/gorm"

	"icode.baidu.com/baidu/scs/x1-base/task/iface"
)

type gormTaskOp struct {
	db *gorm.DB
}

func (gto *gormTaskOp) CreateTask(ctx context.Context, task *iface.Task) error {
	return gto.db.WithContext(ctx).Create(task).Error
}

func (gto *gormTaskOp) CreateTasks(ctx context.Context, tasks []*iface.Task) error {
	return gto.db.WithContext(ctx).Create(&tasks).Error
}

func (gto *gormTaskOp) UpdateTask(ctx context.Context, task *iface.Task) error {
	task.Version++
	task.UpdatedAt = time.Now()
	ret := gto.db.Model(task).Where("task_id = ? AND version = ?", task.TaskID, task.Version-1).Updates(task)
	if ret.Error != nil {
		task.Version--
		return ret.Error
	}
	if ret.RowsAffected <= 0 {
		task.Version--
		return fmt.Errorf("forbid outdated update, outdated version %d", task.Version)
	}
	return nil
}

func (gto *gormTaskOp) UpdateTasks(ctx context.Context, tasks []*iface.Task) error {
	return gto.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, task := range tasks {
			task.Version++
			task.UpdatedAt = time.Now()
			ret := tx.Model(task).Where("task_id = ? AND version = ?", task.TaskID, task.Version-1).Updates(task)
			if ret.Error != nil {
				task.Version--
				return ret.Error
			}
			if ret.RowsAffected <= 0 {
				task.Version--
				return fmt.Errorf("forbid outdated update, outdated version %d", task.Version)
			}
		}
		return nil
	})
}

func (gto *gormTaskOp) UpdateTaskToManual(ctx context.Context, taskID string, errStep string, errMsg string) error {
	ret := gto.db.Model(&iface.Task{}).Where("task_id = ?", taskID).Updates(map[string]interface{}{
		"status": iface.TaskStatusManual, "error_step": errStep, "err_msg": errMsg,
	})
	if ret.Error != nil {
		return ret.Error
	}
	if ret.RowsAffected <= 0 {
		return fmt.Errorf("update status failed %s", taskID)
	}
	return nil
}

func (gto *gormTaskOp) RetrieveTasks(ctx context.Context, condFmt string, vals ...interface{}) ([]*iface.Task, error) {
	var tasks []*iface.Task
	if err := gto.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Where(condFmt, vals...).Find(&tasks).Error; err != nil && err != gorm.ErrRecordNotFound {
			return err
		}
		return nil
	}); err != nil {
		return nil, err
	}
	return tasks, nil
}

func (gto *gormTaskOp) CreateRecords(ctx context.Context, records []*iface.ProcessRecord) error {
	return gto.db.WithContext(ctx).Create(&records).Error
}

func (gto *gormTaskOp) RetrieveRecords(ctx context.Context, condFmt string, vals ...interface{}) ([]*iface.ProcessRecord, error) {
	var records []*iface.ProcessRecord
	if err := gto.db.WithContext(ctx).Where(condFmt, vals...).Find(&records).Error; err != nil && err != gorm.ErrRecordNotFound {
		return records, err
	}
	return records, nil
}

func (gto *gormTaskOp) CreateSubTasks(ctx context.Context, pTaskID string, tasks []*iface.CreateTaskParams) error {
	taskList := make([]*iface.Task, 0, len(tasks))
	for _, t := range tasks {
		tmout := 24 * time.Hour
		if t.Timeout != 0 {
			tmout = t.Timeout
		}
		taskList = append(taskList, &iface.Task{
			TaskID:     uuid.New().String(),
			WorkFlow:   t.WorkFlow,
			Status:     "waiting",
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
			Schedule:   t.Schedule,
			Deadline:   t.Schedule.Add(tmout),
			Mutex:      t.Mutex,
			Entity:     t.Entity,
			PTaskID:    pTaskID,
			Parameters: base_utils.Format(t.Parameters),
		})
	}
	return gto.db.WithContext(ctx).Create(&taskList).Error
}

func (gto *gormTaskOp) CheckSubTasksStatus(ctx context.Context, pTaskID string) (string, error) {
	var records []*iface.Task
	if err := gto.db.WithContext(ctx).Where("p_task_id = ?", pTaskID).Find(&records).Error; err != nil && err != gorm.ErrRecordNotFound {
		return "", err
	}
	if len(records) == 0 {
		return iface.TaskStatusSuccess, nil
	}
	errCount, succCount := 0, 0
	for _, record := range records {
		if record.Status == iface.TaskStatusError {
			errCount++
		} else if record.Status == iface.TaskStatusSuccess {
			succCount++
		}
	}
	if errCount+succCount < len(records) {
		return iface.SubTasksStatusRunning, nil
	}
	if errCount > 0 {
		return iface.SubTasksStatusError, nil
	}
	return iface.SubTasksStatusSuccess, nil
}

func (gto *gormTaskOp) GetTaskDetail(ctx context.Context, TaskID string) (*iface.Task, error) {
	var records []*iface.Task
	if err := gto.db.WithContext(ctx).Where("task_id = ?", TaskID).Find(&records).Error; err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	if len(records) == 0 {
		return nil, errors.New("task not found")
	}
	if len(records) > 1 {
		return nil, errors.New("task num > 1")
	}
	return records[0], nil
}

func NewGormTaskOperator(db *gorm.DB) iface.TaskOperator {
	return &gormTaskOp{
		db: db,
	}
}
