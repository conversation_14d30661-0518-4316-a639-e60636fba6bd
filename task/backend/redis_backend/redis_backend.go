/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/11/30, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
使用Redis实现的的Broker
*/

package redis_backend

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
)

const (
	workerStatusKey string = "worker-status-key"
)

type redisBackend struct {
	redisClient redis.Client
}

func (rb *redisBackend) Report(ctx context.Context, taskExecRet *iface.TaskExecResult) error {
	val, err := json.Marshal(taskExecRet)
	if err != nil {
		return err
	}
	return rb.redisClient.HSet(ctx, taskExecRet.TaskBatchID, taskExecRet.TaskExecUnitID, string(val)).Err()
}

func (rb *redisBackend) GetReports(ctx context.Context, batchId string) ([]*iface.TaskExecResult, error) {
	hGetAll, err := rb.redisClient.HGetAll(ctx, batchId).Result()
	if err != nil && err != redis.ErrNil {
		return nil, err
	}
	ret := []*iface.TaskExecResult{}
	for _, val := range hGetAll {
		ter := &iface.TaskExecResult{}
		if err := json.Unmarshal([]byte(val), ter); err != nil {
			return nil, err
		}
		ret = append(ret, ter)
	}
	return ret, nil
}

func (rb *redisBackend) DeleteReports(ctx context.Context, batchID string) error {
	return rb.redisClient.Del(ctx, batchID).Err()
}

func (rb *redisBackend) DeleteReport(ctx context.Context, taskExecRet *iface.TaskExecResult) error {
	return rb.redisClient.HDel(ctx, taskExecRet.TaskBatchID, taskExecRet.TaskExecUnitID).Err()
}

func (rb *redisBackend) ReportWorkerStatus(ctx context.Context, workerStatus *iface.WorkerStatus) error {
	return rb.redisClient.HSet(ctx, workerStatusKey, workerStatus.WorkerID, workerStatus.Status).Err()
}

func (rb *redisBackend) GetWorkerStatus(ctx context.Context) ([]*iface.WorkerStatus, error) {
	hGetAll, err := rb.redisClient.HGetAll(ctx, workerStatusKey).Result()
	if err != nil && err != redis.ErrNil {
		return nil, err
	}
	ret := make([]*iface.WorkerStatus, len(hGetAll))
	idx := 0
	for key, val := range hGetAll {
		ret[idx].WorkerID = key
		ret[idx].Status = val
		idx++
	}
	return ret, nil
}

func NewRedisBackend(redisClient redis.Client) iface.Backend {
	return &redisBackend{
		redisClient: redisClient,
	}
}
