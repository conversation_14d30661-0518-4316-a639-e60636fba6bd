/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/11/30, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
使用Redis实现的的Broker
*/

package redis_broker

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
)

const (
	// WorkerCmdQueuePrefix 向Worker发送命令使用的KEY的前缀
	WorkerCmdQueuePrefix string = "cmd-queue"
	// WorkersGroup Worker的消费组的名字
	WorkersGroup string = "workers-group"
	// WorkerCmdTTL 向Worker发送命令在Redis中的生命周期
	WorkerCmdTTL = 60 * time.Second
	// WorkerConsumeBlock Worker消费时阻塞的最长时间
	WorkerConsumeBlock = 1 * time.Millisecond
)

type redisBroker struct {
	redisClient redis.Client
}

func teuToXAddArgs(queue string, teu *iface.TaskExecUnit) *redis.XAddArgs {
	msg, _ := json.Marshal(teu)
	return &redis.XAddArgs{
		Stream: queue,
		Values: []string{
			"TaskExecUnitID",
			teu.TaskExecUnitID,
			"Message",
			string(msg),
		},
	}
}

func getXMessageItem(key string, xs *redis.XMessage) (string, error) {
	r, found := xs.Values[key]
	if !found {
		return "", fmt.Errorf("field %s is expected in xmessage", key)
	}
	return r.(string), nil
}

func xMessageToTeu(xs *redis.XMessage) (teu *iface.TaskExecUnit, err error) {
	teu = &iface.TaskExecUnit{}
	msgStr, err := getXMessageItem("Message", xs)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(msgStr), teu)
	return
}

func (rb *redisBroker) xAddTaskExecUnits(ctx context.Context, queue string, taskExecUnits []*iface.TaskExecUnit) error {
	for _, taskExecUnit := range taskExecUnits {
		_, err := rb.redisClient.XAdd(ctx, teuToXAddArgs(queue, taskExecUnit)).Result()
		if err != nil {
			return err
		}
	}
	return nil
}

func (rb *redisBroker) xReadGroupAutoCreate(ctx context.Context, queue string, consumer string, count int) ([]redis.XStream, error) {
	xStreams, err := rb.redisClient.XReadGroup(ctx, &redis.XReadGroupArgs{
		Group:    WorkersGroup,
		Consumer: consumer,
		Streams:  []string{queue, ">"},
		Count:    int64(count),
		Block:    WorkerConsumeBlock,
		NoAck:    true,
	}).Result()
	if err != nil && redis.ErrNil != err {
		if strings.HasPrefix(err.Error(), "NOGROUP") {
			if _, err := rb.redisClient.XGroupCreateMkStream(ctx, queue, WorkersGroup, "0").Result(); err != nil {
				return nil, err
			}
			xStreams, err = rb.redisClient.XReadGroup(ctx, &redis.XReadGroupArgs{
				Group:    WorkersGroup,
				Consumer: consumer,
				Streams:  []string{queue, ">"},
				Count:    int64(count),
				Block:    WorkerConsumeBlock,
			}).Result()
			if err != nil && redis.ErrNil != err {
				return nil, err
			}
		} else {
			// fmt.Println(err.Error())
			return nil, err
		}
	}
	return xStreams, nil
}

func (rb *redisBroker) xReadGroupTaskExecUnits(ctx context.Context, queue string, consumer string, count int) ([]*iface.TaskExecUnit, error) {
	xStreams, err := rb.xReadGroupAutoCreate(ctx, queue, consumer, count)
	if err != nil {
		return nil, err
	}
	for sidx := range xStreams {
		xStream := &xStreams[sidx]
		if xStream.Stream != queue {
			continue
		}
		ret := []*iface.TaskExecUnit{}
		for midx := range xStream.Messages {
			teu, err := xMessageToTeu(&xStream.Messages[midx])
			if err != nil {
				return nil, err
			}
			ret = append(ret, teu)
		}
		return ret, nil
	}
	return nil, nil
}

func (rb *redisBroker) autoCreateQueue(ctx context.Context, queue string) error {
	ret, err := rb.redisClient.Exists(ctx, queue).Result()
	if err != nil {
		return err
	}
	if ret == 0 {
		if _, err := rb.redisClient.XGroupCreateMkStream(ctx, queue, WorkersGroup, "0").Result(); err != nil {
			return err
		}
	}
	return nil
}

func (rb *redisBroker) getPendingTaskExecUnits(ctx context.Context, queue string) ([]*iface.TaskExecUnit, error) {
	if err := rb.autoCreateQueue(ctx, queue); err != nil {
		return nil, err
	}
	xinfoRet, err := rb.redisClient.Do(ctx, "XINFO", "GROUPS", queue).Result()
	if err != nil {
		return nil, err
	}
	lastId := ""
	switch xinfoRet := xinfoRet.(type) {
	case []interface{}:
		for i := range xinfoRet {
			switch xinfoRetItems := xinfoRet[i].(type) {
			case []interface{}:
				for j := range xinfoRetItems {
					if j%2 == 0 && len(xinfoRetItems) > j+1 && xinfoRetItems[j].(string) == "last-delivered-id" {
						lastId = xinfoRetItems[j+1].(string)
					}
				}
			default:
				return nil, fmt.Errorf("SliceCmd is expected")
			}
		}
	default:
		return nil, fmt.Errorf("SliceCmd is expected")
	}
	// fmt.Printf("lastId %s\n", lastId)
	if lastId == "" || lastId == "0-0" {
		lastId = "-"
		xRagneRet, err := rb.redisClient.XRange(ctx, queue, lastId, "+").Result()
		if err != nil {
			return nil, err
		}
		ret := []*iface.TaskExecUnit{}
		for idx := range xRagneRet {
			teu, err := xMessageToTeu(&xRagneRet[idx])
			if err != nil {
				return nil, err
			}
			ret = append(ret, teu)
		}
		return ret, nil
	} else {
		xRagneRet, err := rb.redisClient.XRange(ctx, queue, lastId, "+").Result()
		if err != nil {
			return nil, err
		}
		if len(xRagneRet) <= 1 {
			return []*iface.TaskExecUnit{}, nil
		}
		ret := []*iface.TaskExecUnit{}
		for idx := range xRagneRet {
			if idx == 0 {
				continue
			}
			teu, err := xMessageToTeu(&xRagneRet[idx])
			if err != nil {
				return nil, err
			}
			ret = append(ret, teu)
		}
		return ret, nil
	}
}

func (rb *redisBroker) setNXWorkersCmd(ctx context.Context, taskBatchId string, workerIDs []string, cmd string) error {
	if len(workerIDs) == 0 {
		if err := rb.redisClient.SetNX(ctx, WorkerCmdQueuePrefix+"_"+taskBatchId, cmd, WorkerCmdTTL).Err(); err != nil {
			return err
		}
	} else {
		for _, workerID := range workerIDs {
			if err := rb.redisClient.SetNX(ctx, WorkerCmdQueuePrefix+"_"+taskBatchId+"_"+workerID, cmd, WorkerCmdTTL).Err(); err != nil {
				return err
			}
		}
	}
	return nil
}

func (rb *redisBroker) getWorkerCmd(ctx context.Context, taskBatchId string, workerID string) (string, error) {
	cmd, err := rb.redisClient.Get(ctx, WorkerCmdQueuePrefix+"_"+taskBatchId).Result()
	if err != nil && redis.ErrNil != err {
		return "", err
	}
	if cmd == "" {
		cmd, err = rb.redisClient.Get(ctx, WorkerCmdQueuePrefix+"_"+taskBatchId+"_"+workerID).Result()
		if err != nil && redis.ErrNil != err {
			return "", err
		}
	}
	return cmd, nil
}

func (rb *redisBroker) Publish(ctx context.Context, queue string, teus []*iface.TaskExecUnit) error {
	return rb.xAddTaskExecUnits(ctx, queue, teus)
}

func (rb *redisBroker) Consume(ctx context.Context, queue string, consumer string, count int) ([]*iface.TaskExecUnit, error) {
	return rb.xReadGroupTaskExecUnits(ctx, queue, consumer, count)
}

func (rb *redisBroker) Pendings(ctx context.Context, queue string) ([]*iface.TaskExecUnit, error) {
	return rb.getPendingTaskExecUnits(ctx, queue)
}

func (rb *redisBroker) PurgePendings(ctx context.Context, queue string) error {
	return rb.redisClient.Del(ctx, queue).Err()
}

func (rb *redisBroker) PublishCmd(ctx context.Context, taskBatchId string, workerIDs []string, cmd string) error {
	return rb.setNXWorkersCmd(ctx, taskBatchId, workerIDs, cmd)
}

func (rb *redisBroker) ConsumeCmd(ctx context.Context, taskBatchId string, workerID string) (string, error) {
	return rb.getWorkerCmd(ctx, taskBatchId, workerID)
}

func NewRedisBroker(c redis.Client) iface.Broker {
	return &redisBroker{
		redisClient: c,
	}
}
