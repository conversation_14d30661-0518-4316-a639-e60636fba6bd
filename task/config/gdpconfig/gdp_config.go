/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: C<PERSON>yi01 (<EMAIL>)
 * Date: 2022/04/19
 * File: task.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

package gdpconfig

import (
	"context"
	"os"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/conf"

	"icode.baidu.com/baidu/scs/x1-base/task/iface"
)

type config struct {
	EngineConfig     *engineConfig
	ScheduleConfig   *scheduleConfig
	WorkerConfig     *workerConfig
	RetryConfig      *retryConfig
	HTTPServerConfig *httpServerConfig
}

func (c *config) HTTPServerRunMode() string {
	return c.HTTPServerConfig.HTTPServerRunMode
}

func (c *config) HTTPServerPort() int {
	return c.HTTPServerConfig.HTTPServerPort
}

type engineConfig struct {
	EngineRunInterval            int
	EngineRunningTimeout         int
	EngineLockTTL                int
	EngineID                     string
	FailoverMaxPrerunWaitingTime int
	FailoverPauseOtherTasks      bool
}

type scheduleConfig struct {
	ScheduleRunInterval    int
	ScheduleRunningTimeout int
	ScheduleLockTTL        int
	ScheduleID             string
}

type retryConfig struct {
	Level1RetryCount    int
	Level1RetryInterval int
	Level2RetryCount    int
	Level2RetryInterval int
	Level3RetryCount    int
	Level3RetryInterval int
	ManualRetryCount    int
}

type workerConfig struct {
	WorkerMaxReportErrorTime  int
	WorkerHighPriorityCount   int
	WorkerMediumPriorityCount int
	WorkerLowPriorityCount    int
	WorkerRunInterval         int
}

type httpServerConfig struct {
	HTTPServerRunMode string
	HTTPServerPort    int
}

func (c *config) EngineRunInterval() time.Duration {
	return time.Duration(c.EngineConfig.EngineRunInterval) * time.Millisecond
}

func (c *config) EngineRunningTimeout() time.Duration {
	return time.Duration(c.EngineConfig.EngineRunningTimeout) * time.Millisecond
}

func (c *config) EngineLockTTL() time.Duration {
	return time.Duration(c.EngineConfig.EngineLockTTL) * time.Millisecond
}

func (c *config) EngineID() string {
	return c.EngineConfig.EngineID
}

func (c *config) ScheduleRunInterval() time.Duration {
	return time.Duration(c.ScheduleConfig.ScheduleRunInterval) * time.Millisecond
}

func (c *config) ScheduleRunningTimeout() time.Duration {
	return time.Duration(c.ScheduleConfig.ScheduleRunningTimeout) * time.Millisecond
}

func (c *config) ScheduleLockTTL() time.Duration {
	return time.Duration(c.ScheduleConfig.ScheduleLockTTL) * time.Millisecond
}

func (c *config) ScheduleID() string {
	return c.ScheduleConfig.ScheduleID
}

func (c *config) WorkerMaxReportErrorTime() time.Duration {
	return time.Duration(c.WorkerConfig.WorkerMaxReportErrorTime) * time.Millisecond
}

func (c *config) WorkerRunInterval() time.Duration {
	return time.Duration(c.WorkerConfig.WorkerRunInterval) * time.Millisecond
}

func (c *config) WorkerHighPriorityCount() int {
	return c.WorkerConfig.WorkerHighPriorityCount
}

func (c *config) WorkerMediumPriorityCount() int {
	return c.WorkerConfig.WorkerMediumPriorityCount
}

func (c *config) WorkerLowPriorityCount() int {
	return c.WorkerConfig.WorkerLowPriorityCount
}

func (c *config) Level1RetryCount() int {
	return c.RetryConfig.Level1RetryCount
}

func (c *config) Level1RetryInterval() time.Duration {
	return time.Duration(c.RetryConfig.Level1RetryInterval) * time.Millisecond
}

func (c *config) Level2RetryCount() int {
	return c.RetryConfig.Level2RetryCount
}

func (c *config) Level2RetryInterval() time.Duration {
	return time.Duration(c.RetryConfig.Level2RetryInterval) * time.Millisecond
}

func (c *config) Level3RetryCount() int {
	return c.RetryConfig.Level3RetryCount
}

func (c *config) Level3RetryInterval() time.Duration {
	return time.Duration(c.RetryConfig.Level3RetryInterval) * time.Millisecond
}

func (c *config) ManualRetryCount() int {
	return c.RetryConfig.ManualRetryCount
}

func (c *config) FailoverMaxPrerunWaitingTime() time.Duration {
	return time.Duration(c.EngineConfig.FailoverMaxPrerunWaitingTime) * time.Second
}

func (c *config) FailoverPauseOtherTasks() bool {
	return c.EngineConfig.FailoverPauseOtherTasks
}

func LoadConf(ctx context.Context) (iface.Config, error) {
	c := &config{}
	if err := conf.Parse("task.toml", c); err != nil {
		return nil, err
	}
	if c.EngineConfig.EngineID == "" {
		hostname, err := os.Hostname()
		if err != nil {
			return nil, err
		}
		c.EngineConfig.EngineID = hostname + "_engine_" + strconv.FormatInt(time.Now().UnixNano(), 10)
	}
	if c.ScheduleConfig.ScheduleID == "" {
		hostname, err := os.Hostname()
		if err != nil {
			return nil, err
		}
		c.ScheduleConfig.ScheduleID = hostname + "_schedule_" + strconv.FormatInt(time.Now().UnixNano(), 10)
	}
	return c, nil
}
