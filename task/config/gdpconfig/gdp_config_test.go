package gdpconfig

import (
	"testing"
	"time"
)

func Test_config_FailoverMaxPrerunWaitingTime(t *testing.T) {
	c := &config{
		EngineConfig: &engineConfig{
			EngineRunInterval:            0,
			EngineRunningTimeout:         0,
			EngineLockTTL:                0,
			EngineID:                     "",
			FailoverMaxPrerunWaitingTime: 10,
			FailoverPauseOtherTasks:      true,
		},
	}
	if c.FailoverMaxPrerunWaitingTime() != 10*time.Second {
		t.Errorf("FailoverMaxPrerunWaitingTime() = %v, want %v", c.FailoverMaxPrerunWaitingTime(), 10*time.Second)
	}
	if c.FailoverPauseOtherTasks() != true {
		t.<PERSON><PERSON><PERSON>("FailoverPauseOtherTasks() = %v, want %v", c.FailoverPauseOtherTasks(), true)
	}
}
