package ginserver

import (
	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/scs/x1-base/task/httpserver/ifaces"
	"icode.baidu.com/baidu/scs/x1-base/task/httpserver/services"
	"icode.baidu.com/baidu/scs/x1-base/task/resource"
)

func Server() *gin.Engine {
	r := gin.Default()
	gin.SetMode(resource.Config.HTTPServerRunMode())

	r.Any("/workflow", func(c *gin.Context) {
		var reqeust ifaces.WorkflowRequest
		if err := c.Bind<PERSON>(&reqeust); err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
		resp := ifaces.WorkflowResponse{
			WorkFlows: services.GetWorkflows(reqeust.WorkFlowNames),
		}
		c.<PERSON>(200, resp)
	})
	return r
}
