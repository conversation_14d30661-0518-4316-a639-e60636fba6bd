package ginserver

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/scs/x1-base/task/httpserver/ifaces"
	"icode.baidu.com/baidu/scs/x1-base/task/resource"
)

type testConfig struct {
}

func (t testConfig) EngineRunInterval() time.Duration {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) EngineRunningTimeout() time.Duration {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) EngineLockTTL() time.Duration {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) EngineID() string {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) ScheduleRunInterval() time.Duration {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) ScheduleRunningTimeout() time.Duration {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) ScheduleLockTTL() time.Duration {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) ScheduleID() string {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) WorkerMaxReportErrorTime() time.Duration {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) WorkerRunInterval() time.Duration {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) WorkerHighPriorityCount() int {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) WorkerMediumPriorityCount() int {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) WorkerLowPriorityCount() int {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) Level1RetryCount() int {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) Level1RetryInterval() time.Duration {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) Level2RetryCount() int {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) Level2RetryInterval() time.Duration {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) Level3RetryCount() int {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) Level3RetryInterval() time.Duration {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) ManualRetryCount() int {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) HTTPServerRunMode() string {
	return "debug"
}

func (t testConfig) HTTPServerPort() int {
	return 13345
}

func (t testConfig) FailoverMaxPrerunWaitingTime() time.Duration {
	// TODO implement me
	panic("implement me")
}

func (t testConfig) FailoverPauseOtherTasks() bool {
	// TODO implement me
	panic("implement me")
}

func InitForTest() {
	resource.Config = &testConfig{}
}

func TestRouter_WorkflowEndpoint_Success(t *testing.T) {
	InitForTest()
	router := Server()

	requestBody, _ := json.Marshal(ifaces.WorkflowRequest{
		WorkFlowNames: []string{"test1", "test2"},
	})
	req, _ := http.NewRequest("POST", "/workflow", bytes.NewBuffer(requestBody))
	resp := httptest.NewRecorder()

	router.ServeHTTP(resp, req)

	assert.Equal(t, http.StatusOK, resp.Code)
}

func TestRouter_WorkflowEndpoint_BadRequest(t *testing.T) {
	InitForTest()
	router := Server()

	req, _ := http.NewRequest("POST", "/workflow", bytes.NewBuffer([]byte("invalid json")))
	resp := httptest.NewRecorder()

	router.ServeHTTP(resp, req)

	assert.Equal(t, http.StatusBadRequest, resp.Code)
}
