package services

import (
	"testing"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
)

func TestGetWorkflows(t *testing.T) {
	// Mock the workflow.GetWorkFlows function
	workflowMap := map[string][]*workflow.Step{
		"workflow1": {
			{
				Name:     "step1",
				Workflow: "workflow1",
				Option: &workflow.StepOption{
					StepTimeout:        10 * time.Second,
					MaxReentry:         3,
					MaxReentryNextStep: "step2",
				},
				SuccessNextStep: "step3",
				ErrorNextStep:   "step4",
			},
		},
		"workflow2": {
			{
				Name:     "step5",
				Workflow: "workflow2",
				Option: &workflow.StepOption{
					StepTimeout: 20 * time.Second,
				},
				SuccessNextStep: "step6",
				ErrorNextStep:   "step7",
			},
		},
	}
	workflow.SetWorkFlows(workflowMap)

	// Test case 1: no filter
	wfs := GetWorkflows(nil)
	if len(wfs) != 2 {
		t.<PERSON><PERSON><PERSON>("Expected 2 workflows, got %d", len(wfs))
	}

	// Test case 2: filter workflow1
	wfs = GetWorkflows([]string{"workflow1"})
	if len(wfs) != 1 {
		t.Errorf("Expected 1 workflow after filtering, got %d", len(wfs))
	}
	if wfs[0].Name != "workflow1" {
		t.Errorf("Expected workflow1 after filtering, got %s", wfs[0].Name)
	}
	if len(wfs[0].Steps) != 1 {
		t.Errorf("Expected 1 step for workflow1 after filtering, got %d", len(wfs[0].Steps))
	}
	// Add more assertions as needed
}
