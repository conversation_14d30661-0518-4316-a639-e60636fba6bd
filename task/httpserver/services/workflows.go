package services

import (
	"fmt"

	"icode.baidu.com/baidu/scs/x1-base/task/httpserver/ifaces"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
)

func GetWorkflows(wf []string) []*ifaces.WorkFlow {
	workflowMap := workflow.GetWorkFlows()
	var ret []*ifaces.WorkFlow
	for name, steps := range workflowMap {
		if len(wf) > 0 && !inArray(name, wf) {
			continue
		}
		var newSteps []*ifaces.Step
		for _, step := range steps {
			newSteps = append(newSteps, &ifaces.Step{
				Name:     step.Name,
				Workflow: step.Workflow,
				Timeout:  step.Option.StepTimeout,
				Edges: []*ifaces.Edge{
					{
						NextStep: step.SuccessNextStep,
						Mark:     "success",
					},
					{
						NextStep: step.ErrorNextStep,
						Mark:     "error",
					},
				},
			})
			if step.Option.MaxReentry > 0 {
				newSteps[len(newSteps)-1].Edges = append(newSteps[len(newSteps)-1].Edges, &ifaces.Edge{
					NextStep: step.Option.MaxReentryNextStep,
					Mark:     fmt.Sprintf("after retry %d times", step.Option.MaxReentry),
				})
			}
		}
		ret = append(ret, &ifaces.WorkFlow{
			Name:  name,
			Steps: newSteps,
		})
	}
	return ret
}

func inArray(s string, ss []string) bool {
	for _, v := range ss {
		if v == s {
			return true
		}
	}
	return false
}
