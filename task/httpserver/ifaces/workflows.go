package ifaces

import "time"

type WorkflowRequest struct {
	WorkFlowNames []string `json:"workflows"`
}

type WorkflowResponse struct {
	WorkFlows []*WorkFlow `json:"workflows"`
}

type WorkFlow struct {
	Name  string  `json:"name"`
	Steps []*Step `json:"steps"`
}

type Step struct {
	Name     string        `json:"name"`
	Workflow string        `json:"workflow"`
	Timeout  time.Duration `json:"timeout"`
	Edges    []*Edge       `json:"edges"`
}

type Edge struct {
	NextStep string `json:"next_step"`
	Mark     string `json:"mark"`
}
