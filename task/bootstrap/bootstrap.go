/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON>yi<PERSON> (<EMAIL>)
 * Date: 2022/04/19
 * File: task.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

package bootstrap

import (
	"context"
	"fmt"
	"io"
	"time"

	"gorm.io/gorm"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/gorm_adapter"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/mysql"
	"icode.baidu.com/baidu/gdp/redis"

	"icode.baidu.com/baidu/scs/x1-base/task/backend/redis_backend"
	"icode.baidu.com/baidu/scs/x1-base/task/broker/redis_broker"
	"icode.baidu.com/baidu/scs/x1-base/task/config/gdpconfig"
	"icode.baidu.com/baidu/scs/x1-base/task/engine"
	"icode.baidu.com/baidu/scs/x1-base/task/httpserver/ginserver"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/lock/redis_lock"
	"icode.baidu.com/baidu/scs/x1-base/task/model/gorm_model"
	"icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/task/schedule"
	"icode.baidu.com/baidu/scs/x1-base/task/worker"
)

type bootStrapOption struct {
	engineLogger logit.Logger
	taskOperator iface.TaskOperator
	broker       iface.Broker
	backend      iface.Backend
	lockOperator iface.LockOperator
	config       iface.Config
}

var (
	defaultRedis redis.Client
	defaultDB    *gorm.DB
)

var closeFns []func() error

func tryRegisterCloser(comp any) {
	if c, ok := comp.(io.Closer); ok {
		closeFns = append(closeFns, c.Close)
		return
	}
	if fn, ok := comp.(func() error); ok {
		closeFns = append(closeFns, fn)
	}
}

// BeforeShutdown 退出前执行，资源清理、日志落盘等
func beforeShutdown() {
	for _, fn := range closeFns {
		_ = fn()
	}
}

func WithEngineLogger(engineLogger logit.Logger) func(option *bootStrapOption) {
	return func(option *bootStrapOption) {
		option.engineLogger = engineLogger
	}
}

func WithTaskOperator(taskOperator iface.TaskOperator) func(option *bootStrapOption) {
	return func(option *bootStrapOption) {
		option.taskOperator = taskOperator
	}
}

func WithBroker(broker iface.Broker) func(option *bootStrapOption) {
	return func(option *bootStrapOption) {
		option.broker = broker
	}
}

func WithBackend(backend iface.Backend) func(option *bootStrapOption) {
	return func(option *bootStrapOption) {
		option.backend = backend
	}
}

func WithLockOperator(lockOperator iface.LockOperator) func(option *bootStrapOption) {
	return func(option *bootStrapOption) {
		option.lockOperator = lockOperator
	}
}

func WithConfig(config iface.Config) func(option *bootStrapOption) {
	return func(option *bootStrapOption) {
		option.config = config
	}
}

func getRedisClient(ctx context.Context) (redis.Client, error) {
	if defaultRedis != nil {
		return defaultRedis, nil
	}
	opts := []redis.ClientOption{
		// redis.OptHooker(redis.NewMetricsHook(nil)),
		// redis.OptHooker(redis.NewLogHook()),
	}
	return redis.NewClient("broker-redis", opts...)
}

func getDbClient(ctx context.Context) (*gorm.DB, error) {
	if defaultDB != nil {
		return defaultDB, nil
	}
	opts := []mysql.ClientOption{
		// mysql.OptObserver(mysql.NewMetricsObserverFunc(nil)),
	}
	client, err := mysql.NewClient("task-db", opts...)
	// 理论上，只有配置错误，才有可能返回err
	// 如配置不存在，配置内容错误
	if err != nil {
		return nil, err
	}
	gormOpts := []gorm_adapter.Option{}
	return gorm_adapter.NewGorm(client, gormOpts...)
}

func MustInit(ctx context.Context, opts ...func(option *bootStrapOption)) error {
	option := &bootStrapOption{}
	for _, opt := range opts {
		opt(option)
	}
	if option.engineLogger == nil {
		engineLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("logit/engine.toml"))
		if err != nil {
			return err
		}
		tryRegisterCloser(engineLogger)
		option.engineLogger = engineLogger
	}
	resource.EngineLogger = option.engineLogger

	if option.taskOperator == nil {
		db, err := getDbClient(ctx)
		if err != nil {
			return err
		}
		option.taskOperator = gorm_model.NewGormTaskOperator(db)
	}
	resource.TaskOperator = option.taskOperator

	if option.broker == nil {
		r, err := getRedisClient(ctx)
		if err != nil {
			return err
		}
		option.broker = redis_broker.NewRedisBroker(r)
	}
	resource.Broker = option.broker

	if option.backend == nil {
		r, err := getRedisClient(ctx)
		if err != nil {
			return err
		}
		option.backend = redis_backend.NewRedisBackend(r)
	}
	resource.Backend = option.backend

	if option.lockOperator == nil {
		r, err := getRedisClient(ctx)
		if err != nil {
			return err
		}
		option.lockOperator = redis_lock.NewRedisLock(r)
	}
	resource.LockOperator = option.lockOperator

	if option.config == nil {
		c, err := gdpconfig.LoadConf(ctx)
		if err != nil {
			return err
		}
		option.config = c
	}
	resource.Config = option.config
	return nil
}

func Start(ctx context.Context) error {
	defer beforeShutdown()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	var g gtask.Group
	ctx = g.WithContext(ctx)
	g.Go(func() error {
		return engine.StartEngine(ctx)
	})

	g.Go(func() error {
		return schedule.StartSchedule(ctx)
	})

	g.Go(func() error {
		return worker.StartWorkerPool(ctx)
	})

	g.Go(func() error {
		// 启动http server 返回workflow信息
		// 如果启动失败, 5秒后重试, 不影响其他服务
		for {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
				// do something
			}
			if err := ginserver.Server().Run(fmt.Sprintf(":%d", resource.Config.HTTPServerPort())); err != nil {
				resource.EngineLogger.Warning(ctx, "http server run error", logit.Error("error", err))
			}
			time.Sleep(5 * time.Second)
		}
	})

	_, err := g.Wait()
	return err
}
