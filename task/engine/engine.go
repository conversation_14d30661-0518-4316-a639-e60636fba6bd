/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON>yi<PERSON> (<EMAIL>)
 * Date: 2022/04/19
 * File: task.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

package engine

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const (
	engineLockId = "x1_task_engine_lock_d9bdc4d4"
)

const (
	beforePreRunHook         = "before_prerun"
	afterPreRunHook          = "after_prerun"
	beforeRunningHook        = "before_running"
	afterRunningHook         = "after_running"
	afterSuccessCompleteHook = "after_success"
	afterErrorComplete       = "after_error"
)

type EngineHook struct {
	BeforePreRun         func(ctx context.Context, task *iface.Task, sc *workflow.StepContext) error
	AfterPreRun          func(ctx context.Context, task *iface.Task, sc *workflow.StepContext) error
	BeforeRunning        func(ctx context.Context, task *iface.Task, sc *workflow.StepContext) error
	AfterRunning         func(ctx context.Context, task *iface.Task, sc *workflow.StepContext) error
	AfterSuccessComplete func(ctx context.Context, task *iface.Task, sc *workflow.StepContext) error
	AfterErrorComplete   func(ctx context.Context, task *iface.Task, sc *workflow.StepContext) error
}

var (
	hooks []EngineHook
)

func executeHook(ctx context.Context, task *iface.Task, sc *workflow.StepContext, hookType string) error {
	for _, hook := range hooks {
		hookFunc := func(ctx context.Context, task *iface.Task, sc *workflow.StepContext) error {
			return nil
		}
		switch hookType {
		case beforePreRunHook:
			if hook.BeforePreRun != nil {
				hookFunc = hook.BeforePreRun
			}
		case afterPreRunHook:
			if hook.AfterPreRun != nil {
				hookFunc = hook.AfterPreRun
			}
		case beforeRunningHook:
			if hook.BeforeRunning != nil {
				hookFunc = hook.BeforeRunning
			}
		case afterRunningHook:
			if hook.AfterRunning != nil {
				hookFunc = hook.AfterRunning
			}
		case afterSuccessCompleteHook:
			if hook.AfterSuccessComplete != nil {
				hookFunc = hook.AfterSuccessComplete
			}
		case afterErrorComplete:
			if hook.AfterErrorComplete != nil {
				hookFunc = hook.AfterErrorComplete
			}
		}
		if err := hookFunc(ctx, task, sc); err != nil {
			return err
		}
	}
	return nil
}

func executePrerun(ctx context.Context, task *iface.Task, totalTasks []*iface.Task, sc *workflow.StepContext) error {
	if err := executeHook(ctx, task, sc, beforePreRunHook); err != nil {
		return err
	}

	idChunks, err := sc.Step.Option.StepSplitHandler(ctx, task)
	if err != nil {
		return err
	}
	if task.Step == "" {
		task.Step = sc.ToString()
	}
	needRun, err := needRunByFailoverCheck(ctx, task, totalTasks)
	if !needRun {
		return err
	}
	if task.Step != "" && strings.Contains(task.Step, "__") {
		retry, _ := strconv.Atoi(strings.Split(task.Step, "__")[1])
		if retry > resource.Config.Level3RetryCount() && time.Since(task.StepStartAt) < resource.Config.Level3RetryInterval() {
			resource.EngineLogger.Trace(ctx, fmt.Sprintf("task reach level3 retry limit, will retry at %s",
				task.StepStartAt.Add(resource.Config.Level3RetryInterval()).Format("2006-01-02 15:04:05")))
			return nil
		}
		if retry > resource.Config.Level2RetryCount() && time.Since(task.StepStartAt) < resource.Config.Level2RetryInterval() {
			resource.EngineLogger.Trace(ctx, fmt.Sprintf("task reach level2 retry limit, will retry at %s",
				task.StepStartAt.Add(resource.Config.Level2RetryInterval()).Format("2006-01-02 15:04:05")))
			return nil
		}
		if retry > resource.Config.Level1RetryCount() && time.Since(task.StepStartAt) < resource.Config.Level1RetryInterval() {
			resource.EngineLogger.Trace(ctx, fmt.Sprintf("task reach level1 retry limit, will retry at %s",
				task.StepStartAt.Add(resource.Config.Level1RetryInterval()).Format("2006-01-02 15:04:05")))
			return nil
		}
		if retry > resource.Config.ManualRetryCount() {
			task.Status = iface.TaskStatusManual
			return resource.TaskOperator.UpdateTask(ctx, task)
		}
	}
	task.StepStartAt = time.Now()
	task.StepDeadline = time.Now().Add(sc.Step.Option.StepTimeout)
	task.LastTaskBatchID = task.TaskBatchID
	task.TaskBatchID = uuid.NewString() + "|" + task.Step

	teus := []*iface.TaskExecUnit{}
	for _, entityId := range idChunks {
		teus = append(teus, &iface.TaskExecUnit{
			TaskExecUnitID: uuid.NewString(),
			TaskBatchID:    task.TaskBatchID,
			TaskID:         task.TaskID,
			Processor:      sc.Step.Name + "__" + task.WorkFlow,
			Entity:         entityId,
			SendAt:         task.StepStartAt,
			ExpireAt:       task.StepDeadline,
			Parameters:     task.Parameters,
			PTaskID:        task.PTaskID,
		})
	}
	if len(idChunks) == 0 {
		resource.EngineLogger.Notice(ctx, "no sub tasks, create fake sub task")
		ter := &iface.TaskExecResult{
			TaskExecUnitID: uuid.NewString(),
			TaskID:         task.TaskID,
			TaskBatchID:    task.TaskBatchID,
			Entity:         task.Entity,
			StartAt:        time.Now(),
			Status:         iface.TaskExecResultStatusSuccess,
			LastUpdate:     time.Now(),
		}
		if err := resource.Backend.Report(ctx, ter); err != nil {
			resource.EngineLogger.Error(ctx, "report task exec result failed", logit.Error("error", err))
		}
		task.StepTEUCount = 1
	} else {
		if err := resource.Broker.Publish(ctx, iface.GetQueue(task.Priority), teus); err != nil {
			return err
		}
		task.StepTEUCount = len(teus)
	}
	task.Status = iface.TaskStatusRunning

	if err := executeHook(ctx, task, sc, afterPreRunHook); err != nil {
		return err
	}
	return resource.TaskOperator.UpdateTask(ctx, task)
}

func needRunByFailoverCheck(ctx context.Context, task *iface.Task, totalTasks []*iface.Task) (bool, error) {
	var highPriorityTasks []*iface.Task
	var runningNormalTasks []*iface.Task
	for _, tt := range totalTasks {
		if tt.Entity != task.Entity {
			continue
		}
		if isHighPriority(tt) {
			highPriorityTasks = append(highPriorityTasks, tt)
		}
		if !isHighPriority(tt) && isNormalTask(tt) && tt.Status == iface.TaskStatusRunning {
			runningNormalTasks = append(runningNormalTasks, tt)
		}
	}
	// 当前任务不是高优先，存在正在运行的高优先级任务, 不会将当前任务状态改为running
	// 如果开启pause配置, 如果出现冲突, 会将冲突的任务状态改为manual
	if !isHighPriority(task) && len(highPriorityTasks) > 0 {
		resource.EngineLogger.Warning(ctx, "[HIGH PRIORITY TASK CONFLICT] wait for high priority tasks complete")
		if resource.Config.FailoverPauseOtherTasks() && isNormalTask(task) {
			task.Status = iface.TaskStatusManual
			task.ErrMsg = fmt.Sprintf("task conflict with high priority tasks %s", base_utils.Format(highPriorityTasks))
			if err := resource.TaskOperator.UpdateTask(ctx, task); err != nil {
				resource.EngineLogger.Warning(ctx, "[HIGH PRIORITY TASK CONFLICT] pause task failed",
					logit.Error("error", err), logit.String("task_id", task.TaskID))
				return false, err
			}
			resource.EngineLogger.Warning(ctx, "[HIGH PRIORITY TASK CONFLICT] pause task success", logit.String("task_id", task.TaskID))
		}
		return false, nil
	}
	if isHighPriority(task) && len(runningNormalTasks) > 0 {
		resource.EngineLogger.Warning(ctx, "[HIGH PRIORITY TASK CONFLICT] failover task will wait for other task current step complete")
		for _, tt := range runningNormalTasks {
			runningDuration := time.Since(tt.StepStartAt)
			if runningDuration > resource.Config.FailoverMaxPrerunWaitingTime() {
				resource.EngineLogger.Warning(ctx, "[HIGH PRIORITY TASK CONFLICT] failover task waiting for other task current step complete timeout",
					logit.String("running_task_id", tt.TaskID), logit.String("running_task_step", tt.Step),
					logit.String("running_duration", runningDuration.String()),
					logit.String("max_waiting_time", resource.Config.FailoverMaxPrerunWaitingTime().String()))
				sc, err := workflow.NewStepContextFromString(tt.Step, tt.WorkFlow)
				if err != nil {
					resource.EngineLogger.Warning(ctx, "[HIGH PRIORITY TASK CONFLICT] invalid task step", logit.Error("error", err),
						logit.String("task_id", tt.TaskID))
				} else if sc.Step.Option.Cancellable {
					if err := resource.Broker.PublishCmd(ctx, tt.TaskBatchID, nil, iface.CmdCancel); err != nil {
						resource.EngineLogger.Warning(ctx, "[HIGH PRIORITY TASK CONFLICT] send cancel task step command failed",
							logit.Error("error", err),
							logit.String("task_id", tt.TaskID))
					}
					resource.EngineLogger.Warning(ctx, "[HIGH PRIORITY TASK CONFLICT] send cancel task step command success",
						logit.String("task_id", tt.TaskID))
				}
			}
		}
		return false, nil
	}
	return true, nil
}

func isHighPriority(task *iface.Task) bool {
	return task.Priority == iface.TaskPriorityHigh
}

func isNormalTask(task *iface.Task) bool {
	return strings.HasPrefix(task.Mutex, "n_")
}

func executeRunning(ctx context.Context, task *iface.Task, sc *workflow.StepContext) error {
	if err := executeHook(ctx, task, sc, beforeRunningHook); err != nil {
		return err
	}

	ters, err := resource.Backend.GetReports(ctx, task.TaskBatchID)
	if err != nil {
		return err
	}
	nextSc, err := sc.Step.Option.NextStepHandler(ctx, task, ters)
	if err != nil {
		return err
	}

	switch nextSc.Step.Name {
	case workflow.FinalStepError:
		task.Status = iface.TaskStatusError
		task.CompletedAt = time.Now()
		if task.ErrorStep == "" {
			task.ErrorStep = sc.ToString()
			task.ErrMsg = base_utils.Format(ters)
		}
		if err := executeHook(ctx, task, sc, afterSuccessCompleteHook); err != nil {
			return err
		}
	case workflow.FinalStepSuccess:
		task.Status = iface.TaskStatusSuccess
		task.CompletedAt = time.Now()
		if err := executeHook(ctx, task, sc, afterErrorComplete); err != nil {
			return err
		}
	default:
		if !nextSc.IsPromoted {
			return nil
		}
		if nextSc.NeedManual {
			task.Status = iface.TaskStatusManual
		} else {
			task.Status = iface.TaskStatusPreRun
			task.Step = nextSc.ToString()
		}
		// 记录一下进入prerun时间
		task.StepStartAt = time.Now()
		if nextSc.IsErrorPromoted {
			task.ErrorStep = sc.ToString()
			task.ErrMsg = base_utils.Format(ters)
		}
	}

	if err := executeHook(ctx, task, sc, afterRunningHook); err != nil {
		return err
	}
	return resource.TaskOperator.UpdateTask(ctx, task)
}

func executeOne(ctx context.Context, task *iface.Task, totalTasks []*iface.Task) error {
	sc, err := workflow.NewStepContextFromString(task.Step, task.WorkFlow)
	if err != nil {
		resource.EngineLogger.Warning(ctx, "invalid task step", logit.Error("error", err))
		return err
	}
	switch task.Status {
	case iface.TaskStatusPreRun:
		if err := executePrerun(ctx, task, totalTasks, sc); err != nil {
			resource.EngineLogger.Warning(ctx, "publish task to worker failed", logit.Error("error", err))
			return err
		}
		resource.EngineLogger.Notice(ctx, "publish task to worker success",
			logit.String("task_batch_id", task.TaskBatchID),
			logit.String("status change", fmt.Sprintf("status %s -> %s", iface.TaskStatusPreRun, task.Status)))
	case iface.TaskStatusRunning:
		oldStep := task.Step
		oldStatus := task.Status
		if err := executeRunning(ctx, task, sc); err != nil {
			resource.EngineLogger.Warning(ctx, "check task result failed", logit.Error("error", err))
			return err
		}
		resource.EngineLogger.Notice(ctx, "check task result success",
			logit.String("task_batch_id", task.TaskBatchID),
			logit.String("status change", fmt.Sprintf("status %s -> %s", oldStatus, task.Status)),
			logit.String("step change", fmt.Sprintf("step %s -> %s", oldStep, task.Step)))
	default:
		resource.EngineLogger.Warning(ctx, fmt.Sprintf("unexpected task status %s", task.Status))
		return fmt.Errorf("unexpected task status %s", task.Status)
	}
	return nil
}

func parallelExecuteAll(ctx context.Context) error {
	unlock, err := resource.LockOperator.Lock(ctx, engineLockId, resource.Config.EngineLockTTL())
	if err != nil {
		resource.EngineLogger.Notice(ctx, "lock for engine failed", logit.String("lock_id", engineLockId))
		return err
	}
	defer unlock()

	tasks, err := resource.TaskOperator.RetrieveTasks(ctx, "status IN ?", []string{iface.TaskStatusPreRun, iface.TaskStatusRunning})
	if err != nil {
		resource.EngineLogger.Warning(ctx, "get running tasks failed failed", logit.Error("error", err))
	}
	resource.EngineLogger.Notice(ctx, fmt.Sprintf("get %d prerun or running tasks", len(tasks)))

	g := gtask.Group{}
	for _, task := range tasks {
		task := task
		g.Go(func() error {
			sctx := logit.ForkContext(ctx)
			logit.AddAllLevel(sctx, logit.String("task_id", task.TaskID))
			logit.AddAllLevel(sctx, logit.String("entity", task.Entity))
			logit.AddAllLevel(sctx, logit.String("workflow", task.WorkFlow))
			if err := gtask.NoPanic(func() error {
				return executeOne(sctx, task, tasks)
			}); err != nil {
				resource.EngineLogger.Warning(sctx, "exeute task failed", logit.Error("error", err))
			}
			return nil
		})
	}
	g.Wait()
	return nil
}

func AddEngineHook(hook EngineHook) {
	hooks = append(hooks, hook)
}

func StartEngine(ctx context.Context) error {
	ctx = logit.ForkContext(ctx)
	logit.AddAllLevel(ctx, logit.String("engine_id", resource.Config.EngineID()))
	resource.EngineLogger.Notice(ctx, "engine start to process...")
	engineTimer := time.NewTimer(resource.Config.EngineRunInterval())
	for {
		engineTimer.Reset(resource.Config.EngineRunInterval())
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-engineTimer.C:
			{
				execOnce(ctx)
			}
		}
	}
}

func execOnce(ctx context.Context) {
	ctx, cancel := context.WithTimeout(ctx, resource.Config.EngineRunningTimeout())
	defer cancel()
	_ = parallelExecuteAll(ctx)
}
