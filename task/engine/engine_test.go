package engine

import (
	"context"
	"errors"
	"strings"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

type MockLockOperator struct {
}

func (m *MockLockOperator) Lock(ctx context.Context, id string, ttl time.Duration) (unlock func(), err error) {
	return func() {}, nil
}

type MockTaskOperator struct {
}

func (m MockTaskOperator) CreateSubTasks(ctx context.Context, pTaskID string, tasks []*iface.CreateTaskParams) error {
	// TODO implement me
	panic("implement me")
}

func (m MockTaskOperator) CheckSubTasksStatus(ctx context.Context, pTaskID string) (string, error) {
	// TODO implement me
	panic("implement me")
}

func (m MockTaskOperator) GetTaskDetail(ctx context.Context, TaskID string) (*iface.Task, error) {
	// TODO implement me
	panic("implement me")
}

func (m MockTaskOperator) CreateTask(ctx context.Context, task *iface.Task) error {
	// TODO implement me
	panic("implement me")
}

func (m MockTaskOperator) CreateTasks(ctx context.Context, tasks []*iface.Task) error {
	// TODO implement me
	panic("implement me")
}

func (m MockTaskOperator) UpdateTask(ctx context.Context, task *iface.Task) error {
	if strings.Contains(task.TaskID, "update_task_error") {
		return errors.New("error")
	}
	return nil
}

func (m MockTaskOperator) UpdateTasks(ctx context.Context, tasks []*iface.Task) error {
	// TODO implement me
	panic("implement me")
}

func (m MockTaskOperator) RetrieveTasks(ctx context.Context, condFmt string, vals ...any) ([]*iface.Task, error) {
	// TODO implement me
	return []*iface.Task{}, nil
}

func (m MockTaskOperator) UpdateTaskToManual(ctx context.Context, taskID string, errStep string, errMsg string) error {
	// TODO implement me
	panic("implement me")
}

func (m MockTaskOperator) CreateRecords(ctx context.Context, records []*iface.ProcessRecord) error {
	// TODO implement me
	panic("implement me")
}

func (m MockTaskOperator) RetrieveRecords(ctx context.Context, condFmt string, vals ...any) ([]*iface.ProcessRecord, error) {
	// TODO implement me
	panic("implement me")
}

type MockConfig struct {
}

func (m MockConfig) FailoverPauseOtherTasks() bool {
	return true
}

func (m MockConfig) FailoverMaxPrerunWaitingTime() time.Duration {
	return 120 * time.Second
}

func (m MockConfig) HTTPServerRunMode() string {
	// TODO implement me
	panic("implement me")
}

func (m MockConfig) HTTPServerPort() int {
	// TODO implement me
	panic("implement me")
}

func (m MockConfig) HTTPServerReadTimeout() time.Duration {
	// TODO implement me
	panic("implement me")
}

func (m MockConfig) HTTPServerWriteTimeout() time.Duration {
	// TODO implement me
	panic("implement me")
}

func (m MockConfig) EngineRunInterval() time.Duration {
	return 300 * time.Millisecond
}

func (m MockConfig) EngineRunningTimeout() time.Duration {
	return 3000 * time.Millisecond
}

func (m MockConfig) EngineLockTTL() time.Duration {
	return 3000 * time.Millisecond
}

func (m MockConfig) EngineID() string {
	return "abcd"
}

func (m MockConfig) ScheduleRunInterval() time.Duration {
	return 300 * time.Millisecond
}

func (m MockConfig) ScheduleRunningTimeout() time.Duration {
	return 3000 * time.Millisecond
}

func (m MockConfig) ScheduleLockTTL() time.Duration {
	return 3000 * time.Millisecond
}

func (m MockConfig) ScheduleID() string {
	return "abcd"
}

func (m MockConfig) WorkerMaxReportErrorTime() time.Duration {
	return 3000 * time.Millisecond
}

func (m MockConfig) WorkerRunInterval() time.Duration {
	return 3000 * time.Millisecond
}

func (m MockConfig) WorkerHighPriorityCount() int {
	return 10
}

func (m MockConfig) WorkerMediumPriorityCount() int {
	return 10
}

func (m MockConfig) WorkerLowPriorityCount() int {
	return 10
}

func (m MockConfig) Level1RetryCount() int {
	return 10
}

func (m MockConfig) Level1RetryInterval() time.Duration {
	return 10
}

func (m MockConfig) Level2RetryCount() int {
	return 10
}

func (m MockConfig) Level2RetryInterval() time.Duration {
	return 10
}

func (m MockConfig) Level3RetryCount() int {
	return 10
}

func (m MockConfig) Level3RetryInterval() time.Duration {
	return 10
}

func (m MockConfig) ManualRetryCount() int {
	return 10
}

type MockBroker struct {
	Cnt int
}

func (m MockBroker) Publish(ctx context.Context, queue string, teus []*iface.TaskExecUnit) error {
	// TODO implement me
	panic("implement me")
}

func (m MockBroker) Consume(ctx context.Context, queue string, consumer string, count int) ([]*iface.TaskExecUnit, error) {
	// TODO implement me
	panic("implement me")
}

func (m MockBroker) Pendings(ctx context.Context, queue string) ([]*iface.TaskExecUnit, error) {
	// TODO implement me
	panic("implement me")
}

func (m MockBroker) PurgePendings(ctx context.Context, queue string) error {
	// TODO implement me
	panic("implement me")
}

func (m MockBroker) PublishCmd(ctx context.Context, taskBatchId string, workerIDs []string, cmd string) error {
	if taskBatchId == "errr" {
		return errors.New("error")
	}
	return nil
}

func (m MockBroker) ConsumeCmd(ctx context.Context, taskBatchId string, workerID string) (string, error) {
	return "", nil
}

func TestStartEngine(t *testing.T) {
	unittest.UnitTestInit(2)
	resource.LockOperator = &MockLockOperator{}
	resource.TaskOperator = &MockTaskOperator{}
	resource.Config = &MockConfig{}
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	engineLogger, err := logit.NewLogger(ctx, logit.OptLogFileName("../log_ut/engine_ut.log"))
	resource.EngineLogger = engineLogger
	if err != nil {
		t.Errorf("NewLogger failed, err: %v", err)
	}
	defer cancel()
	if err := StartEngine(ctx); err == nil {
		t.Error("StartEngine should return error")
	}
}

func Test_needRun(t *testing.T) {
	// unittest.UnitTestInit(2)
	resource.LockOperator = &MockLockOperator{}
	resource.TaskOperator = &MockTaskOperator{}
	resource.Broker = &MockBroker{}
	resource.Config = &MockConfig{}
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	engineLogger, err := logit.NewLogger(ctx, logit.OptLogFileName("../log_ut/engine_ut.log"))
	if err != nil {
		t.Errorf("NewLogger failed, err: %v", err)
	}
	resource.EngineLogger = engineLogger
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            "failover_step",
		Workflow:        "xxx",
		StepProcess:     nil,
		SuccessNextStep: "sccc",
		ErrorNextStep:   "errr",
	}, workflow.WithCancellable(true))
	failoverTaskPrerun1 := &iface.Task{
		Step:        "failover_step__0",
		StepStartAt: time.Now(),
		TaskID:      "test-task-id-1",
		Status:      "prerun",
		WorkFlow:    "scs-standalone-failover",
		Priority:    iface.TaskPriorityHigh,
		Mutex:       "s_",
	}
	failoverTaskPrerun2 := &iface.Task{
		Step:        "failover_step__0",
		StepStartAt: time.Now(),
		TaskID:      "test-task-id-2",
		Status:      "prerun",
		WorkFlow:    "scs-standalone-failover",
		Priority:    iface.TaskPriorityHigh,
		Mutex:       "s_",
	}
	failoverTaskRunning1 := &iface.Task{
		Step:        "failover_step__0",
		StepStartAt: time.Now(),
		TaskID:      "test-task-id-3",
		Status:      "running",
		WorkFlow:    "scs-standalone-failover",
		Priority:    iface.TaskPriorityHigh,
		Mutex:       "s_",
	}
	failoverTaskRunning2 := &iface.Task{
		Step:        "failover_step__0",
		StepStartAt: time.Now(),
		TaskID:      "test-task-id-4",
		Status:      "running",
		WorkFlow:    "scs-standalone-failover",
		Priority:    iface.TaskPriorityHigh,
		Mutex:       "s_",
	}
	taskPrerun1 := &iface.Task{
		Step:        "failover_step__0",
		StepStartAt: time.Now(),
		TaskID:      "test-task-id-5",
		Status:      "prerun",
		WorkFlow:    "xxx",
		Mutex:       "n_xx",
	}
	taskPrerun2 := &iface.Task{
		Step:        "failover_step__0",
		StepStartAt: time.Now(),
		TaskID:      "test-task-id-6",
		Status:      "prerun",
		WorkFlow:    "xxx",
		Mutex:       "n_xx",
	}
	taskRunning1 := &iface.Task{
		Step:        "failover_step__0",
		StepStartAt: time.Now(),
		TaskID:      "test-task-id-7",
		Status:      "running",
		WorkFlow:    "xxx",
		Mutex:       "n_xx",
	}
	taskRunning2 := &iface.Task{
		Step:        "failover_step__0",
		StepStartAt: time.Now().Add(-5 * time.Minute),
		TaskID:      "test-task-id-8",
		TaskBatchID: "errr",
		Status:      "running",
		WorkFlow:    "xxx",
		Mutex:       "n_xx",
	}
	taskRunning3 := &iface.Task{
		Step:        "failover_step__0",
		StepStartAt: time.Now(),
		TaskID:      "test-task-id-20",
		Status:      "running",
		WorkFlow:    "xxx",
		Entity:      "ssd",
		Mutex:       "n_xx",
	}
	taskRunning4 := &iface.Task{
		Step:        "xxxxxxx__0",
		StepStartAt: time.Now().Add(-5 * time.Minute),
		TaskID:      "test-task-id-30",
		Status:      "running",
		WorkFlow:    "xxx",
		Mutex:       "n_xx",
	}
	taskRunning5 := &iface.Task{
		Step:        "failover_step__0",
		StepStartAt: time.Now(),
		TaskID:      "test-task-id-40",
		Status:      "running",
		WorkFlow:    "xxx",
		Mutex:       "mmm",
	}
	taskPrerun3 := &iface.Task{
		Step:        "failover_step__0",
		StepStartAt: time.Now(),
		TaskID:      "test-task-id-40-update_task_error",
		Status:      "prerun",
		WorkFlow:    "xxx",
		Mutex:       "n_xx",
	}
	type args struct {
		ctx        context.Context
		task       *iface.Task
		totalTasks []*iface.Task
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: t.Name(),
			args: args{
				ctx:  ctx,
				task: failoverTaskPrerun1,
				totalTasks: []*iface.Task{
					failoverTaskPrerun1,
				},
			},
			want: true,
		},
		{
			name: t.Name(),
			args: args{
				ctx:  ctx,
				task: failoverTaskPrerun1,
				totalTasks: []*iface.Task{
					failoverTaskPrerun1, failoverTaskPrerun2,
				},
			},
			want: true,
		},
		{
			name: t.Name(),
			args: args{
				ctx:  ctx,
				task: failoverTaskPrerun2,
				totalTasks: []*iface.Task{
					failoverTaskPrerun1, failoverTaskPrerun2,
				},
			},
			want: true,
		},
		{
			name: t.Name(),
			args: args{
				ctx:  ctx,
				task: failoverTaskPrerun2,
				totalTasks: []*iface.Task{
					failoverTaskPrerun1, failoverTaskPrerun2, taskRunning1, taskRunning2,
				},
			},
			want: false,
		},
		{
			name: t.Name(),
			args: args{
				ctx:  ctx,
				task: failoverTaskPrerun2,
				totalTasks: []*iface.Task{
					failoverTaskPrerun1, failoverTaskPrerun2, taskRunning1, taskRunning2, taskRunning4,
				},
			},
			want: false,
		},
		{
			name: t.Name(),
			args: args{
				ctx:  ctx,
				task: failoverTaskPrerun2,
				totalTasks: []*iface.Task{
					failoverTaskPrerun1, failoverTaskPrerun2, taskPrerun1, taskPrerun2,
				},
			},
			want: true,
		},
		{
			name: t.Name(),
			args: args{
				ctx:  ctx,
				task: failoverTaskPrerun2,
				totalTasks: []*iface.Task{
					failoverTaskPrerun1, failoverTaskPrerun2, taskPrerun1, taskPrerun2, taskRunning3,
				},
			},
			want: true,
		},
		{
			name: t.Name(),
			args: args{
				ctx:  ctx,
				task: failoverTaskPrerun2,
				totalTasks: []*iface.Task{
					failoverTaskPrerun1, failoverTaskPrerun2, taskPrerun1, taskPrerun2, taskRunning5,
				},
			},
			want: true,
		},
		{
			name: t.Name(),
			args: args{
				ctx:  ctx,
				task: taskPrerun1,
				totalTasks: []*iface.Task{
					failoverTaskPrerun1, failoverTaskPrerun2, taskPrerun1,
				},
			},
			want: false,
		},
		{
			name: t.Name(),
			args: args{
				ctx:  ctx,
				task: taskPrerun1,
				totalTasks: []*iface.Task{
					failoverTaskRunning1, failoverTaskRunning2, taskPrerun1,
				},
			},
			want: false,
		},
		{
			name: t.Name(),
			args: args{
				ctx:  ctx,
				task: taskPrerun3,
				totalTasks: []*iface.Task{
					failoverTaskPrerun1, failoverTaskRunning2, taskPrerun3,
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got, _ := needRunByFailoverCheck(tt.args.ctx, tt.args.task, tt.args.totalTasks); got != tt.want {
				t.Errorf("needRunByFailoverCheck() = %v, want %v", got, tt.want)
			}
		})
	}
	t.Run("test parallel", func(t *testing.T) {
		totalTasks := []*iface.Task{
			failoverTaskPrerun1, failoverTaskPrerun2, taskPrerun1,
		}
		g := gtask.Group{}
		g.Go(func() error {
			if needRun, _ := needRunByFailoverCheck(ctx, failoverTaskPrerun1, totalTasks); !needRun {
				t.Errorf("needRunByFailoverCheck() = %v, want %v", false, true)
			}
			failoverTaskPrerun1.Status = "running"
			return nil
		})
		g.Go(func() error {
			if needRun, _ := needRunByFailoverCheck(ctx, failoverTaskPrerun2, totalTasks); !needRun {
				t.Errorf("needRunByFailoverCheck() = %v, want %v", false, true)
			}
			failoverTaskPrerun2.Status = "running"
			return nil
		})
		g.Go(func() error {
			if needRun, _ := needRunByFailoverCheck(ctx, taskPrerun1, totalTasks); needRun {
				t.Errorf("needRunByFailoverCheck() = %v, want %v", false, true)
			}
			return nil
		})
		if _, err := g.Wait(); err != nil {
			t.Errorf("needRunByFailoverCheck() error = %v", err)
		}
		if failoverTaskPrerun1.Status != "running" || failoverTaskPrerun2.Status != "running" || taskPrerun1.Status != "manual" {
			t.Errorf("needRunByFailoverCheck() error, failoverTaskPrerun1.Status: %v, failoverTaskPrerun2.Status: %v, "+
				"taskPrerun1.Status: %v", failoverTaskPrerun1.Status, failoverTaskPrerun2.Status, taskPrerun1.Status)
		}
	})
	failoverTaskPrerun1.Status = "prerun"
	failoverTaskPrerun2.Status = "prerun"
	t.Run("test parallel", func(t *testing.T) {
		totalTasks := []*iface.Task{
			failoverTaskPrerun1, failoverTaskPrerun2, taskRunning1,
		}
		g := gtask.Group{}
		g.Go(func() error {
			if needRun, _ := needRunByFailoverCheck(ctx, failoverTaskPrerun1, totalTasks); needRun {
				t.Errorf("needRunByFailoverCheck() = %v, want %v", true, false)
			}
			// failoverTaskPrerun1.Status = "running"
			return nil
		})
		g.Go(func() error {
			if needRun, _ := needRunByFailoverCheck(ctx, failoverTaskPrerun1, totalTasks); needRun {
				t.Errorf("needRunByFailoverCheck() = %v, want %v", true, false)
			}
			// failoverTaskPrerun2.Status = "running"
			return nil
		})
		if _, err := g.Wait(); err != nil {
			t.Errorf("needRunByFailoverCheck() error = %v", err)
		}
		if failoverTaskPrerun1.Status != "prerun" || failoverTaskPrerun2.Status != "prerun" || taskRunning1.Status != "running" {
			t.Errorf("needRunByFailoverCheck() error, failoverTaskPrerun1.Status: %v, failoverTaskPrerun2.Status: %v, "+
				"taskPrerun1.Status: %v", failoverTaskPrerun1.Status, failoverTaskPrerun2.Status, taskRunning1.Status)
		}
	})
	sc := &workflow.StepContext{
		Step: &workflow.Step{
			Name:            "step_failover",
			Workflow:        "xxx",
			StepProcess:     nil,
			SuccessNextStep: "",
			ErrorNextStep:   "",
			Option: &workflow.StepOption{
				StepSplitHandler:   workflow.DefaultStepSplitHandler,
				NextStepHandler:    nil,
				StepTimeout:        0,
				MaxReentry:         0,
				MaxReentryNextStep: "",
			},
		},
		Reentry:         0,
		IsPromoted:      false,
		IsErrorPromoted: false,
		NeedManual:      false,
	}
	if err := executePrerun(ctx, taskPrerun1, []*iface.Task{failoverTaskRunning1, failoverTaskPrerun1}, sc); err != nil {
		t.Errorf("executePrerun() error = %v", err)
	}
}
