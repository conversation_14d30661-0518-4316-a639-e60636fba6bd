package lb

import "context"

type CreateLbParams struct {
}

type Lb struct {
}

type CreateListenerParams struct {
}

type Rs struct {
}

type Service interface {
	Create(ctx context.Context, params *CreateLbParams) (blb *Lb, err error)
	Get(ctx context.Context, lbID string) (blb *Lb, err error)
	Delete(ctx context.Context, lbID string) (err error)
	CreateListener(ctx context.Context, lbID string, params *CreateListenerParams) (blb *Lb, err error)
	DeleteListener(ctx context.Context, lbID string, port int) (blb *Lb, err error)
	BindRs(ctx context.Context, lbID string, rs []*Rs) (blb *Lb, err error)
	UnbindRs(ctx context.Context, lbID string, rs []*Rs) (blb *Lb, err error)
}
