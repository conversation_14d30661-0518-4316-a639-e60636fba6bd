package adaptor

import (
	"context"
	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/scs/x1-base/model"
	"testing"
)

func TestInit(t *testing.T) {
	g := gtask.Group{}
	g.Go(func() error {
		MustInitDB(context.Background(), &DBConf{})
		return nil
	})
	_, err := g.Wait()
	if err == nil {
		t.<PERSON>rrorf("Expected error")
	}
}
func initMock(t *testing.T) (sqlmock.Sqlmock, func()) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	resourceInst = &model.Resource{
		ModelGorm: gormDB,
	}
	resourceInst.AutoPreload = true
	return mock, func() {
		_ = db.Close()
	}
}
