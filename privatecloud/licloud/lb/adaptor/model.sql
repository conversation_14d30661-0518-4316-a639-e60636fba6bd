CREATE TABLE `li_cloud_lb` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `lb_id` varchar(255) NOT NULL DEFAULT '',
    `name` varchar(255) NOT NULL DEFAULT '',
    `created_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
    `status` varchar(255) NOT NULL DEFAULT '',
    `batch_id` varchar(255) NOT NULL DEFAULT '',
    `vdc` varchar(255) NOT NULL DEFAULT '',
    `vpc` varchar(255) NOT NULL DEFAULT '',
    `cluster` varchar(255) NOT NULL DEFAULT '',
    `env` varchar(255) NOT NULL DEFAULT '',
    `category` varchar(255) NOT NULL DEFAULT '',
    `domain` varchar(255) NOT NULL DEFAULT '',
    `tenant_id` varchar(255) NOT NULL DEFAULT '',
    `owner` varchar(255) NOT NULL DEFAULT '',
    `vip` varchar(255) NOT NULL DEFAULT '',
    `deleted_at` varchar(255) NOT NULL DEFAULT '',
    `ext` varchar(1023) NOT NULL DEFAULT '',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_li_cloud_lb_lb_id` (`lb_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8

CREATE TABLE `li_cloud_listener` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `lb_id` varchar(255) NOT NULL DEFAULT '',
    `port` int(11) NOT NULL DEFAULT 0,
    `backend_port` int(11) NOT NULL DEFAULT 0,
    `protocol` varchar(255) NOT NULL DEFAULT '',
    `scheduler` varchar(255) NOT NULL DEFAULT '',
    `deleted_at` varchar(255) NOT NULL DEFAULT '',
    `ext` varchar(1023) NOT NULL DEFAULT '',
    PRIMARY KEY (`id`),
    KEY `idx_li_cloud_listener_lb_id` (`lb_id`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8

CREATE TABLE `li_cloud_server` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `lb_id` varchar(255) NOT NULL DEFAULT '',
    `server_id` varchar(255) NOT NULL DEFAULT '',
    `ip` varchar(255) NOT NULL DEFAULT '',
    `port` int(11) NOT NULL DEFAULT 0,
    `weight` varchar(255) NOT NULL DEFAULT '',
    `deleted_at` varchar(255) NOT NULL DEFAULT '',
    `ext` varchar(1023) NOT NULL DEFAULT '',
    PRIMARY KEY (`id`),
    KEY `idx_li_cloud_server_lb_id` (`lb_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1001 DEFAULT CHARSET=utf8