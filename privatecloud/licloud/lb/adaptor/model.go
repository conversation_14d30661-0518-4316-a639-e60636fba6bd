package adaptor

import (
	"context"
	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model"
	"time"
)

const (
	StatusPreCreate = "pre_create"
	StatusCreating  = "creating"
	StatusRunning   = "running"
	StatusStopping  = "stopping"
	StatusStopped   = "stopped"
	StatusDeleting  = "deleting"
	StatusDeleted   = "deleted"
)

type Lb struct {
	ID        int64       `json:"id" gorm:"column:id"`
	LbID      string      `json:"lb_id" gorm:"column:lb_id"`
	Name      string      `json:"name" gorm:"column:name"`
	CreatedAt time.Time   `json:"created_at" gorm:"column:created_at"`
	Status    string      `json:"status" gorm:"column:status"`
	BatchID   string      `json:"batch_id" gorm:"column:batch_id"`
	Vdc       string      `json:"vdc" gorm:"column:vdc"`
	Vpc       string      `json:"vpc" gorm:"column:vpc"`
	Cluster   string      `json:"cluster" gorm:"column:cluster"`
	Env       string      `json:"env" gorm:"column:env"`
	Category  string      `json:"category" gorm:"column:category"`
	Domain    string      `json:"domain" gorm:"column:domain"`
	TenantID  string      `json:"tenant_id" gorm:"column:tenant_id"`
	Owner     string      `json:"owner" gorm:"column:owner"`
	Vip       string      `json:"vip" gorm:"column:vip"`
	DeletedAt string      `json:"deleted_at" gorm:"column:deleted_at"`
	Ext       string      `json:"ext" gorm:"column:ext"`
	Listeners []*Listener `json:"listeners" gorm:"foreignKey:LbID;references:LbID"`
	Servers   []*Server   `json:"servers" gorm:"foreignKey:LbID;references:LbID"`
}

func (Lb) TableName() string {
	return "li_cloud_lb"
}

type Listener struct {
	ID          int64  `json:"id" gorm:"column:id"`
	LbID        string `json:"lb_id" gorm:"column:lb_id"`
	Port        int    `json:"port" gorm:"column:port"`
	BackendPort int    `json:"backend_port" gorm:"column:backend_port"`
	Protocol    string `json:"protocol" gorm:"column:protocol"`
	Scheduler   string `json:"scheduler" gorm:"column:scheduler"`
	DeletedAt   string `json:"deleted_at" gorm:"column:deleted_at"`
	Ext         string `json:"ext" gorm:"column:ext"`
}

func (Listener) TableName() string {
	return "li_cloud_listener"
}

type Server struct {
	ID        int64  `json:"id" gorm:"column:id"`
	LbID      string `json:"lb_id" gorm:"column:lb_id"`
	ServerID  string `json:"server_id" gorm:"column:server_id"`
	IP        string `json:"ip" gorm:"column:ip"`
	Port      int    `json:"port" gorm:"column:port"`
	Weight    string `json:"weight" gorm:"column:weight"`
	DeletedAt string `json:"deleted_at" gorm:"column:deleted_at"`
	Ext       string `json:"ext" gorm:"column:ext"`
}

func (Server) TableName() string {
	return "li_cloud_server"
}

type DBConf struct {
	DbLogger     logit.Logger // gdp db logger
	GormLogger   logit.Logger // gorm 操作logger
	ServicerName string       // 自定义x1 mysql servicer name，传空字符串走默认  "db_x1"
}

var (
	resourceInst *model.Resource = nil
)

func MustInitDB(ctx context.Context, conf *DBConf) {
	servicerName := "li-cloud-lb"
	if conf.ServicerName != "" {
		servicerName = conf.ServicerName
	}

	resourcePtr, err := model.InitModel(ctx, model.ResourceCfg{
		ServicerName: servicerName,
		DbLogger:     conf.DbLogger,
		GormLogger:   conf.GormLogger,
		PreloadConf:  map[string][]string{},
		AutoPreload:  true,
	})
	if err != nil {
		panic("init li-cloud-lb model fail")
	}
	resourceInst = resourcePtr
}

func HasInited() bool {
	return resourceInst != nil
}

func GetDbAgent(ctx context.Context) (*model.Resource, error) {
	if !HasInited() {
		return nil, errors.Errorf("li-cloud-lb model has not init")
	}
	return resourceInst, nil
}

func Save(ctx context.Context, lbs []*Lb) error {
	db, err := GetDbAgent(ctx)
	if err != nil {
		return err
	}
	return db.FullSaveAssociationsSave(ctx, lbs)
}

func Retrieve(ctx context.Context, fmtCond string, vals ...interface{}) ([]*Lb, error) {
	db, err := GetDbAgent(ctx)
	if err != nil {
		return nil, err
	}
	var lbs []*Lb
	if err := db.GetAllByCond(ctx, &lbs, fmtCond, vals...); err != nil {
		return nil, err
	}
	return lbs, nil
}
