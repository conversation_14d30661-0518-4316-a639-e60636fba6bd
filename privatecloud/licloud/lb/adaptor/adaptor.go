package adaptor

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/spf13/cast"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/lb/sdk"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/utils/fieldtrans"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/elb"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type adaptorT struct {
	sdk   sdk.Service
	trans Transform
}

var (
	ErrCreateInProgress = errors.New("lb is still in creating status")
	ErrLbNotFound       = errors.New("lb not found")
	initOnce            = sync.Once{}
	adaptorInstance     *adaptorT
)

func (a *adaptorT) CreateELB(ctx context.Context, req *elb.CreateELBRequest) (rsp *elb.OperationResponse, err error) {
	lbs, err := Retrieve(ctx, "name = ? AND deleted_at = ?", req.Name, "")
	if err != nil {
		return nil, err
	}
	if len(lbs) > 0 {
		return getSuccessOperationResponse(req.Auth, lbs[0]), nil
	}
	v := fieldtrans.GetVpcFromString(req.VpcId)
	randStr, _ := base_utils.GenerateRandomString(12)
	env := "test"
	if v.Env == "prod" {
		env = "prod"
	}
	lb := &Lb{
		LbID:     "lilb-" + randStr,
		Name:     req.Name,
		Status:   StatusPreCreate,
		Vdc:      v.Vdc,
		Cluster:  config.DefaultCluster,
		Env:      env,
		Category: LbCategoryLb,
		Domain:   config.DefaultDomain,
		TenantID: v.TanentID,
		Owner:    config.DefaultOwner,
	}
	// 无法获取vdc和env时，使用默认值
	if lb.Env == "" {
		lb.Env = config.DefaultEnv
		lb.Vdc = config.DefaultVdc
		lb.TenantID = config.DefaultTenantID
	}
	if err := Save(ctx, []*Lb{lb}); err != nil {
		return nil, err
	}
	return getSuccessOperationResponse(req.Auth, lb), nil
}

func (a *adaptorT) DeleteELB(ctx context.Context, req *elb.DeleteELBRequest) (rsp *elb.OperationResponse, err error) {
	return nil, errors.New("not implemented")
}

func (a *adaptorT) StartELB(ctx context.Context, req *elb.StartELBRequest) (rsp *elb.OperationResponse, err error) {
	return nil, errors.New("not implemented")
}

func (a *adaptorT) StopELB(ctx context.Context, req *elb.StopELBRequest) (rsp *elb.OperationResponse, err error) {
	return nil, errors.New("not implemented")
}

func (a *adaptorT) CreateListener(ctx context.Context, req *elb.CreateListenerRequest) (rsp *elb.OperationResponse, err error) {
	lbs, err := Retrieve(ctx, "lb_id = ? AND deleted_at = ?", req.ElbId, "")
	if err != nil {
		return nil, err
	}
	if len(lbs) == 0 {
		return nil, fmt.Errorf("elb %s not found", req.ElbId)
	}
	modifyFlag := true
	for _, listener := range lbs[0].Listeners {
		if listener.DeletedAt != "" {
			continue
		}
		if listener.Port == int(req.Port) {
			modifyFlag = false
			break
		}
	}
	if !modifyFlag {
		return getSuccessOperationResponse(req.Auth, lbs[0]), nil
	}
	lbs[0].Listeners = append(lbs[0].Listeners, &Listener{
		LbID:        req.ElbId,
		Port:        int(req.Port),
		BackendPort: int(req.BackendPort),
		Protocol:    req.Type,
		Scheduler:   req.Scheduler,
	})
	if lbs[0].Status != StatusPreCreate && lbs[0].Status != StatusRunning {
		return nil, fmt.Errorf("elb %s is not in listener modifiable status", req.ElbId)
	}
	if lbs[0].Status == StatusRunning {
		if err := a.updateLb(ctx, lbs[0]); err != nil {
			return nil, err
		}
	}
	if err := Save(ctx, lbs); err != nil {
		return nil, err
	}
	return getSuccessOperationResponse(req.Auth, lbs[0]), nil
}

func (a *adaptorT) DeleteListener(ctx context.Context, req *elb.DeleteListenerRequest) (rsp *elb.OperationResponse, err error) {
	lbs, err := Retrieve(ctx, "lb_id = ? AND deleted_at = ?", req.ElbId, "")
	if err != nil {
		return nil, err
	}
	if len(lbs) == 0 {
		return nil, fmt.Errorf("elb %s not found", req.ElbId)
	}
	modifyFlag := false
	for _, listener := range lbs[0].Listeners {
		if listener.DeletedAt != "" {
			continue
		}
		if listener.Port == int(req.ListenerPort) {
			listener.DeletedAt = time.Now().Format("2006-01-02 15:04:05")
			modifyFlag = true
			break
		}
	}
	if !modifyFlag {
		return getSuccessOperationResponse(req.Auth, lbs[0]), nil
	}
	if lbs[0].Status != StatusRunning {
		return nil, fmt.Errorf("elb %s is not in listener modifiable status", req.ElbId)
	}
	if err := a.updateLb(ctx, lbs[0]); err != nil {
		return nil, err
	}
	if err := Save(ctx, lbs); err != nil {
		return nil, err
	}
	return getSuccessOperationResponse(req.Auth, lbs[0]), nil
}

func (a *adaptorT) CreateBackendServer(ctx context.Context, req *elb.CreateBackendServerRequest) (rsp *elb.OperationResponse, err error) {
	lbs, err := Retrieve(ctx, "lb_id = ? AND deleted_at = ?", req.ElbId, "")
	if err != nil {
		return nil, err
	}
	if len(lbs) == 0 {
		return nil, fmt.Errorf("elb %s not found", req.ElbId)
	}
	var newServers []*Server
	ipServer := make(map[string]*Server)
	for _, server := range lbs[0].Servers {
		newServers = append(newServers, server)
		if server.DeletedAt != "" {
			continue
		}
		ipServer[server.ServerID] = server
	}
	modifyFlag := false
	for _, server := range req.BackendServerList {
		if _, ok := ipServer[server.InstanceId]; ok {
			continue
		}
		modifyFlag = true
		ip, err := a.trans.ServerIDToIP(ctx, server.InstanceId)
		if err != nil {
			return nil, err
		}
		newServers = append(newServers, &Server{
			LbID:     req.ElbId,
			ServerID: server.InstanceId,
			IP:       ip,
			Weight:   strconv.Itoa(int(server.Weight)),
		})
	}
	if !modifyFlag {
		return getSuccessOperationResponse(req.Auth, lbs[0]), nil
	}
	switch lbs[0].Status {
	case StatusPreCreate, StatusCreating:
		if err := a.createLb(ctx, lbs[0], newServers); err != nil {
			return nil, err
		}
	case StatusRunning:
		if err := a.updateLb(ctx, lbs[0]); err != nil {
			return nil, err
		}
	default:
		return nil, fmt.Errorf("elb %s is not in backend server modifiable status", req.ElbId)
	}
	if err := Save(ctx, lbs); err != nil {
		return nil, err
	}
	return getSuccessOperationResponse(req.Auth, lbs[0]), nil
}

func (a *adaptorT) DeleteBackendServer(ctx context.Context, req *elb.DeleteBackendServerRequest) (rsp *elb.OperationResponse, err error) {
	lbs, err := Retrieve(ctx, "lb_id = ? AND deleted_at = ?", req.ElbId, "")
	if err != nil {
		return nil, err
	}
	if len(lbs) == 0 {
		return nil, fmt.Errorf("elb %s not found", req.ElbId)
	}
	modifyFlag := false
	for _, server := range lbs[0].Servers {
		if server.DeletedAt != "" {
			continue
		}
		if server.ServerID == req.BackendId {
			server.DeletedAt = time.Now().Format("2006-01-02 15:04:05")
			modifyFlag = true
			break
		}
	}
	if !modifyFlag {
		return getSuccessOperationResponse(req.Auth, lbs[0]), nil
	}
	if lbs[0].Status != StatusRunning {
		return nil, fmt.Errorf("elb %s is not in backend server modifiable status", req.ElbId)
	}
	if err := a.updateLb(ctx, lbs[0]); err != nil {
		return nil, err
	}
	if err := Save(ctx, lbs); err != nil {
		return nil, err
	}
	return getSuccessOperationResponse(req.Auth, lbs[0]), nil
}

func (a *adaptorT) ListElb(ctx context.Context, req *elb.ListElbRequest) (rsp *elb.ListElbResponse, err error) {
	lbs, err := Retrieve(ctx, "lb_id = ? AND deleted_at = ?", req.ElbId, "")
	if err != nil {
		return nil, err
	}
	if len(lbs) == 0 {
		return nil, ErrLbNotFound
	}
	resp := &elb.ListElbResponse{
		Status:     lbs[0].Status,
		Vip:        lbs[0].Vip,
		CreateTime: lbs[0].CreatedAt.Format("2006-01-02 15:04:05"),
		Internal:   false,
		Name:       lbs[0].Name,
		Ovip:       lbs[0].Vip,
		Ipv6:       "",
	}
	return resp, nil
}

func (a *adaptorT) ListListener(ctx context.Context, req *elb.ListElbRequest) (rsp *elb.ListListenerResponse, err error) {
	lbs, err := Retrieve(ctx, "lb_id = ? AND deleted_at = ?", req.ElbId, "")
	if err != nil {
		return nil, err
	}
	if len(lbs) == 0 {
		return nil, ErrLbNotFound
	}
	for _, listener := range lbs[0].Listeners {
		if listener.Port == int(req.Port) {
			if listener.DeletedAt != "" {
				continue
			}
			return &elb.ListListenerResponse{
				BackendPort: int32(listener.BackendPort),
				Type:        listener.Protocol,
				Scheduler:   listener.Scheduler,
			}, nil
		}
	}
	return nil, cerrs.ErrNotFound.Errorf("listener port %d of elb %s not found", req.Port, req.ElbId)
}

func (a *adaptorT) ListBackendServer(ctx context.Context, req *elb.ListElbRequest) (rsp *elb.ListBackendServerResponse, err error) {
	lbs, err := Retrieve(ctx, "lb_id = ? AND deleted_at = ?", req.ElbId, "")
	if err != nil {
		return nil, err
	}
	if len(lbs) == 0 {
		return nil, ErrLbNotFound
	}
	resp := &elb.ListBackendServerResponse{}
	for _, server := range lbs[0].Servers {
		if server.DeletedAt != "" {
			continue
		}
		resp.BackendServerList = append(resp.BackendServerList, &elb.BackendServerList{
			InstanceId: server.IP,
			Weight:     server.Weight,
		})
	}
	return resp, nil
}

func (a *adaptorT) BindEip(ctx context.Context, req *elb.BindEipRequest) (rsp *elb.BindEipResponse, err error) {
	return nil, nil
}

func (a *adaptorT) QueryEip(ctx context.Context, req *elb.BindQueryRequest) (rsp *elb.BindQueryResponse, err error) {
	return nil, nil
}

func (a *adaptorT) UnbindEip(ctx context.Context, req *elb.UnbindEipRequest) (rsp *elb.UnbindEipResponse, err error) {
	return nil, nil
}

func (a *adaptorT) UpdateBackendServer(ctx context.Context, req *elb.UpdateBackendServerRequest) (rsp *elb.OperationResponse, err error) {
	return nil, nil
}

func getSuccessOperationResponse(auth *common.Authentication, lb *Lb) *elb.OperationResponse {
	return &elb.OperationResponse{
		TransactionId: auth.TransactionId,
		BlbList: []*elb.Elb{
			{
				Vip:  lb.Vip,
				Id:   lb.LbID,
				Name: lb.Name,
				Ovip: lb.Vip,
				Type: lb.Category,
				Ipv6: "",
			},
		},
		Success:   true,
		RequestId: auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}
}

func getAuth(ctx context.Context) (*common.Authentication, error) {
	// TODO 需要使用IDaaS鉴权
	return &common.Authentication{
		IamUserId:       "?",
		TransactionId:   "?",
		Credential:      nil,
		ResourceAccount: nil,
	}, nil
}

func (a *adaptorT) createLb(ctx context.Context, lb *Lb, newServers []*Server) error {
	if err := a.sendCreateLb(ctx, lb, newServers); err != nil {
		return err
	}
	nctx, cancel := context.WithTimeout(ctx, time.Duration(config.CreateTimeoutSec)*time.Second)
	defer cancel()
	for {
		select {
		case <-nctx.Done():
			return fmt.Errorf("create lb %s timeout", lb.LbID)
		default:
			if err := a.checkBatchRet(ctx, lb, newServers); err != nil {
				if errors.Is(err, ErrCreateInProgress) {
					time.Sleep(time.Duration(config.CreateCheckIntervalSec) * time.Second)
					continue
				}
				return err
			}
			return nil
		}
	}
}

func (a *adaptorT) sendCreateLb(ctx context.Context, lb *Lb, newServers []*Server) error {
	if lb.Status != StatusPreCreate {
		logger.DefaultLogger.Trace(ctx, "lb %s is not in pre-create status, skip sending", lb.LbID)
		return nil
	}
	request := buildRequest(lb, newServers)
	logger.DefaultLogger.Trace(ctx, "create lb request: %s", base_utils.Format(request))
	resp, err := a.sdk.Create(ctx, request)
	if err != nil {
		logger.DefaultLogger.Warning(ctx, "create lb fail: %s", err.Error())
		return err
	}
	if resp.Data.Accepted != 1 {
		logger.DefaultLogger.Warning(ctx, "create lb fail, resp: %s", base_utils.Format(resp))
		return fmt.Errorf("create lb fail, resp: %s", base_utils.Format(resp))
	}
	lb.BatchID = strconv.Itoa(resp.Data.BatchID)
	lb.Status = StatusCreating
	if err := Save(ctx, []*Lb{lb}); err != nil {
		logger.DefaultLogger.Warning(ctx, "save lb batch id failed: %s", err.Error())
		return err
	}
	return nil
}

func (a *adaptorT) checkBatchRet(ctx context.Context, lb *Lb, newServers []*Server) error {
	if lb.Status != StatusCreating {
		logger.DefaultLogger.Warning(ctx, "lb %s is not in creating status", lb.LbID)
		return fmt.Errorf("lb %s is not in creating status", lb.LbID)
	}
	if lb.BatchID == "" {
		logger.DefaultLogger.Warning(ctx, "lb %s batch id is empty", lb.LbID)
		return fmt.Errorf("lb %s batch id is empty", lb.LbID)
	}
	resp, err := a.sdk.GetBatch(ctx, &sdk.BatchRequest{
		BatchID: cast.ToInt(lb.BatchID),
	})
	if err != nil {
		logger.DefaultLogger.Warning(ctx, "get lb batch status failed: %s", err.Error())
		return err
	}
	logger.DefaultLogger.Trace(ctx, "get lb batch status: %s", base_utils.Format(resp))
	if resp.Code != GetBatchSuccessCode {
		logger.DefaultLogger.Warning(ctx, "lb %s create fail, code: %s, message: %s", lb.LbID, resp.Code, resp.Message)
		return fmt.Errorf("lb %s create fail, code: %s, message: %s", lb.LbID, resp.Code, resp.Message)
	}
	if resp.Data.Status == GetBatchStatusSuccess {
		lb.Status = StatusRunning
		lb.Vip = resp.Data.LBPrivateIP
		lb.Servers = newServers
		if err := Save(ctx, []*Lb{lb}); err != nil {
			logger.DefaultLogger.Warning(ctx, "save lb status failed: %s", err.Error())
			return err
		}
		return nil
	}
	if resp.Data.Status == GetBatchStatusDoing {
		logger.DefaultLogger.Trace(ctx, "lb %s is still in creating status", lb.LbID)
		return ErrCreateInProgress
	}
	return fmt.Errorf("lb %s create fail, resp %s", lb.LbID, base_utils.Format(resp))
}

func (a *adaptorT) updateLb(ctx context.Context, lb *Lb) error {
	return errors.New("update lb not implemented")
}

func buildRequest(lb *Lb, newServers []*Server) *sdk.Request {
	request := &sdk.Request{
		Functional: sdk.Functional{},
		Data: []*sdk.Data{
			{
				Target: &sdk.Target{
					Vdc:     lb.Vdc,
					Cluster: lb.Vdc,
					Env:     lb.Env,
					Domain:  "",
				},
				Resource: &sdk.Resource{
					ApplicationType: "lb",
					Name:            lb.Name,
					TenantID:        lb.TenantID,
					Owner:           lb.Owner,
					Used:            fmt.Sprintf("create-for-%s", lb.Name),
				},
				Backend: &sdk.Backend{
					Category: "lb",
				},
				Listeners: nil,
				Tags:      nil,
			},
		},
	}
	for _, server := range newServers {
		if server.DeletedAt != "" {
			continue
		}
		request.Data[0].Backend.Servers = append(request.Data[0].Backend.Servers, sdk.Server{
			IP:     server.IP,
			Weight: cast.ToInt(server.Weight),
		})
	}
	for _, listener := range lb.Listeners {
		if listener.DeletedAt != "" {
			continue
		}
		request.Data[0].Listeners = append(request.Data[0].Listeners, &sdk.Listener{
			Port:        fmt.Sprintf("%d", listener.Port),
			BackendPort: fmt.Sprintf("%d", listener.BackendPort),
			Protocol:    listener.Protocol,
			Scheduler:   listener.Scheduler,
		})
	}
	request.Data[0].Tags = append(request.Data[0].Tags, &sdk.Tag{
		Key:   TagAddressType,
		Value: config.AddressType,
	})
	request.Data[0].Tags = append(request.Data[0].Tags, &sdk.Tag{
		Key:   TagBillingType,
		Value: config.BillingType,
	})
	request.Data[0].Tags = append(request.Data[0].Tags, &sdk.Tag{
		Key:   TagBandWidth,
		Value: config.BandWidth,
	})
	return request
}

func GetLiCloudLBAdaptor(sdk sdk.Service) elb.ELBService {
	return &adaptorT{
		sdk: sdk,
	}
}

func Init(ctx context.Context, dbConf *DBConf) {
	initOnce.Do(func() {
		adaptorInstance = &adaptorT{
			sdk:   sdk.NewSdk(ServiceName),
			trans: NewTransform(),
		}
	})
	MustLoadConfig()
	MustInitDB(ctx, dbConf)
}

func Instance() elb.ELBService {
	return adaptorInstance
}
