package adaptor

import (
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const (
	ServiceName   = "licloud-lb"
	extraConfName = "LiCloudLBConfig"
)

type Config struct {
	BillingType            string `toml:"BillingType"`
	BandWidth              string `toml:"BandWidth"`
	AddressType            string `toml:"AddressType"`
	CreateTimeoutSec       int    `toml:"CreateTimeoutSec"`
	CreateCheckIntervalSec int    `toml:"CreateCheckIntervalSec"`
	DefaultCluster         string `toml:"DefaultCluster"`
	DefaultTenantID        string `toml:"DefaultTenantID"`
	DefaultDomain          string `toml:"DefaultDomain"`
	DefaultOwner           string `toml:"DefaultOwner"`
	DefaultEnv             string `toml:"DefaultEnv"`
	DefaultVdc             string `toml:"DefaultVdc"`
}

var config *Config

func MustLoadConfig() {
	config = &Config{}
	config.mustLoad(ServiceName)
}

func (conf *Config) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
