package adaptor

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/vm/adaptor"
)

type Transform interface {
	ServerIDToIP(ctx context.Context, serverID string) (string, error)
}

type transform struct {
}

func (t *transform) ServerIDToIP(ctx context.Context, serverID string) (string, error) {
	return adaptor.IDToIP(ctx, serverID)
}

func NewTransform() Transform {
	return &transform{}
}
