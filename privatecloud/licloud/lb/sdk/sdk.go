package sdk

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/iam/sdk"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"strconv"
)

const (
	CreateUrl    = "/apis/v1/loadbalancers"
	UpdateUrl    = "/apis/v1/loadbalancers/%s"
	GetBatchUrl  = "/apis/v1/batches/loadbalancers/%s"
	GetDetailUrl = "/apis/v1/loadbalancers/%s"
)

type sdkT struct {
	common.OpenApi
}

func (s *sdkT) doRequest(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]interface{}, req, rsp interface{}) (err error) {

	liSDEToken, err := sdk.AutoGetSDETokenByIDC(ctx)
	if err != nil {
		return err
	}
	params := &common.OpenApiParams{
		ActionName:      actionName,
		HttpMethod:      httpMethod,
		Uri:             uri,
		Queries:         queries,
		Posts:           req,
		Token:           liSDEToken,
		PrivateCloudEnv: common.PrivateCloudEnvLiXiangSDE,
	}

	// 请求 openapi
	return s.DoRequest(ctx, params, rsp)
}

func (s *sdkT) Create(ctx context.Context, req *Request) (*Response, error) {
	resp := &Response{}
	if err := s.doRequest(ctx, "Create", nil, "POST", CreateUrl, nil, req, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *sdkT) Update(ctx context.Context, req *Request) (*Response, error) {
	resp := &Response{}
	if err := s.doRequest(ctx, "Update", nil, "PUT", fmt.Sprintf(UpdateUrl, req.LbName), nil, req, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *sdkT) GetBatch(ctx context.Context, req *BatchRequest) (*QueryResponse, error) {
	resp := &QueryResponse{}
	if err := s.doRequest(ctx, "GetBatch", nil, "GET", fmt.Sprintf(GetBatchUrl, strconv.Itoa(req.BatchID)), nil, nil, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *sdkT) GetDetail(ctx context.Context, req *QueryRequest) (*QueryResponse, error) {
	resp := &QueryResponse{}
	if err := s.doRequest(ctx, "GetDetail", nil, "GET", fmt.Sprintf(GetDetailUrl, req.LbName), nil, nil, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func NewSdk(serviceName string) Service {
	return &sdkT{
		OpenApi: common.NewOpenApi(serviceName),
	}
}
