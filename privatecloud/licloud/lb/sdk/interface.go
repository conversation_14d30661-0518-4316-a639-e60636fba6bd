package sdk

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

type Request struct {
	Functional Functional `json:"functional"`
	LbName     string     `json:"lb_name,omitempty"`
	Data       []*Data    `json:"data"`
}

type Functional struct {
	Async bool `json:"async"`
}

type Data struct {
	Target    *Target     `json:"target"`
	Resource  *Resource   `json:"resource"`
	Backend   *Backend    `json:"backend"`
	Listeners []*Listener `json:"listeners"`
	Tags      []*Tag      `json:"tags"`
}

type Target struct {
	Vdc     string `json:"vdc"`
	Cluster string `json:"cluster"`
	Env     string `json:"env"`
	Domain  string `json:"domain"`
}

type Resource struct {
	ApplicationType string `json:"application_type"`
	Name            string `json:"name"`
	TenantID        string `json:"tenant_id"`
	Owner           string `json:"owner"`
	Used            string `json:"used"`
}

type Backend struct {
	Category              string   `json:"category"`
	Servers               []Server `json:"servers"`
	Application           string   `json:"application,omitempty"`
	ExternalTrafficPolicy string   `json:"externalTrafficPolicy,omitempty"`
}

type Server struct {
	IP     string `json:"ip"`
	Weight int    `json:"weight"`
}

type Listener struct {
	Port        string `json:"port"`
	BackendPort string `json:"backend_port"`
	Protocol    string `json:"protocol"`
	Scheduler   string `json:"scheduler"`
}

type Tag struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type Response struct {
	Code    string   `json:"code"`
	Message string   `json:"message"`
	Data    RespData `json:"data"`
}

type RespData struct {
	Accepted int `json:"accepted"`
	BatchID  int `json:"batch_id"`
}

type BatchRequest struct {
	BatchID int                    `json:"batch_id"`
	Auth    *common.Authentication `json:"-"`
}

type QueryRequest struct {
	LbName string                 `json:"lb_name"`
	Auth   *common.Authentication `json:"-"`
}

type QueryResponse struct {
	Code    string     `json:"code"`
	Message string     `json:"message"`
	Data    *QueryData `json:"data"`
}

type QueryData struct {
	BatchID     int         `json:"batch_id"`
	Status      string      `json:"status"`
	LBID        string      `json:"lb_id"`
	LBPrivateIP string      `json:"lb_private_ip"`
	LBPublicIP  string      `json:"lb_public_ip"`
	LBName      string      `json:"lb_name"`
	Backend     Backend     `json:"backend"`
	Listeners   []*Listener `json:"listeners"`
	Tags        []*Tag      `json:"tags"`
	Message     string      `json:"message"`
}

type Service interface {
	Create(ctx context.Context, req *Request) (*Response, error)
	Update(ctx context.Context, req *Request) (*Response, error)
	GetBatch(ctx context.Context, req *BatchRequest) (*QueryResponse, error)
	GetDetail(ctx context.Context, req *QueryRequest) (*QueryResponse, error)
}
