/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><EMAIL>)
 * Date: 2024/02/08
 * File: adaptor.go
 */

/*
 * DESCRIPTION
 *   TODO 确认实现
 */

// Package adaptor
package adaptor

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
)

type LiSts struct {
}

const PrivateCloudTokenPrefix = "scs-private|2a5d355806e24876ab025d14529d26e5"

func (l LiSts) GetAssumeRole(ctx context.Context, req *sts.GetAssumeRoleRequest) (rsp *sts.GetAssumeRoleResponse, err error) {
	logger.DefaultLogger.Trace(ctx, "li mock GetAssumeRole")
	uid := req.IamUserId
	return &sts.GetAssumeRoleResponse{Token: &sts.Token{Id: fmt.Sprintf("%s|%s", PrivateCloudTokenPrefix, uid)}}, nil
}

func (l LiSts) GetEncryptResourceAccountId(ctx context.Context) (rsp *sts.EncryptResourceAccountIdResponse, err error) {
	logger.DefaultLogger.Trace(ctx, "li mock GetEncryptResourceAccountId")
	return &sts.EncryptResourceAccountIdResponse{}, nil
}

func (l LiSts) GetOpenApiAuth(ctx context.Context, req *sts.GetOpenApiAuthRequest) (rsp *sts.GetOpenApiAuthResponse, err error) {
	logger.DefaultLogger.Trace(ctx, "li mock GetOpenApiAuth")
	uid := req.IamUserId

	return &sts.GetOpenApiAuthResponse{Auth: &common.Authentication{
		IamUserId:     uid,
		TransactionId: "",
		Credential: &common.Credential{
			Ak:           "",
			Sk:           "",
			SessionToken: fmt.Sprintf("%s|%s", PrivateCloudTokenPrefix, uid),
		},
		ResourceAccount: &common.ResourceAccount{
			ResourceAk:       "",
			EncryptAccountId: fmt.Sprintf("%s|%s", PrivateCloudTokenPrefix, uid),
		},
	}}, nil
}

func NewSTS() sts.StsService {
	return &LiSts{}
}
