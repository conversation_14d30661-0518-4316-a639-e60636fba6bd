/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><EMAIL>)
 * Date: 2024/02/06
 * File: batchcreatevm.go
 */

// Package sdk
package sdk

import (
	"context"
	"fmt"
)

const MyDomainDefault = "default"

type Functional struct {
	Async    bool   `json:"async,omitempty" `
	Callback string `json:"callback,omitempty" `
}

type BatchCreateServersReqDisk struct {
	StorageType string `json:"storage_type,omitempty" `
	SizeInGb    int    `json:"size_in_gb,omitempty" `
}

type BatchCreateServersReqTags struct {
	TenantID     string `json:"tenant_id,omitempty" `
	Owner        string `json:"owner,omitempty" `
	Vdc          string `json:"vdc,omitempty" `
	Domain       string `json:"domain,omitempty" `
	BusinessType string `json:"business_type,omitempty" `
	SubnetID     string `json:"subnet_id,omitempty" `
	Remark       string `json:"remark,omitempty" `
	Env          string `json:"env,omitempty" `
}

type BatchCreateServersReqAttributes struct {
	DataDisks          []*BatchCreateServersReqDisk `json:"data_disks,omitempty" `
	OsImage            string                       `json:"os_image,omitempty" `
	HostnameIDentifier string                       `json:"hostname_identifier,omitempty" `
	InstanceType       string                       `json:"instance_type,omitempty" `
	Tags               *BatchCreateServersReqTags   `json:"tags,omitempty" `
	AdminPassword      string                       `json:"admin_password,omitempty"`
}

type BatchCreateServersReqMachine struct {
	Count      int                              `json:"count,omitempty" `
	Attributes *BatchCreateServersReqAttributes `json:"attributes,omitempty" `
	Type       string                           `json:"type,omitempty" `
}

type BatchCreateServersReqBody struct {
	Functional *Functional                     `json:"functional" `
	Machines   []*BatchCreateServersReqMachine `json:"machines" `
}

type BatchCreateServersRequest struct {
	PostBody *BatchCreateServersReqBody
}

type BatchCreateServersRespReject struct {
	Hostname string `json:"hostname,omitempty" `
	Message  string `json:"Message,omitempty" `
	BatchID  int    `json:"batch_id,omitempty" `
	Code     string `json:"Code,omitempty" `
}

type BatchCreateServersRespData struct {
	BatchID  int                             `json:"batch_id,omitempty" `
	Rejected []*BatchCreateServersRespReject `json:"rejected,omitempty" `
	Accepted int                             `json:"accepted,omitempty" `
}

type BatchCreateServersResponse struct {
	Code    string                      `json:"code,omitempty" `
	Data    *BatchCreateServersRespData `json:"data,omitempty" `
	Message string                      `json:"message,omitempty" `
}

// GetCreateMachineURL 拼接申请云主机url
// POST /apis/v1/domains/:my-domain/machines
// 其中 my-domain设置为default
func GetCreateMachineURL(myDomain string) string {
	if myDomain == "" {
		myDomain = MyDomainDefault
	}
	return fmt.Sprintf("/apis/v1/domains/%s/machines", myDomain)
}

func GetCreateMachinePrecheckURL(myDomain string) string {
	if myDomain == "" {
		myDomain = MyDomainDefault
	}
	return fmt.Sprintf("/apis/v1/domains/%s/machines/precheck", myDomain)
}

func (l *licloudSDEVmSdk) BatchCreateServers(ctx context.Context,
	req *BatchCreateServersRequest) (rsp *BatchCreateServersResponse, err error) {
	resp := &BatchCreateServersResponse{}
	if err := l.doRequest(ctx, "BatchCreateServers", nil, "POST",
		GetCreateMachineURL(""), nil, req.PostBody, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

type GetBatchCreateServersStatusRequest struct {
	Dname   string
	BatchID string
}

type GetBatchCreateServersStatusMachine struct {
	Phase        string `json:"phase,omitempty" `
	Parent       string `json:"parent,omitempty" `
	Hostname     string `json:"hostname,omitempty" `
	Ip           string `json:"ip,omitempty" `
	Description  string `json:"description,omitempty" `
	CloudID      string `json:"cloud_id,omitempty" `
	InstanceType string `json:"instance_type,omitempty" `
	Status       string `json:"status,omitempty" `
}

type GetBatchCreateServersStatusResponse struct {
	Code string `json:"code,omitempty" `
	Data *struct {
		BatchID  int                                   `json:"batch_id,omitempty" `
		Machines []*GetBatchCreateServersStatusMachine `json:"machines,omitempty" `
	} `json:"data,omitempty" `
	Message string `json:"message,omitempty" `
}

func (l *licloudSDEVmSdk) GetBatchCreateServersStatus(ctx context.Context,
	req *GetBatchCreateServersStatusRequest) (rsp *GetBatchCreateServersStatusResponse, err error) {
	resp := &GetBatchCreateServersStatusResponse{}
	if err := l.doRequest(ctx, "GetBatchCreateServersStatus", nil, "POST",
		GetCreateMachineStatusURL(req.Dname, req.BatchID), nil, nil, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func (l *licloudSDEVmSdk) BatchCreatePreCheckServers(ctx context.Context, req *BatchCreateServersRequest) (rsp *BatchCreateServersResponse, err error) {
	resp := &BatchCreateServersResponse{}
	if err := l.doRequest(ctx, "BatchCreatePreCheckServers", nil, "POST",
		GetCreateMachinePrecheckURL(""), nil, req.PostBody, resp); err != nil {
		return nil, err
	}
	return resp, nil
}
