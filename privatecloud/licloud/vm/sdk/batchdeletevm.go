/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2024/02/06
 * File: batchdeletevm.go
 */

// Package sdk
package sdk

import "context"

type BatchDeleteServersReqMachine struct {
	IP string `json:"ip" `
}

type BatchDeleteServersReqBody struct {
	Functional *Functional                     `json:"functional" `
	Machines   []*BatchDeleteServersReqMachine `json:"machines"`
}

type BatchDeleteServersRequest struct {
	PostBody *BatchDeleteServersReqBody
}

type BatchDeleteServersResponse struct {
	Code string `json:"code,omitempty" `
	Data *struct {
		BatchID  int `json:"batch_id,omitempty" `
		Rejected []struct {
			Hostname string `json:"hostname,omitempty" `
			Message  string `json:"Message,omitempty" `
			BatchID  int    `json:"batch_id,omitempty" `
			Code     string `json:"Code,omitempty" `
		} `json:"rejected,omitempty" `
		Accepted int `json:"accepted,omitempty" `
	} `json:"data,omitempty" `
	Message string `json:"message,omitempty" `
}

func (l *licloudSDEVmSdk) BatchDeleteServers(ctx context.Context,
	req *BatchDeleteServersRequest) (rsp *BatchDeleteServersResponse, err error) {
	resp := &BatchDeleteServersResponse{}
	if err := l.doRequest(ctx, "BatchDeleteServers", nil, "POST",
		DeleteMachineURL, nil, req.PostBody, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

type GetBatchDeleteServersStatusRequest struct {
	BatchID string
}

type GetBatchDeleteServersStatusResponse struct {
	Code string `json:"code,omitempty" `
	Data struct {
		BatchID  int `json:"batch_id,omitempty" `
		Machines []struct {
			Phase        string `json:"phase,omitempty" `
			Parent       string `json:"parent,omitempty" `
			Hostname     string `json:"hostname,omitempty" `
			Ip           string `json:"ip,omitempty" `
			Description  string `json:"description,omitempty" `
			SupplierSpec string `json:"supplier_spec,omitempty" `
			CloudID      string `json:"cloud_id,omitempty" `
			InstanceType string `json:"instance_type,omitempty" `
			Status       string `json:"status,omitempty" `
		} `json:"machines,omitempty" `
	} `json:"data,omitempty" `
	Message string `json:"message,omitempty" `
}

func (l *licloudSDEVmSdk) GetBatchDeleteServersStatus(ctx context.Context,
	req *GetBatchDeleteServersStatusRequest) (rsp *GetBatchDeleteServersStatusResponse, err error) {
	resp := &GetBatchDeleteServersStatusResponse{}
	if err := l.doRequest(ctx, "GetBatchDeleteServersStatus", nil, "POST",
		GetDeleteMachineStatusURL(req.BatchID), nil, nil, resp); err != nil {
		return nil, err
	}
	return resp, nil
}
