/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2024/02/05
 * File: sdk.go
 */

// Package sdk
package sdk

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/iam/sdk"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

const DefaultServiceName = "licloud_sde_vm"

const DeleteMachineURL = "/apis/v2/machines"

func GetDeleteMachineStatusURL(batchID string) string {
	return fmt.Sprintf("/apis/v2/machineexportbatches/%s/status", batchID)
}

// GetCreateMachineStatusURL 拼接云主机申请状态查询url
// POST /apis/v1/domains/:dname/machineimportbatches/:batchid/status
// :dname 业务域名称，默认值是default
// :batchid 要查询的批次ID，申请机器的时候，如果申请成功，会返回一个唯一的批次ID
func GetCreateMachineStatusURL(dname string, batchID string) string {
	if dname == "" {
		dname = "default"
	}
	return fmt.Sprintf("/apis/v1/domains/%s/machineimportbatches/%s/status", dname, batchID)
}

type licloudSDEVmSdk struct {
	common.OpenApi
}

func NewDefaultLicloudSDEVmSdk() LicloudSDEVMService {
	return newLicloudVMSdk(DefaultServiceName)
}

func newLicloudVMSdk(serviceName string) *licloudSDEVmSdk {
	if serviceName == "" {
		panic("invalid service name")
	}

	s := &licloudSDEVmSdk{
		OpenApi: common.NewOpenApi(serviceName),
	}
	return s
}

func (l *licloudSDEVmSdk) doRequest(ctx context.Context, actionName string, auth *common.Authentication,
	httpMethod, uri string, queries map[string]any, req, rsp any) (err error) {
	liSDEToken, err := sdk.AutoGetSDETokenByIDC(ctx)
	if err != nil {
		return err
	}
	params := &common.OpenApiParams{
		ActionName:      actionName,
		HttpMethod:      httpMethod,
		Uri:             uri,
		Queries:         queries,
		Posts:           req,
		Token:           liSDEToken,
		PrivateCloudEnv: common.PrivateCloudEnvLiXiangSDE,
	}

	// 请求 openapi
	return l.DoRequest(ctx, params, rsp)
}
