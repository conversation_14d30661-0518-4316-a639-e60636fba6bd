/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2024/02/05
 * File: interface.go
 */

// Package sdk
package sdk

import "context"

type LicloudSDEVMService interface {
	BatchCreatePreCheckServers(ctx context.Context, req *BatchCreateServersRequest) (rsp *BatchCreateServersResponse, err error)
	BatchCreateServers(ctx context.Context, req *BatchCreateServersRequest) (rsp *BatchCreateServersResponse, err error)
	GetBatchCreateServersStatus(ctx context.Context, req *GetBatchCreateServersStatusRequest) (rsp *GetBatchCreateServersStatusResponse, err error)
	BatchDeleteServers(ctx context.Context, req *BatchDeleteServersRequest) (rsp *BatchDeleteServersResponse, err error)
	GetBatchDeleteServersStatus(ctx context.Context, req *Get<PERSON>atchDeleteServersStatusRequest) (rsp *GetBatchDeleteServersStatusResponse, err error)
}
