/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2024/02/04
 * File: adaptor.go
 */

// Package adaptor
package adaptor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cast"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/utils/fieldtrans"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/vm/sdk"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type adaptor struct {
	sdk sdk.LicloudSDEVMService
}

func GetLiCloudOpenStackAdaptor() bcc.OpenStackService {
	return &adaptor{
		sdk: sdk.NewDefaultLicloudSDEVmSdk(),
	}
}

// 1 需要mock的方法

// ExchangeId 原封不动返回
func (a *adaptor) ExchangeId(ctx context.Context, req *bcc.ExchangeIdRequest) (rsp *bcc.ExchangeIdResponse, err error) {
	var mappings []*bcc.Mappings
	for _, ins := range req.InstanceIds {
		mappings = append(mappings, &bcc.Mappings{
			Id:   ins,
			Uuid: ins,
		})
	}
	logger.DefaultLogger.Trace(ctx, "mock exchange id,req:%s,mappings:%s", base_utils.Format(req), base_utils.Format(mappings))

	return &bcc.ExchangeIdResponse{
		Mappings: mappings,
	}, nil
}

// 2 resize cds用的

func (a *adaptor) ShowInstanceInfo(ctx context.Context, req *bcc.ShowInstanceRequest) (rsp *bcc.ShowInstanceResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

func (a *adaptor) ResizeInstance(ctx context.Context, req *bcc.ResizeInstanceRequest) (rsp *bcc.ResizeInstanceResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

func (a *adaptor) InstanceAttachedCdsList(ctx context.Context, req *bcc.CdsMountListRequest) (rsp *bcc.CdsMountListResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

func (a *adaptor) ResizeCds(ctx context.Context, req *bcc.CdsResizeRequest) (rsp *bcc.CdsResizeResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

func (a *adaptor) GetCdsStockWithZone(ctx context.Context, req *bcc.GetCdsStockRequest) (rsp *bcc.GetCdsStockResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

// 3 原版也没用的方法

func (a *adaptor) ShowServer(ctx context.Context, req *bcc.ShowServerRequest) (rsp *bcc.ShowServerResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

func (a *adaptor) SubnetDetail(ctx context.Context, req *bcc.GetSubnetDetailRequest) (rsp *bcc.GetSubnetDetailResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

func (a *adaptor) ShowTransaction(ctx context.Context, req *bcc.ShowTransactionRequest) (rsp *bcc.ShowTransactionResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

func (a *adaptor) SetShowTransactionForUt(ctx context.Context, req *bcc.SetTransactionRequest) (rsp *bcc.SetTransactionResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

func (a *adaptor) CreateDeploySet(ctx context.Context, req *bcc.CreateDeploySetRequest) (rsp *bcc.DeploySetIdsResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

func (a *adaptor) ShowDeploySet(ctx context.Context, req *bcc.ShowDeploySetRequest) (rsp *bcc.ShowDeploySetResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

func (a *adaptor) CreateCds(ctx context.Context, req *bcc.CdsCreateRequest) (rsp *bcc.CdsCreateResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

func (a *adaptor) AttachCds(ctx context.Context, req *bcc.CdsAttachRequest) (rsp *bcc.CdsAttachResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

func (a *adaptor) DetachCds(ctx context.Context, req *bcc.CdsDetachRequest) (rsp *bcc.CdsDetachResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

func (a *adaptor) DeleteCds(ctx context.Context, req *bcc.CdsDeleteRequest) (rsp *bcc.CdsDeleteResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

func (a *adaptor) GetCdsDetail(ctx context.Context, req *bcc.GetCdsDetailRequest) (rsp *bcc.GetCdsDetailResponse, err error) {
	return nil, errors.New("li-cloud sde not support this")
}

// 4 真正有用的方法

func (a *adaptor) BatchCreateServers(ctx context.Context, req *bcc.BatchCreateServersRequest) (rsp *bcc.BatchCreateServersResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.OrderOverTime <= 0 {
		logger.SdkLogger.Warning(ctx, "batchcreateserver's order over time:%d must bigger than 0",
			req.OrderOverTime)
		return nil, cerrs.ErrInvalidParams.Errorf("order over time invalid: %d", req.OrderOverTime)
	}

	if req.CreateInstances == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("instances param is null")
	}

	if len(req.CreateInstances) != 1 {
		return nil, cerrs.ErrInvalidParams.Errorf("li cloud sde only support create 1 by 1 just now,pls check suborder config!")
	}
	metaDataStr, err := buildMetaDataStr(req)
	if err != nil {
		return nil, err
	}

	liVpc := &fieldtrans.Vpc{}
	if req.Auth != nil {
		liVpc = fieldtrans.GetVpcFromString(req.Auth.IamUserId)
	}
	liMachines, err := BceCreateInstancesToLiMachines(ctx, req.CreateInstances, liVpc)
	if err != nil {
		logger.DefaultLogger.Warning(ctx, "transform bce ins to li ins fail:%s", err.Error())
		return nil, err
	}

	liReq := sdk.BatchCreateServersRequest{
		PostBody: &sdk.BatchCreateServersReqBody{
			Functional: &sdk.Functional{
				Async: false, // 不要回调
			},
			Machines: liMachines,
		},
	}
	logger.DefaultLogger.Trace(ctx, "start to call li_cloud sde BatchCreateServers api,req:%s", base_utils.Format(liReq))

	liPrecheckResp, err := a.sdk.BatchCreatePreCheckServers(ctx, &liReq)
	if err != nil {
		logger.DefaultLogger.Warning(ctx, "call li_cloud sde BatchCreateServers Precheck api fail,err:%s", err.Error())
		return nil, err
	}

	if err := a.liBatchCreatePrecheckRespCheck(liPrecheckResp); err != nil {
		logger.DefaultLogger.Warning(ctx, "li_cloud sde BatchCreateServers Precheck api result check fail,err:%s", err.Error())
		return nil, err
	}

	liResp, err := a.sdk.BatchCreateServers(ctx, &liReq)
	if err != nil {
		logger.DefaultLogger.Warning(ctx, "call li_cloud sde BatchCreateServers api fail,err:%s", err.Error())
		return nil, err
	}

	if liResp.Data == nil {
		return nil, errors.New("liret data is nil")
	}

	if err := a.liBatchCreateRespCheck(len(liMachines), liResp); err != nil {
		logger.DefaultLogger.Warning(ctx, "li_cloud sde BatchCreateServers api result check fail,err:%s", err.Error())
		return nil, err
	}

	logger.DefaultLogger.Trace(ctx, "call li_cloud sde BatchCreateServers api success,resp:%s", base_utils.Format(liResp))

	if err := SaveBatchInfo(ctx, liReq.PostBody, liResp.Data, metaDataStr); err != nil {
		logger.DefaultLogger.Warning(ctx, "save batch info fail:%s", err.Error())
		return nil, err
	}

	return &bcc.BatchCreateServersResponse{
		Message:     liResp.Message,
		Code:        liResp.Code,
		Requestid:   base_utils.GetReqID(ctx),
		OrderId:     cast.ToString(liResp.Data.BatchID),
		InstanceIds: nil,
	}, nil
}

func (a *adaptor) BatchDeleteServers(ctx context.Context, req *bcc.BatchDeleteServersRequest) (rsp *bcc.BatchDeleteServersResponse, err error) {
	var liMachines []*sdk.BatchDeleteServersReqMachine

	for _, id := range req.InstanceIds {
		liIP, err := IDToIP(ctx, id)
		if err != nil {
			logger.DefaultLogger.Warning(ctx, "transform bce longid to li ip fail:%s", err.Error())
			return nil, err
		}
		liMachines = append(liMachines, &sdk.BatchDeleteServersReqMachine{IP: liIP})
	}

	liReq := sdk.BatchDeleteServersRequest{PostBody: &sdk.BatchDeleteServersReqBody{
		Functional: &sdk.Functional{
			Async: false, // 不要回调
		},
		Machines: liMachines,
	}}
	logger.DefaultLogger.Trace(ctx, "start to call li_cloud BatchDeleteServers sde api,req:%s", base_utils.Format(liReq))
	liResp, err := a.sdk.BatchDeleteServers(ctx, &liReq)
	if err != nil {
		logger.DefaultLogger.Warning(ctx, "call li_cloud sde BatchDeleteServers api fail,err:%s", err.Error())
		return nil, err
	}
	if err := a.liBatchDelRespCheck(len(liMachines), liResp); err != nil {
		logger.DefaultLogger.Warning(ctx, "li_cloud sde BatchDeleteServers api result check fail,err:%s", err.Error())
		return nil, err
	}

	logger.DefaultLogger.Trace(ctx, "call li_cloud sde BatchDeleteServers api success,resp:%s", base_utils.Format(liResp))
	if liResp.Data == nil {
		return nil, errors.New("liret data is nil")
	}
	return &bcc.BatchDeleteServersResponse{
		Message:   liResp.Message,
		Code:      liResp.Code,
		Requestid: base_utils.GetReqID(ctx),
		Result:    cast.ToInt32(len(liMachines) - len(liResp.Data.Rejected)),
	}, nil
}

func (a *adaptor) ShowOrder(ctx context.Context, req *bcc.ShowOrderRequest) (rsp *bcc.ShowOrderResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	liBatchID := req.OrderId

	liReq := sdk.GetBatchCreateServersStatusRequest{
		Dname:   "",
		BatchID: liBatchID,
	}
	logger.DefaultLogger.Trace(ctx, "start to call li_cloud ShowOrder sde api,req:%s", base_utils.Format(liReq))
	liResp, err := a.sdk.GetBatchCreateServersStatus(ctx, &liReq)
	if err != nil {
		logger.DefaultLogger.Warning(ctx, "call li_cloud sde ShowOrder api fail,err:%s", err.Error())
		return nil, err
	}
	logger.DefaultLogger.Trace(ctx, "call li_cloud sde ShowOrder api success,resp:%s", base_utils.Format(liResp))

	var resp = &bcc.ShowOrderResponse{
		Message:   liResp.Message,
		Code:      liResp.Code,
		Requestid: base_utils.GetReqID(ctx),
		OrderId:   liBatchID,
	}
	// 一般是失败
	if liResp.Data == nil {
		resp.Status = "error"
		resp.ErrMsg = liResp.Message
		return resp, nil
	}
	// 所有实例都失败失败,直接返回，这段后面全都是理想接口返回success的
	if strings.ToLower(liResp.Code) != "success" {
		resp.ErrMsg = liResp.Message
		return resp, nil
	}
	// 分类放
	var notdoneIns []*sdk.GetBatchCreateServersStatusMachine // 仍在申请的
	var successIns []*sdk.GetBatchCreateServersStatusMachine // 完成且成功的
	var errIns []*sdk.GetBatchCreateServersStatusMachine     // 完成且错误的
	var unKnowIns []*sdk.GetBatchCreateServersStatusMachine  // 未知状态的
	for _, ins := range liResp.Data.Machines {
		if ins.Phase != "finished" {
			notdoneIns = append(notdoneIns, ins)
		} else {
			if ins.Status == "done" {
				successIns = append(successIns, ins)
			} else if ins.Status == "failed" {
				errIns = append(errIns, ins)
			} else {
				unKnowIns = append(unKnowIns, ins)
			}
		}
	}
	logger.DefaultLogger.Trace(ctx, "group li sde ordershow,not done[%s]:%s,success[%d]:%s,err[%d]:%s,unknown[%d]:%s",
		len(notdoneIns), base_utils.Format(notdoneIns),
		len(successIns), base_utils.Format(successIns),
		len(errIns), base_utils.Format(errIns),
		len(unKnowIns), base_utils.Format(unKnowIns))

	if len(notdoneIns) > 0 {
		resp.Status = "operating"
	} else if len(errIns) > 0 || len(unKnowIns) > 0 {
		resp.Status = "error"
	} else if len(notdoneIns) == 0 && len(errIns) == 0 && len(unKnowIns) == 0 && len(successIns) > 0 {
		resp.Status = "succ"
	}

	var bceBccIns []*bcc.Instances
	if resp.Status == "succ" {
		batchInfo, err := GetBatchInfoByBatchID(ctx, liResp.Data.BatchID)
		if err != nil {
			logger.DefaultLogger.Warning(ctx, "get batchinfo by batch id fail:%s", err.Error())
			return nil, fmt.Errorf("query batch info fail,batchid:%d,err:%w", liResp.Data.BatchID, err)
		}

		adminPass := batchInfo.RootPwd
		metaDataStr := batchInfo.MetaInfo

		for _, ins := range successIns {
			metaStr, err := getInsMetaDataStr(ctx, metaDataStr, ins)
			if err != nil {
				logger.DefaultLogger.Warning(ctx, "get metastr fail:%s", err.Error())
				return nil, err
			}

			bceBccIns = append(bceBccIns, &bcc.Instances{
				Name:       ins.Hostname,
				AdminPass:  adminPass,
				InstanceId: ins.Hostname,
				FloatingIp: ins.Ip,
				InternalIp: ins.Ip, // fix ip
				Flavor:     ins.InstanceType,
				CreateTime: base_utils.FormatWithCST(time.Now()),
				MetaData:   metaStr,
			})
		}
		logger.DefaultLogger.Trace(ctx, "transform to bce ins :%s", base_utils.Format(bceBccIns))
		if err := SaveVmInfo(ctx, successIns, liResp.Data.BatchID, adminPass); err != nil {
			return nil, err
		}
	}
	resp.Instances = bceBccIns
	return resp, nil
}

func (a *adaptor) liBatchCreatePrecheckRespCheck(resp *sdk.BatchCreateServersResponse) error {
	if resp.Code != "" && strings.ToLower(resp.Code) != "success" {
		return fmt.Errorf("code:%s,msg:%s", resp.Code, resp.Message)
	}
	if strings.ToLower(resp.Message) != "success" {
		return fmt.Errorf("code:%s,msg:%s", resp.Code, resp.Message)
	}
	return nil
}

func (a *adaptor) liBatchCreateRespCheck(wantNum int, resp *sdk.BatchCreateServersResponse) error {
	if resp.Code != "" && strings.ToLower(resp.Code) != "success" {
		return fmt.Errorf("code:%s,msg:%s", resp.Code, resp.Message)
	}
	if strings.ToLower(resp.Message) != "success" {
		return fmt.Errorf("code:%s,msg:%s", resp.Code, resp.Message)
	}
	if resp.Data == nil {
		return errors.New("resp data is nil")
	}
	if len(resp.Data.Rejected) != 0 {
		return fmt.Errorf("some vm rejected:%s", base_utils.Format(resp.Data.Rejected))
	}
	if resp.Data.Accepted != wantNum {
		return fmt.Errorf("not all accept,want:%d,accept:%d", wantNum, resp.Data.Accepted)
	}

	return nil
}

func (a *adaptor) liBatchDelRespCheck(wantNum int, resp *sdk.BatchDeleteServersResponse) error {
	if resp.Code != "" && strings.ToLower(resp.Code) != "success" {
		return fmt.Errorf("code:%s,msg:%s", resp.Code, resp.Message)
	}
	if strings.ToLower(resp.Message) != "success" {
		return fmt.Errorf("code:%s,msg:%s", resp.Code, resp.Message)
	}
	if resp.Data == nil {
		return errors.New("resp data is nil")
	}
	if len(resp.Data.Rejected) != 0 {
		return fmt.Errorf("some vm rejected:%s", base_utils.Format(resp.Data.Rejected))
	}
	if resp.Data.Accepted != wantNum {
		return fmt.Errorf("not all accept,want:%d,accept:%d", wantNum, resp.Data.Accepted)
	}

	return nil
}

// buildMetaDataStr 构造metadata
// todo 注意，因为当前理想SDE接口限制，一批只建1台机器，所以可以这么无脑存，
// todo 如果后续每个batch可以超过1台，这里需要大改，因为公有云原有逻辑中，
// todo metaData无法与理想sde的接口返回映射， 即，无法知道这个batch中的metadata与实际开出机器的对应关系
func buildMetaDataStr(req *bcc.BatchCreateServersRequest) (string, error) {
	if len(req.CreateInstances) != 1 {
		return "", cerrs.ErrInvalidParams.Errorf("li cloud sde only support create 1 by 1 just now,pls check suborder config!")
	}
	bccInsConfs := req.CreateInstances[0].Configs
	bBccConfs, err := json.Marshal(bccInsConfs)
	if err != nil {
		return "", cerrs.ErrInvalidParams.Errorf("marshal bcc ins conf to bytes fail,err:%s", err.Error())
	}
	return string(bBccConfs), nil
}

// getInsMetaDataStr 从数据库里记录的订单信息解析出这台机器的metastr
// todo 注意，因为当前理想SDE接口限制，一批只建1台机器，所以可以这么无脑存，
// todo 如果后续每个batch可以超过1台，这里需要大改，因为公有云原有逻辑中，
// todo metaData无法与理想sde的接口返回映射， 即，无法知道这个batch中的metadata与实际开出机器的对应关系
func getInsMetaDataStr(ctx context.Context, metaDataStr string, ins *sdk.GetBatchCreateServersStatusMachine) (string, error) {
	var confList []string
	var confs []*bcc.Configs
	if err := json.Unmarshal([]byte(metaDataStr), &confs); err != nil {
		return "", nil
	}
	logger.DefaultLogger.Trace(ctx, "unmarshal metadata success,ret:%s", base_utils.Format(confs))
	for _, conf := range confs {
		confList = append(confList, fmt.Sprintf("%s=%s", conf.Key, conf.ConfigValue))
	}
	// example: "{entity_ids=node-01__node-02, instance_type=3}"
	retMetaData := strings.Join(confList, ",")
	return fmt.Sprintf("{%s}", retMetaData), nil
}
