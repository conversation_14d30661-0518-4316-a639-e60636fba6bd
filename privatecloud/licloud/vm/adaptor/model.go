/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2024/02/07
 * File: model.go
 */

// Package adaptor
package adaptor

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/vm/sdk"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func SaveBatchInfo(ctx context.Context, lireq *sdk.BatchCreateServersReqBody, liresp *sdk.BatchCreateServersRespData,
	metaInfo string) error {
	batchInfo, err := GetBatchInfoByBatchID(ctx, liresp.BatchID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if err == nil && batchInfo != nil {
		logger.ComponentLogger.Trace(ctx, "batch info already exist,batchid:%d", liresp.BatchID)
		return nil
	}

	liModel, err := model.GetDbAgent(ctx)
	if err != nil {
		return fmt.Errorf("get db agent fail:%w", err)
	}

	bM, err := json.Marshal(lireq.Machines)
	if err != nil {
		return fmt.Errorf("marshal lireq machines fail:%w", err)
	}

	if err := liModel.FullSaveAssociationsSave(ctx, &model.VmCreateBatchInfo{
		BatchID:      liresp.BatchID,
		CreateParams: string(bM),
		MetaInfo:     metaInfo,
		CreateTime:   time.Now(),
		RootPwd:      lireq.Machines[0].Attributes.AdminPassword,
	}); err != nil {
		return fmt.Errorf("save batch info fail:%w", err)
	}

	return nil
}

func GetBatchInfoByBatchID(ctx context.Context, batchID int) (*model.VmCreateBatchInfo, error) {
	liModel, err := model.GetDbAgent(ctx)
	if err != nil {
		return nil, fmt.Errorf("get db agent fail:%w", err)
	}
	var ret *model.VmCreateBatchInfo
	if err := liModel.GetOneByCond(ctx, &ret, "batch_id = ?", batchID); err != nil {
		return nil, err
	}
	return ret, nil
}

func SaveVmInfo(ctx context.Context, successIns []*sdk.GetBatchCreateServersStatusMachine, batchID int, adminPass string) error {
	var vm []*model.VmInfo
	for _, ins := range successIns {
		_, err := GetVmInfoByHostname(ctx, ins.Hostname)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			vm = append(vm, &model.VmInfo{
				VmID:         fmt.Sprintf("%d-%s", batchID, ins.Hostname),
				Hostname:     ins.Hostname,
				BatchID:      batchID,
				Ip:           ins.Ip,
				CreateTime:   time.Now(),
				RootPwd:      adminPass,
				InstanceType: ins.InstanceType,
			})
		}
	}
	liModel, err := model.GetDbAgent(ctx)
	if err != nil {
		return fmt.Errorf("get db agent fail:%w", err)
	}
	if len(vm) == 0 {
		return nil
	}

	if err := liModel.FullSaveAssociationsSave(ctx, vm); err != nil {
		logger.DefaultLogger.Warning(ctx, "save vm info :%s fail,err:%s", base_utils.Format(vm), err.Error())
		return fmt.Errorf("save vm info fail:%w", err)
	}
	return nil
}

func GetVmInfoByHostname(ctx context.Context, hostname string) (*model.VmInfo, error) {
	liModel, err := model.GetDbAgent(ctx)
	if err != nil {
		return nil, fmt.Errorf("get db agent fail:%w", err)
	}
	var ret *model.VmInfo
	if err := liModel.GetOneByCond(ctx, &ret, "hostname = ?", hostname); err != nil {
		return nil, err
	}
	return ret, nil
}
