/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><EMAIL>)
 * Date: 2024/02/07
 * File: transform.go
 */

// Package adaptor
package adaptor

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/conf"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/utils/fieldtrans"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/vm/sdk"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type GetLiSpecParams struct {
	BccIns *bcc.CreateInstances
	LiVpc  *fieldtrans.Vpc
}

func GetSpec(params *GetLiSpecParams) (*sdk.BatchCreateServersReqMachine, error) {
	if params.LiVpc == nil {
		params.LiVpc = &fieldtrans.Vpc{}
	}
	// 除了prod，都用test的配置
	liEnvConf := conf.GetLiEnvConf(params.LiVpc.Env)
	// 用请求传进来的vdc
	vdc := liEnvConf.DefaultVdc
	if params.LiVpc.Vdc != "" {
		vdc = params.LiVpc.Vdc
	}
	// 用请求传进来的tenantID
	tenantID := liEnvConf.DefaultTenantID
	if len(params.LiVpc.TanentID) != 0 {
		tenantID = params.LiVpc.TanentID
	}
	liSpecMap := make(map[int]*conf.LiSpec)
	for _, spec := range conf.GetLiConf().LiSpecs {
		liSpecMap[spec.MemGB] = spec
	}
	// 从比需要的容量大的里面，找出最小的
	var targetSpec *conf.LiSpec
	for memGB, spec := range liSpecMap {
		if memGB >= int(params.BccIns.MemoryCapacityInGB) {
			if targetSpec == nil {
				targetSpec = spec
			} else {
				if spec.MemGB < targetSpec.MemGB {
					targetSpec = spec
				}
			}
		}
	}
	if targetSpec == nil {
		return nil, fmt.Errorf("%d GB out of max instance type", params.BccIns.MemoryCapacityInGB)
	}

	return &sdk.BatchCreateServersReqMachine{
		Count: 1,
		Attributes: &sdk.BatchCreateServersReqAttributes{
			OsImage:            "redis-images",
			HostnameIDentifier: "redis-manager",
			InstanceType:       targetSpec.InstanceType,
			Tags: &sdk.BatchCreateServersReqTags{
				Domain:       "default",
				BusinessType: "store_master",
				Remark:       "baidu_redis_test",
				TenantID:     tenantID,
				Owner:        liEnvConf.DefaultOwner,
				Vdc:          vdc,
				SubnetID:     liEnvConf.DefaultSubnetID,
				Env:          liEnvConf.DefaultEnv,
			},
		},
		Type: "vm",
	}, nil
}

// BceCreateInstancesToLiMachines 基于bce创建bcc的请求信息，合成理想创建请求里要求的机器信息
// 目前因为理想SDE限制，只支持1台机器
func BceCreateInstancesToLiMachines(ctx context.Context, bceIns []*bcc.CreateInstances, liVpc *fieldtrans.Vpc) ([]*sdk.BatchCreateServersReqMachine, error) {
	if len(bceIns) != 1 {
		return nil, cerrs.ErrInvalidParams.Errorf("li cloud sde only support create 1 by 1 just now,pls check suborder config!")
	}
	var ret []*sdk.BatchCreateServersReqMachine
	randStr, err := base_utils.GenerateRandomString(8)
	if err != nil {
		return nil, err
	}
	for _, ins := range bceIns {
		liSpec, err := GetSpec(&GetLiSpecParams{
			BccIns: ins,
			LiVpc:  liVpc,
		})
		if err != nil {
			return nil, fmt.Errorf("get spec fail:%w", err)
		}
		liSpec.Attributes.AdminPassword = randStr
		ret = append(ret, liSpec)
	}

	return ret, nil
}

// IDToIP 把bce的long id转换成理想节点的ip，
func IDToIP(ctx context.Context, longID string) (string, error) {
	vmInfo, err := GetVmInfoByHostname(ctx, longID)
	if err != nil {
		return "", fmt.Errorf("get vm info fail,hostname:%s,err:%w", longID, err)
	}
	return vmInfo.Ip, nil
}

func GetRootPwdByBatchID(ctx context.Context, batchID int) (string, error) {
	batchInfo, err := GetBatchInfoByBatchID(ctx, batchID)
	if err != nil {
		return "", fmt.Errorf("query batch info fail,batchid:%d,err:%w", batchID, err)
	}
	return batchInfo.RootPwd, nil
}
