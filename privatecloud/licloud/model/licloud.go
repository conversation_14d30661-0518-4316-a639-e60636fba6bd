/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2024/02/07
 * File: licloud.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package model TODO package function desc
package model

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/model"
)

type Conf struct {
	DbLogger     logit.Logger // gdp db logger
	GormLogger   logit.Logger // gorm 操作logger
	ServicerName string       // 自定义x1 mysql servicer name，传空字符串走默认  "db_x1"
}

var (
	resourceInst *model.Resource = nil
)

func Init(ctx context.Context, conf Conf) {
	servicerName := "private-ext-licloud"
	if conf.ServicerName != "" {
		servicerName = conf.ServicerName
	}

	resourcePtr, err := model.InitModel(ctx, model.ResourceCfg{
		ServicerName: servicerName,
		DbLogger:     conf.DbLogger,
		GormLogger:   conf.GormLogger,
		PreloadConf:  map[string][]string{},
		AutoPreload:  true,
	})
	if err != nil {
		panic("init private-ext-licloud model fail")
	}
	resourceInst = resourcePtr
}

func HasInited() bool {
	return resourceInst != nil
}

func GetDbAgent(ctx context.Context) (*model.Resource, error) {
	if !HasInited() {
		return nil, errors.Errorf("li-cloud model has not init")
	}
	return resourceInst, nil
}
