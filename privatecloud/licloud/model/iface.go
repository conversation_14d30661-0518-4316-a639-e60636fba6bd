/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2024/02/07
 * File: iface.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package model TODO package function desc
package model

import "time"

type VmCreateBatchInfo struct {
	ID           int       `json:"id" gorm:"column:id"`
	BatchID      int       `json:"batch_id" gorm:"column:batch_id"`
	CreateParams string    `json:"create_params" gorm:"column:create_params"`
	CreateTime   time.Time `json:"create_time" gorm:"column:create_time"`
	FinishTime   time.Time `json:"finish_time" gorm:"column:finish_time"`
	MetaInfo     string    `json:"meta_info" gorm:"column:meta_info"`
	RootPwd      string    `json:"root_pwd" gorm:"column:root_pwd"`
}

func (m *VmCreateBatchInfo) TableName() string {
	return "vm_create_batch_info"
}

type VmInfo struct {
	ID           int       `json:"id" gorm:"column:id"`
	VmID         string    `json:"vm_id" gorm:"column:vm_id"`
	Hostname     string    `json:"hostname" gorm:"column:hostname"`
	BatchID      int       `json:"batch_id" gorm:"column:batch_id"`
	Ip           string    `json:"ip" gorm:"column:ip"`
	CreateTime   time.Time `json:"create_time" gorm:"column:create_time"`
	DeleteTime   time.Time `json:"delete_time" gorm:"column:delete_time"`
	RootPwd      string    `json:"root_pwd" gorm:"column:root_pwd"`
	InstanceType string    `json:"instance_type" gorm:"column:instance_type"`
}

func (m *VmInfo) TableName() string {
	return "vm_info"
}
