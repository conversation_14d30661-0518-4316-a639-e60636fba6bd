package fieldtrans

import (
	"testing"
)

func TestVpcToString(t *testing.T) {
	vpc := &Vpc{Env: "prod", Vdc: "us-west-2"}
	expected := "prod#us-west-2"
	if vpc.ToString() != expected {
		t.<PERSON>rf("Expected %s, got %s", expected, vpc.ToString())
	}
}

func TestFromStringWithValidInput(t *testing.T) {
	vpc := GetVpcFromString("prod,us-west-2,utest,vdctest")
	if vpc.TanentID != "prod" || vpc.Env != "us-west-2" || vpc.Unit != "utest" || vpc.Vdc != "vdctest" {
		t.<PERSON>rf("Expected prod and us-west-2, got %s and %s", vpc.Env, vpc.Vdc)
	}
}

func TestFromStringWithEmptyInput(t *testing.T) {
	vpc := GetVpcFromString("")
	if vpc.Env != "" || vpc.Vdc != "" {
		t.Errorf("Expected empty strings, got %s and %s", vpc.Env, vpc.Vdc)
	}
}

func TestFromStringWithSingleValue(t *testing.T) {
	vpc := GetVpcFromString("prod")
	if vpc.Env != "" || vpc.Vdc != "prod" {
		t.Errorf("Expected empty string and prod, got %s and %s", vpc.Env, vpc.Vdc)
	}
}
