package fieldtrans

import "strings"

type Vpc struct {
	TanentID string `json:"tanentID"`
	Env      string `json:"env"`
	Unit     string `json:"unit"`
	Vdc      string `json:"vdc"`
}

func (vpc *Vpc) ToString() string {
	return vpc.Env + "#" + vpc.Vdc
}

func GetVpcFromString(s string) *Vpc {
	vpc := &Vpc{}
	if s == "" {
		return vpc
	}
	sp := strings.Split(s, ",")
	if len(sp) < 2 {
		vpc.Vdc = s
		return vpc
	}
	if len(sp) == 4 {
		vpc.TanentID = sp[0]
		vpc.Env = sp[1]
		vpc.Unit = sp[2]
		vpc.Vdc = sp[3]
	}
	return vpc
}
