/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2024/02/05
 * File: const.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package conf TODO package function desc
package conf

type ConstStruItem struct {
	AppName       string
	AppID         string
	AppSecret     string
	SDEEndpoint   string
	IDaaSEndpoint string
	SDEServiceID  string
}

type ConstStru struct {
	Prod   *ConstStruItem
	Ontest *ConstStruItem
}

var Consts = &ConstStru{
	Ontest: &ConstStruItem{
		AppName:       "BceRedis",
		AppID:         "5Wl4gBQ7iY13D4X9Fm3XE4",
		AppSecret:     "eyJrdHkiOiJvY3QiLCJraWQiOiJjcGFxUEQxei1RIiwiYWxnIjoiSFMyNTYiLCJrIjoiZmVxU2Njc0NVM0EzNHB6OE15YlNNSjZwQmFSX0dQVFhaN1oxS0NId3FhOCJ9",
		SDEEndpoint:   "http://devops-sde-valet-api.ontest.k8s.chj.cloud",
		IDaaSEndpoint: "https://id-ontest.lixiang.com/api",
		SDEServiceID:  "6yM0cmVN8ZuFf6iFzTxLBX",
	},
	Prod: &ConstStruItem{
		AppName:       "BceRedis",
		AppID:         "4QmCQLC4dvvrcPYftPJeKC",
		AppSecret:     "eyJrdHkiOiJvY3QiLCJraWQiOiJOcGRnVU5oLXF3IiwiYWxnIjoiSFMyNTYiLCJrIjoiVFJLTmMxc0dlT1lpR3NIT0Q5Wkw4VUR2bHFNbXlyUjNMUHdLd1lzZ2lWMCJ9",
		SDEEndpoint:   "http://devops-sde-valet-api.prod-devops.k8s.chj.cloud",
		IDaaSEndpoint: "https://id.lixiang.com/api",
		SDEServiceID:  "4DatuxbuejaHcEWUAVARVc",
	},
}
