/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2024/02/05
 * File: sdk.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package sdk TODO package function desc
package sdk

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/scs/thirdparty-private-sdks/chehejia/li-id/idaas/iam-idaas-go-sdk/lib/secret"
	"icode.baidu.com/baidu/scs/thirdparty-private-sdks/chehejia/li-id/idaas/iam-idaas-go-sdk/lib/service"
	idaasUtils "icode.baidu.com/baidu/scs/thirdparty-private-sdks/chehejia/li-id/idaas/iam-idaas-go-sdk/utils"

	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/utils/conf"
)

const EnvTest = "licloudontest"
const EnvOnline = "licloudprod"

// 因为在百度云环境init这些玩意会引入一些奇怪的case，比如InitJSON内部修改了全局Jsoniter的默认行为，所以把他们限制到只有车和家环境init
func init() {
	if env.IDC() == EnvTest || env.IDC() == EnvOnline {
		idaasUtils.InitJSON()
		idaasUtils.InitClient()
		idaasUtils.InitLog()
		idaasUtils.InitMetric()
	}
}

func AutoGetSDETokenByIDC(ctx context.Context) (token string, err error) {
	if env.IDC() == EnvTest {
		// todo 现在sde线上测试环境也是用的prod的toekn
		return getSDEToken(ctx, EnvOnline)
	} else if env.IDC() == EnvOnline {
		return getSDEToken(ctx, EnvOnline)
	}

	return "", fmt.Errorf("only support %s and %s idc", EnvTest, EnvOnline)
}

func getSDEToken(ctx context.Context, env string) (token string, err error) {
	appSecret := conf.Consts.Ontest.AppSecret
	appID := conf.Consts.Ontest.AppID
	idaasDomain := conf.Consts.Prod.IDaaSEndpoint
	serviceID := conf.Consts.Prod.SDEServiceID
	if env == EnvOnline {
		appSecret = conf.Consts.Prod.AppSecret
		appID = conf.Consts.Prod.AppID
	} else {
		env = EnvTest
	}
	logger.DefaultLogger.Trace(ctx, "env:%s,start to get li-id sde token,appid:%s,secret:%s,idaas domain:%s,sde service id:%s",
		env, appID, appSecret, idaasDomain, serviceID)

	manager := service.InitAppTokenManager(secret.Single(func(clientID string) string {
		return appSecret
	}), func() string {
		return idaasDomain
	})
	token, err = manager.PrettyGetToken(appID, serviceID, "read", "all")
	if err != nil {
		// 获取凭证失败
		logger.DefaultLogger.Warning(ctx, "获取凭证失败:%s", err.Error())
		return "", fmt.Errorf("获取凭证失败:%s", err.Error())
	}
	logger.DefaultLogger.Trace(ctx, "get li-id sde token success,token:%s", token)
	return token, nil
}
