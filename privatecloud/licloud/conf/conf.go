/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><EMAIL>)
 * Date: 2024/02/24
 * File: conf.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package conf TODO package function desc
package conf

import (
	"context"
	"fmt"
	"sync"

	"icode.baidu.com/baidu/gdp/env"

	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const ServicerName = "licloud_sde_vm"
const ExtraConfName = "LiCloudVmConfig"

type LiEnvConf struct {
	DefaultOwner        string `toml:"DefaultOwner"`
	DefaultTenantID     string `toml:"DefaultTenantID"`
	DefaultVdc          string `toml:"<PERSON><PERSON>ultVdc"`
	DefaultEnv          string `toml:"DefaultEnv"`
	DefaultSubnetID     string `toml:"DefaultSubnetID"`
	DefaultInstanceType string `toml:"DefaultInstanceType"`
}

type LiConf struct {
	Prod    *LiEnvConf `toml:"Prod"`
	Test    *LiEnvConf `toml:"Test"`
	LiSpecs []*LiSpec  `toml:"LiSpecs"`
}

type LiSpec struct {
	InstanceType string `toml:"InstanceType"`
	Core         int    `toml:"Core"`
	MemGB        int    `toml:"MemGB"`
}

var once = &sync.Once{}
var liconf = &LiConf{}

// GetLiConf 加载配置
// todo 加载配置
func GetLiConf() *LiConf {
	once.Do(func() {
		liconf.mustLoad(ServicerName)
		logger.DefaultLogger.Trace(context.Background(), "load li conf success:%s", base_utils.Format(liconf))
	})
	return liconf
}

func GetLiEnvConf(liEnv string) *LiEnvConf {
	cnf := GetLiConf()
	// 显式传prod，返回prod配置
	if liEnv == "prod" {
		return cnf.Prod
	}
	// 没传，则只有idc是prod时候返回prod
	if liEnv == "" && env.IDC() == "licloudprod" {
		return cnf.Prod
	}
	return cnf.Test
}

func (conf *LiConf) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[ExtraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", ExtraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.Error()))
	}
}
