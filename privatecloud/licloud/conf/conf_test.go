/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2024/02/27
 * File: conf_test.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package conf TODO package function desc
package conf

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

func init() {
	unittest.UnitTestInit(3)
}

func TestGetLiConf(t *testing.T) {
	fmt.Println(base_utils.Format(GetLiConf()))
}
