package privatecloud

import (
	"icode.baidu.com/baidu/gdp/env"
	"testing"
)

func TestIsPrivateENV(t *testing.T) {
	tests := []struct {
		name string
		want bool
	}{
		{
			name: "licloudontest",
			want: true,
		},
		{
			name: "licloudprod",
			want: true,
		},
		{
			name: "dbstacktest",
			want: true,
		},
		{
			name: "dbstack",
			want: true,
		},
		{
			name: "test",
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env.Default = env.New(env.Option{
				IDC: tt.name,
			})
			if got := IsPrivateENV(); got != tt.want {
				t.Errorf("IsPrivateENV() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetPrivateEnvType(t *testing.T) {
	tests := []struct {
		name string
		want string
	}{
		{
			name: "licloudontest",
			want: "licloudontest",
		},
		{
			name: "licloudprod",
			want: "licloudprod",
		},
		{
			name: "dbstacktest",
			want: "dbstack",
		},
		{
			name: "dbstack",
			want: "dbstack",
		},
		{
			"test",
			"",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env.Default = env.New(env.Option{
				IDC: tt.name,
			})
			if got := GetPrivateEnvType(); got != tt.want {
				t.Errorf("GetPrivateEnvType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetPrivateOpenStackSDK(t *testing.T) {
	env.Default = env.New(env.Option{
		IDC: "dbstacktest",
	})
	_ = GetPrivateOpenStackSDK()
	_ = GetPrivateStsSDK()
	env.Default = env.New(env.Option{
		IDC: "licloudontest",
	})
	_ = GetPrivateOpenStackSDK()
	_ = GetPrivateStsSDK()
}
