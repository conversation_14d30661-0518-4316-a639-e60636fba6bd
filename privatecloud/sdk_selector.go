/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2024/02/08
 * File: selector.go
 */

/*
 * DESCRIPTION
 *   用于返回指定私有化场景的sdk、component
 */

// Package privatecloud 开发对接私有化场景的适配组件
package privatecloud

import (
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/env"

	dbStackVmAdaptor "icode.baidu.com/baidu/scs/x1-base/privatecloud/dbstack/resource/adaptor"
	dbStackStsAdaptor "icode.baidu.com/baidu/scs/x1-base/privatecloud/dbstack/sts/adaptor"
	liStsAdaptor "icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/sts/adaptor"
	liVmAdaptor "icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/vm/adaptor"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
)

const EnvLiTest = "licloudontest"
const EnvLiProd = "licloudprod"
const DBStackPrefix = "dbstack"
const DBStackAdaptorBCC = "dbstackadaptorbcc"

// 当前已经支持的私有化 env.IDC(),除此以外都按非法处理
var privateEnvs = []string{EnvLiTest, EnvLiProd}
var privateEnvPrefixes = []string{DBStackPrefix}

func IsPrivateENV() bool {
	for _, pE := range privateEnvs {
		if env.IDC() == pE {
			return true
		}
	}
	for _, privateEnvPrefix := range privateEnvPrefixes {
		if strings.HasPrefix(env.IDC(), privateEnvPrefix) {
			return true
		}
	}
	return false
}

func IsDBStackAdaptorBCCENV() bool {
	if env.IDC() == DBStackAdaptorBCC {
		return true
	}

	return false
}

func GetPrivateEnvType() string {
	for _, pE := range privateEnvs {
		if env.IDC() == pE {
			return pE
		}
	}
	for _, privateEnvPrefix := range privateEnvPrefixes {
		if strings.HasPrefix(env.IDC(), privateEnvPrefix) {
			return privateEnvPrefix
		}
	}
	return ""
}

func GetPrivateOpenStackSDK() bcc.OpenStackService {
	switch GetPrivateEnvType() {
	case EnvLiTest, EnvLiProd:
		return liVmAdaptor.GetLiCloudOpenStackAdaptor()
	case DBStackPrefix:
		return dbStackVmAdaptor.GetDbstackOpenstackAdaptor()
	default:
		panic(fmt.Sprintf("not support this idc:%s", env.IDC()))
	}
}

func GetPrivateStsSDK() sts.StsService {
	switch GetPrivateEnvType() {
	case EnvLiTest, EnvLiProd:
		return liStsAdaptor.NewSTS()
	case DBStackPrefix:
		return dbStackStsAdaptor.NewSTS()
	default:
		panic(fmt.Sprintf("not support this idc:%s", env.IDC()))
	}
}
