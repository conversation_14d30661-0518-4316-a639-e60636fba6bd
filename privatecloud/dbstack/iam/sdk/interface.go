package sdk

import "context"

const (
	CheckUrl = "/v1/sso/check"
)

type CheckRequest struct {
	Token string `json:"token"`
}

type CheckResponse struct {
	Success bool   `json:"success"`
	Result  Result `json:"result"`
}

type Result struct {
	PrivilegeFlag int      `json:"privilegeFlag"`
	GroupList     []*Group `json:"groupList"`
	UserName      string   `json:"userName"`
	PrivilegeList []string `json:"privilegeList"`
}

type Group struct {
	GroupId       string `json:"groupId"`
	GroupName     string `json:"groupName"`
	GroupRemark   string `json:"groupRemark"`
	ParentGroupId string `json:"parentGroupId"`
	Status        string `json:"status"`
}

type Service interface {
	Check(ctx context.Context, req *CheckRequest) (*CheckResponse, error)
}
