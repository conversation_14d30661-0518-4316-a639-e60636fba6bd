package sdk

import (
	"context"
	"github.com/pkg/errors"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"testing"
)

type mockOpenApi struct {
	err error
}

func (m *mockOpenApi) SetRalCaller(caller sdk_utils.RalCaller) sdk_utils.RalCaller {
	// TODO implement me
	panic("implement me")
}

func (m *mockOpenApi) DoRequest(ctx context.Context, params *common.OpenApiParams, result any, opts ...sdk_utils.ROption) error {
	return m.err
}

func (m *mockOpenApi) DoRequestEx(ctx context.Context, params *common.OpenApiParams, result any, opts ...sdk_utils.ROption) (*sdk_utils.HttpResponse, error) {
	return nil, m.err
}

func (m *mockOpenApi) DoRequestWithCustomizeHeader(ctx context.Context, params *common.OpenApiParams, result any,
	opts ...sdk_utils.ROption) (*sdk_utils.HttpResponse, error) {
	return nil, m.err
}

func (m *mockOpenApi) DoRequestWithCustomizeHeaderEx(ctx context.Context, params *common.OpenApiParams, result any,
	opts ...sdk_utils.ROption) (*sdk_utils.HttpResponse, error) {
	return nil, m.err
}

func getMockOpenApi(err error) common.OpenApi {
	return &mockOpenApi{
		err: err,
	}
}

func Test_sdkT_Check(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		s := &sdkT{
			OpenApi: getMockOpenApi(nil),
		}
		_, err := s.Check(context.Background(), &CheckRequest{})
		if err != nil {
			t.Errorf("Check() error = %v", err)
		}
	})
	t.Run("error", func(t *testing.T) {
		s := &sdkT{
			OpenApi: getMockOpenApi(errors.New("errDummy")),
		}
		_, err := s.Check(context.Background(), &CheckRequest{})
		if err == nil {
			t.Error("Check() error = nil; want errDummy")
		}
	})
	t.Run("getSdkError", func(t *testing.T) {
		s := NewDefaultSdk()
		_, err := s.Check(context.Background(), &CheckRequest{})
		if err == nil {
			t.Error("Check() error = nil; want errDummy")
		}
	})
}
