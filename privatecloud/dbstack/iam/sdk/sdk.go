package sdk

import (
	"context"
	"github.com/google/uuid"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

const DefaultService = "dbstack-iam"

type sdkT struct {
	common.OpenApi
}

func (s *sdkT) doRequest(ctx context.Context, actionName string, httpMethod, uri string, queries map[string]any, req, rsp any) (err error) {
	params := &common.OpenApiParams{
		ActionName: actionName,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Token:      uuid.New().String(),
	}
	// 请求 openapi
	return s.DoRequest(ctx, params, rsp)
}

func (s *sdkT) Check(ctx context.Context, req *CheckRequest) (*CheckResponse, error) {
	resp := &CheckResponse{}
	if err := s.doRequest(ctx, "Check", "POST", CheckUrl, nil, req, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func NewSdk(serviceName string) Service {
	return &sdkT{
		OpenApi: common.NewOpenApi(serviceName),
	}
}

func NewDefaultSdk() Service {
	return NewSdk(DefaultService)
}
