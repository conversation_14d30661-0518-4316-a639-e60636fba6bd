### Get All vips
GET http://*************:8181/json-api/v1/application/haproxy/getAllInfo

### create a vip
GET http://*************:8181/json-api/v1/application/haproxy/createPort?port=8181

### delete a vip
DELETE http://*************:8181/json-api/v1/application/haproxy/deletePort
Content-Type: application/json

{
    "ip": "*************",
    "port": 8861
}

### insert rs
POST http://*************:8181/json-api/v1/application/haproxy/addRS
Content-Type: application/json

{
  "port": 8861,
  "ip": "*************",
  "rs_list": [
    {
      "ip": "***********",
      "port": 10574
    },
    {
      "ip": "***********",
      "port": 10574
    }
  ]
}

### delete rs
DELETE http://*************:8181/json-api/v1/application/haproxy/deleteRS
Content-Type: application/json

{
  "port": 8861,
  "ip": "*************",
  "rs_list": [
    {
      "ip": "***********",
      "port": 10574
    }
  ]
}