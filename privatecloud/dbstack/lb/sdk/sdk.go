package sdk

import (
	"context"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
)

const (
	DefaultServiceName = "dbstack-lb"
)

type sdkT struct {
	common.OpenApi
}

func (s *sdkT) doRequest(ctx context.Context, actionName string, httpMethod, uri string, queries map[string]any, req, rsp any) (err error) {
	params := &common.OpenApiParams{
		ActionName: actionName,
		HttpMethod: httpMethod,
		Uri:        uri,
		Queries:    queries,
		Posts:      req,
		Token:      uuid.New().String(),
	}
	// 请求 openapi
	return s.DoRequest(ctx, params, rsp)
}

func (s *sdkT) CreateLb(ctx context.Context, req *CreateLbRequest) (*CreateLbResponse, error) {
	resp := &CreateLbResponse{}
	var queries map[string]any
	if req.Port > 0 {
		queries = map[string]any{
			"port": req.Port,
		}
	}
	if err := s.doRequest(ctx, "Create", "GET", CreateLbUrl, queries, nil, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *sdkT) DeleteLb(ctx context.Context, req *DeleteLbRequest) (*DeleteLbResponse, error) {
	resp := &DeleteLbResponse{}
	if err := s.doRequest(ctx, "Delete", "DELETE", DeleteRsUrl, nil, req, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *sdkT) AddRs(ctx context.Context, req *AddRsRequest) (*AddRsResponse, error) {
	resp := &AddRsResponse{}
	if err := s.doRequest(ctx, "AddRs", "POST", AddRsUrl, nil, req, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *sdkT) DeleteRs(ctx context.Context, req *DeleteRsRequest) (*DeleteRsResponse, error) {
	resp := &DeleteRsResponse{}
	if err := s.doRequest(ctx, "DeleteRs", "DELETE", DeleteRsUrl, nil, req, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *sdkT) GetAllInfo(ctx context.Context) (*GetAllInfoResponse, error) {
	resp := &GetAllInfoResponse{}
	if err := s.doRequest(ctx, "GetAllInfo", "GET", GetAllInfo, nil, nil, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func NewSdk(serviceName string) Service {
	return &sdkT{
		OpenApi: common.NewOpenApi(serviceName),
	}
}
