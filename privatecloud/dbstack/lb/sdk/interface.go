package sdk

import "context"

const (
	CreateLbUrl = "/json-api/v1/application/haproxy/createPort"
	AddRsUrl    = "/json-api/v1/application/haproxy/addRS"
	DeleteRsUrl = "/json-api/v1/application/haproxy/deleteRS"
	GetAllInfo  = "/json-api/v1/application/haproxy/getAllInfo"
)

const (
	SuccessCode = 1
)

type CreateLbRequest struct {
	Port int `json:"port"`
}

type CreateLbResponse struct {
	Success   int    `json:"success"`
	IP        string `json:"ip,omitempty"`
	Port      string `json:"port"`
	ErrorInfo string `json:"errorInfo,omitempty"`
}

type DeleteLbRequest struct {
	Port int    `json:"port"`
	IP   string `json:"ip"`
}

type DeleteLbResponse struct {
	Success     int    `json:"success"`
	Application string `json:"application,omitempty"`
	ErrorInfo   string `json:"errorInfo,omitempty"`
}

type AddRsRequest struct {
	Port   int    `json:"port"`
	IP     string `json:"ip"`
	RsList []*Rs  `json:"rs_list"`
}

type Rs struct {
	IP   string `json:"ip"`
	Port int    `json:"port"`
}

type AddRsResponse struct {
	Success     int    `json:"success"`
	Application string `json:"application,omitempty"`
	ErrorInfo   string `json:"errorInfo,omitempty"`
}

type DeleteRsRequest struct {
	IP     string `json:"ip"`
	Port   int    `json:"port"`
	RsList []*Rs  `json:"rs_list"`
}

type DeleteRsResponse struct {
	Success     int    `json:"success"`
	Application string `json:"application,omitempty"`
	ErrorInfo   string `json:"error info,omitempty"`
}

type GetAllInfoResponse struct {
	Data    []*DataItem `json:"data,omitempty"`
	Success int         `json:"success,omitempty"`
}

type DataItem struct {
	Rs   []*RsItem `json:"rs,omitempty"`
	Port string    `json:"port,omitempty"`
	IP   string    `json:"ip,omitempty"`
}

type RsItem struct {
	Zone   string `json:"zone,omitempty"`
	RsIP   string `json:"rs_ip,omitempty"`
	RsPort string `json:"rs_port,omitempty"`
	Region string `json:"region,omitempty"`
}

type Service interface {
	CreateLb(ctx context.Context, req *CreateLbRequest) (*CreateLbResponse, error)
	DeleteLb(ctx context.Context, req *DeleteLbRequest) (*DeleteLbResponse, error)
	AddRs(ctx context.Context, req *AddRsRequest) (*AddRsResponse, error)
	DeleteRs(ctx context.Context, req *DeleteRsRequest) (*DeleteRsResponse, error)
	GetAllInfo(ctx context.Context) (*GetAllInfoResponse, error)
}
