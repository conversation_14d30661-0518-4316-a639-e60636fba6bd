package sdk

import (
	"context"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

type mockOpenApi struct {
	err error
}

func (m *mockOpenApi) SetRalCaller(caller sdk_utils.RalCaller) sdk_utils.RalCaller {
	// TODO implement me
	panic("implement me")
}

func (m *mockOpenApi) DoRequest(ctx context.Context, params *common.OpenApiParams, result any, opts ...sdk_utils.ROption) error {
	return m.err
}

func (m *mockOpenApi) DoRequestEx(ctx context.Context, params *common.OpenApiParams, result any, opts ...sdk_utils.ROption) (*sdk_utils.HttpResponse, error) {
	return nil, m.err
}

func (m *mockOpenApi) DoRequestWithCustomizeHeader(ctx context.Context, params *common.OpenApiParams, result any,
	opts ...sdk_utils.ROption) (*sdk_utils.HttpResponse, error) {
	return nil, m.err
}

func (m *mockOpenApi) DoRequestWithCustomizeHeaderEx(ctx context.Context, params *common.OpenApiParams, result any,
	opts ...sdk_utils.ROption) (*sdk_utils.HttpResponse, error) {
	return nil, m.err
}

func getMockOpenApi(err error) common.OpenApi {
	return &mockOpenApi{
		err: err,
	}
}

func Test_sdkT_CreateLb(t *testing.T) {
	s := &sdkT{
		OpenApi: getMockOpenApi(nil),
	}
	_, err := s.CreateLb(context.Background(), &CreateLbRequest{})
	if err != nil {
		t.Errorf("CreateLb() error = %v", err)
		return
	}
}

func Test_sdkT_DeleteLb(t *testing.T) {
	s := &sdkT{
		OpenApi: getMockOpenApi(nil),
	}
	_, err := s.DeleteLb(context.Background(), &DeleteLbRequest{})
	if err != nil {
		t.Errorf("DeleteLb() error = %v", err)
		return
	}
}

func Test_sdkT_AddRs(t *testing.T) {
	s := &sdkT{
		OpenApi: getMockOpenApi(nil),
	}
	_, err := s.AddRs(context.Background(), &AddRsRequest{})
	if err != nil {
		t.Errorf("AddRs() error = %v", err)
		return
	}
}

func Test_sdkT_DeleteRs(t *testing.T) {
	s := &sdkT{
		OpenApi: getMockOpenApi(nil),
	}
	_, err := s.DeleteRs(context.Background(), &DeleteRsRequest{})
	if err != nil {
		t.Errorf("DeleteRs() error = %v", err)
		return
	}
}

func Test_sdkT_GetAllInfo(t *testing.T) {
	s := &sdkT{
		OpenApi: getMockOpenApi(nil),
	}
	_, err := s.GetAllInfo(context.Background())
	if err != nil {
		t.Errorf("GetAllInfo() error = %v", err)
		return
	}
}
