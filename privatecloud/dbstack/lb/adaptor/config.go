package adaptor

import (
	"fmt"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const extraConfName = "DbstackLbConfs"

type DbstackLbConfs struct {
	RegionSpecVips []*RegionSpecVip `toml:"RegionSpecVips"`
}

type RegionSpecVip struct {
	Region string `toml:"Region"`
	Vip    string `toml:"Vip"`
}

var dbstackLbConfs *DbstackLbConfs

func (conf *DbstackLbConfs) mustLoad(serviceName string) {
	ralConf, err := sdk_utils.GetRalConf(serviceName)
	if err != nil {
		panic(fmt.Sprintf("get ral conf fail: %s", err.Error()))
	}

	extraConf, ok := ralConf.Extra[extraConfName]
	if !ok {
		panic(fmt.Sprintf("need ral extra conf: %s", extraConfName))
	}

	err = sdk_utils.CastWithTag(extraConf, conf, "toml")
	if err != nil {
		panic(fmt.Sprintf("ral extra conf format error: %s", err.<PERSON>rror()))
	}
}
