package adaptor

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"

	"github.com/spf13/cast"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/dbstack/lb/sdk"
	elbv2 "icode.baidu.com/baidu/scs/x1-base/sdk/elb_v2"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type adaptorT struct {
	sdk sdk.Service
}

var once sync.Once

func GetIpPortFromID(id string) (string, int, error) {
	sp := strings.Split(id, "-")
	if len(sp) != 3 {
		return "", 0, fmt.Errorf("invalid id: %s", id)
	}
	ip := strings.ReplaceAll(sp[1], "_", ".")
	port, err := strconv.Atoi(sp[2])
	if err != nil {
		return "", 0, fmt.<PERSON>rrorf("invalid id: %s", id)
	}
	return ip, port, nil
}

func GetRegionMappedIP(vpcId string, ip string) string {
	vpcParts := strings.Split(vpcId, "-")
	region := vpcParts[len(vpcParts)-1]
	for _, regionSpecVip := range dbstackLbConfs.RegionSpecVips {
		if regionSpecVip.Region == region {
			return regionSpecVip.Vip
		}
	}
	return ip
}

func (a adaptorT) getInfoByBlbID(ctx context.Context, ip string, port int) (*sdk.DataItem, error) {
	listResp, err := a.sdk.GetAllInfo(ctx)
	if err != nil {
		return nil, err
	}
	for _, lbInfo := range listResp.Data {
		if lbInfo.Port == strconv.Itoa(port) {
			// 如果blbID中的ip是配置map中的IP, 则直接返回
			for _, regionSpecVip := range dbstackLbConfs.RegionSpecVips {
				if regionSpecVip.Vip == ip {
					return lbInfo, nil
				}
			}
			// 如果blbID中的ip不是配置中的IP, 如果与LB返回的IP匹配, 则返回
			if ip == lbInfo.IP {
				return lbInfo, nil
			}
		}
	}
	return nil, cerrs.ErrNotFound.Errorf("lb not found: %s:%d", ip, port)
}

func (a adaptorT) CreateAppELB(ctx context.Context, req *elbv2.CreateELBRequest) (rsp *elbv2.CreateELBResponse, err error) {
	resp, err := a.sdk.CreateLb(ctx, &sdk.CreateLbRequest{})
	if err != nil {
		return nil, err
	}
	if resp.Success != sdk.SuccessCode || resp.IP == "" {
		return nil, fmt.Errorf("create lb failed, code: %d, resp: %s", resp.Success, base_utils.Format(resp))
	}
	resp.IP = GetRegionMappedIP(req.VpcID, resp.IP)
	return &elbv2.CreateELBResponse{
		BLBID:       fmt.Sprintf("haproxy-%s-%s", strings.ReplaceAll(resp.IP, ".", "_"), resp.Port),
		Name:        fmt.Sprintf("haproxy-%s-%s", strings.ReplaceAll(resp.IP, ".", "_"), resp.Port),
		Desc:        fmt.Sprintf("haproxy-%s-%s", strings.ReplaceAll(resp.IP, ".", "_"), resp.Port),
		Address:     resp.IP,
		UnderlayVip: resp.IP,
		RequestID:   req.Auth.TransactionId,
	}, nil
}

func (a adaptorT) DeleteAppELB(ctx context.Context, req *elbv2.DeleteELBRequest) (rsp *elbv2.OperationResponse, err error) {
	ip, port, err := GetIpPortFromID(req.BLBID)
	if err != nil {
		return nil, err
	}
	lbInfo, err := a.getInfoByBlbID(ctx, ip, port)
	if err != nil {
		if cerrs.Is(err, cerrs.ErrNotFound) {
			return &elbv2.OperationResponse{
				RequestID: req.Auth.TransactionId,
				Code:      "SUCCESS",
				Message:   "SUCCESS",
			}, nil
		}
		return nil, err
	}
	resp, err := a.sdk.DeleteLb(ctx, &sdk.DeleteLbRequest{
		Port: port,
		IP:   lbInfo.IP,
	})
	if err != nil {
		return nil, err
	}
	if resp.Success != sdk.SuccessCode {
		return nil, fmt.Errorf("delete lb failed, code: %d, resp: %s", resp.Success, base_utils.Format(resp))
	}
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
}

func (a adaptorT) UpdateAppELB(ctx context.Context, req *elbv2.UpdateELBRequest) (rsp *elbv2.OperationResponse, err error) {
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
}

func (a adaptorT) ListAppELB(ctx context.Context, req *elbv2.ListELBRequest) (rsp *elbv2.ListELBResponse, err error) {
	rsp = &elbv2.ListELBResponse{
		BLBList:   make([]*elbv2.AppBLB, 0),
		RequestID: req.Auth.TransactionId,
		Message:   "SUCCESS",
		Code:      "SUCCESS",
	}
	ip, port, err := GetIpPortFromID(req.BLBID)
	if err != nil {
		return nil, err
	}
	_, err = a.getInfoByBlbID(ctx, ip, port)
	if err != nil && !cerrs.Is(err, cerrs.ErrNotFound) {
		return nil, err
	}
	if err == nil {
		rsp.BLBList = append(rsp.BLBList, &elbv2.AppBLB{
			BLBID:   req.BLBID,
			Address: ip,
			Name:    req.BLBID,
			Status:  "available",
		})
	}
	return rsp, nil
}

func (a adaptorT) GetAppELBDetail(ctx context.Context, req *elbv2.GetAppELBDetailRequest) (rsp *elbv2.GetAppELBDetailResponse, err error) {
	ip, port, err := GetIpPortFromID(req.BLBID)
	if err != nil {
		return nil, err
	}
	_, err = a.getInfoByBlbID(ctx, ip, port)
	if err != nil {
		return nil, err
	}
	return &elbv2.GetAppELBDetailResponse{
		BlbID:     req.BLBID,
		Address:   ip,
		Name:      req.BLBID,
		Status:    "available",
		RequestID: req.Auth.TransactionId,
		Message:   "SUCCESS",
		Code:      "SUCCESS",
		Listener: []*elbv2.ListenerInfo{
			{
				Port: strconv.Itoa(port),
				Type: "TCP",
			},
		},
	}, nil
}

func (a adaptorT) GetEncryptAccountID(ctx context.Context, req *elbv2.GetEncryptAccountRequest) (rsp *elbv2.GetEncryptAccountResponse, err error) {
	return &elbv2.GetEncryptAccountResponse{
		EncryptAccountID: "haproxy-no-account",
		RequestID:        req.Auth.TransactionId,
		Message:          "SUCCESS",
		Code:             "SUCCESS",
	}, nil
}

func (a adaptorT) CreateAppServerGroup(ctx context.Context, req *elbv2.CreateServerGroupRequest) (rsp *elbv2.CreateServerGroupResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) DeleteAppServerGroup(ctx context.Context, req *elbv2.DeleteServerGroupRequest) (rsp *elbv2.OperationResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) UpdateAppServerGroup(ctx context.Context, req *elbv2.UpdateServerGroupRequest) (rsp *elbv2.OperationResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) ListAppServerGroup(ctx context.Context, req *elbv2.ListServerGroupRequest) (rsp *elbv2.ListServerGroupResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) CreateAppServerGroupPort(ctx context.Context, req *elbv2.CommonServerGroupPortRequest) (rsp *elbv2.CreateServerGroupPortResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) UpdateAppServerGroupPort(ctx context.Context, req *elbv2.CommonServerGroupPortRequest) (rsp *elbv2.OperationResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) DeleteAppServerGroupPort(ctx context.Context, req *elbv2.DeleteServerGroupPortRequest) (rsp *elbv2.OperationResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) CreateAppBLBRs(ctx context.Context, req *elbv2.CommonAppBlbRsRequest) (rsp *elbv2.OperationResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) UpdateAppBLBRs(ctx context.Context, req *elbv2.CommonAppBlbRsRequest) (rsp *elbv2.OperationResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) ListAppBLBRs(ctx context.Context, req *elbv2.ListAppBlbRsRequest) (rsp *elbv2.ListAppBlbRsResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) DeleteAppBLBRs(ctx context.Context, req *elbv2.DeleteAppBlbRsRequest) (rsp *elbv2.OperationResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) ListAppBLBRsMount(ctx context.Context, req *elbv2.CommonAppBlbRsListRequest) (rsp *elbv2.CommonAppBlbRsListResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) ListAppBLBRsUnmount(ctx context.Context, req *elbv2.CommonAppBlbRsListRequest) (rsp *elbv2.CommonAppBlbRsListResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) CreateAppTCPListener(ctx context.Context, req *elbv2.CreateAppTCPListenerRequest) (rsp *elbv2.OperationResponse, err error) {
	ip, port, err := GetIpPortFromID(req.BlbID)
	if err != nil {
		return nil, err
	}
	_, err = a.getInfoByBlbID(ctx, ip, port)
	if err != nil {
		return nil, err
	}
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
	return nil, fmt.Errorf("lb not found: %s", req.BlbID)
}

func (a adaptorT) UpdateAppTCPListener(ctx context.Context, req *elbv2.CommonAppTCPListenerRequest) (rsp *elbv2.OperationResponse, err error) {
	ip, port, err := GetIpPortFromID(req.BlbID)
	if err != nil {
		return nil, err
	}
	_, err = a.getInfoByBlbID(ctx, ip, port)
	if err != nil {
		return nil, err
	}
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
	return nil, fmt.Errorf("lb not found: %s", req.BlbID)
}

func (a adaptorT) ListAppTCPListener(ctx context.Context, req *elbv2.CommonListAppListenerRequest) (rsp *elbv2.ListAppTCPListenerResponse, err error) {
	ip, port, err := GetIpPortFromID(req.BlbID)
	if err != nil {
		return nil, err
	}
	_, err = a.getInfoByBlbID(ctx, ip, port)
	if err != nil {
		return nil, err
	}
	return &elbv2.ListAppTCPListenerResponse{
		ListenerList: []*elbv2.TCPListenerList{
			{
				ListenerPort:      port,
				Scheduler:         "RoundRobin",
				TCPSessionTimeout: 0,
			},
		},
		RequestID: req.Auth.TransactionId,
		Message:   "SUCCESS",
		Code:      "SUCCESS",
	}, nil
}

func (a adaptorT) CreateAppHTTPListener(ctx context.Context, req *elbv2.CommonAppHTTPListenerRequest) (rsp *elbv2.OperationResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) UpdateAppHTTPListener(ctx context.Context, req *elbv2.CommonAppHTTPListenerRequest) (rsp *elbv2.OperationResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) ListAppHTTPListener(ctx context.Context, req *elbv2.CommonListAppListenerRequest) (rsp *elbv2.ListAppHTTPListenerResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (a adaptorT) DeleteAppListener(ctx context.Context, req *elbv2.DeleteAppListenerRequest) (rsp *elbv2.OperationResponse, err error) {
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
}

func (a adaptorT) CreateAppPolicys(ctx context.Context, req *elbv2.CreateAppPolicysRequest) (rsp *elbv2.OperationResponse, err error) {
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
}

func (a adaptorT) ListAppPolicys(ctx context.Context, req *elbv2.ListAppPolicysRequest) (rsp *elbv2.ListAppPolicysResponse, err error) {
	return &elbv2.ListAppPolicysResponse{
		PolicyList: []*elbv2.PolicyList{
			{
				ID:                 fmt.Sprintf("policy-%s", req.BlbID),
				AppServerGroupID:   fmt.Sprintf("group-%s", req.BlbID),
				AppServerGroupName: fmt.Sprintf("group-%s", req.BlbID),
			},
		},
		RequestID: req.Auth.TransactionId,
		Message:   "SUCCESS",
		Code:      "SUCCESS",
	}, nil
}

func (a adaptorT) DeleteAppPolicys(ctx context.Context, req *elbv2.DeleteAppPolicysRequest) (rsp *elbv2.OperationResponse, err error) {
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
}

func (a adaptorT) CreateAppIPGroup(ctx context.Context, req *elbv2.CreateAppIPGroupRequest) (rsp *elbv2.CreateAppIPGroupResponse, err error) {
	ip, port, err := GetIpPortFromID(req.BlbID)
	if err != nil {
		return nil, err
	}
	_, err = a.getInfoByBlbID(ctx, ip, port)
	if err != nil {
		return nil, err
	}
	return &elbv2.CreateAppIPGroupResponse{
		ID:        fmt.Sprintf("ipgroup-%s", req.BlbID),
		Name:      fmt.Sprintf("ipgroup-%s", req.BlbID),
		Desc:      fmt.Sprintf("ipgroup-%s", req.BlbID),
		RequestID: req.Auth.TransactionId,
		Message:   "SUCCESS",
		Code:      "SUCCESS",
	}, nil
}

func (a adaptorT) DeleteAppIPGroup(ctx context.Context, req *elbv2.DeleteAppIPGroupRequest) (rsp *elbv2.OperationResponse, err error) {
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
}

func (a adaptorT) UpdateAppIPGroup(ctx context.Context, req *elbv2.UpdateAppIPGroupRequest) (rsp *elbv2.OperationResponse, err error) {
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
}

func (a adaptorT) ListAppIPGroup(ctx context.Context, req *elbv2.ListAppIPGroupRequest) (rsp *elbv2.ListAppIPGroupResponse, err error) {
	ip, port, err := GetIpPortFromID(req.BlbID)
	if err != nil {
		return nil, err
	}
	_, err = a.getInfoByBlbID(ctx, ip, port)
	if err != nil {
		return nil, err
	}
	return &elbv2.ListAppIPGroupResponse{
		AppIPGroupList: []*elbv2.AppIPGroupList{
			{
				ID:   fmt.Sprintf("ipgroup-%s", req.BlbID),
				Name: fmt.Sprintf("ipgroup-%s", req.BlbID),
				Desc: fmt.Sprintf("ipgroup-%s", req.BlbID),
				BackendPolicyList: []*elbv2.BackendPolicyList{
					{
						ID: fmt.Sprintf("policy-%s", req.BlbID),
					},
				},
			},
		},
		RequestID: req.Auth.TransactionId,
		Message:   "SUCCESS",
		Code:      "SUCCESS",
	}, nil
}

func (a adaptorT) CreateAppIPGroupPolicy(ctx context.Context, req *elbv2.CommonAppIPGroupPolicyRequest) (rsp *elbv2.OperationResponse, err error) {
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
}

func (a adaptorT) UpdateAppIPGroupPolicy(ctx context.Context, req *elbv2.CommonAppIPGroupPolicyRequest) (rsp *elbv2.OperationResponse, err error) {
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
}

func (a adaptorT) DeleteAppIPGroupPolicy(ctx context.Context, req *elbv2.DeleteAppIPGroupPolicyRequest) (rsp *elbv2.OperationResponse, err error) {
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
}

func (a adaptorT) CreateAppIPGroupMember(ctx context.Context, req *elbv2.CommonAppIPGroupMemberRequest) (rsp *elbv2.OperationResponse, err error) {
	ip, port, err := GetIpPortFromID(req.BlbID)
	if err != nil {
		return nil, err
	}
	lbInfo, err := a.getInfoByBlbID(ctx, ip, port)
	if err != nil {
		return nil, err
	}
	addRsReq := &sdk.AddRsRequest{
		Port: port,
		IP:   ip,
	}
	for _, toAdd := range req.MemberList {
		found := false
		for _, curRs := range lbInfo.Rs {
			if curRs.RsIP == toAdd.IP && curRs.RsPort == strconv.Itoa(toAdd.Port) {
				found = true
				break
			}
		}
		if !found {
			addRsReq.RsList = append(addRsReq.RsList, &sdk.Rs{
				IP:   toAdd.IP,
				Port: toAdd.Port,
			})
		}
	}
	if len(addRsReq.RsList) == 0 {
		return &elbv2.OperationResponse{
			RequestID: req.Auth.TransactionId,
			Code:      "SUCCESS",
			Message:   "SUCCESS",
		}, nil
	}
	resp, err := a.sdk.AddRs(ctx, addRsReq)
	if err != nil {
		return nil, err
	}
	if resp.Success != sdk.SuccessCode {
		return nil, fmt.Errorf("add rs failed, code: %d, resp: %s", resp.Success, base_utils.Format(resp))
	}
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
}

func (a adaptorT) UpdateAppIPGroupMember(ctx context.Context, req *elbv2.CommonAppIPGroupMemberRequest) (rsp *elbv2.OperationResponse, err error) {
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
}

func (a adaptorT) DeleteAppIPGroupMember(ctx context.Context, req *elbv2.DeleteAppIPGroupMemberRequest) (rsp *elbv2.OperationResponse, err error) {
	ip, port, err := GetIpPortFromID(req.BlbID)
	if err != nil {
		return nil, err
	}
	lbInfo, err := a.getInfoByBlbID(ctx, ip, port)
	if err != nil {
		return nil, err
	}
	delRsReq := &sdk.DeleteRsRequest{
		Port: port,
		IP:   ip,
	}
	for _, toDelID := range req.MemberIDList {
		memberIp, memberPort, err := GetIpPortFromID(toDelID)
		if err != nil {
			return nil, err
		}
		for _, curRs := range lbInfo.Rs {
			if curRs.RsIP == memberIp && curRs.RsPort == strconv.Itoa(memberPort) {
				delRsReq.RsList = append(delRsReq.RsList, &sdk.Rs{
					IP:   memberIp,
					Port: memberPort,
				})
			}
		}
	}
	if len(delRsReq.RsList) == 0 {
		return &elbv2.OperationResponse{
			RequestID: req.Auth.TransactionId,
			Code:      "SUCCESS",
			Message:   "SUCCESS",
		}, nil
	}
	resp, err := a.sdk.DeleteRs(ctx, delRsReq)
	if err != nil {
		return nil, err
	}
	if resp.Success != sdk.SuccessCode {
		return nil, fmt.Errorf("delete rs failed, code: %d, resp: %s", resp.Success, base_utils.Format(resp))
	}
	return &elbv2.OperationResponse{
		RequestID: req.Auth.TransactionId,
		Code:      "SUCCESS",
		Message:   "SUCCESS",
	}, nil
}

func (a adaptorT) ListAppIPGroupMember(ctx context.Context, req *elbv2.ListAppIPGroupMemberRequest) (rsp *elbv2.ListAppIPGroupMemberResponse, err error) {
	ip, port, err := GetIpPortFromID(req.BlbID)
	if err != nil {
		return nil, err
	}
	lbInfo, err := a.getInfoByBlbID(ctx, ip, port)
	if err != nil {
		return nil, err
	}
	var memberList []*elbv2.MemberList
	for _, rs := range lbInfo.Rs {
		memberList = append(memberList, &elbv2.MemberList{
			MemberID: fmt.Sprintf("rs-%s-%s", strings.ReplaceAll(rs.RsIP, ".", "_"), rs.RsPort),
			IP:       rs.RsIP,
			Port:     cast.ToInt(rs.RsPort),
			Weight:   100,
		})
	}
	return &elbv2.ListAppIPGroupMemberResponse{
		MemberList: memberList,
		RequestID:  req.Auth.TransactionId,
		Message:    "SUCCESS",
		Code:       "SUCCESS",
	}, nil
}

func GetDbstackLbAdaptor(s sdk.Service) elbv2.AppELBService {
	return &adaptorT{
		sdk: s,
	}
}

func GetDefaultDbstackLbAdaptor() elbv2.AppELBService {
	once.Do(func() {
		dbstackLbConfs = &DbstackLbConfs{}
		dbstackLbConfs.mustLoad(sdk.DefaultServiceName)
	})
	return &adaptorT{
		sdk: sdk.NewSdk(sdk.DefaultServiceName),
	}
}
