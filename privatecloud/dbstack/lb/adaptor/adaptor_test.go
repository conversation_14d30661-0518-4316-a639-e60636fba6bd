package adaptor

import (
	"context"
	"errors"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/privatecloud/dbstack/lb/sdk"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	elbv2 "icode.baidu.com/baidu/scs/x1-base/sdk/elb_v2"
)

type MockSdk struct {
	Err     bool
	NotSucc bool
}

func (m MockSdk) CreateLb(ctx context.Context, req *sdk.CreateLbRequest) (*sdk.CreateLbResponse, error) {
	if m.Err {
		return nil, errors.New("create lb error")
	}
	if m.NotSucc {
		return &sdk.CreateLbResponse{
			Success:   0,
			ErrorInfo: "create lb not success",
		}, nil
	}
	return &sdk.CreateLbResponse{
		Success: 1,
		IP:      "***********",
		Port:    "8080",
	}, nil
}

func (m MockSdk) DeleteLb(ctx context.Context, req *sdk.DeleteLbRequest) (*sdk.DeleteLbResponse, error) {
	if m.Err {
		return nil, errors.New("delete lb error")
	}
	if m.NotSucc {
		return &sdk.DeleteLbResponse{
			Success:   0,
			ErrorInfo: "delete lb not success",
		}, nil
	}
	return &sdk.DeleteLbResponse{
		Success:     1,
		Application: "success delete lb",
	}, nil
}

func (m MockSdk) AddRs(ctx context.Context, req *sdk.AddRsRequest) (*sdk.AddRsResponse, error) {
	if m.Err {
		return nil, errors.New("add rs error")
	}
	if m.NotSucc {
		return &sdk.AddRsResponse{
			Success:   0,
			ErrorInfo: "add rs not success",
		}, nil
	}
	return &sdk.AddRsResponse{
		Success:     1,
		Application: "success add rs",
	}, nil
}

func (m MockSdk) DeleteRs(ctx context.Context, req *sdk.DeleteRsRequest) (*sdk.DeleteRsResponse, error) {
	if m.Err {
		return nil, errors.New("delete rs error")
	}
	if m.NotSucc {
		return &sdk.DeleteRsResponse{
			Success:   0,
			ErrorInfo: "delete rs not success",
		}, nil
	}
	return &sdk.DeleteRsResponse{
		Success:     1,
		Application: "success delete rs",
	}, nil
}

func (m MockSdk) GetAllInfo(ctx context.Context) (*sdk.GetAllInfoResponse, error) {
	if m.Err {
		return nil, errors.New("get all info error")
	}
	return &sdk.GetAllInfoResponse{
		Data: []*sdk.DataItem{
			{
				Rs: []*sdk.RsItem{
					{
						Zone:   "zoneA",
						RsIP:   "*********",
						RsPort: "10234",
						Region: "bj",
					},
					{
						Zone:   "zoneB",
						RsIP:   "*********",
						RsPort: "10234",
						Region: "bj",
					},
				},
				Port: "8080",
				IP:   "***********",
			},
		},
		Success: 0,
	}, nil
}

func NewMockSdk(err, notSucc bool) sdk.Service {
	dbstackLbConfs = &DbstackLbConfs{
		RegionSpecVips: []*RegionSpecVip{
			{
				Region: "bj",
				Vip:    "***********",
			},
		},
	}
	return &MockSdk{
		Err:     err,
		NotSucc: notSucc,
	}
}

func TestGetIpPortFromID(t *testing.T) {
	tests := []struct {
		name     string
		id       string
		wantIp   string
		wantPort int
		wantErr  bool
	}{
		{
			name:     "valid id",
			id:       "haproxy-192_168_1_1-8080",
			wantIp:   "***********",
			wantPort: 8080,
			wantErr:  false,
		},
		{
			name:     "invalid id",
			id:       "invalid-id",
			wantIp:   "",
			wantPort: 0,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotIp, gotPort, err := GetIpPortFromID(tt.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetIpPortFromID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotIp != tt.wantIp {
				t.Errorf("GetIpPortFromID() gotIp = %v, want %v", gotIp, tt.wantIp)
			}
			if gotPort != tt.wantPort {
				t.Errorf("GetIpPortFromID() gotPort = %v, want %v", gotPort, tt.wantPort)
			}
		})
	}
}

func Test_adaptorT_CreateAppELB(t *testing.T) {
	aErr := GetDbstackLbAdaptor(NewMockSdk(true, false))
	aNotSucc := GetDbstackLbAdaptor(NewMockSdk(false, true))
	a := GetDbstackLbAdaptor(NewMockSdk(false, false))
	req := &elbv2.CreateELBRequest{
		Auth: &common.Authentication{
			TransactionId: "transaction-id",
		},
		VpcID: "vpcId-bj",
	}
	t.Run("error", func(t *testing.T) {
		_, err := aErr.CreateAppELB(context.Background(), req)
		if err == nil {
			t.Errorf("CreateAppELB() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("not success", func(t *testing.T) {
		_, err := aNotSucc.CreateAppELB(context.Background(), req)
		if err == nil {
			t.Errorf("CreateAppELB() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("success", func(t *testing.T) {
		resp, err := a.CreateAppELB(context.Background(), req)
		if err != nil {
			t.Errorf("CreateAppELB() error = %v, wantErr %v", err, false)
		}
		if resp == nil {
			t.Errorf("CreateAppELB() resp = %v, wantResp not nil", resp)
		}
		if resp.BLBID != "haproxy-192_168_1_1-8080" {
			t.Errorf("CreateAppELB() resp.BLBID = %v, want haproxy-192_168_1_1-8080", resp.BLBID)
		}
	})
}

func Test_adaptorT_DeleteAppELB(t *testing.T) {
	aErr := GetDbstackLbAdaptor(NewMockSdk(true, false))
	aNotSucc := GetDbstackLbAdaptor(NewMockSdk(false, true))
	a := GetDbstackLbAdaptor(NewMockSdk(false, false))
	req := &elbv2.DeleteELBRequest{
		Auth: &common.Authentication{
			TransactionId: "transaction-id",
		},
		BLBID: "haproxy-192_168_1_1-8080",
	}
	t.Run("error", func(t *testing.T) {
		_, err := aErr.DeleteAppELB(context.Background(), req)
		if err == nil {
			t.Errorf("DeleteAppELB() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("not success", func(t *testing.T) {
		_, err := aNotSucc.DeleteAppELB(context.Background(), req)
		if err == nil {
			t.Errorf("DeleteAppELB() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("success", func(t *testing.T) {
		_, err := a.DeleteAppELB(context.Background(), req)
		if err != nil {
			t.Errorf("DeleteAppELB() error = %v, wantErr %v", err, false)
		}
	})
	t.Run("invalid id", func(t *testing.T) {
		req.BLBID = "invalid-id"
		_, err := a.DeleteAppELB(context.Background(), req)
		if err == nil {
			t.Errorf("DeleteAppELB() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("not exist", func(t *testing.T) {
		req.BLBID = "haproxy-192_168_1_2-8080"
		_, err := a.DeleteAppELB(context.Background(), req)
		if err != nil {
			t.Errorf("DeleteAppELB() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_UpdateAppELB(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.UpdateELBRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
			BLBID: "haproxy-192_168_1_1-8080",
		}
		_, err := a.UpdateAppELB(context.Background(), req)
		if err != nil {
			t.Errorf("UpdateAppELB() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_ListAppELB(t *testing.T) {
	aErr := GetDbstackLbAdaptor(NewMockSdk(true, false))
	a := GetDbstackLbAdaptor(NewMockSdk(false, false))
	t.Run("invalid id", func(t *testing.T) {
		_, err := a.ListAppELB(context.Background(), &elbv2.ListELBRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
			BLBID: "invalid-id",
		})
		if err == nil {
			t.Errorf("ListAppELB() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("error", func(t *testing.T) {
		_, err := aErr.ListAppELB(context.Background(), &elbv2.ListELBRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
			BLBID: "haproxy-192_168_1_1-8080",
		})
		if err == nil {
			t.Errorf("ListAppELB() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("success", func(t *testing.T) {
		resp, err := a.ListAppELB(context.Background(), &elbv2.ListELBRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
			BLBID: "haproxy-192_168_1_1-8080",
		})
		if err != nil {
			t.Errorf("ListAppELB() error = %v, wantErr %v", err, false)
		}
		if resp == nil {
			t.Errorf("ListAppELB() resp = %v, wantResp not nil", resp)
		}
		if len(resp.BLBList) != 1 || resp.BLBList[0].BLBID != "haproxy-192_168_1_1-8080" {
			t.Errorf("ListAppELB() resp.BLBList = %v, want haproxy-192_168_1_1-8080", resp.BLBList)
		}
	})
	t.Run("empty", func(t *testing.T) {
		resp, err := a.ListAppELB(context.Background(), &elbv2.ListELBRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
			BLBID: "haproxy-192_168_1_2-8080",
		})
		if err != nil {
			t.Errorf("ListAppELB() error = %v, wantErr %v", err, false)
		}
		if resp == nil {
			t.Errorf("ListAppELB() resp = %v, wantResp not nil", resp)
		}
		if len(resp.BLBList) != 0 {
			t.Errorf("ListAppELB() resp.BLBList = %v, want empty", resp.BLBList)
		}
	})
}

func Test_adaptorT_GetAppELBDetail(t *testing.T) {
	aErr := GetDbstackLbAdaptor(NewMockSdk(true, false))
	a := GetDbstackLbAdaptor(NewMockSdk(false, false))
	t.Run("invalid id", func(t *testing.T) {
		_, err := a.GetAppELBDetail(context.Background(), &elbv2.GetAppELBDetailRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
			BLBID: "invalid-id",
		})
		if err == nil {
			t.Errorf("GetAppELBDetail() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("error", func(t *testing.T) {
		_, err := aErr.GetAppELBDetail(context.Background(), &elbv2.GetAppELBDetailRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
			BLBID: "haproxy-192_168_1_1-8080",
		})
		if err == nil {
			t.Errorf("GetAppELBDetail() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("success", func(t *testing.T) {
		resp, err := a.GetAppELBDetail(context.Background(), &elbv2.GetAppELBDetailRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
			BLBID: "haproxy-192_168_1_1-8080",
		})
		if err != nil {
			t.Errorf("GetAppELBDetail() error = %v, wantErr %v", err, false)
		}
		if resp == nil {
			t.Errorf("GetAppELBDetail() resp = %v, wantResp not nil", resp)
		}
		if resp.BlbID != "haproxy-192_168_1_1-8080" {
			t.Errorf("GetAppELBDetail() resp.BLBID = %v, want haproxy-192_168_1_1-8080", resp.BlbID)
		}
	})
	t.Run("not found", func(t *testing.T) {
		_, err := a.GetAppELBDetail(context.Background(), &elbv2.GetAppELBDetailRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
			BLBID: "haproxy-192_168_1_2-8080",
		})
		if err == nil {
			t.Errorf("GetAppELBDetail() error = %v, wantErr %v", err, true)
		}
	})
}

func Test_adaptorT_GetEncryptAccountID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.GetEncryptAccountRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		_, err := a.GetEncryptAccountID(context.Background(), req)
		if err != nil {
			t.Errorf("GetEncryptAccountID() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_CreateAppServerGroup(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.CreateServerGroupRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("CreateAppServerGroup() did not panic")
			}
		}()
		_, _ = a.CreateAppServerGroup(context.Background(), req)
	})
}

func Test_adaptorT_DeleteAppServerGroup(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.DeleteServerGroupRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("DeleteAppServerGroup() did not panic")
			}
		}()
		_, _ = a.DeleteAppServerGroup(context.Background(), req)
	})
}

func Test_adaptorT_UpdateAppServerGroup(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.UpdateServerGroupRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("UpdateAppServerGroup() did not panic")
			}
		}()
		_, _ = a.UpdateAppServerGroup(context.Background(), req)
	})
}

func Test_adaptorT_ListAppServerGroup(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.ListServerGroupRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("ListAppServerGroup() did not panic")
			}
		}()
		_, _ = a.ListAppServerGroup(context.Background(), req)
	})
}

func Test_adaptorT_CreateAppServerGroupPort(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.CommonServerGroupPortRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("CreateAppServerGroupPort() did not panic")
			}
		}()
		_, _ = a.CreateAppServerGroupPort(context.Background(), req)
	})
}

func Test_adaptorT_UpdateAppServerGroupPort(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.CommonServerGroupPortRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("UpdateAppServerGroupPort() did not panic")
			}
		}()
		_, _ = a.UpdateAppServerGroupPort(context.Background(), req)
	})
}

func Test_adaptorT_DeleteAppServerGroupPort(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.DeleteServerGroupPortRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("DeleteAppServerGroupPort() did not panic")
			}
		}()
		_, _ = a.DeleteAppServerGroupPort(context.Background(), req)
	})
}

func Test_adaptorT_CreateAppBLBRs(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.CommonAppBlbRsRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("CreateAppBLBRs() did not panic")
			}
		}()
		_, _ = a.CreateAppBLBRs(context.Background(), req)
	})
}

func Test_adaptorT_UpdateAppBLBRs(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.CommonAppBlbRsRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("UpdateAppBLBRs() did not panic")
			}
		}()
		_, _ = a.UpdateAppBLBRs(context.Background(), req)
	})
}

func Test_adaptorT_ListAppBLBRs(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.ListAppBlbRsRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("ListAppBLBRs() did not panic")
			}
		}()
		_, _ = a.ListAppBLBRs(context.Background(), req)
	})
}

func Test_adaptorT_DeleteAppBLBRs(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.DeleteAppBlbRsRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("DeleteAppBLBRs() did not panic")
			}
		}()
		_, _ = a.DeleteAppBLBRs(context.Background(), req)
	})
}

func Test_adaptorT_ListAppBLBRsMount(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.CommonAppBlbRsListRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("ListAppBLBRsMount() did not panic")
			}
		}()
		_, _ = a.ListAppBLBRsMount(context.Background(), req)
	})
}

func Test_adaptorT_ListAppBLBRsUnmount(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.CommonAppBlbRsListRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("ListAppBLBRsUnmount() did not panic")
			}
		}()
		_, _ = a.ListAppBLBRsUnmount(context.Background(), req)
	})
}

func Test_adaptorT_CreateAppTCPListener(t *testing.T) {
	aErr := GetDbstackLbAdaptor(NewMockSdk(true, false))
	a := GetDbstackLbAdaptor(NewMockSdk(false, false))
	req := &elbv2.CreateAppTCPListenerRequest{
		Auth: &common.Authentication{
			TransactionId: "transaction-id",
		},
	}
	t.Run("invalid-id", func(t *testing.T) {
		req.BlbID = "invalid-id"
		_, err := a.CreateAppTCPListener(context.Background(), req)
		if err == nil {
			t.Errorf("CreateAppTCPListener() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("error", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		_, err := aErr.CreateAppTCPListener(context.Background(), req)
		if err == nil {
			t.Errorf("CreateAppTCPListener() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("success", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		resp, err := a.CreateAppTCPListener(context.Background(), req)
		if err != nil {
			t.Errorf("CreateAppTCPListener() error = %v, wantErr %v", err, false)
		}
		if resp == nil {
			t.Errorf("CreateAppTCPListener() resp = %v, wantResp not nil", resp)
		}
	})
	t.Run("not found", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_2-8080"
		_, err := a.CreateAppTCPListener(context.Background(), req)
		if err == nil {
			t.Errorf("CreateAppTCPListener() error = %v, wantErr %v", err, true)
		}
	})
}

func Test_adaptorT_UpdateAppTCPListener(t *testing.T) {
	aErr := GetDbstackLbAdaptor(NewMockSdk(true, false))
	a := GetDbstackLbAdaptor(NewMockSdk(false, false))
	req := &elbv2.CommonAppTCPListenerRequest{
		Auth: &common.Authentication{
			TransactionId: "transaction-id",
		},
	}
	t.Run("invalid-id", func(t *testing.T) {
		req.BlbID = "invalid-id"
		_, err := a.UpdateAppTCPListener(context.Background(), req)
		if err == nil {
			t.Errorf("UpdateAppTCPListener() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("error", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		_, err := aErr.UpdateAppTCPListener(context.Background(), req)
		if err == nil {
			t.Errorf("UpdateAppTCPListener() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("success", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		_, err := a.UpdateAppTCPListener(context.Background(), req)
		if err != nil {
			t.Errorf("UpdateAppTCPListener() error = %v, wantErr %v", err, false)
		}
	})
	t.Run("not found", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_2-8080"
		_, err := a.UpdateAppTCPListener(context.Background(), req)
		if err == nil {
			t.Errorf("UpdateAppTCPListener() error = %v, wantErr %v", err, true)
		}
	})
}

func Test_adaptorT_ListAppTCPListener(t *testing.T) {
	aErr := GetDbstackLbAdaptor(NewMockSdk(true, false))
	a := GetDbstackLbAdaptor(NewMockSdk(false, false))
	req := &elbv2.CommonListAppListenerRequest{
		Auth: &common.Authentication{
			TransactionId: "transaction-id",
		},
	}
	t.Run("invalid-id", func(t *testing.T) {
		req.BlbID = "invalid-id"
		_, err := a.ListAppTCPListener(context.Background(), req)
		if err == nil {
			t.Errorf("ListAppTCPListener() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("error", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		_, err := aErr.ListAppTCPListener(context.Background(), req)
		if err == nil {
			t.Errorf("ListAppTCPListener() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("success", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		resp, err := a.ListAppTCPListener(context.Background(), req)
		if err != nil {
			t.Errorf("ListAppTCPListener() error = %v, wantErr %v", err, false)
		}
		if resp == nil {
			t.Errorf("ListAppTCPListener() resp = %v, wantResp not nil", resp)
		}
		if len(resp.ListenerList) != 1 || resp.ListenerList[0].ListenerPort != 8080 {
			t.Errorf("ListAppTCPListener() resp.ListenerList = %v, want haproxy-192_168_1_1-8080", resp.ListenerList)
		}
	})
	t.Run("not found", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_2-8080"
		_, err := a.ListAppTCPListener(context.Background(), req)
		if err == nil {
			t.Errorf("ListAppTCPListener() error = %v, wantErr %v", err, true)
		}
	})
}

func Test_adaptorT_CreateAppHTTPListener(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.CommonAppHTTPListenerRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("DeleteAppBLBRs() did not panic")
			}
		}()
		_, _ = a.CreateAppHTTPListener(context.Background(), req)
	})
}

func Test_adaptorT_UpdateAppHTTPListener(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.CommonAppHTTPListenerRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("UpdateAppHTTPListener() did not panic")
			}
		}()
		_, _ = a.UpdateAppHTTPListener(context.Background(), req)
	})
}

func Test_adaptorT_ListAppHTTPListener(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.CommonListAppListenerRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("ListAppHTTPListener() did not panic")
			}
		}()
		_, _ = a.ListAppHTTPListener(context.Background(), req)
	})
}

func Test_adaptorT_DeleteAppListener(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.DeleteAppListenerRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
			BlbID: "haproxy-192_168_1_1-8080",
		}
		_, err := a.DeleteAppListener(context.Background(), req)
		if err != nil {
			t.Errorf("UpdateAppELB() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_CreateAppPolicys(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.CreateAppPolicysRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		_, err := a.CreateAppPolicys(context.Background(), req)
		if err != nil {
			t.Errorf("CreateAppPolicys() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_ListAppPolicys(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.ListAppPolicysRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		_, err := a.ListAppPolicys(context.Background(), req)
		if err != nil {
			t.Errorf("ListAppPolicys() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_DeleteAppPolicys(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.DeleteAppPolicysRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		_, err := a.DeleteAppPolicys(context.Background(), req)
		if err != nil {
			t.Errorf("DeleteAppPolicys() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_CreateAppIPGroup(t *testing.T) {
	aErr := GetDbstackLbAdaptor(NewMockSdk(true, false))
	a := GetDbstackLbAdaptor(NewMockSdk(false, false))
	req := &elbv2.CreateAppIPGroupRequest{
		Auth: &common.Authentication{
			TransactionId: "transaction-id",
		},
	}
	t.Run("invalid-id", func(t *testing.T) {
		req.BlbID = "invalid-id"
		_, err := a.CreateAppIPGroup(context.Background(), req)
		if err == nil {
			t.Errorf("CreateAppIPGroup() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("error", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		_, err := aErr.CreateAppIPGroup(context.Background(), req)
		if err == nil {
			t.Errorf("CreateAppIPGroup() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("success", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		resp, err := a.CreateAppIPGroup(context.Background(), req)
		if err != nil {
			t.Errorf("CreateAppIPGroup() error = %v, wantErr %v", err, false)
		}
		if resp == nil {
			t.Errorf("CreateAppIPGroup() resp = %v, wantResp not nil", resp)
		}
		if resp.ID != "ipgroup-haproxy-192_168_1_1-8080" {
			t.Errorf("CreateAppIPGroup() resp.ID = %v, want ipgroup-haproxy-192_168_1_1-8080", resp.ID)
		}
	})
	t.Run("not found", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_2-8080"
		_, err := a.CreateAppIPGroup(context.Background(), req)
		if err == nil {
			t.Errorf("CreateAppIPGroup() error = %v, wantErr %v", err, true)
		}
	})
}

func Test_adaptorT_DeleteAppIPGroup(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.DeleteAppIPGroupRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
			BlbID: "haproxy-192_168_1_1-8080",
		}
		_, err := a.DeleteAppIPGroup(context.Background(), req)
		if err != nil {
			t.Errorf("UpdateAppELB() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_UpdateAppIPGroup(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.UpdateAppIPGroupRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
			BlbID: "haproxy-192_168_1_1-8080",
		}
		_, err := a.UpdateAppIPGroup(context.Background(), req)
		if err != nil {
			t.Errorf("UpdateAppELB() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_ListAppIPGroup(t *testing.T) {
	aErr := GetDbstackLbAdaptor(NewMockSdk(true, false))
	a := GetDbstackLbAdaptor(NewMockSdk(false, false))
	req := &elbv2.ListAppIPGroupRequest{
		Auth: &common.Authentication{
			TransactionId: "transaction-id",
		},
	}
	t.Run("invalid-id", func(t *testing.T) {
		req.BlbID = "invalid-id"
		_, err := a.ListAppIPGroup(context.Background(), req)
		if err == nil {
			t.Errorf("ListAppIPGroup() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("error", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		_, err := aErr.ListAppIPGroup(context.Background(), req)
		if err == nil {
			t.Errorf("ListAppIPGroup() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("success", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		resp, err := a.ListAppIPGroup(context.Background(), req)
		if err != nil {
			t.Errorf("ListAppIPGroup() error = %v, wantErr %v", err, false)
		}
		if resp == nil {
			t.Errorf("ListAppIPGroup() resp = %v, wantResp not nil", resp)
		}
		if len(resp.AppIPGroupList) != 1 || resp.AppIPGroupList[0].ID != "ipgroup-haproxy-192_168_1_1-8080" {
			t.Errorf("ListAppIPGroup() resp.IPGroupList = %v, want ipgroup-haproxy-192_168_1_1-8080", resp.AppIPGroupList)
		}
	})
	t.Run("not found", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_2-8080"
		_, err := a.ListAppIPGroup(context.Background(), req)
		if err == nil {
			t.Errorf("ListAppIPGroup() error = %v, wantErr %v", err, true)
		}
	})
}

func Test_adaptorT_CreateAppIPGroupPolicy(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.CommonAppIPGroupPolicyRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		_, err := a.CreateAppIPGroupPolicy(context.Background(), req)
		if err != nil {
			t.Errorf("CreateAppIPGroupPolicy() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_UpdateAppIPGroupPolicy(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.CommonAppIPGroupPolicyRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		_, err := a.UpdateAppIPGroupPolicy(context.Background(), req)
		if err != nil {
			t.Errorf("UpdateAppIPGroupPolicy() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_DeleteAppIPGroupPolicy(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.DeleteAppIPGroupPolicyRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
		}
		_, err := a.DeleteAppIPGroupPolicy(context.Background(), req)
		if err != nil {
			t.Errorf("DeleteAppIPGroupPolicy() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_CreateAppIPGroupMember(t *testing.T) {
	aErr := GetDbstackLbAdaptor(NewMockSdk(true, false))
	a := GetDbstackLbAdaptor(NewMockSdk(false, false))
	req := &elbv2.CommonAppIPGroupMemberRequest{
		Auth: &common.Authentication{
			TransactionId: "transaction-id",
		},
		MemberList: []*elbv2.MemberList{
			{
				IP:   "*********",
				Port: 1233,
			},
			{
				IP:   "*********",
				Port: 1234,
			},
		},
		BlbID: "haproxy-192_168_1_1-8080",
	}
	t.Run("invalid-id", func(t *testing.T) {
		req.BlbID = "invalid-id"
		_, err := a.CreateAppIPGroupMember(context.Background(), req)
		if err == nil {
			t.Errorf("CreateAppIPGroupMember() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("error", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		_, err := aErr.CreateAppIPGroupMember(context.Background(), req)
		if err == nil {
			t.Errorf("CreateAppIPGroupMember() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("success", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		resp, err := a.CreateAppIPGroupMember(context.Background(), req)
		if err != nil {
			t.Errorf("CreateAppIPGroupMember() error = %v, wantErr %v", err, false)
		}
		if resp == nil {
			t.Errorf("CreateAppIPGroupMember() resp = %v, wantResp not nil", resp)
		}
	})
	t.Run("not found", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_2-8080"
		_, err := a.CreateAppIPGroupMember(context.Background(), req)
		if err == nil {
			t.Errorf("CreateAppIPGroupMember() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("add nothing", func(t *testing.T) {
		req.MemberList = []*elbv2.MemberList{
			{
				IP:   "*********",
				Port: 10234,
			},
			{
				IP:   "*********",
				Port: 10234,
			},
		}
		req.BlbID = "haproxy-192_168_1_1-8080"
		_, err := a.CreateAppIPGroupMember(context.Background(), req)
		if err != nil {
			t.Errorf("CreateAppIPGroupMember() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_UpdateAppIPGroupMember(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		a := GetDbstackLbAdaptor(NewMockSdk(false, false))
		req := &elbv2.CommonAppIPGroupMemberRequest{
			Auth: &common.Authentication{
				TransactionId: "transaction-id",
			},
			BlbID: "haproxy-192_168_1_1-8080",
		}
		_, err := a.UpdateAppIPGroupMember(context.Background(), req)
		if err != nil {
			t.Errorf("UpdateAppIPGroupMember() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_DeleteAppIPGroupMember(t *testing.T) {
	aErr := GetDbstackLbAdaptor(NewMockSdk(true, false))
	a := GetDbstackLbAdaptor(NewMockSdk(false, false))
	req := &elbv2.DeleteAppIPGroupMemberRequest{
		Auth: &common.Authentication{
			TransactionId: "transaction-id",
		},
		BlbID: "haproxy-192_168_1_1-8080",
		MemberIDList: []string{
			"rs-172_3_4_1-10234",
		},
	}
	t.Run("invalid-id", func(t *testing.T) {
		req.BlbID = "invalid-id"
		_, err := a.DeleteAppIPGroupMember(context.Background(), req)
		if err == nil {
			t.Errorf("DeleteAppIPGroupMember() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("error", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		_, err := aErr.DeleteAppIPGroupMember(context.Background(), req)
		if err == nil {
			t.Errorf("DeleteAppIPGroupMember() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("success", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		_, err := a.DeleteAppIPGroupMember(context.Background(), req)
		if err != nil {
			t.Errorf("DeleteAppIPGroupMember() error = %v, wantErr %v", err, false)
		}
	})
	t.Run("not found", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_2-8080"
		_, err := a.DeleteAppIPGroupMember(context.Background(), req)
		if err == nil {
			t.Errorf("DeleteAppIPGroupMember() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("delete noting", func(t *testing.T) {
		req.MemberIDList = []string{
			"rs-172_1_1_1-10234",
		}
		req.BlbID = "haproxy-192_168_1_1-8080"
		_, err := a.DeleteAppIPGroupMember(context.Background(), req)
		if err != nil {
			t.Errorf("DeleteAppIPGroupMember() error = %v, wantErr %v", err, false)
		}
	})
}

func Test_adaptorT_ListAppIPGroupMember(t *testing.T) {
	aErr := GetDbstackLbAdaptor(NewMockSdk(true, false))
	a := GetDbstackLbAdaptor(NewMockSdk(false, false))
	req := &elbv2.ListAppIPGroupMemberRequest{
		Auth: &common.Authentication{
			TransactionId: "transaction-id",
		},
		BlbID: "haproxy-192_168_1_1-8080",
	}
	t.Run("invalid-id", func(t *testing.T) {
		req.BlbID = "invalid-id"
		_, err := a.ListAppIPGroupMember(context.Background(), req)
		if err == nil {
			t.Errorf("ListAppIPGroupMember() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("error", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		_, err := aErr.ListAppIPGroupMember(context.Background(), req)
		if err == nil {
			t.Errorf("ListAppIPGroupMember() error = %v, wantErr %v", err, true)
		}
	})
	t.Run("success", func(t *testing.T) {
		req.BlbID = "haproxy-192_168_1_1-8080"
		resp, err := a.ListAppIPGroupMember(context.Background(), req)
		if err != nil {
			t.Errorf("ListAppIPGroupMember() error = %v, wantErr %v", err, false)
		}
		if resp == nil {
			t.Errorf("ListAppIPGroupMember() resp = %v, wantResp not nil", resp)
		}
		if len(resp.MemberList) != 2 && resp.MemberList[0].MemberID != "rs-172_3_2_1-10234" {
			t.Errorf("ListAppIPGroupMember() resp.MemberList = %v, want rs-172_3_2_1-10234", resp.MemberList)
		}
	})
}
