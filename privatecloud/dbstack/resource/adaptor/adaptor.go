package adaptor

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
)

type AdaptorT struct {
}

func (a AdaptorT) CreateCds(ctx context.Context, req *bcc.CdsCreateRequest) (rsp *bcc.CdsCreateResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) AttachCds(ctx context.Context, req *bcc.CdsAttachRequest) (rsp *bcc.CdsAttachResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) DetachCds(ctx context.Context, req *bcc.CdsDetachRequest) (rsp *bcc.CdsDetachResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) DeleteCds(ctx context.Context, req *bcc.CdsDeleteRequest) (rsp *bcc.CdsDeleteResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) GetCdsDetail(ctx context.Context, req *bcc.GetCdsDetailRequest) (rsp *bcc.GetCdsDetailResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) ShowServer(ctx context.Context, req *bcc.ShowServerRequest) (rsp *bcc.ShowServerResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) ShowTransaction(ctx context.Context, req *bcc.ShowTransactionRequest) (rsp *bcc.ShowTransactionResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) SetShowTransactionForUt(ctx context.Context, req *bcc.SetTransactionRequest) (rsp *bcc.SetTransactionResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) ExchangeId(ctx context.Context, req *bcc.ExchangeIdRequest) (rsp *bcc.ExchangeIdResponse, err error) {
	var mappings []*bcc.Mappings
	for _, ins := range req.InstanceIds {
		mappings = append(mappings, &bcc.Mappings{
			Id:   ins,
			Uuid: ins,
		})
	}
	return &bcc.ExchangeIdResponse{
		Mappings: mappings,
	}, nil
}

func (a AdaptorT) BatchCreateServers(ctx context.Context, req *bcc.BatchCreateServersRequest) (rsp *bcc.BatchCreateServersResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) BatchDeleteServers(ctx context.Context, req *bcc.BatchDeleteServersRequest) (rsp *bcc.BatchDeleteServersResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) ShowOrder(ctx context.Context, req *bcc.ShowOrderRequest) (rsp *bcc.ShowOrderResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) ShowInstanceInfo(ctx context.Context, req *bcc.ShowInstanceRequest) (rsp *bcc.ShowInstanceResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) ResizeInstance(ctx context.Context, req *bcc.ResizeInstanceRequest) (rsp *bcc.ResizeInstanceResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) InstanceAttachedCdsList(ctx context.Context, req *bcc.CdsMountListRequest) (rsp *bcc.CdsMountListResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) ResizeCds(ctx context.Context, req *bcc.CdsResizeRequest) (rsp *bcc.CdsResizeResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) CreateDeploySet(ctx context.Context, req *bcc.CreateDeploySetRequest) (rsp *bcc.DeploySetIdsResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) ShowDeploySet(ctx context.Context, req *bcc.ShowDeploySetRequest) (rsp *bcc.ShowDeploySetResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) GetCdsStockWithZone(ctx context.Context, req *bcc.GetCdsStockRequest) (rsp *bcc.GetCdsStockResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (a AdaptorT) SubnetDetail(ctx context.Context, req *bcc.GetSubnetDetailRequest) (rsp *bcc.GetSubnetDetailResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func GetDbstackOpenstackAdaptor() bcc.OpenStackService {
	return &AdaptorT{}
}
