package adaptor

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"testing"
)

func TestAdaptorT_ExchangeId(t *testing.T) {
	a := GetDbstackOpenstackAdaptor()
	resp, err := a.ExchangeId(context.Background(), &bcc.ExchangeIdRequest{
		InstanceIds: []string{"1", "2"},
	})
	if err != nil {
		t.<PERSON><PERSON>("ExchangeId failed: %v", err)
	}
	if len(resp.Mappings) != 2 {
		t.<PERSON><PERSON><PERSON>("ExchangeId failed: %v", resp.Mappings)
	}
}

func TestAdaptorT_ShowServer(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.<PERSON>rf("The code did not panic")
			}
		}()
		_, _ = a.ShowServer(context.Background(), &bcc.ShowServerRequest{})
	})
}

func TestAdaptorT_ShowTransaction(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.ShowTransaction(context.Background(), &bcc.ShowTransactionRequest{})
	})
}

func TestAdaptorT_SetShowTransactionForUt(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.SetShowTransactionForUt(context.Background(), &bcc.SetTransactionRequest{})
	})
}

func TestAdaptorT_BatchCreateServers(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.BatchCreateServers(context.Background(), &bcc.BatchCreateServersRequest{})
	})
}

func TestAdaptorT_BatchDeleteServers(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.BatchDeleteServers(context.Background(), &bcc.BatchDeleteServersRequest{})
	})
}

func TestAdaptorT_ShowOrder(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.ShowOrder(context.Background(), &bcc.ShowOrderRequest{})
	})
}

func TestAdaptorT_ShowInstanceInfo(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.ShowInstanceInfo(context.Background(), &bcc.ShowInstanceRequest{})
	})
}

func TestAdaptorT_ResizeInstance(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.ResizeInstance(context.Background(), &bcc.ResizeInstanceRequest{})
	})
}

func TestAdaptorT_InstanceAttachedCdsList(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.InstanceAttachedCdsList(context.Background(), &bcc.CdsMountListRequest{})
	})
}

func TestAdaptorT_ResizeCds(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.ResizeCds(context.Background(), &bcc.CdsResizeRequest{})
	})
}

func TestAdaptorT_CreateDeploySet(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.CreateDeploySet(context.Background(), &bcc.CreateDeploySetRequest{})
	})
}

func TestAdaptorT_ShowDeploySet(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.ShowDeploySet(context.Background(), &bcc.ShowDeploySetRequest{})
	})
}

func TestAdaptorT_GetCdsStockWithZone(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.GetCdsStockWithZone(context.Background(), &bcc.GetCdsStockRequest{})
	})
}

func TestAdaptorT_SubnetDetail(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.SubnetDetail(context.Background(), &bcc.GetSubnetDetailRequest{})
	})
}

func TestAdaptorT_AttachCds(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.AttachCds(context.Background(), &bcc.CdsAttachRequest{})
	})
}

func TestAdaptorT_DetachCds(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.DetachCds(context.Background(), &bcc.CdsDetachRequest{})
	})
}

func TestAdaptorT_DeleteCds(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.DeleteCds(context.Background(), &bcc.CdsDeleteRequest{})
	})
}

func TestAdaptorT_GetCdsDetail(t *testing.T) {
	t.Run("panic", func(t *testing.T) {
		a := AdaptorT{}
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = a.GetCdsDetail(context.Background(), &bcc.GetCdsDetailRequest{})
	})
}
