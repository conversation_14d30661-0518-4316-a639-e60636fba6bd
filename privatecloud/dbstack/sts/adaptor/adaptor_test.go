package adaptor

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"strings"
	"testing"
)

func TestAdaptorT_GetAssumeRole(t *testing.T) {
	a := NewSTS()
	resp, err := a.GetAssumeRole(context.Background(), &sts.GetAssumeRoleRequest{
		IamUserId:     "test",
		TransactionId: "test",
	})
	if err != nil {
		t.Error(err)
	}
	if !strings.HasPrefix(resp.Token.Id, PrivateCloudTokenPrefix) {
		t.<PERSON>rrorf("expect prefix %s, got %s", PrivateCloudTokenPrefix, resp.Token.Id)
	}
}

func TestAdaptorT_GetEncryptResourceAccountId(t *testing.T) {
	a := NewSTS()
	resp, err := a.GetEncryptResourceAccountId(context.Background())
	if err != nil {
		t.Error(err)
	}
	if resp.EncryptAccountId != "dbstack-encrypt-account-id" {
		t.<PERSON><PERSON><PERSON>("expect dbstack-encrypt-account-id, got %s", resp.EncryptAccountId)
	}
	if resp.ResourceAk != "dbstack-resource-ak" {
		t.Errorf("expect dbstack-resource-ak, got %s", resp.ResourceAk)
	}
}

func TestAdaptorT_GetOpenApiAuth(t *testing.T) {
	a := NewSTS()
	resp, err := a.GetOpenApiAuth(context.Background(), &sts.GetOpenApiAuthRequest{
		TransactionId:       "test",
		IamUserId:           "test",
		NeedResourceAccount: false,
	})
	if err != nil {
		t.Error(err)
	}
	if resp.Auth.Credential.SessionToken != "scs-private|2a5d355806e24876ab025d14529d26e5|test" {
		t.Errorf("expect scs-private|2a5d355806e24876ab025d14529d26e5|test, got %s", resp.Auth.Credential.SessionToken)
	}
	if resp.Auth.ResourceAccount.EncryptAccountId != "scs-private|2a5d355806e24876ab025d14529d26e5|test" {
		t.Errorf("expect scs-private|2a5d355806e24876ab025d14529d26e5|test, got %s", resp.Auth.ResourceAccount.EncryptAccountId)
	}
}
