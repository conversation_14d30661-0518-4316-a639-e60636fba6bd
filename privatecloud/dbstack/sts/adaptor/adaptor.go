package adaptor

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
)

type AdaptorT struct {
}

const PrivateCloudTokenPrefix = "scs-private|2a5d355806e24876ab025d14529d26e5"

func (l *AdaptorT) GetAssumeRole(ctx context.Context, req *sts.GetAssumeRoleRequest) (rsp *sts.GetAssumeRoleResponse, err error) {
	uid := req.IamUserId
	return &sts.GetAssumeRoleResponse{
		AccessKeyId:     "dbstack-ak",
		SecretAccessKey: "dbstack-sk",
		SessionToken:    "dbstack-sts",
		CreateTime:      time.Now().Format("2006-01-02T15:04:05Z"),
		Expiration:      time.Now().Add(time.Hour).Format("2006-01-02T15:04:05Z"),
		UserId:          req.IamUserId,
		RoleId:          "dbstack-role",
		Token:           &sts.Token{Id: fmt.Sprintf("%s|%s", PrivateCloudTokenPrefix, uid)},
		Code:            "SUCCESS",
		Message:         "SUCCESS",
		RequestId:       req.TransactionId,
	}, nil
}

func (l *AdaptorT) GetEncryptResourceAccountId(ctx context.Context) (rsp *sts.EncryptResourceAccountIdResponse, err error) {
	return &sts.EncryptResourceAccountIdResponse{
		EncryptAccountId: "dbstack-encrypt-account-id",
		ResourceAk:       "dbstack-resource-ak",
	}, nil
}

func (l *AdaptorT) GetOpenApiAuth(ctx context.Context, req *sts.GetOpenApiAuthRequest) (rsp *sts.GetOpenApiAuthResponse, err error) {
	uid := req.IamUserId
	return &sts.GetOpenApiAuthResponse{Auth: &common.Authentication{
		IamUserId:     uid,
		TransactionId: base_utils.GetReqID(ctx),
		Credential: &common.Credential{
			Ak:           "dbstack-ak",
			Sk:           "dbstack-sk",
			SessionToken: fmt.Sprintf("%s|%s", PrivateCloudTokenPrefix, uid),
		},
		ResourceAccount: &common.ResourceAccount{
			ResourceAk:       "dbstack-resource-ak",
			EncryptAccountId: fmt.Sprintf("%s|%s", PrivateCloudTokenPrefix, uid),
		},
	}}, nil
}

func NewSTS() sts.StsService {
	return &AdaptorT{}
}
