package adaptor

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/dns"
)

type adaptorT struct {
}

func (a adaptorT) CreateDomain(ctx context.Context, req *dns.DNSCreateRequest) (rsp *dns.DNSCreateResponse, err error) {
	return &dns.DNSCreateResponse{
		ID: "dbstack-has-no-dns",
	}, nil
}

func (a adaptorT) DeleteDomain(ctx context.Context, req *dns.DNSDeleteRequest) (err error) {
	return nil
}

func (a adaptorT) UpdateDomain(ctx context.Context, req *dns.DNSUpdateRequest) (rsp *dns.DNSUpdateResponse, err error) {
	return &dns.DNSUpdateResponse{
		ID: "dbstack-has-no-dns",
	}, nil
}

func (a adaptorT) ListDomain(ctx context.Context, req *dns.DNSListRequest) (rsp *dns.DNSListResponse, err error) {
	return &dns.DNSListResponse{
		Records: []*dns.DNSRecord{},
	}, nil
}

func (a adaptorT) CreatePnetDomain(ctx context.Context, req *dns.PnetResquest) (err error) {
	return nil
}

func (a adaptorT) DeletePnetDomain(ctx context.Context, req *dns.PnetResquest) (err error) {
	return nil
}

func GetDbstackDnsAdaptor() dns.DNSService {
	return &adaptorT{}
}
