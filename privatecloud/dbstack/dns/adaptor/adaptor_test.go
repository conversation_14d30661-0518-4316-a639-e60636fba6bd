package adaptor

import (
	"testing"
)

func Test_adaptorT_CreateDomain(t *testing.T) {
	a := GetDbstackDnsAdaptor()
	_, err := a.CreateDomain(nil, nil)
	if err != nil {
		t.<PERSON><PERSON>rf("CreateDomain() error = %v", err)
		return
	}
}

func Test_adaptorT_DeleteDomain(t *testing.T) {
	a := GetDbstackDnsAdaptor()
	err := a.DeleteDomain(nil, nil)
	if err != nil {
		t.<PERSON>rrorf("DeleteDomain() error = %v", err)
		return
	}
}

func Test_adaptorT_UpdateDomain(t *testing.T) {
	a := GetDbstackDnsAdaptor()
	_, err := a.UpdateDomain(nil, nil)
	if err != nil {
		t.Errorf("UpdateDomain() error = %v", err)
		return
	}
}

func Test_adaptorT_ListDomain(t *testing.T) {
	a := GetDbstackDnsAdaptor()
	_, err := a.ListDomain(nil, nil)
	if err != nil {
		t.<PERSON><PERSON>("ListDomain() error = %v", err)
		return
	}
}

func Test_adaptorT_CreatePnetDomain(t *testing.T) {
	a := GetDbstackDnsAdaptor()
	err := a.CreatePnetDomain(nil, nil)
	if err != nil {
		t.Errorf("CreatePnetDomain() error = %v", err)
		return
	}
}

func Test_adaptorT_DeletePnetDomain(t *testing.T) {
	a := GetDbstackDnsAdaptor()
	err := a.DeletePnetDomain(nil, nil)
	if err != nil {
		t.Errorf("DeletePnetDomain() error = %v", err)
		return
	}
}
