/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建BCC 资源
*/

package gmaster

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/gmaster"
)

type (
	Redis = gmaster.Redis

	Proxy = gmaster.Proxy
)

type AddNodesParams struct {
	AppGroupID string
	AppID      string
	UserID     string
	Nodes      []*global_model.AppGroupRedis
}

type DeleteNodesParams struct {
	AppGroupID string
	AppID      string
	UserID     string
	NodeIds    []string
}

type GetNodesParams struct {
	AppGroupID    string
	AppID         string
	UserID        string
	ShardGlobalID string
	WithoutLock   bool
}

type AddProxiesParams struct {
	AppGroupID string
	AppID      string
	UserID     string
	Proxies    []*global_model.AppGroupProxy
}

type DeleteProxiesParams struct {
	AppGroupID string
	AppID      string
	UserID     string
	ProxyIDs   []string
}

type GetProxiesParams struct {
	AppGroupID string
	AppID      string
	UserID     string
}

type AddShardsParams struct {
	AppGroupID       string
	AppID            string
	UserID           string
	TargetShardCount int
}

type GetShardsParams struct {
	AppGroupID string
	AppID      string
	UserID     string
}

type DeleteShardsParams struct {
	AppGroupID string
	AppID      string
	UserID     string
	ShardIDs   []string `json:"shardIds"`
}

type LocalFailoverParams struct {
	AppGroupID     string
	AppID          string
	UserID         string
	ShardGlobalID  string
	NewNodeID      string
	IsManualSwitch bool
	UseForbidWrite bool
	SyncOffsetDiff int64
}

type SlaveOfMasterParams struct {
	AppGroupID    string
	AppID         string
	UserID        string
	ShardGlobalID string
	NewNodeID     string
}

type UpdateInnerSecurityParams struct {
	AppGroupID  string
	AppID       string
	UserID      string
	ToAddIpList []string `json:"to_add_ip_list"`
	ToDelIpList []string `json:"to_del_ip_list"`
	PortList    []int32  `json:"port_list"`
}

type UpdateModifyStatusStageParams struct {
	AppID      string
	AppGroupID string
	UserID     string
	ID         int
	Stage      string
	Progress   string
	Version    int
}

type AddModifyStatusParams = gmaster.AddModifyStatusRequest

type ListCacheGroupDetailRes = gmaster.ListCacheGroupDetailResponse

type SlaveOfMasterRes = gmaster.SlaveOfMasterResponse

type ApplyTemplateRequest struct {
	UserId           string               `json:"-"`
	TemplateID       int64                `json:"templateId"`
	TemplateShowID   string               `json:"templateShowId"`
	CacheClusterList []*gmaster.ApplyItem `json:"cacheClusterList"`
	Extra            int                  `json:"extra"`
	RebootType       int                  `json:"rebootType"` // 0 不重启 1 维护时间重启 2 立即重启
	Parameters       []*gmaster.Param     `json:"parameters"`
}

type InitStandaloneTopoReq struct {
	AppID          string `json:"appId"`
	UserID         string `json:"-"`
	AppGroupID     string `json:"appGroupId"`
	ShardID        string `json:"shardId"`
	NewMasterID    string `json:"newMasterId"`
	NewMasterFixIP string `json:"newMasterFixIp"`
	NewMasterPort  int    `json:"newMasterPort"`
}

type UpdateBnsServiceParams struct {
	AppID         string
	GroupID       string
	OldBnsService string
	NewBnsService string
}

type GlobalMasterService interface {
	AddNodes(ctx context.Context, params *AddNodesParams) error
	DeleteNodes(ctx context.Context, params *DeleteNodesParams) error
	GetNodes(ctx context.Context, params *GetNodesParams) ([]*global_model.AppGroupRedis, error)
	AddProxies(ctx context.Context, params *AddProxiesParams) error
	DeleteProxies(ctx context.Context, params *DeleteProxiesParams) error
	GetProxies(ctx context.Context, params *GetProxiesParams) ([]*global_model.AppGroupProxy, error)
	AddShards(ctx context.Context, params *AddShardsParams) error
	DeleteShards(ctx context.Context, params *DeleteShardsParams) error
	GetShards(ctx context.Context, params *GetShardsParams) ([]*global_model.AppGroupShard, error)
	LocalFailover(ctx context.Context, params *LocalFailoverParams) (*gmaster.LocalFailoverResponse, error)
	SlaveOfMaster(ctx context.Context, params *SlaveOfMasterParams) (*SlaveOfMasterRes, error)
	UpdateInnerSecurity(ctx context.Context, params *UpdateInnerSecurityParams) error
	GetModifyStatus(ctx context.Context, appGroupID string, includeCompleted bool, userID string) ([]*gmaster.ModifyStatus, error)
	UpdateModifyStatusStage(ctx context.Context, params *UpdateModifyStatusStageParams) error
	AddModifyStatus(ctx context.Context, param *AddModifyStatusParams, userID string) error
	GroupStatusCAS(ctx context.Context, appGroupID string, assumes []string, dest string, userID string) error
	ListCacheGroupDetail(ctx context.Context, appGroupID string, userID string) (*ListCacheGroupDetailRes, error)
	UpdateSecurity(ctx context.Context, params *UpdateInnerSecurityParams) error
	ApplyTemplate(ctx context.Context, params *ApplyTemplateRequest) (err error)
	InitStandaloneTopo(ctx context.Context, params *InitStandaloneTopoReq) (err error)
	UpdateBnsService(ctx context.Context, params *UpdateBnsServiceParams) error
}
