package gmaster

import (
	"context"
	"testing"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest/sdkmock"
)

func Test_gmasterOp_SlaveOfMaster(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockGmasterService := sdkmock.NewMockGmasterService(ctrl)
	mockStsService := &sdkmock.MockStsSdk{}
	mockGmasterService.EXPECT().SlaveOfMaster(gomock.Any(), gomock.Any()).Return(&gmaster.SlaveOfMasterResponse{Code: ""}, nil)

	patch := gomonkey.ApplyFunc(compo_utils.GetGMasterToken, func(ctx context.Context, userId string) (string, error) {
		return "", nil
	})
	defer patch.Reset()

	m := &gmasterOp{
		gmasterSdk: mockGmasterService,
		stsSdk:     mockStsService,
	}
	resp, err := m.SlaveOfMaster(context.Background(), &SlaveOfMasterParams{
		AppID:         "24_0_1",
		AppGroupID:    "24_0_1_1",
		ShardGlobalID: "24_0_1_1_1",
		NewNodeID:     "pg_24_0_1_1_1",
		UserID:        "testuser",
	})
	if err != nil {
		t.Errorf("SlaveOfMaster() error = %v", err)
		return
	}
	if resp == nil || resp.Code != "" {
		t.Errorf("SlaveOfMaster() response = %v, want ErrCode = 0", resp)
	}
}

func Test_gmasterOp_LocalFailover(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockGmasterService := sdkmock.NewMockGmasterService(ctrl)
	mockStsService := &sdkmock.MockStsSdk{}
	mockGmasterService.EXPECT().LocalFailover(gomock.Any(), gomock.Any()).Return(&gmaster.LocalFailoverResponse{Code: ""}, nil)

	patch := gomonkey.ApplyFunc(compo_utils.GetGMasterToken, func(ctx context.Context, userId string) (string, error) {
		return "", nil
	})
	defer patch.Reset()

	m := &gmasterOp{
		gmasterSdk: mockGmasterService,
		stsSdk:     mockStsService,
	}
	err := m.LocalFailover(context.Background(), &LocalFailoverParams{
		AppID:          "24_0_1",
		AppGroupID:     "24_0_1_1",
		ShardGlobalID:  "24_0_1_1_1",
		UserID:         "testuser",
		NewNodeID:      "pg_24_0_1_1_1",
		IsManualSwitch: true,
		UseForbidWrite: true,
		SyncOffsetDiff: 0,
	})
	if err != nil {
		t.Errorf("LocalFailover() error = %v", err)
		return
	}
}
