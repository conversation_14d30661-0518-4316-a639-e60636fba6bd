/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建BCC 资源
*/

package gmaster

import (
	"context"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
)

type gmasterOp struct {
	gmasterSdk gmaster.GmasterService
	stsSdk     sts.StsService
}

var (
	initOnce     sync.Once
	gmasterOpObj *gmasterOp
)

func GlobalMasterOp() GlobalMasterService {
	initOnce.Do(func() {
		gmasterOpObj = &gmasterOp{
			gmasterSdk: gmaster.NewDefaultGmasterSdk(),
			stsSdk:     sts.NewDefaultStsSdk(),
		}
	})
	return gmasterOpObj
}

func (m *gmasterOp) AddNodes(ctx context.Context, params *AddNodesParams) error {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &gmaster.AddRedisRequest{
		AppID:      params.AppID,
		AppGroupID: params.AppGroupID,
		Token:      token,
		Nodes:      params.Nodes,
	}
	_, err = m.gmasterSdk.AddNodes(ctx, req)
	return err
}

func (m *gmasterOp) DeleteNodes(ctx context.Context, params *DeleteNodesParams) error {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &gmaster.DeleteRedisRequest{
		AppID:      params.AppID,
		AppGroupID: params.AppGroupID,
		Token:      token,
		NodeIDs:    params.NodeIds,
	}
	_, err = m.gmasterSdk.DeleteNodes(ctx, req)
	return err
}

func (m *gmasterOp) GetNodes(ctx context.Context, params *GetNodesParams) ([]*global_model.AppGroupRedis, error) {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return nil, err
	}
	req := &gmaster.GetRedisRequest{
		AppID:       params.AppID,
		AppGroupID:  params.AppGroupID,
		Token:       token,
		ShardID:     params.ShardGlobalID,
		WithoutLock: params.WithoutLock,
	}
	resp, err := m.gmasterSdk.GetNodes(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.Nodes, nil
}

func (m *gmasterOp) AddProxies(ctx context.Context, params *AddProxiesParams) error {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &gmaster.AddProxyRequest{
		AppID:      params.AppID,
		AppGroupID: params.AppGroupID,
		Token:      token,
		Proxies:    params.Proxies,
	}
	_, err = m.gmasterSdk.AddProxies(ctx, req)
	return err
}

func (m *gmasterOp) DeleteProxies(ctx context.Context, params *DeleteProxiesParams) error {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &gmaster.DeleteProxyRequest{
		AppID:      params.AppID,
		AppGroupID: params.AppGroupID,
		Token:      token,
		ProxyIDs:   params.ProxyIDs,
	}
	_, err = m.gmasterSdk.DeleteProxies(ctx, req)
	return err
}

func (m *gmasterOp) GetProxies(ctx context.Context, params *GetProxiesParams) ([]*global_model.AppGroupProxy, error) {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return nil, err
	}
	req := &gmaster.GetProxyRequest{
		AppID:      params.AppID,
		AppGroupID: params.AppGroupID,
		Token:      token,
	}
	resp, err := m.gmasterSdk.GetProxies(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.Proxies, nil
}

func (m *gmasterOp) LocalFailover(ctx context.Context, params *LocalFailoverParams) (*gmaster.LocalFailoverResponse, error) {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return nil, err
	}
	req := &gmaster.LocalFailoverRequest{
		AppID:          params.AppID,
		AppGroupID:     params.AppGroupID,
		Token:          token,
		ShardID:        params.ShardGlobalID,
		NewNodeID:      params.NewNodeID,
		IsManualSwitch: params.IsManualSwitch,
		UseForbidWrite: params.UseForbidWrite,
		SyncOffsetDiff: params.SyncOffsetDiff,
	}
	resp, err := m.gmasterSdk.LocalFailover(ctx, req)
	return resp, err
}

func (m *gmasterOp) SlaveOfMaster(ctx context.Context, params *SlaveOfMasterParams) (*SlaveOfMasterRes, error) {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return nil, err
	}
	req := &gmaster.SlaveOfMasterRequest{
		AppID:      params.AppID,
		AppGroupID: params.AppGroupID,
		Token:      token,
		ShardID:    params.ShardGlobalID,
		NewNodeID:  params.NewNodeID,
	}
	return m.gmasterSdk.SlaveOfMaster(ctx, req)
}

func (m *gmasterOp) AddShards(ctx context.Context, params *AddShardsParams) error {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &gmaster.AddShardRequest{
		AppID:            params.AppID,
		AppGroupID:       params.AppGroupID,
		Token:            token,
		TargetShardCount: params.TargetShardCount,
	}
	_, err = m.gmasterSdk.AddShards(ctx, req)
	return err
}

func (m *gmasterOp) DeleteShards(ctx context.Context, params *DeleteShardsParams) error {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &gmaster.DeleteShardRequest{
		AppID:      params.AppID,
		AppGroupID: params.AppGroupID,
		Token:      token,
		ShardIDs:   params.ShardIDs,
	}
	_, err = m.gmasterSdk.DeleteShards(ctx, req)
	return err
}

func (m *gmasterOp) GetShards(ctx context.Context, params *GetShardsParams) ([]*global_model.AppGroupShard, error) {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return nil, err
	}
	req := &gmaster.GetShardRequest{
		AppID:      params.AppID,
		AppGroupID: params.AppGroupID,
		Token:      token,
	}
	resp, err := m.gmasterSdk.GetShards(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.Shards, nil
}

func (m *gmasterOp) UpdateInnerSecurity(ctx context.Context, params *UpdateInnerSecurityParams) error {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &gmaster.UpdateInnerSecurityRequest{
		AppID:       params.AppID,
		AppGroupID:  params.AppGroupID,
		Token:       token,
		ToAddIpList: params.ToAddIpList,
		ToDelIpList: params.ToDelIpList,
		PortList:    params.PortList,
	}
	_, err = m.gmasterSdk.UpdateInnerSecurity(ctx, req)
	return err
}

func (m *gmasterOp) GetModifyStatus(ctx context.Context, appGroupID string, includeCompleted bool, userID string) ([]*gmaster.ModifyStatus, error) {
	token, err := compo_utils.GetGMasterToken(ctx, userID)
	if err != nil {
		return nil, err
	}
	req := &gmaster.GetModifyStatusRequest{
		AppGroupID:       appGroupID,
		Token:            token,
		IncludeCompleted: includeCompleted,
	}
	resp, err := m.gmasterSdk.GetModifyStatus(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.ModifyStatuses, nil
}

func (m *gmasterOp) UpdateModifyStatusStage(ctx context.Context, params *UpdateModifyStatusStageParams) error {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &gmaster.UpdateModifyStatusStageRequest{
		AppID:      params.AppID,
		AppGroupID: params.AppGroupID,
		Stage:      params.Stage,
		Progress:   params.Progress,
		Token:      token,
		ID:         params.ID,
		Version:    params.Version,
	}
	_, err = m.gmasterSdk.UpdateModifyStatusStage(ctx, req)
	return err
}

func (m *gmasterOp) AddModifyStatus(ctx context.Context, param *AddModifyStatusParams, userID string) error {
	token, err := compo_utils.GetGMasterToken(ctx, userID)
	if err != nil {
		return err
	}
	param.Token = token
	_, err = m.gmasterSdk.AddModifyStatus(ctx, param)
	return err
}

func (m *gmasterOp) GroupStatusCAS(ctx context.Context, appGroupID string, assumes []string, dest string, userID string) error {
	token, err := compo_utils.GetGMasterToken(ctx, userID)
	if err != nil {
		return err
	}
	req := &gmaster.GroupStatusCASRequest{
		AppGroupID:     appGroupID,
		AssumeStatuses: assumes,
		DestStatus:     dest,
		Token:          token,
	}
	_, err = m.gmasterSdk.GroupStatusCAS(ctx, req)
	return err
}

func (m *gmasterOp) ListCacheGroupDetail(ctx context.Context, appGroupID string, userID string) (*ListCacheGroupDetailRes, error) {
	token, err := compo_utils.GetGMasterToken(ctx, userID)
	if err != nil {
		return nil, err
	}
	req := &gmaster.ListCacheGroupDetailRequest{
		AppGroupID: appGroupID,
		Token:      token,
	}
	resp, err := m.gmasterSdk.ListCacheGroupDetail(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *gmasterOp) UpdateSecurity(ctx context.Context, params *UpdateInnerSecurityParams) error {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &gmaster.UpdateInnerSecurityRequest{
		AppID:       params.AppID,
		AppGroupID:  params.AppGroupID,
		Token:       token,
		ToAddIpList: params.ToAddIpList,
		ToDelIpList: params.ToDelIpList,
		PortList:    params.PortList,
	}
	_, err = m.gmasterSdk.UpdateSecurity(ctx, req)
	return err
}

func (m *gmasterOp) ApplyTemplate(ctx context.Context, params *ApplyTemplateRequest) (err error) {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserId)
	if err != nil {
		return err
	}
	req := &gmaster.ApplyTemplateRequest{
		TemplateID:       params.TemplateID,
		TemplateShowID:   params.TemplateShowID,
		CacheClusterList: params.CacheClusterList,
		Extra:            params.Extra,
		RebootType:       params.RebootType,
		Parameters:       params.Parameters,
		Token:            token,
	}
	err = m.gmasterSdk.ApplyTemplate(ctx, req)
	return err
}

func (m *gmasterOp) InitStandaloneTopo(ctx context.Context, params *InitStandaloneTopoReq) (err error) {
	token, err := compo_utils.GetGMasterToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &gmaster.InitStandaloneTopoReq{
		AppID:          params.AppID,
		AppGroupID:     params.AppGroupID,
		Token:          token,
		ShardID:        params.ShardID,
		NewMasterID:    params.NewMasterID,
		NewMasterFixIP: params.NewMasterFixIP,
		NewMasterPort:  params.NewMasterPort,
	}
	err = m.gmasterSdk.InitStandaloneTopo(ctx, req)
	return err
}

func (m *gmasterOp) UpdateBnsService(ctx context.Context, params *UpdateBnsServiceParams) error {
	token, err := compo_utils.GetGMasterToken(ctx, params.AppID)
	if err != nil {
		return err
	}
	req := &gmaster.UpdateBnsServiceRequest{
		AppID:         params.AppID,
		GroupID:       params.GroupID,
		OldBnsService: params.OldBnsService,
		NewBnsService: params.NewBnsService,
		Token:         token,
	}
	_, err = m.gmasterSdk.UpdateBnsService(ctx, req)
	return err
}
