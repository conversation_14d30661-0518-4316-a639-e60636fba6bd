package repo

import (
	"regexp"
)

// types
const (
	TypeNone = "none"
	TypeHttp = "http"
	TypeFtp  = "ftp"
	TypeBos  = "bos"
)

// patterns
const (
	bosAccessPattern  = `(?i)^bos://(\S+?)/(\S+)`
	httpPrefixPattern = `(?i)^http(s)?://`
	ftpPrefixPattern  = `(?i)^ftp://`
)

// regexps
var (
	bosAccessRegExp  = regexp.MustCompile(bosAccessPattern)
	httpPrefixRegExp = regexp.MustCompile(httpPrefixPattern)
	ftpPrefixRegExp  = regexp.MustCompile(ftpPrefixPattern)
)

// file
const (
	supperFileSize = 200 * 1024 * 1024
)
