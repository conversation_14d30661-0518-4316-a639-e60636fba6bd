package repo

import (
	"fmt"
)

type Repo struct {
	Conf   *Conf
	Client Client
}

var newClientHandlers = map[string]func(*Conf) Client{
	TypeNone: newNoneRepoClient,
	TypeHttp: newHttpRepoClient,
	TypeFtp:  newFtpRepoClient,
	TypeBos:  newBosRepoClient,
}

// GetRepo 获取Repo
func GetRepo(repoName string) *Repo {
	reposConf := loadConf().ReposConf
	if repoConf, has := reposConf[repoName]; !has {
		panic(fmt.Sprintf("repo not exist, repoName: %s", repoName))
	} else if newHandler, has := newClientHandlers[repoConf.Type]; !has {
		panic(fmt.Sprintf("repo type not exist, repoName: %s, repoType: %s", repoName, repoConf.Type))
	} else {
		repo := &Repo{
			Conf:   repoConf,
			Client: new<PERSON><PERSON><PERSON>(repoConf),
		}
		return repo
	}
}
