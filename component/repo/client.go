package repo

import (
	"context"

	bceBosApi "github.com/baidubce/bce-sdk-go/services/bos/api"
)

type Client interface {
	Upload(ctx context.Context, localFileName, remoteFileName string) error
	Download(ctx context.Context, remoteFileName, localFileName string) error
	GetFileUrl(remoteFileName string) string
	GetFileUrlWithExpireTime(remoteFileName string, expireInSeconds int) string
	GetFileUrlWithExpireTimeAndPathSign(remoteFileName string, expireInSeconds int) string
	ListObjects(bucketName string, listObjectsArgs *bceBosApi.ListObjectsArgs) (*bceBosApi.ListObjectsResult, error)
	CheckIsExists(bucketName string, objectName string) (bool, error)
}
