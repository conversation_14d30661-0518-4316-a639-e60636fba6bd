package repo

import (
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
)

type conf struct {
	ReposConf map[string]*Conf `toml:"Repos"`
	loadOnce  sync.Once        `toml:"-"`
}

type Conf struct {
	BaseRepoConf `toml:",squash"`
	HttpRepoConf `toml:",squash"`
	FtpRepoConf  `toml:",squash"`
	BosRepoConf  `toml:",squash"`
}

type BaseRepoConf struct {
	Name     string `toml:"Name"`
	Type     string `toml:"Type"`
	Endpoint string `toml:"Endpoint"`
	Bucket   string `toml:"Bucket"`
}

type HttpRepoConf struct {
	HttpPath string `toml:"Path"`
}

type FtpRepoConf struct {
	FtpUser   string `toml:"User"`
	FtpPasswd string `toml:"Passwd"`
	FtpPath   string `toml:"Path"`
}

type BosRepoConf struct {
	BosAk                        string `toml:"Ak"`
	BosSk                        string `toml:"Sk"`
	BosBucket                    string `toml:"Bucket"`
	BosAuthDuration              int    `toml:"AuthDuration"`
	CustomLifeCycleBucket        string `toml:"CustomLifeCycleBucket"`
	CustomLifeCycleOneYearPrefix string `toml:"CustomLifeCycleOneYearPrefix"`
}

var defaultConf = &conf{}

func loadConf() *conf {
	defaultConf.loadOnce.Do(func() {
		if err := compo_utils.LoadConf("repo", defaultConf); err != nil {
			panic(err)
		}
	})

	return defaultConf
}
