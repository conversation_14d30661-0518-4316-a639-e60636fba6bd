package repo

import (
	"context"

	bceBosApi "github.com/baidubce/bce-sdk-go/services/bos/api"
)

type noneRepoClient struct{}

func (n noneRepoClient) Upload(ctx context.Context, localFileName, remoteFileName string) error {
	panic("implement me")
}

func (n noneRepoClient) Download(ctx context.Context, remoteFileName, localFileName string) error {
	panic("implement me")
}

func (n noneRepoClient) GetFileUrl(remoteFileName string) string {
	return ""
}

func (n noneRepoClient) GetFileUrlWithExpireTime(remoteFileName string, expireInSeconds int) string {
	return ""
}

func (c noneRepoClient) ListObjects(bucketName string,
	listObjectsArgs *bceBosApi.ListObjectsArgs) (*bceBosApi.ListObjectsResult, error) {
	return nil, nil
}

func (c noneRepoClient) CheckIsExists(bucketName string, objectName string) (bool, error) {
	return false, nil
}

func (n noneRepoClient) GetFileUrlWithExpireTimeAndPathSign(remoteFileName string, expireInSeconds int) string {
	return ""
}

func newNoneRepoClient(conf *Conf) Client {
	c := &noneRepoClient{}
	return c
}
