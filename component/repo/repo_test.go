package repo

import (
	"fmt"
	"testing"
)

func TestBosRepoClient_GetFileUrl(t *testing.T) {
	c := newBosRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "bos",
			Endpoint: "http://bj-bos-sandbox.baidu-int.com",
		},
		BosRepoConf: BosRepoConf{
			BosAk:           "74a500c63f6b4d0e9889813c74f03644",
			BosSk:           "QL9pe7E4ugtzRO8znUEOyV1BDcli9I86rUOtbnQHfaSORNl5cUl5xxAJvdRCZb8dMAbjNzNnG89eFnor_atgyQ",
			BosBucket:       "scs-sandbox-packages",
			BosAuthDuration: 1800,
		},
	})
	fmt.Println(c.GetFileUrl("scs-package_20220216190700.tar.gz"))
}

func TestBosRepoClient_GetFileUrl2(t *testing.T) {
	c := newBosRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "bos",
			Endpoint: "bj.bcebos.com",
		},
		BosRepoConf: BosRepoConf{
			BosAk:           "0731d68455ce4b149fd8a4fd30b5400e",
			BosSk:           "NVAOI_30uBbzgOwv2uqWFSrLNifslEmnnOiLkGTl10yppoQDQFQC4Ps30qJXwWsiBkQiBjRDh8Q78YAmonOZFQ",
			BosBucket:       "scs-backup-rdb-bucket-bj",
			BosAuthDuration: 1800,
		},
	})
	fmt.Println(c.GetFileUrl("expiration5/200002968_17648_b02e8932-12ee-48e3-9f20-a73c682af014"))
}

func TestBosRepoClient_GetFileUrl3(t *testing.T) {
	c := newBosRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "bos",
			Endpoint: "bj.bcebos.com",
		},
		BosRepoConf: BosRepoConf{
			BosAk:           "0731d68455ce4b149fd8a4fd30b5400e",
			BosSk:           "NVAOI_30uBbzgOwv2uqWFSrLNifslEmnnOiLkGTl10yppoQDQFQC4Ps30qJXwWsiBkQiBjRDh8Q78YAmonOZFQ",
			BosBucket:       "scs-backup-rdb-bucket-bj",
			BosAuthDuration: 1800,
		},
	})
	fmt.Println(c.GetFileUrlWithExpireTime("expiration5/200002968_17648_b02e8932-12ee-48e3-9f20-a73c682af014", 3600))
}

func TestBosRepoClient_NewBosClient(t *testing.T) {
	c := NewBosClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "bos",
			Endpoint: "http://bj-bos-sandbox.baidu-int.com",
		},
		BosRepoConf: BosRepoConf{
			BosAk:           "74a500c63f6b4d0e9889813c74f03644",
			BosSk:           "QL9pe7E4ugtzRO8znUEOyV1BDcli9I86rUOtbnQHfaSORNl5cUl5xxAJvdRCZb8dMAbjNzNnG89eFnor_atgyQ",
			BosBucket:       "scs-sandbox-packages",
			BosAuthDuration: 1800,
		},
	})
	fmt.Println(c.GetFileUrl("scs-package_20220216190700.tar.gz"))
}

func TestBosRepoClient_NewBosClientSSL(t *testing.T) {
	c := NewBosClientSSL(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "bos",
			Endpoint: "http://bj-bos-sandbox.baidu-int.com",
		},
		BosRepoConf: BosRepoConf{
			BosAk:           "74a500c63f6b4d0e9889813c74f03644",
			BosSk:           "QL9pe7E4ugtzRO8znUEOyV1BDcli9I86rUOtbnQHfaSORNl5cUl5xxAJvdRCZb8dMAbjNzNnG89eFnor_atgyQ",
			BosBucket:       "scs-sandbox-packages",
			BosAuthDuration: 1800,
		},
	})
	fmt.Println(c.GetFileUrl("scs-package_20220216190700.tar.gz"))
}

func TestFtpRepoClient_GetFileUrl(t *testing.T) {
	c := newFtpRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "ftp",
			Endpoint: "xxx",
		},
		FtpRepoConf: FtpRepoConf{
			FtpUser:   "",
			FtpPasswd: "",
			FtpPath:   "",
		},
	})
	fmt.Println(c.GetFileUrlWithExpireTime("scs-package_20220216190700.tar.gz", 3600))
}

func TestHttpRepoClient_GetFileUrl(t *testing.T) {
	c := newHttpRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "http",
			Endpoint: "xxx",
		},
		HttpRepoConf: HttpRepoConf{
			HttpPath: "",
		},
	})
	fmt.Println(c.GetFileUrlWithExpireTime("scs-package_20220216190700.tar.gz", 3600))
}

func TestNoneRepoClient_GetFileUrl(t *testing.T) {
	c := newNoneRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "none",
			Endpoint: "xxx",
		},
	})
	fmt.Println(c.GetFileUrlWithExpireTime("scs-package_20220216190700.tar.gz", 3600))
}

func TestBosRepoClient_ListObjects(t *testing.T) {
	// case1
	c := newBosRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "bos",
			Endpoint: "bj.bcebos.com",
		},
		BosRepoConf: BosRepoConf{
			BosAk:           "0731d68455ce4b149fd8a4fd30b5400e",
			BosSk:           "NVAOI_30uBbzgOwv2uqWFSrLNifslEmnnOiLkGTl10yppoQDQFQC4Ps30qJXwWsiBkQiBjRDh8Q78YAmonOZFQ",
			BosBucket:       "scs-backup-rdb-bucket-bj",
			BosAuthDuration: 1800,
		},
	})
	fmt.Println(c.ListObjects("", nil))

	// case2
	fmt.Println(c.ListObjects("test_bucket", nil))
}

func TestBosRepoClient_CheckIsExists(t *testing.T) {
	// case1
	c := newBosRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "bos",
			Endpoint: "bj.bcebos.com",
		},
		BosRepoConf: BosRepoConf{
			BosAk:           "0731d68455ce4b149fd8a4fd30b5400e",
			BosSk:           "NVAOI_30uBbzgOwv2uqWFSrLNifslEmnnOiLkGTl10yppoQDQFQC4Ps30qJXwWsiBkQiBjRDh8Q78YAmonOZFQ",
			BosBucket:       "scs-backup-rdb-bucket-bj",
			BosAuthDuration: 1800,
		},
	})
	fmt.Println(c.CheckIsExists("", ""))

	// case2
	fmt.Println(c.CheckIsExists("test_bucket", ""))

	// case3
	fmt.Println(c.CheckIsExists("test_bucket", "test_object"))
}

func TestFtpRepoClient_ListObjects(t *testing.T) {
	c := newFtpRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "ftp",
			Endpoint: "xxx",
		},
		FtpRepoConf: FtpRepoConf{
			FtpUser:   "",
			FtpPasswd: "",
			FtpPath:   "",
		},
	})
	fmt.Println(c.ListObjects("test_bucket", nil))
}

func TestFtpRepoClient_CheckIsExists(t *testing.T) {
	c := newFtpRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "ftp",
			Endpoint: "xxx",
		},
		FtpRepoConf: FtpRepoConf{
			FtpUser:   "",
			FtpPasswd: "",
			FtpPath:   "",
		},
	})
	fmt.Println(c.CheckIsExists("test_bucket", "test_object"))
}

func TestHttpRepoClient_ListObjects(t *testing.T) {
	c := newHttpRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "http",
			Endpoint: "xxx",
		},
		HttpRepoConf: HttpRepoConf{
			HttpPath: "",
		},
	})
	fmt.Println(c.ListObjects("test_bucket", nil))
}

func TestHttpRepoClient_CheckIsExists(t *testing.T) {
	c := newHttpRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "http",
			Endpoint: "xxx",
		},
		HttpRepoConf: HttpRepoConf{
			HttpPath: "",
		},
	})
	fmt.Println(c.CheckIsExists("test_bucket", "test_object"))
}
func TestNoneRepoClient_ListObjects(t *testing.T) {
	c := newNoneRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "none",
			Endpoint: "xxx",
		},
	})
	fmt.Println(c.ListObjects("test_bucket", nil))
}

func TestNoneRepoClient_CheckIsExists(t *testing.T) {
	c := newNoneRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "none",
			Endpoint: "xxx",
		},
	})
	fmt.Println(c.CheckIsExists("test_bucket", "test_object"))
}

func TestBosRepoClient_GetFileUrlWithExpireTime(t *testing.T) {
	c := NewBosClientSSL(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "bos",
			Endpoint: "http://bj.bcebos.com",
		},
		BosRepoConf: BosRepoConf{
			BosAk:           "0731d68455ce4b149fd8a4fd30b5400e",
			BosSk:           "NVAOI_30uBbzgOwv2uqWFSrLNifslEmnnOiLkGTl10yppoQDQFQC4Ps30qJXwWsiBkQiBjRDh8Q78YAmonOZFQ",
			BosBucket:       "scs-backup-rdb-bucket-bj",
			BosAuthDuration: 1800,
		},
	})
	fmt.Println(c.GetFileUrlWithExpireTime("redis/scs-bj-rzejiaghrllb/30427/20231129001317/1701187997682339201_dump.rdb", 1800))
}

func TestBosRepoClient_GetFileUrlWithExpireTimeAndPathSign(t *testing.T) {
	c := NewBosClientSSL(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "bos",
			Endpoint: "http://bj.bcebos.com",
		},
		BosRepoConf: BosRepoConf{
			BosAk:                        "0731d68455ce4b149fd8a4fd30b5400e",
			BosSk:                        "NVAOI_30uBbzgOwv2uqWFSrLNifslEmnnOiLkGTl10yppoQDQFQC4Ps30qJXwWsiBkQiBjRDh8Q78YAmonOZFQ",
			BosBucket:                    "scs-backup-rdb-bucket-bj",
			BosAuthDuration:              1800,
			CustomLifeCycleOneYearPrefix: "one-year/",
			CustomLifeCycleBucket:        "test",
		},
	})
	fmt.Println(c.GetFileUrlWithExpireTimeAndPathSign("redis/scs-bj-rzejiaghrllb/30427/20231129001317/1701187997682339201_dump.rdb", 1800))
}

func TestFtpRepoClient_GetFileUrlWithExpireTimeAndPathSign(t *testing.T) {
	c := newFtpRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "ftp",
			Endpoint: "xxx",
		},
		FtpRepoConf: FtpRepoConf{
			FtpUser:   "",
			FtpPasswd: "",
			FtpPath:   "",
		},
	})
	fmt.Println(c.GetFileUrlWithExpireTimeAndPathSign("scs-package_20220216190700.tar.gz", 3600))
}

func TestHttpRepoClient_GetFileUrlWithExpireTimeAndPathSign(t *testing.T) {
	c := newHttpRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "http",
			Endpoint: "xxx",
		},
		HttpRepoConf: HttpRepoConf{
			HttpPath: "",
		},
	})
	fmt.Println(c.GetFileUrlWithExpireTimeAndPathSign("scs-package_20220216190700.tar.gz", 3600))
}

func TestNoneRepoClient_GetFileUrlWithExpireTimeAndPathSign(t *testing.T) {
	c := newNoneRepoClient(&Conf{
		BaseRepoConf: BaseRepoConf{
			Name:     "package_bos",
			Type:     "none",
			Endpoint: "xxx",
		},
	})
	fmt.Println(c.GetFileUrlWithExpireTimeAndPathSign("scs-package_20220216190700.tar.gz", 3600))
}
