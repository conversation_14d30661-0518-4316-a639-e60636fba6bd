package repo

import (
	"context"
	"fmt"
	"strings"

	bceBosApi "github.com/baidubce/bce-sdk-go/services/bos/api"
)

type httpRepoClient struct {
	conf *Conf
}

func (c httpRepoClient) Upload(ctx context.Context, localFileName, remoteFileName string) error {
	panic("implement me")
}

func (c *httpRepoClient) Download(ctx context.Context, remoteFileName, localFileName string) error {
	panic("implement me")
}

func (c *httpRepoClient) GetFileUrl(remoteFileName string) string {
	return fmt.Sprintf("%s%s%s", c.conf.Endpoint, c.conf.HttpPath, remoteFileName)

}

func (c *httpRepoClient) GetFileUrlWithExpireTime(remoteFileName string, expireInSeconds int) string {
	return fmt.Sprintf("%s%s%s%d", c.conf.Endpoint, c.conf.HttpPath, remoteFileName, expireInSeconds)

}

func (c *httpRepoClient) GetFileUrlWithExpireTimeAndPathSign(remoteFileName string, expireInSeconds int) string {
	return fmt.Sprintf("%s%s%s%d", c.conf.Endpoint, c.conf.HttpPath, remoteFileName, expireInSeconds)

}

func (c *httpRepoClient) ListObjects(bucketName string,
	listObjectsArgs *bceBosApi.ListObjectsArgs) (*bceBosApi.ListObjectsResult, error) {
	return nil, nil
}

func (c *httpRepoClient) CheckIsExists(bucketName string, objectName string) (bool, error) {
	return false, nil
}

func newHttpRepoClient(conf *Conf) Client {
	if !strings.HasPrefix(conf.HttpPath, "/") {
		conf.HttpPath = "/" + conf.HttpPath
	}

	c := &httpRepoClient{conf: conf}
	return c
}
