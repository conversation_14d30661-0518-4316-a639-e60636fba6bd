package repo

import (
	"context"
	"fmt"
	"io"
	"os"
	"strings"

	bceBosApi "github.com/baidubce/bce-sdk-go/services/bos/api"
	"github.com/jlaffaye/ftp"

	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
)

type ftpRepoClient struct {
	conf *Conf
}

func (c *ftpRepoClient) Upload(ctx context.Context, localFileName, remoteFileName string) error {
	conn, err := c.dial(ctx)
	if err != nil {
		return err
	}
	defer func() { _ = conn.Quit() }()

	r, err := os.Open(localFileName)
	if err != nil {
		return err
	}
	defer func() { _ = r.Close() }()

	return conn.Stor(remoteFileName, r)
}

func (c *ftpRepoClient) Download(ctx context.Context, remoteFileName, localFileName string) error {
	conn, err := c.dial(ctx)
	if err != nil {
		return err
	}
	defer func() { _ = conn.Quit() }()

	r, err := conn.Retr(remoteFileName)
	if err != nil {
		return err
	}
	defer func() { _ = r.Close() }()

	w, err := os.Create(localFileName)
	if err != nil {
		return err
	}
	defer func() { _ = w.Close() }()

	_, err = io.Copy(w, r)
	return err
}

func (c *ftpRepoClient) dial(ctx context.Context) (*ftp.ServerConn, error) {
	conn, err := ftp.Dial(c.conf.Endpoint, ftp.DialWithContext(ctx))
	if err != nil {
		return nil, err
	}

	err = conn.Login(c.conf.FtpUser, c.conf.FtpPasswd)
	if err != nil {
		_ = conn.Quit()
		return nil, err
	}

	return conn, nil
}

func (c *ftpRepoClient) GetFileUrl(remoteFileName string) string {
	prefix := fmt.Sprintf("ftp://%s", c.conf.Endpoint)
	if c.conf.FtpUser != "" && c.conf.FtpPasswd != "" {
		prefix = fmt.Sprintf("ftp://%s:%s@%s", c.conf.FtpUser, c.conf.FtpPasswd, c.conf.Endpoint)
	}
	return fmt.Sprintf("%s%s%s", prefix, c.conf.HttpPath, remoteFileName)
}

func (c *ftpRepoClient) GetFileUrlWithExpireTime(remoteFileName string, expireInSeconds int) string {
	return ""
}

func (c *ftpRepoClient) GetFileUrlWithExpireTimeAndPathSign(remoteFileName string, expireInSeconds int) string {
	return ""
}

func (c *ftpRepoClient) ListObjects(bucketName string,
	listObjectsArgs *bceBosApi.ListObjectsArgs) (*bceBosApi.ListObjectsResult, error) {
	return nil, nil
}

func (c *ftpRepoClient) CheckIsExists(bucketName string, objectName string) (bool, error) {
	return false, nil
}

func newFtpRepoClient(conf *Conf) Client {
	if ftpPrefixRegExp.MatchString(conf.Endpoint) {
		conf.Endpoint = conf.Endpoint[len("ftp://"):]
	}
	if !strings.HasPrefix(conf.FtpPath, "/") {
		conf.FtpPath = "/" + conf.FtpPath
	}
	if conf.FtpPasswd == "" {
		conf.FtpUser = "anonymous"
	}
	if conf.FtpPasswd == "" {
		conf.FtpPasswd = "anonymous"
	} else {
		conf.FtpPasswd, _ = crypto_utils.DecryptKey(conf.FtpPasswd)
	}

	c := &ftpRepoClient{conf: conf}
	return c
}
