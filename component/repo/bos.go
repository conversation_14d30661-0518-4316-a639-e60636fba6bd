package repo

import (
	"context"
	"errors"
	"os"
	"strings"

	"github.com/baidubce/bce-sdk-go/bce"
	bceBosApi "github.com/baidubce/bce-sdk-go/services/bos/api"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bce_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
)

type bosRepoClient struct {
	conf      *Conf
	bosClient *bce_utils.BosClient
}

func (c *bosRepoClient) Upload(ctx context.Context, localFileName, remoteFileName string) error {
	bucketName, objectName := c.getBucketAndObjectName(remoteFileName)
	stat, err := os.Stat(localFileName)
	if err != nil {
		return err
	}
	if stat.Size() > supperFileSize {
		return c.bosClient.UploadSuperFile(bucketName, objectName, localFileName, "")
	}
	_, err = c.bosClient.PutObjectFromFile(bucketName, objectName, localFileName, nil)
	return err
}

func (c *bosRepoClient) Download(ctx context.Context, remoteFileName, localFileName string) error {
	bucketName, objectName := c.getBucketAndObjectName(remoteFileName)
	fileMeta, err := c.bosClient.GetObjectMeta(bucketName, objectName)
	if err != nil {
		return err
	}
	if fileMeta.ContentLength > supperFileSize {
		return c.bosClient.DownloadSuperFile(bucketName, objectName, localFileName)
	}
	return c.bosClient.BasicGetObjectToFile(bucketName, objectName, localFileName)
}

func (c *bosRepoClient) getBucketAndObjectName(remoteFileName string) (string, string) {
	if c.conf.BosBucket == "" {
		bucketName, objectName, err := ParseBosAccess(remoteFileName)
		if err != nil {
			panic(err)
		}
		return bucketName, objectName
	}
	return c.conf.BosBucket, remoteFileName
}

func (c *bosRepoClient) GetFileUrl(remoteFileName string) string {
	bucketName, objectName := c.getBucketAndObjectName(remoteFileName)
	return c.bosClient.BasicGeneratePresignedUrl(bucketName, objectName, c.conf.BosAuthDuration)
}

func (c *bosRepoClient) GetFileUrlWithExpireTime(remoteFileName string, expireInSeconds int) string {
	if expireInSeconds == 0 {
		expireInSeconds = c.conf.BosAuthDuration
	}
	bucketName, objectName := c.getBucketAndObjectName(remoteFileName)
	return c.bosClient.BasicGeneratePresignedUrl(bucketName, objectName, expireInSeconds)
}

func (c *bosRepoClient) ListObjects(bucketName string,
	listObjectsArgs *bceBosApi.ListObjectsArgs) (*bceBosApi.ListObjectsResult, error) {
	if bucketName == "" {
		return nil, errors.New("bucketName is null")
	}
	resp, err := c.bosClient.ListObjects(bucketName, listObjectsArgs)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *bosRepoClient) CheckIsExists(bucketName string, objectName string) (bool, error) {
	if bucketName == "" {
		return false, errors.New("bucketName is null")
	}
	if objectName == "" {
		return false, errors.New("objectName is null")
	}
	_, err := c.bosClient.GetObjectMeta(bucketName, objectName)
	if realErr, ok := err.(*bce.BceServiceError); ok {
		if realErr.StatusCode == 404 {
			return false, nil
		}
	}
	return true, nil
}

func (c *bosRepoClient) GetFileUrlWithExpireTimeAndPathSign(remoteFileName string, expireInSeconds int) string {
	if expireInSeconds == 0 {
		expireInSeconds = c.conf.BosAuthDuration
	}
	bucketName, objectName := c.getBucketAndObjectName(remoteFileName)
	return c.bosClient.GeneratePresignedUrlPathStyle(bucketName, objectName, expireInSeconds, "", nil, nil)
}

func newBosRepoClient(conf *Conf) Client {
	if !httpPrefixRegExp.MatchString(conf.Endpoint) {
		conf.Endpoint = "http://" + conf.Endpoint
	}
	bosSk, _ := crypto_utils.DecryptKey(conf.BosSk)

	c := &bosRepoClient{
		conf:      conf,
		bosClient: bce_utils.NewBosClient(conf.BosAk, bosSk, conf.Endpoint),
	}
	return c
}

func newBosRepoClientSSL(conf *Conf) Client {
	if !httpPrefixRegExp.MatchString(conf.Endpoint) {
		conf.Endpoint = "https://" + conf.Endpoint
	}
	if strings.HasPrefix(conf.Endpoint, "http://") {
		conf.Endpoint = strings.Replace(conf.Endpoint, "http://", "https://", 1)
	}
	bosSk, _ := crypto_utils.DecryptKey(conf.BosSk)

	c := &bosRepoClient{
		conf:      conf,
		bosClient: bce_utils.NewBosClient(conf.BosAk, bosSk, conf.Endpoint),
	}
	return c
}

func NewBosClient(conf *Conf) Client {
	return newBosRepoClient(conf)
}

func NewBosClientSSL(conf *Conf) Client {
	return newBosRepoClientSSL(conf)
}

func ParseBosAccess(access string) (bucketName, objectName string, err error) {
	if m := bosAccessRegExp.FindStringSubmatch(access); m == nil {
		return "", "", cerrs.ErrInvalidParams.Errorf(
			"bos access must match pattern '%s', but we got '%s'",
			bosAccessPattern, access)
	} else {
		return m[1], m[2], nil
	}
}
