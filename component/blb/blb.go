/*
modification history
--------------------
2021/12/15, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建BLB资源
*/

package blb

import (
	"context"
	"sync"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/licloud/lb/adaptor"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/elb"
)

type component struct {
	conf     *config
	elbSdk   elb.ELBService
	initOnce sync.Once
}

var defaultComponent = &component{
	conf: &config{},
}

// Instance 获取默认的blb组件，必须在gdp初始化env和ral后使用
func Instance() *component {
	defaultComponent.initOnce.Do(func() {
		// load default conf
		if err := compo_utils.LoadConf("blb", defaultComponent.conf); err != nil {
			panic(err.Error())
		}

		// default elb sdk
		if privatecloud.IsPrivateENV() {
			defaultComponent.elbSdk = adaptor.Instance()
		} else {
			defaultComponent.elbSdk = elb.NewDefaultElbSdk()
		}
	})

	return defaultComponent
}

// CreateBLB 创建BLB
// 1. 发送ipv4创建请求
// 2. 如果需要发送ipv6创建请求
// 相关代码 State::create_elb_instance
func (c *component) CreateBLB(ctx context.Context, params *CreateBLBParam) ([]*BLB, error) {
	// get auth
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return nil, err
	}

	// do request
	blbList := make([]*BLB, 0)
	for k, blbParam := range params.BLBParams {
		elbType := elb.ElbTypeNormal
		if blbParam.IpType == x1model.Ipv6 {
			elbType = elb.ElbTypeIpV6
		}

		createReq := &elb.CreateELBRequest{
			Internal:          c.conf.BLBConf.Internal,
			Name:              blbParam.Name,
			Count:             1,
			Source:            params.Product,
			VpcId:             blbParam.VpcId,
			SubnetId:          blbParam.SubnetId,
			IsVpc:             true,
			Type:              elbType,
			BgwGroupId:        blbParam.BgwGroupId,
			BgwGroupExclusive: blbParam.BgwGroupExclusive,
			MasterAZ:          blbParam.MasterAZ,
			SlaveAZ:           blbParam.SlaveAZ,
			BgwGroupMode:      blbParam.BgwGroupMode,
			Auth:              auth.Reuse(k),
		}

		createRsp, err := c.elbSdk.CreateELB(ctx, createReq)
		if err != nil {
			return nil, err
		}
		if len(createRsp.BlbList) == 0 {
			return nil, cerrs.Errorf("got empty blb list")
		}

		blbList = append(blbList, &BLB{
			BLBID:  createRsp.BlbList[0].Id,
			IpType: blbParam.IpType,
			Vip:    createRsp.BlbList[0].Vip,
			OVip:   createRsp.BlbList[0].Ovip,
			IPV6:   createRsp.BlbList[0].Ipv6,
		})
	}

	return blbList, nil
}

// DeleteBLB 删除BLB
func (c *component) DeleteBLB(ctx context.Context, params *DeleteBLBParam) error {
	// get auth
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	// do request
	for k, blbId := range params.BLBIDs {
		deleteReq := &elb.DeleteELBRequest{
			ElbId: blbId,
			Auth:  auth.Reuse(k),
		}

		if _, err := c.elbSdk.DeleteELB(ctx, deleteReq); err != nil {
			return err
		}
	}

	return nil
}

// CreateListener 创建BLB listener
//  1. 对每一个BLB执行
//     a)检查listener是否已经创建
//     b)如果没有创建，则进行创建
//
// 相关代码 State::create_listener_by_ip_type
func (c *component) CreateListener(ctx context.Context, params *CreateListenerParam) error {
	// get auth
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	// do request
	g := &gtask.Group{
		Concurrent:    createListenerConcurrent,
		AllowSomeFail: false,
	}
	for k, blbId := range params.BLBIDs {
		auth := auth.Reuse(k)
		blbId := blbId
		g.Go(func() error {
			return c.createListener(ctx, auth, blbId, params.ListenPort, params.BackendPort)
		})
	}
	_, err = g.Wait()
	return err
}

func (c *component) createListener(ctx context.Context, auth *common.Authentication, blbId string, listenPort, backendPort int32) error {
	// 检查listener是否已经创建
	listReq := &elb.ListElbRequest{
		ElbId: blbId,
		Port:  listenPort,
		Auth:  auth,
	}

	_, err := c.elbSdk.ListListener(ctx, listReq)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return err
	}

	// 创建listener
	createReq := &elb.CreateListenerRequest{
		Type:                        c.conf.BLBListenerConf.Type,
		Port:                        listenPort,
		BackendPort:                 c.conf.BLBListenerConf.BackendPort,
		Scheduler:                   c.conf.BLBListenerConf.Scheduler,
		HealthCheckPort:             c.conf.BLBListenerConf.HealthCheckPort,
		HealthCheck:                 c.conf.BLBListenerConf.Type,
		HealthCheckTimeoutInSecond:  c.conf.BLBListenerConf.HealthCheckTimeoutInSecond,
		HealthCheckUpRetry:          c.conf.BLBListenerConf.HealthCheckUpRetry,
		HealthCheckDownRetry:        c.conf.BLBListenerConf.HealthCheckDownRetry,
		HealthCheckIntervalInSecond: c.conf.BLBListenerConf.HealthCheckIntervalInSecond,
		TcpSessionTimeout:           c.conf.BLBListenerConf.TcpSessionTimeoutInMiliSecond,
		ElbId:                       blbId,
		Auth:                        auth,
	}

	if backendPort > 0 {
		createReq.BackendPort = backendPort
		createReq.HealthCheckPort = backendPort
	}

	_, err = c.elbSdk.CreateListener(ctx, createReq)
	return err
}

// BindRs 绑定Rs
// 1. 查询blb所有已绑定的rs
// 2. 需要增加的rs中，如果有未绑定的，加入到请求中进行绑定
// 3. 发送绑定rs的请求
// 相关代码 ProcessorRedisBase::update_elb_rs_by_ip_type
func (c *component) BindRs(ctx context.Context, params *BindRsParam) error {
	// get auth
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	// do request
	g := &gtask.Group{
		Concurrent:    createBackendConcurrent,
		AllowSomeFail: false,
	}
	for k, blbId := range params.BLBIDs {
		auth := auth.Reuse(k)
		blbId := blbId
		g.Go(func() error {
			return c.bindRs(ctx, auth, blbId, params.Rss)
		})
	}
	_, err = g.Wait()
	return err
}

func (c *component) bindRs(ctx context.Context, auth *common.Authentication, blbId string, rss []*Rs) error {
	// 查询已经建立的backend
	listReq := &elb.ListElbRequest{
		ElbId: blbId,
		Auth:  auth,
	}
	listRsp, err := c.elbSdk.ListBackendServer(ctx, listReq)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return err
	}

	// 获得没有建立过的backend
	rsExists := map[string]bool{}
	for _, rs := range listRsp.BackendServerList {
		rsExists[rs.InstanceId] = true
	}

	backendList := make([]*elb.BackendServer, 0)
	for _, rs := range rss {
		if rsExists[rs.UUID] {
			continue
		}
		backendList = append(backendList, &elb.BackendServer{
			InstanceId: rs.UUID,
			Port:       rs.Port,
			Weight:     rs.Weight,
		})
	}
	if len(backendList) == 0 {
		return nil
	}

	// 创建这些backend
	createReq := &elb.CreateBackendServerRequest{
		BackendServerList: backendList,
		ElbId:             blbId,
		Auth:              auth,
	}
	_, err = c.elbSdk.CreateBackendServer(ctx, createReq)
	return err
}

// UnbindRs 解绑Rs
// 1. 查询blb所有已绑定的rs
// 2. 需要解绑的rs中，如果有已绑定的，加入到请求中进行解绑
// 3. 发送解绑rs的请求
// 相关代码 ProcessorRedisBase::update_elb_rs_by_ip_type
func (c *component) UnbindRs(ctx context.Context, params *UnbindRsParam) error {
	// get auth
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	// do request
	g := &gtask.Group{
		Concurrent:    deleteBackendConcurrent,
		AllowSomeFail: false,
	}
	for k, blbId := range params.BLBIDs {
		auth := auth.Reuse(k)
		blbId := blbId
		g.Go(func() error {
			return c.unbindRs(ctx, auth, blbId, params.UUIDs)
		})
	}
	_, err = g.Wait()
	return err
}

func (c *component) unbindRs(ctx context.Context, auth *common.Authentication, blbId string, UUIDs []string) error {
	// 查询已经建立的backend
	listReq := &elb.ListElbRequest{
		ElbId: blbId,
		Auth:  auth,
	}
	listRsp, err := c.elbSdk.ListBackendServer(ctx, listReq)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return err
	}

	// 获得没有建立过的backend
	rsExists := map[string]bool{}
	for _, rs := range listRsp.BackendServerList {
		rsExists[rs.InstanceId] = true
	}

	uuidList := make([]string, 0)
	for _, uuid := range UUIDs {
		if !rsExists[uuid] {
			continue
		}
		uuidList = append(uuidList, uuid)
	}
	if len(uuidList) == 0 {
		return nil
	}

	// 删除这些backend
	for _, uuid := range uuidList {
		deleteReq := &elb.DeleteBackendServerRequest{
			BackendId: uuid,
			ElbId:     blbId,
			Auth:      auth,
		}
		if _, err := c.elbSdk.DeleteBackendServer(ctx, deleteReq); err != nil {
			return err
		}
	}
	return nil
}

func (c *component) ListELB(ctx context.Context, userId string, blbId string) (listrsp *elb.ListElbResponse, err error) {
	// get auth
	auth, err := compo_utils.GetOpenapiAuth(ctx, userId)
	if err != nil {
		return
	}
	return c.listElb(ctx, auth, blbId)
}

func (c *component) listElb(ctx context.Context, auth *common.Authentication, blbId string) (listrsp *elb.ListElbResponse, err error) {
	listElbReq := elb.ListElbRequest{
		ElbId: blbId,
		Auth:  auth,
	}
	listrsp, err = c.elbSdk.ListElb(ctx, &listElbReq)
	if err != nil {
		return
	}
	return
}

func (c *component) getElbStatus(ctx context.Context, auth *common.Authentication, blbId string) (status string, err error) {
	listElbRsp, err := c.listElb(ctx, auth, blbId)
	if err != nil {
		return
	}
	status = listElbRsp.Status
	return
}

func (c *component) UpdateElbsRs(ctx context.Context, params *UpdateElbRsParams) (err error) {
	// get auth
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	for _, blb := range params.BLbs {
		// 只管绑了主的
		if blb.Type == x1model.BLBTypeNormal {
			status, err := c.getElbStatus(ctx, auth, blb.BlbId)
			if err != nil {
				return err
			}
			if status == "error" {
				return cerrs.Errorf("blb status error")
			}
			if status != "available" && status != "paused" {
				return cerrs.Errorf("blb status not available nor paused")
			}
			err = c.updateElbRs(ctx, params.ToAddRsUuids, params.ToDelRsUuids, auth, blb.BlbId, params.Port)
			if err != nil {
				return err
			}
		}
	}
	return
}

func (c *component) UpdateElbsRsForRo(ctx context.Context, params *UpdateElbRsForRoParams) (err error) {
	// get auth
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}
	err = c.updateElbRsForRo(ctx, params, auth)
	if err != nil {
		return err
	}
	return
}

func (c *component) updateElbRsForRo(ctx context.Context, params *UpdateElbRsForRoParams, auth *common.Authentication) (err error) {
	toUpdateBackendList := make([]*elb.CommonBackendServer, 0)
	for _, rs := range params.ToUpdateRs {
		uuid := rs.UUID
		toUpdateBackendList = append(toUpdateBackendList, &elb.CommonBackendServer{
			InstanceId: uuid,
			Weight:     rs.Weight,
		})
	}
	if len(toUpdateBackendList) != 0 {
		updateReq := &elb.UpdateBackendServerRequest{
			BackendServerList: toUpdateBackendList,
			ElbId:             params.BLbs,
			Auth:              auth,
		}
		if _, err = c.elbSdk.UpdateBackendServer(ctx, updateReq); err != nil {
			return err
		}
	}

	return nil
}

func (c *component) updateElbRs(ctx context.Context, toAddRsUuids []string, toDelRsUuids []string, auth *common.Authentication, blbId string, port int32) (err error) {
	// 查询已经建立的backend
	listReq := &elb.ListElbRequest{
		ElbId: blbId,
		Auth:  auth,
	}
	listRsp, err := c.elbSdk.ListBackendServer(ctx, listReq)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return
	}
	toAddBackendList := make([]*elb.BackendServer, 0)
	for _, uuid := range toAddRsUuids {
		if !isInBackendServerList(uuid, listRsp.BackendServerList) {
			toAddBackendList = append(toAddBackendList, &elb.BackendServer{
				InstanceId: uuid,
				Port:       port,
				Weight:     1,
			})
		}
	}
	if len(toAddBackendList) != 0 {
		createReq := &elb.CreateBackendServerRequest{
			BackendServerList: toAddBackendList,
			ElbId:             blbId,
			Auth:              auth,
		}
		if _, err = c.elbSdk.CreateBackendServer(ctx, createReq); err != nil {
			return err
		}
	}

	for _, uuid := range toDelRsUuids {
		if isInBackendServerList(uuid, listRsp.BackendServerList) {
			deleteReq := &elb.DeleteBackendServerRequest{
				BackendId: uuid,
				ElbId:     blbId,
				Auth:      auth,
			}
			if _, err := c.elbSdk.DeleteBackendServer(ctx, deleteReq); err != nil {
				return err
			}
		}
	}
	return nil
}

func isInBackendServerList(uuid string, backendServerList []*elb.BackendServerList) bool {
	for _, server := range backendServerList {
		if uuid == server.InstanceId {
			return true
		}
	}
	return false
}

func (c *component) BindEipWithPnetIp(ctx context.Context, params *BindEipWithPnetIpParams) error {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	productResource := "SCS"
	if c.conf.BLBConf.Product != "" {
		productResource = c.conf.BLBConf.Product
	}

	sdkBindReq := &elb.BindEipRequest{
		InstanceType: productResource,
		InstanceId:   params.AppID,
		PnetIp:       params.PnetIP,
		Eip:          params.Eip,
		Auth:         auth,
	}

	_, err = c.elbSdk.BindEip(ctx, sdkBindReq)
	return err
}

func (c *component) BindEipWithBLBID(ctx context.Context, params *BindEipWithBLBIDParams) error {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	productResource := "SCS"
	if c.conf.BLBConf.Product != "" {
		productResource = c.conf.BLBConf.Product
	}
	sdkBindReq := &elb.BindEipRequest{
		InstanceType:         productResource,
		InstanceId:           params.AppID,
		InstanceInternalId:   params.BLBID,
		InstanceInternalType: "BLB",
		Eip:                  params.Eip,
		Auth:                 auth,
	}

	_, err = c.elbSdk.BindEip(ctx, sdkBindReq)
	return err
}

func (c *component) UnbindEip(ctx context.Context, params *UnbindEipParams) error {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	sdkUnBindReq := &elb.UnbindEipRequest{
		Eip:  params.Eip,
		Auth: auth,
	}
	_, err = c.elbSdk.UnbindEip(ctx, sdkUnBindReq)
	return err
}

func (c *component) DeleteListener(ctx context.Context, params *DeleteListenerParam) error {
	// get auth
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	// do request
	g := &gtask.Group{
		Concurrent:    deleteListenerConcurrent,
		AllowSomeFail: false,
	}
	for k, blbId := range params.BLBIDs {
		auth := auth.Reuse(k)
		blbId := blbId
		g.Go(func() error {
			return c.deleteListener(ctx, auth, blbId, params.ListenPort)
		})
	}
	_, err = g.Wait()
	return err
}

func (c *component) deleteListener(ctx context.Context, auth *common.Authentication, blbID string, listenPort int32) error {
	// 检查listener是否已经存在
	listReq := &elb.ListElbRequest{
		ElbId: blbID,
		Port:  listenPort,
		Auth:  auth,
	}

	_, err := c.elbSdk.ListListener(ctx, listReq)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return err
	}

	// 删除listener
	deleteReq := &elb.DeleteListenerRequest{
		ListenerPort: int64(listenPort),
		ElbId:        blbID,
		Auth:         auth,
	}

	_, err = c.elbSdk.DeleteListener(ctx, deleteReq)
	return err
}

func (c *component) ListRs(ctx context.Context, userId string, blbId string) (rsList []*Rs, err error) {
	// get auth
	auth, err := compo_utils.GetOpenapiAuth(ctx, userId)
	if err != nil {
		return
	}
	listReq := &elb.ListElbRequest{
		ElbId: blbId,
		Auth:  auth,
	}
	listRsp, err := c.elbSdk.ListBackendServer(ctx, listReq)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return nil, err
	}
	if listRsp == nil || len(listRsp.BackendServerList) == 0 {
		return []*Rs{}, nil
	}
	for _, rs := range listRsp.BackendServerList {
		rsList = append(rsList, &Rs{
			UUID:   rs.InstanceId,
			Weight: cast.ToInt32(rs.Weight),
			Port: func() int32 {
				if len(rs.PortList) == 0 {
					return 0
				}
				return rs.PortList[0].ListenerPort
			}(),
		})
	}
	return
}
