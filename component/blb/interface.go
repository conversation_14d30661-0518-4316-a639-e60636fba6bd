package blb

import "icode.baidu.com/baidu/scs/x1-base/model/x1model"

type CreateBLBParam struct {
	UserID    string
	Product   string
	BLBParams []*BLBParam
}

type BLBParam struct {
	Name              string
	VpcId             string
	SubnetId          string
	IpType            string
	BgwGroupId        string
	BgwGroupExclusive bool
	BgwGroupMode      string
	MasterAZ          string
	SlaveAZ           string
}

type BLB struct {
	BLBID  string
	IpType string
	Vip    string
	OVip   string
	IPV6   string
}

type DeleteBLBParam struct {
	UserID string
	BLBIDs []string
}

type CreateListenerParam struct {
	UserID      string
	BLBIDs      []string
	ListenPort  int32
	BackendPort int32
}

type DeleteListenerParam struct {
	UserID     string
	BLBIDs     []string
	ListenPort int32
}

type BindRsParam struct {
	UserID string
	BLBIDs []string
	Rss    []*Rs
}

type UnbindRsParam struct {
	UserID string
	BLBIDs []string
	UUIDs  []string
}

type Rs struct {
	UUID   string
	Weight int32
	Port   int32
}

type UpdateElbRsParams struct {
	UserID       string
	ToAddRsUuids []string
	ToDelRsUuids []string
	BLbs         []*x1model.BLB
	Port         int32
}

type UpdateElbRsForRoParams struct {
	UserID     string
	ToUpdateRs []*Rs
	BLbs       string
	Port       int32
}

type BindEipWithPnetIpParams struct {
	UserID string
	AppID  string
	Eip    string
	PnetIP string
}

type BindEipWithBLBIDParams struct {
	UserID string
	AppID  string
	Eip    string
	BLBID  string
}

type UnbindEipParams struct {
	UserID string
	Eip    string
}
