package blb

type config struct {
	BLBConf         *blbConf
	BLBListenerConf *blbListenerConf
}

type blbConf struct {
	Internal string `toml:"Internal,omitempty"`
	Product  string `toml:"Product,omitempty"`
}

type blbListenerConf struct {
	Type                          string
	BackendPort                   int32
	HealthCheckPort               int32
	HealthCheckTimeoutInSecond    int32
	HealthCheckUpRetry            int32
	HealthCheckDownRetry          int32
	HealthCheckIntervalInSecond   int32
	Scheduler                     string
	TcpSessionTimeoutInMiliSecond int32
}
