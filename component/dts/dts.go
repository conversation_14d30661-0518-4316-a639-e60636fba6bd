/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * author: wangbin<PERSON> (<EMAIL>)
 * Date: 2024-04-10
 * File: dts.go
 */
package dts

import (
	"context"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/dts"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
)

type component struct {
	conf     *Config
	dtsSdk   dts.DTSService
	initOnce sync.Once
}

var defaultComponent = &component{
	conf: &Config{},
}

// Instance 获取默认的dts组件
func Instance() *component {
	defaultComponent.initOnce.Do(func() {
		// load default conf
		if err := compo_utils.LoadConf("dts", defaultComponent.conf); err != nil {
			panic(err.Error())
		}

		// default dts sdk
		defaultComponent.dtsSdk = dts.NewDefaultDtsSdk()
	})

	return defaultComponent
}

// 查找指定集群的标准版迁移到集群版的任务
func (c *component) findTask(ctx context.Context, appID string, auth *common.Authentication) (TaskID string, err error) {
	listReq := dts.ListTaskRequest{
		MaxKeys: 1000,
		Type:    "migration",
		Auth:    auth,
	}
	listRsp, err := c.dtsSdk.ListTask(ctx, &listReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "list task fail,err:%s", err.Error())
		return "", err
	}

	taskName := DTSPrefix + appID
	for _, record := range listRsp.Task {
		if record.TaskName == taskName {
			return record.DtsID, nil
		}
	}

	err = cerrs.ErrNotFound.Errorf("Dts Task not found")
	return "", err
}

// CreateTask 创建任务，如果已存在则返回成功，否则新建并配置任务。
// 参数：
//
//	ctx context.Context - 上下文信息，包含请求的状态和其他可选信息。
//	params *CreateTaskParams - 创建任务所需的参数，包括应用ID、源Redis IP、源Redis端口、源Redis密码、目标Redis IP、目标Redis端口、目标Redis密码。
//
// 返回值：
//
//	err error - 错误信息，如果任务创建或配置成功则为nil，否则为对应的错误信息。
func (c *component) CreateTask(ctx context.Context, params *CreateTaskParams) (err error) {
	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     c.conf.ResourceID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return
	}
	auth := authRsp.Auth

	// find
	taskID, err := c.findTask(ctx, params.AppID, auth)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "list domain fail,err:%s", err.Error())
		return err
	}

	if taskID != "" {
		return nil
	}

	// create
	createReq := dts.CreateTaskRequest{
		ProductType:        "postpay",
		Type:               "migration",
		Standard:           "large",
		SourceInstanceType: "public",
		TargetInstanceType: "public",
		OrderInfo: &dts.OrderInfo{
			Src: &dts.Src{
				InstanceType: "public",
				DbType:       "redis",
			},
			Dst: &dts.Dst{
				InstanceType: "public",
				DbType:       "redis",
			},
		},
		Auth: auth,
	}
	logger.ComponentLogger.Notice(ctx, "Create Domain Req: %+v", *params)
	createRsp, err := c.dtsSdk.CreateTask(ctx, &createReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "create task fail,err:%s", err.Error())
		return err
	}
	dtsTask := createRsp.DtsTasks[0]
	taskID = dtsTask.DtsID

	// config
	configReq := dts.ConfigTaskRequest{
		Type:     "migration",
		DtsID:    taskID,
		TaskName: DTSPrefix + params.AppID,
		DataType: []string{"increment", "base"},
		SrcConnection: &dts.Connection{
			InstanceType:     "public",
			DbType:           "redis",
			Region:           c.conf.Region,
			DbHost:           params.SrcRedisIP,
			DbPort:           params.SrcRedisPort,
			DbPass:           params.SrcRedisPassword,
			PositionStrategy: "fail",
		},
		DstConnection: &dts.Connection{
			InstanceType:     "public",
			DbType:           "redis",
			Region:           c.conf.Region,
			DbHost:           params.DstRedisIP,
			DbPort:           params.DstRedisPort,
			DbPass:           params.DstRedisPassword,
			PositionStrategy: "fail",
		},
		SchemaMapping: []dts.Schema{},
		Granularity:   "instance",
		Auth:          auth,
	}
	_, err = c.dtsSdk.ConfigTask(ctx, &configReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "config task fail,err:%s", err.Error())
		return err
	}
	return
}

// launchPrecheck + showPrecheck + skipPrecheck
/* showPrecheck
{
    "result": [
        {
            "status": "warning",
            "message": "源端DB数量[256]上限高于目标端[1]，可能导致迁移失败",
            "name": "dbCountLimit",
            "subscription": "检查目标端实例的DB数量上限是否满足迁移要求"
        },
        {
            "status": "warning",
            "message": "查询目标端master_replid失败, 请人工确认源端与目标端是否是同一集群",
            "name": "dstSameCluster",
            "subscription": "检查实例级迁移下目标端实例是否与源端实例是同一集群"
        },
        {
            "status": "pass",
            "message": "",
            "name": "srcRedisConnect",
            "subscription": "检查数据传输服务器是否能连通源数据库"
        },
        {
            "status": "pass",
            "message": "",
            "name": "dstRedisConnect",
            "subscription": "检查数据传输服务器是否能连通目标数据库"
        },
        {
            "status": "pass",
            "message": "",
            "name": "forbidToSelf",
            "subscription": "检查是否相同实例之间进行迁移"
        },
        {
            "status": "pass",
            "message": "",
            "name": "srcDstVersion",
            "subscription": "检查上游实例为版本 2.8x 以上版本的主从架构实例，且上游实例版本不高于下游实例版本"
        }
    ],
    "success": true,
}
*/
func (c *component) PrecheckTask(ctx context.Context, params *PrecheckTaskParams) (err error) {
	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     c.conf.ResourceID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return
	}
	auth := authRsp.Auth

	// find
	taskID, err := c.findTask(ctx, params.AppID, auth)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "list domain fail,err:%s", err.Error())
		return err
	}

	if taskID == "" {
		return errors.Errorf("dts task not found")
	}

	// launchTaskPrecheck
	launchTaskPrecheckReq := dts.CommonRequest{
		DtsID: taskID,
		Auth:  auth,
	}
	_, err = c.dtsSdk.LaunchTaskPrecheck(ctx, &launchTaskPrecheckReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "launch task precheck fail,err:%s", err.Error())
		return err
	}

	// showTaskPrecheck
	needSkipTaskPrecheck := false
	for {
		showTaskPrecheckReq := dts.CommonRequest{
			DtsID: taskID,
			Auth:  auth,
		}
		showTaskPrecheckRsp, err := c.dtsSdk.ShowTaskPrecheck(ctx, &showTaskPrecheckReq)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "show task precheck fail,err:%s", err.Error())
			return err
		}

		if len(showTaskPrecheckRsp.Result) == 0 || hasReadyStatus(showTaskPrecheckRsp.Result) {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(1 * time.Second):
				continue
			}
		}

		for _, checkItem := range showTaskPrecheckRsp.Result {
			// 目标集群是 proxy，info 信息中 cluster_enable 为 1，故而 dbCountLimit 有警告;
			// info 中没有 master_replid 字段，故而 dstSameCluster 有警告
			if checkItem.Status == "warning" && (checkItem.Name == "dbCountLimit" || checkItem.Name == "dstSameCluster" || checkItem.Name == "srcAOFEnabled") {
				needSkipTaskPrecheck = true
				continue
			}

			if checkItem.Status != "pass" {
				return errors.Errorf("precheck fail")
			}
		}
		break
	}

	// skipTaskPrecheck
	if needSkipTaskPrecheck {
		skipTaskPrecheckReq := dts.CommonRequest{
			DtsID: taskID,
			Auth:  auth,
		}
		_, err := c.dtsSdk.SkipTaskPrecheck(ctx, &skipTaskPrecheckReq)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "skip task precheck fail,err:%s", err.Error())
			return err
		}
	}
	return nil
}

// StartTask 开始任务函数，参数为上下文、启动任务的参数结构体指针，返回值为error类型，用于处理授权、查询任务、启动任务等操作
func (c *component) StartTask(ctx context.Context, params *StartTaskParams) (err error) {
	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     c.conf.ResourceID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return
	}
	auth := authRsp.Auth

	// find
	taskID, err := c.findTask(ctx, params.AppID, auth)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "list domain fail,err:%s", err.Error())
		return err
	}

	if taskID == "" {
		return errors.Errorf("dts task not found")
	}

	// startTask
	startTaskReq := dts.CommonRequest{
		DtsID: taskID,
		Auth:  auth,
	}
	_, err = c.dtsSdk.StartTask(ctx, &startTaskReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "start task fail,err:%s", err.Error())
		return err
	}
	return nil
}

/*
	"dynamicInfo": {
	    "base": [
	        {
	            "count": "0",
	            "status": null,
	            "expectFinishTime": "-1",
	            "finishTableCount": "-1",
	            "totalTableCount": "-1",
	            "current": "0",
	            "speed": "-1"
	        }
	    ],
	    "increment": {
	        "delay": 0,
	        "position": "165128699",
	        "syncStatus": "正常"
	    },
	    "schema": null
	}
*/
func (c *component) CheckTaskSync(ctx context.Context, params *CheckTaskParams) (err error) {
	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     c.conf.ResourceID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return
	}
	auth := authRsp.Auth

	// find
	taskID, err := c.findTask(ctx, params.AppID, auth)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "list domain fail,err:%s", err.Error())
		return err
	}

	if taskID == "" {
		return errors.Errorf("dts task not found")
	}

	// checkTask
	for {
		showTaskReq := dts.ShowTaskRequest{
			DtsID: taskID,
			Auth:  auth,
		}
		showTaskRsp, err := c.dtsSdk.ShowTask(ctx, &showTaskReq)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "show task fail,err:%s", err.Error())
			return err
		}

		if showTaskRsp.Status != "running" && showTaskRsp.Status != "starting" {
			return errors.Errorf("dts task status not running or starting")
		}

		if showTaskRsp.DynamicInfo == nil {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(5 * time.Second):
				continue
			}
		}

		// 已追上同步(延迟小于 3s)
		if showTaskRsp.DynamicInfo.Increment.Delay < 3 && showTaskRsp.DynamicInfo.Increment.SyncStatus == "正常" {
			return nil
		}
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(1 * time.Second):
			continue
		}

	}
}

// ShutdownTask 停止任务，包括查询任务状态、关闭任务和等待任务完成
// 参数：
//
//	ctx context.Context                      上下文信息，用于传递请求的超时、取消等信息
//	params *ShutdownTaskParams               停止任务的参数，包括应用ID和认证信息
//
// 返回值：
//
//	err error                                如果发生错误，则返回相应的错误信息；否则返回nil
func (c *component) ShutdownTask(ctx context.Context, params *ShutdownTaskParams) (err error) {
	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     c.conf.ResourceID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return
	}
	auth := authRsp.Auth

	// find
	taskID, err := c.findTask(ctx, params.AppID, auth)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "list domain fail,err:%s", err.Error())
		return err
	}

	if taskID == "" {
		return errors.Errorf("dts task not found")
	}

	// check cur status
	curTaskRsp, err := c.dtsSdk.ShowTask(ctx, &dts.ShowTaskRequest{
		DtsID: taskID,
		Auth:  auth,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "show task fail,err:%s", err.Error())
		return err
	}

	// shutdownTask
	if curTaskRsp.Status == "running" {
		shutdownTaskReq := dts.CommonRequest{
			DtsID: taskID,
			Auth:  auth,
		}
		_, err = c.dtsSdk.ShutdownTask(ctx, &shutdownTaskReq)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "shutdown task fail,err:%s", err.Error())
			return err
		}
	}

	// checkTask status(running --> finishing --> finished)
	for {
		showTaskReq := dts.ShowTaskRequest{
			DtsID: taskID,
			Auth:  auth,
		}
		showTaskRsp, err := c.dtsSdk.ShowTask(ctx, &showTaskReq)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "show task fail,err:%s", err.Error())
			return err
		}

		if showTaskRsp.Status == "finished" {
			return nil
		}

		if showTaskRsp.DynamicInfo == nil {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(1 * time.Second):
				continue
			}
		}
	}
}

// DeleteTask 删除任务函数，接收一个上下文context和指向DeleteTaskParams结构体的指针params，返回一个error类型的错误
func (c *component) DeleteTask(ctx context.Context, params *DeleteTaskParams) (err error) {
	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     c.conf.ResourceID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return
	}
	auth := authRsp.Auth

	// find
	taskID, err := c.findTask(ctx, params.AppID, auth)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "list domain fail,err:%s", err.Error())
		return err
	}

	if taskID == "" {
		return nil
	}

	// deleteTask
	deleteTaskReq := dts.CommonRequest{
		DtsID: taskID,
		Auth:  auth,
	}
	_, err = c.dtsSdk.DeleteTask(ctx, &deleteTaskReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "delete task fail,err:%s", err.Error())
		return err
	}
	return nil
}

// 用于检查是否有检查项的状态为"ready"
func hasReadyStatus(results []dts.CheckResult) bool {
	for _, checkItem := range results {
		if checkItem.Status == "ready" {
			return true
		}
	}
	return false
}
