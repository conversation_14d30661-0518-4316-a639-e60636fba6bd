/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * author: wang<PERSON><PERSON> (<EMAIL>)
 * Date: 2024-04-10
 * File: dts_test.go
 */
package dts

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	dtsSDK "icode.baidu.com/baidu/scs/x1-base/sdk/dts"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest/sdkmock"
)

const (
	TestAppID            = "scs-test-app"
	TestAppIDNotExists   = "scs-test-app2"
	TestDtsID            = "dtsmtpjo0nym456mhqsf"
	TestTaskName         = "SCS_MODIFYTYPE:scs-test-app"
	TaskDtsStatus        = "running"
	TestSrcRedisIP       = "127.0.0.1"
	TestSrcRedisPort     = 6379
	TestSrcRedisPassword = ""
	TestDstRedisIP       = "*********"
	TestDstRedisPort     = 6379
	TestDstRedisPassword = ""
)

// TestCreateTaskExists 测试函数，用于创建任务并检查是否已经存在
// 参数：*testing.T - 单元测试的上下文对象，包含了对测试结果的反馈和记录
// 返回值：无
func TestCreateTaskExists(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDtsSDK := sdkmock.NewMockDTSService(ctrl)
	mockDtsSDK.EXPECT().ListTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.ListTaskRequest) (rsp *dtsSDK.ListTaskResponse, err error) {
			var dtsTasks []dtsSDK.ShowTaskResponse
			dtsTasks = append(dtsTasks, dtsSDK.ShowTaskResponse{
				DtsID:    TestDtsID,
				TaskName: TestTaskName,
				Status:   TaskDtsStatus,
			})

			rsp = &dtsSDK.ListTaskResponse{
				Task: dtsTasks,
			}
			return rsp, nil
		})
	mockDtsSDK.EXPECT().CreateTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.CreateTaskRequest) (rsp *dtsSDK.CreateTaskResponse, err error) {
			var dtsTasks []dtsSDK.DtsTask
			dtsTasks = append(dtsTasks, dtsSDK.DtsTask{DtsID: TestDtsID})

			rsp = &dtsSDK.CreateTaskResponse{
				DtsTasks: dtsTasks,
			}
			return rsp, nil
		})
	mockDtsSDK.EXPECT().ConfigTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.ConfigTaskRequest) (rsp *dtsSDK.ConfigTaskResponse, err error) {
			return nil, nil
		})

	testComponent := &component{
		conf: &Config{},
	}
	if err := compo_utils.LoadConf("dts", testComponent.conf); err != nil {
		t.Errorf("Read conf failed: %v", err)
	}
	testComponent.dtsSdk = mockDtsSDK
	createParams := CreateTaskParams{
		AppID:            TestAppID,
		SrcRedisIP:       TestSrcRedisIP,
		SrcRedisPort:     TestSrcRedisPort,
		SrcRedisPassword: TestSrcRedisPassword,
		DstRedisIP:       TestDstRedisIP,
		DstRedisPort:     TestDstRedisPort,
		DstRedisPassword: TestDstRedisPassword,
	}

	err := testComponent.CreateTask(ctx, &createParams)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

}

// TestCreateTaskNotExists 测试函数，用于创建任务（如果不存在）。
// 参数：*testing.T - t，表示单元测试的对象，用于记录错误信息。
// 返回值：无
func TestCreateTaskNotExists(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDtsSDK := sdkmock.NewMockDTSService(ctrl)
	mockDtsSDK.EXPECT().ListTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.ListTaskRequest) (rsp *dtsSDK.ListTaskResponse, err error) {
			var dtsTasks []dtsSDK.ShowTaskResponse
			dtsTasks = append(dtsTasks, dtsSDK.ShowTaskResponse{
				DtsID:    TestDtsID,
				TaskName: TestTaskName,
				Status:   TaskDtsStatus,
			})

			rsp = &dtsSDK.ListTaskResponse{
				Task: dtsTasks,
			}
			return rsp, nil
		})
	mockDtsSDK.EXPECT().CreateTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.CreateTaskRequest) (rsp *dtsSDK.CreateTaskResponse, err error) {
			var dtsTasks []dtsSDK.DtsTask
			dtsTasks = append(dtsTasks, dtsSDK.DtsTask{DtsID: TestDtsID})

			rsp = &dtsSDK.CreateTaskResponse{
				DtsTasks: dtsTasks,
			}
			return rsp, nil
		})
	mockDtsSDK.EXPECT().ConfigTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.ConfigTaskRequest) (rsp *dtsSDK.ConfigTaskResponse, err error) {
			return nil, nil
		})

	testComponent := &component{
		conf: &Config{},
	}
	if err := compo_utils.LoadConf("dts", testComponent.conf); err != nil {
		t.Errorf("Read conf failed: %v", err)
	}
	testComponent.dtsSdk = mockDtsSDK
	createParams := CreateTaskParams{
		AppID:            TestAppIDNotExists,
		SrcRedisIP:       TestSrcRedisIP,
		SrcRedisPort:     TestSrcRedisPort,
		SrcRedisPassword: TestSrcRedisPassword,
		DstRedisIP:       TestDstRedisIP,
		DstRedisPort:     TestDstRedisPort,
		DstRedisPassword: TestDstRedisPassword,
	}

	err := testComponent.CreateTask(ctx, &createParams)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

}

// TestPrecheckTask 测试PrecheckTask函数，该函数用于检查任务是否可以启动。
// 参数t：*testing.T类型，表示当前测试用例的对象指针。
// 返回值：无返回值
func TestPrecheckTask(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDtsSDK := sdkmock.NewMockDTSService(ctrl)
	mockDtsSDK.EXPECT().ListTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.ListTaskRequest) (rsp *dtsSDK.ListTaskResponse, err error) {
			var dtsTasks []dtsSDK.ShowTaskResponse
			dtsTasks = append(dtsTasks, dtsSDK.ShowTaskResponse{
				DtsID:    TestDtsID,
				TaskName: TestTaskName,
				Status:   TaskDtsStatus,
			})

			rsp = &dtsSDK.ListTaskResponse{
				Task: dtsTasks,
			}
			return rsp, nil
		})
	mockDtsSDK.EXPECT().LaunchTaskPrecheck(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.CommonRequest) (rsp *dtsSDK.CommonResponse, err error) {
			rsp = &dtsSDK.CommonResponse{
				Success: true,
			}
			return rsp, nil
		})
	mockDtsSDK.EXPECT().ShowTaskPrecheck(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.CommonRequest) (rsp *dtsSDK.ShowTaskPrecheckResponse, err error) {
			var result []dtsSDK.CheckResult
			result = append(result, dtsSDK.CheckResult{
				Name:         "dbCountLimit",
				Status:       "warning",
				Message:      "",
				Subscription: "",
			})
			rsp = &dtsSDK.ShowTaskPrecheckResponse{
				Success: true,
				Result:  result,
			}
			return rsp, nil
		})
	mockDtsSDK.EXPECT().SkipTaskPrecheck(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.CommonRequest) (rsp *dtsSDK.CommonResponse, err error) {
			rsp = &dtsSDK.CommonResponse{
				Success: true,
			}
			return rsp, nil
		})

	testComponent := &component{
		conf: &Config{},
	}
	if err := compo_utils.LoadConf("dts", testComponent.conf); err != nil {
		t.Errorf("Read conf failed: %v", err)
	}
	testComponent.dtsSdk = mockDtsSDK
	precheckParams := PrecheckTaskParams{
		AppID: TestAppID,
	}

	err := testComponent.PrecheckTask(ctx, &precheckParams)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}

// TestStartTask 测试函数，用于启动任务。
// 参数：
//   - t *testing.T: 指向 testing.T 类型的指针，表示当前测试用例。
//
// 返回值：
//   - (无)
func TestStartTask(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDtsSDK := sdkmock.NewMockDTSService(ctrl)
	mockDtsSDK.EXPECT().ListTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.ListTaskRequest) (rsp *dtsSDK.ListTaskResponse, err error) {
			var dtsTasks []dtsSDK.ShowTaskResponse
			dtsTasks = append(dtsTasks, dtsSDK.ShowTaskResponse{
				DtsID:    TestDtsID,
				TaskName: TestTaskName,
				Status:   TaskDtsStatus,
			})

			rsp = &dtsSDK.ListTaskResponse{
				Task: dtsTasks,
			}
			return rsp, nil
		})
	mockDtsSDK.EXPECT().StartTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.CommonRequest) (rsp *dtsSDK.CommonResponse, err error) {
			rsp = &dtsSDK.CommonResponse{
				Success: true,
			}
			return rsp, nil
		})

	testComponent := &component{
		conf: &Config{},
	}
	if err := compo_utils.LoadConf("dts", testComponent.conf); err != nil {
		t.Errorf("Read conf failed: %v", err)
	}
	testComponent.dtsSdk = mockDtsSDK
	startParams := StartTaskParams{
		AppID: TestAppID,
	}

	err := testComponent.StartTask(ctx, &startParams)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}

// TestCheckTaskSync 测试检查同步任务是否成功，参数为*testing.T，返回值为error
func TestCheckTaskSync(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDtsSDK := sdkmock.NewMockDTSService(ctrl)
	mockDtsSDK.EXPECT().ListTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.ListTaskRequest) (rsp *dtsSDK.ListTaskResponse, err error) {
			var dtsTasks []dtsSDK.ShowTaskResponse
			dtsTasks = append(dtsTasks, dtsSDK.ShowTaskResponse{
				DtsID:    TestDtsID,
				TaskName: TestTaskName,
				Status:   TaskDtsStatus,
			})

			rsp = &dtsSDK.ListTaskResponse{
				Task: dtsTasks,
			}
			return rsp, nil
		})
	mockDtsSDK.EXPECT().ShowTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.ShowTaskRequest) (rsp *dtsSDK.ShowTaskResponse, err error) {
			rsp = &dtsSDK.ShowTaskResponse{
				DtsID:    TestDtsID,
				TaskName: TestTaskName,
				Status:   TaskDtsStatus,
				DynamicInfo: &dtsSDK.DynamicInfo{
					Increment: dtsSDK.Increment{
						Delay:      0,
						SyncStatus: "正常",
					},
				},
			}
			return rsp, nil
		})

	testComponent := &component{
		conf: &Config{},
	}
	if err := compo_utils.LoadConf("dts", testComponent.conf); err != nil {
		t.Errorf("Read conf failed: %v", err)
	}
	testComponent.dtsSdk = mockDtsSDK
	checkParams := CheckTaskParams{
		AppID: TestAppID,
	}

	err := testComponent.CheckTaskSync(ctx, &checkParams)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}

// TestShutdownTask 测试ShutdownTask函数，该函数用于关闭一个任务
// 参数t *testing.T：表示单元测试的上下文，必须传入
// 返回值error：如果有错误发生，则返回非nil错误
func TestShutdownTask(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDtsSDK := sdkmock.NewMockDTSService(ctrl)
	mockDtsSDK.EXPECT().ListTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.ListTaskRequest) (rsp *dtsSDK.ListTaskResponse, err error) {
			var dtsTasks []dtsSDK.ShowTaskResponse
			dtsTasks = append(dtsTasks, dtsSDK.ShowTaskResponse{
				DtsID:    TestDtsID,
				TaskName: TestTaskName,
				Status:   TaskDtsStatus,
			})

			rsp = &dtsSDK.ListTaskResponse{
				Task: dtsTasks,
			}
			return rsp, nil
		})
	mockDtsSDK.EXPECT().ShowTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.ShowTaskRequest) (rsp *dtsSDK.ShowTaskResponse, err error) {
			rsp = &dtsSDK.ShowTaskResponse{
				DtsID:    TestDtsID,
				TaskName: TestTaskName,
				Status:   "finished",
			}
			return rsp, nil
		})

	testComponent := &component{
		conf: &Config{},
	}
	if err := compo_utils.LoadConf("dts", testComponent.conf); err != nil {
		t.Errorf("Read conf failed: %v", err)
	}
	testComponent.dtsSdk = mockDtsSDK
	shutdownParams := ShutdownTaskParams{
		AppID: TestAppID,
	}

	err := testComponent.ShutdownTask(ctx, &shutdownParams)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}

// TestDeleteTask 测试删除任务函数DeleteTask，包括对DTS SDK的调用和返回值的验证。
// 参数t是*testing.T类型，表示当前正在运行的单元测试；DeleteTask函数会使用该参数来记录错误信息。
// 返回值没有，但如果DeleteTask函数中发生了错误，将通过t.Errorf输出错误信息。
func TestDeleteTask(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDtsSDK := sdkmock.NewMockDTSService(ctrl)
	mockDtsSDK.EXPECT().ListTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.ListTaskRequest) (rsp *dtsSDK.ListTaskResponse, err error) {
			var dtsTasks []dtsSDK.ShowTaskResponse
			dtsTasks = append(dtsTasks, dtsSDK.ShowTaskResponse{
				DtsID:    TestDtsID,
				TaskName: TestTaskName,
				Status:   TaskDtsStatus,
			})

			rsp = &dtsSDK.ListTaskResponse{
				Task: dtsTasks,
			}
			return rsp, nil
		})
	mockDtsSDK.EXPECT().DeleteTask(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *dtsSDK.CommonRequest) (rsp *dtsSDK.CommonResponse, err error) {
			rsp = &dtsSDK.CommonResponse{
				Success: true,
			}
			return rsp, nil
		})

	testComponent := &component{
		conf: &Config{},
	}
	if err := compo_utils.LoadConf("dts", testComponent.conf); err != nil {
		t.Errorf("Read conf failed: %v", err)
	}
	testComponent.dtsSdk = mockDtsSDK
	deleteParams := DeleteTaskParams{
		AppID: TestAppID,
	}

	err := testComponent.DeleteTask(ctx, &deleteParams)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}
