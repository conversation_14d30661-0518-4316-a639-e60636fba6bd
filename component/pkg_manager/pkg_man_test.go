/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* pkg_man_test.go */
/*
modification history
--------------------
2023/04/10 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package pkg_manager

import (
	"context"
	"testing"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

func TestInit(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	println(env.IDC())
	println(GetDownloadUrl("test.tar.gz", -1))
	println(SpecificPkgGetDownloadUrl("bd.bcebos.com", "scs-package-manager-onlinebd", "test.tar.gz", -1))

	env.Default = env.New(env.Option{
		AppName: "a",
		IDC:     "dbstack-test",
		RunMode: "",
		RootDir: "",
		DataDir: "",
		LogDir:  "",
		ConfDir: "",
	})
	println(env.IDC())
	println(GetDownloadUrl("test.tar.gz", -1))

	_ = BosClient()
}
