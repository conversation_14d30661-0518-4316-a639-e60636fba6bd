/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* init.go */
/*
modification history
--------------------
2023/04/10 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package pkg_manager

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"sync"

	"github.com/baidubce/bce-sdk-go/services/bos"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type pkgmanConf struct {
	BosAK       string
	BosSK       string
	BosEndpoint string
	BosBucket   string
}

type PkgManagerInst struct {
	initOnce  sync.Once
	conf      *pkgmanConf
	bosClient *bos.Client
}

var defaultPkgManager = &PkgManagerInst{
	conf: &pkgmanConf{},
}

type PkgManager interface {
	GetDownloadUrl(filename string, timeoutSec int) (downloadUrl string)
	SpecificPkgGetDownloadUrl(BosEndpoint string, BosBucket string, filename string, timeoutSec int) (downloadUrl string)
	BosClient() *bos.Client
}

func initPkgMan() {
	// load default conf

	if err := compo_utils.LoadConf("pkg_man", defaultPkgManager.conf); err != nil {
		panic(err.Error())
	}
	logger.ComponentLogger.Trace(context.Background(), "load pkg man conf suc,conf:%s", base_utils.Format(defaultPkgManager.conf))

	clientConfig := bos.BosClientConfiguration{
		Ak:               defaultPkgManager.conf.BosAK,
		Sk:               defaultPkgManager.conf.BosSK,
		Endpoint:         defaultPkgManager.conf.BosEndpoint,
		RedirectDisabled: false,
	}

	bosClient, err := bos.NewClientWithConfig(&clientConfig)
	if err != nil {
		panic(err.Error())
	}
	defaultPkgManager.bosClient = bosClient
}

func GetDownloadUrl(filename string, timeoutSec int) (downloadUrl string) {
	defaultPkgManager.initOnce.Do(initPkgMan)
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return fmt.Sprintf("%s/%s", defaultPkgManager.conf.BosEndpoint, filename)
	}
	return defaultPkgManager.bosClient.BasicGeneratePresignedUrl(defaultPkgManager.conf.BosBucket, filename, timeoutSec)
}

func SpecificPkgGetDownloadUrl(BosEndpoint string, BosBucket string, filename string, timeoutSec int) (downloadUrl string) {
	defaultPkgManager.initOnce.Do(initPkgMan)
	clientConfig := bos.BosClientConfiguration{
		Ak:               defaultPkgManager.conf.BosAK,
		Sk:               defaultPkgManager.conf.BosSK,
		Endpoint:         BosEndpoint,
		RedirectDisabled: false,
	}

	bosClient, err := bos.NewClientWithConfig(&clientConfig)
	if err != nil {
		panic(err.Error())
	}
	return bosClient.BasicGeneratePresignedUrl(BosBucket, filename, timeoutSec)
}

func BosClient() *bos.Client {
	defaultPkgManager.initOnce.Do(initPkgMan)
	return defaultPkgManager.bosClient
}
