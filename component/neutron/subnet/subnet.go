/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
操作虚机安全组相关方法
*/

package subnet

import (
	"context"
	"sync"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc/neutron"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/network"
)

type config struct {
	RetainIpCount       int
	SupportedIPTypes    []string
	SubnetReservedIpNum int64
	NeedDeleteIpNum     int64
}

type GetAvailableIPCountParams struct {
	UserID string
	Subnet []string
}

type CheckSubnetsSupportIPV6Params struct {
	UserID string
	Subnet []string
}

type component struct {
	conf       *config
	vpcSdk     bcc.NeutronVpcService
	neutronSdk bcc.NeutronService
	stsSdk     sts.StsService
	initOnce   sync.Once
}

var defaultComponent = &component{
	conf: &config{},
}

func Instance() *component {
	defaultComponent.initOnce.Do(func() {
		if err := compo_utils.LoadConf("subnet", defaultComponent.conf); err != nil {
			panic(err.Error())
		}
		defaultComponent.neutronSdk = neutron.NewNeutronSdk(neutron.DefaultServiceName)
		defaultComponent.stsSdk = sts.NewStsSdk(sts.DefaultServiceName)
		defaultComponent.vpcSdk = neutron.NewNeutronVpcSdk(neutron.DefaultVpcServiceName)
	})
	return defaultComponent
}

// GetAvailableIPCount 获取子网可用的IP数量, 返回子网ID-可用ip数量的map
// 1. 获取的可用IP = 总IP数量 - 已用IP数量 - Config.RetainIpCount
func (c *component) GetAvailableIPCount(ctx context.Context, params *GetAvailableIPCountParams) (ret map[string]int64, err error) {
	logger.ComponentLogger.Trace(ctx, "start to check available ip num", logit.String("params", base_utils.Format(params)))
	ret = make(map[string]int64, 0)
	stsReq := &sts.GetAssumeRoleRequest{
		IamUserId:     params.UserID,
		TransactionId: base_utils.GetReqID(ctx),
	}

	stsRsp, err := c.stsSdk.GetAssumeRole(ctx, stsReq)
	if err != nil {
		return ret, err // todo 细化err
	}
	for _, subnetId := range params.Subnet {
		totalIpNum, err := c.getSubnetTotalIpNum(ctx, subnetId, stsRsp.Token.Id)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "get subnet total ip num fail", logit.String("subnetid", subnetId),
				logit.Error("err", err))
			return ret, err // todo 细化err
		}
		usedIpNum, err := c.getSubnetUsedIpNum(ctx, subnetId, stsRsp.Token.Id)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "get subnet used ip num fail", logit.String("subnetid", subnetId),
				logit.Error("err", err))
			return ret, err // todo 细化err
		}
		ret[subnetId] = totalIpNum - usedIpNum - c.conf.NeedDeleteIpNum
		if ret[subnetId] < 0 {
			ret[subnetId] = 0
		}
	}

	return
}

func (c *component) getSubnetTotalIpNum(ctx context.Context, subnetId string, token string) (num int64, err error) {
	getSubnetInfoReq := &bcc.GetSubnetInfoRequest{
		SubnetId: subnetId,
		Token:    token,
	}
	getSubnetInfoRsp, err := c.neutronSdk.GetSubnetInfo(ctx, getSubnetInfoReq)
	if err != nil {
		return 0, err
	}

	cidr := getSubnetInfoRsp.Subnet.Cidr
	num, err = network.CidrToIpNum(cidr)
	if err != nil {
		return 0, err
	}
	num -= c.conf.SubnetReservedIpNum
	return
}

func (c *component) getSubnetUsedIpNum(ctx context.Context, subnetId string, token string) (num int64, err error) {
	getSubnetIpIdReq := &bcc.GetSubnetIpIdRequest{
		SubnetId: subnetId,
		Token:    token,
	}
	getSubnetIpIdRsp, err := c.neutronSdk.GetSubnetIpId(ctx, getSubnetIpIdReq)
	if err != nil {
		return 0, err
	}
	portsSize := len(getSubnetIpIdRsp.Ports)
	num = cast.ToInt64(portsSize)
	return
}

// CheckSubnetsSupportIPV6 检查子网是否支持ipv6
//  1. 获取子网type
//     a) 如果子网均支持ipv6，且配置可以创建ipv6实例，返回true
//     b) 如果子网均不支持ipv6，返回false
//     c) 其他情况报错退出
func (c *component) CheckSubnetsSupportIPV6(ctx context.Context, params *CheckSubnetsSupportIPV6Params) (ret bool, err error) {
	ret = false
	if params == nil {
		return false, errors.Errorf("params is nilptr")
	}
	if len(params.Subnet) == 0 {
		return false, errors.Errorf("need subnet")
	}
	if len(params.UserID) == 0 {
		return false, errors.Errorf("need iamUserId")
	}

	stsReq := &sts.GetAssumeRoleRequest{
		IamUserId:     params.UserID,
		TransactionId: base_utils.GetReqID(ctx),
	}

	stsRsp, err := c.stsSdk.GetAssumeRole(ctx, stsReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "GetAssumeRole Fail,req: %+v", stsReq)
		return false, err
	}
	logger.ComponentLogger.Trace(ctx, "GetAssumeRole Req:%+v, Rsp: %+v", stsReq, stsRsp)
	enableIpv6Num := 0

	for _, subnetId := range params.Subnet {
		getIpTypeReq := &bcc.GetSubnetIpTypeRequest{SubnetId: subnetId, Token: stsRsp.Token.Id}
		getIpTypeRsp, err := c.vpcSdk.GetSubnetIpType(ctx, getIpTypeReq)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "GetSubnetIpType Fail,error:%s , req:%+v", err.Error(), getIpTypeReq)
			return false, errors.Errorf("GetSubnetIpType Fail,error:%s , req:%+v", err.Error(), getIpTypeReq)
		}
		if getIpTypeRsp == nil {
			logger.ComponentLogger.Warning(ctx, "GetSubnetIpType Rsp is Nilptr , req:%+v", getIpTypeReq)
			return false, errors.Errorf("GetSubnetIpType Rsp is Nilptr , req:%+v", getIpTypeReq)
		}
		logger.ComponentLogger.Trace(ctx, "GetSubnetIpType Req:%+v, Rsp: %+v", getIpTypeReq, getIpTypeRsp)
		if getIpTypeRsp.Subnet.EnableIpv6 {
			enableIpv6Num++
		}

	}

	if enableIpv6Num > 0 && enableIpv6Num != len(params.Subnet) {
		logger.ComponentLogger.Trace(ctx, "Not All Enable Ipv6")
		return false, nil
	}

	if len(c.conf.SupportedIPTypes) == 0 {
		logger.ComponentLogger.Warning(ctx, "Conf Has No Ip Protocol")
		return false, errors.Errorf("Conf Has No Ip Protocol")
	}

	if enableIpv6Num != 0 {
		// 支持ipv6
		confSupportV6, err := base_utils.InArray("ipv6", c.conf.SupportedIPTypes)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "Check If Support Ipv6 In Conf Fail")
			return false, errors.Errorf("Check If Support Ipv6 In Conf Fail")
		}
		logger.ComponentLogger.Trace(ctx, "Conf's Support Protocal:%+v", confSupportV6)
		if confSupportV6 {
			ret = true
		}
	}

	return
}

func (c *component) GetSubnetDetail(ctx context.Context, userId string, subnetId string) (resp *bcc.GetSubnetInfoResponse, err error) {
	token, err := compo_utils.GetOpenapiToken(ctx, userId)
	if err != nil {
		return
	}
	req := &bcc.GetSubnetInfoRequest{
		SubnetId: subnetId,
		Token:    token,
	}
	resp, err = c.neutronSdk.GetSubnetInfo(ctx, req)
	return
}

func (c *component) ListNetworks(ctx context.Context, userId string) (resp *bcc.ListNetworkResponse, err error) {
	token, err := compo_utils.GetOpenapiToken(ctx, userId)
	if err != nil {
		return
	}
	req := &bcc.ListNetworkRequest{
		Token: token,
	}
	resp, err = c.neutronSdk.ListNetwork(ctx, req)
	return
}

func (c *component) GetSubnets(ctx context.Context, userId string) (resp *bcc.GetSubnetsResponse, err error) {
	token, err := compo_utils.GetOpenapiToken(ctx, userId)
	if err != nil {
		return
	}
	req := &bcc.GetSubnetsRequest{
		Token: token,
	}
	resp, err = c.neutronSdk.GetSubnets(ctx, req)
	return
}
