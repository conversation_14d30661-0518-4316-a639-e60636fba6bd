/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/06/06 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file endpoint.go
 * <AUTHOR>
 * @date 2022/06/06 16:05:01
 * @brief neutron vpc endpoint component
 *
 **/

package endpoint

import (
	"context"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc/neutron"
)

// CreateEndpointParams definition
type CreateEndpointParams struct {
	VpcID    string
	SubnetID string
	Service  string
	UserID   string
	Product  string
}

// CommonEndpointParams definition
type CommonEndpointParams struct {
	EndpointID string
	UserID     string
}

// ListEndpointParams definition
type ListEndpointParams struct {
	VpcID  string
	UserID string
}

// EndPoint definition
type EndPoint struct {
	EndpointID  string
	Name        string
	IPAddress   string
	Status      string
	Service     string
	SubnetID    string
	Description string
	CreateTime  string
	ProductType string
	VpcID       string
}

type component struct {
	neutronEndpointSdk bcc.NeutronEndpointService
	initOnce           sync.Once
}

var defaultComponent = &component{}

// Instance 单例
func Instance() *component {
	defaultComponent.initOnce.Do(func() {
		defaultComponent.neutronEndpointSdk = neutron.NewNeutronEndpointSdk(neutron.DefaultEndpointServiceName)
	})
	return defaultComponent
}

// CreateEndpoint will create endpoint by vpc
func (c *component) CreateEndpoint(ctx context.Context, params *CreateEndpointParams) (endpointID, ipAddr string, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get Token Fail, userid:%s , err:%s", params.UserID, err.Error())
		return "", "", err
	}

	endpointName := "scsvpcendpoint"
	if params.Product != "" {
		endpointName = params.Product + "vpcendpoint"
	}
	endpointParam := bcc.EndpointParams{
		VpcID:       params.VpcID,
		Name:        endpointName,
		SubnetID:    params.SubnetID,
		Service:     params.Service,
		Description: "",
		IPAddress:   "",
		Billing: &bcc.Billing{
			PaymentTiming: "Postpaid",
		},
	}
	endpoint := bcc.CreateEndpointRequest{
		EndpointParams: endpointParam,
		Auth:           auth,
	}

	createEndpointResponse, err := c.neutronEndpointSdk.CreateEndpoint(ctx, &endpoint)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Create vpc endpoint Fail,req:%+v, token:%s, err:%s", endpoint, endpoint.Auth.IamUserId, err.Error())
		return "", "", err
	}
	logger.ComponentLogger.Trace(ctx, "Create endpoint Suc,req:%+v, user:%s createendpoint_response:%+v", endpoint, endpoint.Auth.IamUserId, createEndpointResponse)
	return createEndpointResponse.Endpoint.ID, createEndpointResponse.Endpoint.IPAddress, nil
}

// DeleteEndpoint will delete endpoint by vpc
func (c *component) DeleteEndpoint(ctx context.Context, params *CommonEndpointParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get Token Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}

	endpoint := bcc.DeleteEndpointRequest{
		EndpointID: params.EndpointID,
		Auth:       auth,
	}

	err = c.neutronEndpointSdk.DeleteEndpoint(ctx, &endpoint)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "delete vpc endpoint Fail,req:%+v, user:%s, err:%s", endpoint, auth.IamUserId, err.Error())
		return err
	}
	return nil
}

// DeleteEndpoint will delete endpoint by vpc
func (c *component) GetEndpointDetail(ctx context.Context, params *CommonEndpointParams) (EndPoint, error) {
	var ret EndPoint
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get Token Fail, userid:%s , err:%s", params.UserID, err.Error())
		return ret, err
	}

	endpoint := bcc.GetEndpointDetailRequest{
		EndpointID: params.EndpointID,
		Auth:       auth,
	}

	resp, err := c.neutronEndpointSdk.GetEndpointDetail(ctx, &endpoint)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get vpc endpoint Fail,req:%+v, user:%s, err:%s", endpoint, auth.IamUserId, err.Error())
		return ret, err
	}
	ret = EndPoint{
		VpcID:       resp.VpcID,
		EndpointID:  resp.EndpointID,
		Name:        resp.Name,
		IPAddress:   resp.IPAddress,
		Status:      resp.Status,
		Service:     resp.Service,
		SubnetID:    resp.SubnetID,
		Description: resp.Description,
		CreateTime:  resp.CreateTime,
		ProductType: resp.ProductType,
	}
	return ret, nil
}

// GetEndpointList will list endpoint by vpc
func (c *component) GetEndpointList(ctx context.Context, params *ListEndpointParams) ([]EndPoint, error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get Token Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}

	endpoint := bcc.GetEndpointListRequest{
		VpcID: params.VpcID,
		Auth:  auth,
	}

	resp, err := c.neutronEndpointSdk.GetEndpointList(ctx, &endpoint)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get vpc endpoint list Fail,req:%+v, user:%s, err:%s", endpoint, auth.IamUserId, err.Error())
		return nil, err
	}
	length := len(resp.Endpoints)
	ret := make([]EndPoint, 0)
	for i := 0; i < length; i++ {
		ret = append(ret, EndPoint(*resp.Endpoints[i]))
	}
	return ret, nil
}
