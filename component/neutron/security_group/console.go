/*
modification history
--------------------
2022/01/24, by shangshuai02(<EMAIL>), create
*/

/*
DESCRIPTION
使用console接口操作虚机安全组相关方法封装
*/

package security_group

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/console"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type UnbindVpcSecurityGroupsParams struct {
	UserId string
	AppId  string
}

// UnbindVpcSecurityGroups 列举并删除指定app的vpc sg
func UnbindVpcSecurityGroups(ctx context.Context, params *UnbindVpcSecurityGroupsParams) error {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserId)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail, err: %s", err)
		return err
	}

	s := console.NewDefaultConsoleSdk()
	listReq := &console.GetSecurityGroupIdsRequest{
		InstanceId:   params.AppId,
		InstanceType: "",
		Auth:         auth,
	}
	listRsp, err := s.GetSecurityGroupIds(ctx, listReq)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "list vpc sg fail, appId: %s, err: %s", params.AppId, err)
		return err
	}
	if listRsp.Result == nil || len(listRsp.Result.SecurityGroups) == 0 {
		logger.ComponentLogger.Trace(ctx, "no vpc sg bound, appId: %s, listRsp: %s",
			params.AppId, base_utils.Format(listRsp))
		return nil
	}

	sgUUIDs := make([]string, len(listRsp.Result.SecurityGroups))
	for i, sg := range listRsp.Result.SecurityGroups {
		sgUUIDs[i] = sg.Id
	}

	unbindReq := &console.UnbindVpcSecurityGroupsRequest{
		InstanceId:         params.AppId,
		InstanceType:       "",
		SecurityGroupUuids: sgUUIDs,
		Auth:               auth,
	}

	_, err = s.UnbindVpcSecurityGroups(ctx, unbindReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "unbind vpc sg fail, appId: %s, err: %s", params.AppId, err)
		return err
	}

	return nil
}
