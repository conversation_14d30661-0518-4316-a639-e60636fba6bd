/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
操作虚机安全组相关方法
*/

package security_group

import (
	"context"
	"strings"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc/neutron"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type config struct {
	WhiteIPs     []string
	XAgentPort   int
	GServiceName string
	//CsmasterMasterIp   string
	//CsmasterSlaveIp    string
	//MetaserverMasterIp string
	//MetaserverSlaveIp  string
	DefaultSgIpList []string
	DefaultVmPort   int32
}

type CreateSecurityGroupParams struct {
	XAuthToken string
	UserID     string
	VpcID      string
}

type DeleteSecurityGroupParams struct {
	UserID          string
	SecurityGroupID string
}

type InitSecurityGroupRulesParams struct {
	UserID          string
	SecurityGroupID string
	ServicePorts    []int
	IsEnableIPV6    bool
	VpcID           string
	Ports           []int32
}

type UpdateSecurityGroupRulesParams struct {
	SecurityGroupID string
	ServicePorts    []int
	ClientIPs       []string
	ClientIPV6s     []string
	NeedIPV6        bool
}

type ReopenSecurityGroupRulesParams struct {
	SecurityGroupID string
	ServicePorts    []int32
	WhitelistIPs    []string
	UserId          string
	IsEnableIpv6    bool
}

type CreateShardSgAndDefaultRulesParams struct {
	UserID       string
	VpcID        string
	IsEnableIpv6 bool
}

type CreateSecurityRuleParams struct {
	XAuthToken string
	IAmUserId  string
	SgId       string
	port       int32
	ip         string
}

type DeleteSecurityRulesPortParams struct {
	SgId   string
	UserId string
	Port   int32
}

type UnbindSecurityGroupParams struct {
	UserId string
	UUIDs  []string
}

type component struct {
	conf       *config
	neutronSdk bcc.NeutronService
	stsSdk     sts.StsService
	initOnce   sync.Once
}

var defaultComponent = &component{
	conf: &config{},
}

func Instance() *component {
	defaultComponent.initOnce.Do(func() {
		if err := compo_utils.LoadConf("security-group", defaultComponent.conf); err != nil {
			panic(err.Error())
		}
		defaultComponent.neutronSdk = neutron.NewNeutronSdk(neutron.DefaultServiceName)
		defaultComponent.stsSdk = sts.NewStsSdk(sts.DefaultServiceName)
	})
	return defaultComponent
}

// CreateSecurityGroup 创建安全组
// 创建安全组，并返回安全组id.
// 1. 创建安全组
// 2. x1-base.record.Record() 记录创建安全组行为，便于后续资源审计
// BCCS_CSMASTER_ERROR_NO NeutronComponents::create_security_group
func (c *component) CreateSecurityGroup(ctx context.Context, params *CreateSecurityGroupParams) (securityGroupId string, err error) {
	token := params.XAuthToken
	if token == "" {
		if token, err = compo_utils.GetOpenapiToken(ctx, params.UserID); err != nil {
			return
		}
	}
	createSecurityGroupReq := bcc.CreateSecurityGroupRequest{
		SecurityGroup: &bcc.SecurityGroupReq{
			Name:        params.UserID,
			Description: params.XAuthToken,
			Creator:     c.conf.GServiceName,
			VpcId:       params.VpcID,
		},
		Token: token,
	}
	createSecurityRsp, err := c.neutronSdk.CreateSecurityGroup(ctx, &createSecurityGroupReq)
	if err != nil {
		return "", err
	}

	return createSecurityRsp.SecurityGroup.Id, nil
}

func (c *component) DeleteSecurityGroup(ctx context.Context, params *DeleteSecurityGroupParams) (err error) {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return
	}
	deleteSecurityGroupReq := &bcc.DeleteSecurityGroupRequest{
		SecurityGroupId: params.SecurityGroupID,
		Token:           token,
	}
	_, err = c.neutronSdk.DeleteSecurityGroup(ctx, deleteSecurityGroupReq)
	if cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Trace(ctx, "DeleteSecurityGroup not found, securityGroupID: %s",
			params.SecurityGroupID)
		return nil
	}
	return
}

func (c *component) CreateSecurityRule(ctx context.Context, params *CreateSecurityRuleParams) (rsp *bcc.CreateSecurityGroupRulesResponse, err error) {
	token := params.XAuthToken
	if token == "" {
		if token, err = compo_utils.GetOpenapiToken(ctx, params.IAmUserId); err != nil {
			return
		}
	}
	req := bcc.CreateSecurityGroupRulesRequest{
		SecurityGroupRule: &bcc.SecurityGroupRules{
			Direction:       "ingress",
			Ethertype:       "IPv4",
			PortRangeMax:    params.port,
			PortRangeMin:    params.port,
			Protocol:        "tcp",
			SecurityGroupId: params.SgId,
			Creator:         c.conf.GServiceName,
		},
		Token: token,
	}
	if params.ip != "" {
		req.SecurityGroupRule.RemoteIpPrefix = params.ip
	}
	return c.neutronSdk.CreateSecurityGroupRules(ctx, &req)
}

// InitSecurityGroupRules 初始化安全组规则
// STEP1 创建一个新的安全组
// STEP2 把所有ip的对用户设置/默认的对外服务PORT的ingress权限打开（不传ip即为全部）
// STEP3 把管控节点ip对22端口的ingress权限打开
// STEP4 把中控机的banet ips对XAGENT端口的权限打开
// STEP5 把ICMP开启
// todo 集群版
// BCCS_CSMASTER_ERROR_NO OriginalReqToGeneralReqProcessor::create_security_group
func (c *component) InitSecurityGroupRules(ctx context.Context, params *InitSecurityGroupRulesParams) (securityGroupId string, err error) {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return "", err
	}
	// ===============================STEP1 创建一个新的安全组===================================
	securityGroupId, err = c.CreateSecurityGroup(ctx, &CreateSecurityGroupParams{
		XAuthToken: token,
		UserID:     params.UserID,
		VpcID:      params.VpcID,
	})
	if err != nil {
		return "", err
	}

	// ========STEP2 把所有ip的对用户设置/默认的对外服务PORT的ingress权限打开（不传ip即为全部）========
	// 创建开启虚拟机端口的安全组规则，针对集群模式依然开启配置端口
	// 创建Proxy对外开放的端口
	for _, outerPort := range params.Ports {
		err = c.createSecurityRulesPort(ctx, outerPort, securityGroupId, token, params.IsEnableIPV6)
		if err != nil {
			return "", err
		}
	}

	// ========================STEP3 把管控节点ip对22端口的ingress权限打开========================
	defaultPorts := []int32{22, x1model.DefaultXagentPort}
	for _, ip := range c.conf.DefaultSgIpList {
		for _, defaultport := range defaultPorts {
			err = c.createSecurityRulesIpPort(ctx, defaultport, securityGroupId, token, ip)
			if err != nil {
				return "", nil
			}
		}
	}
	// ipv6 不支持单独ip，目前ip参数保留但实际没用
	if params.IsEnableIPV6 {
		for _, portV6 := range defaultPorts {
			err = c.createSecurityRulesIpPortIpv6ForAllIp(ctx, portV6, securityGroupId, token)
			if err != nil {
				return "", nil
			}
		}
	}

	// ===================================STEP5 把ICMP开启==================================
	rule := bcc.CreateSecurityGroupRulesRequest{
		SecurityGroupRule: &bcc.SecurityGroupRules{
			Direction:       "ingress",
			Ethertype:       "IPv4",
			Protocol:        "icmp",
			SecurityGroupId: securityGroupId,
			Creator:         c.conf.GServiceName,
		},
		Token: token,
	}
	createSecurityGroupRulesResponse, err := c.neutronSdk.CreateSecurityGroupRules(ctx, &rule)
	if err != nil {
		return "", err
	}
	logger.ComponentLogger.Trace(ctx, "createIcmpRules_Rule:%+v,token:%s, createIcmpRules_response:%+v", *rule.SecurityGroupRule, rule.Token, createSecurityGroupRulesResponse)

	if params.IsEnableIPV6 {
		rule.SecurityGroupRule.Ethertype = "IPv6"
		createSecurityGroupRulesResponse, err = c.neutronSdk.CreateSecurityGroupRules(ctx, &rule)
		if err != nil {
			return "", err
		}
		logger.ComponentLogger.Trace(ctx, "createIcmpRulesV6_Rule:%+v,token:%s,, createIcmpRulesV6_response:%+v", *rule.SecurityGroupRule, rule.Token, createSecurityGroupRulesResponse)
	}

	// todo V5

	return
}

// UpdateSecurityGroupRules 更新安全组规则
// 1. 如果params.ClientIPs中存在0.0.0.0或*.*.*.*；删除并重新创建ServicePorts对应的规则，不限制入向ip
// 2. 否则，查询ServicePorts对应的规则中的ip，对比params.ClientIPs，增加/删除规则中的ip
// 3. 如果params.NeedIPV6, 进行1、2的ipv6版本操作
func (c *component) UpdateSecurityGroupRules(ctx context.Context, params *UpdateSecurityGroupRulesParams) error {
	return nil
}

func (c *component) ReopenSecurityGroupRules(ctx context.Context, params *ReopenSecurityGroupRulesParams) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserId)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get Token Fail, userid:%s , err:%s", params.UserId, err.Error())
		return err
	}
	// merge 默认白名单ip
	logger.ComponentLogger.Trace(ctx, "before merge, param.whitelist: %s", base_utils.Format(params.WhitelistIPs))
	for _, defaultIp := range c.conf.DefaultSgIpList {
		inWhiteList, _ := base_utils.InArray(defaultIp, params.WhitelistIPs)
		if !inWhiteList {
			params.WhitelistIPs = append(params.WhitelistIPs, defaultIp)
		}
	}
	logger.ComponentLogger.Trace(ctx, "merge default ips, default ips: %s, after merge param.whitelist: %s",
		base_utils.Format(c.conf.DefaultSgIpList), base_utils.Format(params.WhitelistIPs))

	for _, port := range params.ServicePorts {
		isAll, _ := base_utils.InArray("*.*.*.*", params.WhitelistIPs)
		if isAll {
			err = c.deleteSecurityRulesPort(ctx, port, params.SecurityGroupID, token)
			if err != nil {
				return err
			}
			err = c.createSecurityRulesPort(ctx, port, params.SecurityGroupID, token, params.IsEnableIpv6)
			if err != nil {
				return err
			}
		} else {
			showRulesResp, err := c.showSecurityGroup(ctx, params.SecurityGroupID, token)
			if err != nil {
				return err
			}
			existIps := make([]string, 0)
			for _, rule := range showRulesResp.SecurityGroup.SecurityGroupRules {
				if rule.PortRangeMax == port && rule.PortRangeMin == port {
					ruleIp := rule.RemoteIpPrefix
					ruleIpSlice := strings.Split(ruleIp, "/32")
					if len(ruleIpSlice) == 0 {
						logger.ComponentLogger.Warning(ctx, "Ilegal Ip, ip:%s ", ruleIp)
						return cerrs.Errorf("Ilegal Ip, ip:%s ", ruleIp)
					}
					existIps = append(existIps, ruleIpSlice[0])
					needKeep, _ := base_utils.InArray(ruleIpSlice[0], params.WhitelistIPs)
					if !needKeep {
						logger.ComponentLogger.Trace(ctx, "Ip:%s , no need, deleting", ruleIpSlice[0])
						err = c.delSecurityRule(ctx, rule.Id, token)
						if err != nil {
							return err
						}
					}
				}
			}
			logger.ComponentLogger.Trace(ctx, "Elder Ips : %s", base_utils.Format(existIps))
			for _, newIp := range params.WhitelistIPs {
				hasExist, _ := base_utils.InArray(newIp, existIps)
				if !hasExist {
					logger.ComponentLogger.Trace(ctx, "Ip:%s , not exist, adding", newIp)
					err = c.createSecurityRulesIpPort(ctx, port, params.SecurityGroupID, token, newIp) //具体Ip不支持ipv6
					if err != nil {
						return err
					}
				}
			}
		}
		if params.IsEnableIpv6 {
			err = c.createSecurityRulesIpPortIpv6ForAllIp(ctx, port, params.SecurityGroupID, token) //具体Ip不支持ipv6
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (c *component) DeleteSecurityRulesPort(ctx context.Context, params *DeleteSecurityRulesPortParams) (err error) {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserId)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get Token Fail, userid:%s , err:%s", params.UserId, err.Error())
		return err
	}
	return c.deleteSecurityRulesPort(ctx, params.Port, params.SgId, token)
}

func (c *component) deleteSecurityRulesPort(ctx context.Context, port int32, sgId string, token string) (err error) {
	showRulesResp, err := c.showSecurityGroup(ctx, sgId, token)
	if cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Trace(ctx, "deleteSecurityRulesPort not found, securityGroupID: %s", sgId)
		return nil
	}
	if err != nil {
		return err
	}

	for _, rule := range showRulesResp.SecurityGroup.SecurityGroupRules {
		if rule.PortRangeMax == port && rule.PortRangeMin == port {
			err = c.delSecurityRule(ctx, rule.Id, token)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (c *component) createSecurityRulesPort(ctx context.Context, port int32, sgId string, token string, isEnableIPV6 bool) error {
	rule := bcc.CreateSecurityGroupRulesRequest{
		SecurityGroupRule: &bcc.SecurityGroupRules{
			Direction:       "ingress",
			Ethertype:       "IPv4",
			PortRangeMax:    port,
			PortRangeMin:    port,
			Protocol:        "tcp",
			SecurityGroupId: sgId,
			Creator:         c.conf.GServiceName,
		},
		Token: token,
	}
	createSecurityGroupRulesResponse, err := c.neutronSdk.CreateSecurityGroupRules(ctx, &rule)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Create Security Rule Fail,req:%+v, token:%s, err:%s", *rule.SecurityGroupRule, rule.Token, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "Create Security Rule Suc,req:%+v, token:%s, createServiceRules_response:%+v", *rule.SecurityGroupRule, rule.Token, createSecurityGroupRulesResponse)

	if isEnableIPV6 {
		rule.SecurityGroupRule.Ethertype = "IPv6"
		createSecurityGroupRulesResponse, err = c.neutronSdk.CreateSecurityGroupRules(ctx, &rule)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "Create Security Rule Fail,req:%+v, token:%s, err:%s", *rule.SecurityGroupRule, rule.Token, err.Error())
			return err
		}
		logger.ComponentLogger.Trace(ctx, "Create Security Rule Suc,req:%+v, token:%s, createServiceRulesV6_response:%+v", *rule.SecurityGroupRule, rule.Token, createSecurityGroupRulesResponse)
	}
	return nil
}

func (c *component) createSecurityRulesIpPort(ctx context.Context, port int32, sgId string, token string, ip string) error {

	rule := bcc.CreateSecurityGroupRulesRequest{
		SecurityGroupRule: &bcc.SecurityGroupRules{
			Direction:       "ingress",
			Ethertype:       "IPv4",
			PortRangeMax:    port,
			PortRangeMin:    port,
			Protocol:        "tcp",
			RemoteIpPrefix:  ip,
			SecurityGroupId: sgId,
			Creator:         c.conf.GServiceName,
		},
		Token: token,
	}
	createSecurityGroupRulesResponse, err := c.neutronSdk.CreateSecurityGroupRules(ctx, &rule)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Create Security Rule Fail,req:%+v, token:%s, err:%s", *rule.SecurityGroupRule, rule.Token, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "Create Security Rule Suc,req:%+v, token:%s, createSshRule_response:%+v", *rule.SecurityGroupRule, rule.Token, createSecurityGroupRulesResponse)

	//if isEnableIPV6 {
	//	rule.SecurityGroupRule.Ethertype = "IPv6"
	//	createSecurityGroupRulesResponse, err = c.neutronSdk.CreateSecurityGroupRules(ctx, &rule)
	//	if err != nil {
	//		logger.ComponentLogger.Warning(ctx, "Create Security Rule Fail,req:%+v, token:%s, err:%s", *rule.SecurityGroupRule, rule.Token, err.Error())
	//		return err
	//	}
	//	logger.ComponentLogger.Trace(ctx, "Create Security Rule Suc,req:%+v, token:%s createSshRuleV6_response:%+v", *rule.SecurityGroupRule, rule.Token, createSecurityGroupRulesResponse)
	//}
	return nil
}

// ipv6 只支持全开全关
func (c *component) createSecurityRulesIpPortIpv6ForAllIp(ctx context.Context, port int32, sgId string, token string) error {

	rule := bcc.CreateSecurityGroupRulesRequest{
		SecurityGroupRule: &bcc.SecurityGroupRules{
			Direction:       "ingress",
			Ethertype:       "IPv6",
			PortRangeMax:    port,
			PortRangeMin:    port,
			Protocol:        "tcp",
			SecurityGroupId: sgId,
			Creator:         c.conf.GServiceName,
		},
		Token: token,
	}
	createSecurityGroupRulesResponse, err := c.neutronSdk.CreateSecurityGroupRules(ctx, &rule)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Create Security Rule Fail,req:%+v, token:%s, err:%s", *rule.SecurityGroupRule, rule.Token, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "Create Security Rule Suc,req:%+v, token:%s createSshRuleV6_response:%+v", *rule.SecurityGroupRule, rule.Token, createSecurityGroupRulesResponse)
	return nil
}

func (c *component) showSecurityGroup(ctx context.Context, sgId string, token string) (*bcc.ShowSecurityGroupResponse, error) {
	showRulesReq := bcc.ShowSecurityGroupRequest{
		Token:           token,
		SecurityGroupId: sgId,
	}

	showRulesResp, err := c.neutronSdk.ShowSecurityGroup(ctx, &showRulesReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get Security Rules Fail, req:%+v , err:%s", showRulesReq, err.Error())
		return nil, err
	}
	logger.ComponentLogger.Trace(ctx, "Get Security Rules Suc, req:%+v , resp:%+v", showRulesReq, *showRulesResp)
	return showRulesResp, nil
}

func (c *component) delSecurityRule(ctx context.Context, ruleId string, token string) error {
	delReq := bcc.DeleteSecurityGroupRulesRequest{
		SecurityGroupRulesId: ruleId,
		Token:                token,
	}
	delResp, err := c.neutronSdk.DeleteSecurityGroupRules(ctx, &delReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Delete Security Rule Fail, req:%+v , err:%s", delReq, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "Delete Security Rule Suc, req:%+v , resp:%+v", delReq, *delResp)
	return nil
}

func (c *component) UnbindSecurityGroup(ctx context.Context, params *UnbindSecurityGroupParams) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserId)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get Token Fail, userid:%s , err:%s", params.UserId, err.Error())
		return err
	}

	// get ports
	portIds := map[string]bool{}
	for _, uuid := range params.UUIDs {
		getPortsReq := &bcc.GetPortsRequest{
			VmUuid: uuid,
			Token:  token,
		}
		getPortsRsp, err := c.neutronSdk.GetPorts(ctx, getPortsReq)
		if cerrs.ErrNotFound.Is(err) {
			continue
		}
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "get ports Fail, req:%+v, err:%s", *getPortsReq, err.Error())
			return err
		}
		for _, port := range getPortsRsp.Ports {
			portIds[port.Id] = true
		}
	}

	// unbind
	for portId := range portIds {
		unbindReq := &bcc.UnbindSecurityGroupRequest{
			PortId: portId,
			Token:  token,
		}

		_, err := c.neutronSdk.UnbindSecurityGroup(ctx, unbindReq)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "unbind sg Fail, req:%+v, err:%s", *unbindReq, err.Error())
			return err
		}
	}

	return nil
}

func (c *component) GetDefaultIps(ctx context.Context) []string {
	return c.conf.DefaultSgIpList
}

func (c *component) GetIpWhitelistInSg(ctx context.Context, iamUserId string, sgId string) ([]string, error) {
	token, err := compo_utils.GetOpenapiToken(ctx, iamUserId)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get Token Fail, userid:%s , err:%s", iamUserId, err.Error())
		return nil, err
	}
	showRulesResp, err := c.showSecurityGroup(ctx, sgId, token)
	if err != nil {
		return nil, err
	}
	iplist := make([]string, 0)
	for _, rule := range showRulesResp.SecurityGroup.SecurityGroupRules {
		splitIps := strings.Split(rule.RemoteIpPrefix, "/32")
		if len(splitIps) == 0 {
			logger.ComponentLogger.Warning(ctx, "ilegal ip, userid:%s , sgid:%s, ruleid:%s", iamUserId, sgId, rule.Id)
			continue
		}
		iplist = append(iplist, splitIps[0])
	}
	logger.ComponentLogger.Trace(ctx, "get iplist in sg suc, sgid:%s, iplist:%s", sgId, base_utils.Format(iplist))
	return iplist, nil

}

func (c *component) DefaultWhiteList() []string {
	return c.conf.DefaultSgIpList
}
