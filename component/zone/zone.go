/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
获取logicalzone与azone对应
*/

package zone

import (
	"context"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/sdk/zone"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	commUtils "icode.baidu.com/baidu/scs/x1-base/utils/common"
)

type ZoneMapperFunc func(zone string, isLogic bool) (string, bool)

type ZoneOpIface interface {
	GetZoneMap(ctx context.Context, userId string) (ZoneMapperFunc, error)
}

type zoneOpObj struct {
	stsSdk  sts.StsService
	zoneSdk zone.ZoneMapService
}

var once sync.Once

var zoneOpIfaceObj ZoneOpIface

func ZoneOp() ZoneOpIface {
	once.Do(func() {
		if privatecloud.IsPrivateENV() {
			zoneOpIfaceObj = GetPrivateZoneOP()
		} else if commUtils.IsEdgeRegion() {
			zoneOpIfaceObj = &FakeZone{}
		} else {
			zoneOpIfaceObj = &zoneOpObj{
				zoneSdk: zone.NewDefaultZoneSdk(),
				stsSdk:  sts.NewDefaultStsSdk(),
			}
		}
	})
	return zoneOpIfaceObj
}

func SetZoneOpForUt(z ZoneOpIface) {
	once.Do(func() {
		zoneOpIfaceObj = z
	})
}

func (m *zoneOpObj) GetZoneMap(ctx context.Context, userId string) (ZoneMapperFunc, error) {
	stsResp, err := m.stsSdk.GetAssumeRole(ctx, &sts.GetAssumeRoleRequest{
		IamUserId:     userId,
		TransactionId: base_utils.GetReqID(ctx),
	})
	if err != nil {
		return nil, cerrs.ErrAuthFail.Wrap(err)
	}
	zoneResp, err := m.zoneSdk.GetZoneMapList(ctx, &zone.ZoneMapListRequest{
		Auth: &common.Authentication{
			IamUserId:     userId,
			TransactionId: base_utils.GetReqID(ctx),
			Credential: &common.Credential{
				Ak:           stsResp.AccessKeyId,
				Sk:           stsResp.SecretAccessKey,
				SessionToken: stsResp.SessionToken,
			},
		},
	})
	if err != nil {
		return nil, err
	}
	laMap := make(map[string]string)
	alMap := make(map[string]string)
	for _, zonePair := range zoneResp.ZoneMapDetailList {
		laMap[zonePair.LogicalZone] = zonePair.PhysicalZone
		alMap[zonePair.PhysicalZone] = zonePair.LogicalZone
	}
	return func(zone string, isLogic bool) (string, bool) {
		if isLogic {
			r, found := laMap[zone]
			return r, found
		}
		r, found := alMap[zone]
		return r, found
	}, nil
}
