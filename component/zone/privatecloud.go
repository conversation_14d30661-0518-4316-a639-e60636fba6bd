/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2024/02/18
 * File: privatecloud.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package zone TODO package function desc
package zone

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/env"

	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
)

type liZone struct {
}

type FakeZone struct {
}

var liLaMap = make(map[string]string)
var liAlMap = make(map[string]string)

//	LoadZoneMap
//
// todo 要根理想对
func LoadZoneMap() {}

func (l *liZone) GetZoneMap(ctx context.Context, userId string) (ZoneMapperFunc, error) {
	return func(zone string, isLogic bool) (string, bool) {
		// todo 测试用
		return "euc01", true
		/*
			if isLogic {
				r, found := liLaMap[zone]
				return r, found
			}
			r, found := liAlMap[zone]
			return r, found
		*/
	}, nil
}

func (f *FakeZone) GetZoneMap(ctx context.Context, userId string) (ZoneMapperFunc, error) {
	return func(zone string, isLogic bool) (string, bool) {
		return zone, true
	}, nil
}

func GetPrivateZoneOP() ZoneOpIface {
	switch privatecloud.GetPrivateEnvType() {
	case privatecloud.EnvLiTest, privatecloud.EnvLiProd:
		return &liZone{}
	case privatecloud.DBStackPrefix:
		return &FakeZone{}
	default:
		panic(fmt.Sprintf("not support this idc:%s", env.IDC()))
	}
}
