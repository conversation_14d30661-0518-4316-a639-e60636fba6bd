package zone

import (
	"context"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"testing"
)

func TestFakeZone_GetZoneMap(t *testing.T) {
	env.Default = env.New(env.Option{
		IDC: "dbstack-test",
	})
	z := GetPrivateZoneOP()
	zoneMapper, err := z.GetZoneMap(context.Background(), "xxxx")
	if err != nil {
		t.<PERSON>rrorf("GetZoneMap failed: %v", err)
	}
	zone, found := zoneMapper("a", true)
	if !found || zone != "a" {
		t.<PERSON>rf("GetZoneMap failed: %v", zone)
	}
	zone, found = zoneMapper("a", false)
	if !found || zone != "a" {
		t.<PERSON>("GetZoneMap failed: %v", zone)
	}
	env.Default = env.New(env.Option{
		IDC: privatecloud.EnvLiTest,
	})
	z = GetPrivateZoneOP()
	zoneMapper, err = z.<PERSON>one<PERSON>(context.Background(), "xxxx")
	if err != nil {
		t.<PERSON><PERSON>("GetZoneMap failed: %v", err)
	}
	zone, found = zoneMapper("a", true)
	if !found || zone != "euc01" {
		t.Errorf("GetZoneMap failed: %v", zone)
	}
	zone, found = zoneMapper("a", false)
	if !found || zone != "euc01" {
		t.Errorf("GetZoneMap failed: %v", zone)
	}
	env.Default = env.New(env.Option{
		IDC: privatecloud.EnvLiTest,
	})
}
