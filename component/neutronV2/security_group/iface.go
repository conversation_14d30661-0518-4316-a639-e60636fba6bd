/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (<EMAIL>)
 * Date: 2024/02/08
 * File: iface.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package security_group TODO package function desc
package security_group

import (
	"context"
)

type SgOP interface {
	CreateSecurityGroup(ctx context.Context, params *CreateSecurityGroupParams) (securityGroupID string, err error)

	DeleteSecurityGroup(ctx context.Context, params *DeleteSecurityGroupParams) (err error)

	CreateSecurityRule(ctx context.Context, params *CreateSecurityRuleParams) (err error)

	InitSecurityGroupRules(ctx context.Context, params *InitSecurityGroupRulesParams) (securityGroupID string, err error)

	ReopenSecurityGroupRules(ctx context.Context, params *ReopenSecurityGroupRulesParams) error

	DeleteSecurityRulesPort(ctx context.Context, params *DeleteSecurityRulesPortParams) (err error)

	GetDefaultIps(ctx context.Context) []string

	GetIpWhitelistInSg(ctx context.Context, userID string, sgID string) ([]string, error)

	DefaultWhiteList() []string
}
