/*
modification history
--------------------
2023/08/08, by wang<PERSON><PERSON>@baidu.com, create
*/

/*
DESCRIPTION
操作虚机安全组相关方法
*/

package security_group

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	neutron "icode.baidu.com/baidu/scs/x1-base/sdk/bcc/neutronV2"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type config struct {
	WhiteIPs        []string
	XAgentPort      int
	GServiceName    string
	DefaultSgIpList []string
	DefaultVmPort   int32
}

type CreateSecurityGroupParams struct {
	UserID string
	VpcID  string
}

type DeleteSecurityGroupParams struct {
	UserID          string
	SecurityGroupID string
}

type InitSecurityGroupRulesParams struct {
	UserID          string
	SecurityGroupID string
	ServicePorts    []int
	IsEnableIPV6    bool
	VpcID           string
	Ports           []int32
}

type ReopenSecurityGroupRulesParams struct {
	SecurityGroupID string
	ServicePorts    []int32
	WhitelistIPs    []string
	UserID          string
	IsEnableIpv6    bool
}

type CreateSecurityRuleParams struct {
	UserID          string
	SecurityGroupID string
	port            string
	ip              string
}

type DeleteSecurityRulesPortParams struct {
	SecurityGroupID string
	UserID          string
	Port            int32
}

type component struct {
	conf       *config
	neutronSdk neutron.NeutronService
	stsSdk     sts.StsService
}

var once sync.Once

var sgComponentIns SgOP

// Instance 返回默认的组件实例
//
// 函数返回一个指向默认组件类型的指针。
// 如果默认组件尚未被初始化，则会调用一次 `Init` 方法进行初始化，并加载配置文件 `security-group`。
// 初始化过程中可能会发生错误，若出错将导致程序终止。
//
// 参数：无
//
// 返回值：
// - *component: 默认组件实例指针
func Instance() SgOP {
	once.Do(func() {
		if privatecloud.IsPrivateENV() {
			sgComponentIns = GetPrivateSg()
		} else {
			var defaultComponent = &component{
				conf: &config{},
			}
			if err := compo_utils.LoadConf("security-group", defaultComponent.conf); err != nil {
				panic(err.Error())
			}
			defaultComponent.neutronSdk = neutron.NewNeutronSdk(neutron.DefaultSecurityGroupServiceName)
			defaultComponent.stsSdk = sts.NewStsSdk(sts.DefaultServiceName)
			sgComponentIns = defaultComponent
		}
	})
	return sgComponentIns
}

// CreateSecurityGroup 创建安全组
// 创建安全组，并返回安全组id.
// 1. 创建安全组
// 2. x1-base.record.Record() 记录创建安全组行为，便于后续资源审计
// BCCS_CSMASTER_ERROR_NO NeutronComponents::create_security_group
func (c *component) CreateSecurityGroup(ctx context.Context, params *CreateSecurityGroupParams) (securityGroupID string, err error) {
	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     params.UserID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return
	}
	auth := authRsp.Auth

	// create
	productName := "scs"
	if c.conf.GServiceName != "" {
		productName = c.conf.GServiceName
	}
	sgName := fmt.Sprintf("%s_%s", productName, params.UserID)
	createSecurityGroupReq := neutron.CreateSecurityGroupRequest{
		Name:  sgName,
		Desc:  "x1 create",
		VpcID: params.VpcID,
		Auth:  auth,
	}
	createSecurityRsp, err := c.neutronSdk.CreateSecurityGroup(ctx, &createSecurityGroupReq)
	if err != nil {
		return "", err
	}

	return createSecurityRsp.SecurityGroupID, nil
}

// DeleteSecurityGroup 删除安全组
//
// Params:
//
//	ctx: 上下文对象
//	params: DeleteSecurityGroupParams 参数结构体指针，包含要删除的安全组 ID 和用户 ID
//
// Returns:
//
//	err: 错误信息
func (c *component) DeleteSecurityGroup(ctx context.Context, params *DeleteSecurityGroupParams) (err error) {
	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     params.UserID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return
	}
	auth := authRsp.Auth

	// delete
	deleteSecurityGroupReq := neutron.DeleteSecurityGroupRequest{
		SecurityGroupID: params.SecurityGroupID,
		Auth:            auth,
	}
	_, err = c.neutronSdk.DeleteSecurityGroup(ctx, &deleteSecurityGroupReq)
	if cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Trace(ctx, "DeleteSecurityGroup not found, securityGroupID: %s",
			params.SecurityGroupID)
		return nil
	}
	return
}

// CreateSecurityRule 创建安全规则(当前未用到此函数)
//
// 参数：
//
//	ctx: 上下文对象
//	params: 创建安全规则的参数
//
// 返回值：
//
//	错误信息，如果创建成功则为空
func (c *component) CreateSecurityRule(ctx context.Context, params *CreateSecurityRuleParams) (err error) {
	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     params.UserID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return
	}
	auth := authRsp.Auth

	// create rule
	req := neutron.CreateSecurityGroupRulesRequest{
		SecurityGroupID: params.SecurityGroupID,
		Rule: &neutron.SecurityGroupRuleModel{
			Protocol:        "tcp",
			Ethertype:       "IPv4",
			PortRange:       params.port,
			SecurityGroupID: params.SecurityGroupID,
			Direction:       "ingress",
		},
		Auth: auth,
	}
	if params.ip != "" {
		req.Rule.SourceIP = params.ip
	} else {
		req.Rule.SourceIP = "all"
	}
	_, err = c.neutronSdk.CreateSecurityGroupRules(ctx, &req)
	if err != nil {
		return err
	}
	return nil
}

// InitSecurityGroupRules 初始化安全组规则
// STEP1 创建一个新的安全组
// STEP2 把所有ip的对用户设置/默认的对外服务PORT的ingress权限打开（不传ip即为全部）
// STEP3 把管控节点ip对22端口的ingress权限打开
// STEP4 把中控机的banet ips对XAGENT端口的权限打开
// STEP5 把ICMP开启
// todo 集群版
// BCCS_CSMASTER_ERROR_NO OriginalReqToGeneralReqProcessor::create_security_group
func (c *component) InitSecurityGroupRules(ctx context.Context, params *InitSecurityGroupRulesParams) (securityGroupID string, err error) {
	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     params.UserID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return "", err
	}
	auth := authRsp.Auth

	// ===============================STEP1 创建一个新的安全组===================================
	securityGroupID, err = c.CreateSecurityGroup(ctx, &CreateSecurityGroupParams{
		UserID: params.UserID,
		VpcID:  params.VpcID,
	})
	if err != nil {
		return "", err
	}

	// ========STEP2 把所有ip的对用户设置/默认的对外服务PORT的ingress权限打开（不传ip即为全部）========
	// 创建开启虚拟机端口的安全组规则，针对集群模式依然开启配置端口
	// 创建Proxy对外开放的端口
	for _, outerPort := range params.Ports {
		err = c.createSecurityRulesPortForAllIP(ctx, fmt.Sprint(outerPort), securityGroupID, auth, params.IsEnableIPV6)
		if err != nil {
			return "", err
		}
	}

	// ========================STEP3 把管控节点ip对22端口的ingress权限打开========================
	// 5000-5999 用来给预留服务使用，包括 dbsc-agent 使用的 5100 端口
	defaultPorts := []string{"22", "5000-5999", fmt.Sprint(x1model.DefaultXagentPort)}
	for _, ip := range c.conf.DefaultSgIpList {
		for _, defaultport := range defaultPorts {
			err = c.createSecurityRulesPortForOneIP(ctx, defaultport, securityGroupID, auth, ip)
			if err != nil {
				return "", err
			}
		}
	}
	// ipv6 不支持单独ip，目前ip参数保留但实际没用
	if params.IsEnableIPV6 {
		for _, portV6 := range defaultPorts {
			err = c.createSecurityRulesIpPortIpv6ForAllIp(ctx, portV6, securityGroupID, auth)
			if err != nil {
				return "", err
			}
		}
	}

	// ===================================STEP5 把ICMP开启==================================
	err = c.createSecurityRulesICMP(ctx, securityGroupID, auth, params.IsEnableIPV6)
	if err != nil {
		return "", err
	}

	// ===================================STEP6 添加 egress 规则=============================
	err = c.createSecurityRulesEgress(ctx, securityGroupID, auth)
	if err != nil {
		return "", err
	}

	// todo V5

	return securityGroupID, nil
}

// ReopenSecurityGroupRules 重启安全组规则
//
// 参数：
// ctx - 上下文
// params - ReopenSecurityGroupRulesParams结构体指针，包含以下参数
//  1. UserID 用户ID
//  2. ServicePorts 服务端口列表
//  3. WhitelistIPs 普通Ip白名单
//  4. SecurityGroupID 安全组ID
//  5. IsEnableIpv6 是否开启IPV6
//
// 返回值：
// error - 错误信息
func (c *component) ReopenSecurityGroupRules(ctx context.Context, params *ReopenSecurityGroupRulesParams) error {
	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     params.UserID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return err
	}
	auth := authRsp.Auth

	// merge 默认白名单ip
	logger.ComponentLogger.Trace(ctx, "before merge, param.whitelist: %s", base_utils.Format(params.WhitelistIPs))
	for _, defaultIp := range c.conf.DefaultSgIpList {
		inWhiteList, _ := base_utils.InArray(defaultIp, params.WhitelistIPs)
		if !inWhiteList {
			params.WhitelistIPs = append(params.WhitelistIPs, defaultIp)
		}
	}
	logger.ComponentLogger.Trace(ctx, "merge default ips, default ips: %s, after merge param.whitelist: %s",
		base_utils.Format(c.conf.DefaultSgIpList), base_utils.Format(params.WhitelistIPs))

	for _, port := range params.ServicePorts {
		isAll, _ := base_utils.InArray("*.*.*.*", params.WhitelistIPs)
		if isAll {
			err = c.createSecurityRulesPortForAllIP(ctx, fmt.Sprint(port), params.SecurityGroupID, auth, params.IsEnableIpv6)
			if err != nil {
				return err
			}
		} else {
			showRulesResp, err := c.showSecurityGroup(ctx, params.SecurityGroupID, auth)
			if err != nil {
				return err
			}
			existIps := make([]string, 0)
			for _, rule := range showRulesResp.Rules {
				if rule.PortRange == fmt.Sprint(port) {
					ruleIp := rule.SourceIP
					ruleIpSlice := strings.Split(ruleIp, "/32")
					if len(ruleIpSlice) == 0 {
						logger.ComponentLogger.Warning(ctx, "Ilegal Ip, ip:%s ", ruleIp)
						return cerrs.Errorf("Ilegal Ip, ip:%s ", ruleIp)
					}
					existIps = append(existIps, ruleIpSlice[0])
					needKeep, _ := base_utils.InArray(ruleIpSlice[0], params.WhitelistIPs)
					if !needKeep {
						logger.ComponentLogger.Trace(ctx, "Ip:%s , no need, deleting", ruleIpSlice[0])
						err = c.delSecurityRule(ctx, rule.SecurityGroupRuleID, auth)
						if err != nil {
							return err
						}
					}
				}
			}
			logger.ComponentLogger.Trace(ctx, "Elder Ips : %s", base_utils.Format(existIps))
			for _, newIp := range params.WhitelistIPs {
				hasExist, _ := base_utils.InArray(newIp, existIps)
				if !hasExist {
					logger.ComponentLogger.Trace(ctx, "Ip:%s , not exist, adding", newIp)
					err = c.createSecurityRulesPortForOneIP(ctx, fmt.Sprint(port), params.SecurityGroupID, auth, newIp) // 具体Ip不支持ipv6
					if err != nil {
						return err
					}
				}
			}

			// 放行全端口时，内部会判断 ipv6 并增加相关规则
			if params.IsEnableIpv6 {
				err = c.createSecurityRulesIpPortIpv6ForAllIp(ctx, fmt.Sprint(port), params.SecurityGroupID, auth) // 具体Ip不支持ipv6
				if err != nil {
					return err
				}
			}
		}

	}

	return nil
}

// DeleteSecurityRulesPort 删除安全组中的端口规则
// 将集群放入回收站中时使用
func (c *component) DeleteSecurityRulesPort(ctx context.Context, params *DeleteSecurityRulesPortParams) (err error) {
	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     params.UserID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return
	}
	auth := authRsp.Auth

	// delete
	return c.deleteSecurityRulesPort(ctx, fmt.Sprint(params.Port), params.SecurityGroupID, auth)
}

// deleteSecurityRulesPort 从安全组中删除指定端口的安全规则
// 参数：
// ctx：上下文对象
// port：需要删除的端口号
// securityGroupID：要从中删除端口规则的安全组ID
// auth：鉴权信息，类型为*common.Authentication指针
// 返回值：
// 如果未找到安全组或错误发生时返回nil，否则返回错误信息
func (c *component) deleteSecurityRulesPort(ctx context.Context, port string, securityGroupID string, auth *common.Authentication) (err error) {
	showRulesResp, err := c.showSecurityGroup(ctx, securityGroupID, auth)
	if cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Trace(ctx, "deleteSecurityRulesPort not found, securityGroupID: %s", securityGroupID)
		return nil
	}
	if err != nil {
		return err
	}

	for _, rule := range showRulesResp.Rules {
		if rule.PortRange == port {
			err = c.delSecurityRule(ctx, rule.SecurityGroupRuleID, auth)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// createSecurityRulesPortForAllIP 函数用于创建安全组规则，支持 IPv4 和 IPv6。
// 参数 ctx 为上下文对象。
// 参数 port 为端口号。
// 参数 securityGroupID 为安全组 ID。
// 参数 auth 为认证信息对象指针。
// 参数 isEnableIPV6 表示是否启用 IPv6。
// 返回值为错误信息
func (c *component) createSecurityRulesPortForAllIP(ctx context.Context, port string, securityGroupID string, auth *common.Authentication, isEnableIPV6 bool) error {
	// 1. create rule for ipv4
	req := neutron.CreateSecurityGroupRulesRequest{
		SecurityGroupID: securityGroupID,
		Rule: &neutron.SecurityGroupRuleModel{
			Protocol:        "tcp",
			Ethertype:       "IPv4",
			PortRange:       port,
			SecurityGroupID: securityGroupID,
			Direction:       "ingress",
			SourceIP:        "all",
		},
		Auth: auth,
	}
	_, err := c.neutronSdk.CreateSecurityGroupRules(ctx, &req)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Create Security Rule Fail,req:%+v, err:%s", req, err.Error())
		return err
	}

	// 2. create rule for ipv6
	if isEnableIPV6 {
		req.Rule.Ethertype = "IPv6"
		_, err = c.neutronSdk.CreateSecurityGroupRules(ctx, &req)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "Create Security Rule Fail, req:%+v, err:%s", req, err.Error())
			return err
		}
	}

	// 3. 删除非 all 的相关规则
	showRulesResp, err := c.showSecurityGroup(ctx, securityGroupID, auth)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get Security Rules Fail, req:%+v, err:%s", req, err.Error())
		return err
	}

	for _, rule := range showRulesResp.Rules {
		if rule.PortRange != port {
			continue
		}

		if rule.SourceIP == "all" {
			continue
		}

		err = c.delSecurityRule(ctx, rule.SecurityGroupRuleID, auth)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "Delete Security Rule Fail, req:%+v, err:%s", req, err.Error())
			return err
		}
	}

	logger.ComponentLogger.Trace(ctx, "Create Security Rule Suc, open the port %s for all ip, req:%+v", port, req)
	return nil
}

// createSecurityRulesPortForOneIP 函数用于在OpenStack中创建安全组规则
// 参数：
//   - ctx: 上下文，表示当前操作的上下文
//   - port: 需要创建的安全组规则端口号
//   - securityGroupID: 要创建的安全组ID
//   - auth: OpenStack身份认证信息
//   - ip: 需要创建安全组规则的源IP地址
//
// 返回值：
//   - error: 创建安全组规则时的错误信息
func (c *component) createSecurityRulesPortForOneIP(ctx context.Context, port string, securityGroupID string, auth *common.Authentication, ip string) error {
	req := neutron.CreateSecurityGroupRulesRequest{
		SecurityGroupID: securityGroupID,
		Rule: &neutron.SecurityGroupRuleModel{
			Protocol:        "tcp",
			Ethertype:       "IPv4",
			PortRange:       port,
			SecurityGroupID: securityGroupID,
			Direction:       "ingress",
			SourceIP:        ip,
		},
		Auth: auth,
	}
	_, err := c.neutronSdk.CreateSecurityGroupRules(ctx, &req)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Create Security Rule Fail,req:%+v, err:%s", req, err.Error())
		return err
	}

	logger.ComponentLogger.Trace(ctx, "Create Security Rule Suc,req:%+v", req)
	return nil
}

// ipv6 只支持全开全关
func (c *component) createSecurityRulesIpPortIpv6ForAllIp(ctx context.Context, port string, securityGroupID string, auth *common.Authentication) error {
	req := neutron.CreateSecurityGroupRulesRequest{
		SecurityGroupID: securityGroupID,
		Rule: &neutron.SecurityGroupRuleModel{
			Protocol:        "tcp",
			Ethertype:       "IPv6",
			PortRange:       port,
			SecurityGroupID: securityGroupID,
			Direction:       "ingress",
			SourceIP:        "all",
		},
		Auth: auth,
	}
	_, err := c.neutronSdk.CreateSecurityGroupRules(ctx, &req)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Create Security Rule Fail,req:%+v, err:%s", req, err.Error())
		return err
	}

	logger.ComponentLogger.Trace(ctx, "Create Security Rule Suc,req:%+v", req)
	return nil
}

// createSecurityRulesICMP 函数用于创建IPv4和IPv6的安全组规则。
// 参数：
//
//	ctx: 上下文对象，类型为context.Context。
//	securityGroupID: 安全组ID，字符串类型。
//	auth: 用户认证信息，类型为*common.Authentication。
//	isEnableIPV6: 是否启用IPv6，bool类型。
//
// 返回值：
//
//	如果操作成功则返回nil；否则返回错误信息。
func (c *component) createSecurityRulesICMP(ctx context.Context, securityGroupID string, auth *common.Authentication, isEnableIPV6 bool) error {
	// create rule for ipv4
	req := neutron.CreateSecurityGroupRulesRequest{
		SecurityGroupID: securityGroupID,
		Rule: &neutron.SecurityGroupRuleModel{
			Protocol:        "icmp",
			Ethertype:       "IPv4",
			SecurityGroupID: securityGroupID,
			Direction:       "ingress",
			SourceIP:        "all",
		},
		Auth: auth,
	}
	_, err := c.neutronSdk.CreateSecurityGroupRules(ctx, &req)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Create Security icmp Rule Fail, req:%+v, err:%s", req, err.Error())
		return err
	}

	logger.ComponentLogger.Trace(ctx, "Create Security icmp Rule Suc,req:%+v", req)

	// create rule for ipv6
	if isEnableIPV6 {
		req.Rule.Ethertype = "IPv6"
		_, err = c.neutronSdk.CreateSecurityGroupRules(ctx, &req)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "Create Security icmpV6 Rule Fail, req:%+v, err:%s", req, err.Error())
			return err
		}
		logger.ComponentLogger.Trace(ctx, "Create Security icmpV6 Rule Suc,req:%+v", req)
	}
	return nil
}

// egress 规则，安全组 openapi 需要显示的设置
func (c *component) createSecurityRulesEgress(ctx context.Context, securityGroupID string, auth *common.Authentication) error {
	// create rule for ipv4
	req := neutron.CreateSecurityGroupRulesRequest{
		SecurityGroupID: securityGroupID,
		Rule: &neutron.SecurityGroupRuleModel{
			Protocol:        "all",
			Ethertype:       "IPv4",
			SecurityGroupID: securityGroupID,
			Direction:       "egress",
			DestIP:          "all",
		},
		Auth: auth,
	}
	_, err := c.neutronSdk.CreateSecurityGroupRules(ctx, &req)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Create Security egress Rule Fail, req:%+v, err:%s", req, err.Error())
		return err
	}

	logger.ComponentLogger.Trace(ctx, "Create Security egress Rule Suc, req:%+v", req)

	// create rule for ipv6
	req.Rule.Ethertype = "IPv6"
	_, err = c.neutronSdk.CreateSecurityGroupRules(ctx, &req)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Create Security egressV6 Rule Fail, req:%+v, err:%s", req, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "Create Security egressV6 Rule Suc, req:%+v", req)
	return nil
}

// showSecurityGroup 函数用于展示一个安全组的信息
// 参数：
//
//	ctx: 上下文对象，用于记录日志和追踪请求流程
//	securityGroupID: 需要获取的安全组ID
//	auth: 认证信息
//
// 返回值：
//
//	*neutron.ShowSecurityGroupResponse: 返回查询到的安全组信息
//	error: 如果出现错误则返回对应的错误信息
func (c *component) showSecurityGroup(ctx context.Context, securityGroupID string, auth *common.Authentication) (*neutron.ShowSecurityGroupResponse, error) {
	showRulesReq := neutron.ShowSecurityGroupRequest{
		SecurityGroupID: securityGroupID,
		Auth:            auth,
	}

	showRulesResp, err := c.neutronSdk.ShowSecurityGroup(ctx, &showRulesReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get Security Rules Fail, req:%+v , err:%s", showRulesReq, err.Error())
		return nil, err
	}
	logger.ComponentLogger.Trace(ctx, "Get Security Rules Suc, req:%+v , resp:%+v", showRulesReq, *showRulesResp)
	return showRulesResp, nil
}

// 删除安全组规则
//
// 参数：
//
//	ctx 上下文对象。
//	ruleID 安全组规则的 ID。
//	auth 认证信息对象指针。
//
// 返回值：
//
//	如果成功，返回 nil；否则返回错误信息。
func (c *component) delSecurityRule(ctx context.Context, ruleID string, auth *common.Authentication) error {
	delReq := neutron.DeleteSecurityGroupRulesRequest{
		SecurityGroupRulesID: ruleID,
		Auth:                 auth,
	}
	delResp, err := c.neutronSdk.DeleteSecurityGroupRules(ctx, &delReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Delete Security Rule Fail, req:%+v , err:%s", delReq, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "Delete Security Rule Suc, req:%+v , resp:%+v", delReq, *delResp)
	return nil
}

// x1-task
func (c *component) GetDefaultIps(ctx context.Context) []string {
	return c.conf.DefaultSgIpList
}

// x1-api
func (c *component) GetIpWhitelistInSg(ctx context.Context, userID string, sgID string) ([]string, error) {
	// auth
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     userID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return nil, err
	}
	auth := authRsp.Auth

	showRulesResp, err := c.showSecurityGroup(ctx, sgID, auth)
	if err != nil {
		return nil, err
	}
	iplist := make([]string, 0)
	for _, rule := range showRulesResp.Rules {
		splitIps := strings.Split(rule.SourceIP, "/32")
		if len(splitIps) == 0 {
			logger.ComponentLogger.Warning(ctx, "ilegal ip, userid:%s , sgid:%s, ruleid:%s", userID, sgID, rule.SecurityGroupRuleID)
			continue
		}
		iplist = append(iplist, splitIps[0])
	}
	logger.ComponentLogger.Trace(ctx, "get iplist in sg suc, sgid:%s, iplist:%s", sgID, base_utils.Format(iplist))
	return iplist, nil
}

// x1-api
func (c *component) DefaultWhiteList() []string {
	return c.conf.DefaultSgIpList
}
