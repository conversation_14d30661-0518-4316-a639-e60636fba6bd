/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
操作虚机安全组相关方法
*/

package security_group

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const (
	TestIamUserID              = "ea2c4a2286ca4540afcb7f7d4ba2d199"
	TestIamUserIDInvalid       = "ea2c4a2286ca4540afcb7f7d4ba2d199x"
	TestVpcID                  = "58d53a2e-27fe-4ead-9041-c72ec67ec1c1"
	TestVpcIDInvalid           = "58d53a2e-27fe-4ead-9041-c72ec67ec1c1x"
	TestSecurityGroupID        = "g-jjq9ev53rpp5"
	TestSecurityGroupIDInvalid = "g-jjq9ev53rpp5x"
)

// TestCreateSecurityGroupOK 函数是一个测试函数，用于测试创建安全组操作的正确性
func TestCreateSecurityGroupOK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()

	// create group
	createReq := CreateSecurityGroupParams{
		UserID: TestIamUserID,
		VpcID:  TestVpcID,
	}
	securityGroupID, err := op.CreateSecurityGroup(ctx, &createReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(base_utils.Format(securityGroupID))
	fmt.Println("create group success")

	// create rule
	createRuleReq := CreateSecurityRuleParams{
		UserID:          TestIamUserID,
		SecurityGroupID: securityGroupID,
		port:            "22",
	}
	err = op.CreateSecurityRule(ctx, &createRuleReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("create rule1 success")

	createRuleReq2 := CreateSecurityRuleParams{
		UserID:          TestIamUserID,
		SecurityGroupID: securityGroupID,
		port:            "22",
		ip:              "127.0.0.1",
	}
	err = op.CreateSecurityRule(ctx, &createRuleReq2)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("create rule2 success")

	// delete rule
	deleteRuleReq := DeleteSecurityRulesPortParams{
		UserID:          TestIamUserID,
		SecurityGroupID: securityGroupID,
		Port:            22,
	}
	err = op.DeleteSecurityRulesPort(ctx, &deleteRuleReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("delete rule success")

	// delete delete
	deleteReq := DeleteSecurityGroupParams{
		UserID:          TestIamUserID,
		SecurityGroupID: securityGroupID,
	}
	err = op.DeleteSecurityGroup(ctx, &deleteReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("delete group success")
}

// TestCreateSecurityGroupERR
func TestCreateSecurityGroupERR(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()

	// auth error
	createReq := CreateSecurityGroupParams{
		UserID: TestIamUserIDInvalid,
		VpcID:  TestVpcID,
	}

	_, err := op.CreateSecurityGroup(ctx, &createReq)
	if err.Error() != "credential fail, code: NotFound, message: could not found: user" {
		fmt.Println(err)
		return
	}
	fmt.Println("success")

	// {"requestId":"xx","code":"BadRequest","message":"Bad request parameters or illegal request."}
	createReq = CreateSecurityGroupParams{
		UserID: TestIamUserID,
		VpcID:  TestVpcIDInvalid,
	}

	_, err = op.CreateSecurityGroup(ctx, &createReq)
	if strings.Contains(err.Error(), "Bad request parameters or illegal request.") {
		fmt.Println("success")
	}
}

// TestCreateSecurityGroupRuleERR
func TestCreateSecurityGroupRuleERR(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()

	// auth error
	createRuleReq := CreateSecurityRuleParams{
		UserID:          TestIamUserIDInvalid,
		SecurityGroupID: TestSecurityGroupID,
		port:            "22",
	}
	err := op.CreateSecurityRule(ctx, &createRuleReq)
	if err.Error() != "credential fail, code: NotFound, message: could not found: user" {
		fmt.Println(err)
		return
	}
	fmt.Println("success")
}

// TestDeleteSecurityRulesPortERR
func TestDeleteSecurityRulesPortERR(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()

	// auth error
	deleteRuleReq := DeleteSecurityRulesPortParams{
		UserID:          TestIamUserIDInvalid,
		SecurityGroupID: TestSecurityGroupID,
		Port:            22,
	}
	err := op.DeleteSecurityRulesPort(ctx, &deleteRuleReq)
	if err.Error() != "credential fail, code: NotFound, message: could not found: user" {
		fmt.Println(err)
		return
	}
	fmt.Println("success")

}

// TestDeleteSecurityGroupERR
func TestDeleteSecurityGroupERR(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()

	// auth error
	deleteReq := DeleteSecurityGroupParams{
		UserID:          TestIamUserIDInvalid,
		SecurityGroupID: TestSecurityGroupIDInvalid,
	}

	err := op.DeleteSecurityGroup(ctx, &deleteReq)
	if err.Error() != "credential fail, code: NotFound, message: could not found: user" {
		fmt.Println(err)
		return
	}
	fmt.Println("success")

	// if securitygroupid not exists, delete return success
	deleteReq = DeleteSecurityGroupParams{
		UserID:          TestIamUserID,
		SecurityGroupID: TestSecurityGroupIDInvalid,
	}

	err = op.DeleteSecurityGroup(ctx, &deleteReq)
	if err == nil {
		fmt.Println("success")
	}
}

// 创建集群时初始化的安全组 ipv4
func TestInitSecurityGroupRulesIPv4OK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()

	// create
	initReq := InitSecurityGroupRulesParams{
		UserID:       TestIamUserID,
		ServicePorts: nil,
		IsEnableIPV6: false,
		VpcID:        TestVpcID,
		Ports:        []int32{6379},
	}
	securityGroupID, err := op.InitSecurityGroupRules(ctx, &initReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(base_utils.Format(securityGroupID))
	fmt.Println("success")

	// delete
	deleteReq := DeleteSecurityGroupParams{
		UserID:          TestIamUserID,
		SecurityGroupID: securityGroupID,
	}
	err = op.DeleteSecurityGroup(ctx, &deleteReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("success")
}

// 创建集群时初始化的安全组 ipv6
func TestInitSecurityGroupRulesIPv6OK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()

	// create
	initReq := InitSecurityGroupRulesParams{
		UserID:       TestIamUserID,
		ServicePorts: nil,
		IsEnableIPV6: true,
		VpcID:        TestVpcID,
		Ports:        []int32{6379},
	}
	securityGroupID, err := op.InitSecurityGroupRules(ctx, &initReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(base_utils.Format(securityGroupID))
	fmt.Println("success")

	// delete
	deleteReq := DeleteSecurityGroupParams{
		UserID:          TestIamUserID,
		SecurityGroupID: securityGroupID,
	}
	err = op.DeleteSecurityGroup(ctx, &deleteReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("success")
}

// 创建集群时初始化的安全组
func TestInitSecurityGroupRulesERR(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()

	// auth error
	initReq := InitSecurityGroupRulesParams{
		UserID:       TestIamUserIDInvalid,
		ServicePorts: nil,
		IsEnableIPV6: false,
		VpcID:        TestVpcID,
		Ports:        []int32{6379},
	}
	_, err := op.InitSecurityGroupRules(ctx, &initReq)
	if err.Error() != "credential fail, code: NotFound, message: could not found: user" {
		fmt.Println(err)
		return
	}
	fmt.Println("success")
}

// TestReopenSecurityGroupRulesOK 测试函数
func TestReopenSecurityGroupRules1OK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()

	// create
	reopenReq := ReopenSecurityGroupRulesParams{
		SecurityGroupID: TestSecurityGroupID,
		ServicePorts:    []int32{6379},
		WhitelistIPs:    []string{"***********"},
		UserID:          TestIamUserID,
		IsEnableIpv6:    true,
	}
	err := op.ReopenSecurityGroupRules(ctx, &reopenReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("success")
}

// TestReopenSecurityGroupRulesOK 测试函数
func TestReopenSecurityGroupRules2OK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()

	// create
	reopenReq := ReopenSecurityGroupRulesParams{
		SecurityGroupID: TestSecurityGroupID,
		ServicePorts:    []int32{6379},
		WhitelistIPs:    []string{"***********", "*.*.*.*"},
		UserID:          TestIamUserID,
		IsEnableIpv6:    true,
	}
	err := op.ReopenSecurityGroupRules(ctx, &reopenReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("success")
}

// TestReopenSecurityGroupRulesERR
func TestReopenSecurityGroupRulesERR(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()

	// create
	reopenReq := ReopenSecurityGroupRulesParams{
		SecurityGroupID: TestSecurityGroupID,
		ServicePorts:    []int32{6379},
		WhitelistIPs:    []string{"***********"},
		UserID:          TestIamUserIDInvalid,
		IsEnableIpv6:    true,
	}
	err := op.ReopenSecurityGroupRules(ctx, &reopenReq)
	if err.Error() != "credential fail, code: NotFound, message: could not found: user" {
		fmt.Println(err)
		return
	}
	fmt.Println("success")
}

// test eq
func testEq(a, b []string) bool {
	// If one is nil, the other must also be nil.
	if (a == nil) != (b == nil) {
		return false
	}

	if len(a) != len(b) {
		return false
	}

	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}

	return true
}

// TestGetDefaultIps
// conf_ut/component/security-group.toml
func TestDefaultIpsOK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()
	IpList := op.GetDefaultIps(ctx)
	if testEq(IpList, []string{"*************"}) {
		fmt.Println("success")
		return
	}
	fmt.Println("error")
}

// TestGetIpWhitelistInSg
func TestGetIpWhitelistInSgOK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()
	iplist, err := op.GetIpWhitelistInSg(ctx, TestIamUserID, TestSecurityGroupID)
	if err != nil {
		fmt.Println(err)
		return
	}
	// ["***********","*************","all"]
	fmt.Println(iplist)
	fmt.Println("success")
}

// TestGetIpWhitelistInSgERR
func TestGetIpWhitelistInSgERR(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()

	// auth err
	_, err := op.GetIpWhitelistInSg(ctx, TestIamUserIDInvalid, TestSecurityGroupID)
	if err.Error() != "credential fail, code: NotFound, message: could not found: user" {
		fmt.Println(err)
		return
	}
	fmt.Println("success")
}

// TestDefaultWhiteList
func TestDefaultWhiteList(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	op := Instance()
	IpList := op.DefaultWhiteList()
	if testEq(IpList, []string{"*************"}) {
		fmt.Println("success")
		return
	}
	fmt.Println("error")
}
