package security_group

import (
	"context"
	"icode.baidu.com/baidu/gdp/env"
	"testing"
)

func TestGetPrivateSg(t *testing.T) {
	env.Default = env.New(env.Option{
		IDC: "dbstacktest",
	})
	sc := GetPrivateSg()
	_, err := sc.CreateSecurityGroup(context.Background(), nil)
	if err != nil {
		t.<PERSON>("CreateSecurityGroup failed: %v", err)
	}
	err = sc.DeleteSecurityGroup(context.Background(), nil)
	if err != nil {
		t.<PERSON>rrorf("DeleteSecurityGroup failed: %v", err)
	}
	err = sc.CreateSecurityRule(context.Background(), nil)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("CreateSecurityRule failed: %v", err)
	}
	_, err = sc.InitSecurityGroupRules(context.Background(), nil)
	if err != nil {
		t.<PERSON>("InitSecurityGroupRules failed: %v", err)
	}
	err = sc.ReopenSecurityGroupRules(context.Background(), nil)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("ReopenSecurityGroupRules failed: %v", err)
	}
	err = sc.DeleteSecurityRulesPort(context.Background(), nil)
	if err != nil {
		t.Errorf("DeleteSecurityRulesPort failed: %v", err)
	}
	_ = sc.GetDefaultIps(context.Background())
	_, err = sc.GetIpWhitelistInSg(context.Background(), "", "")
	if err != nil {
		t.Errorf("GetIpWhitelistInSg failed: %v", err)
	}
	_ = sc.DefaultWhiteList()
}
