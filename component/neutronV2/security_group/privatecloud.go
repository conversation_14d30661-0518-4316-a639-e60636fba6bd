/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2024/02/18
 * File: private_cloud.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package security_group TODO package function desc
package security_group

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/env"

	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
)

type fakeSg struct {
}

func (l *fakeSg) CreateSecurityGroup(ctx context.Context, params *CreateSecurityGroupParams) (securityGroupID string, err error) {
	return "licloud-fake-sgid", nil
}

func (l *fakeSg) DeleteSecurityGroup(ctx context.Context, params *DeleteSecurityGroupParams) (err error) {
	return nil
}

func (l *fakeSg) CreateSecurityRule(ctx context.Context, params *CreateSecurityRuleParams) (err error) {
	return nil
}

func (l *fakeSg) InitSecurityGroupRules(ctx context.Context, params *InitSecurityGroupRulesParams) (securityGroupID string, err error) {
	return "licloud-fake-sgid", nil
}

func (l *fakeSg) ReopenSecurityGroupRules(ctx context.Context, params *ReopenSecurityGroupRulesParams) error {
	return nil
}

func (l *fakeSg) DeleteSecurityRulesPort(ctx context.Context, params *DeleteSecurityRulesPortParams) (err error) {
	return nil
}

func (l *fakeSg) GetDefaultIps(ctx context.Context) []string {
	return []string{}
}

func (l *fakeSg) GetIpWhitelistInSg(ctx context.Context, userID string, sgID string) ([]string, error) {
	return []string{}, nil
}

func (l *fakeSg) DefaultWhiteList() []string {
	return []string{}
}

func GetPrivateSg() SgOP {
	switch privatecloud.GetPrivateEnvType() {
	case privatecloud.EnvLiTest, privatecloud.EnvLiProd, privatecloud.DBStackPrefix:
		return &fakeSg{}
	default:
		panic(fmt.Sprintf("not support this idc:%s", env.IDC()))
	}
}
