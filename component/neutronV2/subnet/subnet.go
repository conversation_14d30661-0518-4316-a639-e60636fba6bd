package subnetOpenapi

import (
	"context"
	"strings"
	"sync"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc/neutronV2"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type SubnetResource interface {
	GetSubnetDetail(ctx context.Context, params *GetSubnetDetailReq) (*bcc.GetSubnetDetailResponse, error)
	GetAvailableIPCount(ctx context.Context, params *GetAvailableIPCountParams) (ret map[string]int64, err error)
	CheckSubnetsSupportIPV6(ctx context.Context, params *CheckSubnetsSupportIPV6Params) (ret bool, err error)
}

type config struct {
	RetainIpCount       int
	SupportedIPTypes    []string
	SubnetReservedIpNum int64
	NeedDeleteIpNum     int64
}

type GetSubnetDetailReq struct {
	UserID   string
	SubnetID string
}

type GetAvailableIPCountParams struct {
	UserID string
	Subnet []string
}

type CheckSubnetsSupportIPV6Params struct {
	UserID string
	Subnet []string
}

type subnetResourceOp struct {
	stsSdk    sts.StsService
	subnetSdk bcc.NeutronV2Service
	conf      *config
}

var subnetResourceOpObj SubnetResource

var once sync.Once

func SubnetResourceOp() SubnetResource {
	once.Do(func() {
		if privatecloud.IsPrivateENV() {
			subnetResourceOpObj = GetPrivateSubnetOP()
		} else {
			subnetIns := &subnetResourceOp{
				stsSdk:    sts.NewDefaultStsSdk(),
				subnetSdk: neutronV2.NewDefaultNeutronV2Sdk(),
				conf:      &config{},
			}
			if err := compo_utils.LoadConf("subnet", subnetIns.conf); err != nil {
				panic(err.Error())
			}
			subnetResourceOpObj = subnetIns
		}
	})
	return subnetResourceOpObj
}

// 兼容 v1 neutron/subnet
func Instance() SubnetResource {
	return SubnetResourceOp()
}

func (sr *subnetResourceOp) GetSubnetDetail(ctx context.Context, params *GetSubnetDetailReq) (*bcc.GetSubnetDetailResponse, error) {
	auth, err := compo_utils.GetOpenapiAuthWithResourceAccount(ctx, params.UserID)
	if err != nil {
		return nil, err
	}

	req := &bcc.GetSubnetDetailRequest{
		SubnetID: params.SubnetID,
		Auth:     auth,
	}
	resp, err := sr.subnetSdk.GetSubnetDetail(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// GetAvailableIPCount 获取子网可用的IP数量
func (sr *subnetResourceOp) GetAvailableIPCount(ctx context.Context, params *GetAvailableIPCountParams) (ret map[string]int64, err error) {
	logger.ComponentLogger.Trace(ctx, "start to check available ip num", logit.String("params", base_utils.Format(params)))
	ret = make(map[string]int64, 0)
	auth, err := compo_utils.GetOpenapiAuthWithResourceAccount(ctx, params.UserID)
	if err != nil {
		return nil, err
	}
	for _, subnetID := range params.Subnet {
		subnetShortID, err := getSubnetShortID(ctx, params.UserID, subnetID)
		if err != nil {
			return nil, err
		}
		req := &bcc.GetSubnetDetailRequest{
			SubnetID: subnetShortID,
			Auth:     auth,
		}
		resp, err := sr.subnetSdk.GetSubnetDetail(ctx, req)
		if err != nil {
			return nil, err
		}
		ret[subnetID] = resp.Subnet.AvailableIp
		if ret[subnetID] < 0 {
			ret[subnetID] = 0
		}
	}
	return
}

// CheckSubnetsSupportIPV6 检查子网是否支持ipv6
//  1. 获取子网type
//     a) 如果子网均支持ipv6，且配置可以创建ipv6实例，返回true
//     b) 如果子网均不支持ipv6，返回false
//     c) 其他情况报错退出
func (sr *subnetResourceOp) CheckSubnetsSupportIPV6(ctx context.Context, params *CheckSubnetsSupportIPV6Params) (ret bool, err error) {
	ret = false
	if params == nil {
		return false, errors.Errorf("params is nilptr")
	}
	if len(params.Subnet) == 0 {
		return false, errors.Errorf("need subnet")
	}
	if len(params.UserID) == 0 {
		return false, errors.Errorf("need iamUserId")
	}

	auth, err := compo_utils.GetOpenapiAuthWithResourceAccount(ctx, params.UserID)
	if err != nil {
		return false, err
	}
	enableIpv6Num := 0
	for _, subnetID := range params.Subnet {
		subnetShortID, err := getSubnetShortID(ctx, params.UserID, subnetID)
		if err != nil {
			return false, err
		}
		req := &bcc.GetSubnetDetailRequest{
			SubnetID: subnetShortID,
			Auth:     auth,
		}
		resp, err := sr.subnetSdk.GetSubnetDetail(ctx, req)
		if err != nil {
			return false, err
		}
		if resp.Subnet.Ipv6Cidr != "" {
			enableIpv6Num++
		}
	}

	if enableIpv6Num > 0 && enableIpv6Num != len(params.Subnet) {
		logger.ComponentLogger.Trace(ctx, "Not All Enable Ipv6")
		return false, nil
	}

	if len(sr.conf.SupportedIPTypes) == 0 {
		logger.ComponentLogger.Warning(ctx, "Conf Has No Ip Protocol")
		return false, errors.Errorf("Conf Has No Ip Protocol")
	}

	if enableIpv6Num != 0 {
		// 支持ipv6
		confSupportV6, err := base_utils.InArray("ipv6", sr.conf.SupportedIPTypes)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "Check If Support Ipv6 In Conf Fail")
			return false, errors.Errorf("Check If Support Ipv6 In Conf Fail")
		}
		logger.ComponentLogger.Trace(ctx, "Conf's Support Protocal:%+v", confSupportV6)
		if confSupportV6 {
			ret = true
		}
	}

	return
}

// 获取 subnet 短ID
func getSubnetShortID(ctx context.Context, userID string, subnetID string) (string, error) {
	if strings.HasPrefix(subnetID, "sbn-") {
		return subnetID, nil
	}

	var subnetShortID string
	subnetExchange := &bccresource.ExchangeIDParams{
		ObjectType:  bcc.ExchangeIDObjectTypeSubnet,
		InstanceIds: []string{subnetID},
		UserID:      userID,
	}
	subnetMapping, err := bccresource.BccResourceOp().ExchangeProductLongIDToShort(ctx, subnetExchange)
	if err != nil {
		return "", err
	}
	for idx := range subnetMapping {
		if subnetMapping[idx].ID == subnetID {
			subnetShortID = subnetMapping[idx].UUID
		}
	}
	return subnetShortID, nil
}
