package subnetOpenapi

import (
	"context"
	"icode.baidu.com/baidu/gdp/env"
	"testing"
)

func TestGetPrivateSubnetOP(t *testing.T) {
	env.Default = env.New(env.Option{
		IDC: "licloudontest",
	})
	sc := GetPrivateSubnetOP()
	_, err := sc.GetSubnetDetail(context.Background(), &GetSubnetDetailReq{})
	if err != nil {
		t.<PERSON>rf("GetSubnetDetail failed: %v", err)
	}
	_, err = sc.GetAvailableIPCount(context.Background(), &GetAvailableIPCountParams{})
	if err != nil {
		t.Errorf("GetAvailableIPCount failed: %v", err)
	}
	_, err = sc.CheckSubnetsSupportIPV6(context.Background(), &CheckSubnetsSupportIPV6Params{})
	if err != nil {
		t.<PERSON><PERSON>rf("CheckSubnetsSupportIPV6 failed: %v", err)
	}
	env.Default = env.New(env.Option{
		IDC: "dbstacktest",
	})
	sc = GetPrivateSubnetOP()
	_, err = sc.GetSubnetDetail(context.Background(), &GetSubnetDetailReq{})
	if err != nil {
		t.Errorf("GetSubnetDetail failed: %v", err)
	}
	_, err = sc.GetAvailableIPCount(context.Background(), &GetAvailableIPCountParams{})
	if err != nil {
		t.Errorf("GetAvailableIPCount failed: %v", err)
	}
	_, err = sc.CheckSubnetsSupportIPV6(context.Background(), &CheckSubnetsSupportIPV6Params{})
	if err != nil {
		t.Errorf("CheckSubnetsSupportIPV6 failed: %v", err)
	}
}
