package subnetOpenapi

import (
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

func init() {
	unittest.UnitTestInit(3)
}

//func TestGetSubnetDetail(t *testing.T) {
//	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
//	defer cancel()
//
//	subnetOp := &subnetResourceOp{
//		stsSdk:    sts.NewDefaultStsSdk(),
//		subnetSdk: neutronV2.NewDefaultNeutronV2Sdk(),
//	}
//
//	response, err := subnetOp.GetSubnetDetail(ctx, &GetSubnetDetailReq{
//		UserID:   "409ce0b1c58f4c9ea95f56ea7579ef18",
//		SubnetID: "213ec9f9-cdbf-4bac-8da0-66282c084707",
//	})
//	if err != nil {
//		t.Fatalf("expect no error, actual err %s", err.Error())
//	}
//	fmt.Println("GetSubnetDetail resp:", base_utils.Format(response))
//
//}