/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2024/02/18
 * File: private_cloud.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package subnetOpenapi TODO package function desc
package subnetOpenapi

import (
	"context"
	"fmt"
	"math"

	"icode.baidu.com/baidu/gdp/env"

	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
)

type fakeSubnet struct {
}

func (l *fakeSubnet) GetSubnetDetail(ctx context.Context, params *GetSubnetDetailReq) (*bcc.GetSubnetDetailResponse, error) {
	return &bcc.GetSubnetDetailResponse{}, nil
}

func (l *fakeSubnet) GetAvailableIPCount(ctx context.Context, params *GetAvailableIPCountParams) (ret map[string]int64, err error) {
	// logger.DefaultLogger.Trace(ctx, "li-cloud ipcount always MaxInt64")
	ret = make(map[string]int64)
	for _, subnet := range params.Subnet {
		ret[subnet] = math.MaxInt64
	}
	return ret, nil
}

func (l *fakeSubnet) CheckSubnetsSupportIPV6(ctx context.Context, params *CheckSubnetsSupportIPV6Params) (ret bool, err error) {
	// logger.DefaultLogger.Trace(ctx, "li-cloud ipv6 always false")
	return false, nil
}

func GetPrivateSubnetOP() SubnetResource {
	switch privatecloud.GetPrivateEnvType() {
	case privatecloud.EnvLiTest, privatecloud.EnvLiProd, privatecloud.DBStackPrefix:
		return &fakeSubnet{}
	default:
		panic(fmt.Sprintf("not support this idc:%s", env.IDC()))
	}
}
