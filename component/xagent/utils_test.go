/**
* @Copyright 2023 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2023/6/2 12:19
**/

package xagent

import (
	"context"
	"reflect"
	"testing"

	. "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
)

func TestLivenessProbe(t *testing.T) {
	Convey("LivenessProbe", t, func() {
		var c *client
		Convey("call xagent failed", func() {
			type args struct {
				xagentAddr *Addr
				target     *x1model.Node
			}
			tests := []struct {
				name    string
				args    args
				want    bool
				wantErr bool
			}{
				{
					name: "test1",
					args: args{
						xagentAddr: &Addr{
							Host: "127.0.0.1",
						},
						target: &x1model.Node{
							Id:     1,
							Engine: x1model.EngineRedis,
						},
					},
					want:    true,
					wantErr: false,
				},
				{
					name: "test2",
					args: args{
						xagentAddr: &Addr{
							Host: "127.0.0.1",
						},
						target: &x1model.Node{
							Id:     1,
							Engine: x1model.EnginePegaDB,
						},
					},
					want:    true,
					wantErr: false,
				},
			}
			for _, tt := range tests {
				t.Run(tt.name, func(t *testing.T) {
					_, _ = LivenessProbe(context.Background(), tt.args.xagentAddr, tt.args.target, "")
				})
			}
		})

		Convey("call xagent succeed, PONG", func() {
			patches := ApplyMethod(reflect.TypeOf(c), "Do", func(_ *client, ctx context.Context, req *Request) (rsp *Response, err error) {
				return &Response{Result: "PONG"}, nil
			})
			defer patches.Reset()
			type args struct {
				xagentAddr *Addr
				target     *x1model.Node
			}
			tests := []struct {
				name    string
				args    args
				want    bool
				wantErr bool
			}{
				{
					name: "test1",
					args: args{
						xagentAddr: &Addr{
							Host: "127.0.0.1",
						},
						target: &x1model.Node{
							Id:     1,
							Engine: x1model.EngineRedis,
						},
					},
					want:    true,
					wantErr: false,
				},
				{
					name: "test2",
					args: args{
						xagentAddr: &Addr{
							Host: "127.0.0.1",
						},
						target: &x1model.Node{
							Id:     1,
							Engine: x1model.EnginePegaDB,
						},
					},
					want:    true,
					wantErr: false,
				},
			}
			for _, tt := range tests {
				t.Run(tt.name, func(t *testing.T) {
					_, _ = LivenessProbe(context.Background(), tt.args.xagentAddr, tt.args.target, "")
				})
			}
		})

		Convey("call xagent succeed, Others", func() {
			outputs := []OutputCell{
				{Values: Params{&Response{Result: "Foo"}, nil}},
				{Values: Params{&Response{Result: "Foo"}, nil}},
			}
			patches := ApplyMethodSeq(reflect.TypeOf(c), "Do", outputs)
			defer patches.Reset()
			type args struct {
				xagentAddr *Addr
				target     *x1model.Node
			}
			tests := []struct {
				name    string
				args    args
				want    bool
				wantErr bool
			}{
				{
					name: "test1",
					args: args{
						xagentAddr: &Addr{
							Host: "127.0.0.1",
						},
						target: &x1model.Node{
							Id:     1,
							Engine: x1model.EngineRedis,
						},
					},
					want:    false,
					wantErr: false,
				},
				{
					name: "test2",
					args: args{
						xagentAddr: &Addr{
							Host: "127.0.0.1",
						},
						target: &x1model.Node{
							Id:     1,
							Engine: x1model.EnginePegaDB,
						},
					},
					want:    false,
					wantErr: false,
				},
			}
			for _, tt := range tests {
				t.Run(tt.name, func(t *testing.T) {
					_, _ = LivenessProbe(context.Background(), tt.args.xagentAddr, tt.args.target, "")
				})
			}
		})
	})
}

func TestIsRedisAlive(t *testing.T) {
	tests := []struct {
		name  string
		input string
		want  bool
	}{
		{
			name:  "redis alive",
			input: "PONG",
			want:  true,
		},
		{
			name:  "redis alive 2",
			input: "NOAUTH",
			want:  true,
		},
		{
			name:  "redis alive 3",
			input: "ERR max number of clients reached",
			want:  true,
		},
		{
			name:  "redis alive 4",
			input: "Redis is loading the dataset in memory",
			want:  true,
		},
		{
			name:  "redis alive 5",
			input: "foo",
			want:  false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := IsRedisAlive(tt.input)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestIsPegaAlive(t *testing.T) {
	tests := []struct {
		name  string
		input string
		want  bool
	}{
		{
			name:  "restoring the db from backup",
			input: "ERR restoring the db from backup",
			want:  true,
		},
		{
			name:  "wrong number of arguments",
			input: "ERR wrong number of arguments",
			want:  true,
		},
		{
			name:  "redis alive",
			input: "PONG",
			want:  true,
		},
		{
			name:  "redis alive 2",
			input: "NOAUTH",
			want:  true,
		},
		{
			name:  "redis alive 3",
			input: "ERR max number of clients reached",
			want:  true,
		},
		{
			name:  "redis alive 4",
			input: "Redis is loading the dataset in memory",
			want:  true,
		},
		{
			name:  "redis alive 5",
			input: "foo",
			want:  false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := IsPegaAlive(tt.input)
			assert.Equal(t, tt.want, got)
		})
	}
}
