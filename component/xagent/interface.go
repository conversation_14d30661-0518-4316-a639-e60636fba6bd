/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-01-21
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据xagent.proto生成的interface文件
 */

// Package xagent
package xagent

type Addr struct {
	Host string `json:"host"`
	Port int32  `json:"port"`
}

type Request struct {
	Addr    *Addr       `json:"-"`
	Action  string      `json:"action"`
	Product string      `json:"Product"`
	Params  interface{} `json:"params"`
}

type Response struct {
	Result interface{} `json:"result"`
}

type AsyncRequest struct {
	Addr       *Addr       `json:"-"`
	Action     string      `json:"action"`
	Product    string      `json:"Product"`
	Params     interface{} `json:"params"`
	TimeoutSec int32       `json:"-"`
}

type AsyncResponse struct {
	TaskId     int64  `json:"task_id"`
	TaskStatus string `json:"task_status"`
	TaskResult string `json:"task_result"`
}

// Meta common meta info for redis commands
type Meta struct {
	Engine        string `json:"engine"`
	EngineVersion string `json:"engine_version"`
	Basedir       string `json:"basedir"`
	Port          int32  `json:"port"`
	AccountName   string `json:"account_name"`
	// Password encrypted
	Password string `json:"password"`
}
