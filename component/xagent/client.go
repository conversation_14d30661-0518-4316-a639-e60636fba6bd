/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-01-10
 * File: xagent.go
 */

/*
 * DESCRIPTION
 *   调用xagent执行 cmd="scs", data={"action":action_name,"params":params}
 *   支持同步和异步，cmd script 实现地址：
 *   https://console.cloud.baidu-int.com/devops/icode/repos/baidu/bdrp/xagent/tree/scs_deploy_server:xagent/script/scs
 */

// Package xagent
package xagent

import (
	"context"
	"sync"
	"sync/atomic"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/xagent"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type Client interface {
	Do(ctx context.Context, req *Request) (rsp *Response, err error)
	DoAsync(ctx context.Context, req *AsyncRequest) *TaskContext
}

type TaskContext struct {
	procCtx    context.Context
	finished   int32
	doneChan   chan struct{}
	cancel     context.CancelFunc
	startTime  time.Time
	timeoutSec int32
	costMs     int32
	taskId     int64
	req        *AsyncRequest
	rsp        *AsyncResponse
	err        error
}

func newTaskContext(ctx context.Context, req *AsyncRequest) *TaskContext {
	procCtx, cancel := context.WithCancel(ctx)
	timeoutSec := req.TimeoutSec
	if timeoutSec <= 0 {
		timeoutSec = asyncTaskDefaultTimeoutSec
	}
	return &TaskContext{
		procCtx:    procCtx,
		finished:   0,
		doneChan:   make(chan struct{}),
		cancel:     cancel,
		startTime:  time.Now(),
		timeoutSec: timeoutSec,
		req:        req,
	}
}

type client struct {
	sdk      xagent.XAgentService
	initOnce sync.Once
}

var defaultClient = &client{}

// Instance 返回defaultClient
func Instance() Client {
	defaultClient.initOnce.Do(func() {
		defaultClient.sdk = xagent.NewDefaultXAgentSdk()
	})
	return defaultClient
}

// Do 执行命令(同步)
func (c *client) Do(ctx context.Context, req *Request) (rsp *Response, err error) {
	cmdName := uniCommandName
	if req.Product != "" {
		cmdName = req.Product
	}
	sdkReq := &xagent.ExecCmdRequest{
		XagentHost: req.Addr.Host,
		XagentPort: req.Addr.Port,
		CmdName:    cmdName,
		Data:       req,
	}

	retryTimes := 0
	for {
		sdkRsp, err := c.sdk.ExecCmd(ctx, sdkReq)
		if err == nil {
			rsp = &Response{Result: sdkRsp.Data}
			return rsp, err
		}

		logger.ComponentLogger.Warning(ctx, "exec command fail, action[%s], retryTimes[%d], err[%s]",
			req.Action, retryTimes, err.Error())

		if !sdk_utils.IsConnectFail(err) {
			return nil, err
		}

		retryTimes++
		if retryTimes > retryTimesOnConnectFail {
			return nil, err
		}

		if err := c.wait(ctx, time.Second*retryDelaySecOnFail); err != nil {
			return nil, err
		}
	}
}

// DoAsync 执行命令(异步)
func (c *client) DoAsync(ctx context.Context, req *AsyncRequest) *TaskContext {
	task := newTaskContext(ctx, req)

	go func() {
		defer func() {
			if p := recover(); p != nil {
				if e, ok := p.(error); ok {
					task.err = cerrs.ErrPanic.Wrap(e)
				} else {
					task.err = cerrs.ErrPanic.Errorf("[panic] %v", p)
				}
			}
			task.costMs = int32(time.Now().Sub(task.startTime) / time.Millisecond)
			atomic.StoreInt32(&task.finished, 1)
			close(task.doneChan)
		}()

		if err := c.createAsyncTask(task); err != nil {
			task.err = err
			return
		}

		task.rsp, task.err = c.waitTaskFinished(task)
	}()

	return task
}

// createAsyncTask 创建task
func (c *client) createAsyncTask(task *TaskContext) error {
	cmdName := uniCommandName
	if task.req.Product != "" {
		cmdName = task.req.Product
	}
	sdkReq := &xagent.CreateTaskRequest{
		XagentHost: task.req.Addr.Host,
		XagentPort: task.req.Addr.Port,
		Data:       task.req,
		TaskName:   cmdName,
		Timeout:    task.timeoutSec,
	}

	retryTimes := 0
	for {
		sdkRsp, err := c.sdk.CreateTask(task.procCtx, sdkReq)
		if err == nil {
			task.taskId = sdkRsp.TaskId
			return nil
		}

		logger.ComponentLogger.Warning(task.procCtx, "create task fail, action[%s], retryTimes[%d], err[%s]",
			task.req.Action, retryTimes, err.Error())

		if !sdk_utils.IsConnectFail(err) {
			return err
		}

		retryTimes++
		if retryTimes > retryTimesOnConnectFail {
			return err
		}

		if err := c.wait(task.procCtx, time.Second*retryDelaySecOnFail); err != nil {
			logger.ComponentLogger.Warning(task.procCtx, "cancel waiting for create task for action[%s], cause: %s",
				task.req.Action, err.Error())
			return err
		}
	}
}

// waitTaskFinished 查询task进度
func (c *client) waitTaskFinished(task *TaskContext) (*AsyncResponse, error) {
	sdkReq := &xagent.QueryTaskRequest{
		XagentHost: task.req.Addr.Host,
		XagentPort: task.req.Addr.Port,
		TaskId:     task.taskId,
	}

	procCtx, cancel := context.WithTimeout(task.procCtx, time.Duration(task.timeoutSec)*time.Second)
	defer cancel()

	for {
		queryRsp, err := c.sdk.QueryTask(procCtx, sdkReq)

		var retryDelay time.Duration
		if err != nil {
			retryDelay = retryDelaySecOnFail * time.Second
		} else {
			if queryRsp.TaskStatus != xagent.TaskSuccess && queryRsp.TaskStatus != xagent.TaskFailed {
				retryDelay = asyncTaskQueryDelaySec * time.Second
			} else {
				if queryRsp.TaskStatus == xagent.TaskFailed {
					taskResult := base_utils.FormatText(queryRsp.TaskResult)
					logger.ComponentLogger.Warning(procCtx, "async task(%d) failed, result: %s",
						task.taskId, taskResult)
					return nil, cerrs.ErrXAgentTaskFailed.Errorf(taskResult)
				}

				rsp := &AsyncResponse{
					TaskId:     task.taskId,
					TaskStatus: queryRsp.TaskStatus,
					TaskResult: queryRsp.TaskResult,
				}
				return rsp, nil
			}
		}

		if err = c.wait(procCtx, retryDelay); err != nil {
			logger.ComponentLogger.Warning(procCtx, "cancel waiting for task(%d), cause: %s",
				task.taskId, err.Error())
			return nil, err
		}
	}
}

func (c *client) wait(ctx context.Context, t time.Duration) error {
	select {
	case <-time.After(t):
		return nil
	case <-ctx.Done():
		if cerrs.Is(ctx.Err(), context.Canceled) {
			return cerrs.ErrCanceled.Wrap(ctx.Err())
		}
		return cerrs.ErrTimeout.Wrap(ctx.Err())
	}
}

// ParseResult unmarshal result
func (rsp *Response) ParseResult(result interface{}) error {
	return xagent.ParseResult(rsp.Result, result)
}

// ParseResult unmarshal async result
func (rsp *AsyncResponse) ParseResult(result interface{}) error {
	return xagent.ParseResult(rsp.TaskResult, result)
}

// Done async task done chan
func (task *TaskContext) Done() <-chan struct{} {
	return task.doneChan
}

// Wait wait async task finished
func (task *TaskContext) wait() {
	if atomic.LoadInt32(&task.finished) != 1 {
		<-task.doneChan
	}
}

// Err async task error
func (task *TaskContext) Err() error {
	task.wait()
	return task.err
}

// Rsp async task rsp
func (task *TaskContext) Rsp() *AsyncResponse {
	task.wait()
	return task.rsp
}

// Wait async task finished and get rsp
func (task *TaskContext) Wait() (*AsyncResponse, error) {
	task.wait()
	return task.rsp, task.err
}

// CostMs get async task cost(ms)
func (task *TaskContext) CostMs() int32 {
	task.wait()
	return task.costMs
}

// Cancel wait for async task
func (task *TaskContext) Cancel() {
	task.cancel()
}
