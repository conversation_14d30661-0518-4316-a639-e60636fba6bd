//protofsg -with_context -json_tag=1 xagent.proto interface.go
package x1base.component.xagent

message Interface {}

message Addr {
    optional string host = 1;
    optional int32 port = 2;
}

message Request {
    //@inject_tag json:"-"
    optional Addr addr = 1;
    optional string action = 2;
    optional Interface params = 3;
}

message Response {
    optional Interface result = 1;
}

message AsyncRequest {
    //@inject_tag json:"-"
    optional Addr addr = 1;
    optional string action = 2;
    optional Interface params = 3;
    //@inject_tag json:"-"
    optional int32 timeout_sec = 4;
}

message AsyncResponse {
    optional int64  task_id = 1;
    optional string task_status = 2;
    optional string task_result = 3;
}

// Meta common meta info for redis commands
message Meta {
    optional string engine = 1;
    optional string engine_version = 2;
    optional string basedir = 3;
    optional int32  port = 4;
    optional string account_name = 5;
    optional string password = 6; //encrypted
}
