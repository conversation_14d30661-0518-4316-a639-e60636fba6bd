/*
modification history
--------------------
2023/03/27, by wang<PERSON><PERSON>(wang<PERSON><EMAIL>), create
*/

/*
DESCRIPTION
EIP 绑定
*/

package eip

import (
	"context"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/eip"
)

type component struct {
	eipSdk   eip.EIPService
	initOnce sync.Once
}

var defaultComponent = &component{}

// Instance 获取默认的blb组件，必须在gdp初始化env和ral后使用
func Instance() *component {
	defaultComponent.initOnce.Do(func() {
		// default eip sdk
		defaultComponent.eipSdk = eip.NewDefaultEipSdk()
	})

	return defaultComponent
}

func (c *component) BindEip(ctx context.Context, params *BindEipParams) error {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	instanceType := "SCS"
	if params.Product != "" {
		instanceType = params.Product
	}
	sdkBindReq := &eip.BindEipRequest{
		InstanceType:         instanceType,
		InstanceId:           params.AppID,
		InstanceInternalId:   params.InternalID,
		InstanceInternalType: params.InternalType,
		Eip:                  params.Eip,
		Auth:                 auth,
	}
	err = c.eipSdk.BindEip(ctx, sdkBindReq)
	return err
}
