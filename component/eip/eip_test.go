package eip

import (
	"context"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const (
	TestIamUserID = "ea2c4a2286ca4540afcb7f7d4ba2d199"
)

func TestBindEip(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	eipOp := Instance()

	// case 1
	bindReq := BindEipParams{
		UserID:       TestIamUserID,
		AppID:        "scs-bj-sghxikunzuve",
		Eip:          "*************",
		InternalID:   "endpoint-38c97fbe",
		InternalType: "SNIC",
		Product:      "SCS",
	}
	err := eipOp.BindEip(ctx, &bindReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("success")
}
