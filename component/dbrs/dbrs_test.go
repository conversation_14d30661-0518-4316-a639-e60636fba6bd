package dbrs

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/scs/x1-base/sdk/dbrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest/sdkmock"
)

const (
	TestAppID                      = "scs-fsh-pnqbzeysdpyv"
	TestClusterID                  = "49601"
	TestDataBackupRestoreServiceID = "1682393863812128701"
)

func TestCreateBackupPolicy(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	dbrsOp := DbrsResourceOp()

	fmt.Println("case_1")
	rsp, err := dbrsOp.CreateBackupPolicy(ctx, &CreatePolicyConf{
		DataType:              "PegaDB",
		AppID:                 "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay:     []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"},
		DataBackupTime:        "20:25:16Z",
		DayLevelRetentionTime: 5,
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = dbrsOp.CreateBackupPolicy(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	rsp, err = dbrsOp.CreateBackupPolicy(ctx, &CreatePolicyConf{})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	rsp, err = dbrsOp.CreateBackupPolicy(ctx, &CreatePolicyConf{
		DataType: "PegaDB",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_5")
	rsp, err = dbrsOp.CreateBackupPolicy(ctx, &CreatePolicyConf{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_6")
	rsp, err = dbrsOp.CreateBackupPolicy(ctx, &CreatePolicyConf{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"},
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_7")
	rsp, err = dbrsOp.CreateBackupPolicy(ctx, &CreatePolicyConf{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"},
		DataBackupTime:    "20:25:16Z",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_8")
	rsp, err = dbrsOp.CreateBackupPolicy(ctx, &CreatePolicyConf{
		DataType:              "PegaDB",
		AppID:                 "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay:     []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"},
		DataBackupTime:        "20:25:16Z",
		DayLevelRetentionTime: 5,
		Type:                  "",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_9")
	rsp, err = dbrsOp.CreateBackupPolicy(ctx, &CreatePolicyConf{
		DataType:              "PegaDB",
		AppID:                 "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay:     []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"},
		DataBackupTime:        "20:25:16Z",
		DayLevelRetentionTime: 5,
		Type:                  "bos",
		BucketId:              "",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_9")
	rsp, err = dbrsOp.CreateBackupPolicy(ctx, &CreatePolicyConf{
		DataType:              "PegaDB",
		AppID:                 "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay:     []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"},
		DataBackupTime:        "20:25:16Z",
		DayLevelRetentionTime: 5,
		Type:                  "bos",
		BucketId:              "scs-rdb-test",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_10")
	rsp, err = dbrsOp.CreateBackupPolicy(ctx, &CreatePolicyConf{
		DataType:              "Redis",
		AppID:                 "scs-bj-efwgoqtwuizd",
		DataBackupWeekDay:     []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"},
		DataBackupTime:        "20:25:16Z",
		DayLevelRetentionTime: 5,
		Type:                  "bos",
		BucketId:              "bj-rds-binlog-flash-file-test",
		Region:                "bj",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestDeleteBackupPolicy(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	dbrsOp := DbrsResourceOp()

	fmt.Println("case_1")
	rsp, err := dbrsOp.DeleteBackupPolicy(ctx, &CommonPolicyParams{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = dbrsOp.DeleteBackupPolicy(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	rsp, err = dbrsOp.DeleteBackupPolicy(ctx, &CommonPolicyParams{
		DataType: "PegaDB",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	rsp, err = dbrsOp.DeleteBackupPolicy(ctx, &CommonPolicyParams{
		AppID: "scs-fsh-pnqbzeysdpyv",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestQueryBackupPolicy(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	dbrsOp := DbrsResourceOp()

	fmt.Println("case_1")
	rsp, err := dbrsOp.QueryBackupPolicy(ctx, &CommonPolicyParams{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = dbrsOp.QueryBackupPolicy(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	rsp, err = dbrsOp.QueryBackupPolicy(ctx, &CommonPolicyParams{
		DataType: "PegaDB",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	rsp, err = dbrsOp.QueryBackupPolicy(ctx, &CommonPolicyParams{
		AppID: "scs-fsh-pnqbzeysdpyv",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestModifyBackupPolicy(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	dbrsOp := DbrsResourceOp()

	fmt.Println("case_1")
	rsp, err := dbrsOp.ModifyBackupPolicy(ctx, &CommonPolicyConf{
		DataType:              "PegaDB",
		AppID:                 "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay:     []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"},
		DataBackupTime:        "20:25:16Z",
		DayLevelRetentionTime: 5,
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = dbrsOp.ModifyBackupPolicy(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	rsp, err = dbrsOp.ModifyBackupPolicy(ctx, &CommonPolicyConf{})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	rsp, err = dbrsOp.ModifyBackupPolicy(ctx, &CommonPolicyConf{
		DataType: "PegaDB",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_5")
	rsp, err = dbrsOp.ModifyBackupPolicy(ctx, &CommonPolicyConf{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_6")
	rsp, err = dbrsOp.ModifyBackupPolicy(ctx, &CommonPolicyConf{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"},
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_7")
	rsp, err = dbrsOp.ModifyBackupPolicy(ctx, &CommonPolicyConf{
		DataType:          "PegaDB",
		AppID:             "scs-fsh-pnqbzeysdpyv",
		DataBackupWeekDay: []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"},
		DataBackupTime:    "20:25:16Z",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestQueryBackupList(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	dbrsOp := DbrsResourceOp()

	fmt.Println("case_1")
	rsp, err := dbrsOp.QueryBackupList(ctx, &CommonPolicyParams{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = dbrsOp.QueryBackupList(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	rsp, err = dbrsOp.QueryBackupList(ctx, &CommonPolicyParams{
		DataType: "PegaDB",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	rsp, err = dbrsOp.QueryBackupList(ctx, &CommonPolicyParams{
		AppID: "scs-fsh-pnqbzeysdpyv",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestCreateBackupTask(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	dbrsOp := DbrsResourceOp()

	fmt.Println("case_1")
	rsp, err := dbrsOp.CreateBackupTask(ctx, &dbrs.CreateBackupTaskRequest{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
		Mode:     "manual",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = dbrsOp.CreateBackupTask(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	rsp, err = dbrsOp.CreateBackupTask(ctx, &dbrs.CreateBackupTaskRequest{})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	rsp, err = dbrsOp.CreateBackupTask(ctx, &dbrs.CreateBackupTaskRequest{
		DataType: "PegaDB",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_5")
	rsp, err = dbrsOp.CreateBackupTask(ctx, &dbrs.CreateBackupTaskRequest{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestDeleteBackupRecord(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	dbrsOp := DbrsResourceOp()

	fmt.Println("case_1")
	rsp, err := dbrsOp.DeleteBackupRecord(ctx, &dbrs.DeleteBackupRecordRequest{
		DataType:        "PegaDB",
		AppID:           "scs-fsh-pnqbzeysdpyv",
		AppDataBackupID: "1686229550618563320",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = dbrsOp.DeleteBackupRecord(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	rsp, err = dbrsOp.DeleteBackupRecord(ctx, &dbrs.DeleteBackupRecordRequest{})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	rsp, err = dbrsOp.DeleteBackupRecord(ctx, &dbrs.DeleteBackupRecordRequest{
		DataType: "PegaDB",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_5")
	rsp, err = dbrsOp.DeleteBackupRecord(ctx, &dbrs.DeleteBackupRecordRequest{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestQueryAppBackupDetailByAppBackupId(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	dbrsOp := DbrsResourceOp()

	fmt.Println("case_1")
	rsp, err := dbrsOp.QueryAppBackupDetailByAppBackupId(ctx, &QueryAppBackupDetailParams{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = dbrsOp.QueryAppBackupDetailByAppBackupId(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	rsp, err = dbrsOp.QueryAppBackupDetailByAppBackupId(ctx, &QueryAppBackupDetailParams{
		DataType: "PegaDB",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	rsp, err = dbrsOp.QueryAppBackupDetailByAppBackupId(ctx, &QueryAppBackupDetailParams{
		AppID: "scs-fsh-pnqbzeysdpyv",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_5")
	rsp, err = dbrsOp.QueryAppBackupDetailByAppBackupId(ctx, &QueryAppBackupDetailParams{
		DataType:        "PegaDB",
		AppID:           "scs-fsh-pnqbzeysdpyv",
		AppDataBackupID: "123",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_6")
	rsp, err = dbrsOp.QueryAppBackupDetailByAppBackupId(ctx, &QueryAppBackupDetailParams{
		DataType:             "PegaDB",
		AppID:                "scs-fsh-pnqbzeysdpyv",
		AppDataBackupID:      "123",
		DownloadUrlExpireSec: 10,
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestQueryBackupDetailByBackupId(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	dbrsOp := DbrsResourceOp()

	fmt.Println("case_1")
	rsp, err := dbrsOp.QueryBackupDetailByBackupId(ctx, &QueryBackupDetailParams{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_2")
	rsp, err = dbrsOp.QueryBackupDetailByBackupId(ctx, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_3")
	rsp, err = dbrsOp.QueryBackupDetailByBackupId(ctx, &QueryBackupDetailParams{})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	rsp, err = dbrsOp.QueryBackupDetailByBackupId(ctx, &QueryBackupDetailParams{
		DataType: "PegaDB",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_4")
	rsp, err = dbrsOp.QueryBackupDetailByBackupId(ctx, &QueryBackupDetailParams{
		DataType: "PegaDB",
		AppID:    "scs-fsh-pnqbzeysdpyv",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_6")
	rsp, err = dbrsOp.QueryBackupDetailByBackupId(ctx, &QueryBackupDetailParams{
		DataType:  "PegaDB",
		AppID:     "scs-fsh-pnqbzeysdpyv",
		ClusterID: "124",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")

	fmt.Println("case_6")
	rsp, err = dbrsOp.QueryBackupDetailByBackupId(ctx, &QueryBackupDetailParams{
		DataType:     "PegaDB",
		AppID:        "scs-fsh-pnqbzeysdpyv",
		ClusterID:    "124",
		DatabackupID: "12678",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(base_utils.Format(rsp))
	fmt.Println("success")
}

func TestModifyBackupEncryptPolicyOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDbrsService := sdkmock.NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().ModifyBackupEncryptPolicy(gomock.Any(), gomock.Any()).Return(&dbrs.CommonPolicyResponse{}, nil)

	testComponent := &dbrsResourceOp{}
	testComponent.dbrsSdk = mockDbrsService

	modifyParams := ModifyEncryptPolicyParams{
		DataType: "redis",
		AppID:    "scs-test-xxx",
	}
	err := testComponent.ModifyBackupEncryptPolicy(ctx, &modifyParams)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}

func TestCreateBackupRestoreOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDbrsService := sdkmock.NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().CreateBackupRestore(gomock.Any(), gomock.Any()).Return(&dbrs.CreateRestoreResponse{
		DataBackupRestoreServiceID: TestDataBackupRestoreServiceID,
	}, nil)

	testComponent := &dbrsResourceOp{}
	testComponent.dbrsSdk = mockDbrsService

	createParams := CreateRestoreParams{
		DataType:           "Redis",
		SourceAppID:        TestAppID,
		SourceClusterID:    TestClusterID,
		SourceDataBackupID: "1718610686873522801",
		DestAppID:          TestAppID,
	}
	createParams.DestInstances = append(createParams.DestInstances, DestInstance{
		AgentPoolName:  "public-scs",
		InstanceID:     "xxxuuid",
		XagentHost:     "127.0.0.1",
		XagentPort:     8500,
		AgentHost:      "127.0.0.1",
		AgentPort:      8995,
		AgentLocalPort: 8995,
		DatabaseHost:   "127.0.0.1",
		DatabasePort:   6379,
		DatabasePath:   "/home/<USER>/dbrs/database/redis_1",
	})
	restoreServiceID, err := testComponent.CreateBackupRestore(ctx, &createParams)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	if restoreServiceID != TestDataBackupRestoreServiceID {
		t.Errorf("restoreServiceID not match")
	}
}

func TestQueryBackupRestoreOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDbrsService := sdkmock.NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().QueryBackupRestore(gomock.Any(), gomock.Any()).Return(&dbrs.QueryRestoreResponse{
		Status: "success",
	}, nil)

	testComponent := &dbrsResourceOp{}
	testComponent.dbrsSdk = mockDbrsService

	queryParams := QueryRestoreParams{
		DataType:                   "Redis",
		DataBackupRestoreServiceID: TestDataBackupRestoreServiceID,
	}
	restoreServiceStatus, err := testComponent.QueryBackupRestore(ctx, &queryParams)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	if restoreServiceStatus != "success" {
		t.Errorf("restoreServiceStatus not match")
	}
}
func TestQueryBackupUsageOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDbrsService := sdkmock.NewMockDbrsService(ctrl)
	mockDbrsService.EXPECT().QueryBackupUsage(gomock.Any(), gomock.Any()).Return(&dbrs.QueryBackupUsageResponse{
		DataType:                           "Redis",
		AppID:                              TestAppID,
		ClusterID:                          "",
		LogicalLogBackupSizeBytes:          0,
		LogicalLogBackupBillingSizeBytes:   0,
		PhysicalLogBackupSizeBytes:         0,
		PhysicalLogBackupBillingSizeBytes:  0,
		LogicalDataBackupSizeBytes:         0,
		LogicalDataBackupBillingSizeBytes:  0,
		PhysicalDataBackupSizeBytes:        2343432644,
		PhysicalDataBackupBillingSizeBytes: 2343432644,
		SnapshotDataBackupSizeBytes:        0,
		SnapshotDataBackupBillingSizeBytes: 0,
	}, nil)

	testComponent := &dbrsResourceOp{}
	testComponent.dbrsSdk = mockDbrsService

	queryParams := QueryBackupUsageParams{
		DataType:  "redis",
		AppID:     TestAppID,
		DbaasType: "public-scs",
	}
	resp, err := testComponent.QueryBackupUsage(ctx, &queryParams)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	if resp.AppID != TestAppID {
		t.Errorf("restoreServiceStatus not match")
	}
}
