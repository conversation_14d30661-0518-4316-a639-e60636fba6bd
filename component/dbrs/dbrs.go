package dbrs

import (
	"context"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/dbrs"
)

type dbrsResourceOp struct {
	dbrsSdk dbrs.DbrsService
}

var dbrsResourceOpObj DbrsResource

var once sync.Once

func DbrsResourceOp() DbrsResource {
	once.Do(func() {
		dbrsResourceOpObj = &dbrsResourceOp{
			dbrsSdk: dbrs.NewBefaultDbrsSdk(),
		}

	})
	return dbrsResourceOpObj
}

// 支持创建 RDB(快照) 和 AOF 备份策略
func (dr *dbrsResourceOp) CreateBackupPolicy(ctx context.Context, req *CreatePolicyConf) (rsp *CommonPolicyResult, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.PolicyType == "" && req.DataBackupTime == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataBackupTime is null")
	}
	if req.PolicyType == "" && req.DayLevelRetentionTime == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DayLevelRetentionTime is 0")
	}
	if req.Type == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param Type is null")
	}
	if req.BucketId == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param BucketId is null")
	}
	if req.Region == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param Region is null")
	}

	dbaasType := "public-scs"
	if req.Product != "" {
		dbaasType = req.Product
	}

	createPolicyRequest := dbrs.CreatePolicyRequest{
		DataType:  req.DataType,
		AppID:     req.AppID,
		DbaasType: dbaasType, // 备份产品线
		Region:    req.Region,
	}

	if req.PolicyType == "logical_log_backup" {
		// SCS 使用的 public-scs
		createPolicyRequest.PoolName = dbaasType
		createPolicyRequest.PolicyType = req.PolicyType
		// 策略名称
		createPolicyRequest.PolicyName = req.AppID
		createPolicyRequest.BackupStorages = []dbrs.DataStoragesConf{
			{
				Type:     req.Type,
				BucketId: req.BucketId,
			},
		}
		createPolicyRequest.BackupRetainStrategys = []dbrs.BackupRetainStrategys{
			{
				StartSeconds: 0,
				EndSeconds:   -req.DayLevelRetentionTime * 24 * 3600,
				RetainCount:  0, // 不限制
				Precision:    1, // 过期删除时的计算精度
			},
		}
	} else {
		createPolicyRequest.DataBackupWeekDay = req.DataBackupWeekDay
		createPolicyRequest.DataBackupTime = req.DataBackupTime

		createPolicyRequest.DataBackupRetainStrategys = []dbrs.BackupRetainStrategys{
			{
				StartSeconds: 0,
				EndSeconds:   -req.DayLevelRetentionTime * 24 * 3600,
				RetainCount:  25, // 备份数量上限为25个,15个例行备份+10个手动备份
				Precision:    86400,
			},
		}
		createPolicyRequest.DataStorages = []dbrs.DataStoragesConf{
			{
				Type:     req.Type,
				BucketId: req.BucketId,
			},
		}

	}
	resp, err := dr.dbrsSdk.CreateBackupPolicy(ctx, &createPolicyRequest)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "create backup policy fail,err:%s", err.Error())
		return nil, err
	}
	return &CommonPolicyResult{
		DataType: resp.DataType,
		AppID:    resp.AppID,
	}, nil
}

func (dr *dbrsResourceOp) DeleteBackupPolicy(ctx context.Context, req *CommonPolicyParams) (rsp *CommonPolicyResult, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}

	dbaasType := "public-scs"
	if req.Product != "" {
		dbaasType = req.Product
	}
	resp, err := dr.dbrsSdk.DeleteBackupPolicy(ctx, &dbrs.DeletePolicyRequest{
		DataType:   req.DataType,
		AppID:      req.AppID,
		DbaasType:  dbaasType, // 备份产品线
		PolicyType: req.PolicyType,
	})
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "delete backup policy fail,err:%s", err.Error())
		return nil, err
	}
	return &CommonPolicyResult{
		DataType: resp.DataType,
		AppID:    resp.AppID,
	}, nil
}

func (dr *dbrsResourceOp) QueryBackupPolicy(ctx context.Context, req *CommonPolicyParams) (rsp *CommonPolicyConf, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}

	var resp CommonPolicyConf
	resp.AppID = req.AppID
	resp.DataType = req.DataType

	logger.ComponentLogger.Trace(ctx, "query backup policy Req: %+v", *req)

	dbaasType := "public-scs"
	if req.Product != "" {
		dbaasType = req.Product
	}
	backupPolicy, err := dr.dbrsSdk.QueryBackupPolicy(ctx, &dbrs.QueryPolicyRequest{
		DataType:   req.DataType,
		AppID:      req.AppID,
		DbaasType:  dbaasType, // 备份产品线
		PolicyType: req.PolicyType,
	})
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "query backup policy fail,err:%s", err.Error())
		return nil, err
	}

	// 集群没有备份策略
	if len(backupPolicy.Policys) == 0 {
		logger.ComponentLogger.Trace(ctx, "app:%s has no backup policy.", req.AppID)
		return &resp, nil
	} else if len(backupPolicy.Policys) > 1 {
		// 备份策略应该只有一条
		logger.ComponentLogger.Warning(ctx, "app:%s has more than one backup policy, please check it.", req.AppID)
		return nil, err
	}

	backupRetainStrategy := backupPolicy.Policys[0].DataBackupRetainStrategys
	// 集群备份保留策略
	if len(backupRetainStrategy) == 0 {
		logger.ComponentLogger.Trace(ctx, "app:%s has no backup retain strategys.", req.AppID)
		return &resp, nil
	} else if len(backupRetainStrategy) > 1 {
		// 集群备份保留策略应该只有一条
		logger.ComponentLogger.Warning(ctx, "app:%s has more than one backup retain strategys, please check it.", req.AppID)
		return nil, err
	}

	dayLevelRetentionTime := (backupRetainStrategy[0].StartSeconds - backupRetainStrategy[0].EndSeconds) / 3600 / 24

	return &CommonPolicyConf{
		DataType:              backupPolicy.DataType,
		AppID:                 backupPolicy.AppID,
		DataBackupWeekDay:     backupPolicy.Policys[0].DataBackupWeekDay,
		DataBackupTime:        backupPolicy.Policys[0].DataBackupTime, // 备份开始时间，UTC格式
		DayLevelRetentionTime: dayLevelRetentionTime,                  // 备份保留天数,产品设计的取值范围为1-15

	}, nil
}

func (dr *dbrsResourceOp) ModifyBackupPolicy(ctx context.Context, req *CommonPolicyConf) (rsp *CommonPolicyResult, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.DataBackupTime == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataBackupTime is null")
	}
	if req.DayLevelRetentionTime == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DayLevelRetentionTime is 0")
	}

	dbaasType := "public-scs"
	if req.Product != "" {
		dbaasType = req.Product
	}
	resp, err := dr.dbrsSdk.ModifyBackupPolicy(ctx, &dbrs.CommonPolicyRequest{
		DataType:          req.DataType,
		AppID:             req.AppID,
		DataBackupWeekDay: req.DataBackupWeekDay,
		DataBackupTime:    req.DataBackupTime,
		DataBackupRetainStrategys: []dbrs.BackupRetainStrategys{
			{
				StartSeconds: 0,
				EndSeconds:   -req.DayLevelRetentionTime * 24 * 3600,
				RetainCount:  25, // 备份数量上限为25个,15个例行备份+10个手动备份
				Precision:    86400,
			},
		},
		DbaasType: dbaasType, // 备份产品线
	})
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "modify backup policy fail,err:%s", err.Error())
		return nil, err
	}
	return &CommonPolicyResult{
		DataType: resp.DataType,
		AppID:    resp.AppID,
	}, nil
}

func (dr *dbrsResourceOp) QueryBackupList(ctx context.Context, req *CommonPolicyParams) (rsp *dbrs.QueryBackupListResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}

	dbaasType := "public-scs"
	if req.Product != "" {
		dbaasType = req.Product
	}
	resp, err := dr.dbrsSdk.QueryBackupList(ctx, &dbrs.QueryBackupListRequest{
		DataType:  req.DataType,
		AppID:     req.AppID,
		Marker:    0,
		MaxKeys:   25,        // 最多只能创建25个备份记录,15个例行备份+10个手动备份
		DbaasType: dbaasType, // 备份产品线
	})
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "query backup list fail,err:%s", err.Error())
		return nil, err
	}
	return resp, nil
}

func (dr *dbrsResourceOp) CreateBackupTask(ctx context.Context, req *dbrs.CreateBackupTaskRequest) (rsp *dbrs.CreateBackupTaskResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.Mode == "" {
		req.Mode = "manual" // 默认手动备份
	}

	dbaasType := "public-scs"
	if req.Product != "" {
		dbaasType = req.Product
	}
	if req.DbaasType == "" {
		req.DbaasType = dbaasType // 跟dbrs约定的产品线
	}

	resp, err := dr.dbrsSdk.CreateBackupTask(ctx, req)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "create backup task fail,err:%s", err.Error())
		return nil, err
	}
	return resp, nil
}

func (dr *dbrsResourceOp) DeleteBackupRecord(ctx context.Context, req *dbrs.DeleteBackupRecordRequest) (rsp *dbrs.DeleteBackupRecordResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.AppDataBackupID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppDataBackupID is null")
	}

	dbaasType := "public-scs"
	if req.Product != "" {
		dbaasType = req.Product
	}
	if req.DbaasType == "" {
		req.DbaasType = dbaasType // 跟dbrs约定的产品线
	}

	resp, err := dr.dbrsSdk.DeleteBackupRecord(ctx, req)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "delete backup record fail,err:%s", err.Error())
		return nil, err
	}
	return resp, nil
}

func (dr *dbrsResourceOp) QueryAppBackupDetailByAppBackupId(ctx context.Context,
	req *QueryAppBackupDetailParams) (rsp *dbrs.QueryAppBackupDetailResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.AppDataBackupID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppDataBackupID is null")
	}
	if req.DownloadUrlExpireSec == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DownloadUrlExpireSec is null")
	}

	dbaasType := "public-scs"
	if req.Product != "" {
		dbaasType = req.Product
	}

	resp, err := dr.dbrsSdk.QueryAppBackupDetailByAppBackupId(ctx, &dbrs.QueryAppBackupDetailRequest{
		DataType:             req.DataType,
		AppID:                req.AppID,
		AppDataBackupID:      req.AppDataBackupID,
		DownloadUrlExpireSec: req.DownloadUrlExpireSec,
		Marker:               0,
		MaxKeys:              1000,      // 默认值：1000
		DbaasType:            dbaasType, // 备份产品线
	})
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "query app backup detail fail,err:%s", err.Error())
		return nil, err
	}
	return resp, nil
}

func (dr *dbrsResourceOp) QueryBackupDetailByBackupId(ctx context.Context, req *QueryBackupDetailParams) (rsp *dbrs.QueryShardBackupDetailResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is null")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is null")
	}
	if req.ClusterID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param ClusterID is null")
	}
	if req.DatabackupID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DatabackupID is null")
	}
	if req.DownloadUrlExpireSec == 0 {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DownloadUrlExpireSec is null")
	}

	dbaasType := "public-scs"
	if req.Product != "" {
		dbaasType = req.Product
	}
	resp, err := dr.dbrsSdk.QueryBackupDetailByBackupId(ctx, &dbrs.QueryShardBackupDetailRequest{
		DataType:             req.DataType,
		AppID:                req.AppID,
		ClusterID:            req.ClusterID,
		DatabackupID:         req.DatabackupID,
		DownloadUrlExpireSec: req.DownloadUrlExpireSec,
		DbaasType:            dbaasType, // 备份产品线
	})
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "query shard backup detail fail,err:%s", err.Error())
		return nil, err
	}
	return resp, nil
}

func (dr *dbrsResourceOp) ModifyBackupEncryptPolicy(ctx context.Context, req *ModifyEncryptPolicyParams) (err error) {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.AppID == "" {
		return cerrs.ErrInvalidParams.Errorf("req param AppID is empty")
	}

	_, err = dr.dbrsSdk.ModifyBackupEncryptPolicy(ctx, &dbrs.ModifyEncryptPolicyRequest{
		DataType: req.DataType,
		AppID:    req.AppID,
		EncryptStrategy: &dbrs.EncryptStrategy{
			EncryptEnable:            req.EncryptEnable,
			KeyManagementType:        req.KeyManagementType,
			KeyManagementServiceName: req.KeyManagementServiceName,
			KecretKeyID:              req.KecretKeyID,
		},
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "modify backup encrypt policy fail,err:%s", err.Error())
		return err
	}
	return nil
}

func (dr *dbrsResourceOp) CreateBackupRestore(ctx context.Context, req *CreateRestoreParams) (restoreServiceID string, err error) {
	if req == nil {
		return "", cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return "", cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.SourceAppID == "" {
		return "", cerrs.ErrInvalidParams.Errorf("req param source AppID is empty")
	}
	if req.SourceClusterID == "" {
		return "", cerrs.ErrInvalidParams.Errorf("req param source ClusterID is empty")
	}
	if req.SourceDataBackupID == "" {
		return "", cerrs.ErrInvalidParams.Errorf("req param source DataBackupID is empty")
	}
	if req.DestAppID == "" {
		return "", cerrs.ErrInvalidParams.Errorf("req param dest AppID is empty")
	}
	if req.DestInstances == nil {
		return "", cerrs.ErrInvalidParams.Errorf("req param dest Instances is null")
	}

	createRequest := dbrs.CreateRestoreRequest{
		DataType:              req.DataType,
		AppID:                 req.SourceAppID,
		ClusterID:             req.SourceClusterID,
		DataBackupID:          req.SourceDataBackupID,
		Name:                  req.DestAppID,
		PerformanceFlavorID:   "middle",
		SpeedLimitBytesPerSec: 536870912,
	}
	for _, destInstance := range req.DestInstances {
		createRequest.DestInstances = append(createRequest.DestInstances, dbrs.DestInstance{
			AgentPoolName:  destInstance.AgentPoolName,
			InstanceID:     destInstance.InstanceID,
			XagentHost:     destInstance.XagentHost,
			XagentPort:     destInstance.XagentPort,
			AgentHost:      destInstance.AgentHost,
			AgentPort:      destInstance.AgentPort,
			AgentLocalPort: destInstance.AgentLocalPort,
			DatabaseHost:   destInstance.DatabaseHost,
			DatabasePort:   destInstance.DatabasePort,
			DatabasePath:   destInstance.DatabasePath,
		})
	}
	resp, err := dr.dbrsSdk.CreateBackupRestore(ctx, &createRequest)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "CreateBackupRestore fail,err:%s", err.Error())
		return "", err
	}
	restoreServiceID = resp.DataBackupRestoreServiceID
	return restoreServiceID, nil
}

func (dr *dbrsResourceOp) QueryBackupRestore(ctx context.Context, req *QueryRestoreParams) (restoreServiceStatus string, err error) {
	if req == nil {
		return "", cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return "", cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.DataBackupRestoreServiceID == "" {
		return "", cerrs.ErrInvalidParams.Errorf("req param DataBackupRestoreServiceID is empty")
	}

	resp, err := dr.dbrsSdk.QueryBackupRestore(ctx, &dbrs.QueryRestoreRequest{
		DataType:                   req.DataType,
		DataBackupRestoreServiceID: req.DataBackupRestoreServiceID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "modify backup encrypt policy fail,err:%s", err.Error())
		return "", err
	}
	restoreServiceStatus = resp.Status
	return restoreServiceStatus, nil
}

func (dr *dbrsResourceOp) PrecheckPointInTimeRestore(ctx context.Context, req *PrecheckPointInTimeRestoreParams) (
	rsp *dbrs.PrecheckPointInTimeRestoreResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is empty")
	}
	if req.ClusterID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param ClusterID is empty")
	}
	if req.RestorePointInDateTime == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param RestorePointInDateTime is empty")
	}

	rsp, err = dr.dbrsSdk.PrecheckPointInTimeRestore(ctx, &dbrs.PrecheckPointInTimeRestoreRequest{
		DataType:               req.DataType,
		AppID:                  req.AppID,
		ClusterID:              req.ClusterID,
		RestorePointInDateTime: req.RestorePointInDateTime,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "PrecheckPointInTimeRestore fail,err:%s", err.Error())
		return rsp, err
	}
	return rsp, nil
}
func (dr *dbrsResourceOp) QueryPointInTimeRestoreRange(ctx context.Context, req *QueryPointInTimeRestoreRangeParams) (
	rsp *dbrs.QueryPointInTimeRestoreRangeResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is empty")
	}
	if req.ClusterID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param ClusterID is empty")
	}

	rsp, err = dr.dbrsSdk.QueryPointInTimeRestoreRange(ctx, &dbrs.QueryPointInTimeRestoreRangeRequest{
		DataType:  req.DataType,
		AppID:     req.AppID,
		ClusterID: req.ClusterID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "QueryPointInTimeRestoreRange fail,err:%s", err.Error())
		return rsp, err
	}
	return rsp, nil
}
func (dr *dbrsResourceOp) CreatePointInTimeRestore(ctx context.Context, req *CreatePointInTimeRestoreParams) (restoreServiceID string, err error) {
	if req == nil {
		return "", cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return "", cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.SourceAppID == "" {
		return "", cerrs.ErrInvalidParams.Errorf("req param source AppID is empty")
	}
	if req.SourceClusterID == "" {
		return "", cerrs.ErrInvalidParams.Errorf("req param source ClusterID is empty")
	}
	if req.RestorePointInDateTime == "" {
		return "", cerrs.ErrInvalidParams.Errorf("req param RestorePointInDateTime is empty")
	}
	if req.DestAppID == "" {
		return "", cerrs.ErrInvalidParams.Errorf("req param dest AppID is empty")
	}
	if req.DestInstances == nil {
		return "", cerrs.ErrInvalidParams.Errorf("req param dest Instances is null")
	}

	createRequest := dbrs.CreatePointInTimeRestoreRequest{
		DataType:               req.DataType,
		AppID:                  req.SourceAppID,
		ClusterID:              req.SourceClusterID,
		RestorePointInDateTime: req.RestorePointInDateTime,
		Name:                   req.DestAppID,
		PerformanceFlavorID:    "middle",
		SpeedLimitBytesPerSec:  536870912,
	}
	for _, destInstance := range req.DestInstances {
		createRequest.DestInstances = append(createRequest.DestInstances, dbrs.DestInstance{
			AgentPoolName:  destInstance.AgentPoolName,
			Region:         destInstance.Region,
			InstanceID:     destInstance.InstanceID,
			XagentHost:     destInstance.XagentHost,
			XagentPort:     destInstance.XagentPort,
			AgentHost:      destInstance.AgentHost,
			AgentPort:      destInstance.AgentPort,
			AgentLocalPort: destInstance.AgentLocalPort,
			DatabaseHost:   destInstance.DatabaseHost,
			DatabasePort:   destInstance.DatabasePort,
			DatabasePath:   destInstance.DatabasePath,
		})
	}
	resp, err := dr.dbrsSdk.CreatePointInTimeRestore(ctx, &createRequest)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "CreatePointInTimeRestore fail,err:%s", err.Error())
		return "", err
	}
	restoreServiceID = resp.PointInTimeRestoreServiceID
	return restoreServiceID, nil
}

func (dr *dbrsResourceOp) QueryPointInTimeRestore(ctx context.Context, req *QueryPointInTimeRestoreParams) (restoreServiceStatus string, err error) {
	if req == nil {
		return "", cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return "", cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.PointInTimeRestoreServiceID == "" {
		return "", cerrs.ErrInvalidParams.Errorf("req param PointInTimeRestoreServiceID is empty")
	}

	resp, err := dr.dbrsSdk.QueryPointInTimeRestore(ctx, &dbrs.QueryPointInTimeRestoreRequest{
		DataType:                    req.DataType,
		PointInTimeRestoreServiceID: req.PointInTimeRestoreServiceID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "QueryPointInTimeRestore fail,err:%s", err.Error())
		return "", err
	}
	restoreServiceStatus = resp.Status
	return restoreServiceStatus, nil
}

func (dr *dbrsResourceOp) QueryBackupUsage(ctx context.Context, req *QueryBackupUsageParams) (rsp *dbrs.QueryBackupUsageResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.DataType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DataType is empty")
	}
	if req.AppID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param AppID is empty")
	}
	if req.DbaasType == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param DbaasType is empty")
	}

	resp, err := dr.dbrsSdk.QueryBackupUsage(ctx, &dbrs.QueryBackupUsageRequest{
		DataType:  req.DataType,
		AppID:     req.AppID,
		DbaasType: req.DbaasType,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "QueryBackupUsage fail,err:%s", err.Error())
		return nil, err
	}
	return resp, nil
}
