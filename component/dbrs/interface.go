package dbrs

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/dbrs"
)

type CommonPolicyParams struct {
	DataType   string `json:"dataType"` // 例：PegaDB/Redis
	AppID      string `json:"appID"`
	Product    string `json:"product"`
	PolicyType string `json:"policyType"` // 策略类型：logical_log_backup（逻辑日志备份）, 默认是 RDB/快照备份
}
type CommonPolicyResult struct {
	DataType string `json:"dataType"` // 例：PegaDB/Redis
	AppID    string `json:"appID"`
}

type CreatePolicyConf struct {
	DataType              string   `json:"dataType"` // 例：PegaDB
	AppID                 string   `json:"appID"`
	DataBackupWeekDay     []string `json:"dataBackupWeekDay"`
	DataBackupTime        string   `json:"dataBackupTime"`        // 备份开始时间，UTC格式
	DayLevelRetentionTime int64    `json:"dayLevelRetentionTime"` // 备份保留天数,产品设计的取值范围为1-15
	Type                  string   `json:"type"`                  // "bos"
	BucketId              string   `json:"id"`
	Region                string   `json:"region"`
	Product               string   `json:"product"`
	PolicyType            string   `json:"policyType"`
}

type CommonPolicyConf struct {
	DataType              string   `json:"dataType"` // 例：PegaDB
	AppID                 string   `json:"appID"`
	DataBackupWeekDay     []string `json:"dataBackupWeekDay"`
	DataBackupTime        string   `json:"dataBackupTime"`        // 备份开始时间，UTC格式
	DayLevelRetentionTime int64    `json:"dayLevelRetentionTime"` // 备份保留天数,产品设计的取值范围为1-15
	Product               string   `json:"product"`
}

type QueryAppBackupDetailParams struct {
	DataType             string `json:"dataType"` // 例：PegaDB/Redis
	AppID                string `json:"appID"`
	AppDataBackupID      string `json:"appDataBackupID"`
	DownloadUrlExpireSec int64  `json:"downloadUrlExpireSec"` // 默认值：43200
	Product              string `json:"product"`
}

type QueryBackupDetailParams struct {
	DataType             string `json:"dataType"` // 例：PegaDB/Redis
	AppID                string `json:"appID"`
	ClusterID            string `json:"clusterID"`
	DatabackupID         string `json:"databackupID"`
	DownloadUrlExpireSec int64  `json:"downloadUrlExpireSec"` // 默认值：43200
	Product              string `json:"product"`
}

type ModifyEncryptPolicyParams struct {
	DataType                 string `json:"dataType"` // 例：PegaDB
	AppID                    string `json:"appID"`
	EncryptEnable            bool   `json:"encryptEnable"`
	KeyManagementType        string `json:"keyManagementType"` // 密钥管理方式：自托管(self_kms)、百度KMS(baidu_kms)
	KeyManagementServiceName string `json:"keyManagementServiceName,omitempty"`
	KecretKeyID              string `json:"secretKeyID,omitempty"`
}

type DestInstance struct {
	AgentPoolName  string `json:"agentPoolName"`
	Region         string `json:"region"`
	InstanceID     string `json:"instanceID"`
	XagentHost     string `json:"xagentHost"`
	XagentPort     int    `json:"xagentPort"`
	AgentHost      string `json:"agentHost"`
	AgentPort      int    `json:"agentPort"`
	AgentLocalPort int    `json:"agentLocalPort"`
	DatabaseHost   string `json:"databaseHost"`
	DatabasePort   int    `json:"databasePort"`
	DatabasePath   string `json:"databasePath"`
}

type CreateRestoreParams struct {
	DataType           string         `json:"dataType"`
	SourceAppID        string         `json:"appID"`
	SourceClusterID    string         `json:"clusterID"`
	SourceDataBackupID string         `json:"dataBackupID"`
	DestAppID          string         `json:"name"`
	DestInstances      []DestInstance `json:"destInstances"`
}

type QueryRestoreParams struct {
	DataType                   string `json:"dataType"`
	DataBackupRestoreServiceID string `json:"dataBackupRestoreServiceID"`
}
type PrecheckPointInTimeRestoreParams struct {
	DataType               string `json:"dataType"`
	AppID                  string `json:"appID"`
	ClusterID              string `json:"clusterID"`
	RestorePointInDateTime string `json:"restorePointInDateTime"`
}
type QueryPointInTimeRestoreRangeParams struct {
	DataType  string `json:"dataType"`
	AppID     string `json:"appID"`
	ClusterID string `json:"clusterID"`
}
type CreatePointInTimeRestoreParams struct {
	DataType               string         `json:"dataType"`
	SourceAppID            string         `json:"appID"`
	SourceClusterID        string         `json:"clusterID"`
	RestorePointInDateTime string         `json:"restorePointInDateTime"`
	DestAppID              string         `json:"name"`
	DestInstances          []DestInstance `json:"destInstances"`
}

type QueryPointInTimeRestoreParams struct {
	DataType                    string `json:"dataType"`
	PointInTimeRestoreServiceID string `json:"pointInTimeRestoreServiceID"`
}

type QueryBackupUsageParams struct {
	DataType  string `json:"dataType"`
	AppID     string `json:"appID"`
	DbaasType string `json:"dbaasType"`
}

type DbrsResource interface {
	CreateBackupPolicy(ctx context.Context, req *CreatePolicyConf) (rsp *CommonPolicyResult, err error)
	DeleteBackupPolicy(ctx context.Context, req *CommonPolicyParams) (rsp *CommonPolicyResult, err error)
	QueryBackupPolicy(ctx context.Context, req *CommonPolicyParams) (rsp *CommonPolicyConf, err error)
	ModifyBackupPolicy(ctx context.Context, req *CommonPolicyConf) (rsp *CommonPolicyResult, err error)
	QueryBackupList(ctx context.Context, req *CommonPolicyParams) (rsp *dbrs.QueryBackupListResponse, err error)
	CreateBackupTask(ctx context.Context, req *dbrs.CreateBackupTaskRequest) (rsp *dbrs.CreateBackupTaskResponse, err error)
	DeleteBackupRecord(ctx context.Context, req *dbrs.DeleteBackupRecordRequest) (rsp *dbrs.DeleteBackupRecordResponse, err error)
	QueryAppBackupDetailByAppBackupId(ctx context.Context, req *QueryAppBackupDetailParams) (rsp *dbrs.QueryAppBackupDetailResponse, err error)
	QueryBackupDetailByBackupId(ctx context.Context, req *QueryBackupDetailParams) (rsp *dbrs.QueryShardBackupDetailResponse, err error)
	ModifyBackupEncryptPolicy(ctx context.Context, req *ModifyEncryptPolicyParams) (err error)
	CreateBackupRestore(ctx context.Context, req *CreateRestoreParams) (restoreServiceID string, err error)
	QueryBackupRestore(ctx context.Context, req *QueryRestoreParams) (restoreServiceStatus string, err error)
	PrecheckPointInTimeRestore(ctx context.Context, req *PrecheckPointInTimeRestoreParams) (rsp *dbrs.PrecheckPointInTimeRestoreResponse, err error)
	QueryPointInTimeRestoreRange(ctx context.Context, req *QueryPointInTimeRestoreRangeParams) (rsp *dbrs.QueryPointInTimeRestoreRangeResponse, err error)
	CreatePointInTimeRestore(ctx context.Context, req *CreatePointInTimeRestoreParams) (restoreServiceID string, err error)
	QueryPointInTimeRestore(ctx context.Context, req *QueryPointInTimeRestoreParams) (restoreServiceStatus string, err error)
	QueryBackupUsage(ctx context.Context, req *QueryBackupUsageParams) (rsp *dbrs.QueryBackupUsageResponse, err error)
}
