/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/12/13
 * File: commands_test.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package metaserver TODO package function desc
package metaserver

import (
	"context"
	"testing"
)

func TestSetPegaAutoBalanceHashTagForSync(t *testing.T) {
	go InitMockMetaserver()
	ctx := context.Background()
	fakeCli, err := getFakeMetaComp(ctx, TypeScsSlot, t)
	if err != nil {
		t.Fatal(err.Error())
	}
	alwaysErrCli, err := getAlwaysErrMetaComp(ctx, TypeScsSlot, t)
	if err != nil {
		t.Fatal(err.Error())
	}
	if err := fakeCli.SetPegaAutoBalanceHashTagForSync(ctx, 1, 1, "no", "no"); err != nil {
		t.Errorf(err.<PERSON>rror())
	}
	if err := alwaysErrCli.SetPegaAutoBalanceHashTagForSync(ctx, 1, 1, "no", "no"); err == nil {
		t.Errorf(err.Error())
	}
}

func TestShrinkPegaHashTagForSync(t *testing.T) {
	go InitMockMetaserver()
	ctx := context.Background()
	fakeCli, err := getFakeMetaComp(ctx, TypeScsSlot, t)
	if err != nil {
		t.Fatal(err.Error())
	}
	alwaysErrCli, err := getAlwaysErrMetaComp(ctx, TypeScsSlot, t)
	if err != nil {
		t.Fatal(err.Error())
	}
	if err := fakeCli.ShrinkPegaHashTagForSync(ctx, 1, -1, "no", "no"); err != nil {
		t.Errorf(err.Error())
	}
	if err := alwaysErrCli.ShrinkPegaHashTagForSync(ctx, 1, -1, "no", "no"); err == nil {
		t.Errorf(err.Error())
	}
}

func TestShrinkClusterHashTagForSync(t *testing.T) {
	go InitMockMetaserver()
	ctx := context.Background()
	fakeCli, err := getFakeMetaComp(ctx, TypeScsSlot, t)
	if err != nil {
		t.Fatal(err.Error())
	}
	alwaysErrCli, err := getAlwaysErrMetaComp(ctx, TypeScsSlot, t)
	if err != nil {
		t.Fatal(err.Error())
	}
	if err := fakeCli.ShrinkClusterHashTagForSync(ctx, 1, -1, "no", "no"); err != nil {
		t.Errorf(err.Error())
	}
	if err := alwaysErrCli.ShrinkClusterHashTagForSync(ctx, 1, -1, "no", "no"); err == nil {
		t.Errorf(err.Error())
	}
}

func TestMcTypeSet(t *testing.T) {
	go InitMockMetaserver()
	ctx := context.Background()
	fakeCli, err := getFakeMetaComp(ctx, TypeScsSlot, t)
	if err != nil {
		t.Fatal(err.Error())
	}
	alwaysErrCli, err := getAlwaysErrMetaComp(ctx, TypeScsSlot, t)
	if err != nil {
		t.Fatal(err.Error())
	}
	if err := fakeCli.McTypeSet(ctx, 1, "redis"); err != nil {
		t.Errorf(err.Error())
	}
	if err := alwaysErrCli.McTypeSet(ctx, 1, "redis"); err == nil {
		t.Errorf(err.Error())
	}
	if err := fakeCli.McTypeSet(ctx, 1, "questionmark"); err == nil {
		t.Errorf(err.Error())
	}
}
