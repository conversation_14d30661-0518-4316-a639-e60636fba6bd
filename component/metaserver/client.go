/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
BCC创建配置
*/

package metaserver

import (
	"context"
	goredis "github.com/go-redis/redis/v8"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
)

func getMasterNode(metaCluster *x1model.MetaCluster) (*x1model.MetaNode, error) {
	for _, node := range metaCluster.MetaNodes {
		if node.MetaNodeID == metaCluster.CurMaster {
			return node, nil
		}
	}
	return nil, cerrs.ErrMetaFailed.Errorf("current master %s not found", metaCluster.CurMaster)
}

func getNodeByIpAndPort(metaCluster *x1model.MetaCluster, ip string, port int) (*x1model.MetaNode, error) {
	for _, node := range metaCluster.MetaNodes {
		if node.Ip == ip && node.Port == port {
			return node, nil
		}
	}
	return nil, cerrs.ErrMetaFailed.Errorf("node %s:%d not found", ip, port)
}

func getMasterClient(ctx context.Context, metaCluster *x1model.MetaCluster) (*single_redis.SingleClient, error) {
	masterNode, err := getMasterNode(metaCluster)
	if err != nil {
		return nil, err
	}
	c := single_redis.NewClient(masterNode.FloatingIP, masterNode.Port,
		single_redis.WithPassword(metaCluster.Password), single_redis.WithRetry(config.Connection.Retry),
		single_redis.WithTimeout(&single_redis.ConfigTimeout{
			Connect: time.Duration(config.Connection.ConnectTimeout) * time.Millisecond,
			Read:    time.Duration(config.Connection.ReadTimeout) * time.Millisecond,
			Write:   time.Duration(config.Connection.WriteTimeout) * time.Millisecond,
		}))
	infoRaw, err := c.Info(ctx, "replication").Result()
	if err != nil {
		c.Close()
		return nil, err
	}
	info, err := single_redis.ParseReplicationInfo(ctx, infoRaw)
	if err != nil {
		c.Close()
		return nil, err
	}
	if info.Role == "master" {
		return c, nil
	} else {
		newMasterNode, err := getNodeByIpAndPort(metaCluster, info.MasterHost, info.MasterPort)
		if err != nil {
			c.Close()
			return nil, err
		}
		return single_redis.NewClient(newMasterNode.FloatingIP, newMasterNode.Port,
			single_redis.WithPassword(metaCluster.Password), single_redis.WithRetry(config.Connection.Retry),
			single_redis.WithTimeout(&single_redis.ConfigTimeout{
				Connect: time.Duration(config.Connection.ConnectTimeout) * time.Millisecond,
				Read:    time.Duration(config.Connection.ReadTimeout) * time.Millisecond,
				Write:   time.Duration(config.Connection.WriteTimeout) * time.Millisecond,
			})), nil
	}
}

func GetMetaserverClient(ctx context.Context, metaCluster *x1model.MetaCluster) (*MetaserverClient, error) {
	if metaCluster.Type != TypeScs && metaCluster.Type != TypeScsSlot && metaCluster.Type != TypeBDRP {
		return nil, cerrs.ErrMetaFailed.Errorf("type %s not support", metaCluster.Type)
	}
	cli, err := getMasterClient(ctx, metaCluster)
	if err != nil {
		return nil, cerrs.ErrMetaFailed.Wrap(err)
	}
	mc := &MetaserverClient{
		cli:         cli,
		metaCluster: metaCluster,
	}
	mc.process = cli.Process
	mc.metaType = metaCluster.Type
	mc.checksync = func(sctx context.Context) error {
		return single_redis.CheckCmdSync(sctx, cli, metaCluster.Quorum, config.Connection.Retry, time.Duration(config.Consistency.SyncCheckInterval)*time.Millisecond)
	}
	mc.getPipe = func() goredis.Pipeliner {
		return mc.cli.Pipeline()
	}
	return mc, nil
}
