/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
BCC创建配置
*/

package metaserver

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"testing"

	"github.com/alicebob/miniredis/v2"
	"github.com/tidwall/redcon"

	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
)

func getFakeMetaComp(ctx context.Context, metaType string, t *testing.T) (*MetaserverClient, error) {
	var fakeRedisCli = single_redis.NewClient("127.0.0.1", "7900")
	mc := &MetaserverClient{
		cli: fakeRedisCli,
	}
	mc.process = fakeRedisCli.Process
	mc.metaType = metaType
	mc.checksync = func(sctx context.Context) error {
		return nil
	}
	return mc, nil
}

func getAlwaysErrMetaComp(ctx context.Context, metaType string, t *testing.T) (*MetaserverClient, error) {
	s := miniredis.RunT(t)
	var fakeRedisCli = single_redis.NewClient(s.Host(), s.Port())
	mc := &MetaserverClient{
		cli: fakeRedisCli,
	}
	mc.process = fakeRedisCli.Process
	mc.metaType = metaType
	mc.checksync = func(sctx context.Context) error {
		return nil
	}
	return mc, nil
}

func TestMetaserver(t *testing.T) {
	// ctx := context.Background()
	// appID := "test_set_cluster_auto_balance_hashtag"
	//
	// app, err := x1model.ApplicationGetByAppId(ctx, appID)
	// if err != nil {
	//	fmt.Println(err)
	//	return
	// }
	// metaCli, err := getMetaCliTest(ctx, app)
	// if err != nil {
	//	return
	// }
	//
	// clusterId := app.AppShortID
	//
	// err = metaCli.SetClusterAutoBalanceHashTagForSync(ctx, clusterId, 1, "true", "false")
	// if err != nil {
	//	fmt.Println(err)
	// }
	// fmt.Println("SetClusterAutoBalanceHashTagForSync success")
	//
	// err = metaCli.ShrinkClusterHashTagForSync(ctx, clusterId, 1, "true", "false")
	// if err != nil {
	//	fmt.Println(err)
	// }
	fmt.Println("ShrinkClusterHashTagForSync success")
}

var addr = "127.0.0.1:7900"

var initMockMetaserverOnce sync.Once

func InitMockMetaserver() {
	initMockMetaserverOnce.Do(func() {
		go log.Printf("started server at %s", addr)
		err := redcon.ListenAndServe(addr,
			func(conn redcon.Conn, cmd redcon.Command) {
				switch strings.ToLower(string(cmd.Args[0])) {
				default:
					conn.WriteString("OK")
				case "mccheckscalestatus":
					conn.WriteInt(1)
				case "mcget":
					conn.WriteArray(0)
				case "mcshrink":
					conn.WriteArray(2)
					conn.WriteBulkString("item 1")
					conn.WriteBulkString("item 2")
				}
			},
			func(conn redcon.Conn) bool {
				// Use this function to accept or deny the connection.
				// log.Printf("accept: %s", conn.RemoteAddr())
				return true
			},
			func(conn redcon.Conn, err error) {
				// This is called when the connection has been closed
				// log.Printf("closed: %s, err: %v", conn.RemoteAddr(), err)
			},
		)
		if err != nil {
			log.Fatal(err)
		}
	})
}
