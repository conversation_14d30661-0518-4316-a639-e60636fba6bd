/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
BCC创建配置
*/

package metaserver

import (
	"context"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
)

var (
	config   *Config    = &Config{}
	initOnce *sync.Once = &sync.Once{}
)

type Config struct {
	Consistency        *Consistency
	Connection         *Connection
	DefaultClusterFlag *DefaultClusterFlag
	IDCMap             []*IDCMap
}

type Consistency struct {
	SyncCheckRetry    int
	SyncCheckInterval int
}

type Connection struct {
	Retry          int
	ConnectTimeout int
	ReadTimeout    int
	WriteTimeout   int
}

type DefaultClusterFlag struct {
	MasterFailover bool
	MasterRead     bool
	SlaveRead      bool
}

type IDCMap struct {
	OriginalIDC string
	TargetIDC   string
}

func MustLoadConf(ctx context.Context) {
	initOnce.Do(func() {
		if err := compo_utils.LoadConf("metaserver", config); err != nil {
			panic(err.Error())
		}
	})
}

func GetDefaultClusterFlag(ctx context.Context) *DefaultClusterFlag {
	return config.DefaultClusterFlag
}
