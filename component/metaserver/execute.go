/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
BCC创建配置
*/

package metaserver

import (
	"context"
	"strconv"

	goredis "github.com/go-redis/redis/v8"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func getRedisInfo(ctx context.Context, ip string, port int) (*single_redis.ReplicationInfo, error) {
	c := single_redis.NewClient(ip, port)
	defer c.Close()
	infoRaw, err := c.Info(ctx, "replication").Result()
	if err != nil {
		return nil, err
	}
	return single_redis.ParseReplicationInfo(ctx, infoRaw)
}

func sendSlaveOf(ctx context.Context, ip string, port int, masterIp string, masterPort int) error {
	c := single_redis.NewClient(ip, port)
	defer c.Close()
	return slaveOfRenamed(ctx, c, masterIp, strconv.Itoa(masterPort))
}

func checkAndSetSlaveOf(ctx context.Context, m metaserverable, shardId int) error {
	s, err := m.GetShard(ctx, shardId)
	if err != nil {
		return cerrs.ErrMetaFailed.Wrap(err)
	}
	mri, err := m.GetRedis(ctx, s.Master)
	if err != nil {
		return cerrs.ErrMetaFailed.Wrap(err)
	}
	mriInfo, err := getRedisInfo(ctx, mri.Ip.FloatingIp, mri.Port)
	if err != nil {
		return err
	}
	logger.ComponentLogger.Trace(ctx,"[DEBUG]mriInfo:%s",base_utils.Format(mriInfo))

	if mriInfo.Role != "master" {
		return cerrs.ErrMetaFailed.Errorf("master redis(%s:%d) of shard %d in metaserver is not master", mri.Ip.FloatingIp, mri.Port, shardId)
	}
	for _, slaveId := range s.Slaves {
		sri, err := m.GetRedis(ctx, slaveId)
		if err != nil {
			return err
		}
		sriInfo, err := getRedisInfo(ctx, sri.Ip.FloatingIp, sri.Port)
		if err != nil {
			logger.ComponentLogger.Trace(ctx, "get redis(%s:%d) of shard %d info failed", sri.Ip.FloatingIp, sri.Port, shardId)
			continue
		}
		if sriInfo.Role != "slave" || (sriInfo.MasterHost != mri.Ip.Ip || sriInfo.MasterPort != mri.Port) {
			if err := sendSlaveOf(ctx, sri.Ip.FloatingIp, sri.Port, mri.Ip.Ip, mri.Port); err != nil {
				logger.ComponentLogger.Trace(ctx, "send slaveof %s %d to redis(%s:%d) of shard %d info failed", mri.Ip.Ip, mri.Port, sri.Ip.FloatingIp, sri.Port, shardId)
				return err
			}
		}
	}
	return nil
}

func executeAndCheckIntCmds(ctx context.Context, process processFunc, expectRets []int64, cmds ...interface{}) error {
	cmd := goredis.NewIntCmd(ctx, cmds...)
	_ = process(ctx, cmd)
	ret, err := cmd.Result()
	if err != nil {
		return cerrs.ErrMetaFailed.Errorf("execute metaserver cmd %s failed, err: %s, reply: %d", base_utils.Format(cmds), err.Error(), ret)
	}
	if in, _ := base_utils.InArray(ret, expectRets); !in {
		return cerrs.ErrMetaFailed.Errorf("execute metaserver cmd %s failed, expect: %s, actual reply: %d",
			base_utils.Format(cmds), base_utils.Format(expectRets), ret)
	}
	return nil
}

func executeAndCheckStatusCmds(ctx context.Context, process processFunc, cmds ...interface{}) error {
	cmd := goredis.NewStatusCmd(ctx, cmds...)
	_ = process(ctx, cmd)
	ret, err := cmd.Result()
	if err != nil {
		return cerrs.ErrMetaFailed.Errorf("execute metaserver cmd %s failed, err: %s, reply: %s", base_utils.Format(cmds), err.Error(), ret)
	}
	if ret != "OK" {
		return cerrs.ErrMetaFailed.Errorf("execute metaserver cmd %s failed, expect: OK, actual reply: %s", base_utils.Format(cmds), ret)
	}
	return nil
}

func executeStringSliceCmds(ctx context.Context, process processFunc, cmds ...interface{}) ([]string, error) {
	cmd := goredis.NewStringSliceCmd(ctx, cmds...)
	_ = process(ctx, cmd)
	ret, err := cmd.Result()
	if err != nil {
		return nil, cerrs.ErrMetaFailed.Errorf("execute metaserver cmd %s failed, err: %s, reply: %s", base_utils.Format(cmds), err.Error(), base_utils.Format(ret))
	}
	return ret, nil
}

func executeIntCmds(ctx context.Context, process processFunc, cmds ...interface{}) (int64, error) {
	cmd := goredis.NewIntCmd(ctx, cmds...)
	_ = process(ctx, cmd)
	ret, err := cmd.Result()
	if err != nil {
		return -1, cerrs.ErrMetaFailed.Errorf("execute metaserver cmd %s failed, err: %s, reply: %s", base_utils.Format(cmds), err.Error(), base_utils.Format(ret))
	}
	return ret, nil
}

func slaveOfRenamed(ctx context.Context, c *single_redis.SingleClient, masterHost, masterPort string) error {
	if err := c.SlaveOf(ctx, masterHost, masterPort).Err(); err != nil {
		logger.ComponentLogger.Trace(ctx, "cmd slaveof %s %s failed, err: %s", masterHost, masterPort, err.Error())
		return c.Do(ctx, "aae420ac56ef116058218c11d8b35b30SLAVEOF", masterHost, masterPort).Err()
	}
	return nil
}
