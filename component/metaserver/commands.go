/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
BCC创建配置
*/

package metaserver

import (
	"context"
	"strconv"
	"strings"

	goredis "github.com/go-redis/redis/v8"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/vep"
)

func IsNil(err error) bool {
	return strings.Contains(err.Error(), goredis.Nil.Error())
}

// GetAppClusterId 获取app绑定的cluster id
// Metaserver命令：maget ${cluster_id}
func (m metaserverable) GetAppClusterId(ctx context.Context, appId int) (int, error) {
	ret, err := executeStringSliceCmds(ctx, m.process, "maget", appId)
	if err != nil {
		return -1, err
	}
	if len(ret) < 2 || ret[0] != "cluster" {
		return -1, cerrs.ErrMetaFailed.Errorf("invalid maget reply %s", base_utils.Format(ret))
	}
	clusterId, err := strconv.Atoi(ret[1])
	if err != nil {
		return -1, cerrs.ErrMetaFailed.Errorf("invalid maget reply %s", base_utils.Format(ret))
	}
	return clusterId, nil
}

// SetAppClusterId 将cluster绑定到app
// Metaserver命令：maset ${app_id} ${cluster_id}
func (m metaserverable) SetAppClusterId(ctx context.Context, appId int, clusterId int) error {
	if c, err := m.GetAppClusterId(ctx, appId); err == nil && c == clusterId {
		logger.ComponentLogger.Trace(ctx, "cluster %d app %d has already been set", clusterId, appId)
		return nil
	}
	if err := executeAndCheckIntCmds(ctx, m.process, []int64{0, 1}, "maset", appId, clusterId); err != nil {
		return err
	}
	return m.checksync(ctx)
}

// GetCluster 获取cluster信息
func (m metaserverable) GetCluster(ctx context.Context, clusterId int) (*Cluster, error) {
	ret, err := executeStringSliceCmds(ctx, m.process, "mcget", clusterId)
	if err != nil {
		return nil, err
	}
	return parseMcget(ret, m.metaType)
}

// GetCluster 获取cluster信息
func (m metaserverable) GetClusterDetail(ctx context.Context, clusterId int) (*ClusterDetail, error) {
	ret, err := executeStringSliceCmds(ctx, m.process, "mcget", clusterId)
	if err != nil {
		return nil, err
	}
	return parseMcgetDetail(ret, m.metaType)
}

func (m metaserverable) UpdateSlotAndPublish(ctx context.Context, slotId int, clusterId int, srcShard int, destShard int, publishToProxy int) error {
	err := executeAndCheckStatusCmds(ctx, m.process, "mslotupdate", slotId, clusterId, srcShard, destShard, publishToProxy)
	if err != nil {
		return err
	}
	return m.checksync(ctx)
}

// SetCluster 设置cluster信息
// Metaserver命令: mcset ${cluster_id} ${proxy_id} ${port} ${master_failover_flag} ${master_read_flag} ${slave_read_flag}
func (m metaserverable) SetCluster(ctx context.Context, clusterId int, proxyId int, port int, flags map[string]bool) error {
	if _, err := m.GetCluster(ctx, clusterId); err == nil {
		logger.ComponentLogger.Trace(ctx, "cluster %d has been set", clusterId)
		return nil
	}
	var flagsMasterFailover, flagsMasterRead, flagsSlaveRead int
	if f, ok := flags[FlagsMasterFailover]; ok && f {
		flagsMasterFailover = 1
	}
	if f, ok := flags[FlagsMasterRead]; ok && f {
		flagsMasterRead = 1
	}
	if f, ok := flags[FlagsSlaveRead]; ok && f {
		flagsSlaveRead = 1
	}
	if err := executeAndCheckIntCmds(ctx, m.process, []int64{1}, "mcset", clusterId, proxyId, port, flagsMasterFailover, flagsMasterRead, flagsSlaveRead); err != nil {
		return err
	}
	return m.checksync(ctx)
}

// SetClusterFlag 设置cluster flag
func (m metaserverable) SetClusterFlag(ctx context.Context, clusterId int, flag string, val string) error {
	if err := executeAndCheckStatusCmds(ctx, m.process, "mcflagset", clusterId, flag, val); err != nil {
		return err
	}
	return m.checksync(ctx)
}

func (m metaserverable) SetClusterMaster(ctx context.Context, clusterId int, domain string, port int) error {
	if err := executeAndCheckStatusCmds(ctx, m.process, "mcsetmaster", clusterId, domain, port); err != nil {
		return err
	}
	return m.checksync(ctx)
}

func (m metaserverable) ShrinkCluster(ctx context.Context, clusterId int, shardCount int) error {
	inBalance, err := m.IsClusterInAutoBalance(ctx, clusterId)
	if err != nil {
		return err
	}
	if inBalance {
		logger.ComponentLogger.Trace(ctx, "cluster %d is in balance", clusterId)
		return nil
	}
	c, err := m.GetCluster(ctx, clusterId)
	if err != nil {
		return err
	}
	toShrinkCount := c.ShardNum - shardCount
	if toShrinkCount <= 0 {
		return cerrs.ErrMetaFailed.Errorf("target shard count %d less than current shard count %d", shardCount, c.ShardNum)
	}
	shrinkReply, err := executeStringSliceCmds(ctx, m.process, "mcshrink", clusterId, toShrinkCount)
	if err != nil {
		return err
	}
	if len(shrinkReply) < 2 {
		return cerrs.ErrMetaFailed.Errorf("unexpected mcshrink reploy %s", base_utils.Format(shrinkReply))
	}
	return m.checksync(ctx)
}

func (m metaserverable) ShrinkClusterForSync(ctx context.Context, clusterId int, shardCount int, opHeaderFlag string) error {
	inBalance, err := m.IsClusterInAutoBalance(ctx, clusterId)
	if err != nil {
		return err
	}
	if inBalance {
		logger.ComponentLogger.Trace(ctx, "cluster %d is in balance", clusterId)
		return nil
	}
	c, err := m.GetCluster(ctx, clusterId)
	if err != nil {
		return err
	}
	toShrinkCount := c.ShardNum - shardCount
	if toShrinkCount <= 0 {
		return cerrs.ErrMetaFailed.Errorf("target shard count %d less than current shard count %d", shardCount, c.ShardNum)
	}
	shrinkReply, err := executeStringSliceCmds(ctx, m.process, "mcshrink", clusterId, toShrinkCount, opHeaderFlag)
	if err != nil {
		return err
	}
	if len(shrinkReply) < 2 {
		return cerrs.ErrMetaFailed.Errorf("unexpected mcshrink reploy %s", base_utils.Format(shrinkReply))
	}
	return m.checksync(ctx)
}

func (m metaserverable) ShrinkClusterHashTagForSync(ctx context.Context, clusterId int, shardCount int,
	hashTagFlag string, opHeaderFlag string) error {
	return m.shrinkCluster(ctx, clusterId, shardCount, hashTagFlag, opHeaderFlag, false)
}

func (m metaserverable) ShrinkPegaHashTagForSync(ctx context.Context, clusterId int, shardCount int,
	hashTagFlag string, opHeaderFlag string) error {
	return m.shrinkCluster(ctx, clusterId, shardCount, hashTagFlag, opHeaderFlag, true)
}

// shrinkCluster 基于老的ShrinkClusterHashTagForSync，增加forceSlotsMode参数，用于控制是否强制使用slots_migrate模式迁移分片数据
// 注意：如果是老版本metaserver，这么用会报错，所以必须要先升级metaserver再上线管控代码，按计划目前只用于Pega热活
// 内核文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/CyUESrdXXe/8344d79c133240
func (m metaserverable) shrinkCluster(ctx context.Context, clusterId int, shardCount int,
	hashTagFlag string, opHeaderFlag string, forceSlotsMode bool) error {
	inBalance, err := m.IsClusterInAutoBalance(ctx, clusterId)
	if err != nil {
		return err
	}
	if inBalance {
		logger.ComponentLogger.Trace(ctx, "cluster %d is in balance", clusterId)
		return nil
	}
	c, err := m.GetCluster(ctx, clusterId)
	if err != nil {
		return err
	}
	toShrinkCount := c.ShardNum - shardCount
	if toShrinkCount <= 0 {
		return cerrs.ErrMetaFailed.Errorf("target shard count %d less than current shard count %d", shardCount, c.ShardNum)
	}
	var shrinkReply []string
	if forceSlotsMode {
		shrinkReply, err = executeStringSliceCmds(ctx, m.process, "mcshrink", clusterId, toShrinkCount,
			"hash_tag", hashTagFlag, "op_header", opHeaderFlag, "migrate_type", "slots_migrate")
	} else {
		shrinkReply, err = executeStringSliceCmds(ctx, m.process, "mcshrink", clusterId, toShrinkCount,
			"hash_tag", hashTagFlag, "op_header", opHeaderFlag)
	}

	if err != nil {
		return err
	}
	if len(shrinkReply) < 2 {
		return cerrs.ErrMetaFailed.Errorf("unexpected mcshrink reploy %s", base_utils.Format(shrinkReply))
	}
	return m.checksync(ctx)
}

func (m metaserverable) GetClusterShrinkedShards(ctx context.Context, clusterId int) ([]int, error) {
	ret, err := executeStringSliceCmds(ctx, m.process, "mcshrinkshard", clusterId)
	if err != nil {
		return nil, err
	}
	return parseMcshrinkshard(ret, m.metaType)
}

func (m metaserverable) SetClusterAutoBalance(ctx context.Context, clusterId int, flag int) error {
	if err := executeAndCheckStatusCmds(ctx, m.process, "mcbalaceset", clusterId, flag); err != nil {
		return err
	}
	return m.checksync(ctx)
}

func (m metaserverable) SetClusterAutoBalanceForSync(ctx context.Context, clusterId int, flag int, opHeaderFlag string) error {
	if err := executeAndCheckStatusCmds(ctx, m.process, "mcbalaceset", clusterId, flag, opHeaderFlag); err != nil {
		return err
	}
	return m.checksync(ctx)
}

func (m metaserverable) SetClusterAutoBalanceHashTagForSync(ctx context.Context, clusterId int,
	flag int, hashTagFlag string, opHeaderFlag string) error {
	if err := executeAndCheckStatusCmds(ctx, m.process, "mcbalaceset", clusterId, flag,
		"hash_tag", hashTagFlag, "op_header", opHeaderFlag); err != nil {
		return err
	}
	return m.checksync(ctx)
}

// SetPegaAutoBalanceHashTagForSync 强制使用slots_migrate模式迁移分片数据
// 注意：如果是老版本metaserver，这么用会报错，所以必须要先升级metaserver再上线管控代码，按计划目前只用于Pega热活
// 内核文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/CyUESrdXXe/8344d79c133240
func (m metaserverable) SetPegaAutoBalanceHashTagForSync(ctx context.Context, clusterId int,
	flag int, hashTagFlag string, opHeaderFlag string) error {
	if err := executeAndCheckStatusCmds(ctx, m.process, "mcbalaceset", clusterId, flag,
		"hash_tag", hashTagFlag, "op_header", opHeaderFlag, "migrate_type", "slots_migrate"); err != nil {
		return err
	}
	return m.checksync(ctx)
}

func (m metaserverable) IsClusterInAutoBalance(ctx context.Context, clusterId int) (bool, error) {
	ret, err := executeIntCmds(ctx, m.process, "mccheckscalestatus", clusterId)
	if err != nil {
		return false, err
	}
	if ret != 1 {
		return true, nil
	}
	return false, nil
}

func (m metaserverable) IsClusterLastAutoBalanceSuccess(ctx context.Context, clusterId int) (bool, error) {
	if m.metaType != TypeScs {
		return true, nil
	}
	ret, err := executeIntCmds(ctx, m.process, "mccheckscalestatus", clusterId)
	if err != nil {
		return false, err
	}
	if ret == 0 || ret == -1 {
		return true, nil
	}
	return false, nil
}

func (m metaserverable) GetScaleStatus(ctx context.Context, clusterId int) (*ScaleStatus, error) {
	ret, err := executeStringSliceCmds(ctx, m.process, "mcscalestatus", clusterId)
	if err != nil {
		return nil, err
	}
	return parseMcscaleStatus(ret, m.metaType)
}

func (m metaserverable) DeleteCluster(ctx context.Context, clusterId int) error {
	if _, err := m.GetCluster(ctx, clusterId); err != nil && IsNil(err) {
		logger.ComponentLogger.Trace(ctx, "cluster %d has been delete", clusterId)
		return nil
	}
	if err := executeAndCheckIntCmds(ctx, m.process, []int64{1}, "mcdel", clusterId); err != nil {
		return err
	}
	return m.checksync(ctx)
}

func (m metaserverable) InitCluster(ctx context.Context, clusterId int) error {
	if c, err := m.GetCluster(ctx, clusterId); err == nil {
		initedShards := map[int]bool{}
		for shardId := range c.Slot {
			initedShards[shardId] = true
		}
		if len(initedShards) == len(c.Shards) {
			logger.ComponentLogger.Trace(ctx, "cluster %d has been init", clusterId)
			return nil
		}
	}
	if err := executeAndCheckStatusCmds(ctx, m.process, "mcinit", clusterId); err != nil {
		return err
	}
	return m.checksync(ctx)
}

func (m metaserverable) ForceUpdateClusterSlotsDist(ctx context.Context, clusterId int, targetDist map[int]Slots) error {
	c, err := m.GetCluster(ctx, clusterId)
	if err != nil {
		return err
	}
	targetDistSs, err := unParseSlotToStringSlice(targetDist)
	if err != nil {
		return err
	}
	curDistSs, err := unParseSlotToStringSlice(c.Slot)
	if err != nil {
		return err
	}
	for idx, targetShardId := range targetDistSs {
		curShardId := curDistSs[idx]
		if curShardId != targetShardId {
			if err := executeAndCheckStatusCmds(ctx, m.process, "mslotupdate", idx, clusterId, curShardId, targetShardId, 1); err != nil {
				return err
			}
			if err := m.checksync(ctx); err != nil {
				return err
			}
		}
	}
	return nil
}

func (m metaserverable) GetShard(ctx context.Context, shardId int) (*Shard, error) {
	ret, err := executeStringSliceCmds(ctx, m.process, "msget", shardId)
	if err != nil {
		return nil, err
	}
	return parseMsget(ret, m.metaType)
}

func (m metaserverable) SetShard(ctx context.Context, shardId int, clusterId int) error {
	if _, err := m.GetShard(ctx, shardId); err == nil {
		logger.ComponentLogger.Trace(ctx, "shard %d has been set", shardId)
		return nil
	}
	if err := executeAndCheckIntCmds(ctx, m.process, []int64{1}, "msset", shardId, clusterId); err != nil {
		return err
	}
	return m.checksync(ctx)
}

func (m metaserverable) DeleteShard(ctx context.Context, shardId int) error {
	if _, err := m.GetShard(ctx, shardId); err != nil && IsNil(err) {
		logger.ComponentLogger.Trace(ctx, "shard %d has been delete", shardId)
		return nil
	}
	if err := executeAndCheckIntCmds(ctx, m.process, []int64{1}, "msdel", shardId); err != nil {
		return err
	}
	return m.checksync(ctx)
}

func (m metaserverable) GetProxy(ctx context.Context, proxyId int) (*Proxy, error) {
	ret, err := executeStringSliceCmds(ctx, m.process, "mpget", proxyId)
	if err != nil {
		return nil, err
	}
	return parseMpget(ret, m.metaType)
}

func (m metaserverable) SetProxy(ctx context.Context, proxyId int) error {
	if _, err := m.GetProxy(ctx, proxyId); err == nil {
		logger.ComponentLogger.Trace(ctx, "shard %d has been set", proxyId)
		return nil
	}
	if err := executeAndCheckIntCmds(ctx, m.process, []int64{1}, "mpset", proxyId); err != nil {
		return err
	}
	return m.checksync(ctx)
}

func (m metaserverable) DeleteProxy(ctx context.Context, proxyId int) error {
	if _, err := m.GetProxy(ctx, proxyId); err != nil && IsNil(err) {
		logger.ComponentLogger.Trace(ctx, "proxy %d has been delete", proxyId)
		return nil
	}
	if err := executeAndCheckIntCmds(ctx, m.process, []int64{1}, "mpdel", proxyId); err != nil {
		return err
	}
	return m.checksync(ctx)
}

func (m metaserverable) GetProxyInstance(ctx context.Context, proxyInstId int) (*ProxyInst, error) {
	ret, err := executeStringSliceCmds(ctx, m.process, "mpiget", proxyInstId)
	if err != nil {
		return nil, err
	}
	return parseMpiget(ret, m.metaType)
}

func (m metaserverable) SetProxyInstance(ctx context.Context, proxyInstId int, proxyId int, ip *MetaIp, cdn string) error {
	cdn = getIdc(cdn)
	if pi, err := m.GetProxyInstance(ctx, proxyInstId); err == nil {
		if pi != nil && pi.Ip.FloatingIp == ip.FloatingIp && pi.Ip.Ip == ip.Ip && pi.Cdn == cdn && pi.Proxy == proxyId {
			logger.ComponentLogger.Trace(ctx, "proxy inst %d has been set", proxyInstId)
			return nil
		}
		npi := &ProxyInst{
			Proxy: proxyId,
			Ip:    &MetaIp{FloatingIp: ip.FloatingIp, Ip: ip.Ip},
			Cdn:   cdn,
		}
		return cerrs.ErrMetaFailed.Errorf("cannot update proxy inst %s to %s", base_utils.Format(pi), base_utils.Format(npi))
	}
	if err := executeAndCheckIntCmds(ctx, m.process, []int64{1}, "mpiset", proxyInstId, proxyId, ip.FloatingIp+","+ip.Ip, cdn); err != nil {
		return err
	}
	return m.checksync(ctx)
}

func (m metaserverable) DeleteProxyInstance(ctx context.Context, proxyInstId int) error {
	if _, err := m.GetProxyInstance(ctx, proxyInstId); err != nil && IsNil(err) {
		logger.ComponentLogger.Trace(ctx, "proxy inst %d has been delete", proxyInstId)
		return nil
	}
	if err := executeAndCheckIntCmds(ctx, m.process, []int64{1}, "mpidel", proxyInstId); err != nil {
		return err
	}
	return m.checksync(ctx)
}

func (m metaserverable) GetRedis(ctx context.Context, redisId int) (*Redis, error) {
	ret, err := executeStringSliceCmds(ctx, m.process, "mriget", redisId)
	if err != nil {
		return nil, err
	}
	return parseMriget(ret, m.metaType)
}

func (m metaserverable) SetRedis(ctx context.Context, redisId int, shardId int, ip *MetaIp, port int, role int, priority int, cdn string) error {
	var err error
	var vnetPort int = port
	cdn = getIdc(cdn)
	var isEdge bool = func() bool {
		if strings.HasPrefix(ip.FloatingIp, "vep://") {
			return true
		}
		return false
	}()
	if isEdge {
		ip.FloatingIp, port, err = vep.GetVpcEndpointModelServices().ParseVpcEndpoint(ip.FloatingIp, port)
		if err != nil {
			return cerrs.ErrMetaFailed.Errorf("parse vep %s failed: %s", ip.FloatingIp, err.Error())
		}
	}
	if ri, err := m.GetRedis(ctx, redisId); err == nil {
		if ri != nil && ri.Shard == shardId && ri.Type == role && ri.Ip.FloatingIp == ip.FloatingIp && ri.Ip.Ip == ip.Ip && ri.Cdn == cdn && ri.Port == port && ri.Priority == priority {
			logger.ComponentLogger.Trace(ctx, "redis inst %d has been set", redisId)
			return nil
		}
		nri := &Redis{
			Shard:    shardId,
			Type:     role,
			Ip:       &MetaIp{FloatingIp: ip.FloatingIp, Ip: ip.Ip},
			Port:     port,
			Priority: priority,
			Cdn:      cdn,
		}
		return cerrs.ErrMetaFailed.Errorf("cannot update redis inst %s to %s", base_utils.Format(ri), base_utils.Format(nri))
	}
	if isEdge {
		if err := executeAndCheckIntCmds(ctx, m.process, []int64{1},
			"mriset",
			redisId,
			shardId,
			ip.FloatingIp+","+ip.Ip,
			port,
			vnetPort,
			role,
			priority,
			cdn); err != nil {
			return err
		}
	} else {
		if err := executeAndCheckIntCmds(ctx, m.process, []int64{1},
			"mriset",
			redisId,
			shardId,
			ip.FloatingIp+","+ip.Ip,
			port,
			role,
			priority,
			cdn); err != nil {
			return err
		}
	}
	return m.checksync(ctx)
}

func (m metaserverable) DeleteRedis(ctx context.Context, redisId int) error {
	if _, err := m.GetRedis(ctx, redisId); err != nil && IsNil(err) {
		logger.ComponentLogger.Trace(ctx, "proxy inst %d has been delete", redisId)
		return nil
	}
	if err := executeAndCheckIntCmds(ctx, m.process, []int64{1}, "mridel", redisId); err != nil {
		return err
	}
	return m.checksync(ctx)
}

func (m metaserverable) scsManualHandover(ctx context.Context, shardId int, redisId int, isGlobalMeta bool) error {
	if ri, err := m.GetRedis(ctx, redisId); err == nil {
		if ri.Type == RedisMaster {
			logger.ComponentLogger.Trace(ctx, "redis %d in shard %d already master", redisId, shardId)
			return nil
		}
	} else {
		return cerrs.ErrMetaFailed.Wrap(err)
	}
	if err := executeAndCheckStatusCmds(ctx, m.process, "msmasterchange", shardId, redisId); err != nil {
		return err
	}
	if err := m.checksync(ctx); err != nil {
		return err
	}
	if isGlobalMeta {
		return nil
	}
	// return checkAndSetSlaveOf(ctx, m, shardId)
	return nil
}

func (m metaserverable) ManualHandover(ctx context.Context, shardId int, redisId int) error {
	switch m.metaType {
	case TypeScs, TypeScsSlot:
		return m.scsManualHandover(ctx, shardId, redisId, false)
	default:
		return cerrs.ErrMetaFailed.Errorf("metaserver type %s not support manual handover", m.metaType)
	}
}

func (m metaserverable) ManualHandoverGlobal(ctx context.Context, shardId int, redisId int) error {
	switch m.metaType {
	case TypeScs, TypeScsSlot:
		return m.scsManualHandover(ctx, shardId, redisId, true)
	default:
		return cerrs.ErrMetaFailed.Errorf("metaserver type %s not support manual handover", m.metaType)
	}
}

func getIdc(src string) string {
	for _, pair := range config.IDCMap {
		if pair.OriginalIDC == src {
			return pair.TargetIDC
		}
	}
	return src
}

// McTypeSet 设置集群类型
// go版本metaserver扩展命令 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/CyUESrdXXe/PPwJnEY3Qq-2s-
func (m metaserverable) McTypeSet(ctx context.Context, clusterID int, clusterType string) error {
	switch clusterType {
	case "redis", "pegadb":
		if err := executeAndCheckStatusCmds(ctx, m.process, "mctypeset", clusterID, clusterType); err != nil {
			return err
		}
		if err := m.checksync(ctx); err != nil {
			return err
		}
	default:
		return cerrs.ErrMetaFailed.Errorf("cluster type %s not support mctypeset", m.metaType)
	}
	return nil
}

// 批量修改redis实例配置
func (m metaserverable) MriUpdatePipeline(ctx context.Context, params []*MriUpdateParam) error {
	if len(params) == 0 {
		return nil
	}
	pipe := m.getPipe()
	for _, param := range params {
		pipe.Do(ctx, "mriupdate", param.RedisID, param.Field, param.Value)
	}
	_, err := pipe.Exec(ctx)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "mriupdate pipeline error: %s", err.Error())
		return cerrs.ErrMetaFailed.Wrap(err)
	}
	return m.checksync(ctx)
}

// 批量修改proxy实例配置
func (m metaserverable) MpiUpdatePipeline(ctx context.Context, params []*MpiUpdateParam) error {
	if len(params) == 0 {
		return nil
	}
	pipe := m.getPipe()
	for _, param := range params {
		pipe.Do(ctx, "mpiupdate", param.ProxyInstID, param.Field, param.Value)
	}
	_, err := pipe.Exec(ctx)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "mpiupdate pipeline error: %s", err.Error())
		return cerrs.ErrMetaFailed.Wrap(err)
	}
	return m.checksync(ctx)
}
