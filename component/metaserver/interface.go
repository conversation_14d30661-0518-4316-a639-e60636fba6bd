/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
BCC创建配置
*/

package metaserver

import (
	"context"
	"time"

	goredis "github.com/go-redis/redis/v8"

	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
)

const (
	TypeScs     = "scs"
	TypeScsSlot = "scs_slot"
	TypeBDRP    = "bdrp"
	TypeData    = "data"
)

const (
	FlagValTrue  = "yes"
	FlagValFalse = "no"
)

const (
	FlagsMasterFailover        = "master_failover"
	FlagsMasterRead            = "master_read"
	FlagsSlaveRead             = "slave_read"
	FlagsForceMasterChange     = "force_master_change"
	FlagsGreyLineAutoFailover  = "grey_line_auto_failover"
	FlagsIncrementalHandover   = "incremental_handover"
	FlagsAdvancedFailover      = "advanced_failover"
	FlagsCrossIdcFailover      = "cross_idc_failover"
	FlagsForceAuth             = "force_auth"
	FlagsAdvancedSetPassword   = "advanced_set_password"
	FlagsForbidWrite           = "forbid_write"
	FlagsScsOnlySlaveCluster   = "slave_cluster"
	FlagsScsOnlyForbiddenWrite = "forbidden_write"
)

const (
	RedisMaster = 1
	RedisSlave  = 2
)

const (
	AutoBalance   = 1
	ManualBalance = 2
)

type Slots [][2]int

type MetaIp struct {
	FloatingIp string
	Ip         string
}

type ProxyInst struct {
	Proxy  int
	Ip     *MetaIp
	Status int
	Cdn    string
}

type Proxy struct {
	ClusterNum int
	Cluster    int
	InstNum    int
	ProxyInst  []int
}

type Cluster struct {
	Proxy    int
	Slot     map[int]Slots
	PoolPort int
	Flags    map[string]bool
	App      int
	ShardNum int
	Shards   []int
}

type ClusterDetail struct {
	Proxy    int
	Slot     []int
	PoolPort int
	Flags    map[string]bool
	App      int
	ShardNum int
	Shards   []int
}

type Shard struct {
	Cluster  int
	SeqID    int
	Master   int
	SlaveNum int
	Slaves   []int
}

type Redis struct {
	Shard    int
	Ip       *MetaIp
	Port     int
	Type     int
	Status   int
	Priority int
	Cdn      string
}

type ScaleStatus struct {
	ClusterID             int
	StartTime             time.Time
	EndTime               time.Time
	TotalMigrateSlots     int
	FinishedMigrateSlots  int
	CtsTotalJobCount      int
	CtsSuccessJobCount    int
	CtsProcessingJobCount int
	CtsProcessingJobs     []string
	ScaleProgress         string
	ScaleFinished         bool
	ErrorCode             int
}

type processFunc func(ctx context.Context, cmder goredis.Cmder) error

type checksyncFunc func(ctx context.Context) error

type metaserverable struct {
	process   processFunc
	checksync checksyncFunc
	metaType  string
	getPipe   func() goredis.Pipeliner
}

type MetaserverClient struct {
	metaserverable
	cli         *single_redis.SingleClient
	metaCluster *x1model.MetaCluster
}

type MriUpdateParam struct {
	RedisID int    `json:"redis_id"`
	Field   string `json:"field"`
	Value   string `json:"value"`
}

type MpiUpdateParam struct {
	ProxyInstID int    `json:"proxy_inst_id"`
	Field       string `json:"field"`
	Value       string `json:"value"`
}

type MetaserverAble interface {
	GetAppClusterId(ctx context.Context, appId int) (int, error)
	SetAppClusterId(ctx context.Context, appId int, clusterId int) error
	GetCluster(ctx context.Context, clusterId int) (*Cluster, error)
	SetCluster(ctx context.Context, clusterId int, proxyId int, port int, flags map[string]bool) error
	SetClusterFlag(ctx context.Context, clusterId int, flag string, val string) error
	SetClusterMaster(ctx context.Context, clusterId int, domain string, port int) error
	ShrinkCluster(ctx context.Context, clusterId int, shardCount int) error
	ShrinkClusterForSync(ctx context.Context, clusterId int, shardCount int, opHeaderFlag string) error
	ShrinkClusterHashTagForSync(ctx context.Context, clusterId int, shardCount int, hashTagFlag string, opHeaderFlag string) error
	ShrinkPegaHashTagForSync(ctx context.Context, clusterId int, shardCount int,
		hashTagFlag string, opHeaderFlag string) error
	GetClusterShrinkedShards(ctx context.Context, clusterId int) ([]int, error)
	SetClusterAutoBalance(ctx context.Context, clusterId int, flag int) error
	SetClusterAutoBalanceForSync(ctx context.Context, clusterId int, flag int, opHeaderFlag string) error
	SetClusterAutoBalanceHashTagForSync(ctx context.Context, clusterId int, flag int, hashTagFlag string, opHeaderFlag string) error
	SetPegaAutoBalanceHashTagForSync(ctx context.Context, clusterId int,
		flag int, hashTagFlag string, opHeaderFlag string) error
	IsClusterInAutoBalance(ctx context.Context, clusterId int) (bool, error)
	IsClusterLastAutoBalanceSuccess(ctx context.Context, clusterId int) (bool, error)
	DeleteCluster(ctx context.Context, clusterId int) error
	InitCluster(ctx context.Context, clusterId int) error
	ForceUpdateClusterSlotsDist(ctx context.Context, clusterId int, targetDist map[int]Slots) error
	GetShard(ctx context.Context, shardId int) (*Shard, error)
	SetShard(ctx context.Context, shardId int, clusterId int) error
	DeleteShard(ctx context.Context, shardId int) error
	GetProxy(ctx context.Context, proxyId int) (*Proxy, error)
	SetProxy(ctx context.Context, proxyId int) error
	DeleteProxy(ctx context.Context, proxyId int) error
	GetProxyInstance(ctx context.Context, proxyInstId int) (*ProxyInst, error)
	SetProxyInstance(ctx context.Context, proxyInstId int, proxyId int, ip *MetaIp, cdn string) error
	DeleteProxyInstance(ctx context.Context, proxyInstId int) error
	GetRedis(ctx context.Context, redisId int) (*Redis, error)
	SetRedis(ctx context.Context, redisId int, shardId int, ip *MetaIp, port int, role int, priority int, cdn string) error
	DeleteRedis(ctx context.Context, redisId int) error
	ManualHandover(ctx context.Context, shardId int, redisId int) error
	McTypeSet(ctx context.Context, clusterID int, clusterType string) error
	MriUpdatePipeline(ctx context.Context, params []*MriUpdateParam) error
	MpiUpdatePipeline(ctx context.Context, params []*MpiUpdateParam) error
}

var (
	_ MetaserverAble = (*MetaserverClient)(nil)
)

var (
	ScsFlagsSeq  = [...]string{FlagsMasterFailover, FlagsMasterRead, FlagsSlaveRead, FlagsScsOnlySlaveCluster, FlagsScsOnlyForbiddenWrite}
	BdrpFlagsSeq = [...]string{FlagsMasterFailover, FlagsMasterRead, FlagsSlaveRead, FlagsForceMasterChange, FlagsGreyLineAutoFailover, FlagsIncrementalHandover, FlagsAdvancedFailover, FlagsCrossIdcFailover, FlagsForceAuth, FlagsAdvancedSetPassword, FlagsForbidWrite}
)
