/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
BCC创建配置
*/

package metaserver

import (
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func parseFlags(rawFlag string, metaType string) (map[string]bool, error) {
	var seq []string
	ret := make(map[string]bool)
	switch metaType {
	case TypeScs, TypeScsSlot:
		seq = ScsFlagsSeq[:]
	case TypeBDRP:
		seq = BdrpFlagsSeq[:]
	default:
		return nil, cerrs.ErrMetaFailed.Errorf("invalid meta type %s", metaType)
	}
	intFlag := cast.ToInt(rawFlag)
	for i, seqv := range seq {
		if intFlag&(1<<i) > 0 {
			ret[seqv] = true
		} else {
			ret[seqv] = false
		}
	}
	return ret, nil
}

func parseSlot(raw string) (map[int]Slots, error) {
	ret := make(map[int]Slots)
	slots := strings.Split(raw, ",")
	if len(slots) != 16384 {
		return nil, cerrs.ErrMetaFailed.Errorf("invalid slot format %s", raw)
	}
	for slot, shard := range slots {
		shardId := cast.ToInt(shard)
		if shardId <= 0 {
			continue
		}
		if _, ok := ret[shardId]; !ok {
			ret[shardId] = Slots{{slot, slot}}
		} else {
			last := &ret[shardId][len(ret[shardId])-1]
			if slot == (*last)[1]+1 {
				(*last)[1] = slot
			} else {
				ret[shardId] = append(ret[shardId], [2]int{slot, slot})
			}
		}
	}
	return ret, nil
}

func unParseSlotToStringSlice(s map[int]Slots) ([]string, error) {
	ret := make([]string, 16384)
	for shard, slots := range s {
		for _, slotRange := range slots {
			for i := slotRange[0]; i <= slotRange[1]; i++ {
				if i < 0 || i >= 16384 {
					return nil, cerrs.ErrMetaFailed.Errorf("invalid slot dist %s", base_utils.Format(s))
				}
				ret[i] = strconv.Itoa(shard)
			}
		}
	}
	return ret, nil
}

func parseIdList(raw string) ([]int, error) {
	items := strings.Split(raw, ",")
	ret := make([]int, len(items))
	for i, item := range items {
		ret[i] = cast.ToInt(item)
	}
	return ret, nil
}

func parseMcget(raw []string, metaType string) (*Cluster, error) {
	var err error
	ret := &Cluster{}
	for i, item := range raw {
		if i%2 == 0 && i+1 < len(raw) {
			switch item {
			case "proxy":
				ret.Proxy = cast.ToInt(raw[i+1])
			case "slot":
				ret.Slot, err = parseSlot(raw[i+1])
				if err != nil {
					return nil, err
				}
			case "pool_port":
				ret.PoolPort = cast.ToInt(raw[i+1])
			case "flags":
				ret.Flags, err = parseFlags(raw[i+1], metaType)
				if err != nil {
					return nil, err
				}
			case "app":
				ret.App = cast.ToInt(raw[i+1])
			case "shard_num":
				ret.ShardNum = cast.ToInt(raw[i+1])
			case "shard":
				ret.Shards, err = parseIdList(raw[i+1])
				if err != nil {
					return nil, err
				}
			}
		}
	}
	return ret, nil
}

func parseMcgetDetail(raw []string, metaType string) (*ClusterDetail, error) {
	var err error
	ret := &ClusterDetail{}
	for i, item := range raw {
		if i%2 == 0 && i+1 < len(raw) {
			switch item {
			case "proxy":
				ret.Proxy = cast.ToInt(raw[i+1])
			case "slot":
				ret.Slot, err = parseIdList(raw[i+1])
				if err != nil {
					return nil, err
				}
			case "pool_port":
				ret.PoolPort = cast.ToInt(raw[i+1])
			case "flags":
				ret.Flags, err = parseFlags(raw[i+1], metaType)
				if err != nil {
					return nil, err
				}
			case "app":
				ret.App = cast.ToInt(raw[i+1])
			case "shard_num":
				ret.ShardNum = cast.ToInt(raw[i+1])
			case "shard":
				ret.Shards, err = parseIdList(raw[i+1])
				if err != nil {
					return nil, err
				}
			}
		}
	}
	return ret, nil
}

func parseMsget(raw []string, metaType string) (*Shard, error) {
	var err error
	ret := &Shard{}
	for i, item := range raw {
		if i%2 == 0 && i+1 < len(raw) {
			switch item {
			case "cluster":
				ret.Cluster = cast.ToInt(raw[i+1])
			case "seq_id":
				ret.SeqID = cast.ToInt(raw[i+1])
			case "master":
				ret.Master = cast.ToInt(raw[i+1])
			case "slave_num":
				ret.SlaveNum = cast.ToInt(raw[i+1])
			case "slaves":
				if raw[i+1] == "" {
					ret.Slaves = []int{}
				} else {
					ret.Slaves, err = parseIdList(raw[i+1])
					if err != nil {
						return nil, err
					}
				}
			}
		}
	}
	return ret, nil
}

func parseMpget(raw []string, metaType string) (*Proxy, error) {
	var err error
	ret := &Proxy{}
	for i, item := range raw {
		if i%2 == 0 && i+1 < len(raw) {
			switch item {
			case "cluster_num":
				ret.ClusterNum = cast.ToInt(raw[i+1])
			case "cluster":
				ret.Cluster = cast.ToInt(raw[i+1])
			case "inst_num":
				ret.InstNum = cast.ToInt(raw[i+1])
			case "proxy_inst":
				ret.ProxyInst, err = parseIdList(raw[i+1])
				if err != nil {
					return nil, err
				}
			}
		}
	}
	return ret, nil
}

func parseIp(raw string) (*MetaIp, error) {
	ips := strings.Split(raw, ",")
	switch len(ips) {
	case 1:
		return &MetaIp{ips[0], ips[0]}, nil
	case 2:
		return &MetaIp{ips[0], ips[1]}, nil
	default:
		return nil, cerrs.ErrMetaFailed.Errorf("invalid ip %s", raw)
	}
}

func parseMpiget(raw []string, metaType string) (*ProxyInst, error) {
	var err error
	ret := &ProxyInst{}
	for i, item := range raw {
		if i%2 == 0 && i+1 < len(raw) {
			switch item {
			case "proxy":
				ret.Proxy = cast.ToInt(raw[i+1])
			case "ip":
				ret.Ip, err = parseIp(raw[i+1])
				if err != nil {
					return nil, err
				}
			case "status":
				ret.Status = cast.ToInt(raw[i+1])
			case "cdn":
				ret.Cdn = raw[i+1]
			}
		}
	}
	return ret, nil
}

func parseMriget(raw []string, metaType string) (*Redis, error) {
	var err error
	ret := &Redis{}
	for i, item := range raw {
		if i%2 == 0 && i+1 < len(raw) {
			switch item {
			case "shard":
				ret.Shard = cast.ToInt(raw[i+1])
			case "ip":
				ret.Ip, err = parseIp(raw[i+1])
				if err != nil {
					return nil, err
				}
			case "port":
				ret.Port = cast.ToInt(raw[i+1])

			case "type":
				ret.Type = cast.ToInt(raw[i+1])
			case "status":
				ret.Status = cast.ToInt(raw[i+1])
			case "priority":
				ret.Status = cast.ToInt(raw[i+1])
			case "cdn":
				ret.Cdn = raw[i+1]
			}
		}
	}
	return ret, nil
}

func parseMcshrinkshard(raw []string, metaType string) ([]int, error) {
	if len(raw) < 2 {
		return nil, cerrs.ErrMetaFailed.Errorf("invalid mcshrinkshard reply %s", base_utils.Format(raw))
	}
	var ret []int
	for _, shardStr := range strings.Split(raw[1], ",") {
		shardId := cast.ToInt(shardStr)
		ret = append(ret, shardId)
	}
	return ret, nil
}

func parseMcscaleStatus(raw []string, metaType string) (*ScaleStatus, error) {
	ret := &ScaleStatus{}
	for _, rawLine := range raw {
		if !strings.Contains(rawLine, ":") {
			continue
		}
		chunks := strings.Split(rawLine, ":")
		if len(chunks) != 2 {
			continue
		}
		switch chunks[0] {
		case "cluster_id":
			ret.ClusterID = cast.ToInt(chunks[1])
		case "start_time":
			ret.StartTime = time.Unix(cast.ToInt64(chunks[1]), 0)
		case "end_time":
			ret.EndTime = time.Unix(cast.ToInt64(chunks[1]), 0)
		case "total_migrate_slot_num":
			ret.TotalMigrateSlots = cast.ToInt(chunks[1])
		case "finish_migrate_slot_num":
			ret.FinishedMigrateSlots = cast.ToInt(chunks[1])
		case "cts_total_do_job_num":
			ret.CtsTotalJobCount = cast.ToInt(chunks[1])
		case "cst_success_job_num":
			ret.CtsSuccessJobCount = cast.ToInt(chunks[1])
		case "cts_process_job_num":
			ret.CtsProcessingJobCount = cast.ToInt(chunks[1])
		case "cts_process_jobs":
			ret.CtsProcessingJobs = strings.Split(chunks[1], ",")
		case "scale_progress":
			ret.ScaleProgress = chunks[1] // 扩缩容进度为百分数表示方式,此处直接使用字符串表示
		case "scale_finish":
			ret.ScaleFinished = cast.ToInt(chunks[1]) == 1
		case "error_code":
			ret.ErrorCode = cast.ToInt(chunks[1])
		}
	}
	return ret, nil
}
