package crm

import (
	"context"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/crm"
)

type CrmResource interface {
	GetCustomerDetailInfo(ctx context.Context, userId string) (rsp *crm.CustomerDetailInfo, err error)
}

type crmResourceOp struct {
	crmSdk crm.CrmService
}

var crmResourceOpObj CrmResource

var once sync.Once

func CrmResourceOp() CrmResource {
	once.Do(func() {
		crmResourceOpObj = &crmResourceOp{
			crmSdk: crm.NewBefaultCrmSdk(),
		}

	})
	return crmResourceOpObj
}

func (br *crmResourceOp) GetCustomerDetailInfo(ctx context.Context, userId string) (rsp *crm.CustomerDetailInfo, err error) {
	if userId == "" {
		return &crm.CustomerDetailInfo{
			StsError:   false,
			IsInternal: false,
			UserType:   "",
			ErrorMsg:   "userId is empty",
		}, nil
	}

	// get auth
	auth, err := compo_utils.GetOpenapiAuth(ctx, userId)
	if err != nil {
		return &crm.CustomerDetailInfo{
			StsError:   true,
			IsInternal: false,
			UserType:   "",
			ErrorMsg:   err.Error(),
		}, nil
	}

	response, err := br.crmSdk.GetCustomerDetailInfo(ctx, &crm.GetCustomerDetailReq{
		UserId: userId,
		Auth:   auth,
	})

	if response == nil {
		return &crm.CustomerDetailInfo{
			StsError:   false,
			IsInternal: false,
			UserType:   "",
			ErrorMsg:   err.Error(),
		}, nil
	}

	// 内部客户tag list
	var internalCustomerTagList = []string{"内部计费", "内部测试人员", "服务资源账号", "内部服务账号"}
	for _, customerTag := range response.TagList.TagResponses {
		for _, internalCustomerTag := range internalCustomerTagList {
			if customerTag.TagName == internalCustomerTag {
				return &crm.CustomerDetailInfo{
					StsError:   false,
					IsInternal: true,
					UserType:   response.Customer.UserType,
					ErrorMsg:   "",
				}, nil
			}
		}
	}

	return &crm.CustomerDetailInfo{
		StsError:   false,
		IsInternal: false,
		UserType:   response.Customer.UserType,
		ErrorMsg:   "customer tag is not internal",
	}, nil
}
