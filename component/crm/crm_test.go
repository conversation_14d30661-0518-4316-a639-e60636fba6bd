package crm

import (
	"context"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func TestGetCustomerDetailInfo(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	crmOp := CrmResourceOp()

	rsp, err := crmOp.GetCustomerDetailInfo(ctx, "45492ef3f0a74cda8bdb5660c9dc2d0a")

	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("GetCustomerDetailInfo resp:", base_utils.Format(rsp))
}
