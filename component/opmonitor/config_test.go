package opmonitor

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"testing"
)

func TestLoadConf(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	cf1 := &config{}
	if err := compo_utils.LoadConf("opmonitor", cf1); err != nil {
		t.Fatalf("load conf fail, err: %s", err.<PERSON>rror())
	}

	fmt.Printf("%+v", *cf1)

}
