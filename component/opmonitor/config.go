package opmonitor

type config struct {
	Path            string `toml:"Path"`
	Token           string `toml:"Token"`
	Endpoint        string `toml:"Endpoint"`
	InterfaceConf   InterfaceConf
	BackendConfList []BackendConf
	BnsInstanceConf BnsInstanceConf
}

type InterfaceConf struct {
	NameSuffix string
	GroupName  string
}

type BackendConf struct {
	Engine     string
	NameSuffix string
	GroupName  string
}

type BnsInstanceConf struct {
	RunUser              string
	HealthCheckType      string
	HealthCheckCmd       string
	Disable              int
	Status               int
	DeployPathParentPath string
	Port                 map[string]string
	Tag                  map[string]string
}
