package opmonitor

type ClusterBnsParams struct {
	AppType   string
	ClusterID int64
	Engine    string
}

type NodeInstanceInfo struct {
	ShortID    int
	FloatingIP string
	Engine     string
}

type NodeInstanceBnsParams struct {
	ClusterID     int64
	NodeInstances []*NodeInstanceInfo
}

type ProxyInstanceInfo struct {
	ShortID    int
	FloatingIP string
}

type ProxyInstanceBnsParams struct {
	ClusterID      int64
	ProxyInstances []*ProxyInstanceInfo
}
