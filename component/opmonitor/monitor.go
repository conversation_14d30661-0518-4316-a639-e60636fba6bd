package opmonitor

import (
	"context"
	"errors"
	"sync"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bns"
)

type component struct {
	bnsSdk   bns.BnsService
	conf     *config
	initOnce sync.Once
}

var defaultComponent = &component{
	conf: &config{},
}

func Instance() *component {
	defaultComponent.initOnce.Do(func() {
		// load default conf
		if err := compo_utils.LoadConf("opmonitor", defaultComponent.conf); err != nil {
			panic(err.Error())
		}

		// default component sdk
		defaultComponent.bnsSdk = bns.NewBefaultBnsSdk()
	})

	return defaultComponent
}

func (c *component) createBns(ctx context.Context, name string, group string) error {
	reqCreate := &bns.BnsCreateRequest{
		ParentPath: c.conf.Path,
		AuthKey:    c.conf.Token,
		NodeName:   name,
		RunUser:    c.conf.BnsInstanceConf.RunUser,
	}
	if _, err := c.bnsSdk.BnsServiceCreate(ctx, reqCreate); err != nil && !cerrs.Is(err, cerrs.ErrBNSRequestAlreadyExist) {
		logger.ComponentLogger.Warning(ctx, "bns service create fail",
			logit.Error("err", err), logit.String("service", name))
		return err
	}
	// 往group里添加bns，在bns侧是幂等操作，已经添加的可以重复添加，返回结果仍然是成功
	if _, err := c.bnsSdk.BnsGroupModifyServices(ctx, &bns.BnsGroupModifyServicesRequest{
		GroupName:    group,
		AuthKey:      c.conf.Token,
		ServiceNames: []string{name},
		Action:       "add",
	}); err != nil {
		logger.ComponentLogger.Warning(ctx, "bns service add to group fail",
			logit.Error("err", err), logit.String("service", name), logit.String("group", group))
		return err
	}

	return nil
}

func (c *component) CreateClusterBns(ctx context.Context, req *ClusterBnsParams) error {

	if req.AppType == x1model.AppTypeCluster {
		if err := c.createBns(ctx, c.GenerateInterfaceBnsServiceName(req.ClusterID), c.conf.InterfaceConf.GroupName); err != nil {
			return err
		}
	}
	backendName, err := c.GenerateBackendBnsServiceName(req.ClusterID, req.Engine)
	if err != nil {
		return err
	}
	group, err := c.GetBackendGroupName(req.Engine)
	if err != nil {
		return err
	}
	if err := c.createBns(ctx, backendName, group); err != nil {
		return err
	}
	return nil
}

func (c *component) clearBns(ctx context.Context, name string) error {

	// clear是幂等操作
	if _, err := c.bnsSdk.BnsServiceClearInstance(ctx, &bns.BnsClearInstanceRequest{
		AuthKey:     c.conf.Token,
		ServiceName: name,
	}); err != nil {
		logger.ComponentLogger.Warning(ctx, "clear bns service fail",
			logit.Error("err", err), logit.String("service", name))
		return err
	}

	return nil
}

func (c *component) deleteBns(ctx context.Context, name string) error {

	//删除bns会自动从group里面清理掉
	reqDelete := &bns.BnsDeleteRequest{
		AuthKey:     c.conf.Token,
		ServiceName: name,
	}
	if _, err := c.bnsSdk.BnsServiceDelete(ctx, reqDelete); err != nil && !cerrs.Is(err, cerrs.ErrBNSRequestNotFound) {
		logger.ComponentLogger.Warning(ctx, "bns service delete fail",
			logit.Error("err", err), logit.String("service", name))
		return err
	}

	return nil
}

func (c *component) checkBns(ctx context.Context, name string) error {

	_, err := c.bnsSdk.BnsServiceInstanceInfo(ctx, &bns.BnsServiceInstanceInfoRequest{
		ServiceName: name,
		AuthKey:     c.conf.Token,
	})
	return err
}

func (c *component) DeleteClusterBns(ctx context.Context, req *ClusterBnsParams) error {

	if req.AppType == x1model.AppTypeCluster {
		err := c.checkBns(ctx, c.GenerateInterfaceBnsServiceName(req.ClusterID))
		if err != nil && !cerrs.Is(err, cerrs.ErrBNSRequestNotFound) {
			logger.ComponentLogger.Warning(ctx, "bns service instance info fail",
				logit.Error("err", err), logit.String("service", c.GenerateInterfaceBnsServiceName(req.ClusterID)))
			return err
		}
		if err == nil {
			if err := c.clearBns(ctx, c.GenerateInterfaceBnsServiceName(req.ClusterID)); err != nil {
				return err
			}
			if err := c.deleteBns(ctx, c.GenerateInterfaceBnsServiceName(req.ClusterID)); err != nil {
				return err
			}
		}
	}
	backendName, err := c.GenerateBackendBnsServiceName(req.ClusterID, req.Engine)
	if err != nil {
		return err
	}
	err = c.checkBns(ctx, backendName)
	if err != nil && !cerrs.Is(err, cerrs.ErrBNSRequestNotFound) {
		logger.ComponentLogger.Warning(ctx, "bns service instance info fail",
			logit.Error("err", err), logit.String("service", c.GenerateInterfaceBnsServiceName(req.ClusterID)))
		return err
	}
	if err == nil {
		if err := c.clearBns(ctx, backendName); err != nil {
			return err
		}
		if err := c.deleteBns(ctx, backendName); err != nil {
			return err
		}
	}
	return nil
}

func (c *component) CreateNodeInstanceBns(ctx context.Context, req *NodeInstanceBnsParams) error {

	if len(req.NodeInstances) == 0 {
		return errors.New("nodes need at least one instance")
	}
	serviceName, err := c.GenerateBackendBnsServiceName(req.ClusterID, req.NodeInstances[0].Engine)
	if err != nil {
		return err
	}
	getReq := &bns.BnsServiceInstanceInfoRequest{
		ServiceName: serviceName,
		AuthKey:     c.conf.Token,
	}
	onlineInstsRsp, err := c.bnsSdk.BnsServiceInstanceInfo(ctx, getReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get instance info fail",
			logit.Error("err", err), logit.String("service", serviceName))
		return err
	}

	toCreateNodeInsts := make([]*NodeInstanceInfo, 0)
	var skip bool
	for _, nodeInst := range req.NodeInstances {
		skip = false
		for _, onlineInst := range onlineInstsRsp.InstanceInfo {
			if onlineInst.Offset == cast.ToString(nodeInst.ShortID) {
				skip = true
				break
			}
		}
		if !skip {
			toCreateNodeInsts = append(toCreateNodeInsts, nodeInst)
		}
	}

	//因为noah批量添加实例接口不支持hostname为IP,因此遍历添加
	for _, nodeInst := range toCreateNodeInsts {
		createReq := &bns.BnsAddInstanceRequest{
			ServiceName: serviceName,
			AuthKey:     c.conf.Token,
			InstanceInfo: &bns.BnsInstanceInfo{
				HostName:        nodeInst.FloatingIP,
				Port:            c.conf.BnsInstanceConf.Port,
				Tag:             c.conf.BnsInstanceConf.Tag,
				Disable:         c.conf.BnsInstanceConf.Disable,
				InstanceId:      nodeInst.ShortID,
				Status:          c.conf.BnsInstanceConf.Status,
				DeployPath:      c.GenerateDeployPath(nodeInst.ShortID),
				RunUser:         c.conf.BnsInstanceConf.RunUser,
				HealthCheckCmd:  c.conf.BnsInstanceConf.HealthCheckCmd,
				HealthCheckType: c.conf.BnsInstanceConf.HealthCheckType,
				ContainerId:     "",
			},
		}
		_, err = c.bnsSdk.BnsServiceAddInstance(ctx, createReq)
		if err != nil {
			if cerrs.Is(err, cerrs.ErrBNSRequestAlreadyExist) {
				continue
			}
			logger.ComponentLogger.Warning(ctx, "add instance fail", logit.Error("err", err))
			return err
		}
	}

	return nil
}

func (c *component) DeleteNodeInstanceBns(ctx context.Context, req *NodeInstanceBnsParams) error {

	if len(req.NodeInstances) == 0 {
		return errors.New("nodes need at least one instance")
	}
	serviceName, err := c.GenerateBackendBnsServiceName(req.ClusterID, req.NodeInstances[0].Engine)
	if err != nil {
		return err
	}

	for _, instance := range req.NodeInstances {
		deleteReq := &bns.BnsDeleteInstanceRequest{
			ServiceName: serviceName,
			AuthKey:     c.conf.Token,
			HostName:    instance.FloatingIP,
			InstanceId:  instance.ShortID,
		}
		// 删除实例在bns侧是幂等操作,删除成功的仍可以删除,返回成功
		_, err = c.bnsSdk.BnsServiceDeleteInstance(ctx, deleteReq)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "delete instance fail", logit.Error("err", err))
			return err
		}
	}
	return nil
}

func (c *component) CreateProxyInstanceBns(ctx context.Context, req *ProxyInstanceBnsParams) error {

	if len(req.ProxyInstances) == 0 {
		return errors.New("nodes need at least one instance")
	}
	serviceName := c.GenerateInterfaceBnsServiceName(req.ClusterID)
	getReq := &bns.BnsServiceInstanceInfoRequest{
		ServiceName: serviceName,
		AuthKey:     c.conf.Token,
	}
	onlineInstsRsp, err := c.bnsSdk.BnsServiceInstanceInfo(ctx, getReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get instance info fail",
			logit.Error("err", err), logit.String("service", serviceName))
		return err
	}

	toCreateProxyInsts := make([]*ProxyInstanceInfo, 0)
	var skip bool
	for _, proxyInst := range req.ProxyInstances {
		skip = false
		for _, onlineInst := range onlineInstsRsp.InstanceInfo {
			if onlineInst.Offset == cast.ToString(proxyInst.ShortID) {
				skip = true
				break
			}
		}
		if !skip {
			toCreateProxyInsts = append(toCreateProxyInsts, proxyInst)
		}
	}

	//因为noah批量添加实例接口不支持hostname为IP,因此遍历添加
	for _, proxyInst := range toCreateProxyInsts {
		createReq := &bns.BnsAddInstanceRequest{
			ServiceName: serviceName,
			AuthKey:     c.conf.Token,
			InstanceInfo: &bns.BnsInstanceInfo{
				HostName:        proxyInst.FloatingIP,
				Port:            c.conf.BnsInstanceConf.Port,
				Tag:             c.conf.BnsInstanceConf.Tag,
				Disable:         c.conf.BnsInstanceConf.Disable,
				InstanceId:      proxyInst.ShortID,
				Status:          c.conf.BnsInstanceConf.Status,
				DeployPath:      c.GenerateDeployPath(proxyInst.ShortID),
				RunUser:         c.conf.BnsInstanceConf.RunUser,
				HealthCheckCmd:  c.conf.BnsInstanceConf.HealthCheckCmd,
				HealthCheckType: c.conf.BnsInstanceConf.HealthCheckType,
				ContainerId:     "",
			},
		}
		_, err = c.bnsSdk.BnsServiceAddInstance(ctx, createReq)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "add instance fail", logit.Error("err", err))
			if cerrs.Is(err, cerrs.ErrBNSRequestAlreadyExist) {
				continue
			}
			return err
		}
	}

	return nil
}

func (c *component) DeleteProxyInstanceBns(ctx context.Context, req *ProxyInstanceBnsParams) error {

	if len(req.ProxyInstances) == 0 {
		return errors.New("nodes need at least one instance")
	}

	for _, instance := range req.ProxyInstances {
		deleteReq := &bns.BnsDeleteInstanceRequest{
			ServiceName: c.GenerateInterfaceBnsServiceName(req.ClusterID),
			AuthKey:     c.conf.Token,
			HostName:    instance.FloatingIP,
			InstanceId:  instance.ShortID,
		}
		// 删除实例在bns侧是幂等操作,删除成功的仍可以删除,返回成功
		_, err := c.bnsSdk.BnsServiceDeleteInstance(ctx, deleteReq)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "delete instance fail", logit.Error("err", err))
			return err
		}
	}
	return nil
}

func (c *component) NodeInstanceBns(ctx context.Context, req *NodeInstanceBnsParams) (bns []string, err error) {

	bns = make([]string, len(req.NodeInstances))
	for idx, inst := range req.NodeInstances {
		bns[idx], err = c.GenerateBackendBnsInstanceName(req.ClusterID, inst.ShortID, inst.Engine)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "generate bns name error", logit.Error("err", err))
			return
		}
	}
	return
}

func (c *component) ProxyInstanceBns(req *ProxyInstanceBnsParams) ([]string, error) {

	bnses := make([]string, len(req.ProxyInstances))
	for idx, inst := range req.ProxyInstances {
		bnses[idx] = c.GenerateInterfaceBnsInstanceName(req.ClusterID, inst.ShortID)
	}
	return bnses, nil
}

func (c *component) GenerateInterfaceBnsServiceName(clusterID int64) string {
	return "cluster-" + cast.ToString(clusterID) + "-" + c.conf.InterfaceConf.NameSuffix
}

func (c *component) GenerateInterfaceBnsInstanceName(clusterID int64, instanceID int) string {
	return cast.ToString(instanceID) + "." + c.GenerateInterfaceBnsServiceName(clusterID)
}

func (c *component) GenerateBackendBnsServiceName(clusterID int64, engine string) (string, error) {

	suffix := ""
	for _, backendConf := range c.conf.BackendConfList {
		if backendConf.Engine == engine {
			suffix = backendConf.NameSuffix
			break
		}
	}
	if suffix == "" {
		return "", errors.New("invalid backend")
	}
	return "cluster-" + cast.ToString(clusterID) + "-" + suffix, nil
}

func (c *component) GenerateBackendBnsInstanceName(clusterID int64, instanceID int, engine string) (string, error) {

	serviceName, err := c.GenerateBackendBnsServiceName(clusterID, engine)
	if err != nil {
		return "", err
	}
	return cast.ToString(instanceID) + "." + serviceName, nil
}

func (c *component) GetBackendGroupName(engine string) (string, error) {

	group := ""
	for _, backendConf := range c.conf.BackendConfList {
		if backendConf.Engine == engine {
			group = backendConf.GroupName
			break
		}
	}
	if group == "" {
		return "", errors.New("invalid backend")
	}
	return group, nil
}

func (c *component) GenerateDeployPath(instanceID int) string {

	return c.conf.BnsInstanceConf.DeployPathParentPath + cast.ToString(instanceID) + "/"
}

func (c *component) GetEndPoint() string {

	return c.conf.Endpoint
}

func (c *component) CreateInterfaceBnsServices(ctx context.Context, req *ClusterBnsParams) error {
	if req.AppType == x1model.AppTypeCluster {
		if err := c.createBns(ctx, c.GenerateInterfaceBnsServiceName(req.ClusterID), c.conf.InterfaceConf.GroupName); err != nil {
			return err
		}
	}

	return nil
}

func (c *component) CreateNodeBnsServices(ctx context.Context, req *ClusterBnsParams) error {
	backendName, err := c.GenerateBackendBnsServiceName(req.ClusterID, req.Engine)
	if err != nil {
		return err
	}
	group, err := c.GetBackendGroupName(req.Engine)
	if err != nil {
		return err
	}
	if err := c.createBns(ctx, backendName, group); err != nil {
		return err
	}
	return nil
}

func (c *component) DeleteInterfaceBnsServices(ctx context.Context, req *ClusterBnsParams) error {
	if req.AppType == x1model.AppTypeCluster {
		err := c.checkBns(ctx, c.GenerateInterfaceBnsServiceName(req.ClusterID))
		if err != nil && !cerrs.Is(err, cerrs.ErrBNSRequestNotFound) {
			logger.ComponentLogger.Warning(ctx, "bns service instance info fail",
				logit.Error("err", err), logit.String("service", c.GenerateInterfaceBnsServiceName(req.ClusterID)))
			return err
		}
		if err == nil {
			if err := c.clearBns(ctx, c.GenerateInterfaceBnsServiceName(req.ClusterID)); err != nil {
				return err
			}
			if err := c.deleteBns(ctx, c.GenerateInterfaceBnsServiceName(req.ClusterID)); err != nil {
				return err
			}
		}
	}

	return nil
}

func (c *component) DeleteNodeBnsServices(ctx context.Context, req *ClusterBnsParams) error {
	backendName, err := c.GenerateBackendBnsServiceName(req.ClusterID, req.Engine)
	if err != nil {
		return err
	}
	err = c.checkBns(ctx, backendName)
	if err != nil && !cerrs.Is(err, cerrs.ErrBNSRequestNotFound) {
		logger.ComponentLogger.Warning(ctx, "bns service instance info fail",
			logit.Error("err", err), logit.String("service", c.GenerateInterfaceBnsServiceName(req.ClusterID)))
		return err
	}
	if err == nil {
		if err := c.clearBns(ctx, backendName); err != nil {
			return err
		}
		if err := c.deleteBns(ctx, backendName); err != nil {
			return err
		}
	}
	return nil
}
