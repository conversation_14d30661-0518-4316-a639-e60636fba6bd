package opmonitor

import (
	"context"
	"errors"
	"fmt"
	. "github.com/agiledragon/gomonkey/v2"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bns"
	"reflect"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

func Test_opMonitorResourceOp_deleteBns(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()
	//测试删除失败
	patch1 := ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceDelete",
		func(ctx context.Context, req *bns.BnsDeleteRequest) (rsp *bns.BnsGeneralResponse, err error) {
			return nil, errors.New("abc")
		})
	err := op.deleteBns(ctx, "name")
	if err == nil {
		t.Errorf("[%s] test delete bns error fail: %s\n", t.Name(), err.Error())
	}
	patch1.Reset()
	//测试删除成功1
	patch1 = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceDelete",
		func(ctx context.Context, req *bns.BnsDeleteRequest) (rsp *bns.BnsGeneralResponse, err error) {
			return nil, nil
		})
	err = op.deleteBns(ctx, "name")
	if err != nil {
		t.Errorf("[%s] test delete bns error fail: %s\n", t.Name(), err.Error())
	}
	patch1.Reset()
	//测试删除成功1
	patch1 = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceDelete",
		func(ctx context.Context, req *bns.BnsDeleteRequest) (rsp *bns.BnsGeneralResponse, err error) {
			return nil, cerrs.ErrBNSRequestNotFound.Errorf("abc")
		})
	err = op.deleteBns(ctx, "name")
	if err != nil {
		t.Errorf("[%s] test delete bns error fail: %s\n", t.Name(), err.Error())
	}
	patch1.Reset()
}

//func Test_opMonitorResourceOp_deleteBns_offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	op := Instance()
//	//err := op.deleteBns(ctx, "cluster-200000009-redissandbox.BCE.all", op.conf.BackendConfList[0].GroupName)
//	err := op.deleteBns(ctx, "cluster-200000009-redissandbox.BCE.all", "abc")
//
//	if err != nil {
//		t.Errorf("[%s] test delete bns fail: %s\n", t.Name(), err.Error())
//	}
//}

func Test_opMonitorResourceOp_createBns(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()
	//测试添加失败
	patch := ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsGroupModifyServices",
		func(ctx context.Context, req *bns.BnsGroupModifyServicesRequest) (rsp *bns.BnsGeneralResponse2, err error) {
			return nil, nil
		})
	patch1 := ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceCreate",
		func(ctx context.Context, req *bns.BnsCreateRequest) (rsp *bns.BnsGeneralResponse, err error) {
			return nil, errors.New("abc")
		})
	err := op.createBns(ctx, "name", "group")
	if err == nil {
		t.Errorf("[%s] test create bns error fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patch1.Reset()
	//创建删除成功1
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsGroupModifyServices",
		func(ctx context.Context, req *bns.BnsGroupModifyServicesRequest) (rsp *bns.BnsGeneralResponse2, err error) {
			return nil, nil
		})
	patch1 = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceCreate",
		func(ctx context.Context, req *bns.BnsCreateRequest) (rsp *bns.BnsGeneralResponse, err error) {
			return nil, nil
		})
	err = op.createBns(ctx, "name", "group")
	if err != nil {
		t.Errorf("[%s] test create bns error fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patch1.Reset()
	//测试创建成功2
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsGroupModifyServices",
		func(ctx context.Context, req *bns.BnsGroupModifyServicesRequest) (rsp *bns.BnsGeneralResponse2, err error) {
			return nil, nil
		})
	patch1 = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceCreate",
		func(ctx context.Context, req *bns.BnsCreateRequest) (rsp *bns.BnsGeneralResponse, err error) {
			return nil, cerrs.ErrBNSRequestAlreadyExist.Errorf("abc")
		})
	err = op.createBns(ctx, "name", "group")
	if err != nil {
		t.Errorf("[%s] test create bns error fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patch1.Reset()
	//测试往group里面添加bns失败
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsGroupModifyServices",
		func(ctx context.Context, req *bns.BnsGroupModifyServicesRequest) (rsp *bns.BnsGeneralResponse2, err error) {
			return nil, errors.New("abc")
		})
	patch1 = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceCreate",
		func(ctx context.Context, req *bns.BnsCreateRequest) (rsp *bns.BnsGeneralResponse, err error) {
			return nil, nil
		})
	err = op.createBns(ctx, "name", "group")
	if err == nil {
		t.Errorf("[%s] test delete bns error fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patch1.Reset()
}

//func Test_opMonitorResourceOp_createBns_offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	op := Instance()
//	err := op.createBns(ctx, "cluster-200000010-redissandbox.BCE.all", op.conf.BackendConfList[0].GroupName)
//	if err != nil {
//		t.Errorf("[%s] test create bns fail: %s\n", t.Name(), err.Error())
//	}
//
//}

func Test_component_GenerateDeployPath(t *testing.T) {

	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()
	if op.GenerateDeployPath(123) != "/root/monitor/123/" {
		t.Errorf("[%s] test generate deploy path error\n", t.Name())
	}
}

func Test_component_GenerateBackendBnsInstanceName(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()
	if _, err := op.GenerateBackendBnsInstanceName(999, 123, "unknow"); err == nil {
		t.Errorf("[%s] test generate backend bns instance name error\n", t.Name())
	}
	n, err := op.GenerateBackendBnsInstanceName(999, 123, "redis")
	if err != nil {
		t.Errorf("[%s] test generate backend bns instance name error\n", t.Name())
	}
	if n != "123.cluster-999-redissandbox.BCE.all" {
		t.Errorf(" test generate backend bns instance name error\n")
	}
	n, err = op.GenerateBackendBnsInstanceName(999, 123, "pegadb")
	if err != nil {
		t.Errorf("[%s] test generate backend bns instance name error\n", t.Name())
	}
	if n != "123.cluster-999-redissandbox.BCE.all" {
		t.Errorf(" test generate backend bns instance name error\n")
	}
}

func Test_component_GetBackendGroupName(t *testing.T) {

	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()
	if _, err := op.GetBackendGroupName("unknow"); err == nil {
		t.Errorf("[%s] test get backend group name error\n", t.Name())
	}
	n, err := op.GetBackendGroupName("redis")
	if err != nil {
		t.Errorf("[%s] test get backend group name error\n", t.Name())
	}
	if n != "group.monitorsandbox.bce.all" {
		t.Errorf("[%s] test get backend group name error\n", t.Name())
	}
	n, err = op.GetBackendGroupName("pegadb")
	if err != nil {
		t.Errorf("[%s] test get backend group name error\n", t.Name())
	}
	if n != "group.monitorsandbox.bce.all" {
		t.Errorf("[%s] test get backend group name error\n", t.Name())
	}
}

func Test_component_GenerateInterfaceBnsInstanceName(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()
	if op.GenerateInterfaceBnsInstanceName(999, 123) != "123.cluster-999-proxysandbox.BCE.all" {
		t.Errorf("[%s] test generate interface bns instance name error\n", t.Name())
	}
}

func Test_component_ProxyInstanceBns(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()
	type args struct {
		ctx context.Context
		req *ProxyInstanceBnsParams
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		{"test generate interface bns instance name null parameters",
			args{ctx: ctx, req: &ProxyInstanceBnsParams{}}, []string{}, false},
		{"test generate interface bns instance name null parameters",
			args{ctx: ctx, req: &ProxyInstanceBnsParams{
				ClusterID:      999,
				ProxyInstances: []*ProxyInstanceInfo{},
			}}, []string{}, false},
		{"test generate interface bns instance name null parameters",
			args{ctx: ctx, req: &ProxyInstanceBnsParams{
				ClusterID: 999,
				ProxyInstances: []*ProxyInstanceInfo{
					{
						ShortID:    123,
						FloatingIP: "ip",
					},
					{
						ShortID:    456,
						FloatingIP: "ip",
					},
				},
			}},
			[]string{"123.cluster-999-proxysandbox.BCE.all", "456.cluster-999-proxysandbox.BCE.all"},
			false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			got, err := op.ProxyInstanceBns(tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProxyInstanceBns() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProxyInstanceBns() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_component_NodeInstanceBns(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()
	type args struct {
		ctx context.Context
		req *NodeInstanceBnsParams
	}
	tests := []struct {
		name    string
		args    args
		wantBns []string
		wantErr bool
	}{
		{"test generate node bns instance name null parameters", args{ctx: ctx, req: &NodeInstanceBnsParams{}},
			[]string{}, false},
		{"test generate node bns instance name null parameters", args{ctx: ctx, req: &NodeInstanceBnsParams{
			ClusterID:     999,
			NodeInstances: []*NodeInstanceInfo{},
		}}, []string{}, false},
		{"test generate node bns instance name n", args{ctx: ctx, req: &NodeInstanceBnsParams{
			ClusterID: 999,
			NodeInstances: []*NodeInstanceInfo{
				{
					ShortID:    123,
					FloatingIP: "ip",
				},
				{
					ShortID:    456,
					FloatingIP: "ip",
				},
			},
		}}, make([]string, 2), true},
		{"test generate node bns instance name redis parameters", args{ctx: ctx, req: &NodeInstanceBnsParams{
			ClusterID: 999,
			NodeInstances: []*NodeInstanceInfo{
				{
					ShortID:    123,
					FloatingIP: "ip",
					Engine:     "redis",
				},
				{
					ShortID:    456,
					FloatingIP: "ip",
					Engine:     "redis",
				},
			},
		}},
			[]string{"123.cluster-999-redissandbox.BCE.all", "456.cluster-999-redissandbox.BCE.all"},
			false},
		{"test generate node bns instance name pegadb parameters", args{ctx: ctx, req: &NodeInstanceBnsParams{
			ClusterID: 999,
			NodeInstances: []*NodeInstanceInfo{
				{
					ShortID:    123,
					FloatingIP: "ip",
					Engine:     "pegadb",
				},
				{
					ShortID:    456,
					FloatingIP: "ip",
					Engine:     "pegadb",
				},
			},
		}},
			[]string{"123.cluster-999-redissandbox.BCE.all", "456.cluster-999-redissandbox.BCE.all"},
			false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotBns, err := op.NodeInstanceBns(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("NodeInstanceBns() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotBns, tt.wantBns) {
				t.Errorf("NodeInstanceBns() gotBns = %v, want %v", gotBns, tt.wantBns)
			}
		})
	}
}

func Test_component_DeleteProxyInstanceBns(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()

	type args struct {
		ctx context.Context
		req *ProxyInstanceBnsParams
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{"test generate proxy instance bns instance name null parameters",
			args{ctx: ctx, req: &ProxyInstanceBnsParams{}}, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := op.DeleteProxyInstanceBns(tt.args.ctx, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("DeleteProxyInstanceBns() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	patch := ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceDeleteInstance",
		func(ctx context.Context, req *bns.BnsDeleteInstanceRequest) (rsp *bns.BnsGeneralResponse, err error) {
			return nil, nil
		})
	if op.DeleteProxyInstanceBns(ctx, &ProxyInstanceBnsParams{
		ClusterID: 999,
		ProxyInstances: []*ProxyInstanceInfo{
			{100, "1"},
		},
	}) != nil {
		t.Errorf("DeleteProxyInstanceBns() return not match")
	}
	patch.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceDeleteInstance",
		func(ctx context.Context, req *bns.BnsDeleteInstanceRequest) (rsp *bns.BnsGeneralResponse, err error) {
			return nil, errors.New("test")
		})
	if op.DeleteProxyInstanceBns(ctx, &ProxyInstanceBnsParams{
		ClusterID: 999,
		ProxyInstances: []*ProxyInstanceInfo{
			{100, "1"},
		},
	}) == nil {
		t.Errorf("DeleteProxyInstanceBns() return not match")
	}
	patch.Reset()
}

//func Test_component_DeleteProxyInstanceBns_offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	op := Instance()
//	if op.DeleteProxyInstanceBns(ctx, &ProxyInstanceBnsParams{
//		ClusterID: 20000009611,
//		ProxyInstances: []*ProxyInstanceInfo{
//			{100, "100.88.111.85"},
//		},
//	}) != nil {
//		t.Errorf("DeleteNodeInstanceBns() return not match")
//	}
//}

func Test_component_DeleteNodeInstanceBns(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()

	type args struct {
		ctx context.Context
		req *NodeInstanceBnsParams
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{"test delete node instance bns null parameters",
			args{ctx: ctx, req: &NodeInstanceBnsParams{}}, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := op.DeleteNodeInstanceBns(tt.args.ctx, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("DeleteNodeInstanceBns() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	patch := ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceDeleteInstance",
		func(ctx context.Context, req *bns.BnsDeleteInstanceRequest) (rsp *bns.BnsGeneralResponse, err error) {
			return nil, nil
		})
	if op.DeleteNodeInstanceBns(ctx, &NodeInstanceBnsParams{
		ClusterID: 999,
		NodeInstances: []*NodeInstanceInfo{
			{100, "1", "redis"},
		},
	}) != nil {
		t.Errorf("DeleteNodeInstanceBns() return not match")
	}
	patch.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceDeleteInstance",
		func(ctx context.Context, req *bns.BnsDeleteInstanceRequest) (rsp *bns.BnsGeneralResponse, err error) {
			return nil, errors.New("test")
		})
	if op.DeleteNodeInstanceBns(ctx, &NodeInstanceBnsParams{
		ClusterID: 999,
		NodeInstances: []*NodeInstanceInfo{
			{100, "1", "redis"},
		},
	}) == nil {
		t.Errorf("DeleteProxyInstanceBns() return not match")
	}
	patch.Reset()
}

//func Test_component_DeleteNodeInstanceBns_offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	op := Instance()
//	if op.DeleteNodeInstanceBns(ctx, &NodeInstanceBnsParams{
//		ClusterID: 200000009,
//		NodeInstances: []*NodeInstanceInfo{
//			{104, "100.88.111.85", "pegadb"},
//		},
//	}) != nil {
//		t.Errorf("DeleteNodeInstanceBns() return not match")
//	}
//}

func Test_component_CreateProxyInstanceBns(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()

	type args struct {
		ctx context.Context
		req *ProxyInstanceBnsParams
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{"test create proxy instance bns instance name null parameters",
			args{ctx: ctx, req: &ProxyInstanceBnsParams{}}, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := op.CreateProxyInstanceBns(tt.args.ctx, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("DeleteProxyInstanceBns() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	patch := ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceInstanceInfo",
		func(ctx context.Context, req *bns.BnsServiceInstanceInfoRequest) (
			rsp *bns.BnsServiceInstanceInfoResponse, err error) {
			return nil, cerrs.ErrBNSRequestNotFound.Errorf("BnsServiceInstanceInfo request fail")
		})
	if op.CreateProxyInstanceBns(ctx, &ProxyInstanceBnsParams{
		ClusterID: 999,
		ProxyInstances: []*ProxyInstanceInfo{
			{100, "1"},
		},
	}) == nil {
		t.Errorf("CreateProxyInstanceBns() return not match")
	}
	patch.Reset()
	//检测过滤已存在实例
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceInstanceInfo",
		func(ctx context.Context, req *bns.BnsServiceInstanceInfoRequest) (
			rsp *bns.BnsServiceInstanceInfoResponse, err error) {
			return &bns.BnsServiceInstanceInfoResponse{
				RetCode: 0,
				Msg:     "Success",
				InstanceInfo: []bns.BnsInstanceInfoDecode{
					{
						Offset: "100",
					},
					{
						Offset: "101",
					},
					{
						Offset: "102",
					},
				},
				InstanceStr: "",
			}, nil
		})
	patch1 := ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceAddInstance",
		func(ctx context.Context, req *bns.BnsAddInstanceRequest) (
			rsp *bns.BnsAddInstanceResponse, err error) {
			if req.InstanceInfo.InstanceId == 100 || req.InstanceInfo.InstanceId == 101 ||
				req.InstanceInfo.InstanceId == 102 {
				return nil, cerrs.ErrBNSRequestNotFound.Errorf("BnsServiceAddInstance request fail")
			}
			return nil, nil
		})
	if op.CreateProxyInstanceBns(ctx, &ProxyInstanceBnsParams{
		ClusterID: 999,
		ProxyInstances: []*ProxyInstanceInfo{
			{100, "1"},
			{103, "1"},
		},
	}) != nil {
		t.Errorf("CreateProxyInstanceBns() return not match")
	}
	patch.Reset()
	patch1.Reset()
	//检测重复创建已存在实例
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceInstanceInfo",
		func(ctx context.Context, req *bns.BnsServiceInstanceInfoRequest) (
			rsp *bns.BnsServiceInstanceInfoResponse, err error) {
			return &bns.BnsServiceInstanceInfoResponse{
				RetCode: 0,
				Msg:     "Success",
				InstanceInfo: []bns.BnsInstanceInfoDecode{
					{
						Offset: "100",
					},
					{
						Offset: "101",
					},
				},
				InstanceStr: "",
			}, nil
		})
	patch1 = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceAddInstance",
		func(ctx context.Context, req *bns.BnsAddInstanceRequest) (
			rsp *bns.BnsAddInstanceResponse, err error) {
			if req.InstanceInfo.InstanceId == 102 {
				return nil, cerrs.ErrBNSRequestAlreadyExist.Errorf("BnsServiceAddInstance request fail")
			}
			return nil, nil
		})
	if op.CreateProxyInstanceBns(ctx, &ProxyInstanceBnsParams{
		ClusterID: 999,
		ProxyInstances: []*ProxyInstanceInfo{
			{100, "1"},
			{102, "1"},
			{103, "1"},
		},
	}) != nil {
		t.Errorf("CreateProxyInstanceBns() return not match")
	}
	patch.Reset()
	patch1.Reset()
}

//func Test_component_CreateProxyInstanceBns_Offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	op := Instance()
//
//	if op.CreateProxyInstanceBns(ctx, &ProxyInstanceBnsParams{
//		ClusterID: 200000096,
//		ProxyInstances: []*ProxyInstanceInfo{
//			{102, "100.88.111.86"},
//			{102, "100.88.111.86"},
//		},
//	}) != nil {
//		t.Errorf("CreateProxyInstanceBns() return not match")
//	}
//}

func Test_component_CreateNodeInstanceBns(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()

	type args struct {
		ctx context.Context
		req *NodeInstanceBnsParams
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{"test create node instance bns instance name null parameters",
			args{ctx: ctx, req: &NodeInstanceBnsParams{}}, true},
		{"test create node instance bns instance name unknow engine",
			args{ctx: ctx, req: &NodeInstanceBnsParams{
				NodeInstances: []*NodeInstanceInfo{
					{
						ShortID:    100,
						FloatingIP: "",
						Engine:     "unknow",
					},
				},
			}}, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := op.CreateNodeInstanceBns(tt.args.ctx, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("CreateNodeInstanceBns() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	patch := ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceInstanceInfo",
		func(ctx context.Context, req *bns.BnsServiceInstanceInfoRequest) (
			rsp *bns.BnsServiceInstanceInfoResponse, err error) {
			return nil, cerrs.ErrBNSRequestNotFound.Errorf("BnsServiceInstanceInfo request fail")
		})
	if op.CreateNodeInstanceBns(ctx, &NodeInstanceBnsParams{
		ClusterID: 999,
		NodeInstances: []*NodeInstanceInfo{
			{100, "1", "redis"},
		},
	}) == nil {
		t.Errorf("CreateNodeInstanceBns() return not match")
	}
	patch.Reset()
	//检测过滤已存在实例
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceInstanceInfo",
		func(ctx context.Context, req *bns.BnsServiceInstanceInfoRequest) (
			rsp *bns.BnsServiceInstanceInfoResponse, err error) {
			return &bns.BnsServiceInstanceInfoResponse{
				RetCode: 0,
				Msg:     "Success",
				InstanceInfo: []bns.BnsInstanceInfoDecode{
					{
						Offset: "100",
					},
					{
						Offset: "101",
					},
					{
						Offset: "102",
					},
				},
				InstanceStr: "",
			}, nil
		})
	patch1 := ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceAddInstance",
		func(ctx context.Context, req *bns.BnsAddInstanceRequest) (
			rsp *bns.BnsAddInstanceResponse, err error) {
			if req.InstanceInfo.InstanceId == 100 || req.InstanceInfo.InstanceId == 101 ||
				req.InstanceInfo.InstanceId == 102 {
				return nil, cerrs.ErrBNSRequestNotFound.Errorf("BnsServiceAddInstance request fail")
			}
			return nil, nil
		})
	if op.CreateNodeInstanceBns(ctx, &NodeInstanceBnsParams{
		ClusterID: 999,
		NodeInstances: []*NodeInstanceInfo{
			{100, "1", "redis"},
			{103, "1", "redis"},
		},
	}) != nil {
		t.Errorf("CreateNodeInstanceBns() return not match")
	}
	patch.Reset()
	patch1.Reset()
	//检测重复创建已存在实例
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceInstanceInfo",
		func(ctx context.Context, req *bns.BnsServiceInstanceInfoRequest) (
			rsp *bns.BnsServiceInstanceInfoResponse, err error) {
			return &bns.BnsServiceInstanceInfoResponse{
				RetCode: 0,
				Msg:     "Success",
				InstanceInfo: []bns.BnsInstanceInfoDecode{
					{
						Offset: "100",
					},
					{
						Offset: "101",
					},
				},
				InstanceStr: "",
			}, nil
		})
	patch1 = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceAddInstance",
		func(ctx context.Context, req *bns.BnsAddInstanceRequest) (
			rsp *bns.BnsAddInstanceResponse, err error) {
			if req.InstanceInfo.InstanceId == 102 {
				return nil, cerrs.ErrBNSRequestAlreadyExist.Errorf("BnsServiceAddInstance request fail")
			}
			return nil, nil
		})
	if op.CreateNodeInstanceBns(ctx, &NodeInstanceBnsParams{
		ClusterID: 999,
		NodeInstances: []*NodeInstanceInfo{
			{100, "1", "redis"},
			{102, "1", "redis"},
			{103, "1", "redis"},
		},
	}) != nil {
		t.Errorf("CreateNodeInstanceBns() return not match")
	}
	patch.Reset()
	patch1.Reset()
}

//func Test_component_CreateNodeInstanceBns_Offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	op := Instance()
//	if op.CreateNodeInstanceBns(ctx, &NodeInstanceBnsParams{
//		ClusterID: 200000009,
//		NodeInstances: []*NodeInstanceInfo{
//			{104, "100.88.111.85", "pegadb"},
//		},
//	}) != nil {
//		t.Errorf("CreateNodeInstanceBns() return not match")
//	}
//}

func Test_component_clearBns(t *testing.T) {

	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()
	patch := ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceClearInstance",
		func(ctx context.Context, req *bns.BnsClearInstanceRequest) (
			rsp *bns.BnsGeneralResponse, err error) {
			return nil, errors.New("abc")
		})
	if op.clearBns(ctx, "test-bns") == nil {
		t.Errorf("clearBns() return not match")
	}
	patch.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceClearInstance",
		func(ctx context.Context, req *bns.BnsClearInstanceRequest) (
			rsp *bns.BnsGeneralResponse, err error) {
			return nil, nil
		})
	if op.clearBns(ctx, "test-bns") != nil {
		t.Errorf("clearBns() return not match")
	}
	patch.Reset()
}

func Test_component_checkBns(t *testing.T) {

	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()
	patch := ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceInstanceInfo",
		func(ctx context.Context, req *bns.BnsServiceInstanceInfoRequest) (rsp *bns.BnsServiceInstanceInfoResponse, err error) {
			return nil, errors.New("abc")
		})
	if op.checkBns(ctx, "test-bns") == nil {
		t.Errorf("checkBns() return not match")
	}
	patch.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceInstanceInfo",
		func(ctx context.Context, req *bns.BnsServiceInstanceInfoRequest) (rsp *bns.BnsServiceInstanceInfoResponse, err error) {
			return nil, nil
		})
	if op.checkBns(ctx, "test-bns") != nil {
		t.Errorf("checkBns() return not match")
	}
	patch.Reset()
}

func Test_component_CreateNodeInstanceBns_Offline(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()
	if err := op.clearBns(ctx, "cluster-200002470-proxysandbox.BCE.all"); err != nil {
		fmt.Println(err)
	}
}

// func Test_component_CreateClusterBns_offline(t *testing.T) {
//
//		ctx := context.Background()
//		defer sdk_utils.TestEnvDefer(ctx)()
//		op := Instance()
//		if op.CreateClusterBns(ctx, &ClusterBnsParams{
//			AppType:   x1model.AppTypeStandalone,
//			ClusterID: 200000008,
//			Engine:    "redis",
//		}) != nil {
//			t.Errorf("CreateClusterBns() return not match")
//		}
//		if op.CreateClusterBns(ctx, &ClusterBnsParams{
//			AppType:   x1model.AppTypeCluster,
//			ClusterID: 200000007,
//			Engine:    "redis",
//		}) != nil {
//			t.Errorf("CreateClusterBns() return not match")
//		}
//		if op.CreateClusterBns(ctx, &ClusterBnsParams{
//			AppType:   x1model.AppTypeCluster,
//			ClusterID: 200000006,
//			Engine:    "pegadb",
//		}) != nil {
//			t.Errorf("CreateClusterBns() return not match")
//		}
//	}
//func Test_component_DeleteClusterBns_offline(t *testing.T) {
//
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	op := Instance()
//	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
//		AppType:   x1model.AppTypeStandalone,
//		ClusterID: 200000008,
//		Engine:    "redis",
//	}) != nil {
//		t.Errorf("CreateClusterBns() return not match")
//	}
//	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
//		AppType:   x1model.AppTypeCluster,
//		ClusterID: 200002471,
//		Engine:    "redis",
//	}) != nil {
//		t.Errorf("CreateClusterBns() return not match")
//	}
//	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
//		AppType:   x1model.AppTypeCluster,
//		ClusterID: 200000006,
//		Engine:    "pegadb",
//	}) != nil {
//		t.Errorf("CreateClusterBns() return not match")
//	}
//}

func Test_component_DeleteClusterBns(t *testing.T) {

	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()
	patch := ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceInstanceInfo",
		func(ctx context.Context, req *bns.BnsServiceInstanceInfoRequest) (rsp *bns.BnsServiceInstanceInfoResponse, err error) {
			return nil, cerrs.ErrBNSRequestNotFound.Errorf("abc")
		})
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeStandalone,
		ClusterID: 200000008,
		Engine:    "redis",
	}) != nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeCluster,
		ClusterID: 200002471,
		Engine:    "redis",
	}) != nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeCluster,
		ClusterID: 200000006,
		Engine:    "pegadb",
	}) != nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	patch.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceInstanceInfo",
		func(ctx context.Context, req *bns.BnsServiceInstanceInfoRequest) (rsp *bns.BnsServiceInstanceInfoResponse, err error) {
			return nil, errors.New("abc")
		})
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeStandalone,
		ClusterID: 200000008,
		Engine:    "redis",
	}) == nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeCluster,
		ClusterID: 200002471,
		Engine:    "redis",
	}) == nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeCluster,
		ClusterID: 200000006,
		Engine:    "pegadb",
	}) == nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	patch.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceInstanceInfo",
		func(ctx context.Context, req *bns.BnsServiceInstanceInfoRequest) (rsp *bns.BnsServiceInstanceInfoResponse, err error) {
			return nil, nil
		})
	patch1 := ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceClearInstance",
		func(ctx context.Context, req *bns.BnsClearInstanceRequest) (
			rsp *bns.BnsGeneralResponse, err error) {
			return nil, nil
		})
	patch2 := ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceDelete",
		func(ctx context.Context, req *bns.BnsDeleteRequest) (rsp *bns.BnsGeneralResponse, err error) {
			return nil, nil
		})
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeStandalone,
		ClusterID: 200000008,
		Engine:    "redis",
	}) != nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeCluster,
		ClusterID: 200002471,
		Engine:    "redis",
	}) != nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeCluster,
		ClusterID: 200000006,
		Engine:    "pegadb",
	}) != nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	patch.Reset()
	patch1.Reset()
	patch2.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceInstanceInfo",
		func(ctx context.Context, req *bns.BnsServiceInstanceInfoRequest) (rsp *bns.BnsServiceInstanceInfoResponse, err error) {
			return nil, nil
		})
	patch1 = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceClearInstance",
		func(ctx context.Context, req *bns.BnsClearInstanceRequest) (
			rsp *bns.BnsGeneralResponse, err error) {
			return nil, errors.New("abc")
		})
	patch2 = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceDelete",
		func(ctx context.Context, req *bns.BnsDeleteRequest) (rsp *bns.BnsGeneralResponse, err error) {
			return nil, nil
		})
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeStandalone,
		ClusterID: 200000008,
		Engine:    "redis",
	}) == nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeCluster,
		ClusterID: 200002471,
		Engine:    "redis",
	}) == nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeCluster,
		ClusterID: 200000006,
		Engine:    "pegadb",
	}) == nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	patch.Reset()
	patch1.Reset()
	patch2.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceInstanceInfo",
		func(ctx context.Context, req *bns.BnsServiceInstanceInfoRequest) (rsp *bns.BnsServiceInstanceInfoResponse, err error) {
			return nil, nil
		})
	patch1 = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceClearInstance",
		func(ctx context.Context, req *bns.BnsClearInstanceRequest) (
			rsp *bns.BnsGeneralResponse, err error) {
			return nil, nil
		})
	patch2 = ApplyMethodFunc(reflect.TypeOf(op.bnsSdk), "BnsServiceDelete",
		func(ctx context.Context, req *bns.BnsDeleteRequest) (rsp *bns.BnsGeneralResponse, err error) {
			return nil, errors.New("abc")
		})
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeStandalone,
		ClusterID: 200000008,
		Engine:    "redis",
	}) == nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeCluster,
		ClusterID: 200002471,
		Engine:    "redis",
	}) == nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	if op.DeleteClusterBns(ctx, &ClusterBnsParams{
		AppType:   x1model.AppTypeCluster,
		ClusterID: 200000006,
		Engine:    "pegadb",
	}) == nil {
		t.Errorf("DeleteClusterBns() return not match")
	}
	patch.Reset()
	patch1.Reset()
	patch2.Reset()
}

func Test_component_GetEndPoint(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	op := Instance()
	if op.GetEndPoint() != "http://argus.bj.baidubce.com/translator-web/index.php?r=MonitorData/report" {
		t.Errorf("GetEndPoint fail")
	}
}
