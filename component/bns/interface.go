package bns

import "icode.baidu.com/baidu/scs/x1-base/sdk/bns"

type ModifyBnsGroupParam struct {
	BnsGroup    string
	BnsServices []string
	Action      string `json:"action"` // add：追加服务列表到服务组 remove：从服务组里移出服务
}

type CreateBnsGroupParam struct {
	GroupName        string
	BnsServices      []string
	NodeName         string
	Threshold        int                       // 可选  当此服务组下状态为0的实例数目低于此值时，查询端得到的信息将不会被更新（若不用此特性 请置为0 即可）
	ThresholdPercent int                       // 可选  0~100之间
	IdcMap           map[string]bns.IdcMapConf // idc map
	ProtocolName     string                    // 协议名称,默认值为"nshead"
	ConverterName    string                    // 数据打包方式,默认值为"mcpack2"
	ServiceRtimeout  int                       // 读超时(ms)
	ServiceCtimeout  int                       // 连接超时(ms)
	ServiceWtimeout  int                       // 写超时(ms)
	ServiceRetry     int                       // 重试次数,默认值为0,可自定义配置

}

type BnsAddInstanceParam struct {
	BnsService      string
	Region          string
	HostName        string            `json:"hostName"`
	Port            map[string]string `json:"port"`       // json格式，如：{"main":21,"ctl":20}
	Tag             map[string]string `json:"tag"`        // 如：k1:v1,k2:v2
	Disable         int               `json:"disable"`    // 值为1：加入时缺省为bns不可见  值为0：(缺省)表示一旦加入就bns可见
	InstanceId      int               `json:"instanceId"` // 可选；大于等于0的整数；未设置由系统生成
	DeployPath      string            `json:"deployPath"`
	HealthCheckCmd  string            `json:"healthCheckCmd"`  // 监控的端口:例如(port:2048) 2048为main端口
	HealthCheckType string            `json:"healthCheckType"` // proc：进程方式；script：脚本方式
	ContainerId     string            `json:"containerId"`     // 可选
}

type BnsDeleteInstanceParam struct {
	BnsService string
	HostName   string `json:"hostName"`
	InstanceId int    `json:"instanceid"`
}

type InstanceDisableAndEnableParam struct {
	BnsService   string
	Enable       string   `json:"enable"`
	ContainerIds []string `json:"containerIds"`
}
