package bns

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"strings"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bns"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type BnsResource interface {
	BnsNodeCreate(ctx context.Context, nodeName string) (rsp *bns.BnsGeneralResponse2, err error)
	BnsNodeDelete(ctx context.Context, nodeName string) error
	BnsNodeInfo(ctx context.Context, nodeName string) (rsp *bns.BnsNodeInfoResponse, err error)

	BnsServiceCreate(ctx context.Context, bnsService string, nodeName string) error
	BnsServiceDelete(ctx context.Context, bnsService string) error

	BnsServiceAddInstance(ctx context.Context, req *BnsAddInstanceParam) error
	BnsServiceClearInstance(ctx context.Context, bnsService string) error
	BnsServiceDeleteInstance(ctx context.Context, req *BnsDeleteInstanceParam) error
	BnsServiceInstanceInfo(ctx context.Context, bnsService string) (rsp *bns.BnsServiceInstanceInfoResponse, err error)
	BatchDisableAndEnableInstancesByContainerId(ctx context.Context, req *InstanceDisableAndEnableParam) error

	BnsGroupModifyServices(ctx context.Context, req *ModifyBnsGroupParam) error
	BnsGroupCreate(ctx context.Context, req *CreateBnsGroupParam) error
	BnsGroupDelete(ctx context.Context, groupName string) error
	BnsGroupServices(ctx context.Context, groupName string) ([]string, error)

	GenerateDefaultIdcMap(bnsGroupConf *bns.BnsGroupConfList) (idcMap string, err error)
	GenerateBnsNodeName(app *x1model.Application, baseName string) string
	GenerateBnsServiceName(app *x1model.Application, baseName string) string
	GenerateBnsGroupName(app *x1model.Application, baseName string) string
}

type bnsResourceOp struct {
	bnsSdk  bns.BnsService
	nodeSdk bns.BnsService
}

var bnsResourceOpObj BnsResource

var once sync.Once

func BnsResourceOp() BnsResource {
	once.Do(func() {
		config = &BnsConfig{}
		if err := compo_utils.LoadConf("bns", config); err != nil {
			panic(err.Error())
		}
		logger.ComponentLogger.Trace(context.Background(), "bns config: %s", base_utils.Format(config))
		bnsResourceOpObj = &bnsResourceOp{
			bnsSdk:  bns.NewBefaultBnsSdk(),
			nodeSdk: bns.NewBnsSdk(bns.SpecialServiceName),
		}

	})
	return bnsResourceOpObj
}

func (br *bnsResourceOp) BnsNodeCreate(ctx context.Context, nodeName string) (rsp *bns.BnsGeneralResponse2, err error) {
	if nodeName == "" {
		return nil, cerrs.ErrBNSRequestFail.Errorf("bns node is empty")
	}

	rsp, err = br.nodeSdk.BnsNodeCreate(ctx, &bns.BnsNodeCreateRequest{
		ParentPath: config.ParentConfig.Path,
		Token:      config.ParentConfig.Token,
		NodeName:   nodeName,
		Type:       bnsNodeType,
	})

	if rsp == nil {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsNodeCreate request fail, nodeName:%s , BnsError: %s",
			nodeName, err)
	}

	if !rsp.Success {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsNodeCreate request fail, nodeName:%s , BnsError RetCode: %t , BnsError ErrorMsg: %s",
			nodeName, rsp.Success, rsp.Message)
	}
	return
}

func (br *bnsResourceOp) BnsNodeDelete(ctx context.Context, nodeName string) error {
	if nodeName == "" {
		return cerrs.ErrBNSRequestFail.Errorf("bns node is empty")
	}

	rsp, err := br.nodeSdk.BnsNodeDelete(ctx, &bns.BnsNodeDeleteRequest{
		FullPath: config.ParentConfig.Path + "_" + nodeName,
		Token:    config.ParentConfig.Token,
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsNodeDelete request fail, nodeName:%s , BnsError: %s", nodeName, err)
	}
	if !rsp.Success {
		return cerrs.ErrBNSRequestFail.Errorf("BnsNodeDelete request fail, nodeName:%s , BnsError RetCode: %t , BnsError ErrorMsg: %s",
			nodeName, rsp.Success, rsp.Message)
	}
	return nil
}

func (br *bnsResourceOp) BnsNodeInfo(ctx context.Context, nodeName string) (rsp *bns.BnsNodeInfoResponse, err error) {
	if nodeName == "" {
		return nil, cerrs.ErrBNSRequestFail.Errorf("bns node is empty")
	}

	rsp, err = br.nodeSdk.BnsNodeInfo(ctx, &bns.BnsNodeInfoRequest{
		FullPath: config.ParentConfig.Path + "_" + nodeName,
		Token:    config.ParentConfig.Token,
	})

	return
}

func (br *bnsResourceOp) BnsServiceCreate(ctx context.Context, bnsService string, nodeName string) error {
	if bnsService == "" {
		return cerrs.ErrBNSRequestFail.Errorf("bns service is empty")
	}

	request := &bns.BnsCreateRequest{
		ParentPath: config.ParentConfig.Path,
		AuthKey:    config.ParentConfig.Token,
		NodeName:   bnsService,
	}

	if nodeName != "" {
		request.ParentPath += "_" + nodeName
	}

	rsp, err := br.bnsSdk.BnsServiceCreate(ctx, request)

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceCreate request fail, req:%s , BnsError: %s", base_utils.Format(request), err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceCreate request fail, bns_service:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s", bnsService, rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) BnsServiceDelete(ctx context.Context, bnsSerivce string) error {
	if bnsSerivce == "" {
		return cerrs.ErrBNSRequestFail.Errorf("bns service is empty")
	}

	rsp, err := br.bnsSdk.BnsServiceDelete(ctx, &bns.BnsDeleteRequest{
		ServiceName: bnsSerivce,
		AuthKey:     config.ParentConfig.Token,
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceDelete request fail, bnsService:%s , BnsError: %s", bnsSerivce, err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceDelete request fail, bnsService:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s", bnsSerivce, rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) BnsServiceAddInstance(ctx context.Context, req *BnsAddInstanceParam) error {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp, err := br.bnsSdk.BnsServiceAddInstance(ctx, &bns.BnsAddInstanceRequest{
		ServiceName: req.BnsService,
		AuthKey:     config.ParentConfig.Token,
		InstanceInfo: &bns.BnsInstanceInfo{
			HostName:        req.HostName,
			Port:            req.Port,
			Tag:             req.Tag,
			Disable:         req.Disable,
			InstanceId:      req.InstanceId,
			Status:          config.InstanceConfig.DefaultStatus,
			DeployPath:      req.DeployPath,
			RunUser:         config.InstanceConfig.DefaultRunUser,
			HealthCheckCmd:  req.HealthCheckCmd,
			HealthCheckType: req.HealthCheckType,
			ContainerId:     req.ContainerId,
		},
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceAddInstance request fail,req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceAddInstance request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) BnsServiceClearInstance(ctx context.Context, bnsService string) error {
	if bnsService == "" {
		return cerrs.ErrBNSRequestFail.Errorf("bns service is empty")
	}

	rsp, err := br.bnsSdk.BnsServiceClearInstance(ctx, &bns.BnsClearInstanceRequest{
		ServiceName: bnsService,
		AuthKey:     config.ParentConfig.Token,
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceClearInstance request fail, bnsService:%s , BnsError: %s", bnsService, err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceClearInstance request fail, bnsService:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s", bnsService, rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) BnsServiceDeleteInstance(ctx context.Context, req *BnsDeleteInstanceParam) error {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp, err := br.bnsSdk.BnsServiceDeleteInstance(ctx, &bns.BnsDeleteInstanceRequest{
		ServiceName: req.BnsService,
		AuthKey:     config.ParentConfig.Token,
		HostName:    req.HostName,
		InstanceId:  req.InstanceId,
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceDeleteInstance request fail, req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceDeleteInstance request fail, req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) BnsGroupModifyServices(ctx context.Context, req *ModifyBnsGroupParam) error {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	rsp, err := br.bnsSdk.BnsGroupModifyServices(ctx, &bns.BnsGroupModifyServicesRequest{
		GroupName:    req.BnsGroup,
		AuthKey:      config.ParentConfig.Token,
		ServiceNames: req.BnsServices,
		Action:       req.Action,
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsGroupModifyServices request fail, req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if !rsp.Success {
		return cerrs.ErrBNSRequestFail.Errorf("BnsGroupModifyServices request fail, req:%s , BnsError Success: %t , BnsError ErrorMessage: %s",
			base_utils.Format(req), rsp.Success, rsp.Message)
	}
	return nil
}

func (br *bnsResourceOp) BnsGroupCreate(ctx context.Context, req *CreateBnsGroupParam) error {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	request := &bns.BnsGroupCreateRequest{
		ServiceNames:     req.BnsServices,
		NodePath:         config.ParentConfig.Path,
		AuthKey:          config.ParentConfig.Token,
		Threshold:        req.Threshold,
		ThresholdPercent: req.ThresholdPercent,
		GroupConf: &bns.BnsGroupConfList{
			GroupName:       req.GroupName,
			IdcMap:          req.IdcMap,
			ProtocolName:    req.ProtocolName,
			ConverterName:   req.ConverterName,
			ServiceRtimeout: req.ServiceRtimeout,
			ServiceCtimeout: req.ServiceCtimeout,
			ServiceWtimeout: req.ServiceWtimeout,
			ServiceRetry:    req.ServiceRetry,
		},
		ConfIsJson:        config.GroupConfig.ConfIsJson, // 对GroupConf进行json格式化校验
		ConstrainGroup:    config.GroupConfig.ConstrainGroup,
		OpenDeadhostCheck: config.GroupConfig.OpenDeadhostCheck, // 开启死机检测
	}
	if req.NodeName != "" {
		request.NodePath += "_" + req.NodeName
	}

	rsp, err := br.bnsSdk.BnsGroupCreate(ctx, request)
	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsGroupCreate request fail,req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BnsGroupCreate request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) BnsGroupDelete(ctx context.Context, groupName string) error {
	if groupName == "" {
		return cerrs.ErrBNSRequestFail.Errorf("groupName is empty")
	}
	rsp, err := br.bnsSdk.BnsGroupDelete(ctx, &bns.BnsGroupDeleteRequest{
		GroupName: groupName,
		AuthKey:   config.ParentConfig.Token,
		Limit:     config.GroupConfig.DeleteLimit,
	})
	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsGroupDelete request fail, groupName:%s, BnsError: %s",
			groupName, err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BnsGroupDelete request fail, groupName:%s, BnsError RetCode: %d , BnsError ErrorMsg: %s",
			groupName, rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) BnsGroupServices(ctx context.Context, groupName string) ([]string, error) {
	if groupName == "" {
		return nil, cerrs.ErrBNSRequestFail.Errorf("groupName is empty")
	}
	rsp, err := br.bnsSdk.BnsGroupInfo(ctx, &bns.BnsGroupInfoRequest{
		GroupName: groupName,
		AuthKey:   config.ParentConfig.Token,
	})
	if err != nil {
		return nil, err
	}
	return strings.Split(rsp.Data.ServiceNames, ","), nil
}

func (br *bnsResourceOp) BnsServiceInstanceInfo(ctx context.Context, bnsService string) (rsp *bns.BnsServiceInstanceInfoResponse, err error) {
	if bnsService == "" {
		return nil, cerrs.ErrBNSRequestFail.Errorf("bnsService is empty")
	}

	rsp, err = br.bnsSdk.BnsServiceInstanceInfo(ctx, &bns.BnsServiceInstanceInfoRequest{
		ServiceName: bnsService,
		AuthKey:     config.ParentConfig.Token,
	})

	return rsp, err
}

func (br *bnsResourceOp) BatchDisableAndEnableInstancesByContainerId(ctx context.Context, req *InstanceDisableAndEnableParam) error {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	containerIds, err := br.bnsSdk.BnsArrayToString(req.ContainerIds)
	if err != nil {
		return cerrs.ErrInvalidParams.Errorf("req param containerIds format failed")
	}

	rsp, err := br.bnsSdk.BatchDisableAndEnableInstancesByContainerId(ctx, &bns.InstanceDisableAndEnableRequest{
		ServiceName:  req.BnsService,
		AuthKey:      config.ParentConfig.Token,
		Enable:       req.Enable,
		ContainerIds: containerIds,
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BatchDisableAndEnableInstancesByContainerId request fail,req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BatchDisableAndEnableInstancesByContainerId request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) GenerateDefaultIdcMap(bnsGroupConf *bns.BnsGroupConfList) (idcMap string, err error) {
	idcMap, err = br.bnsSdk.GenerateDefaultIdcMap(bnsGroupConf)
	if err != nil {
		return "", cerrs.ErrBNSRequestFail.Errorf("GenerateDefaultIdcMap request fail,req:%s , BnsError: %s",
			base_utils.Format(bnsGroupConf), err)
	}
	return
}

func (br *bnsResourceOp) GenerateBnsNodeName(app *x1model.Application, baseName string) string {
	chunks := strings.Split(app.AppId, "-")
	return strings.ReplaceAll(baseName+"-"+chunks[len(chunks)-1], "_", "-")
}

func (br *bnsResourceOp) GenerateBnsServiceName(app *x1model.Application, baseName string) string {
	chunks := strings.Split(app.AppId, "-")
	return strings.ReplaceAll(baseName+"-"+chunks[len(chunks)-1]+"."+config.ParentConfig.Product+"."+transformBnsIdc(app.Region), "_", "-")
}

func (br *bnsResourceOp) GenerateBnsGroupName(app *x1model.Application, baseName string) string {
	chunks := strings.Split(app.AppId, "-")
	return strings.ReplaceAll("group."+baseName+"-"+chunks[len(chunks)-1]+"."+config.ParentConfig.Product+".all", "_", "-")
}

func transformBnsIdc(src string) string {
	for _, pair := range config.IDCMap {
		if src == pair.OriginalIDC {
			return pair.TargetIDC
		}
	}
	return src
}

func TransformBnsIdc(src string) string {
	return transformBnsIdc(src)
}
