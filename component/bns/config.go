package bns

// bns node 配置
const (
	bnsNodeType = "service"
)

type ParentConfig struct {
	Path    string
	Token   string
	Product string
}

type GroupConfig struct {
	DeleteLimit       bool
	ConfIsJson        int //  缺省为1，进行json校验
	ConstrainGroup    int //  0：表示不限制，缺省值   1：表示限制
	OpenDeadhostCheck int //  0：表示不开启，缺省值   1：表示开启
}

type ServiceConfig struct {
	UseDynamicNode bool
}

type InstanceConfig struct {
	DefaultStatus  int
	DefaultRunUser string
}

type IDCMap struct {
	OriginalIDC string
	TargetIDC   string
}

type BnsConfig struct {
	ParentConfig   *ParentConfig
	GroupConfig    *GroupConfig
	InstanceConfig *InstanceConfig
	ServiceConfig  *ServiceConfig
	IDCMap         []*IDCMap
}

var (
	config *BnsConfig
)
