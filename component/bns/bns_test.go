package bns

//func TestBnsNodeCreate(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	rsp, err := bccOp.BnsNodeCreate(ctx, "ral-unit-test-node")
//
//	if err != nil {
//		fmt.Println(err)
//		return
//	}
//
//	fmt.Println("BnsNodeCreate resp:", base_utils.Format(rsp))
//}

//func TestBnsServiceCreate(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsServiceCreate(ctx, "ral-unit-test.redis.bj", "ral-unit-test-node")
//
//	if err != nil {
//		fmt.Println(err)
//		t.<PERSON><PERSON><PERSON>("expect nil error, actual %s", err.Error())
//	}
//}

//func TestBnsServiceDelete(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsServiceDelete(ctx, "ral-unit-test.redis.bj")
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}

//func TestBnsServiceAddInstance(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsServiceAddInstance(ctx, &BnsAddInstanceParam{
//		BnsService: "ral-unit-test.redis.bj",
//		Region:     "nj",
//		HostName:   "nj02-bdrp-share59.nj02",
//		Port: map[string]string{
//			"main": "1236",
//			"test": "7787",
//		},
//		Tag: map[string]string{
//			"module": "test1",
//			"test":   "proxy",
//		},
//		Disable:         0,
//		InstanceId:      5,
//		DeployPath:      "/home/<USER>/redis_bbb",
//		HealthCheckCmd:  "prot:6666",
//		HealthCheckType: "proc",
//		ContainerId:     "x1-base-test-55",
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}

//func TestBnsServiceClearInstance(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsServiceClearInstance(ctx, "ral-unit-test.redis.bj")
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}

//func TestBnsServiceDeleteInstance(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsServiceDeleteInstance(ctx, &BnsDeleteInstanceParam{
//		BnsService: "ral-unit-test.redis.bj",
//		HostName:   "nj02-bdrp-share59.nj02",
//		InstanceId: 1,
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}

//func TestBnsGroupModifyServices(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsGroupModifyServices(ctx, &ModifyBnsGroupParam{
//		BnsServices: []string{"ral-unit-test.redis.bj", "ral-unit-test.redis.gz"},
//		Action:      "add",
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}

//func TestBnsGroupCreate(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsGroupCreate(ctx, &CreateBnsGroupParam{
//		GroupName:        "ral-unit-test.redis.all",
//		NodeName:         "ral-unit-test-node",
//		BnsServices:      []string{"ral-unit-test.redis.bj", "ral-unit-test.redis.gz"},
//		Threshold:        5,
//		ThresholdPercent: 20,
//		ProtocolName:     "nshead_test",
//		ConverterName:    "mcpack2_test",
//		ServiceRtimeout:  100,
//		ServiceCtimeout:  50,
//		ServiceWtimeout:  60,
//		ServiceRetry:     4,
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}

//func TestBnsGroupDelete(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsGroupDelete(ctx, "ral-unit-test.redis.all")
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}

//func TestBnsServiceInstanceInfo(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	rsp, err := bccOp.BnsServiceInstanceInfo(ctx, "ral-unit-test.redis.all")
//
//	if err != nil {
//		fmt.Println(err)
//		return
//	}
//
//	fmt.Println("BnsServiceInstanceInfo resp:", base_utils.Format(rsp))
//}

//func TestDisableAndEnableInstance(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BatchDisableAndEnableInstancesByContainerId(ctx, &InstanceDisableAndEnableParam{
//		BnsService: "ral-unit-test.redis.bj",
//		Enable:     "enable",
//		ContainerIds: []string{
//			"x1-base-test-88",
//			"x1-base-test-99",
//		},
//	})
//
//	// test2
//	// rsp, err := bccOp.BatchDisableAndEnableInstancesByContainerId(ctx, nil)
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}
