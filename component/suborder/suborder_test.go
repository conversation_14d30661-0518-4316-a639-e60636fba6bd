package suborder

import (
	"context"
	"errors"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
)

// Mock implementations

// mockWrapper implements the Wrapper interface for testing
type mockWrapper struct {
	requestSplitFunc   func(ctx context.Context, order *OrderRecord, request interface{}) (map[string]interface{}, error)
	responseMergerFunc func(ctx context.Context, order *OrderRecord, resps map[string]interface{}) (interface{}, error)
	sendSubFunc        func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (realRequest interface{}, orderID string, err error)
	querySubFunc       func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (inOperation bool, response interface{}, err error)
}

func (m *mockWrapper) RequestSplit(ctx context.Context, order *OrderRecord, request interface{}) (map[string]interface{}, error) {
	if m.requestSplitFunc != nil {
		return m.requestSplitFunc(ctx, order, request)
	}
	return map[string]interface{}{
		"key1": map[string]interface{}{"param": "value1"},
		"key2": map[string]interface{}{"param": "value2"},
	}, nil
}

func (m *mockWrapper) ResponseMerger(ctx context.Context, order *OrderRecord, resps map[string]interface{}) (interface{}, error) {
	if m.responseMergerFunc != nil {
		return m.responseMergerFunc(ctx, order, resps)
	}
	return map[string]interface{}{"merged": "response"}, nil
}

func (m *mockWrapper) SendSub(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (realRequest interface{}, orderID string, err error) {
	if m.sendSubFunc != nil {
		return m.sendSubFunc(ctx, order, subOrder)
	}
	return map[string]interface{}{"real": "request"}, "mock-bcc-order-id", nil
}

func (m *mockWrapper) QuerySub(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (inOperation bool, response interface{}, err error) {
	if m.querySubFunc != nil {
		return m.querySubFunc(ctx, order, subOrder)
	}
	return false, map[string]interface{}{"status": "success"}, nil
}

// mockModelService implements the ModelService interface for testing
type mockModelService struct {
	getOrderByTaskIDFunc      func(ctx context.Context, orderID string) (*OrderRecord, error)
	getOrderByIDFunc          func(ctx context.Context, orderID string) (*OrderRecord, error)
	saveOrderFunc             func(ctx context.Context, order *OrderRecord) (*OrderRecord, error)
	getSubOrdersByOrderIDFunc func(ctx context.Context, orderID string) ([]*SubOrderRecord, error)
	saveSubOrdersFunc         func(ctx context.Context, orders []*SubOrderRecord) error
}

func (m *mockModelService) GetOrderByTaskID(ctx context.Context, taskID, azone string) (*OrderRecord, error) {
	if m.getOrderByTaskIDFunc != nil {
		return m.getOrderByTaskIDFunc(ctx, taskID)
	}
	return nil, nil
}

func (m *mockModelService) GetOrderByID(ctx context.Context, orderID string) (*OrderRecord, error) {
	if m.getOrderByIDFunc != nil {
		return m.getOrderByIDFunc(ctx, orderID)
	}
	return nil, nil
}

func (m *mockModelService) SaveOrder(ctx context.Context, order *OrderRecord) (*OrderRecord, error) {
	if m.saveOrderFunc != nil {
		return m.saveOrderFunc(ctx, order)
	}
	return order, nil
}

func (m *mockModelService) GetSubOrdersByOrderID(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
	if m.getSubOrdersByOrderIDFunc != nil {
		return m.getSubOrdersByOrderIDFunc(ctx, orderID)
	}
	return []*SubOrderRecord{}, nil
}

func (m *mockModelService) SaveSubOrders(ctx context.Context, orders []*SubOrderRecord) error {
	if m.saveSubOrdersFunc != nil {
		return m.saveSubOrdersFunc(ctx, orders)
	}
	return nil
}

// Test helper functions
func createTestSubOrder(wrapper Wrapper, modelService ModelService) *SubOrder {
	return &SubOrder{
		Wrapper:      wrapper,
		ModelService: modelService,
	}
}

func createTestMeta() *Meta {
	return &Meta{
		AppID:  "test-app-id",
		TaskID: "test-task-id",
		Azone:  "test-azone",
	}
}

// Test cases for NewSubOrder
func TestNewSubOrder(t *testing.T) {
	tests := []struct {
		name         string
		wrapper      Wrapper
		modelService ModelService
		expected     *SubOrder
	}{
		{
			name:         "valid parameters",
			wrapper:      &mockWrapper{},
			modelService: &mockModelService{},
			expected:     &SubOrder{Wrapper: &mockWrapper{}, ModelService: &mockModelService{}},
		},
		{
			name:         "nil wrapper",
			wrapper:      nil,
			modelService: &mockModelService{},
			expected:     nil,
		},
		{
			name:         "nil modelService",
			wrapper:      &mockWrapper{},
			modelService: nil,
			expected:     nil,
		},
		{
			name:         "both nil",
			wrapper:      nil,
			modelService: nil,
			expected:     nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NewSubOrder(tt.wrapper, tt.modelService)
			if (result == nil) != (tt.expected == nil) {
				t.Errorf("NewSubOrder() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// Integration tests
func TestSubOrder_Integration(t *testing.T) {
	ctx := context.Background()
	meta := createTestMeta()
	request := map[string]interface{}{"test": "request"}

	t.Run("complete workflow - send and query success", func(t *testing.T) {
		var createdOrder *OrderRecord
		var createdSubOrders []*SubOrderRecord

		wrapper := &mockWrapper{
			requestSplitFunc: func(ctx context.Context, order *OrderRecord, request interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{
					"key1": map[string]interface{}{"param": "value1"},
				}, nil
			},
			sendSubFunc: func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (realRequest interface{}, orderID string, err error) {
				return map[string]interface{}{"real": "request"}, "bcc-order-id", nil
			},
			querySubFunc: func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (inOperation bool, response interface{}, err error) {
				return false, map[string]interface{}{"result": "success"}, nil
			},
			responseMergerFunc: func(ctx context.Context, order *OrderRecord, resps map[string]interface{}) (interface{}, error) {
				return map[string]interface{}{"merged": "response"}, nil
			},
		}
		modelService := &mockModelService{
			getOrderByTaskIDFunc: func(ctx context.Context, taskID string) (*OrderRecord, error) {
				return createdOrder, nil
			},
			getOrderByIDFunc: func(ctx context.Context, orderID string) (*OrderRecord, error) {
				return createdOrder, nil
			},
			saveOrderFunc: func(ctx context.Context, order *OrderRecord) (*OrderRecord, error) {
				createdOrder = order
				return order, nil
			},
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return createdSubOrders, nil
			},
			saveSubOrdersFunc: func(ctx context.Context, orders []*SubOrderRecord) error {
				createdSubOrders = orders
				return nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		// Send request
		orderID, err := subOrder.Send(ctx, meta, request)
		if err != nil {
			t.Errorf("Send failed: %v", err)
		}
		if orderID == "" {
			t.Error("Expected non-empty orderID")
		}

		// Query result
		response, err := subOrder.Query(ctx, orderID)
		if err != nil {
			t.Errorf("Query failed: %v", err)
		}
		if response == nil {
			t.Error("Expected non-nil response")
		}
	})

	t.Run("complete workflow - send success, query in operation", func(t *testing.T) {
		var createdOrder *OrderRecord
		var createdSubOrders []*SubOrderRecord

		wrapper := &mockWrapper{
			requestSplitFunc: func(ctx context.Context, order *OrderRecord, request interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{
					"key1": map[string]interface{}{"param": "value1"},
				}, nil
			},
			sendSubFunc: func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (realRequest interface{}, orderID string, err error) {
				return map[string]interface{}{"real": "request"}, "bcc-order-id", nil
			},
			querySubFunc: func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (inOperation bool, response interface{}, err error) {
				return true, nil, nil // Still in operation
			},
		}
		modelService := &mockModelService{
			getOrderByTaskIDFunc: func(ctx context.Context, taskID string) (*OrderRecord, error) {
				return createdOrder, nil
			},
			getOrderByIDFunc: func(ctx context.Context, orderID string) (*OrderRecord, error) {
				return createdOrder, nil
			},
			saveOrderFunc: func(ctx context.Context, order *OrderRecord) (*OrderRecord, error) {
				createdOrder = order
				return order, nil
			},
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return createdSubOrders, nil
			},
			saveSubOrdersFunc: func(ctx context.Context, orders []*SubOrderRecord) error {
				createdSubOrders = orders
				return nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		// Send request
		orderID, err := subOrder.Send(ctx, meta, request)
		if err != nil {
			t.Errorf("Send failed: %v", err)
		}

		// Query result - should be in operation
		_, err = subOrder.Query(ctx, orderID)
		if err == nil {
			t.Error("Expected error for in operation, got nil")
		}
		if !cerrs.Is(err, ErrOrderInOperation) {
			t.Errorf("Expected ErrOrderInOperation, got: %v", err)
		}
	})

	t.Run("retry scenario - existing error order", func(t *testing.T) {
		wrapper := &mockWrapper{
			requestSplitFunc: func(ctx context.Context, order *OrderRecord, request interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{
					"key1": map[string]interface{}{"param": "value1"},
				}, nil
			},
			sendSubFunc: func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (realRequest interface{}, orderID string, err error) {
				return map[string]interface{}{"real": "request"}, "bcc-order-id", nil
			},
		}
		existingOrder := &OrderRecord{
			OrderID: "existing-order-id",
			Status:  OrderStatusError,
			TaskID:  meta.TaskID,
		}
		existingSubOrder := &SubOrderRecord{
			OrderID:  "existing-sub-order",
			SplitKey: "key1",
			Status:   OrderStatusError,
			Retry:    0,
		}
		modelService := &mockModelService{
			getOrderByTaskIDFunc: func(ctx context.Context, orderID string) (*OrderRecord, error) {
				return existingOrder, nil
			},
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return []*SubOrderRecord{existingSubOrder}, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		// Send request - should retry existing error order
		orderID, err := subOrder.Send(ctx, meta, request)
		if err != nil {
			t.Errorf("Send failed: %v", err)
		}
		if orderID != existingOrder.OrderID {
			t.Errorf("Expected orderID %s, got %s", existingOrder.OrderID, orderID)
		}
	})
}

// Test cases for querySubAndGetResp method
func TestSubOrder_querySubAndGetResp(t *testing.T) {
	ctx := context.Background()
	order := &OrderRecord{
		OrderID: "test-order-id",
		Status:  OrderStatusCreating,
	}

	t.Run("successful query and merge", func(t *testing.T) {
		wrapper := &mockWrapper{
			querySubFunc: func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (inOperation bool, response interface{}, err error) {
				return false, map[string]interface{}{"result": "success"}, nil
			},
			responseMergerFunc: func(ctx context.Context, order *OrderRecord, resps map[string]interface{}) (interface{}, error) {
				return map[string]interface{}{"merged": "response"}, nil
			},
		}
		subOrders := []*SubOrderRecord{
			{
				OrderID:    "sub-order-1",
				SplitKey:   "key1",
				BccOrderID: "bcc-order-1",
				Status:     OrderStatusCreating,
			},
		}
		modelService := &mockModelService{
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return subOrders, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		response, err := subOrder.querySubAndGetResp(ctx, order)
		if err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
		if response == nil {
			t.Error("Expected non-nil response")
		}
	})

	t.Run("no sub orders found", func(t *testing.T) {
		wrapper := &mockWrapper{}
		modelService := &mockModelService{
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return []*SubOrderRecord{}, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.querySubAndGetResp(ctx, order)
		if err == nil {
			t.Error("Expected error for no sub orders, got nil")
		}
	})

	t.Run("get sub orders error", func(t *testing.T) {
		wrapper := &mockWrapper{}
		modelService := &mockModelService{
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return nil, errors.New("database error")
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.querySubAndGetResp(ctx, order)
		if err == nil {
			t.Error("Expected error, got nil")
		}
	})

	t.Run("sub order in operation", func(t *testing.T) {
		wrapper := &mockWrapper{
			querySubFunc: func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (inOperation bool, response interface{}, err error) {
				return true, nil, nil // In operation
			},
		}
		subOrders := []*SubOrderRecord{
			{
				OrderID:    "sub-order-1",
				SplitKey:   "key1",
				BccOrderID: "bcc-order-1",
				Status:     OrderStatusCreating,
			},
		}
		modelService := &mockModelService{
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return subOrders, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.querySubAndGetResp(ctx, order)
		if err == nil {
			t.Error("Expected error for in operation, got nil")
		}
		if !cerrs.Is(err, ErrOrderInOperation) {
			t.Errorf("Expected ErrOrderInOperation, got: %v", err)
		}
	})

	t.Run("sub order query error", func(t *testing.T) {
		wrapper := &mockWrapper{
			querySubFunc: func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (inOperation bool, response interface{}, err error) {
				return false, nil, errors.New("query error")
			},
		}
		subOrders := []*SubOrderRecord{
			{
				OrderID:    "sub-order-1",
				SplitKey:   "key1",
				BccOrderID: "bcc-order-1",
				Status:     OrderStatusCreating,
			},
		}
		modelService := &mockModelService{
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return subOrders, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.querySubAndGetResp(ctx, order)
		if err == nil {
			t.Error("Expected error for query error, got nil")
		}
		if !cerrs.Is(err, ErrOrderError) {
			t.Errorf("Expected ErrOrderError, got: %v", err)
		}
	})

	t.Run("response merger error", func(t *testing.T) {
		wrapper := &mockWrapper{
			querySubFunc: func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (inOperation bool, response interface{}, err error) {
				return false, map[string]interface{}{"result": "success"}, nil
			},
			responseMergerFunc: func(ctx context.Context, order *OrderRecord, resps map[string]interface{}) (interface{}, error) {
				return nil, errors.New("merge error")
			},
		}
		subOrders := []*SubOrderRecord{
			{
				OrderID:    "sub-order-1",
				SplitKey:   "key1",
				BccOrderID: "bcc-order-1",
				Status:     OrderStatusCreating,
			},
		}
		modelService := &mockModelService{
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return subOrders, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.querySubAndGetResp(ctx, order)
		if err == nil {
			t.Error("Expected error for merge error, got nil")
		}
	})
}

// Test cases for buildSubOrderRecord method
func TestSubOrder_buildSubOrderRecord(t *testing.T) {
	ctx := context.Background()
	order := &OrderRecord{
		OrderID: "test-order-id",
		Status:  OrderStatusToCreate,
	}
	request := map[string]interface{}{"test": "request"}

	t.Run("successful build with new sub orders", func(t *testing.T) {
		wrapper := &mockWrapper{
			requestSplitFunc: func(ctx context.Context, order *OrderRecord, request interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{
					"key1": map[string]interface{}{"param": "value1"},
					"key2": map[string]interface{}{"param": "value2"},
				}, nil
			},
		}
		modelService := &mockModelService{
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return []*SubOrderRecord{}, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		subOrders, err := subOrder.buildSubOrderRecord(ctx, order, request)
		if err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
		if len(subOrders) != 2 {
			t.Errorf("Expected 2 sub orders, got %d", len(subOrders))
		}
	})

	t.Run("request split error", func(t *testing.T) {
		wrapper := &mockWrapper{
			requestSplitFunc: func(ctx context.Context, order *OrderRecord, request interface{}) (map[string]interface{}, error) {
				return nil, errors.New("split error")
			},
		}
		modelService := &mockModelService{
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return []*SubOrderRecord{}, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.buildSubOrderRecord(ctx, order, request)
		if err == nil {
			t.Error("Expected error, got nil")
		}
	})

	t.Run("get sub orders error", func(t *testing.T) {
		wrapper := &mockWrapper{}
		modelService := &mockModelService{
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return nil, errors.New("database error")
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.buildSubOrderRecord(ctx, order, request)
		if err == nil {
			t.Error("Expected error, got nil")
		}
	})

	t.Run("existing sub order with error status", func(t *testing.T) {
		wrapper := &mockWrapper{
			requestSplitFunc: func(ctx context.Context, order *OrderRecord, request interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{
					"key1": map[string]interface{}{"param": "value1"},
				}, nil
			},
		}
		existingSubOrder := &SubOrderRecord{
			OrderID:  "existing-sub-order",
			SplitKey: "key1",
			Status:   OrderStatusError,
			Retry:    0,
		}
		modelService := &mockModelService{
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return []*SubOrderRecord{existingSubOrder}, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		subOrders, err := subOrder.buildSubOrderRecord(ctx, order, request)
		if err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
		// After fixing the bug (now mapping by SplitKey and checking by SplitKey),
		// it should only update the existing sub order, not create a new one
		// So we expect 1 sub order: the existing one (updated)
		if len(subOrders) != 1 {
			t.Errorf("Expected 1 sub order, got %d", len(subOrders))
		}
		// Find the existing sub order that should have been updated
		var updatedSubOrder *SubOrderRecord
		for _, so := range subOrders {
			if so.OrderID == "existing-sub-order" {
				updatedSubOrder = so
				break
			}
		}
		if updatedSubOrder == nil {
			t.Error("Expected to find the existing sub order")
		} else {
			if updatedSubOrder.Status != OrderStatusToCreate {
				t.Errorf("Expected status %s, got %s", OrderStatusToCreate, updatedSubOrder.Status)
			}
			if updatedSubOrder.Retry != 1 {
				t.Errorf("Expected retry count 1, got %d", updatedSubOrder.Retry)
			}
		}
	})

	t.Run("mixed existing and new sub orders", func(t *testing.T) {
		wrapper := &mockWrapper{
			requestSplitFunc: func(ctx context.Context, order *OrderRecord, request interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{
					"key1": map[string]interface{}{"param": "value1"},
					"key2": map[string]interface{}{"param": "value2"},
					"key3": map[string]interface{}{"param": "value3"},
				}, nil
			},
		}
		existingSubOrders := []*SubOrderRecord{
			{
				OrderID:  "existing-sub-order-1",
				SplitKey: "key1",
				Status:   OrderStatusError,
				Retry:    0,
			},
			{
				OrderID:  "existing-sub-order-2",
				SplitKey: "key2",
				Status:   OrderStatusCreated,
				Retry:    0,
			},
		}
		modelService := &mockModelService{
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return existingSubOrders, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		subOrders, err := subOrder.buildSubOrderRecord(ctx, order, request)
		if err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
		// Should have 3 sub orders:
		// - key1: existing error order (updated to retry)
		// - key2: existing success order (kept as is)
		// - key3: new order
		if len(subOrders) != 3 {
			t.Errorf("Expected 3 sub orders, got %d", len(subOrders))
		}

		// Check each sub order
		subOrderMap := make(map[string]*SubOrderRecord)
		for _, so := range subOrders {
			subOrderMap[so.SplitKey] = so
		}

		// key1 should be updated for retry
		if so := subOrderMap["key1"]; so != nil {
			if so.Status != OrderStatusToCreate {
				t.Errorf("Expected key1 status %s, got %s", OrderStatusToCreate, so.Status)
			}
			if so.Retry != 1 {
				t.Errorf("Expected key1 retry count 1, got %d", so.Retry)
			}
		} else {
			t.Error("Expected to find sub order for key1")
		}

		// key2 should remain unchanged (created status)
		if so := subOrderMap["key2"]; so != nil {
			if so.Status != OrderStatusCreated {
				t.Errorf("Expected key2 status %s, got %s", OrderStatusCreated, so.Status)
			}
			if so.Retry != 0 {
				t.Errorf("Expected key2 retry count 0, got %d", so.Retry)
			}
		} else {
			t.Error("Expected to find sub order for key2")
		}

		// key3 should be a new order
		if so := subOrderMap["key3"]; so != nil {
			if so.Status != OrderStatusToCreate {
				t.Errorf("Expected key3 status %s, got %s", OrderStatusToCreate, so.Status)
			}
			if so.Retry != 0 {
				t.Errorf("Expected key3 retry count 0, got %d", so.Retry)
			}
		} else {
			t.Error("Expected to find sub order for key3")
		}
	})

	t.Run("save sub orders error", func(t *testing.T) {
		wrapper := &mockWrapper{}
		modelService := &mockModelService{
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return []*SubOrderRecord{}, nil
			},
			saveSubOrdersFunc: func(ctx context.Context, orders []*SubOrderRecord) error {
				return errors.New("save error")
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.buildSubOrderRecord(ctx, order, request)
		if err == nil {
			t.Error("Expected error, got nil")
		}
	})
}

// Test cases for sendSubRequest method
func TestSubOrder_sendSubRequest(t *testing.T) {
	ctx := context.Background()
	order := &OrderRecord{
		OrderID: "test-order-id",
	}

	t.Run("successful send", func(t *testing.T) {
		wrapper := &mockWrapper{
			sendSubFunc: func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (realRequest interface{}, orderID string, err error) {
				return map[string]interface{}{"real": "request"}, "bcc-order-id", nil
			},
		}
		subOrders := []*SubOrderRecord{
			{
				OrderID:    "sub-order-1",
				Status:     OrderStatusToCreate,
				Parameters: `{"param": "value"}`,
				Retry:      0,
			},
		}
		modelService := &mockModelService{}
		subOrder := createTestSubOrder(wrapper, modelService)

		err := subOrder.sendSubRequest(ctx, subOrders, order)
		if err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
		if subOrders[0].Status != OrderStatusCreating {
			t.Errorf("Expected status %s, got %s", OrderStatusCreating, subOrders[0].Status)
		}
		if subOrders[0].BccOrderID != "bcc-order-id" {
			t.Errorf("Expected BccOrderID 'bcc-order-id', got %s", subOrders[0].BccOrderID)
		}
	})

	t.Run("send sub error", func(t *testing.T) {
		wrapper := &mockWrapper{
			sendSubFunc: func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (realRequest interface{}, orderID string, err error) {
				return nil, "", errors.New("send error")
			},
		}
		subOrders := []*SubOrderRecord{
			{
				OrderID:    "sub-order-1",
				Status:     OrderStatusToCreate,
				Parameters: `{"param": "value"}`,
				Retry:      0,
			},
		}
		modelService := &mockModelService{}
		subOrder := createTestSubOrder(wrapper, modelService)

		err := subOrder.sendSubRequest(ctx, subOrders, order)
		if err == nil {
			t.Error("Expected error, got nil")
		}
		if subOrders[0].Status != OrderStatusError {
			t.Errorf("Expected status %s, got %s", OrderStatusError, subOrders[0].Status)
		}
	})

	t.Run("save sub orders error", func(t *testing.T) {
		wrapper := &mockWrapper{}
		subOrders := []*SubOrderRecord{
			{
				OrderID:    "sub-order-1",
				Status:     OrderStatusToCreate,
				Parameters: `{"param": "value"}`,
				Retry:      0,
			},
		}
		modelService := &mockModelService{
			saveSubOrdersFunc: func(ctx context.Context, orders []*SubOrderRecord) error {
				return errors.New("save error")
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		err := subOrder.sendSubRequest(ctx, subOrders, order)
		if err == nil {
			t.Error("Expected error, got nil")
		}
	})

	t.Run("skip non-to-create orders", func(t *testing.T) {
		wrapper := &mockWrapper{}
		subOrders := []*SubOrderRecord{
			{
				OrderID: "sub-order-1",
				Status:  OrderStatusCreating, // Not to_create
			},
		}
		modelService := &mockModelService{}
		subOrder := createTestSubOrder(wrapper, modelService)

		err := subOrder.sendSubRequest(ctx, subOrders, order)
		if err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
	})
}

// Test cases for Send method
func TestSubOrder_Send(t *testing.T) {
	ctx := context.Background()
	meta := createTestMeta()
	request := map[string]interface{}{"test": "request"}

	t.Run("wrapper is nil", func(t *testing.T) {
		subOrder := createTestSubOrder(nil, &mockModelService{})
		_, err := subOrder.Send(ctx, meta, request)
		if err == nil || err.Error() != "wrapper is nil" {
			t.Errorf("Expected 'wrapper is nil' error, got: %v", err)
		}
	})

	t.Run("new order creation success", func(t *testing.T) {
		wrapper := &mockWrapper{}
		modelService := &mockModelService{
			getOrderByTaskIDFunc: func(ctx context.Context, orderID string) (*OrderRecord, error) {
				return nil, nil // No existing order
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		orderID, err := subOrder.Send(ctx, meta, request)
		if err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
		if orderID == "" {
			t.Error("Expected non-empty orderID")
		}
	})

	t.Run("existing order with to_create status", func(t *testing.T) {
		wrapper := &mockWrapper{}
		existingOrder := &OrderRecord{
			OrderID: "existing-order-id",
			Status:  OrderStatusToCreate,
			TaskID:  meta.TaskID,
		}
		modelService := &mockModelService{
			getOrderByTaskIDFunc: func(ctx context.Context, orderID string) (*OrderRecord, error) {
				return existingOrder, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		orderID, err := subOrder.Send(ctx, meta, request)
		if err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
		if orderID != existingOrder.OrderID {
			t.Errorf("Expected orderID %s, got %s", existingOrder.OrderID, orderID)
		}
	})

	t.Run("model service get order error", func(t *testing.T) {
		wrapper := &mockWrapper{}
		modelService := &mockModelService{
			getOrderByTaskIDFunc: func(ctx context.Context, orderID string) (*OrderRecord, error) {
				return nil, errors.New("database error")
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.Send(ctx, meta, request)
		if err == nil {
			t.Error("Expected error, got nil")
		}
	})

	t.Run("model service save order error", func(t *testing.T) {
		wrapper := &mockWrapper{}
		modelService := &mockModelService{
			getOrderByTaskIDFunc: func(ctx context.Context, orderID string) (*OrderRecord, error) {
				return nil, nil // No existing order
			},
			saveOrderFunc: func(ctx context.Context, order *OrderRecord) (*OrderRecord, error) {
				return nil, errors.New("save error")
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.Send(ctx, meta, request)
		if err == nil {
			t.Error("Expected error, got nil")
		}
	})
}

// Test cases for Query method
func TestSubOrder_Query(t *testing.T) {
	ctx := context.Background()
	orderID := "test-order-id"

	t.Run("wrapper is nil", func(t *testing.T) {
		subOrder := createTestSubOrder(nil, &mockModelService{})
		_, err := subOrder.Query(ctx, orderID)
		if err == nil || err.Error() != "wrapper is nil" {
			t.Errorf("Expected 'wrapper is nil' error, got: %v", err)
		}
	})

	t.Run("order not found", func(t *testing.T) {
		wrapper := &mockWrapper{}
		modelService := &mockModelService{
			getOrderByIDFunc: func(ctx context.Context, orderID string) (*OrderRecord, error) {
				return nil, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.Query(ctx, orderID)
		if err == nil {
			t.Error("Expected error for order not found, got nil")
		}
	})

	t.Run("model service get order error", func(t *testing.T) {
		wrapper := &mockWrapper{}
		modelService := &mockModelService{
			getOrderByIDFunc: func(ctx context.Context, orderID string) (*OrderRecord, error) {
				return nil, errors.New("database error")
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.Query(ctx, orderID)
		if err == nil {
			t.Error("Expected error, got nil")
		}
	})

	t.Run("order status to_create", func(t *testing.T) {
		wrapper := &mockWrapper{}
		order := &OrderRecord{
			OrderID: orderID,
			Status:  OrderStatusToCreate,
		}
		modelService := &mockModelService{
			getOrderByIDFunc: func(ctx context.Context, orderID string) (*OrderRecord, error) {
				return order, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.Query(ctx, orderID)
		if err == nil {
			t.Error("Expected error for unexpected order status, got nil")
		}
	})

	t.Run("successful query with completed sub orders", func(t *testing.T) {
		wrapper := &mockWrapper{
			querySubFunc: func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (inOperation bool, response interface{}, err error) {
				return false, map[string]interface{}{"result": "success"}, nil
			},
		}
		order := &OrderRecord{
			OrderID: orderID,
			Status:  OrderStatusCreating,
		}
		subOrders := []*SubOrderRecord{
			{
				OrderID:    "sub-order-1",
				SplitKey:   "key1",
				BccOrderID: "bcc-order-1",
				Status:     OrderStatusCreating,
			},
		}
		modelService := &mockModelService{
			getOrderByIDFunc: func(ctx context.Context, orderID string) (*OrderRecord, error) {
				return order, nil
			},
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return subOrders, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		response, err := subOrder.Query(ctx, orderID)
		if err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
		if response == nil {
			t.Error("Expected non-nil response")
		}
	})

	t.Run("sub orders in operation", func(t *testing.T) {
		wrapper := &mockWrapper{
			querySubFunc: func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (inOperation bool, response interface{}, err error) {
				return true, nil, nil // In operation
			},
		}
		order := &OrderRecord{
			OrderID: orderID,
			Status:  OrderStatusCreating,
		}
		subOrders := []*SubOrderRecord{
			{
				OrderID:    "sub-order-1",
				SplitKey:   "key1",
				BccOrderID: "bcc-order-1",
				Status:     OrderStatusCreating,
			},
		}
		modelService := &mockModelService{
			getOrderByIDFunc: func(ctx context.Context, orderID string) (*OrderRecord, error) {
				return order, nil
			},
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return subOrders, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.Query(ctx, orderID)
		if err == nil {
			t.Error("Expected error for in operation, got nil")
		}
		if !cerrs.Is(err, ErrOrderInOperation) {
			t.Errorf("Expected ErrOrderInOperation, got: %v", err)
		}
	})

	t.Run("sub orders with error", func(t *testing.T) {
		wrapper := &mockWrapper{
			querySubFunc: func(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (inOperation bool, response interface{}, err error) {
				return false, nil, errors.New("sub order error")
			},
		}
		order := &OrderRecord{
			OrderID: orderID,
			Status:  OrderStatusCreating,
		}
		subOrders := []*SubOrderRecord{
			{
				OrderID:    "sub-order-1",
				SplitKey:   "key1",
				BccOrderID: "bcc-order-1",
				Status:     OrderStatusCreating,
			},
		}
		modelService := &mockModelService{
			getOrderByIDFunc: func(ctx context.Context, orderID string) (*OrderRecord, error) {
				return order, nil
			},
			getSubOrdersByOrderIDFunc: func(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
				return subOrders, nil
			},
		}
		subOrder := createTestSubOrder(wrapper, modelService)

		_, err := subOrder.Query(ctx, orderID)
		if err == nil {
			t.Error("Expected error for sub order error, got nil")
		}
		if !cerrs.Is(err, ErrOrderError) {
			t.Errorf("Expected ErrOrderError, got: %v", err)
		}
	})
}
