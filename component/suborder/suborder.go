package suborder

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

const (
	CodeErrorOrderInOperation = cerrs.CODE_RESOURCE_COMPO_BASE + 1001
	CodeErrorOrderError       = cerrs.CODE_RESOURCE_COMPO_BASE + 1002
)

type Wrapper interface {
	RequestSplit(ctx context.Context, order *OrderRecord, request interface{}) (map[string]interface{}, error)
	ResponseMerger(ctx context.Context, order *OrderRecord, resps map[string]interface{}) (interface{}, error)
	SendSub(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (realRequest interface{}, orderID string, err error)
	QuerySub(ctx context.Context, order *OrderRecord, subOrder *SubOrderRecord) (inOperation bool, response interface{}, err error)
}

type Meta struct {
	AppID  string `json:"app_id"`
	TaskID string `json:"task_id"`
	Azone  string `json:"azone"`
	UserID string `json:"user_id"`
}

type SubOrder struct {
	Wrapper      Wrapper
	ModelService ModelService
}

var (
	ErrOrderInOperation = cerrs.NewConst(CodeErrorOrderInOperation, "Order is in operation", false, nil)
	ErrOrderError       = cerrs.NewConst(CodeErrorOrderError, "Order is error", false, nil)
)

func (s *SubOrder) Send(ctx context.Context, meta *Meta, request interface{}) (orderID string, err error) {
	if s.Wrapper == nil {
		return "", fmt.Errorf("wrapper is nil")
	}
	order, err := s.ModelService.GetOrderByTaskID(ctx, meta.TaskID, meta.Azone)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get order by task id failed: %w", err)
		return "", fmt.Errorf("get order by task id failed: %w", err)
	}
	if order == nil {
		order = &OrderRecord{
			OrderID:    uuid.New().String(),
			CreatedAt:  time.Time{},
			Status:     OrderStatusToCreate,
			TaskID:     meta.TaskID,
			Azone:      meta.Azone,
			AppID:      meta.AppID,
			UserID:     meta.UserID,
			Parameters: base_utils.Format(request),
		}
		if _, err := s.ModelService.SaveOrder(ctx, order); err != nil {
			logger.ComponentLogger.Warning(ctx, "create order failed: %w", err)
			return "", fmt.Errorf("create order failed: %w", err)
		}
	}
	if order.Status == OrderStatusToCreate || order.Status == OrderStatusError {
		subOrders, err := s.buildSubOrderRecord(ctx, order, request)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "build sub order record failed: %w", err)
			return "", fmt.Errorf("build sub order record failed: %w", err)
		}
		if err := s.sendSubRequest(ctx, subOrders, order); err != nil {
			logger.ComponentLogger.Warning(ctx, "send sub request failed: %w", err)
			return "", fmt.Errorf("send sub request failed: %w", err)
		}
		order.Status = OrderStatusCreating
		if _, err := s.ModelService.SaveOrder(ctx, order); err != nil {
			logger.ComponentLogger.Warning(ctx, "update order status to creating failed: %w", err)
			return "", fmt.Errorf("update order status to creating failed: %w", err)
		}
	}
	return order.OrderID, nil
}

func (s *SubOrder) buildSubOrderRecord(ctx context.Context, order *OrderRecord, request interface{}) ([]*SubOrderRecord, error) {
	subOrders, err := s.ModelService.GetSubOrdersByOrderID(ctx, order.OrderID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get sub orders by order id failed: %w", err)
		return nil, fmt.Errorf("get sub orders by order id failed: %w", err)
	}
	subReqeusts, err := s.Wrapper.RequestSplit(ctx, order, request)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "request split failed: %w", err)
		return nil, fmt.Errorf("request split failed: %w", err)
	}
	curSubOrderMap := make(map[string]*SubOrderRecord)
	for _, subOrder := range subOrders {
		curSubOrderMap[subOrder.SplitKey] = subOrder
		subRequest, ok := subReqeusts[subOrder.SplitKey]
		if !ok {
			logger.ComponentLogger.Warning(ctx, "sub request not found for sub order: %s, splitKey: %s",
				subOrder.OrderID, subOrder.SplitKey)
			return nil, fmt.Errorf("sub request not found for sub order: %s, splitKey: %s",
				subOrder.OrderID, subOrder.SplitKey)
		}
		if subOrder.Status != OrderStatusError {
			continue
		}
		// 如果之前的子订单失败了，重新构建子订单
		subOrder.Status = OrderStatusToCreate
		subOrder.Parameters = base_utils.Format(subRequest)
		subOrder.CreatedAt = time.Now()
		subOrder.Retry += 1
	}
	for splitKey, subRequest := range subReqeusts {
		if _, ok := curSubOrderMap[splitKey]; ok {
			continue
		}
		// 如果当前的子订单不存在，创建新的子订单
		subOrders = append(subOrders, &SubOrderRecord{
			OrderID:     uuid.New().String(),
			SplitKey:    splitKey,
			POrderID:    order.OrderID,
			BccOrderID:  "",
			CreatedAt:   time.Now(),
			Parameters:  base_utils.Format(subRequest),
			Status:      OrderStatusToCreate,
			BccRequest:  "",
			BccResponse: "",
			ErrMsg:      "",
		})
	}
	if err := s.ModelService.SaveSubOrders(ctx, subOrders); err != nil {
		logger.ComponentLogger.Warning(ctx, "save sub orders failed: %w", err)
		return nil, fmt.Errorf("save sub orders failed: %w", err)
	}
	return subOrders, nil
}

func (s *SubOrder) sendSubRequest(ctx context.Context, subOrders []*SubOrderRecord, order *OrderRecord) error {
	for _, subOrder := range subOrders {
		if subOrder.Status != OrderStatusToCreate {
			continue
		}
		realRequest, recvOrderID, err := s.Wrapper.SendSub(ctx, order, subOrder)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "send sub request failed: %w", err)
			subOrder.Status = OrderStatusError
			subOrder.ErrMsg = fmt.Sprintf("send sub request failed: %s", err.Error())
		} else {
			subOrder.Status = OrderStatusCreating
			subOrder.BccOrderID = recvOrderID
			subOrder.BccRequest = base_utils.Format(realRequest)
		}
		if err := s.ModelService.SaveSubOrders(ctx, []*SubOrderRecord{subOrder}); err != nil {
			logger.ComponentLogger.Warning(ctx, "save sub order failed: %w, subOrderID: %s", err, subOrder.OrderID)
			return fmt.Errorf("save sub order failed: %w", err)
		}
		if err != nil {
			return fmt.Errorf("send sub request failed: %w", err)
		}
	}
	return nil
}

func (s *SubOrder) Query(ctx context.Context, orderID string) (response interface{}, err error) {
	if s.Wrapper == nil {
		return nil, fmt.Errorf("wrapper is nil")
	}
	order, err := s.ModelService.GetOrderByID(ctx, orderID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get order by id failed", logit.Error("err", err))
		return nil, fmt.Errorf("get order by id failed: %w", err)
	}
	if order == nil {
		return nil, fmt.Errorf("order not found for order id: %s", orderID)
	}
	if order.Status == OrderStatusToCreate {
		logger.ComponentLogger.Warning(ctx, "order status is not expect, %s", order.Status)
		return nil, fmt.Errorf("order status is not expect, %s", order.Status)
	}
	resp, queryErr := s.querySubAndGetResp(ctx, order)
	if queryErr != nil && cerrs.Is(queryErr, ErrOrderError) {
		order.Status = OrderStatusError
		order.ErrMsg = queryErr.Error()
		if _, err := s.ModelService.SaveOrder(ctx, order); err != nil {
			logger.ComponentLogger.Warning(ctx, "save order failed: %w", err)
			return nil, fmt.Errorf("save order failed: %w", err)
		}
		return resp, queryErr
	}
	if queryErr == nil {
		order.Status = OrderStatusCreated
		if _, err := s.ModelService.SaveOrder(ctx, order); err != nil {
			logger.ComponentLogger.Warning(ctx, "save order status to created failed: %w", err)
			return nil, fmt.Errorf("save order status to created failed: %w", err)
		}
		logger.ComponentLogger.Notice(ctx, "order %s status updated to created", order.OrderID)
	}
	return resp, queryErr
}

func (s *SubOrder) querySubAndGetResp(ctx context.Context, order *OrderRecord) (response interface{}, err error) {
	subOrders, err := s.ModelService.GetSubOrdersByOrderID(ctx, order.OrderID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get sub orders by order id failed: %w", err)
		return nil, fmt.Errorf("get sub orders by order id failed: %w", err)
	}
	if len(subOrders) == 0 {
		logger.ComponentLogger.Warning(ctx, "no sub orders found for order id: %s", order.OrderID)
		return nil, fmt.Errorf("no sub orders found for order id: %s", order.OrderID)
	}
	resps := make(map[string]interface{})
	isError := false
	isInOperation := false
	for _, subOrder := range subOrders {
		switch order.Status {
		case OrderStatusError:
			isError = true
		case OrderStatusCreated, OrderStatusCreating:
			// pass
		default:
			logger.ComponentLogger.Warning(ctx, "sub order status is not expect, %s", order.Status)
			return nil, fmt.Errorf("sub order status is not expect, %s", order.Status)
		}
		inOperation, response, err := s.Wrapper.QuerySub(ctx, order, subOrder)
		if inOperation {
			isInOperation = true
			logger.ComponentLogger.Notice(ctx, "query sub order in-operation for subOrderID: %s bccOrderID: %s, error: %w, resp: %s",
				subOrder.OrderID, subOrder.BccOrderID, err, response)
		} else {
			subOrder.BccResponse = base_utils.Format(response)
			if err != nil {
				isError = true
				logger.ComponentLogger.Warning(ctx, "query sub order failed for subOrderID: %s, bccOrderID: %s, error: %w, resp: %s",
					subOrder.OrderID, subOrder.BccOrderID, err, response)
				subOrder.Status = OrderStatusError
				subOrder.ErrMsg = err.Error()
			} else {
				subOrder.Status = OrderStatusCreated
				resps[subOrder.SplitKey] = response
			}
			if err := s.ModelService.SaveSubOrders(ctx, []*SubOrderRecord{subOrder}); err != nil {
				logger.ComponentLogger.Warning(ctx, "save sub order failed", logit.Error("err", err),
					logit.String("subOrderID", subOrder.OrderID))
				return nil, fmt.Errorf("save sub order failed: %w", err)
			}
		}
	}
	if isInOperation {
		logger.ComponentLogger.Notice(ctx, "order %s has sub orders in operation, waiting for next query", order.OrderID)
		return nil, ErrOrderInOperation.Wrap(fmt.Errorf("order %s has sub orders in operation", order.OrderID))
	}
	if isError {
		errCnt := 0
		var firstErrMsg string
		for _, subOrder := range subOrders {
			if subOrder.Status == OrderStatusError {
				errCnt++
				if firstErrMsg == "" {
					firstErrMsg = subOrder.ErrMsg
				}
			}
		}
		return nil, ErrOrderError.Wrap(fmt.Errorf("order %s has %d sub orders in error status, first error: %s",
			order.OrderID, errCnt, firstErrMsg))
	}
	return s.Wrapper.ResponseMerger(ctx, order, resps)
}

func NewSubOrder(wrapper Wrapper, modelService ModelService) *SubOrder {
	if wrapper == nil || modelService == nil {
		logger.ComponentLogger.Error(nil, "wrapper or modelService is nil")
		return nil
	}
	return &SubOrder{
		Wrapper:      wrapper,
		ModelService: modelService,
	}
}
