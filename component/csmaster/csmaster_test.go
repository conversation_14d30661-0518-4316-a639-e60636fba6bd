package csmaster

import (
	"context"
	"errors"
	"fmt"
	"testing"

	. "github.com/agiledragon/gomonkey/v2"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

type MockCsmasterSdk struct {
}

func (m MockCsmasterSdk) UpdateClusterModel(ctx context.Context,
	req *csmaster.CsmasterClusterRequest) (rsp *csmaster.CsmasterClusterResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) GetClusterModel(ctx context.Context,
	req *csmaster.CsmasterClusterRequest) (rsp *csmaster.CsmasterClusterResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) SaveInstanceModels(ctx context.Context,
	req *csmaster.CsmasterInstancesRequest) (resp *csmaster.CsmasterInstancesResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) GetInstanceModels(ctx context.Context,
	req *csmaster.CsmasterInstancesRequest) (resp *csmaster.CsmasterInstancesResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) DeleteInstanceModels(ctx context.Context,
	req *csmaster.CsmasterInstancesRequest) (resp *csmaster.CsmasterInstancesResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) UpdateAclStatus(ctx context.Context,
	req *csmaster.CsmasterAclStatusUpdateRequest) (resp *csmaster.CsmasterAclStatusUpdateResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) GetAclUserList(ctx context.Context,
	req *csmaster.CsmasterAclGetUserListRequest) (resp *csmaster.CsmasterAclGetUserListResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) UpdateBackupRecords(ctx context.Context,
	req *csmaster.CsmasterUpdateBackupRecordsRequest) (resp *csmaster.CsmasterUpdateBackupRecordsResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) GetWhiteIPs(ctx context.Context,
	req *csmaster.CsmasterGetWhiteIPsRequest) (resp *csmaster.CsmasterGetWhiteIPsResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) CsmasterAclActions(ctx context.Context,
	req *csmaster.CsmasterAclActionsRequest) (rsp *csmaster.CsmasterAclActionsResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) CsmasterSetExpectVersion(ctx context.Context,
	req *csmaster.CsmasterSetExpectVersionRequest) (rsp *csmaster.CsmasterSetExpectVersionResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) CsmasterModifyClientAuth(ctx context.Context,
	req *csmaster.CsmasterModifyClientAuthRequest) (rsp *csmaster.CsmasterModifyClientAuthResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) CsmasterAdapter(ctx context.Context, req ghttp.Request) (*csmaster.CsmasterAdapterResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) CsmasterGetClusterDetail(ctx context.Context,
	req *csmaster.CsmasterGetClusterDetailRequest) (rsp *csmaster.ClusterDetailResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) CsmasterModifyConfigInfo(ctx context.Context,
	req *csmaster.ModifyConfigRequest) (resp *csmaster.ModifyConfigResponse, err error) {
	return &csmaster.ModifyConfigResponse{
		Code:    0,
		Message: "SUCCESS",
	}, nil
}

func (m MockCsmasterSdk) CsmasterCreateOrder(ctx context.Context,
	req *csmaster.CreateOrderRequest) (resp *csmaster.CreateOrderResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) ResetShardFailoverFlag(ctx context.Context,
	req *csmaster.ResetShardFailoverFlagRequest) (resp *csmaster.ResetShardFailoverFlagResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) CsmasterGetClusterConfigInfo(ctx context.Context,
	req *csmaster.SimpleCacheClusterReq) (resp *csmaster.GetConfigInfoResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) CsmasterNotEnableReplace(ctx context.Context,
	req *csmaster.NotEnableReplaceRequestForOP) (resp *csmaster.CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) SetClusterStaleReadable(ctx context.Context,
	req *csmaster.SetStaleReadReq) (resp *csmaster.CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) SetClusterQps(ctx context.Context,
	req *csmaster.SetClusterQpsReq) (resp *csmaster.CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) ModifyClusterIpWhitelist(ctx context.Context,
	req *csmaster.ModifyIpListRequest) (rsp *csmaster.CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) ListClusterIpWhitelist(ctx context.Context,
	req *csmaster.SimpleCacheClusterReq) (rsp *csmaster.ListWhiteIpResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) UpdateGroupInfo(ctx context.Context,
	req *csmaster.UpdateGroupRequest) (rsp *csmaster.CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) SetClusterSlot(ctx context.Context,
	req *csmaster.SetClusterSlotRequest) (rsp *csmaster.CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) GetClusterSlot(ctx context.Context,
	req *csmaster.SimpleCacheClusterReq) (rsp *csmaster.SlotInfoResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) SetClusterAsSlave(ctx context.Context,
	req *csmaster.SetClusterAsSlaveReq) (rsp *csmaster.CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) SetClusterAsMaster(ctx context.Context,
	req *csmaster.SimpleCacheClusterReq) (rsp *csmaster.CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) ListCacheCluster(ctx context.Context,
	req *csmaster.SimpleCacheClusterReq) (rsp *csmaster.ListCacheClusterRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) SetClusterForbiddenWrite(ctx context.Context,
	req *csmaster.SimpleCacheClusterReq) (rsp *csmaster.CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) SetClusterWrite(ctx context.Context,
	req *csmaster.SimpleCacheClusterReq) (rsp *csmaster.CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) GetSyncStatus(ctx context.Context,
	req *csmaster.SimpleCacheClusterReq) (rsp *csmaster.GetSyncStatusResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) GetVpcSgIdsByCluster(ctx context.Context,
	req *csmaster.SimpleCacheClusterReq) (rsp *csmaster.GetSecurityGroupIdsResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) GetClusterInfoKeyspace(ctx context.Context,
	req *csmaster.SimpleCacheClusterReq) (rsp *csmaster.ClusterInfoKeyspaceResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) AddNewTask(ctx context.Context,
	req *csmaster.AddNewTaskReq) (rsp *csmaster.X1CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) SlaveOfMaster(ctx context.Context,
	req *csmaster.SlaveOfMasterRequest) (rsp *csmaster.SlaveOfMasterResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) SlaveOfNoOne(ctx context.Context,
	req *csmaster.SlaveOfNoOneRequest) (rsp *csmaster.X1CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) FindNewMaster(ctx context.Context,
	req *csmaster.FindNewMasterRequest) (rsp *csmaster.FindNewMasterResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) UpdateInnerSecurity(ctx context.Context,
	req *csmaster.UpdateInnerSecurityRequest) (rsp *csmaster.X1CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) GetClusterInfoReplication(ctx context.Context,
	req *csmaster.SimpleCacheClusterReq) (rsp *csmaster.ClusterInfoReplicationResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) UpdateSecurity(ctx context.Context,
	req *csmaster.UpdateInnerSecurityRequest) (rsp *csmaster.X1CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) UpdateRoSecurity(ctx context.Context,
	req *csmaster.UpdateInnerSecurityRequest) (rsp *csmaster.X1CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) DeleteSyncGroupPoint(ctx context.Context,
	req *csmaster.SimpleCacheClusterReq) (resp *csmaster.X1CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) GetClusterStatus(ctx context.Context,
	req *csmaster.SimpleCacheClusterReq) (rsp *csmaster.GetClusterStatusResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) ModifySyncChannel(ctx context.Context,
	req *csmaster.ModifySyncChannelReq) (resp *csmaster.X1CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) GetSyncDelay(ctx context.Context,
	req *csmaster.GetSyncGroupDelayRequest) (resp *csmaster.GetSyncGroupDelayResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) GetSyncDelayTime(ctx context.Context,
	req *csmaster.GetSyncGroupDelayRequest) (resp *csmaster.GetSyncGroupDelayResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) ModifySyncGroup(ctx context.Context,
	req *csmaster.ModifySyncGroupRequest) (resp *csmaster.X1CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) CsmasterBackupAction(ctx context.Context,
	req *csmaster.BackupActionReq) (resp *csmaster.BackupActionResp, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) CsmasterCreateBigkeyTask(ctx context.Context,
	params *csmaster.CreateBigkeyTaskReq) (*csmaster.CreateBigkeyTaskResp, error) {
	//TODO implement me
	panic("implement me")
}
func (m MockCsmasterSdk) OperateSyncGroupBcmResource(ctx context.Context, req *csmaster.SyncGroupBcmResourceRequest) (resp *csmaster.X1CommonRes, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockCsmasterSdk) GetClusterDetailFromCsmaster(ctx context.Context,
	req *csmaster.CsmasterGetClusterDetailRequest) (rsp *csmaster.ClusterDetailResponse, err error) {
	return nil, nil
}

func (m MockCsmasterSdk) RestartMcProxy(ctx context.Context,
	req *csmaster.SimpleCacheClusterReq) (resp *csmaster.X1CommonRes, err error) {
	panic("implement me")
}

func (m MockCsmasterSdk) DealTimeWindowTask(ctx context.Context,
	req *csmaster.DealTimeWindowTaskReq) (resp *csmaster.CommonRes, err error) {
	return nil, errors.New("mock")
}

func (m MockCsmasterSdk) PutClusterTopoInXmaster(ctx context.Context,
	req *csmaster.PutClusterTopoInXmasterRequest) (resp *csmaster.X1CommonRes, err error) {
	return nil, errors.New("mock")
}

func Test_csmasterOp_CsmasterModifyConfigInfo(t *testing.T) {
	m := &csmasterOp{
		csmsaterSdk: &MockCsmasterSdk{},
	}
	if err := m.CsmasterModifyConfigInfo(context.Background(), &ModifyConfigParam{
		ConfItem: &ConfItem{
			ConfName:   "a",
			ConfModule: 0,
			ConfValue:  "a",
		},
		UserID: "mock",
		AppID:  "mock-app",
		From:   "admin",
	}); err != nil {
		t.Error(err)
	}
}

func Test_csmasterOp_CsmasterBackupAction(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	m := &csmasterOp{
		csmsaterSdk: &MockCsmasterSdk{},
	}
	resp, err := m.CsmasterBackupAction(context.Background(), &BackupActionReq{
		UserID:             "mock",
		CacheClusterShowId: "mock-app",
		Action:             "list_records",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(resp)
}

func Test_csmasterOp_CsmasterCreateBigkeyTask(t *testing.T) {
	m := &csmasterOp{
		csmsaterSdk: &MockCsmasterSdk{},
	}
	resp, err := m.CsmasterCreateBigkeyTask(context.Background(), &CreateBigkeyTaskReq{
		UserID:             "mock",
		CacheClusterShowId: "mock-app",
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(resp)
}

func Test_csmasterOp_GetClusterDetailFromCsmaster(t *testing.T) {
	m := &csmasterOp{
		csmsaterSdk: &MockCsmasterSdk{},
	}
	_, err := m.GetClusterDetailFromCsmaster(context.Background(), &ListCacheClusterInstancesParam{
		UserID: "mock",
		AppID:  "mock",
	})
	if err != nil {
		fmt.Println(err)
	}
}

func Test_csmasterOp_DealTimeWindowTask(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	patch := ApplyFunc(compo_utils.GetOpenapiToken, func(ctx context.Context, userId string) (string, error) {
		return "", nil
	})
	defer patch.Reset()
	m := &csmasterOp{
		csmsaterSdk: &MockCsmasterSdk{},
	}
	err := m.DealTimeWindowTask(context.Background(), "", "", 1, "")
	if err == nil {
		t.Error("expect error")
	}
}
