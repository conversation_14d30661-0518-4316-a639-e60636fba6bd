/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建BCC 资源
*/

package csmaster

import (
	"context"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
)

const (
	CsmasterVersionCluser     int32 = 5001
	CsmasterVersionStandalone int32 = 7001
)

const (
	CsmasterStatusRunning       int32 = 5
	CsmasterStatusCreatedFailed int32 = 12
	CsmasterStatusPause         int32 = 8
	CsmasterStatusDeleted       int32 = 10
	CsmasterStatusModifyFailed  int32 = 16
	CsmasterStatusReplacing     int32 = 17
	CsmasterStatusMigrateToX1   int32 = 24
)

const (
	CsmasterAclActionCreateDefaultUser     = "create_default_user"
	CsmasterAclActionCreate                = "create"
	CsmasterAclActionCreateModifyPassword  = "modify_passwd"
	CsmasterAclActionCreateModifyAuthority = "modify_authority"
	CsmasterAclActionCreateModifyExtra     = "modify_extra"
)

const (
	CsmasterSetExpectVersionLatest   = "latest"
	CsmasterSetExpectVersionRelaunch = "relaunch"
)

const (
	CsmasterNotEnableReplaceActionAdd = "add"
	CsmasterNotEnableReplaceActionDel = "delete"
	CsmasterNotEnableReplacePasswd    = "scs_modify"
)

const (
	CsmasterSetHAServicerUseCsmaster = 0
	CsmasterSetHAServicerUseXmaster  = 1
)

type csmasterOp struct {
	csmsaterSdk csdk.CsmasterService
	stsSdk      sts.StsService
}

type CsmasterOpIface interface {
	UpdateClusterModel(ctx context.Context, params *UpdateClusterModelParams) error
	GetClusterModel(ctx context.Context, userId string, appId string) (*csdk.CsmasterCluster, error)
	SaveInstanceModels(ctx context.Context, params *SaveInstancesParams) error
	GetInstanceModels(ctx context.Context, userId string, appId string) ([]*csdk.CsmasterInstance, error)
	DeleteInstanceModels(ctx context.Context, params *DeleteInstanceParams) error
	UpdateAclStatus(ctx context.Context, params *UpdateAclParams) error
	UpdateBackupRecordsStatus(ctx context.Context, params *UpdateBackupRecordsStatusParams) error
	GetWhiteIPs(ctx context.Context, userId string, appId string) ([]string, error)
	CsmasterAclActions(ctx context.Context, params *CsmasterAclActionsParams) error
	CsmasterSetExpectVersion(ctx context.Context, params *CsmasterSetExpectVersionParams) error
	CsmasterModifyClientAuth(ctx context.Context, req *CsmasterModifyClientAuthParam) error
	CsmasterGetClusterDetail(ctx context.Context, req *ListCacheClusterInstancesParam) (*csdk.ClusterDetailResponse, error)
	GetClusterDetailFromCsmaster(ctx context.Context, req *ListCacheClusterInstancesParam) (*csdk.ClusterDetailResponse, error)
	CsmasterModifyConfigInfo(ctx context.Context, req *ModifyConfigParam) error
	CreateOrder(ctx context.Context, req *CreateOrderParam) error
	SlaveOfMaster(ctx context.Context, params *SetSlaveOfParam) error
	SlaveOfNoOne(ctx context.Context, params *SetSlaveOfParam) error
	ResetShardFailoverFlag(ctx context.Context, params *ResetShardFailoverFlagParam) error
	DeleteGroupId(ctx context.Context, appId string, userId string, role int32, groupId string) error
	GetClusterStatus(ctx context.Context, appId string, userId string) (int32, error)
	ModifySyncChannel(ctx context.Context, req *ModifySyncChannelParam) error
	ModifyClusterIPWhitelist(ctx context.Context, req *ModifyClusterIPWhitelistParam) error
	DeleteSyncGroupPoint(ctx context.Context, req *DeleteSyncPointParam) error
	GetSyncGroupDelay(ctx context.Context, req *GetSyncGroupDelayParam) (*csdk.GetSyncGroupDelayResponse, error)
	ModifySyncGroup(ctx context.Context, req *ModifySyncGroupParam) error
	CsmasterNotEnableReplace(ctx context.Context, req *NotEnableReplaceParam) error
	CsmasterBackupAction(ctx context.Context, params *BackupActionReq) (*csdk.BackupActionResp, error)
	CsmasterCreateBigkeyTask(ctx context.Context, params *CreateBigkeyTaskReq) (*csdk.CreateBigkeyTaskResp, error)
	RestartMcProxy(ctx context.Context, userId string, appId string) error
	DealTimeWindowTask(ctx context.Context, userId string, clusterShowID string, taskId int64, action string) error
}

type (
	CsmasterCluster = csdk.CsmasterCluster

	CsmasterInstance = csdk.CsmasterInstance

	ConfItem = csdk.ConfItem

	ClusterDetailResponse = csdk.ClusterDetailResponse
)

type UpdateClusterModelParams struct {
	Model          *csdk.CsmasterCluster
	UserID         string
	AppID          string
	RequiredFields []string `json:"requiredFields"`
}

type SaveInstancesParams struct {
	Models         []*csdk.CsmasterInstance
	UserID         string
	AppID          string
	RequiredFields []string `json:"requiredFields"`
}

type DeleteInstanceParams struct {
	IDs             []string
	UserID          string
	AppID           string
	StatusForDelete string
}

type AclStatus struct {
	UserName string
	Status   string
}

type UpdateAclParams struct {
	UserID        string
	AppID         string
	AclStatusList []*AclStatus
}

type UpdateBackupRecordsStatusParams struct {
	UserID  string
	AppID   string
	Records []*csdk.CsmasterBackupRecord
}

type CsmasterAclActionsParams struct {
	UserID    string
	AppID     string
	AclAction string
	UserName  string
	UserType  int
	Password  string
	Extra     string
}

type CsmasterSetExpectVersionParams struct {
	UserID        string
	AppID         string
	KernelVersion string
	UpdateType    string
}

type CsmasterModifyClientAuthParam struct {
	UserID   string
	AppID    string
	Password string
}

// ListCacheClusterInstancesParam define listinstances param
type ListCacheClusterInstancesParam struct {
	UserID string
	AppID  string
}

// ModifyConfigParam define modifyConfigInfo param
type ModifyConfigParam struct {
	ConfItem *csdk.ConfItem
	UserID   string
	AppID    string
	From     string
}

// CreateOrderParam strcut  definition
type CreateOrderParam struct {
	UserID  string
	OrderID string
	APPID   string
	Action  int
}

type SetSlaveOfParam struct {
	UserID        string
	ShardGlobalID string
	MasterIp      string
	MasterPort    int
	AppID         string
	NewMasterId   string
}

type ResetShardFailoverFlagParam struct {
	UserID       string
	AppID        string
	ShardShortID int
	AppShortID   int
}

type ModifySyncChannelParam struct {
	UserID            string
	AppID             string
	Action            string
	PeerClusterShowID string
	PeerIP            string
	PeerPort          int
	PeerAuth          string
}

// ModifyClusterIPWhitelistParam ModifyClusterIpWhitelist  master.proto : message ModifyIpListRequest
type ModifyClusterIPWhitelistParam struct {
	UserID        string
	AppID         string
	Action        string
	ClusterIpList []string
}

// DeleteSyncPointParam definition
type DeleteSyncPointParam struct {
	UserID string
	AppID  string
}

// GetSyncGroupDelayParam definition
type GetSyncGroupDelayParam struct {
	UserID       string
	AppID        string
	DestClusters []csdk.DestInfo
}

// ModifySyncGroupParam definition
type ModifySyncGroupParam struct {
	UserID      string
	AppID       string
	SyncGroupID string
}

// NotEnableReplaceParam definition
type NotEnableReplaceParam struct {
	UserID    string
	Action    string
	ClusterID int64
	PassWD    string
}

type BackupActionReq struct {
	UserID             string                `json:"userId,omitempty"`
	CacheClusterShowId string                `json:"cacheClusterShowId,omitempty"`
	Action             string                `json:"action,omitempty"`
	AutoBackupConfig   string                `json:"autoBackupConfig,omitempty"`
	BackupId           int                   `json:"backupId,omitempty"`
	BatchId            string                `json:"batchId,omitempty"`
	Comment            string                `json:"comment,omitempty"`
	UrlExpiration      int                   `json:"urlExpiration,omitempty"`
	ListOrder          string                `json:"listOrder,omitempty"`
	Page               int                   `json:"page,omitempty"`
	PageSize           int                   `json:"pageSize,omitempty"`
	UpdateRecords      []*UpdateRecordParams `json:"update_records,omitempty"`
}

type CreateBigkeyTaskReq struct {
	UserID             string `json:"userId,omitempty"`
	CacheClusterShowId string `json:"cacheClusterShowId,omitempty"`
}

type UpdateRecordParams struct {
	InstID int    `json:"inst_id,omitempty"`
	Size   int    `json:"size,omitempty"`
	Status string `json:"status,omitempty"`
}

var once sync.Once

var csmasterOpObj CsmasterOpIface

func CsmasterOp() CsmasterOpIface {
	once.Do(func() {
		csmasterOpObj = &csmasterOp{
			csmsaterSdk: csdk.NewDefaultCsmasterSdk(),
			stsSdk:      sts.NewDefaultStsSdk(),
		}
	})
	return csmasterOpObj
}

// UpdateClusterModel 更新csmaster cache_cluster表的数据
// params中的AppID，分别对于x1.applicaiton.app_id 与 cache_cluster.cluster_show_id
func (m *csmasterOp) UpdateClusterModel(ctx context.Context, params *UpdateClusterModelParams) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	cReq := &csdk.CsmasterClusterRequest{
		Model:          params.Model,
		AppID:          params.AppID,
		Token:          token,
		RequiredFields: params.RequiredFields,
	}
	_, err = m.csmsaterSdk.UpdateClusterModel(ctx, cReq)
	return err
}

// GetClusterModel 获取csmaster cache_cluster表的数据
func (m *csmasterOp) GetClusterModel(ctx context.Context, userId string, appId string) (*csdk.CsmasterCluster, error) {
	token, err := compo_utils.GetOpenapiToken(ctx, userId)
	if err != nil {
		return nil, err
	}
	cReq := &csdk.CsmasterClusterRequest{
		AppID: appId,
		Token: token,
	}
	cResp, err := m.csmsaterSdk.GetClusterModel(ctx, cReq)
	if err != nil {
		return nil, err
	}
	return &cResp.Model, nil
}

// SaveInstanceModels 更新csmaster cache_instance表的数据
// 如果cache_instance表中没有，则会进行插入
func (m *csmasterOp) SaveInstanceModels(ctx context.Context, params *SaveInstancesParams) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	cReq := &csdk.CsmasterInstancesRequest{
		Models:         params.Models,
		AppID:          params.AppID,
		Token:          token,
		RequiredFields: params.RequiredFields,
	}
	_, err = m.csmsaterSdk.SaveInstanceModels(ctx, cReq)
	return err
}

// GetInstanceModels 获取csmaster cache_instance表的数据
func (m *csmasterOp) GetInstanceModels(ctx context.Context, userId string, appId string) ([]*csdk.CsmasterInstance, error) {
	token, err := compo_utils.GetOpenapiToken(ctx, userId)
	if err != nil {
		return nil, err
	}
	cReq := &csdk.CsmasterInstancesRequest{
		AppID: appId,
		Token: token,
	}
	cResp, err := m.csmsaterSdk.GetInstanceModels(ctx, cReq)
	if err != nil {
		return nil, err
	}
	return cResp.Models, err
}

// DeleteInstanceModels 删除csmaster cache_instance表的数据
func (m *csmasterOp) DeleteInstanceModels(ctx context.Context, params *DeleteInstanceParams) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	cReq := &csdk.CsmasterInstancesRequest{
		AppID:           params.AppID,
		Token:           token,
		StatusForDelete: params.StatusForDelete,
	}
	for _, id := range params.IDs {
		cReq.Models = append(cReq.Models, &csdk.CsmasterInstance{
			Uuid: id,
		})
	}
	_, err = m.csmsaterSdk.DeleteInstanceModels(ctx, cReq)
	return err
}

// UpdateAclStatus 更新Acl的状态
func (m *csmasterOp) UpdateAclStatus(ctx context.Context, params *UpdateAclParams) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	getReq := &csdk.CsmasterAclGetUserListRequest{
		AppID: params.AppID,
		Token: token,
	}
	getResp, err := m.csmsaterSdk.GetAclUserList(ctx, getReq)
	if err != nil {
		return err
	}
	for _, acl := range getResp.Acls {
		for _, s := range params.AclStatusList {
			// 当UpdateStatus=1，2（创建中、修改中）；更新状态为success
			// 当UpdateStatus=4（删除中）；更新状态为deleted
			// http://wiki.baidu.com/pages/viewpage.action?pageId=483599379#csmaster%E5%AF%B9%E5%A4%96API-%E5%A4%9A%E7%A7%9F%E6%88%B7%E7%9B%B8%E5%85%B3
			if acl.UserName == s.UserName &&
				(((acl.UpdateStatus == 1 || acl.UpdateStatus == 2) && s.Status == x1model.ACLStatusInUse) ||
					(acl.UpdateStatus == 4 && s.Status == x1model.ACLStatusDeleted)) {
				if _, err := m.csmsaterSdk.UpdateAclStatus(ctx, &csdk.CsmasterAclStatusUpdateRequest{
					UserName:    acl.UserName,
					ForceStatus: s.Status,
					AppID:       params.AppID,
					Token:       token,
				}); err != nil {
					return err
				}
				break
			}
		}
	}
	return nil
}

// UpdateBackupRecordsStatus 更新csmster中备份记录的状态
func (m *csmasterOp) UpdateBackupRecordsStatus(ctx context.Context, params *UpdateBackupRecordsStatusParams) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &csdk.CsmasterUpdateBackupRecordsRequest{
		Token:   token,
		AppID:   params.AppID,
		Records: params.Records,
	}
	_, err = m.csmsaterSdk.UpdateBackupRecords(ctx, req)
	return err
}

// 获取ip白名单
func (m *csmasterOp) GetWhiteIPs(ctx context.Context, userId string, appId string) ([]string, error) {
	token, err := compo_utils.GetOpenapiToken(ctx, userId)
	if err != nil {
		return nil, err
	}
	req := &csdk.CsmasterGetWhiteIPsRequest{
		Token: token,
		AppID: appId,
		Mode:  "all",
	}
	resp, err := m.csmsaterSdk.GetWhiteIPs(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.IPs, nil
}

func (m *csmasterOp) CsmasterAclActions(ctx context.Context, params *CsmasterAclActionsParams) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &csdk.CsmasterAclActionsRequest{
		Token:     token,
		AppID:     params.AppID,
		AclAction: params.AclAction,
		UserName:  params.UserName,
		UserType:  params.UserType,
		Password:  params.Password,
		Extra:     params.Extra,
	}
	_, err = m.csmsaterSdk.CsmasterAclActions(ctx, req)
	return err
}

func (m *csmasterOp) CsmasterSetExpectVersion(ctx context.Context, params *CsmasterSetExpectVersionParams) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &csdk.CsmasterSetExpectVersionRequest{
		Token:         token,
		AppID:         params.AppID,
		KernelVersion: params.KernelVersion,
		UpdateType:    params.UpdateType,
		IsDefer:       false,
	}
	_, err = m.csmsaterSdk.CsmasterSetExpectVersion(ctx, req)
	return err
}

func (m *csmasterOp) CsmasterModifyClientAuth(ctx context.Context, params *CsmasterModifyClientAuthParam) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &csdk.CsmasterModifyClientAuthRequest{
		Token:      token,
		AppID:      params.AppID,
		ClientAuth: params.Password,
	}
	_, err = m.csmsaterSdk.CsmasterModifyClientAuth(ctx, req)
	return err
}

func (m *csmasterOp) CsmasterGetClusterDetail(ctx context.Context, params *ListCacheClusterInstancesParam) (*csdk.ClusterDetailResponse, error) {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return nil, err
	}
	req := &csdk.CsmasterGetClusterDetailRequest{
		Token: token,
		AppID: params.AppID,
	}
	detail, err := m.csmsaterSdk.CsmasterGetClusterDetail(ctx, req)
	if err != nil {
		return nil, err
	}
	return detail, nil
}

func (m *csmasterOp) GetClusterDetailFromCsmaster(ctx context.Context, params *ListCacheClusterInstancesParam) (*csdk.ClusterDetailResponse, error) {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return nil, err
	}
	req := &csdk.CsmasterGetClusterDetailRequest{
		Token: token,
		AppID: params.AppID,
	}
	detail, err := m.csmsaterSdk.GetClusterDetailFromCsmaster(ctx, req)
	if err != nil {
		return nil, err
	}
	return detail, nil
}

func (m *csmasterOp) CsmasterModifyConfigInfo(ctx context.Context, params *ModifyConfigParam) error {
	var token string
	var err error
	if params.UserID != "mock" {
		token, err = compo_utils.GetOpenapiToken(ctx, params.UserID)
		if err != nil {
			return err
		}
	}
	req := &csdk.ModifyConfigRequest{
		Token:    token,
		AppID:    params.AppID,
		ConfItem: params.ConfItem,
		From:     params.From,
	}
	_, err = m.csmsaterSdk.CsmasterModifyConfigInfo(ctx, req)
	if err != nil {
		return err
	}
	return nil
}

func (m *csmasterOp) CreateOrder(ctx context.Context, params *CreateOrderParam) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &csdk.CreateOrderRequest{
		Token:   token,
		AppID:   params.APPID,
		OrderID: params.OrderID,
		Action:  params.Action,
	}
	_, err = m.csmsaterSdk.CsmasterCreateOrder(ctx, req)
	if err != nil {
		return err
	}
	return nil
}

func (m *csmasterOp) SlaveOfMaster(ctx context.Context, params *SetSlaveOfParam) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &csdk.SlaveOfMasterRequest{
		ShardGlobalID: params.ShardGlobalID,
		MasterIp:      params.MasterIp,
		MasterPort:    params.MasterPort,
		AppID:         params.AppID,
		Token:         token,
	}
	_, err = m.csmsaterSdk.SlaveOfMaster(ctx, req)
	if err != nil {
		return err
	}
	return nil
}

func (m *csmasterOp) SlaveOfNoOne(ctx context.Context, params *SetSlaveOfParam) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &csdk.SlaveOfNoOneRequest{
		ShardGlobalID: params.ShardGlobalID,
		NewMasterID:   params.NewMasterId,
		AppID:         params.AppID,
		Token:         token,
	}
	_, err = m.csmsaterSdk.SlaveOfNoOne(ctx, req)
	if err != nil {
		return err
	}
	return nil
}

func (m *csmasterOp) ResetShardFailoverFlag(ctx context.Context, params *ResetShardFailoverFlagParam) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &csdk.ResetShardFailoverFlagRequest{
		ClusterID: params.AppShortID,
		ShardID:   params.ShardShortID,
		AppID:     params.AppID,
		Token:     token,
	}
	_, err = m.csmsaterSdk.ResetShardFailoverFlag(ctx, req)
	if err != nil {
		return err
	}
	return nil
}

func (m *csmasterOp) DeleteGroupId(ctx context.Context, appId string, userId string, role int32, groupId string) error {
	token, err := compo_utils.GetOpenapiToken(ctx, userId)
	if err != nil {
		return err
	}
	req := csdk.UpdateGroupRequest{
		Action:             csdk.GlobalGroupActionDelete,
		GroupId:            groupId,
		CacheClusterShowId: appId,
		Token:              token,
		GroupRole:          role,
	}
	_, err = m.csmsaterSdk.UpdateGroupInfo(ctx, &req)
	if err != nil {
		return err
	}
	return nil
}

func (m *csmasterOp) GetClusterStatus(ctx context.Context, appId string, userId string) (int32, error) {
	token, err := compo_utils.GetOpenapiToken(ctx, userId)
	if err != nil {
		return 0, err
	}
	req := csdk.SimpleCacheClusterReq{
		CacheClusterShowId: appId,
		Token:              token,
	}
	resp, err := m.csmsaterSdk.GetClusterStatus(ctx, &req)
	if err != nil {
		return 0, err
	}
	return resp.Status, nil
}

func (m *csmasterOp) ModifySyncChannel(ctx context.Context, params *ModifySyncChannelParam) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := csdk.ModifySyncChannelReq{
		Action:            params.Action,
		AppID:             params.AppID,
		PeerClusterShowID: params.PeerClusterShowID,
		PeerIP:            params.PeerIP,
		PeerPort:          params.PeerPort,
		PeerAuth:          params.PeerAuth,
		Token:             token,
	}
	_, err = m.csmsaterSdk.ModifySyncChannel(ctx, &req)
	if err != nil {
		return err
	}
	return nil
}

func (m *csmasterOp) ModifyClusterIPWhitelist(ctx context.Context, params *ModifyClusterIPWhitelistParam) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := csdk.ModifyIpListRequest{
		ClusterIpList:      params.ClusterIpList,
		CacheClusterShowId: params.AppID,
		Action:             params.Action,
		Token:              token,
	}
	_, err = m.csmsaterSdk.ModifyClusterIpWhitelist(ctx, &req)
	if err != nil {
		return err
	}
	return nil
}

func (m *csmasterOp) DeleteSyncGroupPoint(ctx context.Context, params *DeleteSyncPointParam) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := csdk.SimpleCacheClusterReq{
		CacheClusterShowId: params.AppID,
		Token:              token,
	}
	_, err = m.csmsaterSdk.DeleteSyncGroupPoint(ctx, &req)
	if err != nil {
		return err
	}
	return nil
}

func (m *csmasterOp) GetSyncGroupDelay(ctx context.Context, params *GetSyncGroupDelayParam) (*csdk.GetSyncGroupDelayResponse, error) {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return nil, err
	}
	req := csdk.GetSyncGroupDelayRequest{
		SourceCluster: params.AppID,
		DestClusters:  params.DestClusters,
		Token:         token,
	}
	resp, err := m.csmsaterSdk.GetSyncDelay(ctx, &req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *csmasterOp) ModifySyncGroup(ctx context.Context, params *ModifySyncGroupParam) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := csdk.ModifySyncGroupRequest{
		CacheClusterShowID: params.AppID,
		SyncGroupShowID:    params.SyncGroupID,
		Token:              token,
	}
	_, err = m.csmsaterSdk.ModifySyncGroup(ctx, &req)
	if err != nil {
		return err
	}
	return nil
}

// CsmasterNotEnableReplace 更新控制集群维度自愈&切换开关
// params中的ClusterID，对应 cache_cluster.cluster_id
func (m *csmasterOp) CsmasterNotEnableReplace(ctx context.Context, params *NotEnableReplaceParam) error {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return err
	}
	cReq := &csdk.NotEnableReplaceRequestForOP{
		Token:     token,
		Action:    params.Action,
		ClusterID: params.ClusterID,
		PassWD:    params.PassWD,
	}
	_, err = m.csmsaterSdk.CsmasterNotEnableReplace(ctx, cReq)
	return err
}

func (m *csmasterOp) CsmasterBackupAction(ctx context.Context, params *BackupActionReq) (*csdk.BackupActionResp, error) {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return nil, err
	}

	updateRecoed := make([]*csdk.UpdateRecordParams, 0)
	if params.Action == "update_records" {
		for _, item := range params.UpdateRecords {
			updateRecoed = append(updateRecoed, &csdk.UpdateRecordParams{
				InstID: item.InstID,
				Size:   item.Size,
				Status: item.Status,
			})
		}
	}

	cReq := &csdk.BackupActionReq{
		Token:              token,
		CacheClusterShowId: params.CacheClusterShowId,
		Action:             params.Action,
		AutoBackupConfig:   params.AutoBackupConfig,
		BackupId:           params.BackupId,
		BatchId:            params.BatchId,
		Comment:            params.Comment,
		UrlExpiration:      params.UrlExpiration,
		ListOrder:          params.ListOrder,
		Page:               params.Page,
		PageSize:           params.PageSize,
		UpdateRecords:      updateRecoed,
	}
	resp, err := m.csmsaterSdk.CsmasterBackupAction(ctx, cReq)
	return resp, err
}

func (m *csmasterOp) CsmasterCreateBigkeyTask(ctx context.Context, params *CreateBigkeyTaskReq) (*csdk.CreateBigkeyTaskResp, error) {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		return nil, err
	}

	cReq := &csdk.CreateBigkeyTaskReq{
		Token:              token,
		CacheClusterShowId: params.CacheClusterShowId,
	}
	resp, err := m.csmsaterSdk.CsmasterCreateBigkeyTask(ctx, cReq)
	return resp, err
}

func (m *csmasterOp) RestartMcProxy(ctx context.Context, userId string, appId string) error {
	token, err := compo_utils.GetOpenapiToken(ctx, userId)
	if err != nil {
		return err
	}
	_, err = m.csmsaterSdk.RestartMcProxy(ctx, &csdk.SimpleCacheClusterReq{CacheClusterShowId: appId,
		Token: token})
	return err
}

func (m *csmasterOp) DealTimeWindowTask(ctx context.Context, userId string, clusterShowID string, taskId int64, action string) error {
	token, err := compo_utils.GetOpenapiToken(ctx, userId)
	if err != nil {
		return err
	}
	_, err = m.csmsaterSdk.DealTimeWindowTask(ctx,
		&csdk.DealTimeWindowTaskReq{Token: token, TaskID: taskId, Action: action, CacheClusterShowId: clusterShowID})
	return err
}
