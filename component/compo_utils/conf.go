package compo_utils

import (
	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/net/servicer"

	"icode.baidu.com/baidu/scs/x1-base/utils/mapstruct"
)

const IdcSpecialKey = "IDC_SPEC"

// LoadConf 给conf.Parse增加IDC特化
func LoadConf(confName string, obj interface{}) error {
	cf := map[string]interface{}{}
	if err := conf.Parse("component/"+confName+".toml", &cf); err != nil {
		return err
	}

	cf = mergeIdcSpecial(cf, env.IDC())

	return mapstruct.DecodeEx(cf, obj, true, "toml")
}

// RegisterIdcSpecConfigHookForServicer 为servicer的配置增加IDC特化
func RegisterIdcSpecConfigHookForServicer() {
	servicer.RegisterConfigHookFunc(IdcSpecialKey, -1,
		func(config map[string]interface{}) (map[string]interface{}, error) {
			return mergeIdcSpecial(config, env.IDC()), nil
		})
}

func mergeIdcSpecial(cf map[string]interface{}, idc string) map[string]interface{} {
	var merged = map[string]interface{}{}
	var idcSpecial map[string]interface{}

	for k, v := range cf {
		if _v, ok := v.(map[string]interface{}); ok {
			if k == IdcSpecialKey {
				if _v, has := _v[idc]; has {
					if _v, ok := _v.(map[string]interface{}); ok {
						idcSpecial = _v
					}
				}
				continue
			}

			v = mergeIdcSpecial(_v, idc)
		}

		merged[k] = v
	}

	for k, v := range idcSpecial {
		merged[k] = v
	}

	return merged
}
