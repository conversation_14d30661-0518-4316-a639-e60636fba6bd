package compo_utils

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"icode.baidu.com/baidu/gdp/env"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
)

type mockStsSdk struct {
}

func (m mockStsSdk) GetAssumeRole(ctx context.Context, req *sts.GetAssumeRoleRequest) (rsp *sts.GetAssumeRoleResponse, err error) {
	panic("implement me")
}

func (m mockStsSdk) GetEncryptResourceAccountId(ctx context.Context) (rsp *sts.EncryptResourceAccountIdResponse, err error) {
	panic("implement me")
}

func (m mockStsSdk) GetOpenApiAuth(ctx context.Context, req *sts.GetOpenApiAuthRequest) (rsp *sts.GetOpenApiAuthResponse, err error) {
	if strings.Contains(req.IamUserId, "error") {
		return nil, fmt.Errorf("mock error")
	}
	return &sts.GetOpenApiAuthResponse{
		Auth: &common.Authentication{
			IamUserId:     "mockIamUserId",
			TransactionId: "mockTransactionId",
			Credential: &common.Credential{
				Ak:           "mockAccessKeyId",
				Sk:           "mockSecretAccessKey",
				SessionToken: "mockSessionToken",
			},
			ResourceAccount: &common.ResourceAccount{
				ResourceAk:       "mockResourceAk",
				EncryptAccountId: "mockEncryptAccountId",
			},
		},
	}, nil
}

func getMockStsSdk() sts.StsService {
	return &mockStsSdk{}
}

func TestGetOpenapiAuthWithResourceAccount2(t *testing.T) {
	if _, err := GetOpenapiAuthWithResourceAccount2(context.Background(), "mockuser", getMockStsSdk()); err != nil {
		t.Errorf("GetOpenapiAuthWithResourceAccount2() error = %v", err)
	}
	if _, err := GetOpenapiAuthWithResourceAccount2(context.Background(), "mockusererror", getMockStsSdk()); err == nil {
		t.Errorf("GetOpenapiAuthWithResourceAccount2() expected error")
	}
}

func TestGetGMasterToken(t *testing.T) {
	type args struct {
		ctx    context.Context
		userId string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				ctx:    context.Background(),
				userId: "mockuser",
			},
			want:    "scs-private|2a5d355806e24876ab025d14529d26e5|mockuser",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.name == "test1" {
				env.Default = env.New(env.Option{
					IDC: "dbstacktest",
				})
			}
			got, err := GetGMasterToken(tt.args.ctx, tt.args.userId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGMasterToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetGMasterToken() got = %v, want %v", got, tt.want)
			}
		})
	}
	t.Run("test2", func(t *testing.T) {
		env.Default = env.New(env.Option{
			IDC: "test",
		})
		defer func() {
			if r := recover(); r == nil {
				t.Errorf("The code did not panic")
			}
		}()
		_, _ = GetGMasterToken(context.Background(), "mockuser")
	})
}
