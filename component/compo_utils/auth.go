package compo_utils

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

// GetOpenapiAuth 获取请求通用OpenAPI的auth信息 ()
func GetOpenapiAuth(ctx context.Context, userId string) (*common.Authentication, error) {
	stsSdk := getStsSdk()
	req := &sts.GetOpenApiAuthRequest{
		TransactionId: base_utils.GetReqID(ctx),
		IamUserId:     userId,
	}
	rsp, err := stsSdk.GetOpenApiAuth(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp.Auth, nil
}

// GetOpenapiAuthWithResourceAccount 获取请求通用OpenAPI的auth信息(携带resource account信息)
func GetOpenapiAuthWithResourceAccount(ctx context.Context, userId string) (*common.Authentication, error) {
	stsSdk := getStsSdk()
	req := &sts.GetOpenApiAuthRequest{
		TransactionId:       base_utils.GetReqID(ctx),
		IamUserId:           userId,
		NeedResourceAccount: true,
	}
	rsp, err := stsSdk.GetOpenApiAuth(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp.Auth, nil
}

func GetOpenapiAuthWithResourceAccount2(ctx context.Context, userId string, stsSdk sts.StsService) (*common.Authentication, error) {
	req := &sts.GetOpenApiAuthRequest{
		TransactionId:       base_utils.GetReqID(ctx),
		IamUserId:           userId,
		NeedResourceAccount: true,
	}
	rsp, err := stsSdk.GetOpenApiAuth(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp.Auth, nil
}

// GetOpenapiToken 获取请求通用OpenAPI的token
func GetOpenapiToken(ctx context.Context, userId string) (string, error) {
	stsSdk := getStsSdk()
	req := &sts.GetAssumeRoleRequest{
		TransactionId: base_utils.GetReqID(ctx),
		IamUserId:     userId,
	}
	rsp, err := stsSdk.GetAssumeRole(ctx, req)
	if err != nil {
		return "", err
	}
	return rsp.Token.Id, nil
}

// GetGMasterToken 获取请求global master的token
func GetGMasterToken(ctx context.Context, userId string) (string, error) {
	stsSdk := getGlobalStsSdk()
	req := &sts.GetAssumeRoleRequest{
		TransactionId: base_utils.GetReqID(ctx),
		IamUserId:     userId,
	}
	rsp, err := stsSdk.GetAssumeRole(ctx, req)
	if err != nil {
		return "", err
	}
	return rsp.Token.Id, nil
}

func getStsSdk() sts.StsService {
	if privatecloud.IsPrivateENV() {
		return privatecloud.GetPrivateStsSDK()
	}
	return sts.NewDefaultStsSdk()
}

func getGlobalStsSdk() sts.StsService {
	if privatecloud.IsPrivateENV() {
		return privatecloud.GetPrivateStsSDK()
	}
	return sts.NewStsSdk("global_sts")
}
