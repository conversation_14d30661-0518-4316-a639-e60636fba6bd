package compo_utils

import (
	"context"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/gdp/env"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

type TestConf struct {
	Key1 int    `toml:"key1"`
	Key2 string `toml:"key2"`
	Key3 struct {
		Key1 int    `toml:"key1"`
		Key2 string `toml:"key2"`
	} `toml:"key3"`
}

func changeIdc(idc string) {
	env.Default = env.New(env.Option{
		AppName: env.AppName(),
		IDC:     idc,
		RunMode: env.RunMode(),
		RootDir: env.RootDir(),
		DataDir: env.DataDir(),
		LogDir:  env.LogDir(),
		ConfDir: env.ConfDir(),
	})
}

func TestLoadConf(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	cf1 := &TestConf{}
	if err := LoadConf("test", cf1); err != nil {
		t.Fatalf("load conf fail, err: %s", err.Error())
	}

	fmt.Printf("%+v\n", *cf1)
	if cf1.Key1 != 1 || cf1.Key2 != "abc" || cf1.Key3.Key1 != 1 || cf1.Key3.Key2 != "abc" {
		t.Fatalf("conf value is wrong")
	}

	changeIdc("test_idc2")

	cf2 := &TestConf{}
	if err := LoadConf("test", cf2); err != nil {
		t.Fatalf("load conf fail, err: %s", err.Error())
	}

	fmt.Printf("%+v\n", *cf2)
	if cf2.Key1 != 2 || cf2.Key2 != "abc" || cf2.Key3.Key1 != 2 || cf2.Key3.Key2 != "bcd" {
		t.Fatalf("conf value is wrong")
	}

	changeIdc("test_idc3")

	cf3 := &TestConf{}
	if err := LoadConf("test", cf3); err != nil {
		t.Fatalf("load conf fail, err: %s", err.Error())
	}

	fmt.Printf("%+v\n", *cf3)
	if cf3.Key1 != 3 || cf3.Key2 != "abc" || cf3.Key3.Key1 != 1 || cf3.Key3.Key2 != "abc" {
		t.Fatalf("conf value is wrong")
	}

}

func init() {
	RegisterIdcSpecConfigHookForServicer()
}

func TestRegisterIdcSpecConfigHookForServicer(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	optTask, err := sdk_utils.GetRalOption("task-db")
	if err != nil {
		t.Fatalf(err.Error())
	}

	type mySQLConfig struct {
		Username string
		Password string
	}

	var configTask mySQLConfig
	_ = optTask.Get("MySQL", &configTask)
	if configTask.Password != "paastest123" {
		t.Fatalf("password wrong expect paastest123, got %s", configTask.Password)
	}

	optTest, err := sdk_utils.GetRalOption("test-db")
	if err != nil {
		t.Fatalf(err.Error())
	}

	var ConfigTest mySQLConfig
	_ = optTest.Get("MySQL", &ConfigTest)
	if ConfigTest.Password != "test" {
		t.Fatalf("password wrong expect test, got %s", ConfigTest.Password)
	}
}
