package compo_utils

// WARNING:必须先执行sts.MustInitMrStsSdk()
// WARNING:必须先执行sts.MustInitMrStsSdk()
// WARNING:必须先执行sts.MustInitMrStsSdk()
// WARNING:必须先执行sts.MustInitMrStsSdk()
import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

// MrGetOpenapiAuth 获取请求通用OpenAPI的auth信息 ()
// WARNING:必须确保已执行 sts.MustInitMrStsSdk()
func MrGetOpenapiAuth(ctx context.Context, userId string, region string) (*common.Authentication, error) {
	req := &sts.GetOpenApiAuthRequest{
		TransactionId: base_utils.GetReqID(ctx),
		IamUserId:     userId,
	}
	rsp, err := sts.MrStsSdk.GetOpenApiAuth(ctx, &sts.MrGetOpenApiAuthRequest{
		Req:    req,
		Region: region,
	})
	if err != nil {
		return nil, err
	}
	return rsp.Auth, nil
}

// MrGetOpenapiAuthWithResourceAccount 获取请求通用OpenAPI的auth信息(携带resource account信息)
// WARNING:必须确保已执行 sts.MustInitMrStsSdk()
func MrGetOpenapiAuthWithResourceAccount(ctx context.Context, userId string, region string) (*common.Authentication, error) {
	req := &sts.GetOpenApiAuthRequest{
		TransactionId:       base_utils.GetReqID(ctx),
		IamUserId:           userId,
		NeedResourceAccount: true,
	}
	rsp, err := sts.MrStsSdk.GetOpenApiAuth(ctx, &sts.MrGetOpenApiAuthRequest{
		Req:    req,
		Region: region,
	})
	if err != nil {
		return nil, err
	}
	return rsp.Auth, nil
}

// MrGetOpenapiToken 获取请求通用OpenAPI的token
// WARNING:必须确保已执行 sts.MustInitMrStsSdk())
func MrGetOpenapiToken(ctx context.Context, userId string, region string) (string, error) {
	req := &sts.GetAssumeRoleRequest{
		TransactionId: base_utils.GetReqID(ctx),
		IamUserId:     userId,
	}
	rsp, err := sts.MrStsSdk.GetAssumeRole(ctx, &sts.MrGetAssumeRoleRequest{
		Req:    req,
		Region: region,
	})
	if err != nil {
		return "", err
	}
	return rsp.Token.Id, nil
}
