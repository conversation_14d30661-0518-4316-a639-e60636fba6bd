/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* sync_group_test.go */
/*
modification history
--------------------
2023/07/14 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package sync_group

import (
	"context"
	"testing"

	"github.com/alicebob/miniredis/v2"

	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

func init() {
	unittest.UnitTestInit(2)
	t := single_redis.SingleClient{}
	MustLoadConf(context.Background(), &t, &t)
}

func TestMustLoadConf(t *testing.T) {
	println(config.IDCCODE)
}

func TestGetSyncGroupShardIDMap(t *testing.T) {
	GetSyncGroupShardIDMap(&x1model.Application{Clusters: []*x1model.Cluster{{ClusterShortID: 107239}}})
}

func TestGetSyncGroupShardIDByClusterShortID(t *testing.T) {
	println(GetSyncGroupShardIDByClusterShortID(107239))
}

func TestSyncChannel(t *testing.T) {
	ctx := context.Background()
	s := miniredis.RunT(t)
	DataServerInstance := DataServerImpl{r: single_redis.NewClient(s.Host(), s.Port())}

	if err := DataServerInstance.AddSyncChannel(ctx,
		"scs-bjtest-appid", "***********", "6379", "1234qwer"); err != nil {
		t.Errorf("error")
	}
	if err := DataServerInstance.DelSyncChannel(ctx,
		"scs-bjtest-appid", "***********", "6379", "1234qwer"); err != nil {
		t.Errorf("error")
	}
}
