/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* hash_BDRP_SYNC_PAUSE.go */
/*
modification history
--------------------
2023/07/15 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
BDRP_SYNC_PAUSE_{src_cluster.appid}_w_{dst_cluster.ElbPnetip}:{dst_cluster.BlbListenerPort}
*/

package sync_group

import (
	"context"
	"encoding/base64"
	"fmt"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
)

const KeyTplBdrpSyncPause = "BDRP_SYNC_PAUSE_%s_w_%s:%s"

func getDataserverSyncPauseKey(srcAppID string, dstElbPnetIP string, dstElbListenerPort string, dstlusterAuth string) string {
	key := fmt.Sprintf(KeyTplBdrpSyncPause, srcAppID, dstElbPnetIP, dstElbListenerPort)
	if len(dstlusterAuth) != 0 {
		key = key + ":" + base64.StdEncoding.EncodeToString([]byte(dstlusterAuth))
	}
	return key
}

func (d *DataServerImpl) PauseSync(ctx context.Context, srcApp *x1model.Application, dstElbPnetIP string, dstElbListenerPort string, dstlusterAuth string) error {
	globalShardIDMap := GetSyncGroupShardIDMap(srcApp)
	for _, gShardID := range globalShardIDMap {
		logger.ComponentLogger.Trace(ctx, "set pause sync,key:%s,field:%s",
			getDataserverSyncPauseKey(srcApp.AppId, dstElbPnetIP, dstElbListenerPort, dstlusterAuth),
			gShardID)
		if err := d.r.HSet(ctx, getDataserverSyncPauseKey(srcApp.AppId, dstElbPnetIP, dstElbListenerPort, dstlusterAuth),
			gShardID, "-1").Err(); err != nil {
			return err
		}
	}
	return nil
}

func (d *DataServerImpl) ResumeSync(ctx context.Context, srcApp *x1model.Application, dstElbPnetIP string, dstElbListenerPort string, dstlusterAuth string) error {
	exists, err := d.r.Exists(ctx, getDataserverSyncPauseKey(srcApp.AppId, dstElbPnetIP, dstElbListenerPort, dstlusterAuth)).Result()
	if err != nil {
		return err
	}
	if exists < 1 {
		logger.ComponentLogger.Trace(ctx, "pause key not exist",
			logit.String("key", getDataserverSyncPauseKey(srcApp.AppId, dstElbPnetIP, dstElbListenerPort, dstlusterAuth)))
		return nil
	}
	logger.ComponentLogger.Trace(ctx, "delete pause sync,key:%s",
		getDataserverSyncPauseKey(srcApp.AppId, dstElbPnetIP, dstElbListenerPort, dstlusterAuth))
	return d.r.Del(ctx, getDataserverSyncPauseKey(srcApp.AppId, dstElbPnetIP, dstElbListenerPort, dstlusterAuth)).Err()
}

func (d *DataServerImpl) IsAllPaused(ctx context.Context, srcApp *x1model.Application,
	dstElbPnetIP string, dstElbListenerPort string, dstlusterAuth string) (bool, error) {
	pauseStatus, err := d.GetPauseStatus(ctx, srcApp, dstElbPnetIP, dstElbListenerPort, dstlusterAuth)
	if err != nil {
		return false, err
	}
	globalShardIDMap := GetSyncGroupShardIDMap(srcApp)
	// 检查分片数必须一致
	if len(pauseStatus) != len(srcApp.Clusters) {
		return false, fmt.Errorf("puaseStatus num %d not match cluster number %d", len(pauseStatus), len(srcApp.Clusters))
	}
	// 检查global shard id 必须一致
	notPausedNumber := 0
	for clusterShortID, gShardID := range globalShardIDMap {
		status, ok := pauseStatus[cast.ToString(gShardID)]
		if !ok {
			return false, fmt.Errorf("cluster short id:%d not found in pause status key", clusterShortID)
		}
		if cast.ToInt64(status) < 0 {
			logger.ComponentLogger.Trace(ctx, "this cluster not paused", logit.Int("cluster short id", clusterShortID))
			notPausedNumber++
		}
	}
	return notPausedNumber == 0, nil
}

func (d *DataServerImpl) GetPauseStatus(ctx context.Context, srcApp *x1model.Application,
	dstElbPnetIP string, dstElbListenerPort string, dstlusterAuth string) (map[string]string, error) {
	logger.ComponentLogger.Trace(ctx, "get pause status,key:%s",
		getDataserverSyncPauseKey(srcApp.AppId, dstElbPnetIP, dstElbListenerPort, dstlusterAuth))
	ret, err := d.r.HGetAll(ctx, getDataserverSyncPauseKey(srcApp.AppId, dstElbPnetIP, dstElbListenerPort, dstlusterAuth)).Result()
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get pause status fail,key:%s,err:%s",
			getDataserverSyncPauseKey(srcApp.AppId, dstElbPnetIP, dstElbListenerPort, dstlusterAuth), err.Error())
	}
	return ret, err
}
