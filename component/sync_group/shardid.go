/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* shardid.go */
/*
modification history
--------------------
2023/07/14 , by <PERSON> (<EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package sync_group

import (
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
)

func GetSyncGroupShardIDMap(app *x1model.Application) (ClusterShortID2GlobalShardID map[int]int64) {
	ClusterShortID2GlobalShardID = make(map[int]int64, 0)
	for _, cluster := range app.Clusters {
		ClusterShortID2GlobalShardID[cluster.ClusterShortID] = GetSyncGroupShardIDByClusterShortID(cluster.ClusterShortID)
	}
	return
}

func GetSyncGroupShardIDByClusterShortID(clusterShortID int) (gShardID int64) {
	gShardID = config.IDCCODE << GShardIdIdcCodeShift
	gShardID += int64(clusterShortID) & 0xffffffff
	return
}
