/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* config.go */
/*
modification history
--------------------
2023/07/14 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package sync_group

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"sync"
)

var (
	config             *Config    = &Config{}
	initOnce           *sync.Once = &sync.Once{}
	dataServerInstance     DataServer
	bdrpDataServerInstance DataServer
)

func MustLoadConf(ctx context.Context, redisForDataserver *single_redis.SingleClient,bdrpRedisForDataserver *single_redis.SingleClient) {
	initOnce.Do(func() {
		if err := compo_utils.LoadConf("sync_group", config); err != nil {
			panic(err.Error())
		}
		if redisForDataserver == nil {
			panic("need redis for dataserver instance")
		}
		if bdrpRedisForDataserver == nil {
			panic("need bdrp for dataserver instance")
		}
		dataServerInstance = &DataServerImpl{r: redisForDataserver}

		bdrpDataServerInstance = &DataServerImpl{r: bdrpRedisForDataserver}
	})
}

func DataserverIns(ctx context.Context, appInfo *x1model.Application) DataServer {
	if appInfo.DataserverType == "bdrp" {
		return bdrpDataServerInstance
	} else {
		return dataServerInstance
	}
}
