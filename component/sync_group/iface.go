/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* iface.go */
/*
modification history
--------------------
2023/07/15 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package sync_group

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
)

type Config struct {
	IDCCODE int64 `toml:"IdcCode"`
}

type DataServer interface {
	PauseSync(ctx context.Context, srcApp *x1model.Application,
		dstElbPnetIP string, dstElbListenerPort string, dstlusterAuth string) error
	ResumeSync(ctx context.Context, srcApp *x1model.Application,
		dstElbPnetIP string, dstElbListenerPort string, dstlusterAuth string) error
	IsAllPaused(ctx context.Context, srcApp *x1model.Application,
		dstElbPnetIP string, dstElbListenerPort string, dstlusterAuth string) (bool, error)
	GetPauseStatus(ctx context.Context, srcApp *x1model.Application,
		dstElbPnetIP string, dstElbListenerPort string, dstlusterAuth string) (map[string]string, error)

	InitAJoinTask(ctx context.Context, seedClusterApp *x1model.Application,
		newClusterElbPnetIP string, newClusterElbListenerPort string, newClusterAuth string) error
	DisableForwardOtherRegionCmds(ctx context.Context, seedClusterApp *x1model.Application,
		newClusterElbPnetIP string, newClusterElbListenerPort string, newClusterAuth string) error
	IsAllRDBSynced(ctx context.Context, seedClusterApp *x1model.Application,
		newClusterElbPnetIP string, newClusterElbListenerPort string, newClusterAuth string) (bool, error)
	GetRDBTaskStatus(ctx context.Context, seedClusterApp *x1model.Application,
		newClusterElbPnetIP string, newClusterElbListenerPort string, newClusterAuth string) (map[string]string, error)

	GetSyncChannelSyncPoint(ctx context.Context, clusterShortID int,
		dstElbPnetIP string, dstElbListenerPort string, dstAuth string) (currentSyncPoint string, err error)
	SetSyncChannelSyncPoint(ctx context.Context, clusterShortID int,
		dstElbPnetIP string, dstElbListenerPort string, syncPoint string, dstAuth string) error
	AddSyncChannel(ctx context.Context, srcAppID string, dstElbPnetIP string, dstElbListenerPort string, dstlusterAuth string) error
	DelSyncChannel(ctx context.Context, srcAppID string, dstElbPnetIP string, dstElbListenerPort string, dstlusterAuth string) error
}

type DataServerImpl struct {
	r *single_redis.SingleClient
}
