/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* string_BDRP_SYNC.go */
/*
modification history
--------------------
2023/07/15 , by <PERSON> (<EMAIL>) , create
*/
/*
DESCRIPTION
BDRP_SYNC_{globalshard_id}_w_{dst_cluster.ElbPnetip}:{dst_cluster.BlbListenerPort}
*/

package sync_group

import (
	"context"
	"encoding/base64"
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/logger"
)

const KeyTplBdrpSync = "BDRP_SYNC_%d_w_%s:%s"

func getBdrpSyncKey(srcGlobalShardID int64, dstElbPnetIP string, dstElbListenerPort string, dstAuth string) string {
	key := fmt.Sprintf(KeyTplBdrpSync, srcGlobalShardID, dstElbPnetIP, dstElbListenerPort)
	if len(dstAuth) != 0 {
		key = key + ":" + base64.StdEncoding.EncodeToString([]byte(dstAuth))
	}
	return key
}

func (d *DataServerImpl) GetSyncChannelSyncPoint(ctx context.Context, clusterShortID int,
	dstElbPnetIP string, dstElbListenerPort string, dstAuth string) (currentSyncPoint string, err error) {
	logger.ComponentLogger.Trace(ctx, "GetSyncChannelSyncPoint,key:%s",
		getBdrpSyncKey(GetSyncGroupShardIDByClusterShortID(clusterShortID), dstElbPnetIP, dstElbListenerPort, dstAuth))
	currentSyncPoint, err = d.r.Get(ctx, getBdrpSyncKey(GetSyncGroupShardIDByClusterShortID(clusterShortID),
		dstElbPnetIP, dstElbListenerPort, dstAuth)).Result()
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "GetSyncChannelSyncPoint fail,key:%s,err:%s",
			getBdrpSyncKey(GetSyncGroupShardIDByClusterShortID(clusterShortID), dstElbPnetIP, dstElbListenerPort, dstAuth),
			err.Error())
	}
	return
}

func (d *DataServerImpl) SetSyncChannelSyncPoint(ctx context.Context, clusterShortID int,
	dstElbPnetIP string, dstElbListenerPort string, syncPoint string, dstAuth string) error {
	logger.ComponentLogger.Trace(ctx, "SetSyncChannelSyncPoint,key:%s,syncpoint:%s",
		getBdrpSyncKey(GetSyncGroupShardIDByClusterShortID(clusterShortID), dstElbPnetIP, dstElbListenerPort, dstAuth),
		syncPoint)
	err := d.r.Set(ctx, getBdrpSyncKey(GetSyncGroupShardIDByClusterShortID(clusterShortID),
		dstElbPnetIP, dstElbListenerPort, dstAuth), syncPoint, 0).Err()
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "SetSyncChannelSyncPoint fail,key:%s,err:%s",
			getBdrpSyncKey(GetSyncGroupShardIDByClusterShortID(clusterShortID), dstElbPnetIP, dstElbListenerPort, dstAuth),
			err.Error())
	}
	return err
}
