/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* set_BDRP_SYNC_GROUP.go */
/*
modification history
--------------------
2023/07/15 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
BDRP_SYNC_GROUP_{src_cluster.appid}
*/

package sync_group

import (
	"context"
	"encoding/base64"
	"fmt"
)

const KeySyncChannel = "BDRP_SYNC_GROUP_%s" // 参数是clustershowid

func getSyncChannelKey(srcAppID string) string {
	key := fmt.Sprintf(KeySyncChannel, srcAppID)
	return key
}

func buildSyncChannelVal(dstElbPnetIP string, dstElbListenerPort string, dstlusterAuth string) string {
	val := fmt.Sprintf("%s:%s", dstElbPnetIP, dstElbListenerPort)
	if len(dstlusterAuth) != 0 {
		val = val + ":" + base64.StdEncoding.EncodeToString([]byte(dstlusterAuth))
	}
	return val
}

func (d *DataServerImpl) AddSyncChannel(ctx context.Context, srcAppID string, dstElbPnetIP string, dstElbListenerPort string, dstlusterAuth string) error {
	if err := d.r.SAdd(ctx, getSyncChannelKey(srcAppID), buildSyncChannelVal(dstElbPnetIP, dstElbListenerPort, dstlusterAuth)).Err(); err != nil {
		return err
	}
	return nil
}

func (d *DataServerImpl) DelSyncChannel(ctx context.Context, srcAppID string, dstElbPnetIP string, dstElbListenerPort string, dstlusterAuth string) error {
	if err := d.r.SRem(ctx, getSyncChannelKey(srcAppID), buildSyncChannelVal(dstElbPnetIP, dstElbListenerPort, dstlusterAuth)).Err(); err != nil {
		return err
	}
	return nil
}
