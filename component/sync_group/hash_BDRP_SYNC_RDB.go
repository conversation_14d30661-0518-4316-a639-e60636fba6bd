/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* key_BDRP_SYNC_RDB.go */
/*
modification history
--------------------
2023/07/15 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
BDRP_SYNC_RDB_{seed_cluster.appid}_w_{new_cluster.ElbPnetip}:{new_cluster.BlbListenerPort}
*/

package sync_group

import (
	"context"
	"encoding/base64"
	"fmt"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"strings"
)

const (
	KeyTplBdrpSyncRDB              = "BDRP_SYNC_RDB_%s_w_%s:%s"
	FieldTplRDBTask                = "sync_rdb_task_%s"
	FieldTplForwardOtherRegionFlag = "forward_other_region_commands_%s"

	ForwardOtherRegionFlagDisable = "disable"

	RDBTaskFieldPrefix = "sync_rdb_task"
)

func getDataserverSyncRDBKey(seedClusterAppID string, newClusterElbPnetIP string,
	newClusterElbListenerPort string, newClusterAuth string) string {
	key := fmt.Sprintf(KeyTplBdrpSyncRDB, seedClusterAppID, newClusterElbPnetIP, newClusterElbListenerPort)
	if len(newClusterAuth) != 0 {
		key = key + ":" + base64.StdEncoding.EncodeToString([]byte(newClusterAuth))
	}
	return key
}

func getRDBTaskField(gShardID int64) string {
	return fmt.Sprintf(FieldTplRDBTask, cast.ToString(gShardID))
}

func getForwardOtherRegionFlagField(gShardID int64) string {
	return fmt.Sprintf(FieldTplForwardOtherRegionFlag, cast.ToString(gShardID))
}

func (d *DataServerImpl) InitAJoinTask(ctx context.Context, seedClusterApp *x1model.Application,
	newClusterElbPnetIP string, newClusterElbListenerPort string, newClusterAuth string) error {
	globalShardIDMap := GetSyncGroupShardIDMap(seedClusterApp)
	for _, gShardID := range globalShardIDMap {
		logger.ComponentLogger.Trace(ctx, "hset rdb task,key:%s,field:%s",
			getDataserverSyncRDBKey(seedClusterApp.AppId, newClusterElbPnetIP, newClusterElbListenerPort, newClusterAuth),
			getRDBTaskField(gShardID))
		if err := d.r.HSet(ctx, getDataserverSyncRDBKey(seedClusterApp.AppId, newClusterElbPnetIP, newClusterElbListenerPort, newClusterAuth),
			getRDBTaskField(gShardID), "0").Err(); err != nil {
			logger.ComponentLogger.Warning(ctx, "InitAJoinTask Fail", logit.String("key",
				getDataserverSyncRDBKey(seedClusterApp.AppId,
					newClusterElbPnetIP, newClusterElbListenerPort, newClusterAuth)), logit.Error("err", err))
			return err
		}
		logger.ComponentLogger.Trace(ctx, "hset forward,key:%s,field:%s",
			getDataserverSyncRDBKey(seedClusterApp.AppId, newClusterElbPnetIP, newClusterElbListenerPort, newClusterAuth),
			getForwardOtherRegionFlagField(gShardID))
		if err := d.r.HSet(ctx, getDataserverSyncRDBKey(seedClusterApp.AppId, newClusterElbPnetIP, newClusterElbListenerPort, newClusterAuth),
			getForwardOtherRegionFlagField(gShardID), ForwardOtherRegionFlagDisable).Err(); err != nil {
			return err
		}
	}
	return nil
}

func (d *DataServerImpl) DisableForwardOtherRegionCmds(ctx context.Context, seedClusterApp *x1model.Application,
	newClusterElbPnetIP string, newClusterElbListenerPort string, newClusterAuth string) error {
	globalShardIDMap := GetSyncGroupShardIDMap(seedClusterApp)
	for _, gShardID := range globalShardIDMap {
		logger.ComponentLogger.Trace(ctx, "disable forward,key:%s,field:%s",
			getDataserverSyncRDBKey(seedClusterApp.AppId, newClusterElbPnetIP, newClusterElbListenerPort, newClusterAuth),
			getForwardOtherRegionFlagField(gShardID))
		if err := d.r.HSet(ctx, getDataserverSyncRDBKey(seedClusterApp.AppId,
			newClusterElbPnetIP, newClusterElbListenerPort, newClusterAuth),
			getForwardOtherRegionFlagField(gShardID), ForwardOtherRegionFlagDisable).Err(); err != nil {
			logger.ComponentLogger.Warning(ctx, "DisableForwardOtherRegionCmds Fail,key:%s,err:%s",
				getDataserverSyncRDBKey(seedClusterApp.AppId, newClusterElbPnetIP, newClusterElbListenerPort, newClusterAuth),
				err.Error())
			return err
		}
	}
	return nil
}

func (d *DataServerImpl) IsAllRDBSynced(ctx context.Context, seedClusterApp *x1model.Application,
	newClusterElbPnetIP string, newClusterElbListenerPort string, newClusterAuth string) (bool, error) {
	rdbTaskStatus, err := d.GetRDBTaskStatus(ctx, seedClusterApp, newClusterElbPnetIP, newClusterElbListenerPort, newClusterAuth)
	if err != nil {
		return false, err
	}
	globalShardIDMap := GetSyncGroupShardIDMap(seedClusterApp)
	// 检查分片数必须一致
	if len(rdbTaskStatus) != len(seedClusterApp.Clusters) {
		return false, fmt.Errorf("rdbTaskStatus num %d not match cluster number %d",
			len(rdbTaskStatus), len(seedClusterApp.Clusters))
	}
	// 检查global shard id 必须一致
	notSyncedNumber := 0
	for clusterShortID, gShardID := range globalShardIDMap {
		status, ok := rdbTaskStatus[getRDBTaskField(gShardID)]
		if !ok {
			return false, fmt.Errorf("cluster short id:%d not found in rdbTaskStatus,gshardID:%d", clusterShortID, gShardID)
		}
		if cast.ToInt64(status) <= 0 {
			logger.ComponentLogger.Trace(ctx, "this cluster rdb not synced", logit.Int("cluster short id", clusterShortID))
			notSyncedNumber++
		}
	}
	return notSyncedNumber == 0, nil
}

func (d *DataServerImpl) GetRDBTaskStatus(ctx context.Context, seedClusterApp *x1model.Application,
	newClusterElbPnetIP string, newClusterElbListenerPort string, newClusterAuth string) (map[string]string, error) {
	logger.ComponentLogger.Trace(ctx, "get all rdb task status,key:%s",
		getDataserverSyncRDBKey(seedClusterApp.AppId, newClusterElbPnetIP, newClusterElbListenerPort, newClusterAuth))
	status, err := d.r.HGetAll(ctx, getDataserverSyncRDBKey(seedClusterApp.AppId,
		newClusterElbPnetIP, newClusterElbListenerPort, newClusterAuth)).Result()
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "GetRDBTaskStatus Fail,key:%s,err:%s",
			getDataserverSyncRDBKey(seedClusterApp.AppId, newClusterElbPnetIP, newClusterElbListenerPort, newClusterAuth),
			err.Error())
		return nil, err
	}
	taskStatus := make(map[string]string, 0)
	for k, v := range status {
		if strings.HasPrefix(k, RDBTaskFieldPrefix) {
			taskStatus[k] = v
		}
	}
	return taskStatus, nil
}
