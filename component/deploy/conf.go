/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-22
 * File: conf.go
 */

/*
 * DESCRIPTION
 *   -
 */

// Package deploy
package deploy

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/component/repo"
)

// package conf
type PackageConf struct {
	Name           string                 `toml:"Name"`
	Repo           string                 `toml:"Repo"`
	DefaultVersion string                 `toml:"DefaultVersion"`
	StaticEnv      map[string]interface{} `toml:"StaticEnv"`
	repo           *repo.Repo             `toml:"-"`
}

func (c *PackageConf) GetRepoType() string {
	return c.repo.Conf.Type
}

func (c *PackageConf) GetRepoEndpoint() string {
	return c.repo.Conf.Endpoint
}

func (c *PackageConf) GetPackageFileName(version string) string {
	return fmt.Sprintf("%s_%s.tar.gz", c.Name, version)
}

func (c *PackageConf) GetMd5FileName(version string) string {
	return fmt.Sprintf("%s_%s.md5", c.Name, version)
}

func (c *PackageConf) GetPackageFileUrl(version string) string {
	return c.repo.Client.GetFileUrl(c.GetPackageFileName(version))
}

func (c *PackageConf) GetMd5FileUrl(version string) string {
	return c.repo.Client.GetFileUrl(c.GetMd5FileName(version))
}

func (c *PackageConf) GetStaticEnvVars() map[string]interface{} {
	return c.StaticEnv
}

// deploy conf
type deployConf struct {
	Packages map[string]*PackageConf `toml:"Packages"`
}

func (c *deployConf) GetPackageConf(packageTag string) *PackageConf {
	packageConf, _ := c.Packages[packageTag]
	return packageConf
}

var clientConfMap = &sync.Map{}

// load deploy conf
func MustLoadConf(clientName string) *deployConf {
	if conf, ok := clientConfMap.Load(clientName); ok {
		if conf, ok := conf.(*deployConf); ok {
			return conf
		}
	}

	conf := &deployConf{}
	if err := compo_utils.LoadConf(clientName, conf); err != nil {
		panic(err)
	}

	for _, c := range conf.Packages {
		c.repo = repo.GetRepo(c.Repo)

		envVars := map[string]interface{}{}
		flattenEnvVars("", c.StaticEnv, envVars)
		c.StaticEnv = envVars
	}

	clientConfMap.Store(clientName, conf)
	return conf
}

func flattenEnvVars(keyPrefix string, inVars map[string]interface{}, outVars map[string]interface{}) {
	for k, v := range inVars {
		if keyPrefix != "" {
			k = keyPrefix + "." + k
		}
		if _v, ok := v.(map[string]interface{}); ok {
			flattenEnvVars(k, _v, outVars)
			continue
		}
		outVars[k] = v
	}
}
