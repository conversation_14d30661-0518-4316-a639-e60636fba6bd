/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-22
 * File: const.go
 */

/*
 * DESCRIPTION
 *   -
 */
// Package deploy

package deploy

import "regexp"

const DefaultClientName = "deploy"

// repo类型
const (
	RepoNone = "none"
	RepoHttp = "http"
	RepoBos  = "bos"
)

// 部署任务状态
const (
	repoNone = "none"
	repoHttp = "http"
	repoFtp  = "ftp"
	repoBos  = "bos"
)

// 特定的部署失败信息
var (
	errPatternWorkDirAlreadyExists = regexp.MustCompile(`(?i)work_dir.+?already exists`)
	errPatternPortAlreadyUsed      = regexp.MustCompile(`(?i)port.+?already been used`)
)
