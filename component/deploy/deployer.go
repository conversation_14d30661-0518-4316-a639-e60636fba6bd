/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-23
 * File: deployer.go
 */

/*
 * DESCRIPTION
 *   对deploy步骤进行了打包封装
 */

// Package deploy
package deploy

import (
	"context"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/xagent"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

// Deployer -  负责部署流程
type Deployer interface {
	Do(req *DeployServerRequest) error
}

// NewDeployer 创建deployer
func NewDeployer(ctx context.Context, opts ...Option) Deployer {
	d := &deployer{
		procCtx: ctx,
		client:  NewDefaultClient(),
		config:  newDefaultConfig(),
	}

	for _, opt := range opts {
		opt(d)
	}

	return d
}

type Option func(d *deployer)

func OptWithClient(c Service) Option {
	return func(d *deployer) {
		d.client = c
	}
}

func OptWaitReady(c *ConfigWaitReady) Option {
	return func(d *deployer) {
		d.config.waitReady = c
	}
}

func OptCreateTask(c *ConfigCreateTask) Option {
	return func(d *deployer) {
		d.config.createTask = c
	}
}

func OptWaitTask(c *ConfigWaitTask) Option {
	return func(d *deployer) {
		d.config.waitTask = c
	}
}

type ConfigWaitReady struct {
	Timeout    time.Duration
	RetryDelay time.Duration
}

type ConfigCreateTask struct {
	RetryTimes int
	RetryDelay time.Duration
}

type ConfigWaitTask struct {
	Timeout             time.Duration
	RetryDelayOnFail    time.Duration
	RetryDelayOnRunning time.Duration
}

// deployer
type deployer struct {
	procCtx context.Context
	client  Service
	config  *config
}

// config 部署配置
type config struct {
	waitReady  *ConfigWaitReady
	createTask *ConfigCreateTask
	waitTask   *ConfigWaitTask
}

func newDefaultConfig() *config {
	return &config{
		waitReady: &ConfigWaitReady{
			Timeout:    5 * time.Minute,
			RetryDelay: 5 * time.Second,
		},
		createTask: &ConfigCreateTask{
			RetryTimes: 3,
			RetryDelay: 5 * time.Second,
		},
		waitTask: &ConfigWaitTask{
			Timeout:             10 * time.Minute,
			RetryDelayOnFail:    5 * time.Second,
			RetryDelayOnRunning: 10 * time.Second,
		},
	}
}

// 部署context
type deployContext struct {
	req    *DeployServerRequest
	taskId int64
}

func (d *deployer) Do(req *DeployServerRequest) error {
	ctx := &deployContext{req: req}
	handlers := []func(*deployContext) error{
		d.waitForReady,
		d.createDeployTask,
		d.waitDeployTask,
	}

	for _, h := range handlers {
		if err := h(ctx); err != nil {
			return err
		}
	}

	return nil
}

func (d *deployer) wait(ctx context.Context, t time.Duration) error {
	select {
	case <-time.After(t):
		return nil
	case <-ctx.Done():
		if cerrs.Is(ctx.Err(), context.Canceled) {
			return cerrs.ErrCanceled.Wrap(ctx.Err())
		}
		return cerrs.ErrTimeout.Wrap(ctx.Err())
	}
}

// waitForReady - 等待xagent启动ready
func (d *deployer) waitForReady(ctx *deployContext) error {
	logger.ComponentLogger.Trace(d.procCtx, "wait for xagent(%s) ready", ctx.req.XagentAddr)

	procCtx, cancel := context.WithTimeout(d.procCtx, d.config.waitReady.Timeout)
	defer cancel()

	checkReq := &CheckAliveRequest{
		XagentAddr: ctx.req.XagentAddr,
	}

	for {
		checkRsp, err := d.client.CheckAlive(procCtx, checkReq)
		if err != nil {
			return err
		}

		if checkRsp.Alive {
			logger.ComponentLogger.Trace(d.procCtx, "xagent(%s) is ready", ctx.req.XagentAddr)
			return nil
		}

		if err = d.wait(procCtx, d.config.waitReady.RetryDelay); err != nil {
			logger.ComponentLogger.Warning(d.procCtx, "cancel waiting for xagent(%s) ready, cause: %s",
				ctx.req.XagentAddr, err)
			return err
		}

		logger.ComponentLogger.Debug(d.procCtx, "wait for xagent(%s) ready", ctx.req.XagentAddr)
	}
}

// createDeployTask - 创建部署任务
func (d *deployer) createDeployTask(ctx *deployContext) error {
	logger.ComponentLogger.Trace(d.procCtx, "create deploy task for agent(%s)", ctx.req.XagentAddr)

	retryCount := 0
	for {
		taskRsp, err := d.client.DeployServer(d.procCtx, ctx.req)
		if err == nil {
			logger.ComponentLogger.Trace(d.procCtx, "deploy task(%d) created for agent(%s)",
				taskRsp.TaskId, ctx.req.XagentAddr)

			ctx.taskId = taskRsp.TaskId
			return nil
		}

		// xagent创建task没有对重复请求做判断而无脑地fork，因此只对连接异常做重试
		if !sdk_utils.IsConnectFail(err) {
			return err
		}

		retryCount++
		if retryCount > d.config.createTask.RetryTimes {
			logger.ComponentLogger.Warning(d.procCtx, "cancel creating deploy task for xagent(%s) after %d times",
				ctx.req.XagentAddr, retryCount)
			return err
		}

		if err = d.wait(d.procCtx, d.config.createTask.RetryDelay); err != nil {
			logger.ComponentLogger.Warning(d.procCtx, "cancel creating deploy task for xagent(%s), cause: %s",
				ctx.req.XagentAddr, err)
			return err
		}

		logger.ComponentLogger.Debug(d.procCtx, "create deploy task for agent(%s), retry(%d)",
			ctx.req.XagentAddr, retryCount)
	}
}

// waitDeployTask - 等待任务结束，更新任务状态
func (d *deployer) waitDeployTask(ctx *deployContext) error {
	logger.ComponentLogger.Trace(d.procCtx, "wait for deploy task(%d) of agent(%s)",
		ctx.taskId, ctx.req.XagentAddr)

	procCtx, cancel := context.WithTimeout(d.procCtx, d.config.waitTask.Timeout)
	defer cancel()

	queryReq := &QueryDeployTaskRequest{
		XagentAddr: ctx.req.XagentAddr,
		TaskId:     ctx.taskId,
	}

	for {
		queryRsp, err := d.client.QueryDeployTask(procCtx, queryReq)

		var retryDelay time.Duration
		if err != nil {
			retryDelay = d.config.waitTask.RetryDelayOnFail
		} else {
			if queryRsp.TaskStatus != xagent.TaskSuccess && queryRsp.TaskStatus != xagent.TaskFailed {
				retryDelay = d.config.waitTask.RetryDelayOnRunning
			} else {
				if queryRsp.TaskStatus == xagent.TaskFailed {
					taskResult := base_utils.FormatText(queryRsp.TaskResult)
					logger.ComponentLogger.Warning(d.procCtx, "deploy task(%d) of agent(%s) failed, result: %s",
						ctx.taskId, ctx.req.XagentAddr, taskResult)

					switch {
					case errPatternWorkDirAlreadyExists.MatchString(taskResult):
						return cerrs.ErrDeployWorkDirExists.Errorf(taskResult)
					case errPatternPortAlreadyUsed.MatchString(taskResult):
						return cerrs.ErrDeployPortAlreadyUsed.Errorf(taskResult)
					default:
						return cerrs.ErrDeployFail.Errorf(taskResult)
					}
				}

				logger.ComponentLogger.Trace(d.procCtx, "deploy task(%d) of agent(%s) finished successfully",
					ctx.taskId, ctx.req.XagentAddr)
				return nil
			}
		}

		if err = d.wait(procCtx, retryDelay); err != nil {
			logger.ComponentLogger.Warning(d.procCtx, "cancel waiting for task(%d) of xagent(%s), cause: %s",
				ctx.taskId, ctx.req.XagentAddr, err)
			return err
		}

		logger.ComponentLogger.Debug(d.procCtx, "wait for deploy task(%d) of agent(%s)",
			ctx.taskId, ctx.req.XagentAddr)
	}
}
