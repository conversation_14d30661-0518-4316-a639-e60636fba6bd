//protofsg -with_context -json_tag=1 deploy.proto interface.go
package x1base.component.deploy;

message Interface {}

message XagentAddr {
    optional string host = 1;
    optional int32 port = 2;
}

message CheckAliveRequest {
    optional XagentAddr xagent_addr = 1;
}

message CheckAliveResponse {
    optional bool alive = 1;
}

message AgentConfEnv {
    optional string container_type = 1;
    optional string csmaster_ip = 2;
    optional int32 csmaster_port = 3;
    optional string bcm_host = 4;
    optional string bcm_port = 5;
    optional string accesskey = 6;
    optional string secretkey = 7;
    optional string iam_host = 8;
    optional string iam_port = 9;
    optional string username = 10;
    optional string password = 11;
}

message CSAgentConfEnv {
    optional string csmonitor_ip = 1;
    optional int32 csmonitor_port = 2;
    optional int64 connection_timeout_ms = 3;
}

message CronConfEnv {
    optional string bos_host = 1;
    optional string bucket_name = 2;
    optional string ak_encrypt = 3;
    optional string sk_encrypt = 4;
    optional string log_transfer_addr = 5;
    optional int32 log_transfer_port = 6;
    optional string log_transfer_url = 7;
    optional string hotkey_transfer_url = 8;
    optional string token = 9;
}

message AgentConf {
    optional AgentConfEnv online = 1;
}

message CSAgentConf {
    optional CSAgentConfEnv online = 1;
}

message CronConf {
    optional CronConfEnv online = 1;
}

message AgentConfigure  {
    // env_type 必须填"online"
    optional string env_type = 1;
    optional AgentConf conf = 2;
}

message CSAgentConfigure {
    // env_type 必须填"online"
    optional string env_type = 1;
    optional CSAgentConf conf = 2;
}

message CronConfigure {
    // env_type 必须填"online"
    optional string env_type = 1;
    optional CronConf conf = 2;
}

// deploy request
message DeployServerRequest {
    optional XagentAddr xagent_addr = 1;
    // xcache_conf 部署使用xagent的install_xcache
    repeated XcacheConf xcache_conf = 2;
    // server_conf 部署使用xagent的install_server
    repeated CommonServerConf server_conf = 3;
}

// XcacheConf xcache conf
message XcacheConf {
    // @inject_tag json:"-"
    // packageTag package tag, 在deploy.conf中配置
    optional string packageTag = 1;
    // version package version, 不填则使用deploy.conf中的默认配置
    optional string version = 2;
    // WORK_DIR 部署目录
    optional string WORK_DIR = 3;
    optional int32 PORT = 4;
    // SERVER_ID node ID
    optional string SERVER_ID = 5;
    optional int64 MAX_SPACE = 6;
    // AGENT_CONFIGURE agent configure，如果不设置则使用配置文件中的静态变量填充
    optional AgentConfigure  AGENT_CONFIGURE = 7;
    // CSAGENT_CONFIGURE csagent configure，如果不设置则使用配置文件中的静态变量填充
    optional CSAgentConfigure CSAGENT_CONFIGURE = 8;
    // CRON_CONFIGURE cron configure，如果不设置则使用配置文件中的静态变量填充
    optional CronConfigure CRON_CONFIGURE = 9;
}

// CommonServerConf 通用server conf
message CommonServerConf {
    // @inject_tag json:"-"
    // packageTag package tag, 在deploy.conf中配置
    optional string packageTag = 1;
    // version package version, 不填则使用deploy.conf中的默认配置
    optional string version = 2;
    optional string WORK_DIR = 3;
    optional int32 PORT = 4;
    // ENV_VARS 环境变量，用于template文件变量替换，将追加或覆盖配置文件中的静态变量
    map<string, Interface> ENV_VARS = 5;
}

// XagentPackage xagent package
message XagentPackage {
    optional string repoEndpoint = 1;
    optional string repoType = 2;
    optional string version = 3;
    optional string packageName = 4;
    optional string packageUri = 5;
}

// XagentDeployServerRequest xagent install request
message XagentDeployServerRequest {
    optional string repoEndpoint = 1;
    optional string envType = 2;
    //@inject_tag json:",omitempty"
    repeated Interface xcache = 3;
    //@inject_tag json:",omitempty"
    repeated Interface server = 4;
}

message DeployServerResponse {
    optional int64 task_id = 1;
}

message QueryDeployTaskRequest {
    optional XagentAddr xagent_addr = 1;
    optional int64 task_id = 2;
}

message QueryDeployTaskResponse {
    optional string task_status = 1;
    optional string task_result = 2;
}

message GetXcacheDeployConfRequest {
    optional XcacheConf conf = 1;
}

message GetXcacheDeployConfResponse {
    optional XagentPackage package = 1;
    optional XcacheConf conf = 2;
}

message GetServerDeployConfRequest {
    optional CommonServerConf conf = 1;
}

message GetServerDeployConfResponse {
    optional XagentPackage package = 1;
    optional CommonServerConf conf = 2;
}

// Service deploy
service Service {
    // check_alive check if the xagent is alive
    rpc check_alive(CheckAliveRequest) returns (CheckAliveResponse);
    // deploy_server deploy server by xagent (async)
    rpc deploy_server(DeployServerRequest) returns (DeployServerResponse);
    // query_deploy_task check if the deployment task finished
    rpc query_deploy_task(QueryDeployTaskRequest) returns (QueryDeployTaskResponse);
    // get_xcache_deploy_conf 获取xagent install_xcache的部署配置
    rpc get_xcache_deploy_conf(GetXcacheDeployConfRequest) returns (GetXcacheDeployConfResponse);
    // get_server_deploy_conf 获取xagent install_server的部署配置
    rpc get_server_deploy_conf(GetServerDeployConfRequest) returns (GetServerDeployConfResponse);
}
