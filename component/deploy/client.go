/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-22
 * File: client.go
 */

/*
 * DESCRIPTION
 *   -
 */

// Package deploy
package deploy

import (
	"context"
	"net"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/net/gaddr"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/xagent"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type client struct {
	xAgentSdk xagent.XAgentService
	conf      *deployConf
}

// NewDefaultClient - 创建xcache client
func NewDefaultClient() Service {
	return newClient(DefaultClientName)
}

// NewClient - 创建xcache client
func NewClient(clientName string) Service {
	return newClient(clientName)
}

func newClient(clientName string) *client {
	c := &client{
		xAgentSdk: xagent.NewDefaultXAgentSdk(),
		conf:      MustLoadConf(clientName),
	}
	return c
}

// xagent addr stringer
func (a *XagentAddr) String() string {
	return gaddr.JoinHostPort(a.Host, int(a.Port))
}

// SetXAgentSdk - for ut
func (c *client) SetXAgentSdk(sdk xagent.XAgentService) *client {
	c.xAgentSdk = sdk
	return c
}

// CheckAlive - 测试xagent是否启动，避免直接使用其他cmd直接反复重试的副作用
func (c *client) CheckAlive(ctx context.Context, req *CheckAliveRequest) (rsp *CheckAliveResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	sdkReq := &xagent.QueryCmdRequest{
		XagentHost: req.XagentAddr.Host,
		XagentPort: req.XagentAddr.Port,
		CmdName:    "xagent_info",
	}

	sdkRsp, err := c.xAgentSdk.QueryCmd(ctx, sdkReq)

	if err != nil {
		netErr := &net.OpError{}
		if !cerrs.As(err, &netErr) {
			logger.ComponentLogger.Warning(ctx, "check alive fail: %s", err.Error())
			return nil, err
		}

		logger.ComponentLogger.Trace(ctx, "xagent not alive: %s", netErr.Error())
		rsp = &CheckAliveResponse{Alive: false}
		return rsp, nil
	}

	logger.ComponentLogger.Trace(ctx, "xagent response: %s", base_utils.CompressJson(sdkRsp.Data))
	alive := sdkRsp.Data != ""
	rsp = &CheckAliveResponse{Alive: alive}
	return rsp, err
}

// DeployServer - 创建xAgent异步task部署server
func (c *client) DeployServer(ctx context.Context, req *DeployServerRequest) (rsp *DeployServerResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	if req.XcacheConf == nil && req.ServerConf == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("both xcache_conf and server_conf are null")
	}

	// 构造 xagent req
	xagentReq := &XagentDeployServerRequest{
		RepoEndpoint: "",
		EnvType:      "online",
		Xcache:       make([]interface{}, 0),
		Server:       make([]interface{}, 0),
	}

	// 添加 xcache deploy conf
	for _, cf := range req.XcacheConf {
		if conf, err := c.getXcacheDeployConfMerged(cf); err != nil {
			logger.ComponentLogger.Warning(ctx, err.Error())
			return nil, err
		} else {
			xagentReq.Xcache = append(xagentReq.Xcache, conf)
		}
	}

	// 添加 common deploy conf
	for _, cf := range req.ServerConf {
		if conf, err := c.getCommonServerDeployConfMerged(cf); err != nil {
			logger.ComponentLogger.Warning(ctx, err.Error())
			return nil, err
		} else {
			xagentReq.Server = append(xagentReq.Server, conf)
		}
	}

	// exec request
	sdkReq := &xagent.CreateTaskRequest{
		XagentHost: req.XagentAddr.Host,
		XagentPort: req.XagentAddr.Port,
		TaskType:   xagent.TASK_TYPE_ASYNC,
		Data:       xagentReq,
		TaskName:   "install",
	}

	sdkRsp, err := c.xAgentSdk.CreateTask(ctx, sdkReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "create install task fail, xagent: %s", req.XagentAddr)
		return nil, err
	}

	rsp = &DeployServerResponse{TaskId: sdkRsp.TaskId}
	return
}

// getXagentPackage 获取xagent部署package相关信息
func (c *client) getXagentPackage(pckConf *PackageConf, version string) *XagentPackage {
	if version == "" {
		version = pckConf.DefaultVersion
	}

	return &XagentPackage{
		RepoEndpoint: pckConf.GetRepoEndpoint(),
		RepoType:     pckConf.GetRepoType(),
		Version:      version,
		PackageName:  pckConf.GetPackageFileName(version),
		PackageUri:   pckConf.GetPackageFileUrl(version),
	}
}

// getXcacheDeployConfMerged - 获取xcache部署配置(平铺形式)
func (c *client) getXcacheDeployConfMerged(cf *XcacheConf) (map[string]interface{}, error) {
	pck, cf, err := c.getXcacheDeployConf(cf)
	if err != nil {
		return nil, err
	}

	conf := map[string]interface{}{}
	_ = sdk_utils.CastWithTag(pck, &conf, "json")
	_ = sdk_utils.CastWithTag(cf, &conf, "json")

	return conf, nil
}

// getCommonServerDeployConfMerged - 获取common server部署配置(平铺形式)
func (c *client) getCommonServerDeployConfMerged(cf *CommonServerConf) (map[string]interface{}, error) {
	pck, cf, err := c.getCommonServerDeployConf(cf)
	if err != nil {
		return nil, err
	}

	conf := map[string]interface{}{}
	_ = sdk_utils.CastWithTag(pck, &conf, "json")
	_ = sdk_utils.CastWithTag(cf, &conf, "json")

	return conf, nil
}

// getXcacheDeployConf - 获取xcache部署配置
func (c *client) getXcacheDeployConf(cf *XcacheConf) (*XagentPackage, *XcacheConf, error) {
	pckConf := c.conf.GetPackageConf(cf.PackageTag)
	if pckConf == nil {
		return nil, nil, cerrs.ErrInvalidParams.Errorf("package tag(%s) does not exist", cf.PackageTag)
	}

	// package info
	pck := c.getXagentPackage(pckConf, cf.Version)

	envVars := pckConf.GetStaticEnvVars()

	// agent configure
	if cf.AgentConfigure == nil {
		cf.AgentConfigure = &AgentConfigure{
			EnvType: "online",
			Conf: &AgentConf{
				Online: &AgentConfEnv{
					ContainerType: cast.ToString(envVars["agent.container_type"]),
					CsmasterIp:    cast.ToString(envVars["agent.csmaster_ip"]),
					CsmasterPort:  cast.ToInt32(envVars["agent.csmaster_port"]),
					BcmHost:       cast.ToString(envVars["agent.bcm_host"]),
					BcmPort:       cast.ToString(envVars["agent.bcm_port"]),
					Accesskey:     cast.ToString(envVars["agent.accesskey"]),
					Secretkey:     cast.ToString(envVars["agent.secretkey"]),
					IamHost:       cast.ToString(envVars["agent.iam_host"]),
					IamPort:       cast.ToString(envVars["agent.iam_port"]),
					Username:      cast.ToString(envVars["agent.username"]),
					Password:      cast.ToString(envVars["agent.password"]),
				},
			},
		}
	}

	// csagent configure
	if cf.CsagentConfigure == nil {
		cf.CsagentConfigure = &CSAgentConfigure{
			EnvType: "online",
			Conf: &CSAgentConf{
				Online: &CSAgentConfEnv{
					CsmonitorIp:         cast.ToString(envVars["csagent.csmonitor_ip"]),
					CsmonitorPort:       cast.ToInt32(envVars["csagent.csmonitor_port"]),
					ConnectionTimeoutMs: cast.ToInt64(envVars["csagent.connection_timeout_ms"]),
				},
			},
		}
	}

	// cron configure
	if cf.CronConfigure == nil {
		cf.CronConfigure = &CronConfigure{
			EnvType: "online",
			Conf: &CronConf{
				Online: &CronConfEnv{
					BosHost:           cast.ToString(envVars["cron.bos_host"]),
					BucketName:        cast.ToString(envVars["cron.bucket_name"]),
					AkEncrypt:         cast.ToString(envVars["cron.ak_encrypt"]),
					SkEncrypt:         cast.ToString(envVars["cron.sk_encrypt"]),
					LogTransferAddr:   cast.ToString(envVars["cron.log_transfer_addr"]),
					LogTransferPort:   cast.ToInt32(envVars["cron.log_transfer_port"]),
					LogTransferUrl:    cast.ToString(envVars["cron.log_transfer_url"]),
					HotkeyTransferUrl: cast.ToString(envVars["cron.hotkey_transfer_url"]),
					Token:             cast.ToString(envVars["cron.token"]),
				},
			},
		}
	}

	return pck, cf, nil
}

// getCommonServerDeployConf - 获取common server部署配置
func (c *client) getCommonServerDeployConf(cf *CommonServerConf) (*XagentPackage, *CommonServerConf, error) {
	pckConf := c.conf.GetPackageConf(cf.PackageTag)
	if pckConf == nil {
		return nil, nil, cerrs.ErrInvalidParams.Errorf("package tag(%s) does not exist", cf.PackageTag)
	}

	// package info
	pck := c.getXagentPackage(pckConf, cf.Version)

	// env vars
	envVars := pckConf.GetStaticEnvVars()

	for k, v := range envVars {
		if cf.EnvVars == nil {
			cf.EnvVars = map[string]interface{}{}
		}
		cf.EnvVars[k] = v
	}

	return pck, cf, nil
}

// QueryDeployTask - 查询部署任务状态
func (c *client) QueryDeployTask(ctx context.Context, req *QueryDeployTaskRequest) (rsp *QueryDeployTaskResponse, err error) {
	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}

	sdkReq := &xagent.QueryTaskRequest{
		XagentHost: req.XagentAddr.Host,
		XagentPort: req.XagentAddr.Port,
		TaskId:     req.TaskId,
	}

	sdkRsp, err := c.xAgentSdk.QueryTask(ctx, sdkReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "query install task fail, xagent: %s, taskId: %d",
			req.XagentAddr, req.TaskId)
		return nil, err
	}

	rsp = &QueryDeployTaskResponse{
		TaskStatus: sdkRsp.TaskStatus,
		TaskResult: sdkRsp.TaskResult,
	}
	return
}

// ParseResult QueryDeployTaskResponse parse task result string to dest result
func (rsp *QueryDeployTaskResponse) ParseResult(result interface{}) error {
	return xagent.ParseResult(rsp.TaskResult, result)
}

// GetXcacheDeployConf 获取xagent install_xcache的部署配置
func (c *client) GetXcacheDeployConf(ctx context.Context, req *GetXcacheDeployConfRequest) (rsp *GetXcacheDeployConfResponse, err error) {
	pck, cf, err := c.getXcacheDeployConf(req.Conf)
	return &GetXcacheDeployConfResponse{Package: pck, Conf: cf}, err
}

// GetServerDeployConf 获取xagent install_server的部署配置
func (c *client) GetServerDeployConf(ctx context.Context, req *GetServerDeployConfRequest) (rsp *GetServerDeployConfResponse, err error) {
	pck, cf, err := c.getCommonServerDeployConf(req.Conf)
	return &GetServerDeployConfResponse{Package: pck, Conf: cf}, err
}
