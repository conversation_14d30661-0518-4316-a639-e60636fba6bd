/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-22
 * File: deploy_test.go
 */

/*
 * DESCRIPTION
 *   deploy client test
 */

// Package deploy
package deploy

import (
	"context"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/xagent"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type stubXAgentSdk struct {
	xagent.XAgentService
	*xagent.ExecCmdResponse
	*xagent.QueryCmdResponse
	*xagent.CreateTaskResponse
	*xagent.QueryTaskResponse
}

func (d *stubXAgentSdk) CreateTask(ctx context.Context, req *xagent.CreateTaskRequest) (
	rsp *xagent.CreateTaskResponse, err error) {
	fmt.Printf("[CreateTask] %s\n", base_utils.Format(req))
	return d.CreateTaskResponse, nil
}

//func TestClient_CheckAlive(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	c := NewDefaultClient()
//
//	req := &CheckAliveRequest{XagentAddr: &XagentAddr{
//		Host: "localhost",
//		Port: 8080,
//	}}
//	rsp, err := c.CheckAlive(ctx, req)
//	fmt.Printf("rsp: %+v, err: %v\n", rsp, err)
//}

func TestClient_DeployServer(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	c := newClient(DefaultClientName).SetXAgentSdk(&stubXAgentSdk{
		CreateTaskResponse: &xagent.CreateTaskResponse{TaskId: 123},
	})

	// package xcache
	rsp, err := c.DeployServer(ctx, &DeployServerRequest{
		XagentAddr: &XagentAddr{
			Host: "localhost",
			Port: 8080,
		},
		ServerConf: []*CommonServerConf{
			{
				PackageTag: "xcache",
				WorkDir:    "/root/test001",
				PORT:       8181,
				EnvVars: map[string]interface{}{
					"SERVER_ID": "0aede855-a496-4040-9820-b8d4643d7e97",
					"MAX_SPACE": 10000000000,
				},
			},
		},
		XcacheConf: []*XcacheConf{
			{
				PackageTag: "xcache",
				WorkDir:    "/root/test002",
				PORT:       8282,
				ServerId:   "cb0b3fd5-7a5d-4aaa-b3ac-bd0f9584449d",
				MaxSpace:   10000000000,
			},
		},
	})

	if err != nil {
		t.Fatalf("deploy server fail, err: %s", err)
	}
	if rsp.TaskId != 123 {
		t.Errorf("wrong taskId: %d", rsp.TaskId)
	}

	// package test
	rsp, err = c.DeployServer(ctx, &DeployServerRequest{
		XagentAddr: &XagentAddr{
			Host: "localhost",
			Port: 8080,
		},
		ServerConf: []*CommonServerConf{
			{
				PackageTag: "test",
				WorkDir:    "/root/test003",
				PORT:       8383,
				EnvVars: map[string]interface{}{
					"SERVER_ID": "467d1628-b65b-40bd-a768-d9fe4913537e",
					"MAX_SPACE": 10000000000,
				},
			},
		},
	})

	if err != nil {
		t.Fatalf("deploy server fail, err: %s", err)
	}
	if rsp.TaskId != 123 {
		t.Errorf("wrong taskId: %d", rsp.TaskId)
	}
}
