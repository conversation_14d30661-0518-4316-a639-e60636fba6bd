/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2022-02-24
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   根据deploy.proto生成的interface文件
 */

// Package deploy
package deploy

import (
	"context"
)

type XagentAddr struct {
	Host string `json:"host"`
	Port int32  `json:"port"`
}

type CheckAliveRequest struct {
	XagentAddr *XagentAddr `json:"xagent_addr"`
}

type CheckAliveResponse struct {
	Alive bool `json:"alive"`
}

type AgentConfEnv struct {
	ContainerType string `json:"container_type"`
	CsmasterIp    string `json:"csmaster_ip"`
	CsmasterPort  int32  `json:"csmaster_port"`
	BcmHost       string `json:"bcm_host"`
	BcmPort       string `json:"bcm_port"`
	Accesskey     string `json:"accesskey"`
	Secretkey     string `json:"secretkey"`
	IamHost       string `json:"iam_host"`
	IamPort       string `json:"iam_port"`
	Username      string `json:"username"`
	Password      string `json:"password"`
}

type CSAgentConfEnv struct {
	CsmonitorIp         string `json:"csmonitor_ip"`
	CsmonitorPort       int32  `json:"csmonitor_port"`
	ConnectionTimeoutMs int64  `json:"connection_timeout_ms"`
}

type CronConfEnv struct {
	BosHost           string `json:"bos_host"`
	BucketName        string `json:"bucket_name"`
	AkEncrypt         string `json:"ak_encrypt"`
	SkEncrypt         string `json:"sk_encrypt"`
	LogTransferAddr   string `json:"log_transfer_addr"`
	LogTransferPort   int32  `json:"log_transfer_port"`
	LogTransferUrl    string `json:"log_transfer_url"`
	HotkeyTransferUrl string `json:"hotkey_transfer_url"`
	Token             string `json:"token"`
}

type AgentConf struct {
	Online *AgentConfEnv `json:"online"`
}

type CSAgentConf struct {
	Online *CSAgentConfEnv `json:"online"`
}

type CronConf struct {
	Online *CronConfEnv `json:"online"`
}

type AgentConfigure struct {
	// EnvType 必须填"online"
	EnvType string     `json:"env_type"`
	Conf    *AgentConf `json:"conf"`
}

type CSAgentConfigure struct {
	// EnvType 必须填"online"
	EnvType string       `json:"env_type"`
	Conf    *CSAgentConf `json:"conf"`
}

type CronConfigure struct {
	// EnvType 必须填"online"
	EnvType string    `json:"env_type"`
	Conf    *CronConf `json:"conf"`
}

type DeployServerRequest struct {
	XagentAddr *XagentAddr `json:"xagent_addr"`
	// XcacheConf 部署使用xagent的install_xcache
	XcacheConf []*XcacheConf `json:"xcache_conf"`
	// ServerConf 部署使用xagent的install_server
	ServerConf []*CommonServerConf `json:"server_conf"`
}

// XcacheConf xcache conf
type XcacheConf struct {
	// PackageTag package tag, 在deploy.conf中配置
	PackageTag string `json:"-"`
	// Version package version, 不填则使用deploy.conf中的默认配置
	Version string `json:"version"`
	// WorkDir 部署目录
	WorkDir string `json:"WORK_DIR"`
	PORT    int32  `json:"PORT"`
	// ServerId node ID
	ServerId string `json:"SERVER_ID"`
	MaxSpace int64  `json:"MAX_SPACE"`
	// AgentConfigure agent configure，如果不设置则使用配置文件中的静态变量填充
	AgentConfigure *AgentConfigure `json:"AGENT_CONFIGURE"`
	// CsagentConfigure csagent configure，如果不设置则使用配置文件中的静态变量填充
	CsagentConfigure *CSAgentConfigure `json:"CSAGENT_CONFIGURE"`
	// CronConfigure cron configure，如果不设置则使用配置文件中的静态变量填充
	CronConfigure *CronConfigure `json:"CRON_CONFIGURE"`

	GlobalSeqId    int64 `json:"global_seq_id"`
	AppGlobalSeqId int64 `json:"app_global_seq_id"`
}

// CommonServerConf 通用server conf
type CommonServerConf struct {
	// PackageTag package tag, 在deploy.conf中配置
	PackageTag string `json:"-"`
	// Version package version, 不填则使用deploy.conf中的默认配置
	Version string `json:"version"`
	WorkDir string `json:"WORK_DIR"`
	PORT    int32  `json:"PORT"`
	// EnvVars 环境变量，用于template文件变量替换，将追加或覆盖配置文件中的静态变量
	EnvVars map[string]interface{} `json:"ENV_VARS"`
}

// XagentPackage xagent package
type XagentPackage struct {
	RepoEndpoint string `json:"repoEndpoint"`
	RepoType     string `json:"repoType"`
	Version      string `json:"version"`
	PackageName  string `json:"packageName"`
	PackageUri   string `json:"packageUri"`
}

// XagentDeployServerRequest xagent install request
type XagentDeployServerRequest struct {
	RepoEndpoint string        `json:"repoEndpoint"`
	EnvType      string        `json:"envType"`
	Xcache       []interface{} `json:"xcache,omitempty"`
	Server       []interface{} `json:"server,omitempty"`
}

type DeployServerResponse struct {
	TaskId int64 `json:"task_id"`
}

type QueryDeployTaskRequest struct {
	XagentAddr *XagentAddr `json:"xagent_addr"`
	TaskId     int64       `json:"task_id"`
}

type QueryDeployTaskResponse struct {
	TaskStatus string `json:"task_status"`
	TaskResult string `json:"task_result"`
}

type GetXcacheDeployConfRequest struct {
	Conf *XcacheConf `json:"conf"`
}

type GetXcacheDeployConfResponse struct {
	Package *XagentPackage `json:"package"`
	Conf    *XcacheConf    `json:"conf"`
}

type GetServerDeployConfRequest struct {
	Conf *CommonServerConf `json:"conf"`
}

type GetServerDeployConfResponse struct {
	Package *XagentPackage    `json:"package"`
	Conf    *CommonServerConf `json:"conf"`
}

// Service deploy
type Service interface {
	// CheckAlive check if the xagent is alive
	CheckAlive(ctx context.Context, req *CheckAliveRequest) (rsp *CheckAliveResponse, err error)
	// DeployServer deploy server by xagent (async)
	DeployServer(ctx context.Context, req *DeployServerRequest) (rsp *DeployServerResponse, err error)
	// QueryDeployTask check if the deployment task finished
	QueryDeployTask(ctx context.Context, req *QueryDeployTaskRequest) (rsp *QueryDeployTaskResponse, err error)
	// GetXcacheDeployConf 获取xagent install_xcache的部署配置
	GetXcacheDeployConf(ctx context.Context, req *GetXcacheDeployConfRequest) (rsp *GetXcacheDeployConfResponse, err error)
	// GetServerDeployConf 获取xagent install_server的部署配置
	GetServerDeployConf(ctx context.Context, req *GetServerDeployConfRequest) (rsp *GetServerDeployConfResponse, err error)
}
