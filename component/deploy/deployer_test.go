/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: shangshuai02 (<EMAIL>)
 * Date: 2021-12-23
 * File: deployer_test.go
 */

/*
 * DESCRIPTION
 *   deployer test
 */

// Package deploy
package deploy

import (
	"context"
	"fmt"
	"net"
	"testing"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/xagent"
)

type stubDeployClient struct {
	checkAlive      func(ctx context.Context) (rsp *CheckAliveResponse, err error)
	deployServer    func(ctx context.Context) (rsp *DeployServerResponse, err error)
	queryDeployTask func(ctx context.Context) (rsp *QueryDeployTaskResponse, err error)
}

func (c *stubDeployClient) GetXcacheDeployConf(ctx context.Context, req *GetXcacheDeployConfRequest) (rsp *GetXcacheDeployConfResponse, err error) {
	panic("implement me")
}

func (c *stubDeployClient) GetServerDeployConf(ctx context.Context, req *GetServerDeployConfRequest) (rsp *GetServerDeployConfResponse, err error) {
	panic("implement me")
}

func (c *stubDeployClient) CheckAlive(ctx context.Context, req *CheckAliveRequest) (rsp *CheckAliveResponse, err error) {
	if c.checkAlive != nil {
		return c.checkAlive(ctx)
	}
	return &CheckAliveResponse{Alive: true}, nil
}

func (c *stubDeployClient) DeployServer(ctx context.Context, req *DeployServerRequest) (rsp *DeployServerResponse, err error) {
	if c.deployServer != nil {
		return c.deployServer(ctx)
	}
	return &DeployServerResponse{TaskId: 123}, nil
}

func (c *stubDeployClient) QueryDeployTask(ctx context.Context, req *QueryDeployTaskRequest) (rsp *QueryDeployTaskResponse, err error) {
	if c.queryDeployTask != nil {
		return c.queryDeployTask(ctx)
	}
	return &QueryDeployTaskResponse{
		TaskStatus: xagent.TaskSuccess,
		TaskResult: "",
	}, nil
}

var _ Service = (*stubDeployClient)(nil)

var testReq = &DeployServerRequest{
	XagentAddr: &XagentAddr{
		Host: "localhost",
		Port: 8080,
	},
	ServerConf: []*CommonServerConf{
		{
			PackageTag: "test",
			WorkDir:    "~",
			PORT:       0,
			EnvVars:    nil,
		},
	},
}

func TestDeployer_Do_Normal_Fast(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	deployer := NewDeployer(ctx, OptWithClient(&stubDeployClient{}))

	err := deployer.Do(testReq)
	if err != nil {
		t.Fatalf("deploy fail with err: %s", err)
	}
}

func TestDeployer_Do_Normal_Slow(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctxDelay, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	deployer := NewDeployer(ctx, OptWithClient(&stubDeployClient{
		queryDeployTask: func(ctx context.Context) (rsp *QueryDeployTaskResponse, err error) {
			rsp = &QueryDeployTaskResponse{
				TaskStatus: xagent.TaskRunning,
				TaskResult: "",
			}
			select {
			case <-ctxDelay.Done():
				rsp.TaskStatus = xagent.TaskSuccess
				return
			default:
				return
			}
		},
	}), OptWaitTask(&ConfigWaitTask{
		Timeout:             10 * time.Second,
		RetryDelayOnFail:    5 * time.Second,
		RetryDelayOnRunning: 2 * time.Second,
	}))

	startTime := time.Now()
	err := deployer.Do(testReq)
	endTime := time.Now()
	if err != nil {
		t.Fatalf("deploy fail with err: %s", err)
	}
	cost := endTime.Sub(startTime)
	if cost < 5*time.Second {
		t.Fatalf("invalid time cost : %fs", cost.Seconds())
	}
}

func TestDeployer_Do_WaitReadyTimeout(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	deployer := NewDeployer(ctx, OptWithClient(&stubDeployClient{
		checkAlive: func(ctx context.Context) (rsp *CheckAliveResponse, err error) {
			return &CheckAliveResponse{Alive: false}, nil
		},
	}), OptWaitReady(&ConfigWaitReady{
		Timeout:    5 * time.Second,
		RetryDelay: 2 * time.Second,
	}))

	err := deployer.Do(testReq)
	if err == nil {
		t.Fatalf("wait ready not timeout, unexpected")
	}
	if !cerrs.Is(err, context.DeadlineExceeded) {
		t.Fatalf("not wait ready timeout error, %T, %+v", err, err)
	}
}

func TestDeployer_Do_DeployServerDialFailFor2TimesAndThenSuccess(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	dialFailCounter := 0
	deployer := NewDeployer(ctx, OptWithClient(&stubDeployClient{
		deployServer: func(ctx context.Context) (rsp *DeployServerResponse, err error) {
			if dialFailCounter == 2 {
				return &DeployServerResponse{TaskId: 123}, nil
			}
			dialFailCounter++
			return nil, cerrs.ErrRalRequestFail.Wrap(&net.OpError{
				Op:  "dial",
				Err: fmt.Errorf("dial fail for times %d", dialFailCounter),
			})
		},
	}), OptCreateTask(&ConfigCreateTask{
		RetryTimes: 10,
		RetryDelay: 2 * time.Second,
	}))

	err := deployer.Do(testReq)
	if err != nil {
		t.Fatalf("deploy fail with error: %s", err)
	}
	if dialFailCounter != 2 {
		t.Fatalf("deploy fail with wrong times: %d", dialFailCounter)
	}
}
