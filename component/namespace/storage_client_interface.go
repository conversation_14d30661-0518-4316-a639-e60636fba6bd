package namespace

import "context"

type fileInfo struct {
	Name string `json:"name"`
	Size int64  `json:"size"`
}

type listFilesResult struct {
	Files       []fileInfo `json:"files"`
	IsTruncated bool       `json:"isTruncated"`
	NextMarker  string     `json:"nextMarker"`
}

type fileDownloadInfoResult struct {
	Name  string `json:"name"`
	Url   string `json:"url"`
	Crc32 string `json:"crc32"`
	Md5   string `json:"md5"`
}

type StorageClient interface {
	FilesLocationExist(ctx context.Context) error
	FileNotExist(ctx context.Context, filePath string) error
	GetFileDownloadInfo(ctx context.Context, filePath string) (*fileDownloadInfoResult, error)
	GetFileContent(ctx context.Context, filePath string, bufferSize int) ([]byte, error)
	ListFiles(ctx context.Context, marker string) (*listFilesResult, error)
}
