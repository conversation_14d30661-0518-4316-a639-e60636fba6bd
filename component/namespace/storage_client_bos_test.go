package namespace

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/baidubce/bce-sdk-go/bce"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"

	"github.com/baidubce/bce-sdk-go/services/bos/api"

	bceBos "github.com/baidubce/bce-sdk-go/services/bos"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"

	. "github.com/agiledragon/gomonkey/v2"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bce_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

//func TestBosStorageClient_FileNotExistOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	configg := loadConf()
//	auth, err := compo_utils.GetOpenapiAuth(ctx, "4093ae6b9e48423e89c69916bc6d5d5e")
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "Get auth fail", logit.Error("err", err),
//			logit.String("iamUserID", "4093ae6b9e48423e89c69916bc6d5d5e"))
//		fmt.Println("Get open api auth fail")
//	}
//	storageClient, err := bce_utils.NewBosClientWithSts(auth.Credential.Ak, auth.Credential.Sk,
//		auth.Credential.SessionToken, configg.BosConf.BosEndpoint)
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "Get bos client fail", logit.Error("err", err))
//		fmt.Println("Get bos client fail")
//	}
//	type fields struct {
//		conf         *config
//		client       *bce_utils.BosClient
//		bucket       string
//		objectPrefix string
//		iamUserID    string
//	}
//	type args struct {
//		ctx      context.Context
//		filePath string
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantErr bool
//	}{
//		{name: "exist", fields: struct {
//			conf         *config
//			client       *bce_utils.BosClient
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "testbos12345", objectPrefix: "namespace1/", iamUserID: ""},
//			args: args{
//				ctx:      context.Background(),
//				filePath: "0/0.meta",
//			}, wantErr: true},
//		{name: "exist", fields: struct {
//			conf         *config
//			client       *bce_utils.BosClient
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "testbos12345", objectPrefix: "namespace1/", iamUserID: ""},
//			args: args{
//				ctx:      context.Background(),
//				filePath: "100/100.meta",
//			}, wantErr: false},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			bc := &BosStorageClient{
//				conf:         tt.fields.conf,
//				client:       tt.fields.client,
//				bucket:       tt.fields.bucket,
//				objectPrefix: tt.fields.objectPrefix,
//				iamUserID:    tt.fields.iamUserID,
//			}
//			if err := bc.FileNotExist(tt.args.ctx, tt.args.filePath); (err != nil) != tt.wantErr {
//				t.Errorf("FileNotExist() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}

func TestBosStorageClient_FileNotExist(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	patchauth := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbase := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	storageClient, err := NewBosStorageClient(context.Background(), &StorageParam{
		StorageType: "",
		IamUserID:   "",
		BosParam: BosParam{
			Bucket:       "TEST",
			ObjectPrefix: "TEST/",
		},
		S3Param: S3Param{},
	}, loadConf())
	if err != nil {
		t.Errorf("NewBosStorageClient() error = %v, wantErr %v", err, false)
	}
	patchbase.Reset()
	patchauth.Reset()
	patch := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket string, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, nil
		})
	if err := storageClient.FileNotExist(context.Background(), "100/100.meta"); err == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch.Reset()
	patch1 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, errors.New("test")
		})
	if err := storageClient.FileNotExist(context.Background(), "100/100.meta"); err == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch1.Reset()
	patch2 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, &bce.BceServiceError{
				StatusCode: 404,
			}
		})
	if err := storageClient.FileNotExist(context.Background(), "100/100.meta"); err != nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch2.Reset()
}

//
//func TestBosStorageClient_FilesLocationExistOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	configg := loadConf()
//	auth, err := compo_utils.GetOpenapiAuth(ctx, "4093ae6b9e48423e89c69916bc6d5d5e")
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "Get auth fail", logit.Error("err", err),
//			logit.String("iamUserID", "4093ae6b9e48423e89c69916bc6d5d5e"))
//		fmt.Println("Get open api auth fail")
//	}
//	storageClient, err := bce_utils.NewBosClientWithSts(auth.Credential.Ak, auth.Credential.Sk,
//		auth.Credential.SessionToken, configg.BosConf.BosEndpoint)
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "Get bos client fail", logit.Error("err", err))
//		fmt.Println("Get bos client fail")
//	}
//	type fields struct {
//		conf         *config
//		client       *bce_utils.BosClient
//		bucket       string
//		objectPrefix string
//		iamUserID    string
//	}
//	type args struct {
//		ctx context.Context
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantErr bool
//	}{
//		{name: "exist", fields: struct {
//			conf         *config
//			client       *bce_utils.BosClient
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "testbos12345", objectPrefix: "namespace1/", iamUserID: ""},
//			args: args{
//				ctx: context.Background(),
//			}, wantErr: false},
//		{name: "not exist", fields: struct {
//			conf         *config
//			client       *bce_utils.BosClient
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "testbos12345", objectPrefix: "namespacenoexist/", iamUserID: ""},
//			args: args{
//				ctx: context.Background(),
//			}, wantErr: true},
//		{name: "forbidden", fields: struct {
//			conf         *config
//			client       *bce_utils.BosClient
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "test", objectPrefix: "namespace1/", iamUserID: ""},
//			args: args{
//				ctx: context.Background(),
//			}, wantErr: true},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			bc := &BosStorageClient{
//				conf:         tt.fields.conf,
//				client:       tt.fields.client,
//				bucket:       tt.fields.bucket,
//				objectPrefix: tt.fields.objectPrefix,
//				iamUserID:    tt.fields.iamUserID,
//			}
//			if err := bc.FilesLocationExist(tt.args.ctx); (err != nil) != tt.wantErr {
//				t.Errorf("FilesLocationExist() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}

func TestBosStorageClient_FilesLocationExist(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	patchauth := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbase := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	storageClient, err := NewBosStorageClient(context.Background(), &StorageParam{
		StorageType: "",
		IamUserID:   "",
		BosParam: BosParam{
			Bucket:       "TEST",
			ObjectPrefix: "TEST/",
		},
		S3Param: S3Param{},
	}, loadConf())
	if err != nil {
		t.Errorf("NewBosStorageClient() error = %v, wantErr %v", err, false)
	}
	patchbase.Reset()
	patchauth.Reset()
	patch := ApplyMethodFunc(&bceBos.Client{}, "DoesBucketExist",
		func(bucket string) (bool, error) {
			return true, nil
		})
	patch1 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, nil
		})
	if err := storageClient.FilesLocationExist(context.Background()); err != nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch.Reset()
	patch1.Reset()
	patch2 := ApplyMethodFunc(&bceBos.Client{}, "DoesBucketExist",
		func(bucket string) (bool, error) {
			return false, nil
		})
	patch3 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, nil
		})
	if err := storageClient.FilesLocationExist(context.Background()); err == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch2.Reset()
	patch3.Reset()
	patch4 := ApplyMethodFunc(&bceBos.Client{}, "DoesBucketExist",
		func(bucket string) (bool, error) {
			return true, nil
		})
	patch5 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, &bce.BceServiceError{
				StatusCode: 404,
			}
		})
	if err := storageClient.FilesLocationExist(context.Background()); err == nil || !cerrs.Is(err, cerrs.ErrNamespaceBosCheckFail) {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch5.Reset()
	patch6 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, &bce.BceServiceError{
				StatusCode: 403,
			}
		})
	if err := storageClient.FilesLocationExist(context.Background()); err == nil || !cerrs.Is(err, cerrs.ErrNamespaceBosCheckFail) {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch6.Reset()
	patch4.Reset()

}

//func TestBosStorageClient_GetFileContentOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	configg := loadConf()
//	auth, err := compo_utils.GetOpenapiAuth(ctx, "4093ae6b9e48423e89c69916bc6d5d5e")
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "Get auth fail", logit.Error("err", err),
//			logit.String("iamUserID", "4093ae6b9e48423e89c69916bc6d5d5e"))
//		fmt.Println("Get open api auth fail")
//	}
//	storageClient, err := bce_utils.NewBosClientWithSts(auth.Credential.Ak, auth.Credential.Sk,
//		auth.Credential.SessionToken, configg.BosConf.BosEndpoint)
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "Get bos client fail", logit.Error("err", err))
//		fmt.Println("Get bos client fail")
//	}
//	type fields struct {
//		conf         *config
//		client       *bce_utils.BosClient
//		bucket       string
//		objectPrefix string
//		iamUserID    string
//	}
//	type args struct {
//		ctx        context.Context
//		filePath   string
//		bufferSize int
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    []byte
//		wantErr bool
//	}{
//		{name: "success", fields: struct {
//			conf         *config
//			client       *bce_utils.BosClient
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "testbos12345", objectPrefix: "namespace1/", iamUserID: ""},
//			args: args{
//				ctx:        context.Background(),
//				filePath:   "0/0.meta",
//				bufferSize: 10240,
//			}, wantErr: false},
//		{name: "content too long", fields: struct {
//			conf         *config
//			client       *bce_utils.BosClient
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "testbos12345", objectPrefix: "namespace1/", iamUserID: ""},
//			args: args{
//				ctx:        context.Background(),
//				filePath:   "0/0.meta",
//				bufferSize: 1,
//			}, wantErr: true},
//		{name: "file not exist", fields: struct {
//			conf         *config
//			client       *bce_utils.BosClient
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "testbos12345", objectPrefix: "only-1-slot1/", iamUserID: ""},
//			args: args{
//				ctx:        context.Background(),
//				filePath:   "100/100.meta",
//				bufferSize: 1,
//			}, wantErr: true},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			bc := &BosStorageClient{
//				conf:         tt.fields.conf,
//				client:       tt.fields.client,
//				bucket:       tt.fields.bucket,
//				objectPrefix: tt.fields.objectPrefix,
//				iamUserID:    tt.fields.iamUserID,
//			}
//			_, err := bc.GetFileContent(tt.args.ctx, tt.args.filePath, tt.args.bufferSize)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("GetFileContent() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			//if !reflect.DeepEqual(got, tt.want) {
//			//	t.Errorf("GetFileContent() got = %v, want %v", got, tt.want)
//			//}
//		})
//	}
//}

//func TestBosStorageClient_GetFileDownloadInfoOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	configg := loadConf()
//	auth, err := compo_utils.GetOpenapiAuth(ctx, "4093ae6b9e48423e89c69916bc6d5d5e")
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "Get auth fail", logit.Error("err", err),
//			logit.String("iamUserID", "4093ae6b9e48423e89c69916bc6d5d5e"))
//		fmt.Println("Get open api auth fail")
//	}
//	storageClient, err := bce_utils.NewBosClientWithSts(auth.Credential.Ak, auth.Credential.Sk,
//		auth.Credential.SessionToken, configg.BosConf.BosEndpoint)
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "Get bos client fail", logit.Error("err", err))
//		fmt.Println("Get bos client fail")
//	}
//	type fields struct {
//		conf         *config
//		client       *bce_utils.BosClient
//		bucket       string
//		objectPrefix string
//		iamUserID    string
//	}
//	type args struct {
//		ctx      context.Context
//		filePath string
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *fileDownloadInfoResult
//		wantErr bool
//	}{
//		{name: "success", fields: struct {
//			conf         *config
//			client       *bce_utils.BosClient
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "testbos12345", objectPrefix: "namespace1/", iamUserID: ""},
//			args: args{
//				ctx:      context.Background(),
//				filePath: "0/0.meta",
//			}, wantErr: false},
//		{name: "file not exist", fields: struct {
//			conf         *config
//			client       *bce_utils.BosClient
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "testbos12345", objectPrefix: "only-1-slot/", iamUserID: ""},
//			args: args{
//				ctx:      context.Background(),
//				filePath: "100/100.meta1",
//			}, wantErr: true},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			bc := &BosStorageClient{
//				conf:         tt.fields.conf,
//				client:       tt.fields.client,
//				bucket:       tt.fields.bucket,
//				objectPrefix: tt.fields.objectPrefix,
//				iamUserID:    tt.fields.iamUserID,
//			}
//			_, err := bc.GetFileDownloadInfo(tt.args.ctx, tt.args.filePath)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("GetFileDownloadInfo() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			//if !reflect.DeepEqual(got, tt.want) {
//			//	t.Errorf("GetFileDownloadInfo() got = %v, want %v", got, tt.want)
//			//}
//		})
//	}
//}

func TestBosStorageClient_GetFileDownloadInfo(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	patchauth := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbase := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	storageClient, err := NewBosStorageClient(context.Background(), &StorageParam{
		StorageType: "",
		IamUserID:   "",
		BosParam: BosParam{
			Bucket:       "TEST",
			ObjectPrefix: "TEST/",
		},
		S3Param: S3Param{},
	}, loadConf())
	if err != nil {
		t.Errorf("NewBosStorageClient() error = %v, wantErr %v", err, false)
	}
	patchbase.Reset()
	patchauth.Reset()
	patch := ApplyMethodReturn(&bceBos.Client{}, "BasicGeneratePresignedUrl",
		"test")
	patch1 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, nil
		})
	if _, err := storageClient.GetFileDownloadInfo(context.Background(), "test"); err != nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch1.Reset()
	patch.Reset()
}

//func TestBosStorageClient_ListFilesOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	configg := loadConf()
//	auth, err := compo_utils.GetOpenapiAuth(ctx, "4093ae6b9e48423e89c69916bc6d5d5e")
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "Get auth fail", logit.Error("err", err),
//			logit.String("iamUserID", "4093ae6b9e48423e89c69916bc6d5d5e"))
//		fmt.Println("Get open api auth fail")
//	}
//	storageClient, err := bce_utils.NewBosClientWithSts(auth.Credential.Ak, auth.Credential.Sk,
//		auth.Credential.SessionToken, configg.BosConf.BosEndpoint)
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "Get bos client fail", logit.Error("err", err))
//		fmt.Println("Get bos client fail")
//	}
//	type fields struct {
//		conf         *config
//		client       *bce_utils.BosClient
//		bucket       string
//		objectPrefix string
//		iamUserID    string
//	}
//	type args struct {
//		ctx    context.Context
//		marker string
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *listFilesResult
//		wantErr bool
//	}{
//		{name: "success", fields: struct {
//			conf         *config
//			client       *bce_utils.BosClient
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "testbos12345", objectPrefix: "ut/list/success", iamUserID: ""},
//			args: args{
//				ctx:    context.Background(),
//				marker: "",
//			}, want: &listFilesResult{}, wantErr: false},
//		{name: "success", fields: struct {
//			conf         *config
//			client       *bce_utils.BosClient
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "ttt111", objectPrefix: "", iamUserID: ""},
//			args: args{
//				ctx: context.Background(),
//			}, want: &listFilesResult{}, wantErr: true},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			bc := &BosStorageClient{
//				conf:         tt.fields.conf,
//				client:       tt.fields.client,
//				bucket:       tt.fields.bucket,
//				objectPrefix: tt.fields.objectPrefix,
//				iamUserID:    tt.fields.iamUserID,
//			}
//			_, err := bc.ListFiles(tt.args.ctx, tt.args.marker)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("ListFiles() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			//if !reflect.DeepEqual(got, tt.want) {
//			//	t.Errorf("ListFiles() got = %v, want %v", got, tt.want)
//			//}
//		})
//	}
//}

func TestBosStorageClient_ListFiles(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	patchauth := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbase := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	storageClient, err := NewBosStorageClient(context.Background(), &StorageParam{
		StorageType: "",
		IamUserID:   "",
		BosParam: BosParam{
			Bucket:       "TEST",
			ObjectPrefix: "TEST/",
		},
		S3Param: S3Param{},
	}, loadConf())
	if err != nil {
		t.Errorf("NewBosStorageClient() error = %v, wantErr %v", err, false)
	}
	patchbase.Reset()
	patchauth.Reset()
	patch := ApplyMethodReturn(&bceBos.Client{}, "ListObjects", &api.ListObjectsResult{
		Contents: []api.ObjectSummaryType{
			{Key: "TEST/", Size: 1},
			{Key: "TEST/TEST", Size: 2},
		},
	}, nil)
	if rsp, err := storageClient.ListFiles(context.Background(), ""); err != nil ||
		!reflect.DeepEqual(*rsp, listFilesResult{
			Files: []fileInfo{
				{Name: "TEST/TEST", Size: 2},
			},
			IsTruncated: false,
			NextMarker:  "",
		}) {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch.Reset()
	patch1 := ApplyMethodReturn(&bceBos.Client{}, "ListObjects", &api.ListObjectsResult{
		Contents: []api.ObjectSummaryType{
			{Key: "TEST/", Size: 1},
			{Key: "TEST/TEST", Size: 2},
		},
	}, errors.New("error"))
	if _, err := storageClient.ListFiles(context.Background(), ""); err == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch1.Reset()
}

func TestNewBosStorageClient(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	type args struct {
		ctx    context.Context
		param  *StorageParam
		config *config
	}
	tests := []struct {
		name    string
		args    args
		want    StorageClient
		wantErr bool
	}{
		{name: "test no param", args: args{
			ctx:    context.Background(),
			param:  nil,
			config: &config{},
		}, want: nil, wantErr: true},
		{name: "test no endpoint", args: args{
			ctx: context.Background(),
			param: &StorageParam{
				StorageType: "",
				IamUserID:   "",
				BosParam: BosParam{
					Bucket:       "TEST",
					ObjectPrefix: "TEST/",
				},
				S3Param: S3Param{},
			},
			config: &config{
				BosConf: &bosConf{},
			},
		}, want: nil, wantErr: true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewBosStorageClient(tt.args.ctx, tt.args.param, tt.args.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewBosStorageClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewBosStorageClient() got = %v, want %v", got, tt.want)
			}
		})
	}
}
