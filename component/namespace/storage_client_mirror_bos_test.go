package namespace

import (
	"context"
	"errors"
	"fmt"
	"path/filepath"
	"reflect"
	"sync"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/net/servicer"

	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/logger"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/bos/api"

	bceBos "github.com/baidubce/bce-sdk-go/services/bos"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"

	. "github.com/agiledragon/gomonkey/v2"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bce_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

var initOnce sync.Once

func SetTestMrConf(ctx context.Context) {
	initOnce.Do(func() {
		pattern := filepath.Join(env.ConfDir(), "servicer", "mr_sts", "*.toml")
		servicer.MustLoad(ctx, servicer.LoadOptFilesGlob(pattern, false))
		MrStsConf := sts.MrStsConf{}
		if err := conf.Parse("multi_region/mr_sts.toml", &MrStsConf); err != nil {
			fmt.Println("load multi_region/mr_sts.toml failed")
		}
		sts.MustInitMrStsSdk(ctx, MrStsConf)
	})
}

//func TestMirrorBosStorageClient_FileNotExistOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	pattern := filepath.Join(env.ConfDir(), "servicer", "mr_sts", "*.toml")
//	servicer.MustLoad(ctx, servicer.LoadOptFilesGlob(pattern, false))
//	MrStsConf := sts.MrStsConf{}
//	if err := conf.Parse("multi_region/mr_sts.toml", &MrStsConf); err != nil {
//		fmt.Println("load multi_region/mr_sts.toml failed")
//	}
//	sts.MustInitMrStsSdk(ctx, MrStsConf)
//	configg := loadConf()
//	client, err := NewMirrorBosStorageClient(context.Background(),
//		&StorageParam{
//			StorageType: "",
//			IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
//			MirrorBosParam: MirrorBosParam{
//				Bucket:       "testbos123456",
//				ObjectPrefix: "namespace1/",
//				MirrorBucket: "testbos12345",
//			},
//			S3Param: S3Param{},
//		},
//		&config{
//			BosConf: &bosConf{
//				BosEndpoint:            configg.BosConf.BosEndpoint,
//				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
//				CheckCRC32:             configg.BosConf.CheckCRC32,
//				CheckMD5:               configg.BosConf.CheckMD5,
//				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
//			},
//			MirrorBosConf: configg.MirrorBosConf,
//		})
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
//		fmt.Println("New MirrorBosStorageClient fail")
//	}
//	if client.FileNotExist(ctx, "0/0.meta") == nil {
//		fmt.Println("FileNotExist fail")
//	} else {
//		fmt.Println("FileNotExist success")
//	}
//	if client.FileNotExist(ctx, "0/000.meta") != nil {
//		fmt.Println("FileNotExist fail")
//	} else {
//		fmt.Println("FileNotExist success")
//	}
//}

func TestMirrorBosStorageClient_FileNotExist(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	mirrorClient := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseMirrorClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return mirrorClient, nil
	})
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	sClient, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "abc",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		fmt.Println("New MirrorBosStorageClient fail")
	}

	patch := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket string, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, nil
		})
	if err := sClient.FileNotExist(context.Background(), "100/100.meta"); err == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch.Reset()
	patch1 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, errors.New("test")
		})
	if err := sClient.FileNotExist(context.Background(), "100/100.meta"); err == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch1.Reset()
	patch2 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, &bce.BceServiceError{
				StatusCode: 404,
			}
		})
	if err := sClient.FileNotExist(context.Background(), "100/100.meta"); err != nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch2.Reset()
	patchauthClient.Reset()
	patchauthMirrorClient.Reset()
	patchbaseClient.Reset()
	patchbaseMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

//func TestMirrorBosStorageClient_FilesLocationExistOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	pattern := filepath.Join(env.ConfDir(), "servicer", "mr_sts", "*.toml")
//	servicer.MustLoad(ctx, servicer.LoadOptFilesGlob(pattern, false))
//	MrStsConf := sts.MrStsConf{}
//	if err := conf.Parse("multi_region/mr_sts.toml", &MrStsConf); err != nil {
//		fmt.Println("load multi_region/mr_sts.toml failed")
//	}
//	sts.MustInitMrStsSdk(ctx, MrStsConf)
//	configg := loadConf()
//	client, err := NewMirrorBosStorageClient(context.Background(),
//		&StorageParam{
//			StorageType: "",
//			IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
//			MirrorBosParam: MirrorBosParam{
//				Bucket:       "testbos123456",
//				ObjectPrefix: "namespace1/",
//				MirrorBucket: "testbos12345",
//			},
//			S3Param: S3Param{},
//		},
//		&config{
//			BosConf: &bosConf{
//				BosEndpoint:            configg.BosConf.BosEndpoint,
//				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
//				CheckCRC32:             configg.BosConf.CheckCRC32,
//				CheckMD5:               configg.BosConf.CheckMD5,
//				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
//			},
//			MirrorBosConf: configg.MirrorBosConf,
//		})
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
//		fmt.Println("New MirrorBosStorageClient fail")
//	}
//	if client.FilesLocationExist(context.Background()) != nil {
//		t.Errorf("FilesLocationExist() error = %v, wantErr %v", err, false)
//	}
//	client.mirrorBucket = "notexist"
//	if client.FilesLocationExist(context.Background()) == nil {
//		t.Errorf("FilesLocationExist() error = %v, wantErr %v", err, true)
//	}
//	client.mirrorBucket = "testbos12345"
//	client.bucket = "notexist"
//	if client.FilesLocationExist(context.Background()) == nil {
//		t.Errorf("FilesLocationExist() error = %v, wantErr %v", err, true)
//	}
//	client.bucket = "testbos123456"
//	client.objectPrefix = "notexist"
//	if client.FilesLocationExist(context.Background()) == nil {
//		t.Errorf("FilesLocationExist() error = %v, wantErr %v", err, true)
//	}
//	client.objectPrefix = "namespace1/"
//	if client.FilesLocationExist(context.Background()) != nil {
//		t.Errorf("FilesLocationExist() error = %v, wantErr %v", err, false)
//	}
//}

func TestMirrorBosStorageClient_FilesLocationExist_Success(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	mirrorClient := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseMirrorClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return mirrorClient, nil
	})
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	storageClient, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "abc",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		fmt.Println("New MirrorBosStorageClient fail")
	}

	patch := ApplyMethodFunc(&bceBos.Client{}, "DoesBucketExist",
		func(bucket string) (bool, error) {
			return true, nil
		})
	patch1 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			if bucket == "testbos12345" {
				return &api.GetObjectMetaResult{}, nil
			}
			return &api.GetObjectMetaResult{}, &bce.BceServiceError{
				StatusCode: 404,
			}
		})
	patch2 := ApplyMethodFunc(&bceBos.Client{}, "GetBucketMirror",
		func(bucket string) (*api.PutBucketMirrorArgs, error) {
			return &api.PutBucketMirrorArgs{
				BucketMirroringConfiguration: []api.MirrorConfigurationRule{
					{Prefix: "",
						SourceUrl:       "bos://bj-bos-sandbox.baidu-int.com/testbos12345",
						PassQueryString: false,
						Mode:            "fetch",
						StorageClass:    "STANDARD",
						PassHeaders:     []string{},
						Resource:        "*",
						Suffix:          "",
						FixedKey:        "",
						PrefixReplace:   "",
						Version:         "v1"},
				},
			}, nil
		})
	if err := storageClient.FilesLocationExist(context.Background()); err != nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch.Reset()
	patch1.Reset()
	patch2.Reset()
	patchauthClient.Reset()
	patchauthMirrorClient.Reset()
	patchbaseClient.Reset()
	patchbaseMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

func TestMirrorBosStorageClient_FilesLocationExist_BucketNotExist(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	mirrorClient := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseMirrorClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return mirrorClient, nil
	})
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	storageClient, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "abc",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		fmt.Println("New MirrorBosStorageClient fail")
	}

	patch2 := ApplyMethodFunc(&bceBos.Client{}, "DoesBucketExist",
		func(bucket string) (bool, error) {
			return false, nil
		})
	patch3 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, nil
		})
	if err := storageClient.FilesLocationExist(context.Background()); err == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch2.Reset()
	patch3.Reset()
	patchauthClient.Reset()
	patchauthMirrorClient.Reset()
	patchbaseClient.Reset()
	patchbaseMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

func TestMirrorBosStorageClient_FilesLocationExist_BucketError(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	mirrorClient := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseMirrorClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return mirrorClient, nil
	})
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	storageClient, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "abc",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		fmt.Println("New MirrorBosStorageClient fail")
	}

	patch2 := ApplyMethodFunc(&bceBos.Client{}, "DoesBucketExist",
		func(bucket string) (bool, error) {
			return false, errors.New("test error")
		})
	patch3 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, nil
		})
	if err := storageClient.FilesLocationExist(context.Background()); err == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch2.Reset()
	patch3.Reset()
	patchauthClient.Reset()
	patchauthMirrorClient.Reset()
	patchbaseClient.Reset()
	patchbaseMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

func TestMirrorBosStorageClient_FilesLocationExist_FileNotExist(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	mirrorClient := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseMirrorClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return mirrorClient, nil
	})
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	storageClient, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "abc",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		fmt.Println("New MirrorBosStorageClient fail")
	}

	patch4 := ApplyMethodFunc(&bceBos.Client{}, "DoesBucketExist",
		func(bucket string) (bool, error) {
			return true, nil
		})
	patch5 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, &bce.BceServiceError{
				StatusCode: 404,
			}
		})
	if err := storageClient.FilesLocationExist(context.Background()); err == nil || !cerrs.Is(err, cerrs.ErrNamespaceBosCheckFail) {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch5.Reset()
	patch4.Reset()
	patchauthClient.Reset()
	patchauthMirrorClient.Reset()
	patchbaseClient.Reset()
	patchbaseMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

func TestMirrorBosStorageClient_FilesLocationExist_FileExist(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	mirrorClient := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseMirrorClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return mirrorClient, nil
	})
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	storageClient, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "abc",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		fmt.Println("New MirrorBosStorageClient fail")
	}

	patch4 := ApplyMethodFunc(&bceBos.Client{}, "DoesBucketExist",
		func(bucket string) (bool, error) {
			return true, nil
		})
	patch5 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, nil
		})
	if err := storageClient.FilesLocationExist(context.Background()); err == nil || !cerrs.Is(err, cerrs.ErrNamespaceBosCheckFail) {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch5.Reset()
	patch4.Reset()
	patchauthClient.Reset()
	patchauthMirrorClient.Reset()
	patchbaseClient.Reset()
	patchbaseMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

func TestMirrorBosStorageClient_FilesLocationExist_MirrorError(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	mirrorClient := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseMirrorClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return mirrorClient, nil
	})
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	storageClient, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "abc",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		fmt.Println("New MirrorBosStorageClient fail")
	}

	patch := ApplyMethodFunc(&bceBos.Client{}, "DoesBucketExist",
		func(bucket string) (bool, error) {
			return true, nil
		})
	patch1 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			if bucket == "testbos12345" {
				return &api.GetObjectMetaResult{}, nil
			}
			return &api.GetObjectMetaResult{}, &bce.BceServiceError{
				StatusCode: 404,
			}
		})
	patch2 := ApplyMethodFunc(&bceBos.Client{}, "GetBucketMirror",
		func(bucket string) (*api.PutBucketMirrorArgs, error) {
			return &api.PutBucketMirrorArgs{
				BucketMirroringConfiguration: []api.MirrorConfigurationRule{
					{Prefix: "",
						SourceUrl:       "bos://bj-bos-sandbox.baidu-int.com/testbos12345",
						PassQueryString: false,
						Mode:            "fetch",
						StorageClass:    "STANDARD",
						PassHeaders:     []string{},
						Resource:        "*",
						Suffix:          "",
						FixedKey:        "",
						PrefixReplace:   "",
						Version:         "v1"},
				},
			}, errors.New(("test error"))
		})
	if err := storageClient.FilesLocationExist(context.Background()); err == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch.Reset()
	patch1.Reset()
	patch2.Reset()
	patchauthClient.Reset()
	patchauthMirrorClient.Reset()
	patchbaseClient.Reset()
	patchbaseMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

func TestMirrorBosStorageClient_FilesLocationExist_MirrorRuleNotMatch(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	mirrorClient := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseMirrorClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return mirrorClient, nil
	})
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	storageClient, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "abc",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		fmt.Println("New MirrorBosStorageClient fail")
	}

	patch := ApplyMethodFunc(&bceBos.Client{}, "DoesBucketExist",
		func(bucket string) (bool, error) {
			return true, nil
		})
	patch1 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			if bucket == "testbos12345" {
				return &api.GetObjectMetaResult{}, nil
			}
			return &api.GetObjectMetaResult{}, &bce.BceServiceError{
				StatusCode: 404,
			}
		})
	patch2 := ApplyMethodFunc(&bceBos.Client{}, "GetBucketMirror",
		func(bucket string) (*api.PutBucketMirrorArgs, error) {
			return &api.PutBucketMirrorArgs{
				BucketMirroringConfiguration: []api.MirrorConfigurationRule{
					{Prefix: "",
						SourceUrl:       "bos://bj-bos-sandbox.baidu-int.com/testbos123456",
						PassQueryString: false,
						Mode:            "fetch",
						StorageClass:    "STANDARD",
						PassHeaders:     []string{},
						Resource:        "*",
						Suffix:          "",
						FixedKey:        "",
						PrefixReplace:   "",
						Version:         "v1"},
				},
			}, nil
		})
	if err := storageClient.FilesLocationExist(context.Background()); err == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch.Reset()
	patch1.Reset()
	patch2.Reset()
	patchauthClient.Reset()
	patchauthMirrorClient.Reset()
	patchbaseClient.Reset()
	patchbaseMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

//func TestMirrorBosStorageClient_GetFileContentOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	SetTestMrConf(ctx)
//	configg := loadConf()
//	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
//		func(bucket string) (string, error) {
//			return "bj", nil
//		})
//	clientt, err := NewMirrorBosStorageClient(context.Background(),
//		&StorageParam{
//			StorageType: "",
//			IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
//			MirrorBosParam: MirrorBosParam{
//				Bucket:       "testbos123456",
//				ObjectPrefix: "namespace1/",
//				MirrorBucket: "testbos12345",
//			},
//			S3Param: S3Param{},
//		},
//		&config{
//			BosConf: &bosConf{
//				BosEndpoint:            configg.BosConf.BosEndpoint,
//				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
//				CheckCRC32:             configg.BosConf.CheckCRC32,
//				CheckMD5:               configg.BosConf.CheckMD5,
//				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
//			},
//			MirrorBosConf: configg.MirrorBosConf,
//		})
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
//		fmt.Println("New MirrorBosStorageClient fail")
//		return
//	}
//	fmt.Printf("%+v\n", clientt)
//	if _, err := clientt.GetFileContent(ctx, "0/0.meta", 10240); err != nil {
//		fmt.Printf("MirrorBosStorageClient.GetFileContent() error = %v, wantErr %v\n", err, false)
//	}
//	if _, err := clientt.GetFileContent(ctx, "0/0.meta", 1); err == nil {
//		fmt.Printf("MirrorBosStorageClient.GetFileContent() error = %v, wantErr %v\n", err, true)
//	}
//	clientt.objectPrefix = "only-1-slot1/"
//	if _, err := clientt.GetFileContent(ctx, "100/0.meta", 10240); err == nil {
//		fmt.Printf("MirrorBosStorageClient.GetFileContent() error = %v, wantErr %v\n", err, false)
//	}
//	patchGetBucketLocation.Reset()
//}

func TestMirrorBosStorageClient_GetFileContentO(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	mirrorClient := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseMirrorClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return mirrorClient, nil
	})
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	storageClient, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "abc",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		fmt.Println("New MirrorBosStorageClient fail")
	}
	patch1 := ApplyMethodFunc(&bceBos.Client{}, "BasicGetObject",
		func(bucket, object string) (*api.GetObjectResult, error) {
			return nil, errors.New("test")
		})
	if _, err := storageClient.GetFileContent(context.Background(), "test", 1); err == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch1.Reset()
	patchauthClient.Reset()
	patchauthMirrorClient.Reset()
	patchbaseClient.Reset()
	patchbaseMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

//
//func TestMirrorBosStorageClient_GetFileDownloadInfoOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	pattern := filepath.Join(env.ConfDir(), "servicer", "mr_sts", "*.toml")
//	servicer.MustLoad(ctx, servicer.LoadOptFilesGlob(pattern, false))
//	MrStsConf := sts.MrStsConf{}
//	if err := conf.Parse("multi_region/mr_sts.toml", &MrStsConf); err != nil {
//		fmt.Println("load multi_region/mr_sts.toml failed")
//	}
//	sts.MustInitMrStsSdk(ctx, MrStsConf)
//	configg := loadConf()
//	client, err := NewMirrorBosStorageClient(context.Background(),
//		&StorageParam{
//			StorageType: "",
//			IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
//			MirrorBosParam: MirrorBosParam{
//				Bucket:       "testbos123456",
//				ObjectPrefix: "namespace1/",
//				MirrorBucket: "testbos12345",
//			},
//			S3Param: S3Param{},
//		},
//		&config{
//			BosConf: &bosConf{
//				BosEndpoint:            configg.BosConf.BosEndpoint,
//				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
//				CheckCRC32:             configg.BosConf.CheckCRC32,
//				CheckMD5:               configg.BosConf.CheckMD5,
//				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
//			},
//			MirrorBosConf: configg.MirrorBosConf,
//		})
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
//		fmt.Println("New MirrorBosStorageClient fail")
//	}
//	type fields struct {
//		conf         *config
//		client       *bce_utils.BosClient
//		bucket       string
//		objectPrefix string
//		iamUserID    string
//		mirrorClient *bce_utils.BosClient
//		mirrorBucket string
//	}
//	type args struct {
//		ctx      context.Context
//		filePath string
//	}
//	if _, err := client.GetFileDownloadInfo(ctx, "0/0.meta"); err != nil {
//		t.Errorf("MirrorBosStorageClient.GetFileDownloadInfo() error = %v, wantErr %v", err, false)
//	}
//	if _, err := client.GetFileDownloadInfo(ctx, "0/00000.meta"); err == nil {
//		t.Errorf("MirrorBosStorageClient.GetFileDownloadInfo() error = %v, wantErr %v", err, true)
//	}
//}

func TestMirrorBosStorageClient_GetFileDownloadInfo(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	mirrorClient := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseMirrorClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return mirrorClient, nil
	})
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	storageClient, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "abc",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		fmt.Println("New MirrorBosStorageClient fail")
	}
	patch := ApplyMethodReturn(&bceBos.Client{}, "BasicGeneratePresignedUrl",
		"test")
	patch1 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, nil
		})
	if _, err := storageClient.GetFileDownloadInfo(context.Background(), "test"); err != nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch1.Reset()
	patch.Reset()
	patchauthClient.Reset()
	patchauthMirrorClient.Reset()
	patchbaseClient.Reset()
	patchbaseMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

func TestMirrorBosStorageClient_GetFileDownloadInfo_NoFileValidation(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	mirrorClient := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseMirrorClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return mirrorClient, nil
	})
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	storageClient, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "abc",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             false,
				CheckMD5:               false,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		fmt.Println("New MirrorBosStorageClient fail")
	}
	patch := ApplyMethodReturn(&bceBos.Client{}, "BasicGeneratePresignedUrl",
		"test")
	patch1 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, nil
		})
	if _, err := storageClient.GetFileDownloadInfo(context.Background(), "test"); err != nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch1.Reset()
	patch.Reset()
	patchauthClient.Reset()
	patchauthMirrorClient.Reset()
	patchbaseClient.Reset()
	patchbaseMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

func TestMirrorBosStorageClient_GetFileDownloadInfo_GetMetaError(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	mirrorClient := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseMirrorClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return mirrorClient, nil
	})
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	storageClient, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "abc",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		fmt.Println("New MirrorBosStorageClient fail")
	}
	patch := ApplyMethodReturn(&bceBos.Client{}, "BasicGeneratePresignedUrl",
		"test")
	patch1 := ApplyMethodFunc(&bceBos.Client{}, "GetObjectMeta",
		func(bucket, object string) (*api.GetObjectMetaResult, error) {
			return &api.GetObjectMetaResult{}, errors.New("test error")
		})
	if _, err := storageClient.GetFileDownloadInfo(context.Background(), "test"); err == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch1.Reset()
	patch.Reset()
	patchauthClient.Reset()
	patchauthMirrorClient.Reset()
	patchbaseClient.Reset()
	patchbaseMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

//func TestMirrorBosStorageClient_ListFilesOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	pattern := filepath.Join(env.ConfDir(), "servicer", "mr_sts", "*.toml")
//	servicer.MustLoad(ctx, servicer.LoadOptFilesGlob(pattern, false))
//	MrStsConf := sts.MrStsConf{}
//	if err := conf.Parse("multi_region/mr_sts.toml", &MrStsConf); err != nil {
//		fmt.Println("load multi_region/mr_sts.toml failed")
//	}
//	sts.MustInitMrStsSdk(ctx, MrStsConf)
//	configg := loadConf()
//	client, err := NewMirrorBosStorageClient(context.Background(),
//		&StorageParam{
//			StorageType: "",
//			IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
//			MirrorBosParam: MirrorBosParam{
//				Bucket:       "testbos123456",
//				ObjectPrefix: "namespace1/",
//				MirrorBucket: "testbos12345",
//			},
//			S3Param: S3Param{},
//		},
//		&config{
//			BosConf: &bosConf{
//				BosEndpoint:            configg.BosConf.BosEndpoint,
//				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
//				CheckCRC32:             configg.BosConf.CheckCRC32,
//				CheckMD5:               configg.BosConf.CheckMD5,
//				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
//			},
//			MirrorBosConf: configg.MirrorBosConf,
//		})
//	if err != nil {
//		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
//		fmt.Println("New MirrorBosStorageClient fail")
//	}
//	client.objectPrefix = "ut/list/success"
//	if _, err := client.ListFiles(context.Background(), ""); err != nil {
//		t.Errorf("MirrorBosStorageClient.ListFiles() error = %v, wantErr %v", err, false)
//	}
//}

func TestMirrorBosStorageClient_ListFiles_Success(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	mirrorClient := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseMirrorClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return mirrorClient, nil
	})
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	storageClient, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "abc",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		fmt.Println("New MirrorBosStorageClient fail")
	}
	patch := ApplyMethodReturn(&bceBos.Client{}, "ListObjects", &api.ListObjectsResult{
		Contents: []api.ObjectSummaryType{
			{Key: "TEST/", Size: 1},
			{Key: "TEST/TEST", Size: 2},
		},
	}, nil)
	if rsp, err := storageClient.ListFiles(context.Background(), ""); err != nil ||
		!reflect.DeepEqual(*rsp, listFilesResult{
			Files: []fileInfo{
				{Name: "TEST/TEST", Size: 2},
			},
			IsTruncated: false,
			NextMarker:  "",
		}) {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch.Reset()
	//patch1 := ApplyMethodReturn(&bceBos.Client{}, "ListObjects", &api.ListObjectsResult{
	//	Contents: []api.ObjectSummaryType{
	//		{Key: "TEST/", Size: 1},
	//		{Key: "TEST/TEST", Size: 2},
	//	},
	//}, errors.New("error"))
	//if _, err := storageClient.ListFiles(context.Background(), ""); err == nil {
	//	t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	//}
	//patch1.Reset()
	patchauthClient.Reset()
	patchauthMirrorClient.Reset()
	patchbaseClient.Reset()
	patchbaseMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

func TestMirrorBosStorageClient_ListFiles_Error(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	client := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return client, nil
	})
	mirrorClient := &bce_utils.BosClient{Client: &bceBos.Client{}}
	patchbaseMirrorClient := ApplyFunc(bce_utils.NewBosClientWithSts, func(ak, sk, sessionToken, endpoint string) (*bce_utils.BosClient, error) {
		return mirrorClient, nil
	})
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	storageClient, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "abc",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		fmt.Println("New MirrorBosStorageClient fail")
	}
	patch1 := ApplyMethodReturn(&bceBos.Client{}, "ListObjects", &api.ListObjectsResult{
		Contents: []api.ObjectSummaryType{
			{Key: "TEST/", Size: 1},
			{Key: "TEST/TEST", Size: 2},
		},
	}, errors.New("error"))
	if _, err := storageClient.ListFiles(context.Background(), ""); err == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch1.Reset()
	patchauthClient.Reset()
	patchauthMirrorClient.Reset()
	patchbaseClient.Reset()
	patchbaseMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

func TestNewMirrorBosStorageClient(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	SetTestMrConf(ctx)
	configg := loadConf()
	type args struct {
		ctx    context.Context
		param  *StorageParam
		config *config
	}
	tests := []struct {
		name    string
		args    args
		want    StorageClient
		wantErr bool
	}{
		{name: "test no param", args: args{
			ctx:    context.Background(),
			param:  nil,
			config: &config{},
		}, want: nil, wantErr: true},
		{name: "test no param", args: args{
			ctx: context.Background(),
			param: &StorageParam{
				MirrorBosParam: MirrorBosParam{
					Bucket:       "testbos123456",
					ObjectPrefix: "TEST/",
					MirrorBucket: "testbos12345",
				},
			},
			config: nil,
		}, want: nil, wantErr: true},
		{name: "test get auth fail", args: args{
			ctx: context.Background(),
			param: &StorageParam{
				StorageType: "",
				IamUserID:   "",
				MirrorBosParam: MirrorBosParam{
					Bucket:       "testbos123456",
					ObjectPrefix: "TEST/",
					MirrorBucket: "testbos12345",
				},
				S3Param: S3Param{},
			},
			config: &config{
				BosConf: &bosConf{
					BosEndpoint:            configg.BosConf.BosEndpoint,
					BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
					CheckCRC32:             configg.BosConf.CheckCRC32,
					CheckMD5:               configg.BosConf.CheckMD5,
					PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
				},
				MirrorBosConf: configg.MirrorBosConf,
			},
		}, want: nil, wantErr: true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewMirrorBosStorageClient(tt.args.ctx, tt.args.param, tt.args.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewMirrorBosStorageClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("NewMirrorBosStorageClient() got = %v, want %v", got, tt.want)
			//}
		})
	}
	patchauthClient := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("error")
	})
	_, err := NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err == nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		t.Errorf("New MirrorBosStorageClient fail")
	}
	patchauthClient.Reset()
	patchauthClient = ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, nil
	})
	patchGetBucketLocation := ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "nj", nil
		})
	_, err = NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err == nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		t.Errorf("New MirrorBosStorageClient fail")
	}
	patchGetBucketLocation.Reset()
	patchGetBucketLocation = ApplyMethodFunc(&bceBos.Client{}, "GetBucketLocation",
		func(bucket string) (string, error) {
			return "bj", nil
		})
	patchauthMirrorClient := ApplyFunc(compo_utils.MrGetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{
			Credential: &common.Credential{
				Ak:           "TEST",
				Sk:           "TEST",
				SessionToken: "TEST",
			},
		}, errors.New("error")
	})
	_, err = NewMirrorBosStorageClient(context.Background(),
		&StorageParam{
			StorageType: "",
			IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
			MirrorBosParam: MirrorBosParam{
				Bucket:       "testbos123456",
				ObjectPrefix: "namespace1/",
				MirrorBucket: "testbos12345",
			},
			S3Param: S3Param{},
		},
		&config{
			BosConf: &bosConf{
				BosEndpoint:            configg.BosConf.BosEndpoint,
				BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
				CheckCRC32:             configg.BosConf.CheckCRC32,
				CheckMD5:               configg.BosConf.CheckMD5,
				PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
			},
			MirrorBosConf: configg.MirrorBosConf,
		})
	if err == nil {
		logger.ComponentLogger.Warning(ctx, "New MirrorBosStorageClient fail", logit.Error("err", err))
		t.Errorf("New MirrorBosStorageClient fail")
	}
	patchauthMirrorClient.Reset()
	patchGetBucketLocation.Reset()
}

//func TestNewMirrorBosStorageClientOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	SetTestMrConf(ctx)
//	configg := loadConf()
//	type args struct {
//		ctx    context.Context
//		param  *StorageParam
//		config *config
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    StorageClient
//		wantErr bool
//	}{
//		//{name: "test no param", args: args{
//		//	ctx:    context.Background(),
//		//	param:  nil,
//		//	config: &config{},
//		//}, want: nil, wantErr: true},
//		{name: "test no endpoint", args: args{
//			ctx: context.Background(),
//			param: &StorageParam{
//				StorageType: "",
//				IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
//				MirrorBosParam: MirrorBosParam{
//					Bucket:       "testbos123456",
//					ObjectPrefix: "namespace1/",
//					MirrorBucket: "testbos12345",
//				},
//				S3Param: S3Param{},
//			},
//			config: &config{
//				BosConf: &bosConf{
//					BosEndpoint:            configg.BosConf.BosEndpoint,
//					BosListMaxKeys:         configg.BosConf.BosListMaxKeys,
//					CheckCRC32:             configg.BosConf.CheckCRC32,
//					CheckMD5:               configg.BosConf.CheckMD5,
//					PresignedUrlExpiration: configg.BosConf.PresignedUrlExpiration,
//				},
//				MirrorBosConf: configg.MirrorBosConf,
//			},
//		}, want: nil, wantErr: false},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := NewMirrorBosStorageClient(tt.args.ctx, tt.args.param, tt.args.config)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("NewMirrorBosStorageClient() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			fmt.Println(got.GetFileDownloadInfo(ctx, "0/0.meta"))
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("NewMirrorBosStorageClient() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func Test_prefixMatch(t *testing.T) {
	type args struct {
		resource string
		prefix   string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{"test1", args{"*", "abc"}, true},
		{"test2", args{"*ff", "abc"}, false},
		{"test2", args{"ff*", "abc"}, false},
		{"test2", args{"ab*", "abc"}, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := prefixMatch(tt.args.resource, tt.args.prefix); got != tt.want {
				t.Errorf("prefixMatch() = %v, want %v", got, tt.want)
			}
		})
	}
}
