/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package namespace

import (
	"bufio"
	"context"
	"errors"
	"io"
	"path/filepath"
	"strings"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/bos/api"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bce_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type MirrorBosStorageClient struct {
	conf         *config
	client       *bce_utils.BosClient
	mirrorClient *bce_utils.BosClient
	bucket       string
	mirrorBucket string
	objectPrefix string
	iamUserID    string
}

func prefixMatch(resource, prefix string) bool {
	if resource == "*" {
		return true
	}
	if !strings.HasSuffix(resource, "*") {
		return false
	}
	if !strings.HasPrefix(prefix, strings.Split(resource, "*")[0]) {
		return false
	}
	return true
}

func (bc *MirrorBosStorageClient) ListFiles(ctx context.Context, marker string) (*listFilesResult, error) {

	args := new(api.ListObjectsArgs)
	args.Prefix = bc.objectPrefix
	args.MaxKeys = cast.ToInt(bc.conf.BosConf.BosListMaxKeys)
	args.Marker = marker
	listObjectResult, err := bc.mirrorClient.ListObjects(bc.mirrorBucket, args)
	if err != nil {
		logger.ComponentLogger.Error(ctx, "List bos files fail", logit.String("list files", err.Error()))
		return nil, err
	}
	rsp := &listFilesResult{
		Files:       make([]fileInfo, 0),
		IsTruncated: listObjectResult.IsTruncated,
		NextMarker:  listObjectResult.NextMarker,
	}
	for _, obj := range listObjectResult.Contents {
		// 忽略"目录"文件
		if strings.HasSuffix(obj.Key, "/") {
			continue
		}
		rsp.Files = append(rsp.Files, fileInfo{
			Name: obj.Key,
			Size: cast.ToInt64(obj.Size),
		})
	}
	return rsp, nil
}

func (bc *MirrorBosStorageClient) FilesLocationExist(ctx context.Context) error {
	// 因为get bucket locaction的时候已经验证过mirror bos bucket,因此这里不需要验证
	exists, err := bc.client.DoesBucketExist(bc.bucket)
	if err == nil && !exists {
		return cerrs.ErrNamespaceBosCheckFail.Errorf("Bucket %s not exist", bc.bucket)
	}
	if err != nil {
		logger.ComponentLogger.Error(ctx, "Check bucket fail", logit.String("err", err.Error()))
		return err
	}

	// 文件路径在mirror bucket存在,在本地bucket不存在
	_, err = bc.mirrorClient.GetObjectMeta(bc.mirrorBucket, bc.objectPrefix)
	if err != nil {
		logger.ComponentLogger.Error(ctx, "get object meta fail",
			logit.String("err", err.Error()), logit.String("bucket", bc.mirrorBucket),
			logit.String("objectPrefix", bc.objectPrefix))
		if realErr, ok := err.(*bce.BceServiceError); ok {
			if realErr.StatusCode == 404 || realErr.StatusCode == 403 {
				return cerrs.ErrNamespaceBosCheckFail.Errorf("Check object fail:%s",
					base_utils.Format(realErr))
			}
		}
		return err
	}
	// 文件路径在mirror bucket存在,在本地bucket不存在
	_, err = bc.client.GetObjectMeta(bc.bucket, bc.objectPrefix)
	if err == nil {
		logger.ComponentLogger.Error(ctx, "get object meta success, except fail",
			logit.String("bucket", bc.bucket),
			logit.String("objectPrefix", bc.objectPrefix))
		return cerrs.ErrNamespaceBosCheckFail.Errorf("ObjectPrefix %s is exist on %s", bc.objectPrefix,
			bc.bucket)
	}
	//404表示不存在,符合预期
	if realErr, ok := err.(*bce.BceServiceError); ok {
		if realErr.StatusCode != 404 {
			return cerrs.ErrNamespaceBosCheckFail.Errorf("Check object fail:%s",
				base_utils.Format(realErr))
		}
	}

	// 检查镜像回源配置
	mirror, err := bc.client.GetBucketMirror(bc.bucket)
	if err != nil {
		logger.ComponentLogger.Error(ctx, "Get bucket mirror fail",
			logit.String("bucket", bc.bucket), logit.String("err", err.Error()))
		return cerrs.ErrNamespaceBosCheckFail.Errorf("Get bucket mirror fail:%s, bucket:%s",
			base_utils.Format(err), bc.bucket)
	}
	found := false
	for _, mirrorConf := range mirror.BucketMirroringConfiguration {
		//配置需满足如下条件:
		//回源方式是镜像
		//不传querystring
		//回源地址为bos bucket
		//回源地址不配置前后缀,不配置替换
		if mirrorConf.Mode != "fetch" || mirrorConf.PassQueryString ||
			mirrorConf.PrefixReplace != "" || mirrorConf.Suffix != "" || mirrorConf.Prefix != "" ||
			!strings.HasPrefix(mirrorConf.SourceUrl, "bos://") ||
			!strings.HasSuffix(mirrorConf.SourceUrl, "/"+bc.mirrorBucket) ||
			!prefixMatch(mirrorConf.Resource, bc.objectPrefix) {
			logger.ComponentLogger.Warning(ctx, "Bucket mirror conf is illegal",
				logit.String("mirrorConf", base_utils.Format(mirrorConf)))
			continue
		}
		found = true
		break
	}
	if !found {
		logger.ComponentLogger.Error(ctx, "Bucket %s not set mirror", bc.bucket)
		return cerrs.ErrNamespaceBosCheckFail.Errorf("Bucket %s not set mirror", bc.bucket)
	}
	return nil
}

func (bc *MirrorBosStorageClient) FileNotExist(ctx context.Context, filePath string) error {
	_, err := bc.mirrorClient.GetObjectMeta(bc.mirrorBucket, filepath.Join(bc.objectPrefix, filePath))
	if err == nil {
		return cerrs.ErrNamespaceError.Errorf("slot has no sst file but meta file:%s exist", filePath)
	}
	if realErr, ok := err.(*bce.BceServiceError); ok {
		if realErr.StatusCode == 404 {
			return nil
		}
	}
	return err
}

func (bc *MirrorBosStorageClient) GetFileDownloadInfo(ctx context.Context, filePath string) (*fileDownloadInfoResult, error) {
	info := &fileDownloadInfoResult{
		Url: bc.client.BasicGeneratePresignedUrl(bc.bucket,
			filepath.Join(bc.objectPrefix, filePath),
			bc.conf.BosConf.PresignedUrlExpiration),
	}
	if !bc.conf.BosConf.CheckCRC32 && !bc.conf.BosConf.CheckMD5 {
		return info, nil
	}
	objectMeta, err := bc.mirrorClient.GetObjectMeta(bc.mirrorBucket,
		filepath.Join(bc.objectPrefix, filePath))
	if err != nil {
		logger.ComponentLogger.Error(ctx, "Get object meta fail", logit.String("err", err.Error()),
			logit.String("rsp", base_utils.Format(objectMeta)))
		return info, err
	}
	info.Crc32 = objectMeta.ContentCrc32
	info.Md5 = objectMeta.ContentMD5
	return info, nil
}

func (bc *MirrorBosStorageClient) GetFileContent(ctx context.Context, filePath string, bufferSize int) ([]byte, error) {
	res, err := bc.mirrorClient.BasicGetObject(bc.mirrorBucket, filepath.Join(bc.objectPrefix, filePath))
	if err != nil {
		logger.ComponentLogger.Error(ctx, "Get object fail", logit.String("err", err.Error()))

		if realErr, ok := err.(*bce.BceServiceError); ok {
			if realErr.StatusCode == 404 {
				return nil, cerrs.ErrNamespaceStorageNoSuchFile.Errorf("file %s not exist", filePath)
			}
		}
		return nil, err
	}
	stream := res.Body
	// 确保关闭Object读取流
	defer stream.Close()
	var buffer []byte
	reader := bufio.NewReader(stream)
	for {
		line, err := reader.ReadBytes('\n')
		buffer = append(buffer, line...)
		if len(buffer) > bufferSize {
			return nil, errors.New("content is too long")
		}
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}
	}
	return buffer, nil
}

func NewMirrorBosStorageClient(ctx context.Context, param *StorageParam, config *config) (*MirrorBosStorageClient, error) {
	if param == nil || param.MirrorBosParam.Bucket == "" || param.MirrorBosParam.ObjectPrefix == "" ||
		param.MirrorBosParam.MirrorBucket == "" {
		return nil, cerrs.ErrNamespaceInvalidParam.Errorf("bucket or objectPrefix is empty")
	}
	if config == nil || config.BosConf == nil ||
		config.BosConf.BosListMaxKeys == 0 || config.BosConf.BosEndpoint == "" ||
		config.BosConf.PresignedUrlExpiration == 0 ||
		len(config.MirrorBosConf) == 0 {
		return nil, cerrs.ErrNamespaceInvalidParam.Errorf("namespace config for bos is invalid")
	}
	bosClient := &MirrorBosStorageClient{
		conf:         config,
		bucket:       param.MirrorBosParam.Bucket,
		mirrorBucket: param.MirrorBosParam.MirrorBucket,
		objectPrefix: param.MirrorBosParam.ObjectPrefix,
		iamUserID:    param.IamUserID,
	}
	auth, err := compo_utils.GetOpenapiAuth(ctx, param.IamUserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth fail", logit.Error("err", err),
			logit.String("iamUserID", param.IamUserID))
		return nil, err
	}
	client, err := bce_utils.NewBosClientWithSts(auth.Credential.Ak, auth.Credential.Sk,
		auth.Credential.SessionToken, config.BosConf.BosEndpoint)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get bos client fail", logit.Error("err", err))
		return nil, err
	}
	bosClient.client = client

	location, err := bosClient.client.GetBucketLocation(param.MirrorBosParam.MirrorBucket)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get bos bucket location fail",
			logit.String("bucket", param.MirrorBosParam.MirrorBucket), logit.Error("err", err))
		return nil, cerrs.ErrNamespaceBosCheckFail.Errorf("found bucket location fail")
	}
	var scsRegion, bosEndpoint string
	for _, mConf := range config.MirrorBosConf {
		if mConf.BosRegion == location {
			scsRegion = mConf.ScsRegion
			bosEndpoint = mConf.BosEndpoint
		}
	}
	if scsRegion == "" || bosEndpoint == "" {
		logger.ComponentLogger.Warning(ctx, "Found config for bos region fail",
			logit.String("location", location), logit.String("bucket", param.MirrorBosParam.MirrorBucket))
		return nil, cerrs.ErrNamespaceBosCheckFail.Errorf("found config for bos region fail")
	}
	mr_auth, err := compo_utils.MrGetOpenapiAuth(ctx, param.IamUserID, scsRegion)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get mr auth fail", logit.Error("err", err),
			logit.String("iamUserID", param.IamUserID), logit.String("scsRegion", scsRegion))
		return nil, err
	}
	mirrorClient, err := bce_utils.NewBosClientWithSts(mr_auth.Credential.Ak,
		mr_auth.Credential.Sk, mr_auth.Credential.SessionToken, bosEndpoint)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get mirror bos client fail", logit.Error("err", err))
		return nil, err
	}
	bosClient.mirrorClient = mirrorClient
	return bosClient, nil
}
