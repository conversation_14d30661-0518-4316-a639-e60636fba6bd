package namespace

import (
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
)

type config struct {
	SstDownloadConf *sstDownloadConf
	SstCleanupConf  *sstCleanupConf
	OperationConf   *operationConf
	BosConf         *bosConf
	S3Conf          *s3Conf
	MirrorBosConf   []mirrorBosConf
	loadOnce        sync.Once `toml:"-"`
}

type sstDownloadConf struct {
	DownloadParentDir         string
	DiskUsagePercentThreshold int64
	DiskFreeBytesThreshold    int64
	TrafficLimitMB            int
	MaxSlotNum                int
	MaxSstFileSize            int64
}

type sstCleanupConf struct {
	Step int
}

type operationConf struct {
	ReplicationOffset int
	ConnectRetry      int
	ConnectTimeout    int
	ReadTimeout       int
	WriteTimeout      int
}

type bosConf struct {
	BosEndpoint            string
	BosListMaxKeys         int
	CheckCRC32             bool
	CheckMD5               bool
	PresignedUrlExpiration int
}

type s3Conf struct {
	S3Endpoint             string
	S3ListMaxKeys          int
	CheckCRC32             bool
	PresignedUrlExpiration int
	Accesskey              string
	Secretkey              string
	UseSSL                 bool
}

type mirrorBosConf struct {
	BosRegion   string
	BosEndpoint string
	ScsRegion   string
}

var defaultConf = &config{}

func loadConf() *config {
	defaultConf.loadOnce.Do(func() {
		if err := compo_utils.LoadConf("namespace", defaultConf); err != nil {
			panic(err)
		}
	})

	return defaultConf
}
