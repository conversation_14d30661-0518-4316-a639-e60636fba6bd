package namespace

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"testing"

	"github.com/go-redis/redismock/v8"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

func TestOperationClient_NamespaceDetails(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	db, mock := redismock.NewClientMock()
	client, err := GetOperationClient(context.Background(), "localhost", 0, "password")
	if err != nil {
		t.Errorf("GetOperationClient() error = %v", err)
	}
	client.client = &single_redis.SingleClient{Client: db}
	mock.ExpectDo("namespace", "details").SetErr(errors.New("error"))
	rsp, err := client.NamespaceDetails(context.Background())
	if err == nil {
		t.<PERSON><PERSON>("validate err fail")
	}
	mock.ExpectDo("namespace", "details").SetVal([]interface{}{"abc", "bca"})
	rsp, err = client.NamespaceDetails(context.Background())
	if err != nil {
		t.Errorf("validate success fail")
	}
	exp := map[string]string{"abc": "bca"}
	if !reflect.DeepEqual(rsp, exp) {
		t.Errorf("validate success fail")
	}
}

func TestOperationClient_SwitchNamespace(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	db, mock := redismock.NewClientMock()
	client, err := GetOperationClient(context.Background(), "localhost", 0, "password")
	if err != nil {
		t.Errorf("GetOperationClient() error = %v", err)
	}
	client.client = &single_redis.SingleClient{Client: db}
	mock.ExpectDo("bulkload", "switchnamespace", "test").SetErr(errors.New("error"))
	err = client.SwitchNamespace(context.Background(), "test")
	if err == nil {
		t.Errorf("validate err fail")
	}
	mock.ExpectDo("bulkload", "switchnamespace", "test").SetVal("OK")
	err = client.SwitchNamespace(context.Background(), "test")
	if err != nil {
		t.Errorf("validate success fail")
	}
	mock.ExpectDo("bulkload", "switchnamespace", "test").SetVal("test")
	err = client.SwitchNamespace(context.Background(), "test")
	if err == nil {
		t.Errorf("validate success fail")
	}
}

func TestOperationClient_DelNamespace(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	db, mock := redismock.NewClientMock()
	client, err := GetOperationClient(context.Background(), "localhost", 0, "password")
	if err != nil {
		t.Errorf("GetOperationClient() error = %v", err)
	}
	client.client = &single_redis.SingleClient{Client: db}
	mock.ExpectDo("bulkload", "delnamespace", "test").SetErr(errors.New("error"))
	err = client.DelNamespace(context.Background(), "test")
	if err == nil {
		t.Errorf("validate err fail")
	}
	mock.ExpectDo("bulkload", "delnamespace", "test").SetVal("OK")
	err = client.DelNamespace(context.Background(), "test")
	if err != nil {
		t.Errorf("validate success fail")
	}
	mock.ExpectDo("bulkload", "delnamespace", "test").SetVal("test")
	err = client.DelNamespace(context.Background(), "test")
	if err == nil {
		t.Errorf("validate success fail")
	}
}

func TestOperationClient_IngestDir(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	db, mock := redismock.NewClientMock()
	client, err := GetOperationClient(context.Background(), "localhost", 0, "password")
	if err != nil {
		t.Errorf("GetOperationClient() error = %v", err)
	}
	client.client = &single_redis.SingleClient{Client: db}
	mock.ExpectDo("bulkload", "ingest", "test", "test").SetErr(errors.New("error"))
	err = client.IngestDir(context.Background(), "test", "test")
	if err == nil {
		t.Errorf("validate err fail")
	}
	mock.ExpectDo("bulkload", "ingest", "test", "test").SetVal("OK")
	err = client.IngestDir(context.Background(), "test", "test")
	if err != nil {
		t.Errorf("validate success fail")
	}
	mock.ExpectDo("bulkload", "ingest", "test", "test").SetVal("test")
	err = client.IngestDir(context.Background(), "test", "test")
	if err == nil {
		t.Errorf("validate success fail")
	}
}

func TestOperationClient_IngestDirWithTaskID(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	db, mock := redismock.NewClientMock()
	client, err := GetOperationClient(context.Background(), "localhost", 0, "password")
	if err != nil {
		t.Errorf("GetOperationClient() error = %v", err)
	}
	client.client = &single_redis.SingleClient{Client: db}
	mock.ExpectDo("bulkload", "ingest", "test", "test", "taskid").SetErr(errors.New("error"))
	err = client.IngestDirWithTaskID(context.Background(), "test", "test", "taskid")
	if err == nil {
		t.Errorf("validate err fail")
	}
	mock.ExpectDo("bulkload", "ingest", "test", "test", "taskid").SetVal("OK")
	err = client.IngestDirWithTaskID(context.Background(), "test", "test", "taskid")
	if err != nil {
		t.Errorf("validate success fail")
	}
	mock.ExpectDo("bulkload", "ingest", "test", "test", "taskid").SetVal("test")
	err = client.IngestDirWithTaskID(context.Background(), "test", "test", "taskid")
	if err == nil {
		t.Errorf("validate success fail")
	}
}

func TestOperationClient_GetSwitchTask(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	db, mock := redismock.NewClientMock()
	client, err := GetOperationClient(context.Background(), "localhost", 0, "password")
	if err != nil {
		t.Errorf("GetOperationClient() error = %v", err)
	}
	client.client = &single_redis.SingleClient{Client: db}
	mock.ExpectDo("bulkload", "switchnamespacestatus").SetErr(errors.New("error"))
	rsp, err := client.GetSwitchTask(context.Background())
	if err == nil {
		t.Errorf("validate err fail")
	}
	mock.ExpectDo("bulkload", "switchnamespacestatus").SetVal([]interface{}{"status", "success", "namespace", "test"})
	rsp, err = client.GetSwitchTask(context.Background())
	if err != nil {
		t.Errorf("validate success fail")
	}
	exp := &OperationTask{
		Status:    "success",
		Namespace: "test",
		Dir:       "",
		KeyNum:    "",
	}
	if !reflect.DeepEqual(rsp, exp) {
		t.Errorf("validate success fail")
	}
}

func TestOperationClient_GetDelTask(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	db, mock := redismock.NewClientMock()
	client, err := GetOperationClient(context.Background(), "localhost", 0, "password")
	if err != nil {
		t.Errorf("GetOperationClient() error = %v", err)
	}
	client.client = &single_redis.SingleClient{Client: db}
	mock.ExpectDo("bulkload", "delnamespacestatus").SetErr(errors.New("error"))
	rsp, err := client.GetDelTask(context.Background())
	if err == nil {
		t.Errorf("validate err fail")
	}
	mock.ExpectDo("bulkload", "delnamespacestatus").SetVal([]interface{}{"status", "success", "namespace", "test"})
	rsp, err = client.GetDelTask(context.Background())
	if err != nil {
		t.Errorf("validate success fail")
	}
	exp := &OperationTask{
		Status:    "success",
		Namespace: "test",
		Dir:       "",
		KeyNum:    "",
	}
	if !reflect.DeepEqual(rsp, exp) {
		t.Errorf("validate success fail")
	}
}

func TestOperationClient_GetIngestTask(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	db, mock := redismock.NewClientMock()
	client, err := GetOperationClient(context.Background(), "localhost", 0, "password")
	if err != nil {
		t.Errorf("GetOperationClient() error = %v", err)
	}
	client.client = &single_redis.SingleClient{Client: db}
	mock.ExpectDo("bulkload", "ingeststatus").SetErr(errors.New("error"))
	rsp, err := client.GetIngestTask(context.Background())
	if err == nil {
		t.Errorf("validate err fail")
	}
	mock.ExpectDo("bulkload", "ingeststatus").SetVal([]interface{}{"status", "success", "namespace", "test", "dir", "test_dir", "keys", "6500", "taskid", "taskid"})
	rsp, err = client.GetIngestTask(context.Background())
	if err != nil {
		t.Errorf("validate success fail")
	}
	exp := &OperationTask{
		Status:    "success",
		Namespace: "test",
		Dir:       "test_dir",
		KeyNum:    "6500",
		TaskID:    "taskid",
	}
	if !reflect.DeepEqual(rsp, exp) {
		t.Errorf("validate success fail")
	}
}

func TestOperationClient_CheckPegaSizeEnough(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	db, mock := redismock.NewClientMock()
	client, err := GetOperationClient(context.Background(), "localhost", 0, "password")
	defer client.Close(ctx)
	if err != nil {
		t.Errorf("GetOperationClient() error = %v", err)
	}
	client.client = &single_redis.SingleClient{Client: db}
	mock.ExpectDo("info", "memory").SetErr(errors.New("error"))
	err = client.CheckPegaSizeEnough(context.Background(), 0)
	if err == nil {
		t.Errorf("validate err fail")
	}
	mock.ExpectDo("info", "memory").SetVal(
		strings.Join([]string{
			"$600",
			"\r\n# Memory",
			"\r\nused_real_memory:55398400",
			"\r\nused_real_memory_rss:55398400",
			"\r\nused_real_memory_human:52.83M",
			"\r\nused_real_memory_peak:55398400",
			"\r\nused_real_memory_peak_human:52.83M",
			"\r\nused_memory_lua:36864",
			"\r\nused_memory_lua_human:36.00K",
			"\r\nused_dbsize:0",
			"\r\nused_dbsize_human:0B",
			"\r\nused_memory:0",
			"\r\nused_memory_human:0B",
			"\r\nmax_dbsize_byte:0",
			"\r\nmax_dbsize:0G",
			"\r\nused_db_percent:0%",
			"\r\nused_dbsize_peak:0",
			"\r\nused_dbsize_peak_human:0B",
			"\r\nused_disk_size:1011338153984",
			"\r\nused_disk_size_human:1.47T",
			"\r\ndisk_capacity:1792895266816",
			"\r\ndisk_capacity_human:1.63T",
			"\r\nused_disk_percent:89%",
			"\r\nused_disk_size_peak:1683013971968",
			"\r\nused_disk_size_peak_human:1.53T"}, ""))
	err = client.CheckPegaSizeEnough(context.Background(), 0)
	if err != nil {
		t.Errorf("validate success fail")
	}
	mock.ExpectDo("info", "memory").SetVal(
		strings.Join([]string{
			"$600",
			"\r\n# Memory",
			"\r\nused_real_memory:55398400",
			"\r\nused_real_memory_rss:55398400",
			"\r\nused_real_memory_human:52.83M",
			"\r\nused_real_memory_peak:55398400",
			"\r\nused_real_memory_peak_human:52.83M",
			"\r\nused_memory_lua:36864",
			"\r\nused_memory_lua_human:36.00K",
			"\r\nused_dbsize:0",
			"\r\nused_dbsize_human:0B",
			"\r\nused_memory:0",
			"\r\nused_memory_human:0B",
			"\r\nmax_dbsize_byte:2",
			"\r\nmax_dbsize:0G",
			"\r\nused_db_percent:0%",
			"\r\nused_dbsize_peak:0",
			"\r\nused_dbsize_peak_human:0B",
			"\r\nused_disk_size:1611338153984",
			"\r\nused_disk_size_human:1.47T",
			"\r\ndisk_capacity:1792895266816",
			"\r\ndisk_capacity_human:1.63T",
			"\r\nused_disk_percent:89%",
			"\r\nused_disk_size_peak:1683013971968",
			"\r\nused_disk_size_peak_human:1.53T"}, ""))
	err = client.CheckPegaSizeEnough(context.Background(), 0)
	fmt.Println(err)
	if err == nil {
		t.Errorf("validate dbsize free bytes err")
	}
	mock.ExpectDo("info", "memory").SetVal(
		strings.Join([]string{
			"$600",
			"\r\n# Memory",
			"\r\nused_real_memory:55398400",
			"\r\nused_real_memory_rss:55398400",
			"\r\nused_real_memory_human:52.83M",
			"\r\nused_real_memory_peak:55398400",
			"\r\nused_real_memory_peak_human:52.83M",
			"\r\nused_memory_lua:36864",
			"\r\nused_memory_lua_human:36.00K",
			"\r\nused_dbsize:1611338153984",
			"\r\nused_dbsize_human:0B",
			"\r\nused_memory:0",
			"\r\nused_memory_human:0B",
			"\r\nmax_dbsize_byte:1792895266816",
			"\r\nmax_dbsize:0G",
			"\r\nused_db_percent:0%",
			"\r\nused_dbsize_peak:0",
			"\r\nused_dbsize_peak_human:0B",
			"\r\nused_disk_size:1",
			"\r\nused_disk_size_human:1.47T",
			"\r\ndisk_capacity:1",
			"\r\ndisk_capacity_human:1.63T",
			"\r\nused_disk_percent:89%",
			"\r\nused_disk_size_peak:1683013971968",
			"\r\nused_disk_size_peak_human:1.53T"}, ""))
	err = client.CheckPegaSizeEnough(context.Background(), 0)
	fmt.Println(err)
	if err == nil {
		t.Errorf("validate dbsize threshold bytes err")
	}
	mock.ExpectDo("info", "memory").SetVal(
		strings.Join([]string{
			"$600",
			"\r\n# Memory",
			"\r\nused_real_memory:55398400",
			"\r\nused_real_memory_rss:55398400",
			"\r\nused_real_memory_human:52.83M",
			"\r\nused_real_memory_peak:55398400",
			"\r\nused_real_memory_peak_human:52.83M",
			"\r\nused_memory_lua:36864",
			"\r\nused_memory_lua_human:36.00K",
			"\r\nused_dbsize:0",
			"\r\nused_dbsize_human:0B",
			"\r\nused_memory:0",
			"\r\nused_memory_human:0B",
			"\r\nmax_dbsize_byte:0",
			"\r\nmax_dbsize:0G",
			"\r\nused_db_percent:0%",
			"\r\nused_dbsize_peak:0",
			"\r\nused_dbsize_peak_human:0B",
			"\r\nused_disk_size:1611338153984",
			"\r\nused_disk_size_human:1.47T",
			"\r\ndisk_capacity:1792895266816",
			"\r\ndisk_capacity_human:1.63T",
			"\r\nused_disk_percent:89%",
			"\r\nused_disk_size_peak:1683013971968",
			"\r\nused_disk_size_peak_human:1.53T"}, ""))
	err = client.CheckPegaSizeEnough(context.Background(), 0)
	if err == nil {
		t.Errorf("validate disk threshold fail")
	}
	mock.ExpectDo("info", "memory").SetVal(
		strings.Join([]string{
			"$600",
			"\r\n# Memory",
			"\r\nused_real_memory:55398400",
			"\r\nused_real_memory_rss:55398400",
			"\r\nused_real_memory_human:52.83M",
			"\r\nused_real_memory_peak:55398400",
			"\r\nused_real_memory_peak_human:52.83M",
			"\r\nused_memory_lua:36864",
			"\r\nused_memory_lua_human:36.00K",
			"\r\nused_dbsize:0",
			"\r\nused_dbsize_human:0B",
			"\r\nused_memory:0",
			"\r\nused_memory_human:0B",
			"\r\nmax_dbsize_byte:0",
			"\r\nmax_dbsize:0G",
			"\r\nused_db_percent:0%",
			"\r\nused_dbsize_peak:0",
			"\r\nused_dbsize_peak_human:0B",
			"\r\nused_disk_size:1711338153984",
			"\r\nused_disk_size_human:1.47T",
			"\r\ndisk_capacity:1792895266816",
			"\r\ndisk_capacity_human:1.63T",
			"\r\nused_disk_percent:89%",
			"\r\nused_disk_size_peak:1683013971968",
			"\r\nused_disk_size_peak_human:1.53T"}, ""))
	err = client.CheckPegaSizeEnough(context.Background(), 0)
	if err == nil {
		t.Errorf("validate disk free bytes fail")
	}

}
