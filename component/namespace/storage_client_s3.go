package namespace

import (
	"context"
	"errors"
	"io"
	"net/url"
	"path/filepath"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
)

type S3StorageClient struct {
	conf         *config
	client       *minio.Client
	bucket       string
	objectPrefix string
	iamUserID    string
}

func (sc *S3StorageClient) FilesLocationExist(ctx context.Context) error {
	exists, err := sc.client.BucketExists(ctx, sc.bucket)
	if err == nil && !exists {
		return cerrs.ErrNamespaceBosCheckFail.Errorf("Bucket %s not exist", sc.bucket)
	}
	if err != nil {
		logger.ComponentLogger.Error(ctx, "Check bucket fail", logit.String("err", err.Error()))
		if minio.ToErrorResponse(err).Code == "AccessDenied" {
			return cerrs.ErrNamespaceBosCheckFail.Errorf("Bucket %s Forbidden Access, 403", sc.bucket)
		}
		return err
	}
	ctx, cancel := context.WithCancel(ctx) // Indicate ListObjects go-routine to exit and stop feeding the objectInfo channel.
	defer cancel()
	for object := range sc.client.ListObjects(ctx, sc.bucket, minio.ListObjectsOptions{
		Prefix:    sc.objectPrefix,
		MaxKeys:   1,
		Recursive: true,
	}) {
		if object.Err != nil {
			return object.Err
		}
		return nil
	}
	return cerrs.ErrNamespaceBosCheckFail.Errorf("Object prefix %s not exist", sc.objectPrefix)
}

func (sc *S3StorageClient) FileNotExist(ctx context.Context, filePath string) error {
	_, err := sc.client.StatObject(ctx, sc.bucket, filepath.Join(sc.objectPrefix, filePath), minio.StatObjectOptions{})
	if err == nil {
		return cerrs.ErrNamespaceError.Errorf("slot has no sst file but meta file:%s exist", filePath)
	}
	if minio.ToErrorResponse(err).Code == "NoSuchKey" {
		return nil
	}
	return err
}

func (sc *S3StorageClient) GetFileDownloadInfo(ctx context.Context, filePath string) (*fileDownloadInfoResult, error) {
	reqParams := make(url.Values)
	reqParams.Set("response-content-disposition", "attachment")
	urll, err := sc.client.PresignedGetObject(ctx, sc.bucket,
		filepath.Join(sc.objectPrefix, filePath),
		time.Duration(sc.conf.S3Conf.PresignedUrlExpiration)*time.Second,
		reqParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get s3 presigned urll fail", logit.String("err", err.Error()))
		return nil, err
	}
	info := &fileDownloadInfoResult{
		Url: urll.String(),
	}
	if !sc.conf.S3Conf.CheckCRC32 {
		return info, nil
	}
	objectMeta, err := sc.client.StatObject(ctx, sc.bucket,
		filepath.Join(sc.objectPrefix, filePath), minio.StatObjectOptions{})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get s3 object meta fail", logit.String("err", err.Error()))
		return info, err
	}
	info.Crc32 = objectMeta.ChecksumCRC32
	return info, nil
}

func (sc *S3StorageClient) GetFileContent(ctx context.Context, filePath string, bufferSize int) ([]byte, error) {
	reader, err := sc.client.GetObject(ctx, sc.bucket, filepath.Join(sc.objectPrefix, filePath), minio.GetObjectOptions{})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "Get s3 object fail", logit.String("err", base_utils.Format(err)))
		return nil, err
	}
	defer reader.Close()
	stat, err := reader.Stat()
	if err != nil {
		logger.ComponentLogger.Error(ctx, "Get s3 reader stat fail", logit.String("err", err.Error()))
		if minio.ToErrorResponse(err).Code == "NoSuchKey" {
			return nil, cerrs.ErrNamespaceStorageNoSuchFile.Errorf("file %s not exist", filePath)
		}
		return nil, err
	}
	if stat.Size > int64(bufferSize) {
		return nil, errors.New("content is too long")
	}
	buffer, err := io.ReadAll(reader)
	if err != nil {
		logger.ComponentLogger.Error(ctx, "Read data from reader fail", logit.String("err", err.Error()))
		return nil, err
	}
	return buffer, nil
}

func (sc *S3StorageClient) ListFiles(ctx context.Context, marker string) (*listFilesResult, error) {

	// List 'N' number of objects from a bucket-name with a matching prefix.
	listObjectsN := func(bucket, prefix string, recursive bool, marker string, maxKeys int) (objsInfo []minio.ObjectInfo, err error) {
		ctxx, cancel := context.WithCancel(ctx)
		// Indicate ListObjects go-routine to exit and stop feeding the objectInfo channel.
		defer cancel()
		count := 0
		for object := range sc.client.ListObjects(ctxx, bucket, minio.ListObjectsOptions{
			Prefix:     sc.objectPrefix,
			MaxKeys:    maxKeys,
			Recursive:  recursive,
			StartAfter: marker,
		}) {
			if object.Err != nil {
				return nil, object.Err
			}
			objsInfo = append(objsInfo, object)
			count++
			if count == maxKeys {
				break
			}
		}
		return objsInfo, nil
	}

	// List recursively
	objsInfo, err := listObjectsN(sc.bucket, sc.objectPrefix, true, marker, sc.conf.S3Conf.S3ListMaxKeys)
	if err != nil {
		logger.ComponentLogger.Error(ctx, "List s3 client fail", logit.Error("err", err))
		return nil, err
	}
	rsp := &listFilesResult{
		Files: make([]fileInfo, 0),
	}
	for _, obj := range objsInfo {
		rsp.Files = append(rsp.Files, fileInfo{
			Name: obj.Key,
			Size: cast.ToInt64(obj.Size),
		})
	}
	if len(rsp.Files) != sc.conf.S3Conf.S3ListMaxKeys {
		rsp.IsTruncated = false
	} else {
		rsp.IsTruncated = true
	}
	if len(rsp.Files) != 0 {
		rsp.NextMarker = rsp.Files[len(rsp.Files)-1].Name
	}
	return rsp, nil
}

func NewS3StorageClient(ctx context.Context, param *StorageParam, config *config) (StorageClient, error) {
	if param == nil || param.S3Param.Bucket == "" || param.S3Param.ObjectPrefix == "" {
		return nil, cerrs.ErrNamespaceInvalidParam.Errorf("bucket or objectPrefix is empty")
	}
	if config == nil || config.S3Conf == nil ||
		config.S3Conf.S3ListMaxKeys == 0 || config.S3Conf.S3Endpoint == "" ||
		config.S3Conf.PresignedUrlExpiration == 0 || config.S3Conf.Accesskey == "" ||
		config.S3Conf.Secretkey == "" {
		logger.ComponentLogger.Error(ctx, "Invalid s3 config", logit.String("config", base_utils.Format(config)))
		return nil, cerrs.ErrNamespaceInvalidParam.Errorf("Invalid s3 config")
	}
	s3Client := &S3StorageClient{
		conf:         config,
		bucket:       param.S3Param.Bucket,
		objectPrefix: param.S3Param.ObjectPrefix,
		iamUserID:    param.IamUserID,
	}
	// Initialize minio client object.
	client, err := minio.New(config.S3Conf.S3Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(config.S3Conf.Accesskey, config.S3Conf.Secretkey, ""),
		Secure: config.S3Conf.UseSSL,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "Get s3 client fail", logit.Error("err", err))
		return nil, err
	}
	s3Client.client = client
	return s3Client, nil
}
