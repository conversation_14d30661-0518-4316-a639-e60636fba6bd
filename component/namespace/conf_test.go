package namespace

import (
	"context"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

func TestLoadConf(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	cf1 := &config{}
	if err := compo_utils.LoadConf("namespace", cf1); err != nil {
		t.Fatalf("load conf fail, err: %s", err.Error())
	}

	fmt.Printf("%+v\n%+v\n%+v\n", cf1.MirrorBosConf[0], *cf1.SstDownloadConf, *cf1.SstCleanupConf)

}
