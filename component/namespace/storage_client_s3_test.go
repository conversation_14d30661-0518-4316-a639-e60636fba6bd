package namespace

import (
	"context"
	"errors"
	"net/url"
	"reflect"
	"testing"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"

	. "github.com/agiledragon/gomonkey/v2"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"

	"github.com/minio/minio-go/v7"
)

//func TestNewS3StorageClientOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	type args struct {
//		ctx    context.Context
//		param  *StorageParam
//		config *config
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    StorageClient
//		wantErr bool
//	}{
//		{name: "test no param", args: args{
//			ctx:    context.Background(),
//			param:  nil,
//			config: &config{},
//		}, want: nil, wantErr: true},
//		{name: "test no endpoint", args: args{
//			ctx: context.Background(),
//			param: &StorageParam{
//				StorageType: "",
//				IamUserID:   "",
//				BosParam:    BosParam{},
//				S3Param: S3Param{
//					Bucket:       "TEST",
//					ObjectPrefix: "TEST/",
//				},
//			},
//			config: &config{
//				S3Conf: &s3Conf{},
//			},
//		}, want: nil, wantErr: true},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := NewS3StorageClient(tt.args.ctx, tt.args.param, tt.args.config)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("NewS3StorageClient() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("NewS3StorageClient() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//	if _, err := NewS3StorageClient(context.Background(), &StorageParam{
//		StorageType: "",
//		IamUserID:   "",
//		BosParam:    BosParam{},
//		S3Param: S3Param{
//			Bucket:       "TEST",
//			ObjectPrefix: "TEST/",
//		},
//	}, loadConf()); err != nil {
//		t.Errorf("NewS3StorageClient() error = %v, wantErr %v", err, false)
//	}
//}

func TestNewS3StorageClient(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	patchconfig := ApplyFuncReturn(loadConf, &config{})
	if _, err := NewS3StorageClient(context.Background(), &StorageParam{
		StorageType: "",
		IamUserID:   "",
		BosParam:    BosParam{},
		S3Param: S3Param{
			Bucket:       "TEST",
			ObjectPrefix: "TEST/",
		},
	}, loadConf()); err == nil {
		t.Errorf("NewS3StorageClient() error = %v, wantErr %v", err, true)
	}
	patchconfig.Reset()
	patch := ApplyFunc(minio.New, func(endpoint string, opts *minio.Options) (*minio.Client, error) {
		return nil, errors.New("test")
	})
	if _, err := NewS3StorageClient(context.Background(), &StorageParam{
		StorageType: "",
		IamUserID:   "",
		BosParam:    BosParam{},
		S3Param: S3Param{
			Bucket:       "TEST",
			ObjectPrefix: "TEST/",
		},
	}, loadConf()); err == nil {
		t.Errorf("NewS3StorageClient() error = %v, wantErr %v", err, true)
	}
	patch.Reset()

	patch1 := ApplyFunc(minio.New, func(endpoint string, opts *minio.Options) (*minio.Client, error) {
		return nil, nil
	})
	if _, err := NewS3StorageClient(context.Background(), &StorageParam{
		StorageType: "",
		IamUserID:   "",
		BosParam:    BosParam{},
		S3Param: S3Param{
			Bucket:       "TEST",
			ObjectPrefix: "TEST/",
		},
	}, loadConf()); err != nil {
		t.Errorf("NewS3StorageClient() error = %v, wantErr %v", err, false)
	}
	patch1.Reset()
}

//func TestS3StorageClient_FileNotExistOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	configg := loadConf()
//	storageClient, err := minio.New(configg.S3Conf.S3Endpoint, &minio.Options{
//		Creds:  credentials.NewStaticV4(configg.S3Conf.Accesskey, configg.S3Conf.Secretkey, ""),
//		Secure: configg.S3Conf.UseSSL})
//	if err != nil {
//		t.Errorf("minio.New() error = %v", err)
//	}
//	type fields struct {
//		conf         *config
//		client       *minio.Client
//		bucket       string
//		objectPrefix string
//		iamUserID    string
//	}
//	type args struct {
//		ctx      context.Context
//		filePath string
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantErr bool
//	}{
//		{name: "exist", fields: struct {
//			conf         *config
//			client       *minio.Client
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "test-xp", objectPrefix: "only-1-slot/", iamUserID: ""},
//			args: args{
//				ctx:      context.Background(),
//				filePath: "100/100.meta",
//			}, wantErr: true},
//		{name: "not exist", fields: struct {
//			conf         *config
//			client       *minio.Client
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "test-xp", objectPrefix: "only-1-slot/", iamUserID: ""},
//			args: args{
//				ctx:      context.Background(),
//				filePath: "200/200.meta",
//			}, wantErr: false},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			sc := &S3StorageClient{
//				conf:         tt.fields.conf,
//				client:       tt.fields.client,
//				bucket:       tt.fields.bucket,
//				objectPrefix: tt.fields.objectPrefix,
//				iamUserID:    tt.fields.iamUserID,
//			}
//			if err := sc.FileNotExist(tt.args.ctx, tt.args.filePath); (err != nil) != tt.wantErr {
//				t.Errorf("FileNotExist() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}

func TestS3StorageClient_FileNotExist(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	client := &minio.Client{}

	patchbase := ApplyFunc(minio.New, func(endpoint string, opts *minio.Options) (*minio.Client, error) {
		return client, nil
	})
	storageClient, err := NewS3StorageClient(context.Background(), &StorageParam{
		StorageType: "",
		IamUserID:   "",
		BosParam:    BosParam{},
		S3Param: S3Param{
			Bucket:       "TEST",
			ObjectPrefix: "TEST/",
		},
	}, loadConf())
	if err != nil {
		t.Errorf("NewS3StorageClient() error = %v, wantErr %v", err, false)
	}
	patchbase.Reset()

	//configg := loadConf()
	patch := ApplyMethodFunc(reflect.TypeOf(client), "StatObject",
		func(ctx context.Context, bucketName string, objectName string, opts minio.StatObjectOptions) (minio.ObjectInfo, error) {
			return minio.ObjectInfo{}, nil
		})
	if storageClient.FileNotExist(context.Background(), "100/100.meta") == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch.Reset()
	patch1 := ApplyMethodFunc(reflect.TypeOf(client), "StatObject",
		func(ctx context.Context, bucketName string, objectName string, opts minio.StatObjectOptions) (minio.ObjectInfo, error) {
			return minio.ObjectInfo{}, errors.New("test")
		})
	if storageClient.FileNotExist(context.Background(), "100/100.meta") == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch2 := ApplyFunc(minio.ToErrorResponse, func(err error) minio.ErrorResponse {
		return minio.ErrorResponse{Code: "NoSuchKey"}
	})
	if storageClient.FileNotExist(context.Background(), "100/100.meta") != nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch1.Reset()
	patch2.Reset()
}

//func TestS3StorageClient_FilesLocationExistOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	configg := loadConf()
//	storageClient, err := minio.New(configg.S3Conf.S3Endpoint, &minio.Options{
//		Creds:  credentials.NewStaticV4(configg.S3Conf.Accesskey, configg.S3Conf.Secretkey, ""),
//		Secure: configg.S3Conf.UseSSL})
//	if err != nil {
//		t.Errorf("minio.New() error = %v", err)
//	}
//	type fields struct {
//		conf         *config
//		client       *minio.Client
//		bucket       string
//		objectPrefix string
//		iamUserID    string
//	}
//	type args struct {
//		ctx context.Context
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantErr bool
//	}{
//		{name: "exist", fields: struct {
//			conf         *config
//			client       *minio.Client
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "test-xp", objectPrefix: "only-1-slot/", iamUserID: ""},
//			args: args{
//				ctx: context.Background(),
//			}, wantErr: false},
//		{name: "bucket not exist", fields: struct {
//			conf         *config
//			client       *minio.Client
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "test-xp1", objectPrefix: "only-1-slot/", iamUserID: ""},
//			args: args{
//				ctx: context.Background(),
//			}, wantErr: true},
//		{name: "file not exist", fields: struct {
//			conf         *config
//			client       *minio.Client
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "test-xp", objectPrefix: "only-1-slot1/", iamUserID: ""},
//			args: args{
//				ctx: context.Background(),
//			}, wantErr: true},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			sc := &S3StorageClient{
//				conf:         tt.fields.conf,
//				client:       tt.fields.client,
//				bucket:       tt.fields.bucket,
//				objectPrefix: tt.fields.objectPrefix,
//				iamUserID:    tt.fields.iamUserID,
//			}
//			if err := sc.FilesLocationExist(tt.args.ctx); (err != nil) != tt.wantErr {
//				t.Errorf("FilesLocationExist() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}

func TestS3StorageClient_FilesLocationExist(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	client := &minio.Client{}

	patchbase := ApplyFunc(minio.New, func(endpoint string, opts *minio.Options) (*minio.Client, error) {
		return client, nil
	})
	storageClient, err := NewS3StorageClient(context.Background(), &StorageParam{
		StorageType: "",
		IamUserID:   "",
		BosParam:    BosParam{},
		S3Param: S3Param{
			Bucket:       "TEST",
			ObjectPrefix: "TEST/",
		},
	}, loadConf())
	if err != nil {
		t.Errorf("NewS3StorageClient() error = %v, wantErr %v", err, false)
	}
	patchbase.Reset()
	patch := ApplyMethodFunc(reflect.TypeOf(client), "BucketExists",
		func(ctx context.Context, bucketName string) (bool, error) {
			return false, nil
		})
	if storageClient.FilesLocationExist(context.Background()) == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch.Reset()
	patch1 := ApplyMethodFunc(reflect.TypeOf(client), "BucketExists",
		func(ctx context.Context, bucketName string) (bool, error) {
			return true, errors.New("test")
		})
	if storageClient.FilesLocationExist(context.Background()) == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch1.Reset()
	patch2 := ApplyMethodFunc(reflect.TypeOf(client), "BucketExists",
		func(ctx context.Context, bucketName string) (bool, error) {
			return true, nil
		})
	patch2_1 := ApplyMethodFunc(reflect.TypeOf(client), "ListObjects",
		func(ctx context.Context, bucketName string, opts minio.ListObjectsOptions) <-chan minio.ObjectInfo {
			objectStatCh := make(chan minio.ObjectInfo, 1)
			defer close(objectStatCh)
			objectStatCh <- minio.ObjectInfo{Key: "1"}
			return objectStatCh
		})
	if err := storageClient.FilesLocationExist(context.Background()); err != nil {
		t.Errorf("FilesLocationExist() error = %v, wantErr %v", err, false)
	}
	patch2_1.Reset()
	patch2_2 := ApplyMethodFunc(reflect.TypeOf(client), "ListObjects",
		func(ctx context.Context, bucketName string, opts minio.ListObjectsOptions) <-chan minio.ObjectInfo {
			objectStatCh := make(chan minio.ObjectInfo, 1)
			defer close(objectStatCh)
			objectStatCh <- minio.ObjectInfo{Err: errors.New("test")}
			return objectStatCh
		})
	if storageClient.FilesLocationExist(context.Background()) == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch2_2.Reset()
	patch2_3 := ApplyMethodFunc(reflect.TypeOf(client), "ListObjects",
		func(ctx context.Context, bucketName string, opts minio.ListObjectsOptions) <-chan minio.ObjectInfo {
			objectStatCh := make(chan minio.ObjectInfo, 1)
			defer close(objectStatCh)
			return objectStatCh
		})
	if storageClient.FilesLocationExist(context.Background()) == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", storageClient.FilesLocationExist(context.Background()), true)
	}
	patch2_3.Reset()
	patch2.Reset()
	patch3 := ApplyMethodFunc(reflect.TypeOf(client), "BucketExists",
		func(ctx context.Context, bucketName string) (bool, error) {
			return true, minio.ErrorResponse{Code: "AccessDenied"}
		})
	if err := storageClient.FilesLocationExist(context.Background()); err == nil || !cerrs.Is(err, cerrs.ErrNamespaceBosCheckFail) {
		t.Errorf("FileNotExist() error = %v, wantErr %v", storageClient.FilesLocationExist(context.Background()), true)
	}
	patch3.Reset()
}

//func TestS3StorageClient_GetFileContentOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	configg := loadConf()
//	storageClient, err := minio.New(configg.S3Conf.S3Endpoint, &minio.Options{
//		Creds:  credentials.NewStaticV4(configg.S3Conf.Accesskey, configg.S3Conf.Secretkey, ""),
//		Secure: configg.S3Conf.UseSSL})
//	if err != nil {
//		t.Errorf("minio.New() error = %v", err)
//	}
//	type fields struct {
//		conf         *config
//		client       *minio.Client
//		bucket       string
//		objectPrefix string
//		iamUserID    string
//	}
//	type args struct {
//		ctx        context.Context
//		filePath   string
//		bufferSize int
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    []byte
//		wantErr bool
//	}{
//		{name: "success", fields: struct {
//			conf         *config
//			client       *minio.Client
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "test-xp", objectPrefix: "only-1-slot/", iamUserID: ""},
//			args: args{
//				ctx:        context.Background(),
//				filePath:   "100/100.meta",
//				bufferSize: 10240,
//			}, wantErr: false},
//		{name: "content too long", fields: struct {
//			conf         *config
//			client       *minio.Client
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "test-xp", objectPrefix: "only-1-slot/", iamUserID: ""},
//			args: args{
//				ctx:        context.Background(),
//				filePath:   "100/100.meta",
//				bufferSize: 1,
//			}, wantErr: true},
//		{name: "file not exist", fields: struct {
//			conf         *config
//			client       *minio.Client
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "test-xp", objectPrefix: "only-1-slot1/", iamUserID: ""},
//			args: args{
//				ctx:        context.Background(),
//				filePath:   "100/100.meta1",
//				bufferSize: 1,
//			}, wantErr: true},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			sc := &S3StorageClient{
//				conf:         tt.fields.conf,
//				client:       tt.fields.client,
//				bucket:       tt.fields.bucket,
//				objectPrefix: tt.fields.objectPrefix,
//				iamUserID:    tt.fields.iamUserID,
//			}
//			_, err := sc.GetFileContent(tt.args.ctx, tt.args.filePath, tt.args.bufferSize)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("GetFileContent() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			//if !reflect.DeepEqual(got, tt.want) {
//			//	t.Errorf("GetFileContent() got = %v, want %v", got, tt.want)
//			//}
//		})
//	}
//}

func TestS3StorageClient_GetFileContent(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	client := &minio.Client{}

	patchbase := ApplyFunc(minio.New, func(endpoint string, opts *minio.Options) (*minio.Client, error) {
		return client, nil
	})
	storageClient, err := NewS3StorageClient(context.Background(), &StorageParam{
		StorageType: "",
		IamUserID:   "",
		BosParam:    BosParam{},
		S3Param: S3Param{
			Bucket:       "TEST",
			ObjectPrefix: "TEST/",
		},
	}, loadConf())
	if err != nil {
		t.Errorf("NewS3StorageClient() error = %v, wantErr %v", err, false)
	}
	patchbase.Reset()
	object := &minio.Object{}
	patch1 := ApplyMethodFunc(reflect.TypeOf(client), "GetObject",
		func(ctx context.Context, bucketName, objectName string, opts minio.GetObjectOptions) (*minio.Object, error) {
			return object, nil
		})
	patch2 := ApplyMethodReturn(&minio.Object{}, "Stat",
		nil, errors.New("error"))
	patch3 := ApplyFunc(minio.ToErrorResponse, func(err error) minio.ErrorResponse {
		return minio.ErrorResponse{Code: "NoSuchKey"}
	})
	patch4 := ApplyMethodReturn(&minio.Object{}, "Close", nil)
	if _, err := storageClient.GetFileContent(context.Background(), "test", 10); err == nil || !cerrs.Is(err, cerrs.ErrNamespaceStorageNoSuchFile) {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	if _, err := storageClient.GetFileContent(context.Background(), "test", 10); err == nil || !cerrs.Is(err, cerrs.ErrNamespaceStorageNoSuchFile) {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch1.Reset()
	patch2.Reset()
	patch3.Reset()
	patch4.Reset()
}

//func TestS3StorageClient_GetFileDownloadInfoOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	configg := loadConf()
//	storageClient, err := minio.New(configg.S3Conf.S3Endpoint, &minio.Options{
//		Creds:  credentials.NewStaticV4(configg.S3Conf.Accesskey, configg.S3Conf.Secretkey, ""),
//		Secure: configg.S3Conf.UseSSL})
//	if err != nil {
//		t.Errorf("minio.New() error = %v", err)
//	}
//	type fields struct {
//		conf         *config
//		client       *minio.Client
//		bucket       string
//		objectPrefix string
//		iamUserID    string
//	}
//	type args struct {
//		ctx      context.Context
//		filePath string
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *fileDownloadInfoResult
//		wantErr bool
//	}{
//		{name: "success", fields: struct {
//			conf         *config
//			client       *minio.Client
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "test-xp", objectPrefix: "only-1-slot/", iamUserID: ""},
//			args: args{
//				ctx:      context.Background(),
//				filePath: "100/100_0.sst",
//			}, wantErr: false},
//		{name: "file not exist", fields: struct {
//			conf         *config
//			client       *minio.Client
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "test-xp", objectPrefix: "only-1-slot/", iamUserID: ""},
//			args: args{
//				ctx:      context.Background(),
//				filePath: "100/100.meta1",
//			}, wantErr: true},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			sc := &S3StorageClient{
//				conf:         tt.fields.conf,
//				client:       tt.fields.client,
//				bucket:       tt.fields.bucket,
//				objectPrefix: tt.fields.objectPrefix,
//				iamUserID:    tt.fields.iamUserID,
//			}
//			_, err := sc.GetFileDownloadInfo(tt.args.ctx, tt.args.filePath)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("GetFileDownloadInfo() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			//if !reflect.DeepEqual(got, tt.want) {
//			//	t.Errorf("GetFileDownloadInfo() got = %v, want %v", got, tt.want)
//			//}
//		})
//	}
//}

func TestS3StorageClient_GetFileDownloadInfo(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	client := &minio.Client{}

	patchbase := ApplyFunc(minio.New, func(endpoint string, opts *minio.Options) (*minio.Client, error) {
		return client, nil
	})
	storageClient, err := NewS3StorageClient(context.Background(), &StorageParam{
		StorageType: "",
		IamUserID:   "",
		BosParam:    BosParam{},
		S3Param: S3Param{
			Bucket:       "TEST",
			ObjectPrefix: "TEST/",
		},
	}, loadConf())
	if err != nil {
		t.Errorf("NewS3StorageClient() error = %v, wantErr %v", err, false)
	}
	patchbase.Reset()

	patch1 := ApplyMethodFunc(reflect.TypeOf(client), "PresignedGetObject",
		func(ctx context.Context, bucketName, objectName string, expires time.Duration, reqParams url.Values) (u *url.URL, err error) {
			return &url.URL{}, nil
		})
	patch2 := ApplyMethodFunc(reflect.TypeOf(client), "StatObject",
		func(ctx context.Context, bucketName, objectName string, opts minio.StatObjectOptions) (minio.ObjectInfo, error) {
			return minio.ObjectInfo{}, nil
		})
	if _, err := storageClient.GetFileDownloadInfo(context.Background(), "test"); err != nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, false)
	}
	patch1.Reset()
	patch2.Reset()
}

//func TestS3StorageClient_ListFiles(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	configg := loadConf()
//	storageClient, err := minio.New(configg.S3Conf.S3Endpoint, &minio.Options{
//		Creds:  credentials.NewStaticV4(configg.S3Conf.Accesskey, configg.S3Conf.Secretkey, ""),
//		Secure: configg.S3Conf.UseSSL})
//	if err != nil {
//		t.Errorf("minio.New() error = %v", err)
//	}
//	fmt.Printf("storageClient: %d\n", configg.S3Conf.S3ListMaxKeys)
//	type fields struct {
//		conf         *config
//		client       *minio.Client
//		bucket       string
//		objectPrefix string
//		iamUserID    string
//	}
//	type args struct {
//		ctx    context.Context
//		marker string
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *listFilesResult
//		wantErr bool
//	}{
//		{name: "success", fields: struct {
//			conf         *config
//			client       *minio.Client
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "test-xp", objectPrefix: "only-1-slot/", iamUserID: ""},
//			args: args{
//				ctx: context.Background(),
//			}, want: &listFilesResult{
//				Files: []fileInfo{
//					{
//						Name: "only-1-slot/100/100.meta",
//						Size: 23,
//					},
//				},
//				IsTruncated: true,
//				NextMarker:  "only-1-slot/100/100.meta",
//			}, wantErr: false},
//		{name: "success", fields: struct {
//			conf         *config
//			client       *minio.Client
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "test-xp", objectPrefix: "only-1-slot/", iamUserID: ""},
//			args: args{
//				ctx:    context.Background(),
//				marker: "only-1-slot/100/100.meta",
//			}, want: &listFilesResult{
//				Files: []fileInfo{
//					{
//						Name: "only-1-slot/100/100_0.sst",
//						Size: 1084,
//					},
//				},
//				IsTruncated: true,
//				NextMarker:  "only-1-slot/100/100_0.sst",
//			}, wantErr: false},
//		{name: "success", fields: struct {
//			conf         *config
//			client       *minio.Client
//			bucket       string
//			objectPrefix string
//			iamUserID    string
//		}{conf: configg, client: storageClient, bucket: "test-xp", objectPrefix: "only-1-slot/", iamUserID: ""},
//			args: args{
//				ctx:    context.Background(),
//				marker: "only-1-slot/100/100_0.sst",
//			}, want: &listFilesResult{
//				Files:       []fileInfo{},
//				IsTruncated: false,
//				NextMarker:  "",
//			}, wantErr: false},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			sc := &S3StorageClient{
//				conf:         tt.fields.conf,
//				client:       tt.fields.client,
//				bucket:       tt.fields.bucket,
//				objectPrefix: tt.fields.objectPrefix,
//				iamUserID:    tt.fields.iamUserID,
//			}
//			got, err := sc.ListFiles(tt.args.ctx, tt.args.marker)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("ListFiles() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("ListFiles() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestS3StorageClient_ListFiles(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	client := &minio.Client{}

	patchbase := ApplyFunc(minio.New, func(endpoint string, opts *minio.Options) (*minio.Client, error) {
		return client, nil
	})
	storageClient, err := NewS3StorageClient(context.Background(), &StorageParam{
		StorageType: "",
		IamUserID:   "",
		BosParam:    BosParam{},
		S3Param: S3Param{
			Bucket:       "TEST",
			ObjectPrefix: "TEST/",
		},
	}, loadConf())
	if err != nil {
		t.Errorf("NewS3StorageClient() error = %v, wantErr %v", err, false)
	}
	patchbase.Reset()
	patch1_1 := ApplyMethodFunc(reflect.TypeOf(client), "ListObjects",
		func(ctx context.Context, bucketName string, opts minio.ListObjectsOptions) <-chan minio.ObjectInfo {
			objectStatCh := make(chan minio.ObjectInfo, 1)
			defer close(objectStatCh)
			objectStatCh <- minio.ObjectInfo{Key: "1"}
			return objectStatCh
		})
	if _, err := storageClient.ListFiles(context.Background(), ""); err != nil {
		t.Errorf("FilesLocationExist() error = %v, wantErr %v", err, false)
	}
	patch1_1.Reset()
	patch1_2 := ApplyMethodFunc(reflect.TypeOf(client), "ListObjects",
		func(ctx context.Context, bucketName string, opts minio.ListObjectsOptions) <-chan minio.ObjectInfo {
			objectStatCh := make(chan minio.ObjectInfo, 1)
			objectStatCh <- minio.ObjectInfo{Err: errors.New("test")}
			close(objectStatCh)
			return objectStatCh
		})
	if _, err := storageClient.ListFiles(context.Background(), ""); err == nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch1_2.Reset()
	patch1_3 := ApplyMethodFunc(reflect.TypeOf(client), "ListObjects",
		func(ctx context.Context, bucketName string, opts minio.ListObjectsOptions) <-chan minio.ObjectInfo {
			objectStatCh := make(chan minio.ObjectInfo, 1)
			defer close(objectStatCh)
			return objectStatCh
		})
	if _, err := storageClient.ListFiles(context.Background(), ""); err != nil {
		t.Errorf("FileNotExist() error = %v, wantErr %v", err, true)
	}
	patch1_3.Reset()
}
