package namespace

import (
	"context"
	"fmt"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"

	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

var (
	_storageTypeToConstructor = map[string]func(context.Context, *StorageParam, *config) (StorageClient, error){
		"bos": func(ctx context.Context, param *StorageParam, conf *config) (StorageClient, error) {
			return NewBosStorageClient(ctx, param, conf)
		},
		"s3": func(ctx context.Context, param *StorageParam, conf *config) (StorageClient, error) {
			return NewS3StorageClient(ctx, param, conf)
		},
		"mirrorBos": func(ctx context.Context, param *StorageParam, conf *config) (StorageClient, error) {
			return NewMirrorBosStorageClient(ctx, param, conf)
		},
	}
)

type DownloadUtils struct {
	conf          *config
	storageClient StorageClient
}

// getFileRelativePath 获取Bos文件全路径
func getFileRelativePath(slotID int, file string) string {

	return filepath.Join(cast.ToString(slotID), file)
}

// getMetaFileRelativePath 获取meta文件全路径
func getMetaFileRelativePath(slotID int) string {

	return getFileRelativePath(slotID, cast.ToString(slotID)+".meta")
}

func getSlotID(path string) (slotID int, err error) {
	dir, file := filepath.Split(path)
	if isSstFile(file) || isMetaFile(file) {
		slotID, err = strconv.Atoi(filepath.Base(dir))
		if err != nil {
			return
		}
	} else {
		slotID, err = strconv.Atoi(filepath.Base(path))
		if err != nil {
			return
		}
	}
	return
}

// isSstFile 是否为sst文件
func isSstFile(fileName string) bool {

	return filepath.Ext(fileName) == ".sst"
}

// isSstFile 是否为meta文件
func isMetaFile(fileName string) bool {

	return filepath.Ext(fileName) == ".meta"
}

// GetDownloadUtils 获取DownloadUtils
func GetDownloadUtils(ctx context.Context, param *StorageParam) (*DownloadUtils, error) {

	downloadUtils := &DownloadUtils{
		conf: loadConf(),
	}
	if _, found := _storageTypeToConstructor[param.StorageType]; !found {
		return nil, fmt.Errorf("unknown storage type %s", param.StorageType)
	}

	logger.ComponentLogger.Notice(ctx, "input", logit.String("config", base_utils.Format(downloadUtils)),
		logit.String("param", base_utils.Format(param)))
	storageClient, err := _storageTypeToConstructor[param.StorageType](ctx, param, downloadUtils.conf)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get storage client fail", logit.Error("err", err))
		return nil, err
	}
	downloadUtils.storageClient = storageClient
	return downloadUtils, nil
}

// GetDiskUsagePercentThreshold 获取磁盘使用比阈值
func (c *DownloadUtils) GetDiskUsagePercentThreshold(ctx context.Context) int64 {
	return c.conf.SstDownloadConf.DiskUsagePercentThreshold
}

// GetDiskFreeBytesThreshold 获取空闲空间阈值
func (c *DownloadUtils) GetDiskFreeBytesThreshold(ctx context.Context) int64 {
	return c.conf.SstDownloadConf.DiskFreeBytesThreshold
}

// GetMaxSlotNum 获取单次下载最大slot
func (c *DownloadUtils) GetMaxSlotNum(ctx context.Context) int {
	return c.conf.SstDownloadConf.MaxSlotNum
}

// GetMaxSstFileSize 获取单次下载最大size
func (c *DownloadUtils) GetMaxSstFileSize(ctx context.Context) int64 {
	return c.conf.SstDownloadConf.MaxSstFileSize
}

// GetTrafficLimit 获取单次下载最大size
func (c *DownloadUtils) GetTrafficLimitMB(ctx context.Context) int {
	return c.conf.SstDownloadConf.TrafficLimitMB
}

// BucketAndObjectPrefixExist 检测bucket以及object是否存在
func (c *DownloadUtils) BucketAndObjectPrefixExist(ctx context.Context) error {

	return c.storageClient.FilesLocationExist(ctx)
}

// SlotSstFileNumMatch 检查list获取的文件是否与meta中预期文件一致
func (c *DownloadUtils) SlotSstFileNumMatch(ctx context.Context, singleSlotSstFile *SingleSlotSstFile) error {

	// 如果Slot下无SST文件,并且meta文件也不存在,则符合预期
	// 如果slot下无SST文件,但是存在meta文件,则报错
	if singleSlotSstFile.FileNum == 0 {
		return c.storageClient.FileNotExist(ctx, getMetaFileRelativePath(singleSlotSstFile.SlotID))
	}

	metaContentBytes, err := c.storageClient.GetFileContent(ctx,
		getMetaFileRelativePath(singleSlotSstFile.SlotID), 10240)
	if err != nil && cerrs.Is(err, cerrs.ErrNamespaceStorageNoSuchFile) {
		return cerrs.ErrNamespaceError.Errorf("slot %d no meta file", singleSlotSstFile.SlotID)
	} else if err != nil {
		return err
	}

	lines := strings.Split(string(metaContentBytes[:]), "\n")

	sstFileNums := 0
	for _, line := range lines {
		if isSstFile(line) {
			sstFileNums++
		}
	}
	if singleSlotSstFile.FileNum != sstFileNums {
		return cerrs.ErrNamespaceError.Errorf("slot %d file num not equal", singleSlotSstFile.SlotID)
	}
	var exist bool
	for _, fileName := range singleSlotSstFile.FileNames {
		exist = false
		for _, line := range lines {
			if fileName == line {
				exist = true
				break
			}
		}
		if !exist {
			return cerrs.ErrNamespaceError.Errorf("slot file %s not exist", fileName)
		}
	}
	return nil
}

// NamespaceMatch检查namespace是否和meta文件中namespace一致
func (c *DownloadUtils) NamespaceMatch(ctx context.Context, namespace string) error {

	ns := ""
	marker := ""
	slotID := -1
	listFilesResult, err := c.storageClient.ListFiles(ctx, marker)
	if err != nil {
		return err
	}
	// 判断目录下是否有文件
	if len(listFilesResult.Files) == 0 {
		return cerrs.ErrNamespaceNamespaceCheckFail.Errorf("no file to list")
	}

	for _, file := range listFilesResult.Files {
		id, err := getSlotID(file.Name)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "get slot id fail", logit.Error("err", err),
				logit.String("fileName", file.Name))
			continue
		}
		slotID = id
		break
	}
	if slotID == -1 {
		return cerrs.ErrNamespaceNamespaceCheckFail.Errorf("no found slot id")
	}

	metaContentBytes, err := c.storageClient.GetFileContent(ctx, getMetaFileRelativePath(slotID), 10240)
	if err != nil && cerrs.Is(err, cerrs.ErrNamespaceStorageNoSuchFile) {
		return cerrs.ErrNamespaceNamespaceCheckFail.Errorf("no found slot id")
	} else if err != nil {
		return err
	}

	lines := strings.Split(string(metaContentBytes[:]), "\n")

	for _, line := range lines {
		if strings.Index(line, "NAMESPACE") != 0 {
			continue
		}
		if len(strings.Split(line, ":")) < 2 {
			continue
		}
		ns = strings.Split(line, ":")[1]
	}
	if ns != namespace {
		return cerrs.ErrNamespaceNamespaceCheckFail.Errorf("namespace not equal, expect: %s actual: %s", namespace, ns)
	}
	return nil
}

// GetSingleSlotDownloadInfo 获取单个slot下载信息
func (c *DownloadUtils) GetSingleSlotDownloadInfo(ctx context.Context, singleSlotSstFile *SingleSlotSstFile) (*SingleSlotDownloadInfo, error) {

	info := &SingleSlotDownloadInfo{
		SlotID:        singleSlotSstFile.SlotID,
		FileTotalSize: singleSlotSstFile.FileTotalSize,
		Files:         make([]*FileDownloadInfo, singleSlotSstFile.FileNum),
	}
	for index, name := range singleSlotSstFile.FileNames {
		result, err := c.storageClient.GetFileDownloadInfo(ctx, getFileRelativePath(singleSlotSstFile.SlotID, name))
		if err != nil {
			logger.ComponentLogger.Error(ctx, "GetFileDownloadInfo error", logit.Error("err", err),
				logit.String("key", getFileRelativePath(singleSlotSstFile.SlotID, name)))
			return nil, err
		}
		info.Files[index] = &FileDownloadInfo{
			Name:  name,
			Url:   result.Url,
			Crc32: result.Crc32,
			Md5:   result.Md5,
		}
	}

	return info, nil
}
func (c *DownloadUtils) getSingleSlotDownloadInfoChan(ctx context.Context, singleSlotSstFile *SingleSlotSstFile, ch chan *SingleSlotDownloadInfo) error {

	info := &SingleSlotDownloadInfo{
		SlotID:        singleSlotSstFile.SlotID,
		FileTotalSize: singleSlotSstFile.FileTotalSize,
		Files:         make([]*FileDownloadInfo, singleSlotSstFile.FileNum),
	}
	for index, name := range singleSlotSstFile.FileNames {
		result, err := c.storageClient.GetFileDownloadInfo(ctx, getFileRelativePath(singleSlotSstFile.SlotID, name))
		if err != nil {
			logger.ComponentLogger.Error(ctx, "GetFileDownloadInfo error", logit.Error("err", err))
			return err
		}
		info.Files[index] = &FileDownloadInfo{
			Name:  name,
			Url:   result.Url,
			Crc32: result.Crc32,
			Md5:   result.Md5,
		}
	}
	ch <- info
	return nil
}

// GetAllSlotDownloadInfo 并发获取文件下载信息
func (c *DownloadUtils) GetAllSlotDownloadInfo(ctx context.Context, allSlotSstFile []*SingleSlotSstFile) ([]*SingleSlotDownloadInfo, error) {

	downloadInfos := make([]*SingleSlotDownloadInfo, len(allSlotSstFile))
	for i := 0; i < len(downloadInfos); i++ {
		downloadInfos[i] = &SingleSlotDownloadInfo{}
	}

	retChan := make(chan *SingleSlotDownloadInfo, len(allSlotSstFile))
	g := gtask.Group{
		Concurrent:    50,
		AllowSomeFail: false,
	}
	for _, singleSlotSstFile := range allSlotSstFile {
		singleSlotSstFile := singleSlotSstFile
		g.Go(func() error {
			return c.getSingleSlotDownloadInfoChan(ctx, singleSlotSstFile, retChan)
		})
	}
	_, err := g.Wait()
	if err != nil {
		close(retChan)
		return nil, err
	}
	close(retChan)

	for singleRet := range retChan {
		downloadInfos[singleRet.SlotID].SlotID = singleRet.SlotID
		downloadInfos[singleRet.SlotID].Files = singleRet.Files
		downloadInfos[singleRet.SlotID].FileTotalSize = singleRet.FileTotalSize

	}

	return downloadInfos, nil
}

// ListSstFiles lists sst file in a bucket,返回[]*SingleSlotSstFile长度为16384
func (c *DownloadUtils) ListSstFiles(ctx context.Context) ([]*SingleSlotSstFile, error) {

	slotSstFile := make([]*SingleSlotSstFile, SlotNum)

	for index := 0; index < SlotNum; index++ {
		slotSstFile[index] = &SingleSlotSstFile{
			SlotID:    index,
			FileNames: make([]string, 0),
		}
	}

	marker := ""
	for true {
		listFilesResult, err := c.storageClient.ListFiles(ctx, marker)
		if err != nil {
			return slotSstFile, err
		}
		for _, file := range listFilesResult.Files {
			dir, fileBaseName := filepath.Split(file.Name)
			if !isSstFile(fileBaseName) {
				continue
			}
			slotID, err := strconv.Atoi(filepath.Base(dir))
			if err != nil {
				return nil, err
			}
			if slotID < 0 || slotID >= SlotNum {
				return nil, cerrs.ErrNamespaceError.Errorf("slot %d is not valid", slotID)
			}
			slotSstFile[slotID].FileNum += 1
			slotSstFile[slotID].FileTotalSize += cast.ToInt64(file.Size)
			slotSstFile[slotID].FileNames = append(slotSstFile[slotID].FileNames, fileBaseName)
		}
		if listFilesResult.IsTruncated {
			marker = listFilesResult.NextMarker
		} else {
			break
		}
	}
	return slotSstFile, nil
}

func GetDownloadDir(ctx context.Context, subDir string) string {

	return filepath.Join(loadConf().SstDownloadConf.DownloadParentDir, subDir)
}

func GetCleanupStep(ctx context.Context) int {
	return loadConf().SstCleanupConf.Step
}

func GetOperationReplicationOffset(ctx context.Context) int {
	return loadConf().OperationConf.ReplicationOffset
}
