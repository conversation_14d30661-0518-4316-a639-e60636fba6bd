package namespace

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
)

type OperationClient struct {
	client *single_redis.SingleClient
	conf   *config
}

// GetOperationClient 初始化客户端
func GetOperationClient(ctx context.Context, host string, port any, password string) (*OperationClient, error) {

	config := loadConf()
	operationClient := &OperationClient{
		conf: config,
	}
	operationClient.client = single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
		single_redis.WithRetry(config.OperationConf.ConnectRetry),
		single_redis.WithTimeout(&single_redis.ConfigTimeout{
			Connect: time.Duration(config.OperationConf.ConnectTimeout) * time.Millisecond,
			Read:    time.Duration(config.OperationConf.ReadTimeout) * time.Millisecond,
			Write:   time.Duration(config.OperationConf.WriteTimeout) * time.Millisecond,
		}))
	return operationClient, nil
}

// NamespaceDetails 获取命名空间
func (c *OperationClient) NamespaceDetails(ctx context.Context) (map[string]string, error) {

	namespaceStatus := make(map[string]string)

	raw, err := c.client.Do(ctx, "namespace", "details").StringSlice()
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get namespace detail error", logit.Error("error", err))
		return namespaceStatus, err
	}
	for index := 0; index < len(raw)/2; index++ {
		namespaceStatus[raw[index*2]] = raw[index*2+1]
	}
	return namespaceStatus, nil
}

// SwitchNamespace切换命名空间
func (c *OperationClient) SwitchNamespace(ctx context.Context, namespace string) error {

	rsp, err := c.client.Do(ctx, "bulkload", "switchnamespace", namespace).Result()
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "switch namespace error", logit.Error("error", err))
		return err
	}
	fmt.Println(rsp)
	if rsp != "OK" {
		return cerrs.ErrNamespaceError.Errorf("switch namespace failed, rsp:%v", rsp)
	}
	return nil
}

// GetSwitchTask 获取最新切换任务
func (c *OperationClient) GetSwitchTask(ctx context.Context) (*OperationTask, error) {

	rsp, err := c.client.Do(ctx, "bulkload", "switchnamespacestatus").StringSlice()
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get switch namespace task error", logit.Error("error", err))
		return nil, err
	}
	return &OperationTask{
		Status:    rsp[1],
		Namespace: rsp[3],
	}, nil
}

// IngestDir 注入目录
func (c *OperationClient) IngestDir(ctx context.Context, dir, namespace string) error {

	rsp, err := c.client.Do(ctx, "bulkload", "ingest", dir, namespace).Result()
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "ingest namespace error", logit.Error("error", err))
		return err
	}
	if rsp != "OK" {
		return cerrs.ErrNamespaceError.Errorf("ingest dir failed, rsp:%v", rsp)
	}
	return nil
}

// IngestDirWithTaskID 注入目录
func (c *OperationClient) IngestDirWithTaskID(ctx context.Context, dir, namespace, taskID string) error {

	rsp, err := c.client.Do(ctx, "bulkload", "ingest", dir, namespace, taskID).Result()
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "ingest namespace error", logit.Error("error", err))
		return err
	}
	if rsp != "OK" {
		return cerrs.ErrNamespaceError.Errorf("ingest dir failed, rsp:%v", rsp)
	}
	return nil
}

// GetIngestTask 获取注入任务
func (c *OperationClient) GetIngestTask(ctx context.Context) (*OperationTask, error) {

	rsp, err := c.client.Do(ctx, "bulkload", "ingeststatus").StringSlice()
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get ingest namespace task error", logit.Error("error", err))

		return nil, err
	}
	taskID := ""
	if len(rsp) >= 10 {
		taskID = rsp[9]
	}
	return &OperationTask{
		Status:    rsp[1],
		Namespace: rsp[3],
		Dir:       rsp[5],
		KeyNum:    rsp[7],
		TaskID:    taskID,
	}, nil
}

// DelNamespace 删除命名空间
func (c *OperationClient) DelNamespace(ctx context.Context, namespace string) error {

	rsp, err := c.client.Do(ctx, "bulkload", "delnamespace", namespace).Result()
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "delete namespace error", logit.Error("error", err))
		return err
	}
	if rsp != "OK" {
		return cerrs.ErrNamespaceError.Errorf("del namespace failed, rsp:%v", rsp)
	}
	return nil
}

// GetDelTask 获取删除任务
func (c *OperationClient) GetDelTask(ctx context.Context) (*OperationTask, error) {

	rsp, err := c.client.Do(ctx, "bulkload", "delnamespacestatus").StringSlice()
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get delete namespace task error", logit.Error("error", err))
		return nil, err
	}
	return &OperationTask{
		Status:    rsp[1],
		Namespace: rsp[3],
	}, nil
}

// CheckPegaSizeEnough 检查空间是否足够
func (c *OperationClient) CheckPegaSizeEnough(ctx context.Context, sstSize int64) error {

	pegadbMemoryInfo, err := single_redis.GetPegaDBMemoryInfo(ctx, c.client)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get pega mem info error", logit.Error("error", err))
		return err
	}
	if pegadbMemoryInfo.MaxDbsizeByte != 0 {
		if (pegadbMemoryInfo.MaxDbsizeByte - pegadbMemoryInfo.UsedDbsize - sstSize) <
			c.conf.SstDownloadConf.DiskFreeBytesThreshold {
			return cerrs.ErrNamespaceDiskCheckFail.Errorf("check dbsize free bytes fail")
		}
		if float64(pegadbMemoryInfo.UsedDbsize+sstSize)/float64(pegadbMemoryInfo.MaxDbsizeByte)*100 >
			float64(c.conf.SstDownloadConf.DiskUsagePercentThreshold) {
			return cerrs.ErrNamespaceDiskCheckFail.Errorf("check dbsize threshold fail")
		}
	}
	if (pegadbMemoryInfo.DiskCapacity - pegadbMemoryInfo.UsedDiskSize - sstSize) <
		c.conf.SstDownloadConf.DiskFreeBytesThreshold {
		return cerrs.ErrNamespaceDiskCheckFail.Errorf("check disk free bytes fail")
	}
	if float64(pegadbMemoryInfo.UsedDiskSize+sstSize)/float64(pegadbMemoryInfo.DiskCapacity)*100 >
		float64(c.conf.SstDownloadConf.DiskUsagePercentThreshold) {
		return cerrs.ErrNamespaceDiskCheckFail.Errorf("check disk threshold fail")
	}
	return nil
}

func (c *OperationClient) Close(ctx context.Context) {

	c.client.Close()
}
