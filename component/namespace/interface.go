package namespace

type StorageParam struct {
	StorageType    string         `json:"storageType"`
	IamUserID      string         `json:"iamUserId"`
	BosParam       BosParam       `json:"bosParam"`
	S3Param        S3Param        `json:"s3Param"`
	MirrorBosParam MirrorBosParam `json:"mirrorBosParam"`
}

type BosParam struct {
	Bucket       string `json:"bucket"`
	ObjectPrefix string `json:"objectPrefix"`
}

type MirrorBosParam struct {
	Bucket       string `json:"bucket"`
	ObjectPrefix string `json:"objectPrefix"`
	MirrorBucket string `json:"mirrorBucket"`
}

type S3Param struct {
	Bucket       string `json:"bucket"`
	ObjectPrefix string `json:"objectPrefix"`
}

type SingleSlotSstFile struct {
	SlotID        int      `json:"slotId"`
	FileNum       int      `json:"fileNum"`
	FileTotalSize int64    `json:"fileTotalSize"`
	FileNames     []string `json:"fileNames"`
}

type SingleSlotDownloadInfo struct {
	SlotID        int                 `json:"slotId"`
	FileTotalSize int64               `json:"fileTotalSize"`
	Files         []*FileDownloadInfo `json:"files"`
}

type FileDownloadInfo struct {
	Name  string `json:"name"`
	Url   string `json:"url"`
	Crc32 string `json:"crc32"`
	Md5   string `json:"md5"`
}

//type ClusterSstFile struct {
//	Shards map[int][]*SingleSlotSstFile `json:"shards"`
//}

type NodeDownloadSstTaskItem struct {
	SlotNum       int                 `json:"slotNum"`
	FileTotalSize int64               `json:"fileTotalSize"`
	SlotIDs       []int               `json:"slotIDs"`
	Files         []*FileDownloadInfo `json:"files"`
}

type NodeDownloadSstTask struct {
	NodeID         string                     `json:"nodeID"`
	NodeFloatingIP string                     `json:"nodeFloatingIP"`
	NodeXagentPort int                        `json:"nodeXagentPort"`
	ItemNum        int                        `json:"itemNum"`
	Items          []*NodeDownloadSstTaskItem `json:"items"`
}

type OperationTask struct {
	Status    string `json:"status"`
	Namespace string `json:"namespace"`
	Dir       string `json:"dir"`
	KeyNum    string `json:"keyNum"`
	TaskID    string `json:"taskID"`
}
