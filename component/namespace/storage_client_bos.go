package namespace

import (
	"bufio"
	"context"
	"errors"
	"io"
	"path/filepath"
	"strings"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/bos/api"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bce_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type BosStorageClient struct {
	conf         *config
	client       *bce_utils.BosClient
	bucket       string
	objectPrefix string
	iamUserID    string
}

func (bc *BosStorageClient) ListFiles(ctx context.Context, marker string) (*listFilesResult, error) {

	args := new(api.ListObjectsArgs)
	args.Prefix = bc.objectPrefix
	args.MaxKeys = cast.ToInt(bc.conf.BosConf.BosListMaxKeys)
	args.Marker = marker
	listObjectResult, err := bc.client.ListObjects(bc.bucket, args)
	if err != nil {
		logger.ComponentLogger.Error(ctx, "List bos files fail", logit.String("list files", err.Error()))
		return nil, err
	}
	rsp := &listFilesResult{
		Files:       make([]fileInfo, 0),
		IsTruncated: listObjectResult.IsTruncated,
		NextMarker:  listObjectResult.NextMarker,
	}
	for _, obj := range listObjectResult.Contents {
		// 忽略"目录"文件
		if strings.HasSuffix(obj.Key, "/") {
			continue
		}
		rsp.Files = append(rsp.Files, fileInfo{
			Name: obj.Key,
			Size: cast.ToInt64(obj.Size),
		})
	}
	return rsp, nil
}

func (bc *BosStorageClient) FilesLocationExist(ctx context.Context) error {
	exists, err := bc.client.DoesBucketExist(bc.bucket)
	if err == nil && !exists {
		return cerrs.ErrNamespaceBosCheckFail.Errorf("Bucket %s not exist", bc.bucket)
	}
	if err != nil {
		logger.ComponentLogger.Error(ctx, "Check bucket fail", logit.String("err", err.Error()))
		return err
	}

	_, err = bc.client.GetObjectMeta(bc.bucket, bc.objectPrefix)
	if err == nil {
		return nil
	}
	if realErr, ok := err.(*bce.BceServiceError); ok {
		if realErr.StatusCode == 404 || realErr.StatusCode == 403 {
			return cerrs.ErrNamespaceBosCheckFail.Errorf("Check object fail:%s",
				base_utils.Format(realErr))
		}
	}
	return err
}

func (bc *BosStorageClient) FileNotExist(ctx context.Context, filePath string) error {
	_, err := bc.client.GetObjectMeta(bc.bucket, filepath.Join(bc.objectPrefix, filePath))
	if err == nil {
		return cerrs.ErrNamespaceError.Errorf("slot has no sst file but meta file:%s exist", filePath)
	}
	if realErr, ok := err.(*bce.BceServiceError); ok {
		if realErr.StatusCode == 404 {
			return nil
		}
	}
	return err
}

func (bc *BosStorageClient) GetFileDownloadInfo(ctx context.Context, filePath string) (*fileDownloadInfoResult, error) {
	info := &fileDownloadInfoResult{
		Url: bc.client.BasicGeneratePresignedUrl(bc.bucket,
			filepath.Join(bc.objectPrefix, filePath),
			bc.conf.BosConf.PresignedUrlExpiration),
	}
	if !bc.conf.BosConf.CheckCRC32 && !bc.conf.BosConf.CheckMD5 {
		return info, nil
	}
	objectMeta, err := bc.client.GetObjectMeta(bc.bucket,
		filepath.Join(bc.objectPrefix, filePath))
	if err != nil {
		logger.ComponentLogger.Error(ctx, "Get object meta fail", logit.String("err", err.Error()),
			logit.String("rsp", base_utils.Format(objectMeta)))
		return info, err
	}
	info.Crc32 = objectMeta.ContentCrc32
	info.Md5 = objectMeta.ContentMD5
	return info, nil
}

func (bc *BosStorageClient) GetFileContent(ctx context.Context, filePath string, bufferSize int) ([]byte, error) {
	res, err := bc.client.BasicGetObject(bc.bucket, filepath.Join(bc.objectPrefix, filePath))
	if err != nil {
		logger.ComponentLogger.Error(ctx, "Get object fail", logit.String("err", err.Error()))

		if realErr, ok := err.(*bce.BceServiceError); ok {
			if realErr.StatusCode == 404 {
				return nil, cerrs.ErrNamespaceStorageNoSuchFile.Errorf("file %s not exist", filePath)
			}
		}
		return nil, err
	}
	stream := res.Body
	// 确保关闭Object读取流
	defer stream.Close()
	var buffer []byte
	reader := bufio.NewReader(stream)
	for {
		line, err := reader.ReadBytes('\n')
		buffer = append(buffer, line...)
		if len(buffer) > bufferSize {
			return nil, errors.New("content is too long")
		}
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}
	}
	return buffer, nil
}

func NewBosStorageClient(ctx context.Context, param *StorageParam, config *config) (StorageClient, error) {
	if param == nil || param.BosParam.Bucket == "" || param.BosParam.ObjectPrefix == "" {
		return nil, cerrs.ErrNamespaceInvalidParam.Errorf("bucket or objectPrefix is empty")
	}
	if config == nil || config.BosConf == nil ||
		config.BosConf.BosListMaxKeys == 0 || config.BosConf.BosEndpoint == "" ||
		config.BosConf.PresignedUrlExpiration == 0 {
		return nil, cerrs.ErrNamespaceInvalidParam.Errorf("namespace config for bos is invalid")
	}
	bosClient := &BosStorageClient{
		conf:         config,
		bucket:       param.BosParam.Bucket,
		objectPrefix: param.BosParam.ObjectPrefix,
		iamUserID:    param.IamUserID,
	}
	auth, err := compo_utils.GetOpenapiAuth(ctx, param.IamUserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth fail", logit.Error("err", err),
			logit.String("iamUserID", param.IamUserID))
		return nil, err
	}
	client, err := bce_utils.NewBosClientWithSts(auth.Credential.Ak, auth.Credential.Sk,
		auth.Credential.SessionToken, config.BosConf.BosEndpoint)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get bos client fail", logit.Error("err", err))
		return nil, err
	}
	bosClient.client = client
	return bosClient, nil
}
