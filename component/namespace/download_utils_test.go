package namespace

import (
	"context"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"

	//"github.com/aws/aws-sdk-go-v2/aws"
	//"github.com/aws/aws-sdk-go-v2/credentials"
	//"github.com/aws/aws-sdk-go-v2/service/s3"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

func Test_getFileRelativePath(t *testing.T) {
	type args struct {
		objectPrefix string
		slotID       int
		file         string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{name: "test",
			args: args{
				slotID: 1,
				file:   "test",
			}, want: "1/test"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getFileRelativePath(tt.args.slotID, tt.args.file); got != tt.want {
				t.Errorf("getFileRelativePath() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getMetaFileRelativePath(t *testing.T) {
	type args struct {
		objectPrefix string
		slotID       int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{name: "test",
			args: args{
				slotID: 1,
			}, want: "1/1.meta"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getMetaFileRelativePath(tt.args.slotID); got != tt.want {
				t.Errorf("getFileRelativePath() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isSstFile(t *testing.T) {
	type args struct {
		fileName string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "true",
			args: args{
				fileName: "test.txt",
			},
			want: false,
		},
		{
			name: "true",
			args: args{
				fileName: "test.sst",
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isSstFile(tt.args.fileName); got != tt.want {
				t.Errorf("isSstFile() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getSlotID(t *testing.T) {
	type args struct {
		path string
	}
	tests := []struct {
		name       string
		args       args
		wantSlotID int
		wantErr    bool
	}{
		{name: "get slot id fail", args: args{path: "testbos12345/ut/list/success"}, wantSlotID: 0, wantErr: true},
		{name: "get slot id success ", args: args{path: "testbos12345/2/2.meta"}, wantSlotID: 2, wantErr: false},
		{name: "get slot id success ", args: args{path: "testbos12345/1/1.sst"}, wantSlotID: 1, wantErr: false},
		{name: "get slot id success ", args: args{path: "testbos12345/3/"}, wantSlotID: 3, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotSlotID, err := getSlotID(tt.args.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("getSlotID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotSlotID != tt.wantSlotID {
				t.Errorf("getSlotID() gotSlotID = %v, want %v", gotSlotID, tt.wantSlotID)
			}
		})
	}
}

func TestDownloadUtils_Config(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	c, err := GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "testbos12345",
			ObjectPrefix: "namespace1/",
		},
		S3Param: S3Param{},
	})
	if err != nil {
		fmt.Printf("InitBosClient failed, err: %v", err)
	}
	if c.GetMaxSstFileSize(ctx) != 1048576000 {
		t.Errorf("c.GetMaxSstFileSize() = %v, want 1048576000", c.GetMaxSstFileSize(ctx))
	}
	if c.GetDiskUsagePercentThreshold(ctx) != 80 {
		t.Errorf("c.GetDiskUsagePercentThreshold() = %v, want 90", c.GetDiskUsagePercentThreshold(ctx))
	}
	if c.GetDiskFreeBytesThreshold(ctx) != 21474836480 {
		t.Errorf("c.GetDiskFreeBytesThreshold() = %v, want 21474836480", c.GetDiskFreeBytesThreshold(ctx))
	}
	if c.GetMaxSlotNum(ctx) != 16 {
		t.Errorf("c.GetMaxSlotNum() = %v, want 16", c.GetMaxSlotNum(ctx))
	}
	if c.GetTrafficLimitMB(ctx) != 100 {
		t.Errorf("c.GetTrafficLimitMB() = %v, want 100", c.GetTrafficLimitMB(ctx))
	}
	if GetDownloadDir(ctx, "test") != "/mnt/download_sst/test" {
		t.Errorf("GetDownloadDir() = %v, want /mnt/download_sst/test", GetDownloadDir(ctx, "test"))
	}
	if GetCleanupStep(ctx) != 10 {
		t.Errorf("GetCleanupStep() = %v, want 10", GetCleanupStep(ctx))
	}
	if GetOperationReplicationOffset(ctx) != 100000 {
		t.Errorf("GetOperationReplicationOffset() = %v, want 100000", GetOperationReplicationOffset(ctx))
	}

}

func TestDownloadUtils_ListSstFilesOffline(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	c, err := GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "testbos12345",
			ObjectPrefix: "ut/list/slotidnoint",
		},
		S3Param: S3Param{},
	})
	_, err = c.ListSstFiles(ctx)
	if err == nil {
		fmt.Println("unit test not pass")
	}
	c, err = GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "testbos12345",
			ObjectPrefix: "ut/list/soltidilegal/",
		},
		S3Param: S3Param{},
	})
	_, err = c.ListSstFiles(ctx)
	if err == nil {
		fmt.Println("unit test not pass")
	}
	c, err = GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "testbos12345",
			ObjectPrefix: "ut/list/success",
		},
		S3Param: S3Param{},
	})
	_, err = c.ListSstFiles(ctx)
	if err != nil {
		fmt.Println("unit test not pass")
	}
}

func TestDownloadUtils_SlotSstFileNumMatchOffline(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	c, _ := GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "testbos12345",
			ObjectPrefix: "namespace1/",
		},
		S3Param: S3Param{},
	})
	rsp, _ := c.ListSstFiles(ctx)
	if c.SlotSstFileNumMatch(ctx, rsp[0]) != nil {
		fmt.Println("unit test not pass")
	}
	if c.SlotSstFileNumMatch(ctx, rsp[1]) != nil {
		fmt.Println("unit test not pass")
	}
	// meta文件不存在,sst文件不存在
	fmt.Println(c.SlotSstFileNumMatch(ctx, rsp[2]))
	// meta文件太大
	fmt.Println(c.SlotSstFileNumMatch(ctx, rsp[3]))
	// 不匹配
	fmt.Println(c.SlotSstFileNumMatch(ctx, rsp[4]))
	// sst不存在,meta存在
	fmt.Println(c.SlotSstFileNumMatch(ctx, rsp[5]))
}

func TestDownloadUtils_NamespaceMatchOffline(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	c, _ := GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "testbos12345",
			ObjectPrefix: "namespace2/",
		},
		S3Param: S3Param{},
	})
	if c.NamespaceMatch(ctx, "abc") != nil {
		fmt.Println("unit test not pass")
	}
	c, _ = GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "testbos12345",
			ObjectPrefix: "namespace2/",
		},
		S3Param: S3Param{},
	})
	if c.NamespaceMatch(ctx, "abcc") == nil {
		fmt.Println("unit test not pass2")
	}
	c, _ = GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "testbos12345",
			ObjectPrefix: "namespace1/",
		},
		S3Param: S3Param{},
	})
	if c.NamespaceMatch(ctx, "abc") == nil {
		fmt.Println("unit test not pass3")
	}
	c, _ = GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "testbos12345",
			ObjectPrefix: "ut/namespace_match/cccc/",
		},
		S3Param: S3Param{},
	})
	if c.NamespaceMatch(ctx, "abc") == nil {
		fmt.Println("unit test not pass4")
	}
	c, _ = GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "testbos12345",
			ObjectPrefix: "ut/list/slotidnoint/",
		},
		S3Param: S3Param{},
	})
	if !cerrs.Is(c.NamespaceMatch(ctx, "abc"), cerrs.ErrNamespaceNamespaceCheckFail) {
		fmt.Println("unit test not pass4")
	}
	c, _ = GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "testbos12345",
			ObjectPrefix: "ut/list/soltidilegal/",
		},
		S3Param: S3Param{},
	})
	if !cerrs.Is(c.NamespaceMatch(ctx, "abc"), cerrs.ErrNamespaceNamespaceCheckFail) {
		fmt.Println("unit test not pass4")
	}
}
func TestDownloadUtils_BucketAndObjectPrefixExistOffline(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	c, _ := GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "noexist",
			ObjectPrefix: "not exist",
		},
		S3Param: S3Param{},
	})
	if c.BucketAndObjectPrefixExist(ctx) == nil {
		fmt.Println("unit test not pass")
	}
	c, _ = GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "testbos12345",
			ObjectPrefix: "not exist",
		},
		S3Param: S3Param{},
	})
	if c.BucketAndObjectPrefixExist(ctx) == nil {
		fmt.Println("unit test not pass")
	}
	c, _ = GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "testbos12345",
			ObjectPrefix: "namespace1/",
		},
		S3Param: S3Param{},
	})
	if c.BucketAndObjectPrefixExist(ctx) != nil {
		fmt.Println("unit test not pass")
	}
}

func TestDownloadUtils_GetSlotDownloadInfoOffline(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	c, _ := GetDownloadUtils(ctx, &StorageParam{
		StorageType: "bos",
		IamUserID:   "4093ae6b9e48423e89c69916bc6d5d5e",
		BosParam: BosParam{
			Bucket:       "testbos12345",
			ObjectPrefix: "ut/list/success",
		},
		S3Param: S3Param{
			Bucket:       "test-xp",
			ObjectPrefix: "only-1-slot/",
		},
	})
	rsp, err := c.ListSstFiles(ctx)
	if err != nil {
		fmt.Println("unit test not pass")
	}
	s, err := c.GetSingleSlotDownloadInfo(ctx, rsp[0])
	fmt.Printf("%+v\n", s)
	a, err := c.GetAllSlotDownloadInfo(ctx, rsp)
	fmt.Printf("%+v\n", a[1].Files[1])
}
