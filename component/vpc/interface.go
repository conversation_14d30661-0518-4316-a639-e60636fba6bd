/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/08/20 <EMAIL> Exp
 *
 **************************************************************************/
package vpc

type OpSgsByBlbIDsParams struct {
	UserID           string   `json:"userId"`
	BlbIDs           []string `json:"BlbIDs"`
	SecurityGroupIDs []string `json:"securityGroupIDs"`
}

type OpSgsByEndpointIDsParams struct {
	UserID           string   `json:"userId"`
	EndpointIDs      []string `json:"endpointIDs"`
	SecurityGroupIDs []string `json:"securityGroupIDs"`
}

type GetSgIDsByBlbIDParams struct {
	UserID string `json:"userId"`
	BlbID  string `json:"blbID"`
}

type GetSgIDsByEndpointIDParams struct {
	UserID     string `json:"userId"`
	EndpointID string `json:"endpointID"`
}

type SecurityGroupIDs struct {
	shortIDs []string
	longIDs  []string
}

type CopyEndpointSgsParams struct {
	UserID        string `json:"userId"`
	SrcEndpointID string `json:"srcEndpointID"`
	DstEndpointID string `json:"dstEndpointID"`
}

type CopyBlbSgsParams struct {
	UserID   string `json:"userId"`
	SrcBlbID string `json:"srcBlbID"`
	DstBlbID string `json:"dstBlbID"`
}

type UnbindAllSgsFromBlbParams struct {
	UserID    string `json:"userId"`
	LongBlbID string `json:"longBlbID"`
}

type UnbindAllSgsFromEndpointParams struct {
	UserID     string `json:"userId"`
	EndpointID string `json:"endpointID"`
}
