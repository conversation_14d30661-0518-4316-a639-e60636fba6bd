/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package vpc

import (
	"context"
	"errors"

	. "github.com/agiledragon/gomonkey/v2"

	"reflect"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"

	"icode.baidu.com/baidu/scs/x1-base/sdk/vpc"
)

const (
	TestIamUserId = "b8096fa260534277ae0058138284416f"
)

func Test_component_BindSgsToBlbs(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	vpcOp := Instance()
	var err error
	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	err = vpcOp.BindSgsToBlbs(ctx, &OpSgsByBlbIDsParams{
		UserID:           "test",
		BlbIDs:           []string{},
		SecurityGroupIDs: []string{},
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()
	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{}, nil
	})
	patches3 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "BindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				errors.New("test BindSecurityGroup fail ")
		})
	err = vpcOp.BindSgsToBlbs(ctx, &OpSgsByBlbIDsParams{
		UserID:           "test",
		BlbIDs:           []string{},
		SecurityGroupIDs: []string{},
	})
	if err == nil || err.Error() != "test BindSecurityGroup fail " {
		t.Errorf("[%s] BindSecurityGroup fail: %s\n", t.Name(), err.Error())
	}
	patches3.Reset()
	patches2.Reset()
	patches4 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "BindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				nil
		})
	patches5 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})
	err = vpcOp.BindSgsToBlbs(ctx, &OpSgsByBlbIDsParams{
		UserID:           "test",
		BlbIDs:           []string{},
		SecurityGroupIDs: []string{},
	})
	if err != nil {
		t.Errorf("[%s] BindSecurityGroup fail: %s\n", t.Name(), err.Error())
	}
	patches4.Reset()
	patches5.Reset()
}

func Test_component_BindSgsToEndpoints(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	vpcOp := Instance()
	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	err := vpcOp.BindSgsToEndpoints(ctx, &OpSgsByEndpointIDsParams{
		UserID:           "test",
		EndpointIDs:      []string{},
		SecurityGroupIDs: []string{},
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()
	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})
	patches3 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "BindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				errors.New("test BindSecurityGroup fail ")
		})
	err = vpcOp.BindSgsToEndpoints(ctx, &OpSgsByEndpointIDsParams{
		UserID:           "test",
		EndpointIDs:      []string{},
		SecurityGroupIDs: []string{},
	})
	if err == nil || err.Error() != "test BindSecurityGroup fail " {
		t.Errorf("[%s] BindSecurityGroup fail: %s\n", t.Name(), err.Error())
	}
	patches3.Reset()
	patches2.Reset()
	patches4 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "BindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				nil
		})
	patches5 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})
	err = vpcOp.BindSgsToEndpoints(ctx, &OpSgsByEndpointIDsParams{
		UserID:           "test",
		EndpointIDs:      []string{},
		SecurityGroupIDs: []string{},
	})
	if err != nil {
		t.Errorf("[%s] BindSecurityGroup fail: %s\n", t.Name(), err.Error())
	}
	patches4.Reset()
	patches5.Reset()
}

func Test_component_GetSgIDsByBlbID(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	vpcOp := Instance()
	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	rsp, err := vpcOp.GetSgIDsByBlbID(ctx, &GetSgIDsByBlbIDParams{
		UserID: "test",
		BlbID:  "test",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()
	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{}, nil
	})
	patches3 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "ListSgsByInstanceID",
		func(ctx context.Context, req *vpc.ListSgsByInstanceIDRequest) (rsp *vpc.ListSgsByInstanceIDResponse, err error) {
			return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				errors.New("test GetSgIDsByEndpointID fail ")
		})
	rsp, err = vpcOp.GetSgIDsByBlbID(ctx, &GetSgIDsByBlbIDParams{
		UserID: "test",
		BlbID:  "test",
	})
	if err == nil || err.Error() != "test GetSgIDsByEndpointID fail " {
		t.Errorf("[%s] GetSgIDsByEndpointID fail: %s\n", t.Name(), err.Error())
	}
	patches3.Reset()
	patches2.Reset()
	patches4 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})
	patches5 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "ListSgsByInstanceID",
		func(ctx context.Context, req *vpc.ListSgsByInstanceIDRequest) (rsp *vpc.ListSgsByInstanceIDResponse, err error) {
			return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules: nil,
						SecurityGroups: []*vpc.SecurityGroup{
							{
								SecurityGroupId: "SecurityGroupId",
								Id:              "Id",
								Name:            "Name",
							},
						},
					}},
				nil
		})
	rsp, err = vpcOp.GetSgIDsByBlbID(ctx, &GetSgIDsByBlbIDParams{
		UserID: "test",
		BlbID:  "test",
	})
	if err != nil {
		t.Errorf("[%s] GetSgIDsByEndpointID fail: %s\n", t.Name(), err.Error())
	}
	if !reflect.DeepEqual(rsp, &SecurityGroupIDs{
		shortIDs: []string{"SecurityGroupId"},
		longIDs:  []string{"Id"},
	}) {
		t.Errorf("[%s] GetSgIDsByEndpointID fail: %v\n", t.Name(), rsp)
	}
	patches5.Reset()
	patches4.Reset()
}

func Test_component_GetSgIDsByEndpointID(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	vpcOp := Instance()
	var err error
	var rsp *SecurityGroupIDs
	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	rsp, err = vpcOp.GetSgIDsByEndpointID(ctx, &GetSgIDsByEndpointIDParams{
		UserID:     "test",
		EndpointID: "test",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()
	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{}, nil
	})
	patches3 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "ListSgsByInstanceID",
		func(ctx context.Context, req *vpc.ListSgsByInstanceIDRequest) (rsp *vpc.ListSgsByInstanceIDResponse, err error) {
			return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				errors.New("test GetSgIDsByEndpointID fail ")
		})
	rsp, err = vpcOp.GetSgIDsByEndpointID(ctx, &GetSgIDsByEndpointIDParams{
		UserID:     "test",
		EndpointID: "test",
	})
	if err == nil || err.Error() != "test GetSgIDsByEndpointID fail " {
		t.Errorf("[%s] GetSgIDsByEndpointID fail: %s\n", t.Name(), err.Error())
	}
	patches3.Reset()
	patches2.Reset()
	patches4 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})
	patches5 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "ListSgsByInstanceID",
		func(ctx context.Context, req *vpc.ListSgsByInstanceIDRequest) (rsp *vpc.ListSgsByInstanceIDResponse, err error) {
			return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules: nil,
						SecurityGroups: []*vpc.SecurityGroup{
							{
								SecurityGroupId: "SecurityGroupId",
								Id:              "Id",
								Name:            "Name",
							},
						},
					}},
				nil
		})
	rsp, err = vpcOp.GetSgIDsByEndpointID(ctx, &GetSgIDsByEndpointIDParams{
		UserID:     "test",
		EndpointID: "test",
	})
	if err != nil {
		t.Errorf("[%s] GetSgIDsByEndpointID fail: %s\n", t.Name(), err.Error())
	}
	if !reflect.DeepEqual(rsp, &SecurityGroupIDs{
		shortIDs: []string{"SecurityGroupId"},
		longIDs:  []string{"Id"},
	}) {
		t.Errorf("[%s] GetSgIDsByEndpointID fail: %v\n", t.Name(), rsp)
	}
	patches5.Reset()
	patches4.Reset()
}

func Test_component_UnBindSgsFromBlbs(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	vpcOp := Instance()
	var err error
	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	err = vpcOp.UnBindSgsFromBlbs(ctx, &OpSgsByBlbIDsParams{
		UserID:           "test",
		BlbIDs:           []string{},
		SecurityGroupIDs: []string{},
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()
	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{}, nil
	})
	patches3 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "UnBindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				errors.New("test UnBindSgsFromBlbs fail ")
		})
	err = vpcOp.UnBindSgsFromBlbs(ctx, &OpSgsByBlbIDsParams{
		UserID:           "test",
		BlbIDs:           []string{},
		SecurityGroupIDs: []string{},
	})
	if err == nil || err.Error() != "test UnBindSgsFromBlbs fail " {
		t.Errorf("[%s] BindSecurityGroup fail: %s\n", t.Name(), err.Error())
	}
	patches3.Reset()
	patches2.Reset()
	patches4 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "UnBindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				nil
		})
	patches5 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{}, nil
	})
	err = vpcOp.UnBindSgsFromBlbs(ctx, &OpSgsByBlbIDsParams{
		UserID:           "test",
		BlbIDs:           []string{},
		SecurityGroupIDs: []string{},
	})
	if err != nil {
		t.Errorf("[%s] UnBindSgsFromBlbs fail: %s\n", t.Name(), err.Error())
	}
	patches4.Reset()
	patches5.Reset()
}

func Test_component_UnBindSgsFromEndpoints(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	vpcOp := Instance()
	var err error
	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	err = vpcOp.UnBindSgsFromEndpoints(ctx, &OpSgsByEndpointIDsParams{
		UserID:           "test",
		EndpointIDs:      []string{},
		SecurityGroupIDs: []string{},
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()
	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{}, nil
	})
	patches3 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "UnBindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				errors.New("test BindSecurityGroup fail ")
		})
	err = vpcOp.UnBindSgsFromEndpoints(ctx, &OpSgsByEndpointIDsParams{
		UserID:           "test",
		EndpointIDs:      []string{"a"},
		SecurityGroupIDs: []string{"a"},
	})
	if err == nil || err.Error() != "test BindSecurityGroup fail " {
		t.Errorf("[%s] BindSecurityGroup fail: %s\n", t.Name(), err.Error())
	}
	patches3.Reset()
	patches2.Reset()
	patches4 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "UnBindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				nil
		})
	patches5 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{}, nil
	})
	err = vpcOp.UnBindSgsFromEndpoints(ctx, &OpSgsByEndpointIDsParams{
		UserID:           "test",
		EndpointIDs:      []string{},
		SecurityGroupIDs: []string{},
	})
	if err != nil {
		t.Errorf("[%s] BindSecurityGroup fail: %s\n", t.Name(), err.Error())
	}
	patches4.Reset()
	patches5.Reset()
}

//func Test_component_CopyBlbSgsOffline1(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	vpcOp := Instance()
//	//var err error
//	err := vpcOp.CopyBlbSgs(ctx, &CopyBlbSgsParams{
//		UserID:   TestIamUserId,
//		SrcBlbID: "6a49384275316870686f464e433666665957373755413d3d", //lb-c2caa81f
//		DstBlbID: "4a655634586676785a2b5453427175776363646836513d3d", //lb-b3127873
//	})
//	fmt.Println(err)
//}
//
//func Test_component_CopyEndpointSgsOffline1(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	vpcOp := Instance()
//	//var err error
//	err := vpcOp.CopyEndpointSgs(ctx, &CopyEndpointSgsParams{
//		UserID:        TestIamUserId,
//		SrcEndpointID: "endpoint-4d26d559",
//		DstEndpointID: "endpoint-93408020",
//	})
//	fmt.Println(err)
//}

func Test_component_CopyBlbSgs(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	vpcOp := Instance()
	var err error
	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	err = vpcOp.CopyBlbSgs(ctx, &CopyBlbSgsParams{
		UserID:   "test",
		SrcBlbID: "src",
		DstBlbID: "dst",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()
	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{}, nil
	})
	patches3 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "ListSgsByInstanceID",
		func(ctx context.Context, req *vpc.ListSgsByInstanceIDRequest) (rsp *vpc.ListSgsByInstanceIDResponse, err error) {
			if req.InstanceId == "src" {
				return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules: nil,
						SecurityGroups: []*vpc.SecurityGroup{
							{

								SecurityGroupId: "SecurityGroupId",
								Id:              "Id",
								Name:            "Name",
							},
						},
					}}, nil
			}
			return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules: nil,
						SecurityGroups: []*vpc.SecurityGroup{
							{
								SecurityGroupId: "SecurityGroupId",
								Id:              "Id",
								Name:            "Name",
							},
						},
					}},
				nil
		})
	err = vpcOp.CopyBlbSgs(ctx, &CopyBlbSgsParams{
		UserID:   "test",
		SrcBlbID: "src",
		DstBlbID: "dst",
	})
	if err != nil {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}

	patches3.Reset()
	patches4 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "ListSgsByInstanceID",
		func(ctx context.Context, req *vpc.ListSgsByInstanceIDRequest) (rsp *vpc.ListSgsByInstanceIDResponse, err error) {
			if req.InstanceId == "src" {
				return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules: nil,
						SecurityGroups: []*vpc.SecurityGroup{
							{

								SecurityGroupId: "SecurityGroupId",
								Id:              "Id",
								Name:            "Name",
							},
							{

								SecurityGroupId: "SecurityGroupId1",
								Id:              "Id1",
								Name:            "Name1",
							},
						},
					}}, nil
			}
			return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules: nil,
						SecurityGroups: []*vpc.SecurityGroup{
							{
								SecurityGroupId: "SecurityGroupId",
								Id:              "Id",
								Name:            "Name",
							},
						},
					}},
				nil
		})
	patches5 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "BindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				errors.New("test BindSecurityGroup fail ")
		})
	err = vpcOp.CopyBlbSgs(ctx, &CopyBlbSgsParams{
		UserID:   "test",
		SrcBlbID: "src",
		DstBlbID: "dst",
	})
	if err == nil {
		t.Errorf("[%s] BindSecurityGroup fail\n", t.Name())
	}
	patches5.Reset()
	patches6 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "BindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				nil
		})
	err = vpcOp.CopyBlbSgs(ctx, &CopyBlbSgsParams{
		UserID:   "test",
		SrcBlbID: "src",
		DstBlbID: "dst",
	})
	if err != nil {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches6.Reset()
	patches4.Reset()
	patches7 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "ListSgsByInstanceID",
		func(ctx context.Context, req *vpc.ListSgsByInstanceIDRequest) (rsp *vpc.ListSgsByInstanceIDResponse, err error) {
			if req.InstanceId == "src" {
				return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules: nil,
						SecurityGroups: []*vpc.SecurityGroup{
							{

								SecurityGroupId: "SecurityGroupId",
								Id:              "Id",
								Name:            "Name",
							},
						},
					}}, errors.New("test ListSgsByInstanceID fail ")
			}
			return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules: nil,
						SecurityGroups: []*vpc.SecurityGroup{
							{
								SecurityGroupId: "SecurityGroupId",
								Id:              "Id",
								Name:            "Name",
							},
						},
					}},
				nil
		})
	err = vpcOp.CopyBlbSgs(ctx, &CopyBlbSgsParams{
		UserID:   "test",
		SrcBlbID: "src",
		DstBlbID: "dst",
	})
	if err == nil {
		t.Errorf("[%s] test fail\n", t.Name())
	}
	err = vpcOp.CopyBlbSgs(ctx, &CopyBlbSgsParams{
		UserID:   "test",
		SrcBlbID: "dst",
		DstBlbID: "src",
	})
	if err == nil {
		t.Errorf("[%s] test fail\n", t.Name())
	}
	patches7.Reset()
	patches2.Reset()
}

func Test_component_CopyEndpointSgs(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	vpcOp := Instance()
	var err error
	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	err = vpcOp.CopyEndpointSgs(ctx, &CopyEndpointSgsParams{
		UserID:        "test",
		SrcEndpointID: "src",
		DstEndpointID: "dst",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()
	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{}, nil
	})
	patches3 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "ListSgsByInstanceID",
		func(ctx context.Context, req *vpc.ListSgsByInstanceIDRequest) (rsp *vpc.ListSgsByInstanceIDResponse, err error) {
			if req.InstanceId == "src" {
				return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules: nil,
						SecurityGroups: []*vpc.SecurityGroup{
							{

								SecurityGroupId: "SecurityGroupId",
								Id:              "Id",
								Name:            "Name",
							},
						},
					}}, nil
			}
			return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules: nil,
						SecurityGroups: []*vpc.SecurityGroup{
							{
								SecurityGroupId: "SecurityGroupId",
								Id:              "Id",
								Name:            "Name",
							},
						},
					}},
				nil
		})
	err = vpcOp.CopyEndpointSgs(ctx, &CopyEndpointSgsParams{
		UserID:        "test",
		SrcEndpointID: "src",
		DstEndpointID: "dst",
	})
	if err != nil {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}

	patches3.Reset()
	patches4 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "ListSgsByInstanceID",
		func(ctx context.Context, req *vpc.ListSgsByInstanceIDRequest) (rsp *vpc.ListSgsByInstanceIDResponse, err error) {
			if req.InstanceId == "src" {
				return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules: nil,
						SecurityGroups: []*vpc.SecurityGroup{
							{

								SecurityGroupId: "SecurityGroupId",
								Id:              "Id",
								Name:            "Name",
							},
							{

								SecurityGroupId: "SecurityGroupId1",
								Id:              "Id1",
								Name:            "Name1",
							},
						},
					}}, nil
			}
			return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules: nil,
						SecurityGroups: []*vpc.SecurityGroup{
							{
								SecurityGroupId: "SecurityGroupId",
								Id:              "Id",
								Name:            "Name",
							},
						},
					}},
				nil
		})
	patches5 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "BindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				errors.New("test BindSecurityGroup fail ")
		})
	err = vpcOp.CopyEndpointSgs(ctx, &CopyEndpointSgsParams{
		UserID:        "test",
		SrcEndpointID: "src",
		DstEndpointID: "dst",
	})
	if err == nil {
		t.Errorf("[%s] BindSecurityGroup fail\n", t.Name())
	}
	patches5.Reset()
	patches6 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "BindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				nil
		})
	err = vpcOp.CopyEndpointSgs(ctx, &CopyEndpointSgsParams{
		UserID:        "test",
		SrcEndpointID: "src",
		DstEndpointID: "dst",
	})
	if err != nil {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches6.Reset()
	patches4.Reset()
	patches2.Reset()
}

func Test_component_UnbindAllSgsFromEndpoint(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	vpcOp := Instance()
	var err error
	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	err = vpcOp.UnbindAllSgsFromEndpoint(ctx, &UnbindAllSgsFromEndpointParams{
		UserID:     "test",
		EndpointID: "dst",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()
	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{}, nil
	})
	patches3 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "ListSgsByInstanceID",
		func(ctx context.Context, req *vpc.ListSgsByInstanceIDRequest) (rsp *vpc.ListSgsByInstanceIDResponse, err error) {
			return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules:    nil,
						SecurityGroups: []*vpc.SecurityGroup{},
					}},
				nil
		})
	err = vpcOp.UnbindAllSgsFromEndpoint(ctx, &UnbindAllSgsFromEndpointParams{
		UserID:     "test",
		EndpointID: "src",
	})
	if err != nil {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}

	patches3.Reset()
	patches4 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "ListSgsByInstanceID",
		func(ctx context.Context, req *vpc.ListSgsByInstanceIDRequest) (rsp *vpc.ListSgsByInstanceIDResponse, err error) {
			return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules: nil,
						SecurityGroups: []*vpc.SecurityGroup{
							{
								SecurityGroupId: "SecurityGroupId",
								Id:              "Id",
								Name:            "Name",
							},
						},
					}},
				nil
		})
	patches5 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "UnBindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				errors.New("test UnBindSecurityGroup fail ")
		})
	err = vpcOp.UnbindAllSgsFromEndpoint(ctx, &UnbindAllSgsFromEndpointParams{
		UserID:     "test",
		EndpointID: "src",
	})
	if err == nil {
		t.Errorf("[%s] UnBindSecurityGroup fail\n", t.Name())
	}
	patches5.Reset()
	patches6 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "UnBindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				nil
		})
	err = vpcOp.UnbindAllSgsFromEndpoint(ctx, &UnbindAllSgsFromEndpointParams{
		UserID:     "test",
		EndpointID: "src",
	})
	if err != nil {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches6.Reset()
	patches4.Reset()
	patches2.Reset()
}

//func Test_component_UnbindAllSgsFromBlOffline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	vpcOp := Instance()
//	//var err error
//	err := vpcOp.UnbindAllSgsFromBlb(ctx, &UnbindAllSgsFromBlbParams{
//		UserID:    TestIamUserId,
//		LongBlbID: "4a655634586676785a2b5453427175776363646836513d3d", //lb-b3127873
//	})
//	fmt.Println(err)
//}

func Test_component_UnbindAllSgsFromBlb(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	vpcOp := Instance()
	var err error
	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	err = vpcOp.UnbindAllSgsFromBlb(ctx, &UnbindAllSgsFromBlbParams{
		UserID:    "test",
		LongBlbID: "long",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()
	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return &common.Authentication{}, nil
	})
	patches3 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "ListSgsByInstanceID",
		func(ctx context.Context, req *vpc.ListSgsByInstanceIDRequest) (rsp *vpc.ListSgsByInstanceIDResponse, err error) {
			return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules:    nil,
						SecurityGroups: []*vpc.SecurityGroup{},
					}},
				nil
		})
	err = vpcOp.UnbindAllSgsFromBlb(ctx, &UnbindAllSgsFromBlbParams{
		UserID:    "test",
		LongBlbID: "long",
	})
	if err != nil {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}

	patches3.Reset()
	patches4 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "ListSgsByInstanceID",
		func(ctx context.Context, req *vpc.ListSgsByInstanceIDRequest) (rsp *vpc.ListSgsByInstanceIDResponse, err error) {
			return &vpc.ListSgsByInstanceIDResponse{
					Message:   "",
					Code:      "",
					RequestID: "",
					Result: &vpc.ListSgsByInstanceIDResult{
						ActiveRules: nil,
						SecurityGroups: []*vpc.SecurityGroup{
							{
								SecurityGroupId: "SecurityGroupId",
								Id:              "Id",
								Name:            "Name",
							},
						},
					}},
				nil
		})
	patches5 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "UnBindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				errors.New("test UnBindSecurityGroup fail ")
		})
	err = vpcOp.UnbindAllSgsFromBlb(ctx, &UnbindAllSgsFromBlbParams{
		UserID:    "test",
		LongBlbID: "long",
	})
	if err == nil {
		t.Errorf("[%s] UnBindSecurityGroup fail\n", t.Name())
	}
	patches5.Reset()
	patches6 := ApplyMethodFunc(reflect.TypeOf(vpcOp.vpcSdk), "UnBindSecurityGroup",
		func(ctx context.Context, req *vpc.SecurityGroupOpRequest) (rsp *vpc.CommonResponse, err error) {
			return &vpc.CommonResponse{
					Message:   "",
					Code:      "",
					RequestID: ""},
				nil
		})
	err = vpcOp.UnbindAllSgsFromBlb(ctx, &UnbindAllSgsFromBlbParams{
		UserID:    "test",
		LongBlbID: "long",
	})
	if err != nil {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches6.Reset()
	patches4.Reset()
	patches2.Reset()
}
