/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/08/20 <EMAIL> Exp
 *
 **************************************************************************/
package vpc

import (
	"context"
	"sync"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/vpc"
)

type component struct {
	conf     *config
	vpcSdk   vpc.VpcService
	initOnce sync.Once
}

var defaultComponent = &component{
	conf: &config{},
}

// Instance return default component
func Instance() *component {
	defaultComponent.initOnce.Do(func() {
		// load default conf
		if err := compo_utils.LoadConf("vpc", defaultComponent.conf); err != nil {
			panic(err.Error())
		}

		defaultComponent.vpcSdk = vpc.NewDefaultvpcSdk()
	})

	return defaultComponent
}

// BindSgsToBlbs 绑定安全组到blb
func (c *component) BindSgsToBlbs(ctx context.Context, params *OpSgsByBlbIDsParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	rsp, err := c.vpcSdk.BindSecurityGroup(ctx, &vpc.SecurityGroupOpRequest{
		UserId:             params.UserID,
		Auth:               auth,
		InstanceUuids:      params.BlbIDs,
		InstanceType:       c.conf.InstanceType,
		SubInstanceType:    SubInstanceTypeBlb,
		SecurityGroupUuids: params.SecurityGroupIDs,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "BindSgsToBlbs Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(rsp)),
			logit.Error("error", err))
		return err
	}
	return nil
}

// UnBindSgsFromBlbs 解绑blb上绑定的安全组
func (c *component) UnBindSgsFromBlbs(ctx context.Context, params *OpSgsByBlbIDsParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	rsp, err := c.vpcSdk.UnBindSecurityGroup(ctx, &vpc.SecurityGroupOpRequest{
		UserId:             params.UserID,
		Auth:               auth,
		InstanceUuids:      params.BlbIDs,
		InstanceType:       c.conf.InstanceType,
		SubInstanceType:    SubInstanceTypeBlb,
		SecurityGroupUuids: params.SecurityGroupIDs,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "UnBindSgsFromBlbs Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(rsp)),
			logit.Error("error", err))
		return err
	}
	return nil
}

// BindSgsToEndpoints 绑定安全组到endpoint
func (c *component) BindSgsToEndpoints(ctx context.Context, params *OpSgsByEndpointIDsParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	rsp, err := c.vpcSdk.BindSecurityGroup(ctx, &vpc.SecurityGroupOpRequest{
		UserId:             params.UserID,
		Auth:               auth,
		InstanceUuids:      params.EndpointIDs,
		InstanceType:       c.conf.InstanceType,
		SubInstanceType:    SubInstanceTypeSnic,
		SecurityGroupUuids: params.SecurityGroupIDs,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "BindSgsToEndpoints Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(rsp)),
			logit.Error("error", err))
		return err
	}
	return nil
}

// UnBindSgsFromEndpoints 解绑endpoint上绑定的安全组
func (c *component) UnBindSgsFromEndpoints(ctx context.Context, params *OpSgsByEndpointIDsParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	rsp, err := c.vpcSdk.UnBindSecurityGroup(ctx, &vpc.SecurityGroupOpRequest{
		UserId:             params.UserID,
		Auth:               auth,
		InstanceUuids:      params.EndpointIDs,
		InstanceType:       c.conf.InstanceType,
		SubInstanceType:    SubInstanceTypeSnic,
		SecurityGroupUuids: params.SecurityGroupIDs,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "UnBindSgsFromEndpoints Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(rsp)),
			logit.Error("error", err))
		return err
	}
	return nil
}

// GetSgIDsByBlbID 获取blb上绑定的安全组
func (c *component) GetSgIDsByBlbID(ctx context.Context, params *GetSgIDsByBlbIDParams) (*SecurityGroupIDs, error) {
	sgIDs := &SecurityGroupIDs{
		shortIDs: make([]string, 0),
		longIDs:  make([]string, 0),
	}
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}
	rsp, err := c.vpcSdk.ListSgsByInstanceID(ctx, &vpc.ListSgsByInstanceIDRequest{
		UserId:          params.UserID,
		Auth:            auth,
		InstanceId:      params.BlbID,
		InstanceType:    c.conf.InstanceType,
		SubInstanceType: SubInstanceTypeBlb,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "GetSgIDsByBlbID Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(rsp)),
			logit.Error("error", err))
		return nil, err
	}
	for _, sg := range rsp.Result.SecurityGroups {
		sgIDs.longIDs = append(sgIDs.longIDs, sg.Id)
		sgIDs.shortIDs = append(sgIDs.shortIDs, sg.SecurityGroupId)
	}
	return sgIDs, nil
}

// GetSgIDsByEndpointID 获取blb上绑定的安全组
func (c *component) GetSgIDsByEndpointID(ctx context.Context, params *GetSgIDsByEndpointIDParams) (*SecurityGroupIDs, error) {
	sgIDs := &SecurityGroupIDs{
		shortIDs: make([]string, 0),
		longIDs:  make([]string, 0),
	}
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}
	rsp, err := c.vpcSdk.ListSgsByInstanceID(ctx, &vpc.ListSgsByInstanceIDRequest{
		UserId:          params.UserID,
		Auth:            auth,
		InstanceId:      params.EndpointID,
		InstanceType:    c.conf.InstanceType,
		SubInstanceType: SubInstanceTypeSnic,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "GetSgIDsByEndpointID Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(rsp)),
			logit.Error("error", err))
		return nil, err
	}
	for _, sg := range rsp.Result.SecurityGroups {
		sgIDs.longIDs = append(sgIDs.longIDs, sg.Id)
		sgIDs.shortIDs = append(sgIDs.shortIDs, sg.SecurityGroupId)
	}
	return sgIDs, nil
}

// CopyEndpointSgs 将endpoint绑定的安全组绑定到目标endpoint
func (c *component) CopyEndpointSgs(ctx context.Context, params *CopyEndpointSgsParams) error {

	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	sgsWithSrc, err := c.vpcSdk.ListSgsByInstanceID(ctx, &vpc.ListSgsByInstanceIDRequest{
		UserId:          params.UserID,
		Auth:            auth,
		InstanceId:      params.SrcEndpointID,
		InstanceType:    c.conf.InstanceType,
		SubInstanceType: SubInstanceTypeSnic,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "ListSgsByInstanceID Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(sgsWithSrc)),
			logit.Error("error", err))
		return err
	}
	sgsWithDst, err := c.vpcSdk.ListSgsByInstanceID(ctx, &vpc.ListSgsByInstanceIDRequest{
		UserId:          params.UserID,
		Auth:            auth,
		InstanceId:      params.DstEndpointID,
		InstanceType:    c.conf.InstanceType,
		SubInstanceType: SubInstanceTypeSnic,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "ListSgsByInstanceID Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(sgsWithDst)),
			logit.Error("error", err))
		return err
	}
	sgsToBind := make([]string, 0)
	for _, sgOrgin := range sgsWithSrc.Result.SecurityGroups {
		found := false
		for _, sgTarget := range sgsWithDst.Result.SecurityGroups {
			if sgOrgin.Id == sgTarget.Id {
				found = true
			}
		}
		if !found {
			sgsToBind = append(sgsToBind, sgOrgin.Id)
		}
	}

	logger.ComponentLogger.Notice(ctx, "CopyEndpointSgs sgsToBind:%s", base_utils.Format(sgsToBind))

	if len(sgsToBind) == 0 {
		return nil
	}

	rsp, err := c.vpcSdk.BindSecurityGroup(ctx, &vpc.SecurityGroupOpRequest{
		UserId:             params.UserID,
		Auth:               auth,
		InstanceUuids:      []string{params.DstEndpointID},
		InstanceType:       c.conf.InstanceType,
		SecurityGroupUuids: sgsToBind,
		SubInstanceType:    SubInstanceTypeSnic,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "CopyEndpointSgs Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(rsp)),
			logit.Error("error", err))
		return err
	}
	return nil
}

// CopyBlbSgs 将blb上绑定的安全组绑定到目标blb
func (c *component) CopyBlbSgs(ctx context.Context, params *CopyBlbSgsParams) error {

	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	sgsWithSrc, err := c.vpcSdk.ListSgsByInstanceID(ctx, &vpc.ListSgsByInstanceIDRequest{
		UserId:          params.UserID,
		Auth:            auth,
		InstanceId:      params.SrcBlbID,
		InstanceType:    c.conf.InstanceType,
		SubInstanceType: SubInstanceTypeBlb,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "ListSgsByInstanceID Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(sgsWithSrc)),
			logit.Error("error", err))
		return err
	}
	sgsWithDst, err := c.vpcSdk.ListSgsByInstanceID(ctx, &vpc.ListSgsByInstanceIDRequest{
		UserId:          params.UserID,
		Auth:            auth,
		InstanceId:      params.DstBlbID,
		InstanceType:    c.conf.InstanceType,
		SubInstanceType: SubInstanceTypeBlb,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "ListSgsByInstanceID Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(sgsWithDst)),
			logit.Error("error", err))
		return err
	}
	sgsToBind := make([]string, 0)
	for _, sgOrgin := range sgsWithSrc.Result.SecurityGroups {
		found := false
		for _, sgTarget := range sgsWithDst.Result.SecurityGroups {
			if sgOrgin.Id == sgTarget.Id {
				found = true
			}
		}
		if !found {
			sgsToBind = append(sgsToBind, sgOrgin.Id)
		}
	}

	logger.ComponentLogger.Notice(ctx, "CopyBlbSgs sgsToBind:%s", base_utils.Format(sgsToBind))
	if len(sgsToBind) == 0 {
		return nil
	}

	rsp, err := c.vpcSdk.BindSecurityGroup(ctx, &vpc.SecurityGroupOpRequest{
		UserId:             params.UserID,
		Auth:               auth,
		InstanceUuids:      []string{params.DstBlbID},
		InstanceType:       c.conf.InstanceType,
		SecurityGroupUuids: sgsToBind,
		SubInstanceType:    SubInstanceTypeBlb,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "CopyBlbSgs Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(rsp)),
			logit.Error("error", err))
		return err
	}
	return nil
}

// UnbindAllSgsFromBlb 将blb上绑定的安全组全部解绑
func (c *component) UnbindAllSgsFromBlb(ctx context.Context, params *UnbindAllSgsFromBlbParams) error {

	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	sgsRsp, err := c.vpcSdk.ListSgsByInstanceID(ctx, &vpc.ListSgsByInstanceIDRequest{
		UserId:          params.UserID,
		Auth:            auth,
		InstanceId:      params.LongBlbID,
		InstanceType:    c.conf.InstanceType,
		SubInstanceType: SubInstanceTypeBlb,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "ListSgsByInstanceID Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(sgsRsp)),
			logit.Error("error", err))
		return err
	}
	sgsToUnBind := make([]string, 0)
	for _, sgOrgin := range sgsRsp.Result.SecurityGroups {
		sgsToUnBind = append(sgsToUnBind, sgOrgin.Id)
	}

	logger.ComponentLogger.Notice(ctx, "sgsToUnBind:%s", base_utils.Format(sgsToUnBind))
	if len(sgsToUnBind) == 0 {
		return nil
	}

	rsp, err := c.vpcSdk.UnBindSecurityGroup(ctx, &vpc.SecurityGroupOpRequest{
		UserId:             params.UserID,
		Auth:               auth,
		InstanceUuids:      []string{params.LongBlbID},
		InstanceType:       c.conf.InstanceType,
		SecurityGroupUuids: sgsToUnBind,
		SubInstanceType:    SubInstanceTypeBlb,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "UnBindSecurityGroup Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(rsp)),
			logit.Error("error", err))
		return err
	}
	return nil
}

// UnbindAllSgsFromEndpoint 将endpoint上绑定的安全组全部解绑
func (c *component) UnbindAllSgsFromEndpoint(ctx context.Context, params *UnbindAllSgsFromEndpointParams) error {

	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	sgsRsp, err := c.vpcSdk.ListSgsByInstanceID(ctx, &vpc.ListSgsByInstanceIDRequest{
		UserId:          params.UserID,
		Auth:            auth,
		InstanceId:      params.EndpointID,
		InstanceType:    c.conf.InstanceType,
		SubInstanceType: SubInstanceTypeSnic,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "ListSgsByInstanceID Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(sgsRsp)),
			logit.Error("error", err))
		return err
	}
	sgsToUnBind := make([]string, 0)
	for _, sgOrgin := range sgsRsp.Result.SecurityGroups {
		sgsToUnBind = append(sgsToUnBind, sgOrgin.Id)
	}

	logger.ComponentLogger.Notice(ctx, "sgsToUnBind:%s", base_utils.Format(sgsToUnBind))
	if len(sgsToUnBind) == 0 {
		return nil
	}

	rsp, err := c.vpcSdk.UnBindSecurityGroup(ctx, &vpc.SecurityGroupOpRequest{
		UserId:             params.UserID,
		Auth:               auth,
		InstanceUuids:      []string{params.EndpointID},
		InstanceType:       c.conf.InstanceType,
		SecurityGroupUuids: sgsToUnBind,
		SubInstanceType:    SubInstanceTypeSnic,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "CopyBlbSgs Fail",
			logit.String("user_id", params.UserID), logit.String("rsp", base_utils.Format(rsp)),
			logit.Error("error", err))
		return err
	}
	return nil
}
