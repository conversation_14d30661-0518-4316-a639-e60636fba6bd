/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/02/20 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file config.go
 * <AUTHOR>
 * @date 2023/02/20 16:11:50
 * @brief
 *
 **/

package bcm

type config struct {
	ServiceName       string `json:"serviceName"`
	Region            string `json:"region"`
	ListGroupPageSize int    `json:"listGroupPageSize"`
}

const GroupInstanceActionAdd = "add"
const GroupInstanceActionRemove = "remove"

const sampleCountMetricName = "CPUUsagePercent"
const staticsSampleCount = "sampleCount"
