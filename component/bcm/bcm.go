/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/02/20 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file bcm.go
 * <AUTHOR>
 * @date 2023/02/20 16:11:05
 * @brief
 *
 **/

package bcm

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"github.com/google/uuid"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcm"
)

type component struct {
	conf     *config
	bcmSdk   bcm.BcmService
	initOnce sync.Once
}

var defaultComponent = &component{
	conf: &config{},
}

// Instance return default component
func Instance() *component {
	defaultComponent.initOnce.Do(func() {
		// load default conf
		if err := compo_utils.LoadConf("bcm", defaultComponent.conf); err != nil {
			panic(err.Error())
		}

		defaultComponent.bcmSdk = bcm.NewDefaultBCMSdk()
	})

	return defaultComponent
}

// PushEvents will push event to bcm
func (c *component) PushEvents(ctx context.Context, params *PushEventParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	newUUID, _ := uuid.NewUUID()
	result := &bcm.Result{
		Region:       c.conf.Region,
		ResourceID:   params.Info.ResourceID,
		ResourceType: params.Info.ResourceType,
		EventID:      newUUID.String(),
		EventType:    params.Info.EventType,
		EventLevel:   params.Info.EventLevel,
		EventAlias:   params.Info.EventAlias,
		Timestamp:    time.Now().Format("2006-01-02T15:04:05Z"),
		Content:      params.Info.Content,
	}

	bcmRequest := bcm.PushEventRequest{
		Result:      result,
		AccountID:   auth.IamUserId,
		ServiceName: c.conf.ServiceName,
		Auth:        auth,
	}

	res, err := c.bcmSdk.PushEvents(ctx, &bcmRequest)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Push Event to BCM Fail,req:%+v, user:%s, err:%s", bcmRequest, bcmRequest.Auth.IamUserId, err.Error())
		return err
	}

	if res.Code != 0 {
		logger.ComponentLogger.Warning(ctx, "Push Event to BCM Fail,req:%+v, user:%s, response:%+v", bcmRequest, bcmRequest.Auth.IamUserId, res)
		return &PushEventError{Code: res.Code, Message: res.Message, EventID: res.EventID}
	}

	logger.ComponentLogger.Trace(ctx, "Push Event to BCM Suc,req:%+v, user:%s response:%+v", bcmRequest, bcmRequest.Auth.IamUserId, res)
	return nil
}

func generateAppResourceID(appID string) string {
	return appID
}

func generateClusterNodeName(appID, hashName, appType string) string {
	if appType == x1model.AppTypeStandalone {
		return appID + "-0"
	}
	subStrings := strings.Split(hashName, "_")
	return appID + "-" + subStrings[len(subStrings)-1]
}

func generateClusterResourceID(appID, hashName, appType string) string {
	return appID + "___" + generateClusterNodeName(appID, hashName, appType)
}

func generateNodeNodeName(nodeFixID string) string {
	return nodeFixID
}

func generateNodeResourceID(appID, nodeFixID string) string {
	return appID + "___" + generateNodeNodeName(nodeFixID)
}

func generateSyncFlowAppID(appID, targetBlb string, targetPort int64) string {
	return appID + "-" + targetBlb + "[" + cast.ToString(targetPort) + "]"
}

func generateSyncFlowClusterResourceID(appID, targetBlb, hashName, appType string, targetPort int64) string {
	return generateSyncFlowAppID(appID, targetBlb, targetPort) + "___" + generateClusterNodeName(appID, hashName, appType)
}

// generateAppResourceIdForGroup 创建集群维度实例组
func generateAppResourceIdForGroup(appID string) string {
	//BCM历史遗留有写死的兼容,cluster show id到cluster id，nodename到nodeid的转换
	return "ClusterId:" + appID
}

// generateClusterResourceIdForGroup
func generateClusterResourceIdForGroup(appID, hashName, appType string) string {
	//BCM历史遗留有写死的兼容,cluster show id到cluster id，nodename到nodeid的转换
	return "ClusterId:" + appID + ";" + "NodeId:" + generateClusterNodeName(appID, hashName, appType)
}

// generateNodeResourceIdForGroup
func generateNodeResourceIdForGroup(appID, nodeFixID string) string {
	//BCM历史遗留有写死的兼容,cluster show id到cluster id，nodename到nodeid的转换
	return "ClusterId:" + appID + ";" + "NodeId:" + generateNodeNodeName(nodeFixID)
}

// DeleteAppResourceIfExisted will delete bcm app resource if exist
func (c *component) DeleteAppResourceIfExisted(ctx context.Context, params *DeleteAppResourceIfExistedParams) (err error) {
	return c.DeleteResourceIfExisted(ctx, &DeleteResourceIfExistedParams{
		UserID:     params.UserID,
		ResourceID: generateAppResourceID(params.AppID),
	})
}

// DeleteClusterResourceIfExisted will delete bcm cluster resource if exist
func (c *component) DeleteClusterResourceIfExisted(ctx context.Context, params *DeleteClusterResourceIfExistedParams) (err error) {
	return c.DeleteResourceIfExisted(ctx, &DeleteResourceIfExistedParams{
		UserID:     params.UserID,
		ResourceID: generateClusterResourceID(params.AppID, params.HashName, params.AppType),
	})
}

// DeleteNodeResourceIfExisted  will delete bcm node resource if exist
func (c *component) DeleteNodeResourceIfExisted(ctx context.Context, params *DeleteNodeResourceIfExistedParams) (err error) {
	return c.DeleteResourceIfExisted(ctx, &DeleteResourceIfExistedParams{
		UserID:     params.UserID,
		ResourceID: generateNodeResourceID(params.AppID, params.NodeFixID),
	})
}

// DeleteResourceIfExisted will delete bcm resource if exist
func (c *component) DeleteResourceIfExisted(ctx context.Context, params *DeleteResourceIfExistedParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	//resourceID := generateResourceID(params.AppID, params.ClusterID, params.NodeFixID, params.HashName)
	getReq := &bcm.GetResourceRequest{
		UserId:      params.UserID,
		Region:      c.conf.Region,
		ServiceName: c.conf.ServiceName,
		ResourceId:  params.ResourceID,
		Auth:        auth,
	}
	getRsp, err := c.bcmSdk.GetResource(ctx, getReq)
	if err != nil && getRsp.Code == "ResourceNotExistException" {
		return nil
	}
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get BCM Resource Fail,req:%+v, user:%s, response:%+v", getReq, getReq.Auth.IamUserId, getRsp)
		return err
	}

	delReq := &bcm.DeleteResourceRequest{
		UserId:      params.UserID,
		ServiceName: c.conf.ServiceName,
		Region:      c.conf.Region,
		ResourceId:  params.ResourceID,
		Auth:        auth,
	}

	delRsp, err := c.bcmSdk.DeleteResource(ctx, delReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Delete BCM Resource Fail,req:%+v, user:%s, response:%+v", delReq, delReq.Auth.IamUserId, delRsp)
		return err
	}
	return nil
}

// CheckAppResourceExist will check app resource exist or not
func (c *component) CheckAppResourceExist(ctx context.Context, params *CheckAppResourceExistParams) (exist bool, err error) {
	return c.CheckResourceExist(ctx, &CheckResourceExistParams{
		UserID:     params.UserID,
		ResourceID: params.AppID,
	})
}

// CheckClusterResourceExist will check cluster resource exist or not
func (c *component) CheckClusterResourceExist(ctx context.Context, params *CheckClusterResourceExistParams) (exist bool, err error) {
	return c.CheckResourceExist(ctx, &CheckResourceExistParams{
		UserID:     params.UserID,
		ResourceID: generateClusterResourceID(params.AppID, params.HashName, params.AppType),
	})
}

// CheckNodeResourceExist will check node resource exist or not
func (c *component) CheckNodeResourceExist(ctx context.Context, params *CheckNodeResourceExistParams) (exist bool, err error) {
	return c.CheckResourceExist(ctx, &CheckResourceExistParams{
		UserID:     params.UserID,
		ResourceID: generateNodeResourceID(params.AppID, params.NodeFixID),
	})
}

// CheckResourceExist will check resource exist or not
func (c *component) CheckResourceExist(ctx context.Context, params *CheckResourceExistParams) (exist bool, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return false, err
	}
	getReq := &bcm.GetResourceRequest{
		UserId:      params.UserID,
		Region:      c.conf.Region,
		ServiceName: c.conf.ServiceName,
		ResourceId:  params.ResourceID,
		Auth:        auth,
	}
	getRsp, err := c.bcmSdk.GetResource(ctx, getReq)
	if err != nil && getRsp.Code == "ResourceNotExistException" {
		return false, nil
	}
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get BCM Resource Fail,req:%+v, user:%s, response:%+v", getReq, getReq.Auth.IamUserId, getRsp)
		return false, err
	}
	return true, nil
}

// CreateAppResource will create app resource
func (c *component) CreateAppResource(ctx context.Context, params *CreateAppResourceParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return
	}

	exist, err := c.CheckAppResourceExist(ctx, &CheckAppResourceExistParams{
		UserID: params.UserID,
		AppID:  params.AppID,
	})
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	typeName := "Instance"
	if params.AppType == x1model.AppTypeCluster {
		typeName = "Cluster"
	}
	createReq := &bcm.CreateResourceRequest{
		UserId:      params.UserID,
		Region:      c.conf.Region,
		ServiceName: c.conf.ServiceName,
		ResourceId:  generateAppResourceID(params.AppID),
		TypeName:    typeName,
		Auth:        auth,
		Properties: []bcm.Property{
			{
				Name:  "ClusterShowId",
				Value: params.AppID,
			},
		},
		Identifiers: []bcm.Identifier{
			{
				Name:  "ClusterId",
				Value: cast.ToString(params.AppShortID),
			},
		}}
	createRsp, err := c.bcmSdk.CreateResource(ctx, createReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get BCM Resource Fail,req:%+v, user:%s, response:%+v", createReq, createReq.Auth.IamUserId, createRsp)
		return
	}
	return
}

// CreateClusterResource will create cluster resource
func (c *component) CreateClusterResource(ctx context.Context, params *CreateClusterResourceParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return
	}

	exist, err := c.CheckClusterResourceExist(ctx, &CheckClusterResourceExistParams{
		UserID:   params.UserID,
		AppID:    params.AppID,
		HashName: params.HashName,
		AppType:  params.AppType,
	})
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	typeName := "Instance"
	createReq := &bcm.CreateResourceRequest{
		UserId:      params.UserID,
		Region:      c.conf.Region,
		ServiceName: c.conf.ServiceName,
		ResourceId:  generateClusterResourceID(params.AppID, params.HashName, params.AppType),
		TypeName:    typeName,
		Auth:        auth,
		Properties: []bcm.Property{
			{
				Name:  "ClusterShowId",
				Value: params.AppID,
			},
			{
				Name:  "NodeName",
				Value: generateClusterNodeName(params.AppID, params.HashName, params.AppType),
			},
		},
		Identifiers: []bcm.Identifier{
			{
				Name:  "ClusterId",
				Value: cast.ToString(params.AppShortID),
			},
			{
				Name:  "NodeId",
				Value: params.HashID,
			},
		}}
	createRsp, err := c.bcmSdk.CreateResource(ctx, createReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get BCM Resource Fail,req:%+v, user:%s, response:%+v", createReq, createReq.Auth.IamUserId, createRsp)
		return
	}
	return
}

// CreateNodeResource will create node resource
func (c *component) CreateNodeResource(ctx context.Context, params *CreateNodeResourceParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return
	}

	exist, err := c.CheckNodeResourceExist(ctx, &CheckNodeResourceExistParams{
		UserID:    params.UserID,
		AppID:     params.AppID,
		NodeFixID: params.NodeFixID,
	})
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	typeName := "Instance"
	createReq := &bcm.CreateResourceRequest{
		UserId:      params.UserID,
		Region:      c.conf.Region,
		ServiceName: c.conf.ServiceName,
		ResourceId:  generateNodeResourceID(params.AppID, params.NodeFixID),
		TypeName:    typeName,
		Auth:        auth,
		Properties: []bcm.Property{
			{
				Name:  "ClusterShowId",
				Value: params.AppID,
			},
			{
				Name:  "NodeName",
				Value: generateNodeNodeName(params.NodeFixID),
			},
		},
		Identifiers: []bcm.Identifier{
			{
				Name:  "ClusterId",
				Value: cast.ToString(params.AppShortID),
			},
			{
				Name:  "NodeId",
				Value: params.HashID,
			},
		}}
	createRsp, err := c.bcmSdk.CreateResource(ctx, createReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get BCM Resource Fail,req:%+v, user:%s, response:%+v", createReq, createReq.Auth.IamUserId, createRsp)
		return
	}
	return
}

// CreateSyncFlowAppResource will create sync flow app resource
func (c *component) CreateSyncFlowAppResource(ctx context.Context, params *CreateSyncFlowAppResourceParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return
	}

	resourceID := generateSyncFlowAppID(params.AppID, params.TargetBlb, params.TargetPort)
	exist, err := c.CheckResourceExist(ctx, &CheckResourceExistParams{
		UserID:     params.UserID,
		ResourceID: resourceID,
	})
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	typeName := "Instance"
	createReq := &bcm.CreateResourceRequest{
		UserId:      params.UserID,
		Region:      c.conf.Region,
		ServiceName: c.conf.ServiceName,
		ResourceId:  resourceID,
		TypeName:    typeName,
		Auth:        auth,
		Properties: []bcm.Property{
			{
				Name:  "ClusterShowId",
				Value: resourceID,
			},
		},
		Identifiers: []bcm.Identifier{
			{
				Name:  "ClusterId",
				Value: cast.ToString(params.AppShortID),
			},
		}}
	createRsp, err := c.bcmSdk.CreateResource(ctx, createReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get BCM Resource Fail,req:%+v, user:%s, response:%+v", createReq, createReq.Auth.IamUserId, createRsp)
		return
	}
	return
}

// CreateSyncFlowClusterResource will create cluster resource
func (c *component) CreateSyncFlowClusterResource(ctx context.Context, params *CreateSyncFlowClusterResourceParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return
	}

	resourceID := generateSyncFlowClusterResourceID(params.AppID, params.TargetBlb,
		params.HashName, params.AppType, params.TargetPort)
	exist, err := c.CheckResourceExist(ctx, &CheckResourceExistParams{
		UserID:     params.UserID,
		ResourceID: resourceID,
	})
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	typeName := "Instance"
	createReq := &bcm.CreateResourceRequest{
		UserId:      params.UserID,
		Region:      c.conf.Region,
		ServiceName: c.conf.ServiceName,
		ResourceId:  resourceID,
		TypeName:    typeName,
		Auth:        auth,
		Properties: []bcm.Property{
			{
				Name:  "ClusterShowId",
				Value: generateSyncFlowAppID(params.AppID, params.TargetBlb, params.TargetPort),
			},
			{
				Name:  "NodeName",
				Value: generateClusterNodeName(params.AppID, params.HashName, params.AppType),
			},
		},
		Identifiers: []bcm.Identifier{
			{
				Name:  "ClusterId",
				Value: cast.ToString(params.AppShortID),
			},
			{
				Name:  "NodeId",
				Value: params.HashID,
			},
		}}
	createRsp, err := c.bcmSdk.CreateResource(ctx, createReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get BCM Resource Fail,req:%+v, user:%s, response:%+v", createReq, createReq.Auth.IamUserId, createRsp)
		return
	}
	return
}

// DeleteSyncFlowAppResourceIfExisted  will delete bcm sync flow app resource if exist
func (c *component) DeleteSyncFlowAppResourceIfExisted(ctx context.Context, params *DeleteSyncFlowAppResourceParams) (err error) {
	return c.DeleteResourceIfExisted(ctx, &DeleteResourceIfExistedParams{
		UserID:     params.UserID,
		ResourceID: generateSyncFlowAppID(params.AppID, params.TargetBlb, params.TargetPort),
	})
}

// DeleteSyncFlowClusterResourceIfExisted  will delete bcm sync flow cluster resource if exist
func (c *component) DeleteSyncFlowClusterResourceIfExisted(ctx context.Context, params *DeleteSyncFlowClusterResourceParams) (err error) {
	return c.DeleteResourceIfExisted(ctx, &DeleteResourceIfExistedParams{
		UserID: params.UserID,
		ResourceID: generateSyncFlowClusterResourceID(params.AppID, params.TargetBlb,
			params.HashName, params.AppType, params.TargetPort),
	})
}

// GetGroupAndPreCheck 获取实例组并预检查
func (c *component) GetGroupAndPreCheck(ctx context.Context, params *operateWithGroupParams, auth *common.Authentication) ([]bcm.InstanceGroupItem, error) {

	//获取所有实例组信息
	groups := make([]bcm.InstanceGroupItem, 0)
	failIDs := make([]int64, 0)
	for _, gID := range params.GroupIDs {
		rsp, err := c.bcmSdk.GetInstanceGroup(ctx, &bcm.GetInstanceGroupRequest{
			UserId:          params.UserID,
			ServiceName:     c.conf.ServiceName,
			Region:          c.conf.Region,
			InstanceGroupID: gID,
			Auth:            auth,
		})
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "Get InstanceGroup Fail",
				logit.Int64("groupID", gID), logit.Error("error", err))
			failIDs = append(failIDs, gID)
		} else {
			groups = append(groups, rsp.InstanceGroupItem)
		}
	}

	//如果存在实例组信息获取失败的实例组
	//不允许忽略失败则直接返回
	//允许忽略失败的实例组，则判断实例组是否真的不存在
	if len(failIDs) > 0 {
		if !params.IgnoreErr {
			return groups, fmt.Errorf("GetInstanceGroup Fail, ids:%s", base_utils.Format(failIDs))
		}
		if err := c.CheckGroupNotExist(ctx, &ListInstanceGroupParams{
			UserID:   params.UserID,
			TypeName: params.TypeName,
		}, failIDs); err != nil {
			logger.ComponentLogger.Warning(ctx, "CheckGroupNotExist Fail",
				logit.String("groupIDs", base_utils.Format(failIDs)), logit.Error("error", err))
			return groups, err
		}
	}
	for _, group := range groups {
		if group.TypeName != params.TypeName {
			logger.ComponentLogger.Warning(ctx, "Get InstanceGroup Fail",
				logit.String("ParamTypeName", params.TypeName), logit.String("group", base_utils.Format(group)))
			return groups, fmt.Errorf("GroupType not match")
		}
	}
	return groups, nil
}

// OperateWithGroup
func (c *component) OperateWithGroup(ctx context.Context, params *operateWithGroupParams) error {

	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}

	groups, err := c.GetGroupAndPreCheck(ctx, params, auth)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "GetGroupAndPreCheck Fail, userid:%s, err:%s", params.UserID, err.Error())
		return err
	}

	monitorResources := make([]*bcm.MonitorResource, len(params.ResourceIDs))
	for i, resourceID := range params.ResourceIDs {
		monitorResources[i] = &bcm.MonitorResource{
			UserId:      params.UserID,
			Region:      c.conf.Region,
			ServiceName: c.conf.ServiceName,
			TypeName:    params.TypeName,
			ResourceId:  resourceID,
		}
	}
	for _, group := range groups {
		requestParam := &bcm.OperateInstanceInGroupRequest{
			UserId:          params.UserID,
			ServiceName:     c.conf.ServiceName,
			Region:          c.conf.Region,
			Auth:            auth,
			InstanceGroupID: group.Id,
			Name:            group.Name,
			TypeName:        group.TypeName,
			ResourceIdList:  monitorResources,
		}
		if params.Action == GroupInstanceActionAdd {
			if _, err := c.bcmSdk.AddInstanceToGroup(ctx, requestParam); err != nil {
				logger.ComponentLogger.Error(ctx, "Add Instance Fail", logit.Error("error", err))
				return err
			}
		} else if params.Action == GroupInstanceActionRemove {
			if _, err := c.bcmSdk.RemoveInstanceFromGroup(ctx, requestParam); err != nil {
				logger.ComponentLogger.Error(ctx, "Add Instance Fail", logit.Error("error", err))
				return err
			}
		} else {
			return fmt.Errorf("unknown action")
		}
	}
	return nil
}

// OperateAppWithGroup app operate params
func (c *component) OperateAppWithGroup(ctx context.Context, params *OperateAppWithGroupParams) (err error) {
	param := &operateWithGroupParams{
		UserID:      params.UserID,
		TypeName:    params.TypeName,
		GroupIDs:    params.GroupIDs,
		ResourceIDs: []string{generateAppResourceIdForGroup(params.AppID)},
		Action:      params.Action,
		IgnoreErr:   params.IgnoreErr,
	}
	return c.OperateWithGroup(ctx, param)
}

// OperateClusterWithGroup app operate params
func (c *component) OperateClusterWithGroup(ctx context.Context, params *OperateClusterWithGroupParams) (err error) {
	ids := make([]string, len(params.HashNames))
	for i, hashName := range params.HashNames {
		ids[i] = generateClusterResourceIdForGroup(params.AppID, hashName, params.AppType)
	}
	param := &operateWithGroupParams{
		UserID:      params.UserID,
		TypeName:    params.TypeName,
		GroupIDs:    params.GroupIDs,
		ResourceIDs: ids,
		Action:      params.Action,
		IgnoreErr:   params.IgnoreErr,
	}
	return c.OperateWithGroup(ctx, param)
}

// OperateAppWithGroupParams app operate params
func (c *component) OperateNodeWithGroup(ctx context.Context, params *OperateNodeWithGroupParams) (err error) {
	ids := make([]string, len(params.NodeFixIDs))
	for i, nodeFixID := range params.NodeFixIDs {
		ids[i] = generateNodeResourceIdForGroup(params.AppID, nodeFixID)
	}
	param := &operateWithGroupParams{
		UserID:      params.UserID,
		TypeName:    params.TypeName,
		GroupIDs:    params.GroupIDs,
		ResourceIDs: ids,
		Action:      params.Action,
		IgnoreErr:   params.IgnoreErr,
	}
	return c.OperateWithGroup(ctx, param)
}

// ListInstanceGroup 列出实例组
func (c *component) ListInstanceGroup(ctx context.Context, params *ListInstanceGroupParams) (*ListInstanceGroupResponse, error) {

	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}
	instanceGroups := make([]*InstanceGroupItem, 0)
	requestParam := &bcm.ListInstanceGroupsRequest{
		UserId:      params.UserID,
		ServiceName: c.conf.ServiceName,
		Region:      c.conf.Region,
		PageNo:      1,
		PageSize:    c.conf.ListGroupPageSize,
		Auth:        auth,
	}
	for true {
		rsp, err := c.bcmSdk.ListInstanceGroups(ctx, requestParam)
		if err != nil {
			logger.ComponentLogger.Error(ctx, "List Instance Group Fail", logit.Error("error", err))
			return nil, err
		}
		for _, item := range rsp.Result {
			if item.TypeName != params.TypeName {
				continue
			}
			instanceGroups = append(instanceGroups, &InstanceGroupItem{
				ID:   item.Id,
				Name: item.Name,
			})
		}
		if rsp.TotalCount <= c.conf.ListGroupPageSize*requestParam.PageNo {
			break
		}
		requestParam.PageNo++
	}
	return &ListInstanceGroupResponse{
		InstanceGroups: instanceGroups,
	}, nil
}

// CheckGroupNotExist 检查实例组是否存在
func (c *component) CheckGroupNotExist(ctx context.Context, params *ListInstanceGroupParams, ids []int64) error {

	rsp, err := c.ListInstanceGroup(ctx, params)
	if err != nil {
		logger.ComponentLogger.Error(ctx, "List Instance Group Fail", logit.Error("error", err))
		return err
	}
	if len(rsp.InstanceGroups) == 0 {
		return nil
	}
	for _, id := range ids {
		for _, group := range rsp.InstanceGroups {
			if id == group.ID {
				return fmt.Errorf("group id:%d,name:%s exist", id, group.Name)
			}
		}
	}
	return nil
}

// QuerySampleCount 获取指定周期内监控指标样本数
func (c *component) QuerySampleCount(ctx context.Context, params *QuerySampleCountParams) ([]*SampleCountData, error) {

	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}
	dimensions := "ClusterId:" + params.AppID
	if params.NodeFixID != "" {
		dimensions += ";NodeId:" + params.NodeFixID
	}

	rsp, err := c.bcmSdk.QueryMetricData(ctx, &bcm.QueryMetricDataRequest{
		UserId:         params.UserID,
		ServiceName:    c.conf.ServiceName,
		Region:         c.conf.Region,
		Auth:           auth,
		Statistics:     []string{staticsSampleCount},
		MetricName:     sampleCountMetricName,
		StartTime:      params.StartTime,
		EndTime:        params.EndTime,
		PeriodInSecond: params.PeriodInSecond,
		Dimensions:     dimensions,
	})
	if err != nil {
		logger.ComponentLogger.Error(ctx, "Query Metric Data Fail", logit.Error("error", err))
		return nil, err
	}
	data := make([]*SampleCountData, len(rsp.DataPoints))
	for index, item := range rsp.DataPoints {
		data[index] = &SampleCountData{
			SampleCount: item.SampleCount,
			Timestamp:   item.Timestamp,
		}
	}
	return data, nil
}
