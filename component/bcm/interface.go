/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/02/20 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file interface.go
 * <AUTHOR>
 * @date 2023/02/20 15:24:35
 * @brief
 *
 **/

package bcm

import (
	"fmt"
	"time"
)

// PushEventParams definition
type PushEventParams struct {
	UserID string `json:"userId"`
	Info   *Info  `json:"-"`
}

type Info struct {
	ResourceID   string `json:"resourceId"`
	ResourceType string `json:"resourceType"`
	EventType    string `json:"eventType"`
	EventLevel   string `json:"eventLevel"`
	EventAlias   string `json:"eventAlias"`
	Content      string `json:"content"`
}

type PushEventError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	EventID string `json:"eventId"`
}

func (e *PushEventError) Error() string {
	return fmt.Errorf("code: %d, message: %s, eventId: %s", e.Code, e.Message, e.EventID).Error()
}

type DeleteAppResourceIfExistedParams struct {
	UserID string `json:"userId"`
	AppID  string `json:"appId"`
}

type DeleteClusterResourceIfExistedParams struct {
	UserID   string `json:"userId"`
	AppID    string `json:"appId"`
	HashName string `json:"hashName"`
	AppType  string `json:"appType"`
}

type DeleteNodeResourceIfExistedParams struct {
	UserID    string `json:"userId"`
	AppID     string `json:"appId"`
	NodeFixID string `json:"nodeFixId"`
}

type DeleteResourceIfExistedParams struct {
	UserID     string `json:"userId"`
	ResourceID string `json:"resourceID"`
}

type CheckAppResourceExistParams struct {
	UserID string `json:"userId"`
	AppID  string `json:"appId"`
}

type CheckClusterResourceExistParams struct {
	UserID   string `json:"userId"`
	AppID    string `json:"appId"`
	HashName string `json:"hashName"`
	AppType  string `json:"appType"`
}

type CheckNodeResourceExistParams struct {
	UserID    string `json:"userId"`
	AppID     string `json:"appId"`
	NodeFixID string `json:"nodeFixId"`
}

type CheckResourceExistParams struct {
	UserID     string `json:"userId"`
	ResourceID string `json:"resourceID"`
}

type CreateAppResourceParams struct {
	UserID     string `json:"userId"`
	AppID      string `json:"appId"`
	AppShortID int64  `json:"appShortID"`
	AppType    string `json:"appType"`
}

type CreateClusterResourceParams struct {
	UserID     string `json:"userId"`
	AppID      string `json:"appId"`
	AppShortID int64  `json:"appShortID"`
	AppType    string `json:"appType"`
	HashName   string `json:"hashName"`
	HashID     string `json:"hashID"`
}

type CreateNodeResourceParams struct {
	UserID     string `json:"userId"`
	AppID      string `json:"appId"`
	AppShortID int64  `json:"appShortID"`
	HashID     string `json:"hashID"`
	NodeFixID  string `json:"nodeFixId"`
}

type CreateSyncFlowAppResourceParams struct {
	UserID     string `json:"userId"`
	AppID      string `json:"appId"`
	AppShortID int64  `json:"appShortID"`
	TargetBlb  string `json:"targetBlb"`
	TargetPort int64  `json:"targetPort"`
}

type DeleteSyncFlowAppResourceParams struct {
	UserID     string `json:"userId"`
	AppID      string `json:"appId"`
	TargetBlb  string `json:"targetBlb"`
	TargetPort int64  `json:"targetPort"`
}

type CreateSyncFlowClusterResourceParams struct {
	UserID     string `json:"userId"`
	AppID      string `json:"appId"`
	AppShortID int64  `json:"appShortID"`
	TargetBlb  string `json:"targetBlb"`
	TargetPort int64  `json:"targetPort"`
	AppType    string `json:"appType"`
	HashName   string `json:"hashName"`
	HashID     string `json:"hashID"`
}

type DeleteSyncFlowClusterResourceParams struct {
	UserID     string `json:"userId"`
	AppID      string `json:"appId"`
	TargetBlb  string `json:"targetBlb"`
	TargetPort int64  `json:"targetPort"`
	AppType    string `json:"appType"`
	HashName   string `json:"hashName"`
}

type ListInstanceGroupParams struct {
	UserID   string `json:"userId"`
	TypeName string `json:"typeName"`
}

type InstanceGroupItem struct {
	ID   int64
	Name string
}

type ListInstanceGroupResponse struct {
	InstanceGroups []*InstanceGroupItem
}

type OperateAppWithGroupParams struct {
	UserID    string  `json:"userId"`
	AppID     string  `json:"appId"`
	TypeName  string  `json:"typeName"`
	GroupIDs  []int64 `json:"groupIds"`
	Action    string  `json:"action"`
	IgnoreErr bool    `json:"ignoreErr"` //group不存在时，是否忽略错误
}

type OperateClusterWithGroupParams struct {
	UserID    string   `json:"userId"`
	AppID     string   `json:"appId"`
	TypeName  string   `json:"typeName"`
	GroupIDs  []int64  `json:"groupIds"`
	HashNames []string `json:"hashNames"`
	AppType   string   `json:"appType"`
	Action    string   `json:"action"`
	IgnoreErr bool     `json:"ignoreErr"` //group不存在时，是否忽略错误
}

type OperateNodeWithGroupParams struct {
	UserID     string   `json:"userId"`
	AppID      string   `json:"appId"`
	TypeName   string   `json:"typeName"`
	GroupIDs   []int64  `json:"groupIds"`
	NodeFixIDs []string `json:"nodeFixIDs"`
	Action     string   `json:"action"`
	IgnoreErr  bool     `json:"ignoreErr"` //group不存在时，是否忽略错误
}

type operateWithGroupParams struct {
	UserID      string   `json:"userId"`
	TypeName    string   `json:"typeName"`
	GroupIDs    []int64  `json:"groupIds"`
	ResourceIDs []string `json:"resourceIDs"`
	Action      string   `json:"action"`
	IgnoreErr   bool     `json:"ignoreErr"` //group不存在时，是否忽略错误
}

type QuerySampleCountParams struct {
	UserID         string    `json:"userId"`
	AppID          string    `json:"appID"`
	NodeFixID      string    `json:"nodeFixId"`
	StartTime      time.Time `json:"startTime"`
	EndTime        time.Time `json:"endTime"`
	PeriodInSecond int       `json:"periodInSecond"`
}

type SampleCountData struct {
	SampleCount int       `json:"sampleCount"`
	Timestamp   time.Time `json:"timestamp"`
}
