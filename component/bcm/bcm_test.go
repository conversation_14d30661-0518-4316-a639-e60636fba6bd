/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/02/20 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file bcm_test.go
 * <AUTHOR>
 * @date 2023/02/20 16:36:28
 * @brief
 *
 **/

package bcm

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	. "github.com/agiledragon/gomonkey/v2"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcm"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const (
	TestIamUserId = "4093ae6b9e48423e89c69916bc6d5d5e"
)

//func TestBcmPushEvent(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bcmOp := Instance()
//	info := &Info{
//		ResourceID:   "scs-bj-xkfslkdgdsfs",
//		ResourceType: "instance",
//		EventType:    "failOverStart",
//		EventLevel:   "NOTICE",
//		EventAlias:   "failOverStart",
//		Content:      "info",
//	}
//
//	err := bcmOp.PushEvents(ctx, &PushEventParams{
//		UserID: "a11602e1c4c24e59865b9bb9209ff16d",
//		Info:   info,
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		return
//	}
//
//	fmt.Println("success")
//}

//func Test_generateResourceID(t *testing.T) {
//	type args struct {
//		AppID     string
//		ClusterID string
//		NodeFixID string
//	}
//	tests := []struct {
//		name string
//		args args
//		want string
//	}{
//		{"app", args{
//			AppID:     "app",
//			ClusterID: "",
//			NodeFixID: "",
//		}, "app"},
//		{"app-cluster", args{
//			AppID:     "app",
//			ClusterID: "cluster",
//			NodeFixID: "",
//		}, "app___cluster"},
//		{"app-node", args{
//			AppID:     "app",
//			ClusterID: "",
//			NodeFixID: "node",
//		}, "app___node"},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if got := generateResourceID(tt.args.AppID, tt.args.ClusterID, tt.args.NodeFixID); got != tt.want {
//				t.Errorf("generateResourceID() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//func Test_component_DeleteResourceIfExisted(t *testing.T) {
//
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	bcmOp := Instance()
//
//	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
//		return nil, errors.New("test get auth fail ")
//	})
//	err := bcmOp.DeleteResourceIfExisted(ctx, &DeleteResourceIfExistedParams{
//		UserID:    TestIamUserId,
//		AppID:     "",
//		ClusterID: "",
//		NodeFixID: "",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	patches1.Reset()
//
//	//if err != nil {
//	//	t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
//	//}
//	err = bcmOp.DeleteResourceIfExisted(ctx, &DeleteResourceIfExistedParams{
//		UserID:    TestIamUserId,
//		AppID:     "not_exist",
//		ClusterID: "",
//		NodeFixID: "",
//	})
//	if err != nil {
//		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
//	}
//
//	auth, err := compo_utils.GetOpenapiAuth(ctx, TestIamUserId)
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//	_, err = bcmOp.bcmSdk.CreateResource(ctx, &bcm.CreateResourceRequest{Auth: auth,
//		UserId:      TestIamUserId,
//		Region:      "bj",
//		ServiceName: "BCE_SCS",
//		ResourceId:  "sandbox___sandbox_comp_test_0",
//		TypeName:    "Instance",
//	})
//	if err != nil {
//		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
//	}
//	err = bcmOp.DeleteResourceIfExisted(ctx, &DeleteResourceIfExistedParams{
//		UserID:    TestIamUserId,
//		AppID:     "sandbox",
//		ClusterID: "sandbox_comp_test_0",
//		NodeFixID: "",
//	})
//	if err != nil {
//		t.Errorf("[%s] test success request fail: %s\n", t.Name(), err.Error())
//	}
//}

func Test_generateAppResourceID(t *testing.T) {
	type args struct {
		appID string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test generate success",
			args{
				appID: "test-app-id",
			},
			"test-app-id",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := generateAppResourceID(tt.args.appID)
			if got != tt.want {
				t.Errorf("generateAppResourceID() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_generateClusterNodeName(t *testing.T) {
	type args struct {
		appID    string
		hashName string
		appType  string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			"test standalone always end with 0",
			args{
				appID:    "test-app-id",
				hashName: "hashname_1",
				appType:  x1model.AppTypeStandalone,
			},
			"test-app-id-0",
		},
		{
			"test standalone always end with 0",
			args{
				appID:    "test-app-id",
				hashName: "hashname_0",
				appType:  x1model.AppTypeStandalone,
			},
			"test-app-id-0",
		},
		{
			"test cluster end with 1",
			args{
				appID:    "test-app-id",
				hashName: "hashname_1",
				appType:  x1model.AppTypeCluster,
			},
			"test-app-id-1",
		},
		{
			"test cluster end with 0",
			args{
				appID:    "test-app-id",
				hashName: "hashname_0",
				appType:  x1model.AppTypeCluster,
			},
			"test-app-id-0",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := generateClusterNodeName(tt.args.appID, tt.args.hashName, tt.args.appType)
			if got != tt.want {
				t.Errorf("generateClusterNodeName() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_generateClusterResourceID(t *testing.T) {
	type args struct {
		appID    string
		hashName string
		appType  string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			"test generate resource id",
			args{
				appID:    "test-app-id",
				hashName: "hashname_1",
				appType:  x1model.AppTypeStandalone,
			},
			"test-app-id___test-app-id-0",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := generateClusterResourceID(tt.args.appID, tt.args.hashName, tt.args.appType); got != tt.want {
				t.Errorf("generateClusterResourceID() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_generateNodeNodeName(t *testing.T) {
	type args struct {
		nodeFixID string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			"test normal",
			args{"node_fix_id"},
			"node_fix_id",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := generateNodeNodeName(tt.args.nodeFixID); got != tt.want {
				t.Errorf("generateNodeNodeName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_generateNodeResourceID(t *testing.T) {
	type args struct {
		appID     string
		nodeFixID string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			"test generate resource id",
			args{
				appID:     "test-app-id",
				nodeFixID: "node_fix_id",
			},
			"test-app-id___node_fix_id",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := generateNodeResourceID(tt.args.appID, tt.args.nodeFixID); got != tt.want {
				t.Errorf("generateNodeResourceID() = %v, want %v", got, tt.want)
			}
		})
	}
}

//func Test_component_CreateAndDeleteAppResourceIfExistedForStandalone(t *testing.T) {
//
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	bcmOp := Instance()
//
//	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
//		return nil, errors.New("test get auth fail ")
//	})
//	err := bcmOp.CreateAppResource(ctx, &CreateAppResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "",
//		AppShortID: 0,
//		AppType:    "",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	_, err = bcmOp.CheckAppResourceExist(ctx, &CheckAppResourceExistParams{
//		UserID: TestIamUserId,
//		AppID:  "",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	err = bcmOp.DeleteAppResourceIfExisted(ctx, &DeleteAppResourceIfExistedParams{
//		UserID: TestIamUserId,
//		AppID:  "",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	patches1.Reset()
//
//	exist, err := bcmOp.CheckAppResourceExist(ctx, &CheckAppResourceExistParams{
//		UserID: TestIamUserId,
//		AppID:  "not-exist-app-id",
//	})
//	if err != nil {
//		t.Errorf("[%s] check not exist app resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == true {
//		t.Errorf("[%s] check not exist app resource fail\n", t.Name())
//	}
//	err = bcmOp.CreateAppResource(ctx, &CreateAppResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "create-app-id",
//		AppShortID: 123456,
//		AppType:    x1model.AppTypeStandalone,
//	})
//	if err != nil {
//		t.Errorf("[%s] create standalone app resource fail: %s\n", t.Name(), err.Error())
//	}
//	exist, err = bcmOp.CheckAppResourceExist(ctx, &CheckAppResourceExistParams{
//		UserID: TestIamUserId,
//		AppID:  "create-app-id",
//	})
//	if err != nil {
//		t.Errorf("[%s] check exist app resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == false {
//		t.Errorf("[%s] check exist app resource fail\n", t.Name())
//	}
//	auth, err := compo_utils.GetOpenapiAuth(ctx, TestIamUserId)
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//	resource, err := bcmOp.bcmSdk.GetResource(ctx, &bcm.GetResourceRequest{Auth: auth,
//		UserId:      TestIamUserId,
//		Region:      "bj",
//		ServiceName: "BCE_SCS",
//		ResourceId:  "create-app-id",
//	})
//	fmt.Printf("%+v\n", resource)
//	if resource.TypeName != "Instance" {
//		t.Errorf("[%s] standalone app resource typename is wrong: %s\n", t.Name(), err.Error())
//	}
//	err = bcmOp.DeleteAppResourceIfExisted(ctx, &DeleteAppResourceIfExistedParams{
//		UserID: TestIamUserId,
//		AppID:  "create-app-id",
//	})
//	if err != nil {
//		t.Errorf("[%s] delete standalone app resource fail: %s\n", t.Name(), err.Error())
//	}
//	exist, err = bcmOp.CheckAppResourceExist(ctx, &CheckAppResourceExistParams{
//		UserID: TestIamUserId,
//		AppID:  "create-app-id",
//	})
//	if err != nil {
//		t.Errorf("[%s] check deleted app resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == true {
//		t.Errorf("[%s] check deleted app resource fail\n", t.Name())
//	}
//}

//
//func Test_component_CreateAndDeleteAppResourceIfExistedForCluster(t *testing.T) {
//
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	bcmOp := Instance()
//
//	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
//		return nil, errors.New("test get auth fail ")
//	})
//	err := bcmOp.CreateAppResource(ctx, &CreateAppResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "",
//		AppShortID: 0,
//		AppType:    "",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	_, err = bcmOp.CheckAppResourceExist(ctx, &CheckAppResourceExistParams{
//		UserID: TestIamUserId,
//		AppID:  "",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	err = bcmOp.DeleteAppResourceIfExisted(ctx, &DeleteAppResourceIfExistedParams{
//		UserID: TestIamUserId,
//		AppID:  "",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	patches1.Reset()
//
//	exist, err := bcmOp.CheckAppResourceExist(ctx, &CheckAppResourceExistParams{
//		UserID: TestIamUserId,
//		AppID:  "not-exist-app-id",
//	})
//	if err != nil {
//		t.Errorf("[%s] check not exist app resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == true {
//		t.Errorf("[%s] check not exist app resource fail\n", t.Name())
//	}
//	err = bcmOp.CreateAppResource(ctx, &CreateAppResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "create-app-id",
//		AppShortID: 123456,
//		AppType:    x1model.AppTypeCluster,
//	})
//	if err != nil {
//		t.Errorf("[%s] create cluster app resource fail: %s\n", t.Name(), err.Error())
//	}
//	exist, err = bcmOp.CheckAppResourceExist(ctx, &CheckAppResourceExistParams{
//		UserID: TestIamUserId,
//		AppID:  "create-app-id",
//	})
//	if err != nil {
//		t.Errorf("[%s] check exist app resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == false {
//		t.Errorf("[%s]  check exist app resource fail\n", t.Name())
//	}
//	auth, err := compo_utils.GetOpenapiAuth(ctx, TestIamUserId)
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//	resource, err := bcmOp.bcmSdk.GetResource(ctx, &bcm.GetResourceRequest{Auth: auth,
//		UserId:      TestIamUserId,
//		Region:      "bj",
//		ServiceName: "BCE_SCS",
//		ResourceId:  "create-app-id",
//	})
//	fmt.Printf("%+v\n", resource)
//	if resource.TypeName != "Cluster" {
//		t.Errorf("[%s] cluster app resource typename is wrong: %s\n", t.Name(), err.Error())
//	}
//	err = bcmOp.DeleteAppResourceIfExisted(ctx, &DeleteAppResourceIfExistedParams{
//		UserID: TestIamUserId,
//		AppID:  "create-app-id",
//	})
//	if err != nil {
//		t.Errorf("[%s] delete cluster app resource fail: %s\n", t.Name(), err.Error())
//	}
//	exist, err = bcmOp.CheckAppResourceExist(ctx, &CheckAppResourceExistParams{
//		UserID: TestIamUserId,
//		AppID:  "create-app-id",
//	})
//	if err != nil {
//		t.Errorf("[%s] check deleted app resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == true {
//		t.Errorf("[%s] check deleted app resource fail\n", t.Name())
//	}
//}
//
//func Test_component_CreateAndDeleteNodeResourceIfExisted(t *testing.T) {
//
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	bcmOp := Instance()
//
//	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
//		return nil, errors.New("test get auth fail ")
//	})
//	err := bcmOp.CreateNodeResource(ctx, &CreateNodeResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "",
//		AppShortID: 0,
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	_, err = bcmOp.CheckNodeResourceExist(ctx, &CheckNodeResourceExistParams{
//		UserID: TestIamUserId,
//		AppID:  "",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	err = bcmOp.DeleteNodeResourceIfExisted(ctx, &DeleteNodeResourceIfExistedParams{
//		UserID: TestIamUserId,
//		AppID:  "",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	patches1.Reset()
//
//	exist, err := bcmOp.CheckNodeResourceExist(ctx, &CheckNodeResourceExistParams{
//		UserID:    TestIamUserId,
//		AppID:     "not-exist-node-id",
//		NodeFixID: "node_fix_id",
//	})
//	if err != nil {
//		t.Errorf("[%s] check not exist app resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == true {
//		t.Errorf("[%s] check not exist app resource fail\n", t.Name())
//	}
//	err = bcmOp.CreateNodeResource(ctx, &CreateNodeResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "create-node-id",
//		AppShortID: 123456,
//		HashID:     "hashid",
//		NodeFixID:  "node_fix_id",
//	})
//	if err != nil {
//		t.Errorf("[%s] create node resource fail: %s\n", t.Name(), err.Error())
//	}
//	exist, err = bcmOp.CheckNodeResourceExist(ctx, &CheckNodeResourceExistParams{
//		UserID:    TestIamUserId,
//		AppID:     "create-node-id",
//		NodeFixID: "node_fix_id",
//	})
//	if err != nil {
//		t.Errorf("[%s] check exist app resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == false {
//		t.Errorf("[%s] check exist app resource fail\n", t.Name())
//	}
//	auth, err := compo_utils.GetOpenapiAuth(ctx, TestIamUserId)
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//	resource, err := bcmOp.bcmSdk.GetResource(ctx, &bcm.GetResourceRequest{Auth: auth,
//		UserId:      TestIamUserId,
//		Region:      "bj",
//		ServiceName: "BCE_SCS",
//		ResourceId:  "create-node-id___node_fix_id",
//	})
//	if resource.TypeName != "Instance" {
//		t.Errorf("[%s] standalone app resource typename is wrong: %s\n", t.Name(), err.Error())
//	}
//	err = bcmOp.DeleteNodeResourceIfExisted(ctx, &DeleteNodeResourceIfExistedParams{
//		UserID:    TestIamUserId,
//		AppID:     "create-node-id",
//		NodeFixID: "node_fix_id",
//	})
//	if err != nil {
//		t.Errorf("[%s] delete node resource fail: %s\n", t.Name(), err.Error())
//	}
//	exist, err = bcmOp.CheckNodeResourceExist(ctx, &CheckNodeResourceExistParams{
//		UserID:    TestIamUserId,
//		AppID:     "create-node-id",
//		NodeFixID: "node_fix_id",
//	})
//	if err != nil {
//		t.Errorf("[%s] check deleted app resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == true {
//		t.Errorf("[%s] check deleted app resource fail\n", t.Name())
//	}
//}
//
//func Test_component_CreateAndDeleteClusterResourceIfExisted(t *testing.T) {
//
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	bcmOp := Instance()
//
//	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
//		return nil, errors.New("test get auth fail ")
//	})
//	err := bcmOp.CreateClusterResource(ctx, &CreateClusterResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "",
//		AppShortID: 0,
//		AppType:    "",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	_, err = bcmOp.CheckClusterResourceExist(ctx, &CheckClusterResourceExistParams{
//		UserID: TestIamUserId,
//		AppID:  "",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	err = bcmOp.DeleteClusterResourceIfExisted(ctx, &DeleteClusterResourceIfExistedParams{
//		UserID: TestIamUserId,
//		AppID:  "",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	patches1.Reset()
//
//	exist, err := bcmOp.CheckClusterResourceExist(ctx, &CheckClusterResourceExistParams{
//		UserID:   TestIamUserId,
//		AppID:    "not-exist-cluster-id",
//		HashName: "hashname_1",
//		AppType:  x1model.AppTypeStandalone,
//	})
//	if err != nil {
//		t.Errorf("[%s] check not exist cluster resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == true {
//		t.Errorf("[%s] check not exist cluster resource fail\n", t.Name())
//	}
//	err = bcmOp.CreateClusterResource(ctx, &CreateClusterResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "create-cluster-id",
//		AppShortID: 123456,
//		AppType:    x1model.AppTypeStandalone,
//		HashName:   "hashname_0",
//		HashID:     "hashid",
//	})
//	if err != nil {
//		t.Errorf("[%s] create standalone cluster resource fail: %s\n", t.Name(), err.Error())
//	}
//	exist, err = bcmOp.CheckClusterResourceExist(ctx, &CheckClusterResourceExistParams{
//		UserID:   TestIamUserId,
//		AppID:    "create-cluster-id",
//		HashName: "hashname_0",
//		AppType:  x1model.AppTypeStandalone,
//	})
//	if err != nil {
//		t.Errorf("[%s] check exist cluster resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == false {
//		t.Errorf("[%s] check exist cluster resource fail\n", t.Name())
//	}
//	auth, err := compo_utils.GetOpenapiAuth(ctx, TestIamUserId)
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//	resource, err := bcmOp.bcmSdk.GetResource(ctx, &bcm.GetResourceRequest{Auth: auth,
//		UserId:      TestIamUserId,
//		Region:      "bj",
//		ServiceName: "BCE_SCS",
//		ResourceId:  "create-cluster-id___create-cluster-id-0",
//	})
//	if resource.TypeName != "Instance" {
//		t.Errorf("[%s] standalone app resource typename is wrong: %s\n", t.Name(), err.Error())
//	}
//	err = bcmOp.DeleteClusterResourceIfExisted(ctx, &DeleteClusterResourceIfExistedParams{
//		UserID:   TestIamUserId,
//		AppID:    "create-cluster-id",
//		HashName: "hashname_0",
//		AppType:  x1model.AppTypeStandalone,
//	})
//	if err != nil {
//		t.Errorf("[%s] delete standalone cluster resource fail: %s\n", t.Name(), err.Error())
//	}
//	exist, err = bcmOp.CheckClusterResourceExist(ctx, &CheckClusterResourceExistParams{
//		UserID:   TestIamUserId,
//		AppID:    "create-cluster-id",
//		HashName: "hashname_0",
//		AppType:  x1model.AppTypeStandalone,
//	})
//	if err != nil || exist == true {
//		t.Errorf("[%s] check deleted app resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == true {
//		t.Errorf("[%s] check deleted app resource fail\n", t.Name())
//	}
//}

//func Test_component_CreateAndDeleteSyncFlowAppResourceIfExisted(t *testing.T) {
//
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	bcmOp := Instance()
//
//	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
//		return nil, errors.New("test get auth fail ")
//	})
//	err := bcmOp.CreateSyncFlowAppResource(ctx, &CreateSyncFlowAppResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "create-app-id",
//		AppShortID: 123456,
//		TargetBlb:  "127.0.0.1",
//		TargetPort: 6379,
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	exist, err := bcmOp.CheckResourceExist(ctx, &CheckResourceExistParams{
//		UserID:     TestIamUserId,
//		ResourceID: "create-app-id-127.0.0.1[6379]",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	err = bcmOp.DeleteSyncFlowAppResourceIfExisted(ctx, &DeleteSyncFlowAppResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "create-app-id",
//		TargetBlb:  "127.0.0.1",
//		TargetPort: 6379,
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	patches1.Reset()
//
//	err = bcmOp.CreateSyncFlowAppResource(ctx, &CreateSyncFlowAppResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "create-app-id",
//		AppShortID: 123456,
//		TargetBlb:  "127.0.0.1",
//		TargetPort: 6379,
//	})
//	if err != nil {
//		t.Errorf("[%s] create cluster app resource fail: %s\n", t.Name(), err.Error())
//	}
//	exist, err = bcmOp.CheckResourceExist(ctx, &CheckResourceExistParams{
//		UserID:     TestIamUserId,
//		ResourceID: "create-app-id-127.0.0.1[6379]",
//	})
//	if err != nil {
//		t.Errorf("[%s] check exist app resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == false {
//		t.Errorf("[%s]  check exist app resource fail\n", t.Name())
//	}
//	auth, err := compo_utils.GetOpenapiAuth(ctx, TestIamUserId)
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//	resource, err := bcmOp.bcmSdk.GetResource(ctx, &bcm.GetResourceRequest{Auth: auth,
//		UserId:      TestIamUserId,
//		Region:      "bj",
//		ServiceName: "BCE_SCS",
//		ResourceId:  "create-app-id-127.0.0.1[6379]",
//	})
//	fmt.Printf("%+v\n", resource)
//	if resource.TypeName != "Instance" {
//		t.Errorf("[%s] cluster app resource typename is wrong: %s\n", t.Name(), err.Error())
//	}
//	err = bcmOp.DeleteSyncFlowAppResourceIfExisted(ctx, &DeleteSyncFlowAppResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "create-app-id",
//		TargetBlb:  "127.0.0.1",
//		TargetPort: 6379,
//	})
//	if err != nil {
//		t.Errorf("[%s] delete cluster app resource fail: %s\n", t.Name(), err.Error())
//	}
//	exist, err = bcmOp.CheckAppResourceExist(ctx, &CheckAppResourceExistParams{
//		UserID: TestIamUserId,
//		AppID:  "create-app-id-127.0.0.1[6379]",
//	})
//	if err != nil {
//		t.Errorf("[%s] check deleted app resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == true {
//		t.Errorf("[%s] check deleted app resource fail\n", t.Name())
//	}
//}
//
//func Test_component_CreateAndDeleteSyncFlowClusterResourceIfExisted(t *testing.T) {
//
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	bcmOp := Instance()
//
//	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
//		return nil, errors.New("test get auth fail ")
//	})
//	err := bcmOp.CreateSyncFlowClusterResource(ctx, &CreateSyncFlowClusterResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "create-app-id",
//		AppShortID: 123456,
//		TargetBlb:  "127.0.0.1",
//		TargetPort: 6379,
//		AppType:    "cluster",
//		HashName:   "abc_3",
//		HashID:     "hash_id",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	exist, err := bcmOp.CheckResourceExist(ctx, &CheckResourceExistParams{
//		UserID:     TestIamUserId,
//		ResourceID: "create-app-id-127.0.0.1[6379]___create-app-id-3",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	err = bcmOp.DeleteSyncFlowClusterResourceIfExisted(ctx, &DeleteSyncFlowClusterResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "create-app-id",
//		TargetBlb:  "127.0.0.1",
//		TargetPort: 6379,
//		AppType:    "cluster",
//		HashName:   "abc_3",
//	})
//	if err == nil || err.Error() != "test get auth fail " {
//		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
//	}
//	patches1.Reset()
//
//	err = bcmOp.CreateSyncFlowClusterResource(ctx, &CreateSyncFlowClusterResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "create-app-id",
//		AppShortID: 123456,
//		TargetBlb:  "127.0.0.1",
//		TargetPort: 6379,
//		AppType:    "cluster",
//		HashName:   "abc_3",
//		HashID:     "hash_id",
//	})
//	if err != nil {
//		t.Errorf("[%s] create cluster app resource fail: %s\n", t.Name(), err.Error())
//	}
//	exist, err = bcmOp.CheckResourceExist(ctx, &CheckResourceExistParams{
//		UserID:     TestIamUserId,
//		ResourceID: "create-app-id-127.0.0.1[6379]___create-app-id-3",
//	})
//	if err != nil {
//		t.Errorf("[%s] check exist app resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == false {
//		t.Errorf("[%s]  check exist app resource fail\n", t.Name())
//	}
//	auth, err := compo_utils.GetOpenapiAuth(ctx, TestIamUserId)
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//	resource, err := bcmOp.bcmSdk.GetResource(ctx, &bcm.GetResourceRequest{Auth: auth,
//		UserId:      TestIamUserId,
//		Region:      "bj",
//		ServiceName: "BCE_SCS",
//		ResourceId:  "create-app-id-127.0.0.1[6379]___create-app-id-3",
//	})
//	fmt.Printf("%+v\n", resource)
//	if resource.TypeName != "Instance" {
//		t.Errorf("[%s] cluster app resource typename is wrong: %s\n", t.Name(), err.Error())
//	}
//	err = bcmOp.DeleteSyncFlowClusterResourceIfExisted(ctx, &DeleteSyncFlowClusterResourceParams{
//		UserID:     TestIamUserId,
//		AppID:      "create-app-id",
//		TargetBlb:  "127.0.0.1",
//		TargetPort: 6379,
//		AppType:    "cluster",
//		HashName:   "abc_3",
//	})
//	if err != nil {
//		t.Errorf("[%s] delete cluster app resource fail: %s\n", t.Name(), err.Error())
//	}
//	exist, err = bcmOp.CheckResourceExist(ctx, &CheckResourceExistParams{
//		UserID:     TestIamUserId,
//		ResourceID: "create-app-id-127.0.0.1[6379]___create-app-id-3",
//	})
//	if err != nil {
//		t.Errorf("[%s] check deleted app resource fail: %s\n", t.Name(), err.Error())
//	}
//	if exist == true {
//		t.Errorf("[%s] check deleted app resource fail\n", t.Name())
//	}
//}

func Test_component_CreateClusterResource(t *testing.T) {

	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	bcmOp := Instance()

	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	err := bcmOp.CreateClusterResource(ctx, &CreateClusterResourceParams{
		UserID:     TestIamUserId,
		AppID:      "",
		AppShortID: 0,
		AppType:    "",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	_, err = bcmOp.CheckResourceExist(ctx, &CheckResourceExistParams{
		UserID:     TestIamUserId,
		ResourceID: "",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	err = bcmOp.DeleteClusterResourceIfExisted(ctx, &DeleteClusterResourceIfExistedParams{
		UserID: TestIamUserId,
		AppID:  "",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()
	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})
	patch := ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "GetResource",
		func(ctx context.Context, req *bcm.GetResourceRequest) (rsp *bcm.GetResourceResponse, err error) {
			return &bcm.GetResourceResponse{
				Message:       "",
				Code:          "ResourceNotExistException",
				RequestID:     "",
				UserId:        "",
				Region:        "",
				ServiceName:   "",
				TypeName:      "",
				ResourceId:    "",
				ErrUpdateTime: nil,
				Identifiers:   nil,
				Properties:    nil,
				Tags:          nil,
			}, cerrs.ErrBCMRequestFail.Errorf("test")
		})
	exist, err := bcmOp.CheckClusterResourceExist(ctx, &CheckClusterResourceExistParams{
		UserID:   TestIamUserId,
		AppID:    "not-exist-cluster-id",
		HashName: "hashname_1",
		AppType:  x1model.AppTypeStandalone,
	})
	if err != nil {
		t.Errorf("[%s] check not exist cluster resource fail: %s\n", t.Name(), err.Error())
	}
	if exist == true {
		t.Errorf("[%s] check not exist cluster resource fail\n", t.Name())
	}
	patch.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "GetResource",
		func(ctx context.Context, req *bcm.GetResourceRequest) (rsp *bcm.GetResourceResponse, err error) {
			return &bcm.GetResourceResponse{
				Message:       "",
				Code:          "",
				RequestID:     "",
				UserId:        "",
				Region:        "",
				ServiceName:   "",
				TypeName:      "",
				ResourceId:    "",
				ErrUpdateTime: nil,
				Identifiers:   nil,
				Properties:    nil,
				Tags:          nil,
			}, nil
		})
	err = bcmOp.CreateClusterResource(ctx, &CreateClusterResourceParams{
		UserID:     TestIamUserId,
		AppID:      "create-cluster-id",
		AppShortID: 123456,
		AppType:    x1model.AppTypeStandalone,
		HashName:   "hashname_0",
		HashID:     "hashid",
	})
	if err != nil {
		t.Errorf("[%s] create standalone cluster resource fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patches2.Reset()
}

func Test_component_CreateNodeResource(t *testing.T) {

	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	bcmOp := Instance()

	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	err := bcmOp.CreateNodeResource(ctx, &CreateNodeResourceParams{
		UserID:     TestIamUserId,
		AppID:      "",
		AppShortID: 0,
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	_, err = bcmOp.CheckNodeResourceExist(ctx, &CheckNodeResourceExistParams{
		UserID: TestIamUserId,
		AppID:  "",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	err = bcmOp.DeleteNodeResourceIfExisted(ctx, &DeleteNodeResourceIfExistedParams{
		UserID: TestIamUserId,
		AppID:  "",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()

	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})
	patch := ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "GetResource",
		func(ctx context.Context, req *bcm.GetResourceRequest) (rsp *bcm.GetResourceResponse, err error) {
			return &bcm.GetResourceResponse{
				Message:       "",
				Code:          "ResourceNotExistException",
				RequestID:     "",
				UserId:        "",
				Region:        "",
				ServiceName:   "",
				TypeName:      "",
				ResourceId:    "",
				ErrUpdateTime: nil,
				Identifiers:   nil,
				Properties:    nil,
				Tags:          nil,
			}, cerrs.ErrBCMRequestFail.Errorf("test")
		})
	exist, err := bcmOp.CheckNodeResourceExist(ctx, &CheckNodeResourceExistParams{
		UserID:    TestIamUserId,
		AppID:     "not-exist-node-id",
		NodeFixID: "node_fix_id",
	})
	if err != nil {
		t.Errorf("[%s] check not exist app resource fail: %s\n", t.Name(), err.Error())
	}
	if exist == true {
		t.Errorf("[%s] check not exist app resource fail\n", t.Name())
	}
	patch.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "GetResource",
		func(ctx context.Context, req *bcm.GetResourceRequest) (rsp *bcm.GetResourceResponse, err error) {
			return &bcm.GetResourceResponse{
				Message:       "",
				Code:          "",
				RequestID:     "",
				UserId:        "",
				Region:        "",
				ServiceName:   "",
				TypeName:      "",
				ResourceId:    "",
				ErrUpdateTime: nil,
				Identifiers:   nil,
				Properties:    nil,
				Tags:          nil,
			}, nil
		})
	err = bcmOp.CreateNodeResource(ctx, &CreateNodeResourceParams{
		UserID:     TestIamUserId,
		AppID:      "create-node-id",
		AppShortID: 123456,
		HashID:     "hashid",
		NodeFixID:  "node_fix_id",
	})
	if err != nil {
		t.Errorf("[%s] create node resource fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patches2.Reset()
}

func Test_component_CreateAppResource(t *testing.T) {

	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	bcmOp := Instance()

	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	err := bcmOp.CreateAppResource(ctx, &CreateAppResourceParams{
		UserID:     TestIamUserId,
		AppID:      "",
		AppShortID: 0,
		AppType:    "",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	_, err = bcmOp.CheckAppResourceExist(ctx, &CheckAppResourceExistParams{
		UserID: TestIamUserId,
		AppID:  "",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()

	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})
	patch := ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "GetResource",
		func(ctx context.Context, req *bcm.GetResourceRequest) (rsp *bcm.GetResourceResponse, err error) {
			return &bcm.GetResourceResponse{
				Message:       "",
				Code:          "ResourceNotExistException",
				RequestID:     "",
				UserId:        "",
				Region:        "",
				ServiceName:   "",
				TypeName:      "",
				ResourceId:    "",
				ErrUpdateTime: nil,
				Identifiers:   nil,
				Properties:    nil,
				Tags:          nil,
			}, cerrs.ErrBCMRequestFail.Errorf("test")
		})
	exist, err := bcmOp.CheckAppResourceExist(ctx, &CheckAppResourceExistParams{
		UserID: TestIamUserId,
		AppID:  "not-exist-app-id",
	})
	if err != nil {
		t.Errorf("[%s] check not exist app resource fail: %s\n", t.Name(), err.Error())
	}
	if exist == true {
		t.Errorf("[%s] check not exist app resource fail\n", t.Name())
	}
	patch.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "GetResource",
		func(ctx context.Context, req *bcm.GetResourceRequest) (rsp *bcm.GetResourceResponse, err error) {
			return &bcm.GetResourceResponse{
				Message:       "",
				Code:          "",
				RequestID:     "",
				UserId:        "",
				Region:        "",
				ServiceName:   "",
				TypeName:      "",
				ResourceId:    "",
				ErrUpdateTime: nil,
				Identifiers:   nil,
				Properties:    nil,
				Tags:          nil,
			}, nil
		})
	err = bcmOp.CreateAppResource(ctx, &CreateAppResourceParams{
		UserID:     TestIamUserId,
		AppID:      "create-app-id",
		AppShortID: 123456,
		AppType:    x1model.AppTypeCluster,
	})
	if err != nil {
		t.Errorf("[%s] create cluster app resource fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patches2.Reset()
}

func Test_generateSyncFlowAppID(t *testing.T) {
	type args struct {
		appID      string
		targetBlb  string
		targetPort int64
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"success", args{"app-id", "127.0.0.1", 6379}, "app-id-127.0.0.1[6379]"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := generateSyncFlowAppID(tt.args.appID, tt.args.targetBlb, tt.args.targetPort); got != tt.want {
				t.Errorf("generateSyncFlowClusterShowID() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_generateSyncFlowClusterResourceID(t *testing.T) {
	type args struct {
		appID      string
		targetBlb  string
		hashName   string
		appType    string
		targetPort int64
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "success",
			args: args{
				appID:      "app-id",
				targetBlb:  "127.0.0.1",
				hashName:   "abc_1",
				appType:    "cluster",
				targetPort: 6379,
			},
			want: "app-id-127.0.0.1[6379]___app-id-1",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := generateSyncFlowClusterResourceID(tt.args.appID, tt.args.targetBlb, tt.args.hashName, tt.args.appType, tt.args.targetPort); got != tt.want {
				t.Errorf("generateSyncFlowClusterResourceID() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_component_CreateAndDeleteSyncFlowAppResourceIfExisted(t *testing.T) {

	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	bcmOp := Instance()

	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	err := bcmOp.CreateSyncFlowAppResource(ctx, &CreateSyncFlowAppResourceParams{
		UserID:     TestIamUserId,
		AppID:      "create-app-id",
		AppShortID: 123456,
		TargetBlb:  "127.0.0.1",
		TargetPort: 6379,
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	_, err = bcmOp.CheckResourceExist(ctx, &CheckResourceExistParams{
		UserID:     TestIamUserId,
		ResourceID: "create-app-id-127.0.0.1[6379]",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	err = bcmOp.DeleteSyncFlowAppResourceIfExisted(ctx, &DeleteSyncFlowAppResourceParams{
		UserID:     TestIamUserId,
		AppID:      "create-app-id",
		TargetBlb:  "127.0.0.1",
		TargetPort: 6379,
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()

	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})
	patch := ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "GetResource",
		func(ctx context.Context, req *bcm.GetResourceRequest) (rsp *bcm.GetResourceResponse, err error) {
			return &bcm.GetResourceResponse{
				Message:       "",
				Code:          "",
				RequestID:     "",
				UserId:        "",
				Region:        "",
				ServiceName:   "",
				TypeName:      "",
				ResourceId:    "",
				ErrUpdateTime: nil,
				Identifiers:   nil,
				Properties:    nil,
				Tags:          nil,
			}, nil
		})
	err = bcmOp.CreateSyncFlowAppResource(ctx, &CreateSyncFlowAppResourceParams{
		UserID:     TestIamUserId,
		AppID:      "create-app-id",
		AppShortID: 123456,
		TargetBlb:  "127.0.0.1",
		TargetPort: 6379,
	})
	if err != nil {
		t.Errorf("[%s] create cluster app resource fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "GetResource",
		func(ctx context.Context, req *bcm.GetResourceRequest) (rsp *bcm.GetResourceResponse, err error) {
			return &bcm.GetResourceResponse{
				Message:       "",
				Code:          "ResourceNotExistException",
				RequestID:     "",
				UserId:        "",
				Region:        "",
				ServiceName:   "",
				TypeName:      "",
				ResourceId:    "",
				ErrUpdateTime: nil,
				Identifiers:   nil,
				Properties:    nil,
				Tags:          nil,
			}, cerrs.ErrBCMRequestFail.Errorf("test")
		})
	patches1 = ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "CreateResource",
		func(ctx context.Context, req *bcm.CreateResourceRequest) (rsp *bcm.CreateResourceResponse, err error) {
			return &bcm.CreateResourceResponse{}, nil
		})
	err = bcmOp.CreateSyncFlowAppResource(ctx, &CreateSyncFlowAppResourceParams{
		UserID:     TestIamUserId,
		AppID:      "create-app-id",
		AppShortID: 123456,
		TargetBlb:  "127.0.0.1",
		TargetPort: 6379,
	})
	if err != nil {
		t.Errorf("[%s] create cluster app resource fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patches1.Reset()

	patch = ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "GetResource",
		func(ctx context.Context, req *bcm.GetResourceRequest) (rsp *bcm.GetResourceResponse, err error) {
			return &bcm.GetResourceResponse{
				Message:       "",
				Code:          "ResourceNotExistException",
				RequestID:     "",
				UserId:        "",
				Region:        "",
				ServiceName:   "",
				TypeName:      "",
				ResourceId:    "",
				ErrUpdateTime: nil,
				Identifiers:   nil,
				Properties:    nil,
				Tags:          nil,
			}, cerrs.ErrBCMRequestFail.Errorf("test")
		})
	err = bcmOp.DeleteSyncFlowAppResourceIfExisted(ctx, &DeleteSyncFlowAppResourceParams{
		UserID:     TestIamUserId,
		AppID:      "create-app-id",
		TargetBlb:  "127.0.0.1",
		TargetPort: 6379,
	})
	if err != nil {
		t.Errorf("[%s] delete cluster app resource fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patches2.Reset()
}

func Test_component_CreateAndDeleteSyncFlowClusterResourceIfExisted(t *testing.T) {

	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	bcmOp := Instance()

	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	err := bcmOp.CreateSyncFlowClusterResource(ctx, &CreateSyncFlowClusterResourceParams{
		UserID:     TestIamUserId,
		AppID:      "create-app-id",
		AppShortID: 123456,
		TargetBlb:  "127.0.0.1",
		TargetPort: 6379,
		AppType:    "cluster",
		HashName:   "abc_3",
		HashID:     "hash_id",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	_, err = bcmOp.CheckResourceExist(ctx, &CheckResourceExistParams{
		UserID:     TestIamUserId,
		ResourceID: "create-app-id-127.0.0.1[6379]",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	err = bcmOp.DeleteSyncFlowAppResourceIfExisted(ctx, &DeleteSyncFlowAppResourceParams{
		UserID:     TestIamUserId,
		AppID:      "create-app-id",
		TargetBlb:  "127.0.0.1",
		TargetPort: 6379,
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()

	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})
	patch := ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "GetResource",
		func(ctx context.Context, req *bcm.GetResourceRequest) (rsp *bcm.GetResourceResponse, err error) {
			return &bcm.GetResourceResponse{
				Message:       "",
				Code:          "",
				RequestID:     "",
				UserId:        "",
				Region:        "",
				ServiceName:   "",
				TypeName:      "",
				ResourceId:    "",
				ErrUpdateTime: nil,
				Identifiers:   nil,
				Properties:    nil,
				Tags:          nil,
			}, nil
		})
	err = bcmOp.CreateSyncFlowClusterResource(ctx, &CreateSyncFlowClusterResourceParams{
		UserID:     TestIamUserId,
		AppID:      "create-app-id",
		AppShortID: 123456,
		TargetBlb:  "127.0.0.1",
		TargetPort: 6379,
		AppType:    "cluster",
		HashName:   "abc_3",
		HashID:     "hash_id",
	})
	if err != nil {
		t.Errorf("[%s] create cluster app resource fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "GetResource",
		func(ctx context.Context, req *bcm.GetResourceRequest) (rsp *bcm.GetResourceResponse, err error) {
			return &bcm.GetResourceResponse{
				Message:       "",
				Code:          "ResourceNotExistException",
				RequestID:     "",
				UserId:        "",
				Region:        "",
				ServiceName:   "",
				TypeName:      "",
				ResourceId:    "",
				ErrUpdateTime: nil,
				Identifiers:   nil,
				Properties:    nil,
				Tags:          nil,
			}, cerrs.ErrBCMRequestFail.Errorf("test")
		})
	patches1 = ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "CreateResource",
		func(ctx context.Context, req *bcm.CreateResourceRequest) (rsp *bcm.CreateResourceResponse, err error) {
			return &bcm.CreateResourceResponse{}, nil
		})
	err = bcmOp.CreateSyncFlowClusterResource(ctx, &CreateSyncFlowClusterResourceParams{
		UserID:     TestIamUserId,
		AppID:      "create-app-id",
		AppShortID: 123456,
		TargetBlb:  "127.0.0.1",
		TargetPort: 6379,
		AppType:    "cluster",
		HashName:   "abc_3",
		HashID:     "hash_id",
	})
	if err != nil {
		t.Errorf("[%s] create cluster app resource fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patches1.Reset()

	patch = ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "GetResource",
		func(ctx context.Context, req *bcm.GetResourceRequest) (rsp *bcm.GetResourceResponse, err error) {
			return &bcm.GetResourceResponse{
				Message:       "",
				Code:          "ResourceNotExistException",
				RequestID:     "",
				UserId:        "",
				Region:        "",
				ServiceName:   "",
				TypeName:      "",
				ResourceId:    "",
				ErrUpdateTime: nil,
				Identifiers:   nil,
				Properties:    nil,
				Tags:          nil,
			}, cerrs.ErrBCMRequestFail.Errorf("test")
		})
	err = bcmOp.DeleteSyncFlowClusterResourceIfExisted(ctx, &DeleteSyncFlowClusterResourceParams{
		UserID:     TestIamUserId,
		AppID:      "create-app-id",
		TargetBlb:  "127.0.0.1",
		TargetPort: 6379,
		AppType:    "cluster",
		HashName:   "abc_3",
	})
	if err != nil {
		t.Errorf("[%s] delete cluster app resource fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patches2.Reset()
}

func Test_generateAppResourceIdForGroup(t *testing.T) {
	type args struct {
		appID string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test", args{"app-id"}, "ClusterId:app-id"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := generateAppResourceIdForGroup(tt.args.appID); got != tt.want {
				t.Errorf("generateAppResourceIdForGroup() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_generateClusterResourceIdForGroup(t *testing.T) {
	type args struct {
		appID    string
		hashName string
		appType  string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test",
			args{"app-id", "hash_name_0", "cluster"},
			"ClusterId:app-id;NodeId:app-id-0"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := generateClusterResourceIdForGroup(tt.args.appID, tt.args.hashName, tt.args.appType); got != tt.want {
				t.Errorf("generateClusterResourceIdForGroup() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_generateNodeResourceIdForGroup(t *testing.T) {
	type args struct {
		appID     string
		nodeFixID string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test",
			args{"app-id", "node-id"},
			"ClusterId:app-id;NodeId:node-id"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := generateNodeResourceIdForGroup(tt.args.appID, tt.args.nodeFixID); got != tt.want {
				t.Errorf("generateNodeResourceIdForGroup() = %v, want %v", got, tt.want)
			}
		})
	}
}

//func Test_component_ListInstanceGroupParams_offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	bcmOp := Instance()
//	rsp, err := bcmOp.ListInstanceGroup(ctx, &ListInstanceGroupParams{
//		UserID:   TestIamUserId,
//		TypeName: "RD_ST_INSTANCE",
//	})
//	fmt.Println(err)
//	fmt.Printf("%+v\n", rsp.InstanceGroups[0])
//	rsp1, err := bcmOp.ListInstanceGroup(ctx, &ListInstanceGroupParams{
//		UserID:   TestIamUserId,
//		TypeName: "RD_ST_INSTANCE1",
//	})
//	fmt.Println(err)
//	fmt.Printf("%+v\n", rsp1)
//}

func Test_component_ListInstanceGroupParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	bcmOp := Instance()
	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	_, err := bcmOp.ListInstanceGroup(ctx, &ListInstanceGroupParams{
		UserID:   TestIamUserId,
		TypeName: "RD_ST_INSTANCE",
	})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()

	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})
	patch := ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "ListInstanceGroups",
		func(ctx context.Context, req *bcm.ListInstanceGroupsRequest) (rsp *bcm.ListInstanceGroupsResponse, err error) {
			return nil, errors.New("test list fail ")
		})
	_, err = bcmOp.ListInstanceGroup(ctx, &ListInstanceGroupParams{
		UserID:   TestIamUserId,
		TypeName: "RD_ST_INSTANCE",
	})
	if err == nil {
		t.Errorf("[%s] test list error fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "ListInstanceGroups",
		func(ctx context.Context, req *bcm.ListInstanceGroupsRequest) (rsp *bcm.ListInstanceGroupsResponse, err error) {
			return &bcm.ListInstanceGroupsResponse{
				Message:    "",
				Code:       "",
				RequestID:  "",
				OrderBy:    "",
				Order:      "",
				PageNo:     1,
				PageSize:   10,
				TotalCount: 30,
				Result: []*bcm.InstanceGroupItem{{
					Id:               int64(req.PageNo),
					Name:             "1",
					ServiceName:      "BCE_SCS",
					TypeName:         "RD_ST_INSTANCE",
					Region:           "bj",
					UserId:           TestIamUserId,
					Uuid:             "uuid",
					Count:            0,
					ServiceNameAlias: "ServiceNameAlias",
					TypeNameAlias:    "TypeNameAlias",
					RegionAlias:      "RegionAlias",
					TagKey:           "TagKey",
					TypeTarget:       "TypeTarget",
				}, {
					Id:               4,
					Name:             "1",
					ServiceName:      "BCE_SCS",
					TypeName:         "RD_ST_INSTANCE1",
					Region:           "bj",
					UserId:           TestIamUserId,
					Uuid:             "uuid",
					Count:            0,
					ServiceNameAlias: "ServiceNameAlias",
					TypeNameAlias:    "TypeNameAlias",
					RegionAlias:      "RegionAlias",
					TagKey:           "TagKey",
					TypeTarget:       "TypeTarget",
				}},
			}, nil
		})
	rsp, err := bcmOp.ListInstanceGroup(ctx, &ListInstanceGroupParams{
		UserID:   TestIamUserId,
		TypeName: "RD_ST_INSTANCE",
	})
	if err != nil {
		t.Errorf("[%s] list fail: %s\n", t.Name(), err.Error())
	}
	if len(rsp.InstanceGroups) != 3 {
		t.Errorf("[%s] list fail: expect 3 instance group, got %d\n", t.Name(), len(rsp.InstanceGroups))
	}
	for i, group := range rsp.InstanceGroups {
		if group.ID != int64(i+1) {
			t.Errorf("[%s] list fail: expect group id %d, got %d\n", t.Name(), i, group.ID)
		}
	}
	patches2.Reset()
}

func Test_component_checkGroupNotExist(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	bcmOp := Instance()
	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	err := bcmOp.CheckGroupNotExist(ctx, &ListInstanceGroupParams{
		UserID:   TestIamUserId,
		TypeName: "RD_ST_INSTANCE",
	}, []int64{1, 2, 3})
	if err == nil || err.Error() != "test get auth fail " {
		t.Errorf("[%s] test get auth fail fail: %s\n", t.Name(), err.Error())
	}
	patches1.Reset()

	patches2 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})
	patch := ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "ListInstanceGroups",
		func(ctx context.Context, req *bcm.ListInstanceGroupsRequest) (rsp *bcm.ListInstanceGroupsResponse, err error) {
			return nil, errors.New("test list fail ")
		})
	err = bcmOp.CheckGroupNotExist(ctx, &ListInstanceGroupParams{
		UserID:   TestIamUserId,
		TypeName: "RD_ST_INSTANCE",
	}, []int64{1, 2, 3})
	if err == nil {
		t.Errorf("[%s] test list error fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patch = ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "ListInstanceGroups",
		func(ctx context.Context, req *bcm.ListInstanceGroupsRequest) (rsp *bcm.ListInstanceGroupsResponse, err error) {
			return &bcm.ListInstanceGroupsResponse{
				Message:    "",
				Code:       "",
				RequestID:  "",
				OrderBy:    "",
				Order:      "",
				PageNo:     1,
				PageSize:   10,
				TotalCount: 30,
				Result: []*bcm.InstanceGroupItem{{
					Id:               int64(req.PageNo),
					Name:             "1",
					ServiceName:      "BCE_SCS",
					TypeName:         "RD_ST_INSTANCE",
					Region:           "bj",
					UserId:           TestIamUserId,
					Uuid:             "uuid",
					Count:            0,
					ServiceNameAlias: "ServiceNameAlias",
					TypeNameAlias:    "TypeNameAlias",
					RegionAlias:      "RegionAlias",
					TagKey:           "TagKey",
					TypeTarget:       "TypeTarget",
				}, {
					Id:               4,
					Name:             "1",
					ServiceName:      "BCE_SCS",
					TypeName:         "RD_ST_INSTANCE1",
					Region:           "bj",
					UserId:           TestIamUserId,
					Uuid:             "uuid",
					Count:            0,
					ServiceNameAlias: "ServiceNameAlias",
					TypeNameAlias:    "TypeNameAlias",
					RegionAlias:      "RegionAlias",
					TagKey:           "TagKey",
					TypeTarget:       "TypeTarget",
				}},
			}, nil
		})
	err = bcmOp.CheckGroupNotExist(ctx, &ListInstanceGroupParams{
		UserID:   TestIamUserId,
		TypeName: "RD_ST_INSTANCE",
	}, []int64{1, 2, 3})
	if err == nil {
		t.Errorf("[%s] list fail: %s\n", t.Name(), err.Error())
	}
	err = bcmOp.CheckGroupNotExist(ctx, &ListInstanceGroupParams{
		UserID:   TestIamUserId,
		TypeName: "RD_ST_INSTANCE",
	}, []int64{4, 5, 6})
	if err != nil {
		t.Errorf("[%s] list fail: %s\n", t.Name(), err.Error())
	}
	patches2.Reset()
}

//func Test_component_getGroupAndPreCheck_offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	auth, err := compo_utils.GetOpenapiAuth(ctx, TestIamUserId)
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//	bcmOp := Instance()
//	_, err = bcmOp.GetGroupAndPreCheck(ctx, &operateWithGroupParams{
//		UserID:      TestIamUserId,
//		TypeName:    "RD_ST_INSTANCE",
//		GroupIDs:    []int64{7282},
//		ResourceIDs: nil,
//		Action:      GroupInstanceActionAdd,
//		IgnoreErr:   false,
//	}, auth)
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//	//检查类型不匹配
//	_, err = bcmOp.GetGroupAndPreCheck(ctx, &operateWithGroupParams{
//		UserID:      TestIamUserId,
//		TypeName:    "RD_ST_INSTANCE1",
//		GroupIDs:    []int64{7282},
//		ResourceIDs: nil,
//		Action:      GroupInstanceActionAdd,
//		IgnoreErr:   false,
//	}, auth)
//	if err == nil {
//		t.Errorf("[%s] check type match fail: %s\n", t.Name(), err.Error())
//	}
//	//忽略不存在的实例组
//	_, err = bcmOp.GetGroupAndPreCheck(ctx, &operateWithGroupParams{
//		UserID:      TestIamUserId,
//		TypeName:    "RD_ST_INSTANCE",
//		GroupIDs:    []int64{7282, 1},
//		ResourceIDs: nil,
//		Action:      GroupInstanceActionAdd,
//		IgnoreErr:   true,
//	}, auth)
//	if err != nil {
//		t.Errorf("[%s] ignore no exist fail: %s\n", t.Name(), err.Error())
//	}
//	//不忽略不存在的实例组
//	_, err = bcmOp.GetGroupAndPreCheck(ctx, &operateWithGroupParams{
//		UserID:      TestIamUserId,
//		TypeName:    "RD_ST_INSTANCE",
//		GroupIDs:    []int64{7282, 1},
//		ResourceIDs: nil,
//		Action:      GroupInstanceActionAdd,
//		IgnoreErr:   false,
//	}, auth)
//	if err == nil {
//		t.Errorf("[%s] ignore no exist fail: %s\n", t.Name(), err.Error())
//	}
//}

func Test_component_GetGroupAndPreCheck(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	bcmOp := Instance()
	patches1 := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})
	auth, _ := compo_utils.GetOpenapiAuth(ctx, TestIamUserId)
	patch := ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "GetInstanceGroup",
		func(ctx context.Context, req *bcm.GetInstanceGroupRequest) (rsp *bcm.InstanceGroupResponse, err error) {
			return nil, errors.New("test get instance fail ")
		})
	_, err := bcmOp.GetGroupAndPreCheck(ctx, &operateWithGroupParams{
		UserID:      TestIamUserId,
		TypeName:    "RD_ST_INSTANCE",
		GroupIDs:    []int64{7282},
		ResourceIDs: nil,
		Action:      GroupInstanceActionAdd,
		IgnoreErr:   false,
	}, auth)
	if err == nil {
		t.Errorf("[%s] test fail: %s\n", t.Name(), err.Error())
	}
	patch2 := ApplyMethodFunc(reflect.TypeOf(bcmOp), "CheckGroupNotExist",
		func(ctx context.Context, params *ListInstanceGroupParams, ids []int64) error {
			return errors.New("test err")
		})
	_, err = bcmOp.GetGroupAndPreCheck(ctx, &operateWithGroupParams{
		UserID:      TestIamUserId,
		TypeName:    "RD_ST_INSTANCE",
		GroupIDs:    []int64{7282},
		ResourceIDs: nil,
		Action:      GroupInstanceActionAdd,
		IgnoreErr:   true,
	}, auth)
	if err == nil {
		t.Errorf("[%s] test fail: %s\n", t.Name(), err.Error())
	}
	patch2 = ApplyMethodFunc(reflect.TypeOf(bcmOp), "CheckGroupNotExist",
		func(ctx context.Context, params *ListInstanceGroupParams, ids []int64) error {
			return nil
		})
	_, err = bcmOp.GetGroupAndPreCheck(ctx, &operateWithGroupParams{
		UserID:      TestIamUserId,
		TypeName:    "RD_ST_INSTANCE",
		GroupIDs:    []int64{7282},
		ResourceIDs: nil,
		Action:      GroupInstanceActionAdd,
		IgnoreErr:   true,
	}, auth)
	if err != nil {
		t.Errorf("[%s] test fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patches1.Reset()
	patch2.Reset()
}

//func Test_component_OperateWithGroup_offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	bcmOp := Instance()
//	err := bcmOp.OperateWithGroup(ctx, &operateWithGroupParams{
//		UserID:      TestIamUserId,
//		TypeName:    "RD_ST_INSTANCE",
//		GroupIDs:    []int64{7282},
//		ResourceIDs: []string{"ClusterId:scs-bj-dqozrervjzwk;NodeId:scs-bj-dqozrervjzwk-0"},
//		Action:      GroupInstanceActionAdd,
//		IgnoreErr:   false,
//	})
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//	//err = bcmOp.OperateWithGroup(ctx, &operateWithGroupParams{
//	//	UserID:      TestIamUserId,
//	//	TypeName:    "RD_ST_INSTANCE",
//	//	GroupIDs:    []int64{7282},
//	//	ResourceIDs: []string{"ABCrId:scs-bj-dqozrervjzwk"},
//	//	Action:      GroupInstanceActionRemove,
//	//	IgnoreErr:   false,
//	//})
//	//if err != nil {
//	//	t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	//}
//	//err = bcmOp.OperateWithGroup(ctx, &operateWithGroupParams{
//	//	UserID:      TestIamUserId,
//	//	TypeName:    "RD_ST_INSTANCE",
//	//	GroupIDs:    []int64{7282},
//	//	ResourceIDs: []string{"ClusterId:scs-bj-dqozrervjzwk"},
//	//	Action:      GroupInstanceActionRemove,
//	//	IgnoreErr:   false,
//	//})
//	//if err != nil {
//	//	t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	//}
//	err = bcmOp.OperateWithGroup(ctx, &operateWithGroupParams{
//		UserID:      TestIamUserId,
//		TypeName:    "RD_ST_INSTANCE",
//		GroupIDs:    []int64{7282},
//		ResourceIDs: []string{"ClusterId:scs-bj-dqozrervjzwk;NodeId:scs-bj-dqozrervjzwk-0", "ClusterId:scs-bj-xvpqpmgfidqv;NodeId:scs-bj-xvpqpmgfidqv-0"},
//		Action:      GroupInstanceActionRemove,
//		IgnoreErr:   false,
//	})
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//	err = bcmOp.OperateWithGroup(ctx, &operateWithGroupParams{
//		UserID:      TestIamUserId,
//		TypeName:    "RD_ST_INSTANCE",
//		GroupIDs:    []int64{7281},
//		ResourceIDs: []string{"ClusterId:scs-bj-dqozrervjzwk;NodeId:scs-bj-dqozrervjzwk-0", "ClusterId:scs-bj-xvpqpmgfidqv;NodeId:scs-bj-xvpqpmgfidqv-0"},
//		Action:      GroupInstanceActionRemove,
//		IgnoreErr:   true,
//	})
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//
//}

//func Test_component_OperateNodeWithGroup_offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	bcmOp := Instance()
//	err := bcmOp.OperateNodeWithGroup(ctx, &OperateNodeWithGroupParams{
//		UserID:     TestIamUserId,
//		AppID:      "scs-bj-dqozrervjzwk",
//		TypeName:   "RD_ST_INSTANCE",
//		GroupIDs:   []int64{7282},
//		NodeFixIDs: []string{"scs-bj-dqozrervjzwk-0", "scs-bj-dqozrervjzwk-1"},
//		Action:     GroupInstanceActionAdd,
//		IgnoreErr:  false,
//	})
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//
//}
//
//func Test_component_OperateClusterWithGroup_offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	bcmOp := Instance()
//	err := bcmOp.OperateClusterWithGroup(ctx, &OperateClusterWithGroupParams{
//		UserID:    TestIamUserId,
//		AppID:     "scs-bj-dqozrervjzwk",
//		TypeName:  "RD_ST_INSTANCE",
//		GroupIDs:  []int64{7282},
//		HashNames: []string{"abc_2"},
//		AppType:   "cluster",
//		Action:    GroupInstanceActionAdd,
//		IgnoreErr: false,
//	})
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//
//}
//
//func Test_component_OperateAppWithGroup_offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	bcmOp := Instance()
//	err := bcmOp.OperateAppWithGroup(ctx, &OperateAppWithGroupParams{
//		UserID:    TestIamUserId,
//		AppID:     "scs-bj-dqozrervjzwk",
//		TypeName:  "RD_ST_INSTANCE",
//		GroupIDs:  []int64{7282, 7292},
//		Action:    GroupInstanceActionAdd,
//		IgnoreErr: false,
//	})
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//
//}

func Test_component_OperateWithGroup(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	bcmOp := Instance()
	patch := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	if err := bcmOp.OperateWithGroup(ctx, &operateWithGroupParams{
		UserID:      "",
		TypeName:    "",
		GroupIDs:    nil,
		ResourceIDs: nil,
		Action:      "",
		IgnoreErr:   false,
	}); err == nil {
		t.Errorf("[%s] test fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patch = ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})
	//auth, err := compo_utils.GetOpenapiAuth(ctx, TestIamUserId)
	patch2 := ApplyMethodFunc(reflect.TypeOf(bcmOp), "GetGroupAndPreCheck",
		func(ctx context.Context, params *operateWithGroupParams, auth *common.Authentication) ([]bcm.InstanceGroupItem, error) {
			return nil, errors.New("test err")
		})
	err := bcmOp.OperateWithGroup(ctx, &operateWithGroupParams{
		UserID:      TestIamUserId,
		TypeName:    "RD_ST_INSTANCE",
		GroupIDs:    []int64{7282},
		ResourceIDs: nil,
		Action:      GroupInstanceActionAdd,
		IgnoreErr:   true,
	})
	if err == nil {
		t.Errorf("[%s] test fail: %s\n", t.Name(), err.Error())
	}
	patch2.Reset()
	patch2 = ApplyMethodFunc(reflect.TypeOf(bcmOp), "GetGroupAndPreCheck",
		func(ctx context.Context, params *operateWithGroupParams, auth *common.Authentication) ([]bcm.InstanceGroupItem, error) {
			return nil, nil
		})
	if err := bcmOp.OperateWithGroup(ctx, &operateWithGroupParams{
		UserID:      "",
		TypeName:    "",
		GroupIDs:    nil,
		ResourceIDs: nil,
		Action:      GroupInstanceActionAdd,
		IgnoreErr:   false,
	}); err != nil {
		t.Errorf("[%s] test fail: %s\n", t.Name(), err.Error())
	}
	patch2.Reset()
	patch2 = ApplyMethodFunc(reflect.TypeOf(bcmOp), "GetGroupAndPreCheck",
		func(ctx context.Context, params *operateWithGroupParams, auth *common.Authentication) ([]bcm.InstanceGroupItem, error) {
			return []bcm.InstanceGroupItem{{
				Id:               99,
				Name:             "tt",
				ServiceName:      "",
				TypeName:         "",
				Region:           "",
				UserId:           "",
				Uuid:             "",
				Count:            0,
				ServiceNameAlias: "",
				TypeNameAlias:    "",
				RegionAlias:      "",
				TagKey:           "",
				TypeTarget:       "",
			}}, nil
		})
	if err := bcmOp.OperateWithGroup(ctx, &operateWithGroupParams{
		UserID:      "",
		TypeName:    "",
		GroupIDs:    []int64{99, 88},
		ResourceIDs: []string{"abc", "test"},
		Action:      "testno",
		IgnoreErr:   false,
	}); err == nil {
		t.Errorf("[%s] test fail: %s\n", t.Name(), err.Error())
	}
	patch3 := ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "AddInstanceToGroup",
		func(ctx context.Context, req *bcm.OperateInstanceInGroupRequest) (rsp *bcm.InstanceGroupResponse, err error) {
			return nil, errors.New("abc")
		})
	if err := bcmOp.OperateWithGroup(ctx, &operateWithGroupParams{
		UserID:      "",
		TypeName:    "",
		GroupIDs:    []int64{99, 88},
		ResourceIDs: []string{"abc", "test"},
		Action:      GroupInstanceActionAdd,
		IgnoreErr:   false,
	}); err == nil {
		t.Errorf("[%s] test fail: %s\n", t.Name(), err.Error())
	}
	patch2.Reset()
	patch3.Reset()
}

func Test_component_OperateAppWithGroup(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	bcmOp := Instance()
	patch3 := ApplyMethodFunc(reflect.TypeOf(bcmOp), "OperateWithGroup",
		func(ctx context.Context, params *operateWithGroupParams) error {
			if params.ResourceIDs[0] == "ClusterId:testId" {
				return nil
			}
			return errors.New("abc")
		})
	err := bcmOp.OperateAppWithGroup(ctx, &OperateAppWithGroupParams{
		UserID:    "",
		AppID:     "testId",
		TypeName:  "",
		GroupIDs:  nil,
		Action:    "",
		IgnoreErr: false,
	})
	if err != nil {
		t.Errorf("[%s] test fail: %s\n", t.Name(), err.Error())
	}
	patch3.Reset()
}

func Test_component_OperateClusterWithGroup(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	bcmOp := Instance()
	patch3 := ApplyMethodFunc(reflect.TypeOf(bcmOp), "OperateWithGroup",
		func(ctx context.Context, params *operateWithGroupParams) error {
			if params.ResourceIDs[0] == "ClusterId:testId;NodeId:testId-0" {
				return nil
			}
			return errors.New("abc")
		})
	err := bcmOp.OperateClusterWithGroup(ctx, &OperateClusterWithGroupParams{
		UserID:    "",
		AppID:     "testId",
		TypeName:  "",
		GroupIDs:  nil,
		HashNames: []string{"abc_0"},
		AppType:   "",
		Action:    "",
		IgnoreErr: false,
	})
	if err != nil {
		t.Errorf("[%s] test fail: %s\n", t.Name(), err.Error())
	}
	patch3.Reset()
}

func Test_component_OperateNodeWithGroup(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	bcmOp := Instance()
	patch3 := ApplyMethodFunc(reflect.TypeOf(bcmOp), "OperateWithGroup",
		func(ctx context.Context, params *operateWithGroupParams) error {
			if params.ResourceIDs[0] == "ClusterId:testId;NodeId:abc-0" {
				return nil
			}
			return errors.New("abc")
		})
	err := bcmOp.OperateNodeWithGroup(ctx, &OperateNodeWithGroupParams{
		UserID:     "",
		AppID:      "testId",
		TypeName:   "",
		GroupIDs:   nil,
		NodeFixIDs: []string{"abc-0"},
		Action:     "",
		IgnoreErr:  false,
	})
	if err != nil {
		t.Errorf("[%s] test fail: %s\n", t.Name(), err.Error())
	}
	patch3.Reset()
}

//func Test_component_QuerySampleCount_offline(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//	bcmOp := Instance()
//	data, err := bcmOp.QuerySampleCount(ctx, &QuerySampleCountParams{
//		UserID:         TestIamUserId,
//		AppID:          "scs-bj-cwkrodbdhrtu",
//		StartTime:      time.Now().UTC().Add(-5 * time.Minute),
//		EndTime:        time.Now().UTC(),
//		PeriodInSecond: 120,
//		NodeFixID:      "scs-bj-cwkrodbdhrtu_redis_4460_3",
//	})
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//	for _, v := range data {
//		fmt.Printf("%v\n", v)
//	}
//	data, err = bcmOp.QuerySampleCount(ctx, &QuerySampleCountParams{
//		UserID:         TestIamUserId,
//		AppID:          "scs-bj-cwkrodbdhrtu",
//		StartTime:      time.Now().UTC().Add(-5 * time.Minute),
//		EndTime:        time.Now().UTC(),
//		PeriodInSecond: 120,
//		NodeFixID:      "",
//	})
//	if err != nil {
//		t.Errorf("[%s] get auth fail: %s\n", t.Name(), err.Error())
//	}
//	for _, v := range data {
//		fmt.Printf("%v\n", v)
//	}
//}

func Test_component_QuerySampleCount(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()
	bcmOp := Instance()
	patch := ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, errors.New("test get auth fail ")
	})
	if _, err := bcmOp.QuerySampleCount(ctx, &QuerySampleCountParams{
		UserID:         TestIamUserId,
		AppID:          "scs-bj-cwkrodbdhrtu",
		StartTime:      time.Now().UTC().Add(-5 * time.Minute),
		EndTime:        time.Now().UTC(),
		PeriodInSecond: 120,
		NodeFixID:      "scs-bj-cwkrodbdhrtu_redis_4460_3",
	}); err == nil {
		t.Errorf("[%s] test fail: %s\n", t.Name(), err.Error())
	}
	patch.Reset()
	patch = ApplyFunc(compo_utils.GetOpenapiAuth, func(ctx context.Context, userId string) (*common.Authentication, error) {
		return nil, nil
	})

	patch1 := ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "QueryMetricData",
		func(ctx context.Context, req *bcm.QueryMetricDataRequest) (rsp *bcm.QueryMetricDataResponse, err error) {
			return nil, errors.New("abc")
		})
	if _, err := bcmOp.QuerySampleCount(ctx, &QuerySampleCountParams{
		UserID:         TestIamUserId,
		AppID:          "scs-bj-cwkrodbdhrtu",
		StartTime:      time.Now().UTC().Add(-5 * time.Minute),
		EndTime:        time.Now().UTC(),
		PeriodInSecond: 120,
		NodeFixID:      "scs-bj-cwkrodbdhrtu_redis_4460_3",
	}); err == nil {
		t.Errorf("[%s] test fail: %s\n", t.Name(), err.Error())
	}
	patch1.Reset()
	patch2 := ApplyMethodFunc(reflect.TypeOf(bcmOp.bcmSdk), "QueryMetricData",
		func(ctx context.Context, req *bcm.QueryMetricDataRequest) (rsp *bcm.QueryMetricDataResponse, err error) {
			return &bcm.QueryMetricDataResponse{
				RequestId: "",
				Code:      "",
				UserId:    "",
				Message:   "",
				DataPoints: []bcm.DataPoint{{
					SampleCount: 1,
					Timestamp:   time.Now().UTC(),
				},
					{
						SampleCount: 2,
						Timestamp:   time.Now().UTC(),
					}},
			}, nil
		})
	rsp, err := bcmOp.QuerySampleCount(ctx, &QuerySampleCountParams{
		UserID:         TestIamUserId,
		AppID:          "scs-bj-cwkrodbdhrtu",
		StartTime:      time.Now().UTC().Add(-5 * time.Minute),
		EndTime:        time.Now().UTC(),
		PeriodInSecond: 120,
		NodeFixID:      "scs-bj-cwkrodbdhrtu_redis_4460_3",
	})
	if err != nil {
		t.Errorf("[%s] test fail: %s\n", t.Name(), err.Error())
	}
	if len(rsp) != 2 {
		t.Errorf("[%s] test fail: %v\n", t.Name(), rsp)
	}
	if rsp[0].SampleCount != 1 {
		t.Errorf("[%s] test fail: %v\n", t.Name(), rsp)
	}
	patch2.Reset()
	patch.Reset()
}
