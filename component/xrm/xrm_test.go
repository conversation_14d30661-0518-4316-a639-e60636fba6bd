package xrm

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"testing"

	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/xrm"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest/sdkmock"
)

// init 函数用于初始化单元测试环境
//
//	func init() {
//		unittest.UnitTestInit(2)
//	}
const (
	TestResourceID          = "0.test_service.scs"
	TestRmunitStatusServing = "serving"
	TestVolumeID            = "v-xxxxxx"
)

func TestXrm_ShowXrmResourcesByOrder_Failed(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockXrmService := sdkmock.NewMockXrmService(ctrl)
	mockXrmService.EXPECT().QueryServerCreationTask(gomock.Any(), gomock.Any()).Return(
		&xrm.QueryServerCreationTaskResponse{
			Success: 1,
			Msg:     "",
			Data: &xrm.QueryServerCreationTaskResponseData{
				Success:   1,
				Status:    "Failed",
				FailedIPs: []string{"***********"},
			},
		}, nil)

	// Set up calls.
	req := &ShowXrmResourcesParams{}
	conf := &config{}
	if err := compo_utils.LoadConf("xrm", conf); err != nil {
		panic(err.Error())
	}

	xrmResourceOpObj := &xrmResourceOp{
		xrmSdk: mockXrmService,
		conf:   conf,
	}
	_, err := xrmResourceOpObj.ShowXrmResourcesByOrder(ctx, req)

	if err == nil {
		t.Fatalf("ShowXrmResourcesByOrder() err not match")
	}
	// Verify results
	if strings.Contains(err.Error(), ErrXrmOrderFailedMsg) {
		fmt.Println("success")
	} else {
		t.Fatalf("ShowXrmResourcesByOrder() error: %v", err)
	}
}

// TestCreateXrmResources creates a new XRM resource using the mocked XRM service
func TestCreateXrmResourcesDefault(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockXrmService := sdkmock.NewMockXrmService(ctrl)
	mockXrmService.EXPECT().CreateServer(gomock.Any(), gomock.Any()).Return(&xrm.CreateServerResponse{}, nil)

	// Set up calls.
	req := &CreateXrmResourceParams{}
	conf := &config{}
	if err := compo_utils.LoadConf("xrm", conf); err != nil {
		panic(err.Error())
	}

	xrmResourceOpObj := &xrmResourceOp{
		xrmSdk: mockXrmService,
		conf:   conf,
	}
	requestID, err := xrmResourceOpObj.CreateXrmResources(ctx, req)

	// Verify results
	if err != nil {
		t.Fatalf("CreateResource() error: %v", err)
	}
	if requestID != "" {
		t.Fatalf("CreateSource() resp is nil")
	}
}

func TestCreateXrmResourcesNano(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockXrmService := sdkmock.NewMockXrmService(ctrl)
	mockXrmService.EXPECT().CreateServer(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, req *xrm.CreateServerRequest) (rsp *xrm.CreateServerResponse, err error) {
			// cache.n1.nano
			if req.Servers[0].Flavor.RamInMB.Quota < 512 {
				if req.Servers[0].Flavor.RamInMB.Limit != req.Servers[0].Flavor.RamInMB.Quota*4 {
					return nil, errors.New("Create nano reqeust failed")
				}
			} else {
				if req.Servers[0].Flavor.RamInMB.Limit != req.Servers[0].Flavor.RamInMB.Quota*2 {
					return nil, errors.New("Create nano reqeust failed")
				}
			}

			rsp = &xrm.CreateServerResponse{}
			return rsp, nil
		})

	// Set up calls.
	req := &CreateXrmResourceParams{}
	item := CreateXrmResourceParamsItem{}
	item.Specification = CreateXrmResourceParamsItemSpecification{}
	item.Specification.MemoryCapacityInMB = 256
	req.Items = append(req.Items, item)

	conf := &config{}
	if err := compo_utils.LoadConf("xrm", conf); err != nil {
		panic(err.Error())
	}

	xrmResourceOpObj := &xrmResourceOp{
		xrmSdk: mockXrmService,
		conf:   conf,
	}
	requestID, err := xrmResourceOpObj.CreateXrmResources(ctx, req)

	// Verify results
	if err != nil {
		t.Fatalf("CreateResource() error: %v", err)
	}
	if requestID != "" {
		t.Fatalf("CreateSource() resp is nil")
	}
}

func TestCreateXrmResourcesWithDiskTypeLabel(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockXrmService := sdkmock.NewMockXrmService(ctrl)
	mockXrmService.EXPECT().CreateServer(gomock.Any(), gomock.Any()).Return(&xrm.CreateServerResponse{}, nil)

	// Set up calls.
	req := &CreateXrmResourceParams{}
	req.Labels = make(map[string]string)
	req.Labels["disk_type"] = "cds"
	item := CreateXrmResourceParamsItem{}
	item.Specification = CreateXrmResourceParamsItemSpecification{}
	item.Specification.MemoryCapacityInMB = 1024
	req.Items = append(req.Items, item)
	conf := &config{}
	if err := compo_utils.LoadConf("xrm", conf); err != nil {
		panic(err.Error())
	}

	xrmResourceOpObj := &xrmResourceOp{
		xrmSdk: mockXrmService,
		conf:   conf,
	}
	requestID, err := xrmResourceOpObj.CreateXrmResources(ctx, req)

	// Verify results
	if err != nil {
		t.Fatalf("CreateResource() error: %v", err)
	}
	if requestID != "" {
		t.Fatalf("CreateSource() resp is nil")
	}
}

func TestXrm_DeleteXrmResource_InvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	xrmOp := XrmResourceOp()
	err := xrmOp.DeleteXrmResource(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	err = xrmOp.DeleteXrmResource(ctx, &DeleteXrmResourceParams{})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	err = xrmOp.DeleteXrmResource(ctx, &DeleteXrmResourceParams{
		ResourceID: "",
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

}

func TestXrm_ResizeXrmResource_InvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	xrmOp := XrmResourceOp()

	// param is null
	_, err := xrmOp.ResizeXrmResource(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// param resourceID is null
	_, err = xrmOp.ResizeXrmResource(ctx, &ResizeXrmResourceParams{})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// param resourceID is null
	_, err = xrmOp.ResizeXrmResource(ctx, &ResizeXrmResourceParams{
		ResourceID: "",
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// not support resizetype
	_, err = xrmOp.ResizeXrmResource(ctx, &ResizeXrmResourceParams{
		ResourceID: TestResourceID,
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// resizeVolume, param volumeType empty
	_, err = xrmOp.ResizeXrmResource(ctx, &ResizeXrmResourceParams{
		ResourceID:                TestResourceID,
		ResizeType:                "resizeVolume",
		TargetVolumeSizeInGBQuota: 20,
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// resizeVolume, param cds volumeID empty
	_, err = xrmOp.ResizeXrmResource(ctx, &ResizeXrmResourceParams{
		ResourceID:                TestResourceID,
		ResizeType:                "resizeVolume",
		TargetVolumeSizeInGBQuota: 20,
		VolumeType:                "cds",
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// resizeVolume, param docker_bind volumePath empty
	_, err = xrmOp.ResizeXrmResource(ctx, &ResizeXrmResourceParams{
		ResourceID:                TestResourceID,
		ResizeType:                "resizeVolume",
		TargetVolumeSizeInGBQuota: 20,
		VolumeType:                "docker_bind",
	})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

}

func TestXrm_ResizeXrmResource_ResizeCpu(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockXrmService := sdkmock.NewMockXrmService(ctrl)
	mockXrmService.EXPECT().UpdateServer(gomock.Any(), gomock.Any()).Return(&xrm.UpdateServerResponse{
		Success: 1,
	}, nil)

	// Set up calls.
	req := &ResizeXrmResourceParams{
		ResizeType:     "combo",
		ResourceID:     TestResourceID,
		TargetCPUCount: 1,
	}
	conf := &config{}
	if err := compo_utils.LoadConf("xrm", conf); err != nil {
		panic(err.Error())
	}

	xrmResourceOpObj := &xrmResourceOp{
		xrmSdk: mockXrmService,
		conf:   conf,
	}

	_, err := xrmResourceOpObj.ResizeXrmResource(ctx, req)

	// Verify results
	if err != nil {
		t.Fatalf("ResizeResource() error: %v", err)
	}
}

func TestXrm_ResizeXrmResource_ResizeVolume(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockXrmService := sdkmock.NewMockXrmService(ctrl)
	mockXrmService.EXPECT().UpdateServer(gomock.Any(), gomock.Any()).Return(&xrm.UpdateServerResponse{
		Success: 1,
	}, nil)

	// Set up calls.
	req := &ResizeXrmResourceParams{
		ResizeType:                "resizeVolume",
		ResourceID:                TestResourceID,
		VolumeType:                "cds",
		VolumeID:                  "v-xxx",
		TargetVolumeSizeInGBQuota: 100,
	}
	conf := &config{}
	if err := compo_utils.LoadConf("xrm", conf); err != nil {
		panic(err.Error())
	}

	xrmResourceOpObj := &xrmResourceOp{
		xrmSdk: mockXrmService,
		conf:   conf,
	}

	_, err := xrmResourceOpObj.ResizeXrmResource(ctx, req)

	// Verify results
	if err != nil {
		t.Fatalf("ResizeResource() error: %v", err)
	}
}

func TestXrm_CheckResizeXrmResource_InvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	xrmOp := XrmResourceOp()

	// param is null
	err := xrmOp.CheckXrmResourceVolumeSize(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// param resourceID is null
	err = xrmOp.CheckXrmResourceVolumeSize(ctx, &CheckXrmResourceVolumeSizeParams{})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}

func TestXrm_CheckResizeXrmResource_OK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockXrmService := sdkmock.NewMockXrmService(ctrl)
	mockXrmService.EXPECT().ShowServer(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, req *xrm.ShowServerRequest) (rsp *xrm.ShowServerResponse, err error) {
			rsp = &xrm.ShowServerResponse{}
			rsp.Success = 1
			rsp.Rmunit = &xrm.Rmunit{}
			rsp.Rmunit.Status = TestRmunitStatusServing
			rsp.Rmunit.Volumes = append(rsp.Rmunit.Volumes, &xrm.VolumeResponse{
				Type:                    "cds",
				ExpectVolumeSizeGBQuota: 100,
				VolumeSizeMBQuota:       100 * 1000,
			})
			rsp.Rmunit.Volumes = append(rsp.Rmunit.Volumes, &xrm.VolumeResponse{
				Type:                    "docker_bind",
				ExpectVolumeSizeGBQuota: 100,
				VolumeSizeMBQuota:       100 * 1000,
			})
			return rsp, nil
		},
	)

	// Set up calls.
	req := &CheckXrmResourceVolumeSizeParams{
		ResourceID: TestResourceID,
	}
	conf := &config{}
	if err := compo_utils.LoadConf("xrm", conf); err != nil {
		panic(err.Error())
	}

	xrmResourceOpObj := &xrmResourceOp{
		xrmSdk: mockXrmService,
		conf:   conf,
	}

	err := xrmResourceOpObj.CheckXrmResourceVolumeSize(ctx, req)

	// Verify results
	if err != nil {
		t.Fatalf("CheckXrmResourceVolumeSize() error: %v", err)
	}
}

func TestXrm_DetachXrmResourceVolume_InvalidParams(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	xrmOp := XrmResourceOp()

	// param is null
	err := xrmOp.DetachXrmResourceVolume(ctx, nil)
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test nil req fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}

	// param resourceID is null
	err = xrmOp.DetachXrmResourceVolume(ctx, &DetachXrmResourceVolumeParams{})
	if !cerrs.ErrInvalidParams.Is(err) {
		t.Errorf("[%s] test serverId params checking fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}

func TestXrm_DetachXrmResourceVolume_OK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockXrmService := sdkmock.NewMockXrmService(ctrl)
	mockXrmService.EXPECT().UpdateServer(gomock.Any(), gomock.Any()).Return(&xrm.UpdateServerResponse{
		Success: 1,
	}, nil)

	// Set up calls.
	req := &DetachXrmResourceVolumeParams{
		ResourceID: TestResourceID,
		VolumeID:   TestVolumeID,
	}
	conf := &config{}
	if err := compo_utils.LoadConf("xrm", conf); err != nil {
		panic(err.Error())
	}

	xrmResourceOpObj := &xrmResourceOp{
		xrmSdk: mockXrmService,
		conf:   conf,
	}

	err := xrmResourceOpObj.DetachXrmResourceVolume(ctx, req)

	// Verify results
	if err != nil {
		t.Fatalf("DetachResourceVolume() error: %v", err)
	}
}

func TestXrm_ShowXrmResource_OK(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Set up mocks.
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockXrmService := sdkmock.NewMockXrmService(ctrl)
	mockXrmService.EXPECT().ShowServer(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, req *xrm.ShowServerRequest) (rsp *xrm.ShowServerResponse, err error) {
			rsp = &xrm.ShowServerResponse{}
			rsp.Success = 1
			rsp.Rmunit = &xrm.Rmunit{}
			rsp.Rmunit.Status = TestRmunitStatusServing
			rsp.Rmunit.Volumes = append(rsp.Rmunit.Volumes, &xrm.VolumeResponse{
				Type:                    "cds",
				ExpectVolumeSizeGBQuota: 100,
				VolumeSizeMBQuota:       100 * 1000,
			})
			rsp.Rmunit.Volumes = append(rsp.Rmunit.Volumes, &xrm.VolumeResponse{
				Type:                    "docker_bind",
				ExpectVolumeSizeGBQuota: 100,
				VolumeSizeMBQuota:       100 * 1000,
			})
			return rsp, nil
		},
	)

	// Set up calls.
	req := &ShowXrmResourceParams{
		ResourceID: TestResourceID,
	}
	conf := &config{}
	if err := compo_utils.LoadConf("xrm", conf); err != nil {
		panic(err.Error())
	}

	xrmResourceOpObj := &xrmResourceOp{
		xrmSdk: mockXrmService,
		conf:   conf,
	}

	_, err := xrmResourceOpObj.ShowXrmResource(ctx, req)

	// Verify results
	if err != nil {
		t.Fatalf("ShowXrmResource() error: %v", err)
	}
}
