package xrm

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/xrm"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"sync"
)

type config struct {
	Region                     string
	ResizeType                 string
	VolumeType                 string
	VolumePath                 string
	MemoryRatioIncludeFrag     float64
	LimitMaxRatio              int64
	DockerRootPath             string
	ScheduleStrategyMaxPerRack int64
	ScheduleStrategyMaxPerHost int64
	ScheduleStrategyPortType   string
	ImageID                    string
}

type XrmResource interface {
	ShowXrmResourcesByOrder(ctx context.Context, params *ShowXrmResourcesParams) ([]XrmResources, error)
	CreateXrmResources(ctx context.Context, params *CreateXrmResourceParams) (string, error)
	DeleteXrmResource(ctx context.Context, params *DeleteXrmResourceParams) error
	ResizeXrmResource(ctx context.Context, params *ResizeXrmResourceParams) (*ResizeXrmResourceResponse, error)
	CheckXrmResourceVolumeSize(ctx context.Context, params *CheckXrmResourceVolumeSizeParams) error
	DetachXrmResourceVolume(ctx context.Context, params *DetachXrmResourceVolumeParams) error
	ShowXrmResource(ctx context.Context, params *ShowXrmResourceParams) (*xrm.ShowServerResponse, error)
}

type CreateXrmResourceParams struct {
	Product string
	PoolID  string
	Azone   string
	ShardID string
	ImageID string
	Ports   map[string]int
	Labels  map[string]string
	Items   []CreateXrmResourceParamsItem
	// optional
	Entrypoint     []string
	StartParams    map[string]interface{}
	VolumePath     string
	Engine         string
	CdsID          string
	CdsSnapshotID  string
	CdsStorageType string
	LogicalRegion  string
	DiskLabels     map[string]string
}

type CreateXrmResourceParamsItem struct {
	Specification CreateXrmResourceParamsItemSpecification
	Count         int
}

type CreateXrmResourceParamsItemSpecification struct {
	CPUCount             int
	MemoryCapacityInMB   int
	RootDiskCapacityInGB int
	DataDiskCapacityInGB int
}

type ShowXrmResourcesParams struct {
	OrderID string
}

type DeleteXrmResourceParams struct {
	ResourceID string
}

type XrmResources struct {
	ID               string
	FloatingIP       string
	Flavor           string
	ShardId          string
	Region           string
	Azone            string
	ImageID          string
	VolumeMountPoint string
	PoolID           string
	CdsID            string
}

type ResizeXrmResourceParams struct {
	ResizeType                string
	ResourceID                string
	TargetCPUCount            int64
	TargetMemorySizeInMB      int64
	VolumeType                string
	VolumeID                  string
	VolumePath                string
	TargetVolumeSizeInGBQuota int64
	Engine                    string
}

type ResizeXrmResourceResponse struct {
	ResourceID                string
	TargetCPUCount            int64
	TargetMemorySizeInMB      int64
	TargetVolumeSizeInGBQuota int64
}

type CheckXrmResourceVolumeSizeParams struct {
	ResourceID string
}

type DetachXrmResourceVolumeParams struct {
	ResourceID string
	VolumeID   string
}

type ShowXrmResourceParams struct {
	ResourceID string
}

type xrmResourceOp struct {
	xrmSdk xrm.XrmService
	conf   *config
}

var (
	ErrXrmOrderRunning   = errors.New("xrm order is running")
	ErrXrmOrderFailedMsg = "xrm order failed"
)

var xrmResourceOpObj XrmResource

var once sync.Once

func XrmResourceOp() XrmResource {
	once.Do(func() {
		conf := &config{}
		if err := compo_utils.LoadConf("xrm", conf); err != nil {
			panic(err.Error())
		}
		xrmResourceOpObj = &xrmResourceOp{
			xrmSdk: xrm.NewDefaultXrmSdk(),
			conf:   conf,
		}
	})
	return xrmResourceOpObj
}

func SetXrmResourceOpForUt(xr XrmResource) {
	once.Do(func() {
		xrmResourceOpObj = xr
	})
}

// ShowXrmResourcesByOrder 查询resources创建任务结果
func (xr *xrmResourceOp) ShowXrmResourcesByOrder(ctx context.Context, params *ShowXrmResourcesParams) ([]XrmResources, error) {

	response, err := xr.xrmSdk.QueryServerCreationTask(ctx, &xrm.QueryServerCreationTaskRequest{
		RequestId: params.OrderID,
	})

	if err != nil {
		logger.ComponentLogger.Warning(ctx, "xrm show order %s failed, resp: %s", params.OrderID, base_utils.Format(response))
		return nil, err
	}
	logger.ComponentLogger.Trace(ctx, "show xrm order %s succ, resp: %s", params.OrderID, base_utils.Format(response))
	switch response.Data.Status {
	case "Running":
		return nil, ErrXrmOrderRunning
	case "Failed":
		if response.Data.FailedIPs == nil {
			response.Data.FailedIPs = []string{}
		}
		return nil, errors.New(fmt.Sprintf("%s, failed_ips: %s", ErrXrmOrderFailedMsg, strings.Join(response.Data.FailedIPs, ",")))
	case "Finished":
		// pass
	default:
		logger.ComponentLogger.Warning(ctx, "unknown status %s", response.Data.Status)
		return nil, errors.Errorf("unknown xrm order status %s", response.Data.Status)
	}
	ret, err := xr.getXrmResources(ctx, params, response)
	if err != nil {
		logger.ComponentLogger.Trace(ctx, "show xrm order resp convert failed")
		return nil, err
	}
	logger.ComponentLogger.Trace(ctx, "show xrm order resp after convert is %s", base_utils.Format(response))
	return ret, nil
}

func (xr *xrmResourceOp) getXrmResources(ctx context.Context, params *ShowXrmResourcesParams, response *xrm.QueryServerCreationTaskResponse) ([]XrmResources, error) {
	xrmResources := make([]XrmResources, len(response.Data.Servers))

	for idx, server := range response.Data.Servers {
		xrmResources[idx].FloatingIP = server.Ip
		xrmResources[idx].ID = server.ServerId
		xrmResources[idx].ShardId = server.ServiceId
		xrmResources[idx].Region = server.Attributes.Region
		xrmResources[idx].Azone = server.Attributes.Zone
		xrmResources[idx].ImageID = server.Attributes.ImageId
		xrmResources[idx].PoolID = server.Attributes.PoolId
		//模仿BCC Flavor返回格式: cpu-内存MB-系统盘-本地盘 最后一个字段为本地盘默认为0
		xrmResources[idx].Flavor = cast.ToString(server.CpuCoresQuota) + "-" + cast.ToString(server.MemMbQuota) + "-" + cast.ToString(server.DiskGb) + "-0"
		if len(server.Attributes.Volumes) == 1 {
			xrmResources[idx].VolumeMountPoint = server.Attributes.Volumes[0].MountPoint
			// rm-master CDS 独占版本前, 没有 Attributes 属性
			if server.Attributes.Volumes[0].Attributes == nil {
				xrmResources[idx].CdsID = ""
			} else {
				xrmResources[idx].CdsID = server.Attributes.Volumes[0].Attributes.ID
			}
		}
	}

	return xrmResources, nil
}

// CreateXrmResources 创建resources
func (xr *xrmResourceOp) CreateXrmResources(ctx context.Context, params *CreateXrmResourceParams) (string, error) {
	logger.ComponentLogger.Trace(ctx, "xrm request before converting %s", base_utils.Format(params))
	request, err := xr.getXrmCreateResourcesRequest(ctx, params)
	if err != nil {
		return "", err
	}
	logger.ComponentLogger.Trace(ctx, "converted xrm request %s", base_utils.Format(request))
	response, err := xr.xrmSdk.CreateServer(ctx, request)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "send xrm request %s failed, resp: %s", base_utils.Format(request), base_utils.Format(response))
		return "", err
	}
	logger.ComponentLogger.Trace(ctx, "send xrm request succ, request id %s", response.RequestId)
	return response.RequestId, nil
}

func (xr *xrmResourceOp) fillXrmRequestAttributePoolId(ctx context.Context, params *CreateXrmResourceParams, ar *xrm.AttributesRequest) error {

	ar.PoolId = params.PoolID
	return nil
}

func (xr *xrmResourceOp) fillXrmRequestAttributeLabel(ctx context.Context, params *CreateXrmResourceParams, ar *xrm.AttributesRequest) error {

	ar.Label = params.Labels
	return nil
}

// fillXrmRequestAttributeVolumes 函数用于填充 xrm.AttributesRequest 中的卷信息。
// 参数：
//
//		ctx 上下文对象。
//	 params CreateXrmResourceParams
//		item CreateXrmResourceParamsItem 类型变量，包含创建资源参数项的数据。
//		ar *xrm.AttributesRequest 类型的指针变量，表示属性请求对象。
//		volumePath string 类型变量，存储卷路径。
func (xr *xrmResourceOp) fillXrmRequestAttributeVolumes(
	ctx context.Context, params *CreateXrmResourceParams, item CreateXrmResourceParamsItem, ar *xrm.AttributesRequest, volumePath string,
	diskLabels map[string]string) error {
	Type := xr.conf.VolumeType
	// disk_type 存在且值为 cds 时，则 type 为 cds, 否则为默认值 docker_bind
	if diskType, found := params.Labels["disk_type"]; found {
		if diskType == "cds" {
			Type = "cds"
		}
		logger.ComponentLogger.Trace(ctx, "diskType:%s, Type:%s", diskType, Type)
	}

	format := true
	cdsID := ""
	snapshotID := ""
	if strings.HasPrefix(params.CdsID, "v-") || strings.HasPrefix(params.CdsSnapshotID, "s-") {
		format = false
		cdsID = params.CdsID
		snapshotID = params.CdsSnapshotID
	}

	ar.Volumes = append(ar.Volumes, &xrm.VolumeRequest{
		Type:              Type,
		VolumeSizeGBQuota: int64(item.Specification.DataDiskCapacityInGB),
		VolumeSizeGBLimit: int64(item.Specification.DataDiskCapacityInGB),
		VolumePath:        volumePath,
		DiskLabels:        diskLabels,
		Attributes: &xrm.VolumeAttributes{
			ID:          cdsID,
			Format:      format,
			SnapshotID:  snapshotID,
			StorageType: params.CdsStorageType,
		},
	})
	return nil
}

// fillXrmRequestFlavor 根据 CreateXrmResourceParamsItem 和 xrm.Flavor 参数，填充 xrm 请求体 Flavor 属性
func (xr *xrmResourceOp) fillXrmRequestFlavor(ctx context.Context, item CreateXrmResourceParamsItem, f *xrm.Flavor, engine string) error {
	// 配置文件中是 xr.conf.LimitMaxRatio 为 2, pega 的 CPU 和 Mem 均使用原申请量规格
	cpuLimitMaxRatio := xr.conf.LimitMaxRatio
	memLimitMaxRatio := xr.conf.LimitMaxRatio
	if engine == x1model.EnginePegaDB {
		cpuLimitMaxRatio = 1
		memLimitMaxRatio = 1
	}

	// Redis 4 核 cpu 则仍保持 4 核(参数中为 4000)
	if engine == x1model.EngineRedis && item.Specification.CPUCount == 4000 {
		cpuLimitMaxRatio = 1
	}

	f.CpuInCore = &xrm.FlavorAttr{
		Quota: int64(item.Specification.CPUCount) / 1000,
		Limit: int64(item.Specification.CPUCount) / 1000 * cpuLimitMaxRatio,
	}

	// cache.n1.nano
	if item.Specification.MemoryCapacityInMB == 256 {
		f.RamInMB = &xrm.FlavorAttr{
			Quota: int64(float64(item.Specification.MemoryCapacityInMB) * xr.conf.MemoryRatioIncludeFrag),
			Limit: int64(float64(item.Specification.MemoryCapacityInMB)*xr.conf.MemoryRatioIncludeFrag) * memLimitMaxRatio * 2,
		}
	} else {
		f.RamInMB = &xrm.FlavorAttr{
			Quota: int64(float64(item.Specification.MemoryCapacityInMB) * xr.conf.MemoryRatioIncludeFrag),
			Limit: int64(float64(item.Specification.MemoryCapacityInMB)*xr.conf.MemoryRatioIncludeFrag) * memLimitMaxRatio,
		}
	}

	f.DiskInGB = append(f.DiskInGB, &xrm.FlavorDiskAttr{
		Quota: int64(item.Specification.RootDiskCapacityInGB),
		Limit: int64(item.Specification.RootDiskCapacityInGB),
		Type:  xr.conf.DockerRootPath,
	})
	f.NetworkInMbps = &xrm.FlavorAttr{
		Quota: 0,
		Limit: 0,
	}
	return nil
}

func (xr *xrmResourceOp) fillXrmRequestScheduleStrategy(ctx context.Context, params *CreateXrmResourceParams, ss *xrm.ScheduleStrategy) error {

	ss.MaxPerHost = xr.conf.ScheduleStrategyMaxPerHost
	ss.MaxPerRack = xr.conf.ScheduleStrategyMaxPerRack
	for name, portValue := range params.Ports {
		if portValue == 0 {
			continue
		}
		ss.Port = append(ss.Port, &xrm.Port{
			Name:  name,
			Type:  xr.conf.ScheduleStrategyPortType,
			Value: int32(portValue),
		})
	}
	return nil
}

// getXRMCreateResourcesRequest 获取创建资源请求的函数
func (xr *xrmResourceOp) getXrmCreateResourcesRequest(ctx context.Context, params *CreateXrmResourceParams) (*xrm.CreateServerRequest, error) {

	request := &xrm.CreateServerRequest{
		ServiceId:        params.ShardID,
		Source:           params.Product,
		ScheduleStrategy: &xrm.ScheduleStrategy{},
	}
	if err := xr.fillXrmRequestScheduleStrategy(ctx, params, request.ScheduleStrategy); err != nil {
		return nil, err
	}

	// default imageid
	imageID := xr.conf.ImageID
	if strings.HasPrefix(params.ImageID, "registry.baidubce.com") {
		imageID = params.ImageID
	}

	// default volumePath eg. /mnt/data
	volumePath := xr.conf.VolumePath
	if strings.HasPrefix(params.VolumePath, "/") {
		volumePath = params.VolumePath
	}

	// default region
	// dbstack 环境只部署了一套 x1-resource，需要提供多地域支持，故使用此参数透传 region
	region := xr.conf.Region
	if params.LogicalRegion != "" {
		region = params.LogicalRegion
	}

	for _, item := range params.Items {
		s := &xrm.ServerRequest{}
		s.Count = int64(item.Count)

		s.Attributes = &xrm.AttributesRequest{
			Region:      region,
			Zone:        params.Azone,
			ImageId:     imageID,
			Entrypoint:  params.Entrypoint,
			StartParams: params.StartParams,
		}
		if err := xr.fillXrmRequestAttributePoolId(ctx, params, s.Attributes); err != nil {
			return nil, err
		}
		if err := xr.fillXrmRequestAttributeLabel(ctx, params, s.Attributes); err != nil {
			return nil, err
		}
		if err := xr.fillXrmRequestAttributeVolumes(ctx, params, item, s.Attributes, volumePath, params.DiskLabels); err != nil {
			return nil, err
		}

		s.Flavor = &xrm.Flavor{}
		if err := xr.fillXrmRequestFlavor(ctx, item, s.Flavor, params.Engine); err != nil {
			return nil, err
		}
		request.Servers = append(request.Servers, s)
	}
	return request, nil
}

// DeleteXrmResource 删除容器
func (xr *xrmResourceOp) DeleteXrmResource(ctx context.Context, params *DeleteXrmResourceParams) error {
	if params == nil {
		return cerrs.ErrInvalidParams.Errorf("param is null")
	}
	if params.ResourceID == "" {
		return cerrs.ErrInvalidParams.Errorf("param resourceID is null")
	}

	req := &xrm.DeleteServerRequest{
		ServerIds: []string{params.ResourceID},
	}
	_, err := xr.xrmSdk.DeleteServer(ctx, req)
	return err
}

// fillXrmResizeFlavor 方法用于填充 xrm 资源调整参数到 ResizeFlavor 中
func (xr *xrmResourceOp) fillXrmResizeFlavor(ctx context.Context, params *ResizeXrmResourceParams, rf *xrm.UpdateData) error {
	// 配置文件中是 xr.conf.LimitMaxRatio 为 2, pega 的 CPU 和 Mem 均使用原申请量规格
	cpuLimitMaxRatio := xr.conf.LimitMaxRatio
	memLimitMaxRatio := xr.conf.LimitMaxRatio
	if params.Engine == x1model.EnginePegaDB {
		cpuLimitMaxRatio = 1
		memLimitMaxRatio = 1
	}

	// Redis 4 核 cpu 则仍保持 4 核(参数中为 4000)
	if params.Engine == x1model.EngineRedis && params.TargetCPUCount == 4000 {
		cpuLimitMaxRatio = 1
	}

	if params.TargetCPUCount != 0 {
		rf.CpuInCore = params.TargetCPUCount / 1000
		rf.CpuInCoreLimit = int64(params.TargetCPUCount) / 1000 * cpuLimitMaxRatio
	}
	if params.TargetMemorySizeInMB != 0 {
		rf.RamInMB = int64(float64(params.TargetMemorySizeInMB) * xr.conf.MemoryRatioIncludeFrag)
		rf.RamInMBLimit = int64(float64(params.TargetMemorySizeInMB)*xr.conf.MemoryRatioIncludeFrag) * memLimitMaxRatio
	}

	// cache.n1.nano
	if params.TargetMemorySizeInMB == 256 {
		rf.RamInMBLimit = int64(float64(params.TargetMemorySizeInMB)*xr.conf.MemoryRatioIncludeFrag) * memLimitMaxRatio * 2
	}
	return nil
}

// ResizeXrmResource 同步请求，容器变配
func (xr *xrmResourceOp) ResizeXrmResource(ctx context.Context, params *ResizeXrmResourceParams) (*ResizeXrmResourceResponse, error) {

	if params == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("param is null")
	}
	if params.ResourceID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("param resourceID is null")
	}

	// resize cpu or mem
	if params.ResizeType == "combo" {
		if params.TargetCPUCount == 0 && params.TargetMemorySizeInMB == 0 {
			return nil, cerrs.ErrInvalidParams.Errorf("param target cpu and memory are zero")
		}

		resizeFlavor := &xrm.UpdateData{}

		if err := xr.fillXrmResizeFlavor(ctx, params, resizeFlavor); err != nil {
			return nil, err
		}
		req := &xrm.UpdateServerRequest{
			ServerId: params.ResourceID,
			Type:     params.ResizeType,
			Data:     resizeFlavor,
		}
		_, err := xr.xrmSdk.UpdateServer(ctx, req)
		if err != nil {
			return nil, err
		}
		return &ResizeXrmResourceResponse{
			ResourceID:           params.ResourceID,
			TargetCPUCount:       resizeFlavor.CpuInCore * 1000,
			TargetMemorySizeInMB: resizeFlavor.RamInMB,
		}, nil
	} else if params.ResizeType == "resizeVolume" {
		if params.TargetVolumeSizeInGBQuota == 0 {
			return nil, cerrs.ErrInvalidParams.Errorf("param target volume size are zero")
		}

		if params.VolumeType != "cds" && params.VolumeType != "docker_bind" {
			return nil, cerrs.ErrInvalidParams.Errorf("param volumeType empty")
		}

		if params.VolumeType == "cds" && params.VolumeID == "" {
			return nil, cerrs.ErrInvalidParams.Errorf("param cds volumeID empty")
		}

		if params.VolumeType == "docker_bind" && params.VolumePath == "" {
			return nil, cerrs.ErrInvalidParams.Errorf("param docker_bind volumePath empty")
		}

		resizeFlavor := &xrm.UpdateData{
			VolumeID:            params.VolumeID,
			VolumeType:          params.VolumeType,
			VolumePath:          params.VolumePath,
			VolumeSizeInGBQuota: params.TargetVolumeSizeInGBQuota,
		}
		req := &xrm.UpdateServerRequest{
			ServerId: params.ResourceID,
			Type:     params.ResizeType,
			Data:     resizeFlavor,
		}
		_, err := xr.xrmSdk.UpdateServer(ctx, req)
		if err != nil {
			return nil, err
		}
		return &ResizeXrmResourceResponse{
			ResourceID:                params.ResourceID,
			TargetVolumeSizeInGBQuota: params.TargetVolumeSizeInGBQuota,
		}, nil
	}

	return nil, cerrs.ErrInvalidParams.Errorf("not support resizetype")
}

// CheckXrmResourceVolumeSize 判断 volume 是否扩容完成(支持 docker_bind && cds)
func (xr *xrmResourceOp) CheckXrmResourceVolumeSize(ctx context.Context, params *CheckXrmResourceVolumeSizeParams) error {
	if params == nil {
		return cerrs.ErrInvalidParams.Errorf("param is null")
	}
	if params.ResourceID == "" {
		return cerrs.ErrInvalidParams.Errorf("param resourceID is null")
	}

	req := &xrm.ShowServerRequest{
		ServerID: params.ResourceID,
	}
	for {
		rsp, err := xr.xrmSdk.ShowServer(ctx, req)
		if err != nil {
			return err
		}

		if rsp.Rmunit.Status == "exec_resize_volume" {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(10 * time.Second):
				continue
			}
		}

		volumeCheckOKCount := 0
		for _, volume := range rsp.Rmunit.Volumes {
			if volume.ExpectVolumeSizeGBQuota*1000 > volume.VolumeSizeMBQuota {
				return errors.Errorf("volume size not match")
			}

			if volume.ExpectVolumeSizeGBQuota*1000 == volume.VolumeSizeMBQuota {
				volumeCheckOKCount = volumeCheckOKCount + 1
			}
		}

		if volumeCheckOKCount == len(rsp.Rmunit.Volumes) {
			return nil
		}
	}
}

// DetachXrmResourceVolume
func (xr *xrmResourceOp) DetachXrmResourceVolume(ctx context.Context, params *DetachXrmResourceVolumeParams) error {
	if params == nil {
		return cerrs.ErrInvalidParams.Errorf("param is null")
	}
	if params.ResourceID == "" {
		return cerrs.ErrInvalidParams.Errorf("param resourceID is null")
	}

	updateData := &xrm.UpdateData{
		VolumeID:   params.VolumeID,
		VolumeType: "cds",
	}
	req := &xrm.UpdateServerRequest{
		ServerId: params.ResourceID,
		Type:     "detachVolume",
		Data:     updateData,
	}
	_, err := xr.xrmSdk.UpdateServer(ctx, req)
	if err != nil {
		return err
	}
	return nil
}

// ShowXrmResource
func (xr *xrmResourceOp) ShowXrmResource(ctx context.Context, params *ShowXrmResourceParams) (*xrm.ShowServerResponse, error) {
	if params == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("param is null")
	}
	if params.ResourceID == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("param resourceID is null")
	}

	req := &xrm.ShowServerRequest{
		ServerID: params.ResourceID,
	}

	rsp, err := xr.xrmSdk.ShowServer(ctx, req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}
