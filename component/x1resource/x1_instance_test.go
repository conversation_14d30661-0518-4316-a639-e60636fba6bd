/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * author: wangbin34
 * Date: 2023-12-06
 * File: x1_instance_test.go
 */

/*
 * DESCRIPTION
 *   x1 instance unit tests
 */

package x1resource

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/scs/x1-base/component/specification"
	"icode.baidu.com/baidu/scs/x1-base/component/suborder"
	"icode.baidu.com/baidu/scs/x1-base/model/x1resource"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	x1ResourceSDK "icode.baidu.com/baidu/scs/x1-base/sdk/x1_resource"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest/sdkmock"
)

const (
	TestAppID           = "scs-test-app"
	TestIamUserID       = "ea2c4a2286ca4540afcb7f7d4ba2d199"
	TestProduct         = "scs"
	TestImageID         = "test_image_id"
	TestVpcID           = "58d53a2e-27fe-4ead-9041-c72ec67ec1c1"
	TestOrderID         = "8f1b1f09-4625-4b22-be4d-5d27ca047bf2"
	TestLogicalZone     = "zoneB"
	TestAzone           = "AZONE-bjyz"
	TestStoreType       = "DRAM"
	TestResouceType     = "container"
	TestPriority        = "low"
	TestNodeType        = "cache.n1.nano"
	TestDeploySetID     = "test_deployset_id"
	TestSecurityGroupID = "test_secgroup_id"
)

// Mock implementations for testing

type mockSubOrder struct {
	sendFunc  func(ctx context.Context, meta *suborder.Meta, request interface{}) (string, error)
	queryFunc func(ctx context.Context, orderID string) (interface{}, error)
}

func (m *mockSubOrder) Send(ctx context.Context, meta *suborder.Meta, request interface{}) (string, error) {
	if m.sendFunc != nil {
		return m.sendFunc(ctx, meta, request)
	}
	return TestOrderID, nil
}

func (m *mockSubOrder) Query(ctx context.Context, orderID string) (interface{}, error) {
	if m.queryFunc != nil {
		return m.queryFunc(ctx, orderID)
	}
	return []ResInstance{}, nil
}

// Interface to match suborder.SubOrder methods we need
type subOrderInterface interface {
	Send(ctx context.Context, meta *suborder.Meta, request interface{}) (string, error)
	Query(ctx context.Context, orderID string) (interface{}, error)
}

func TestInstance_Singleton(t *testing.T) {
	// Test that Instance() returns the same instance
	instance1 := Instance()
	instance2 := Instance()

	if instance1 != instance2 {
		t.Error("Instance() should return the same singleton instance")
	}

	if instance1 == nil {
		t.Error("Instance() should not return nil")
	}
}

func TestCreateInstance_WithMockSubOrder(t *testing.T) {
	// Skip this test since we can't easily mock the subOrder field
	// We'll test CreateInstanceSub directly instead
	t.Skip("Skipping CreateInstance test - testing CreateInstanceSub directly instead")
}

func TestCreateInstanceSub_Success(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Setup test configuration
	GetSpecResourceConfig = func(azone, spec string) *SpecResourceConfig {
		return &SpecResourceConfig{
			AZone:                     azone,
			Spec:                      spec,
			ResourceTag:               "test-tag",
			DeviceCapacityInGB:        100,
			DeviceWriteIOPS:           1000,
			DeviceReadIOPS:            2000,
			DeviceWriteIOBPS:          3000,
			DeviceReadIOBPS:           4000,
			DeviceWriteIOPSLimitRate:  500,
			DeviceReadIOPSLimitRate:   600,
			DeviceWriteIOBPSLimitRate: 700,
			DeviceReadIOBPSLimitRate:  800,
		}
	}

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().CreateInstance(ctx, gomock.Any()).Return(
		&x1ResourceSDK.CreateInstanceResponse{
			OrderId: TestOrderID,
		}, nil)

	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	x1Request := CreateInstanceParams{
		AppID:        TestAppID,
		UserID:       TestIamUserID,
		Product:      TestProduct,
		ImageID:      TestImageID,
		VpcID:        TestVpcID,
		LogicalZone:  TestLogicalZone,
		Azone:        TestAzone,
		StoreType:    TestStoreType,
		ResouceType:  TestResouceType,
		Priority:     TestPriority,
		X1TaskID:     "test-task-id",
		CustomLabels: nil,
		NodeType:     TestNodeType,
	}

	item := CreateInstanceParamsItem{
		Spec: specification.Specification{
			AvailableVolume:      1,
			Name:                 "test-spec",
			CPUCount:             1,
			MemoryCapacityInGB:   1,
			RootDiskCapacityInGB: 20,
			DataDiskCapacityInGB: 20,
		},
		Engine:          "redis",
		Count:           1,
		DeploySetID:     TestDeploySetID,
		SecurityGroupID: TestSecurityGroupID,
		EntityIDs:       nil,
	}
	x1Request.Items = append(x1Request.Items, item)

	request, orderID, err := testComponent.CreateInstanceSub(ctx, &x1Request)
	if err != nil {
		t.Errorf("CreateInstanceSub failed: %v", err)
	}
	if orderID != TestOrderID {
		t.Errorf("Expected orderID %s, got %s", TestOrderID, orderID)
	}
	if request == nil {
		t.Error("Expected non-nil request")
	}
}

func TestCreateInstanceSub_SDKError(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Setup test configuration
	GetSpecResourceConfig = func(azone, spec string) *SpecResourceConfig {
		return &SpecResourceConfig{
			AZone:       azone,
			Spec:        spec,
			ResourceTag: "test-tag",
		}
	}

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().CreateInstance(ctx, gomock.Any()).Return(
		nil, errors.New("SDK error"))

	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	x1Request := CreateInstanceParams{
		AppID:   TestAppID,
		UserID:  TestIamUserID,
		Product: TestProduct,
		Items: []CreateInstanceParamsItem{
			{
				Spec: specification.Specification{
					Name: "test-spec",
				},
			},
		},
	}

	_, _, err := testComponent.CreateInstanceSub(ctx, &x1Request)
	if err == nil {
		t.Error("Expected error from SDK, but got nil")
	}
}

func TestShowCreateInstanceByOrder_WithMockSubOrder(t *testing.T) {
	// Skip this test since we can't easily mock the subOrder field
	t.Skip("Skipping ShowCreateInstanceByOrder test - testing ShowCreateInstanceByOrderSub directly instead")
}

func TestShowCreateInstanceByOrderSub_Success(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().ShowCreateOrder(ctx, gomock.Any()).Return(
		&x1ResourceSDK.ShowCreateOrderResponse{
			Status: x1resource.OrderStatusSuccess,
			Instances: []x1ResourceSDK.Instance{
				{
					Id:           "instance-1",
					Name:         "test-instance-1",
					RootPassword: "password123",
					FixedIp:      "***********",
					Flavor:       "test-flavor",
					EntityID:     "entity-1",
					Port: x1ResourceSDK.InstancePort{
						DbPort:     6379,
						StatPort:   8080,
						XagentPort: 9090,
					},
					HostUUID: "host-uuid-1",
					HostName: "host-1",
				},
			},
		}, nil)

	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	params := &ShowInstanceParams{
		UserID:  TestIamUserID,
		OrderID: TestOrderID,
	}

	instances, err := testComponent.ShowCreateInstanceByOrderSub(ctx, params)
	if err != nil {
		t.Errorf("ShowCreateInstanceByOrderSub failed: %v", err)
	}
	if len(instances) != 1 {
		t.Errorf("Expected 1 instance, got %d", len(instances))
	}
	if instances[0].ID != "instance-1" {
		t.Errorf("Expected instance ID 'instance-1', got '%s'", instances[0].ID)
	}
	if instances[0].RootPassword != "password123" {
		t.Errorf("Expected password 'password123', got '%s'", instances[0].RootPassword)
	}
}

func TestShowCreateInstanceByOrderSub_OrderInOperation(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().ShowCreateOrder(ctx, gomock.Any()).Return(
		&x1ResourceSDK.ShowCreateOrderResponse{
			Status: x1resource.OrderStatusWaiting,
		}, nil)

	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	params := &ShowInstanceParams{
		UserID:  TestIamUserID,
		OrderID: TestOrderID,
	}

	_, err := testComponent.ShowCreateInstanceByOrderSub(ctx, params)
	if err == nil {
		t.Error("Expected ErrInstanceOrderInOperation, but got nil")
	}
	if !errors.Is(err, ErrInstanceOrderInOperation) {
		t.Errorf("Expected ErrInstanceOrderInOperation, got %v", err)
	}
}

func TestShowCreateInstanceByOrderSub_OrderFailed(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().ShowCreateOrder(ctx, gomock.Any()).Return(
		&x1ResourceSDK.ShowCreateOrderResponse{
			Status: x1resource.OrderStatusFail,
		}, nil)

	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	params := &ShowInstanceParams{
		UserID:  TestIamUserID,
		OrderID: TestOrderID,
	}

	_, err := testComponent.ShowCreateInstanceByOrderSub(ctx, params)
	if err == nil {
		t.Error("Expected error for failed order, but got nil")
	}
}

func TestDeleteInstances_Success(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().DeleteInstance(ctx, gomock.Any()).DoAndReturn(
		func(ctx context.Context, req *x1ResourceSDK.DeleteInstancesReq) (*x1ResourceSDK.DeleteInstancesResponse, error) {
			if len(req.InstanceIds) != 2 {
				t.Errorf("Expected 2 instance IDs, got %d", len(req.InstanceIds))
			}
			if req.UserID != TestIamUserID {
				t.Errorf("Expected UserID %s, got %s", TestIamUserID, req.UserID)
			}
			return &x1ResourceSDK.DeleteInstancesResponse{}, nil
		})

	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	params := &DeleteInstanceParams{
		InstanceIds: []string{"instance-1", "instance-2"},
		UserID:      TestIamUserID,
		X1TaskID:    "test-task-id",
	}

	err := testComponent.DeleteInstances(ctx, params)
	if err != nil {
		t.Errorf("DeleteInstances failed: %v", err)
	}
}

func TestDeleteInstances_EmptyList(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	testComponent := &component{}

	params := &DeleteInstanceParams{
		InstanceIds: []string{},
		UserID:      TestIamUserID,
		X1TaskID:    "test-task-id",
	}

	err := testComponent.DeleteInstances(ctx, params)
	if err != nil {
		t.Errorf("DeleteInstances with empty list should not fail: %v", err)
	}
}

func TestResizeInstance_Success(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().ResizeInstance(ctx, gomock.Any()).DoAndReturn(
		func(ctx context.Context, req *x1ResourceSDK.ResizeInstanceReq) (*x1ResourceSDK.ResizeInstanceResponse, error) {
			if req.InstanceId != "instance-1" {
				t.Errorf("Expected InstanceId 'instance-1', got '%s'", req.InstanceId)
			}
			if req.TargetCPUCount != 2000 { // 2 * 1000
				t.Errorf("Expected TargetCPUCount 2000, got %d", req.TargetCPUCount)
			}
			if req.TargetMemoryCapacityInMB != 2048 {
				t.Errorf("Expected TargetMemoryCapacityInMB 2048, got %d", req.TargetMemoryCapacityInMB)
			}
			return &x1ResourceSDK.ResizeInstanceResponse{
				OrderId: TestOrderID,
			}, nil
		})

	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	params := &ResizeInstanceParam{
		InstanceID:           "instance-1",
		UserID:               TestIamUserID,
		TargetCPUCount:       2,
		TargetMemorySizeInMB: 2048,
		TargetDiskSizeInGB:   100,
		Engine:               "redis",
		X1TaskID:             "test-task-id",
	}

	orderID, err := testComponent.ResizeInstance(ctx, params)
	if err != nil {
		t.Errorf("ResizeInstance failed: %v", err)
	}
	if orderID != TestOrderID {
		t.Errorf("Expected orderID %s, got %s", TestOrderID, orderID)
	}
}

func TestShowResizeInstanceByOrder_Success(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().ShowResizeOrder(ctx, gomock.Any()).Return(
		&x1ResourceSDK.ShowResizeOrderResponse{
			Status: x1resource.OrderStatusSuccess,
		}, nil)

	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	params := &ShowInstanceParams{
		UserID:  TestIamUserID,
		OrderID: TestOrderID,
	}

	err := testComponent.ShowResizeInstanceByOrder(ctx, params)
	if err != nil {
		t.Errorf("ShowResizeInstanceByOrder failed: %v", err)
	}
}

func TestShowResizeInstanceByOrder_InOperation(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().ShowResizeOrder(ctx, gomock.Any()).Return(
		&x1ResourceSDK.ShowResizeOrderResponse{
			Status: x1resource.OrderStatusWaiting,
		}, nil)

	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	params := &ShowInstanceParams{
		UserID:  TestIamUserID,
		OrderID: TestOrderID,
	}

	err := testComponent.ShowResizeInstanceByOrder(ctx, params)
	if err == nil {
		t.Error("Expected ErrInstanceOrderInOperation, but got nil")
	}
	if !errors.Is(err, ErrInstanceOrderInOperation) {
		t.Errorf("Expected ErrInstanceOrderInOperation, got %v", err)
	}
}

func TestShowResizeInstanceByOrder_Failed(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().ShowResizeOrder(ctx, gomock.Any()).Return(
		&x1ResourceSDK.ShowResizeOrderResponse{
			Status: x1resource.OrderStatusFail,
		}, nil)

	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	params := &ShowInstanceParams{
		UserID:  TestIamUserID,
		OrderID: TestOrderID,
	}

	err := testComponent.ShowResizeInstanceByOrder(ctx, params)
	if err == nil {
		t.Error("Expected ErrInstanceOrderFailed, but got nil")
	}
	if !errors.Is(err, ErrInstanceOrderFailed) {
		t.Errorf("Expected ErrInstanceOrderFailed, got %v", err)
	}
}

func TestGetCreateInstanceRequest_Success(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Setup test configuration
	GetSpecResourceConfig = func(azone, spec string) *SpecResourceConfig {
		return &SpecResourceConfig{
			AZone:                     azone,
			Spec:                      spec,
			ResourceTag:               "test-tag",
			DeviceCapacityInGB:        100,
			DeviceWriteIOPS:           1000,
			DeviceReadIOPS:            2000,
			DeviceWriteIOBPS:          3000,
			DeviceReadIOBPS:           4000,
			DeviceWriteIOPSLimitRate:  500,
			DeviceReadIOPSLimitRate:   600,
			DeviceWriteIOBPSLimitRate: 700,
			DeviceReadIOBPSLimitRate:  800,
		}
	}

	params := &CreateInstanceParams{
		AppID:                TestAppID,
		UserID:               TestIamUserID,
		Product:              TestProduct,
		ImageID:              TestImageID,
		VpcID:                TestVpcID,
		CustomerDeploySetIDs: []string{},
		LogicalZone:          TestLogicalZone,
		Azone:                TestAzone,
		StoreType:            TestStoreType,
		Priority:             TestPriority,
		X1TaskID:             "test-task-id",
		CustomLabels:         []string{"custom-label-1"},
		NodeType:             TestNodeType,
		LogicalRegion:        "test-region",
		Items: []CreateInstanceParamsItem{
			{
				Spec: specification.Specification{
					AvailableVolume:      1,
					Name:                 "test-spec",
					CPUCount:             2,
					MemoryCapacityInGB:   4,
					RootDiskCapacityInGB: 20,
					DataDiskCapacityInGB: 50,
				},
				Engine:          "redis",
				Count:           1,
				DeploySetID:     TestDeploySetID,
				SecurityGroupID: TestSecurityGroupID,
				EntityIDs:       []string{"entity-1"},
			},
		},
	}

	request, err := getCreateInstanceRequest(ctx, params)
	if err != nil {
		t.Errorf("getCreateInstanceRequest failed: %v", err)
	}

	// Verify basic fields
	if request.AppID != TestAppID {
		t.Errorf("Expected AppID %s, got %s", TestAppID, request.AppID)
	}
	if request.UserID != TestIamUserID {
		t.Errorf("Expected UserID %s, got %s", TestIamUserID, request.UserID)
	}
	if request.ResouceType != "container" {
		t.Errorf("Expected ResouceType 'container', got '%s'", request.ResouceType)
	}

	// Verify items
	if len(request.Items) != 1 {
		t.Errorf("Expected 1 item, got %d", len(request.Items))
	}

	item := request.Items[0]
	if item.Engine != "redis" {
		t.Errorf("Expected Engine 'redis', got '%s'", item.Engine)
	}
	if item.Count != 1 {
		t.Errorf("Expected Count 1, got %d", item.Count)
	}

	// Verify spec conversion
	spec := item.Spec
	if spec.CPUCount != 2000 { // 2 * 1000
		t.Errorf("Expected CPUCount 2000, got %d", spec.CPUCount)
	}
	if spec.MemoryCapacityInMB != 256 { // Special case for cache.n1.nano
		t.Errorf("Expected MemoryCapacityInMB 256 (nano override), got %d", spec.MemoryCapacityInMB)
	}
	if spec.DataDiskCapacityInGB != 50 {
		t.Errorf("Expected DataDiskCapacityInGB 50, got %d", spec.DataDiskCapacityInGB)
	}

	// Verify IOPS calculations
	expectedReadIOPS := int64((float64(600) / 1000) * (float64(50) / float64(100)) * float64(2000))
	if spec.BlkioDeviceReadIOps != expectedReadIOPS {
		t.Errorf("Expected BlkioDeviceReadIOps %d, got %d", expectedReadIOPS, spec.BlkioDeviceReadIOps)
	}

	// Verify custom labels include resource tag
	found := false
	for _, label := range request.CustomLabels {
		if label == "test-tag" {
			found = true
			break
		}
	}
	if !found {
		t.Error("Expected custom labels to include resource tag 'test-tag'")
	}
}

func TestGetCreateInstanceRequest_WithoutNanoNodeType(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Setup test configuration
	GetSpecResourceConfig = func(azone, spec string) *SpecResourceConfig {
		return &SpecResourceConfig{
			AZone:       azone,
			Spec:        spec,
			ResourceTag: "",
		}
	}

	params := &CreateInstanceParams{
		AppID:   TestAppID,
		UserID:  TestIamUserID,
		Product: TestProduct,
		Azone:   TestAzone,
		Items: []CreateInstanceParamsItem{
			{
				Spec: specification.Specification{
					Name:               "test-spec",
					MemoryCapacityInGB: 4,
				},
			},
		},
		NodeType: "cache.n1.large", // Not nano
	}

	request, err := getCreateInstanceRequest(ctx, params)
	if err != nil {
		t.Errorf("getCreateInstanceRequest failed: %v", err)
	}

	// Verify memory is not overridden for non-nano types
	spec := request.Items[0].Spec
	if spec.MemoryCapacityInMB != 4096 { // 4 * 1024
		t.Errorf("Expected MemoryCapacityInMB 4096, got %d", spec.MemoryCapacityInMB)
	}
}

func TestGetInstanceResources_Success(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	params := &ShowInstanceParams{
		UserID:  TestIamUserID,
		OrderID: TestOrderID,
	}

	response := &x1ResourceSDK.ShowCreateOrderResponse{
		Instances: []x1ResourceSDK.Instance{
			{
				Id:           "instance-1",
				Name:         "test-instance-1",
				RootPassword: "password123",
				FixedIp:      "***********",
				Ipv6FixedIp:  "2001:db8::1",
				FloatingIp:   "********",
				Flavor:       "test-flavor",
				EntityID:     "entity-1",
				Port: x1ResourceSDK.InstancePort{
					DbPort:         6379,
					StatPort:       8080,
					XagentPort:     9090,
					XagentSyncPort: 9091,
					McpackPort:     9092,
				},
				HostUUID: "host-uuid-1",
				HostName: "host-1",
			},
			{
				Id:           "instance-2",
				Name:         "test-instance-2",
				RootPassword: "password456",
				FixedIp:      "***********",
				Flavor:       "test-flavor-2",
				EntityID:     "entity-2",
			},
		},
	}

	instances, err := getInstanceResources(ctx, params, response)
	if err != nil {
		t.Errorf("getInstanceResources failed: %v", err)
	}

	if len(instances) != 2 {
		t.Errorf("Expected 2 instances, got %d", len(instances))
	}

	// Verify first instance
	inst1 := instances[0]
	if inst1.ID != "instance-1" {
		t.Errorf("Expected ID 'instance-1', got '%s'", inst1.ID)
	}
	if inst1.Name != "test-instance-1" {
		t.Errorf("Expected Name 'test-instance-1', got '%s'", inst1.Name)
	}
	if inst1.RootPassword != "password123" {
		t.Errorf("Expected RootPassword 'password123', got '%s'", inst1.RootPassword)
	}
	if inst1.FixIP != "***********" {
		t.Errorf("Expected FixIP '***********', got '%s'", inst1.FixIP)
	}
	if inst1.FixIPv6 != "2001:db8::1" {
		t.Errorf("Expected FixIPv6 '2001:db8::1', got '%s'", inst1.FixIPv6)
	}
	if inst1.FloatingIP != "********" {
		t.Errorf("Expected FloatingIP '********', got '%s'", inst1.FloatingIP)
	}
	if inst1.Port.DbPort != 6379 {
		t.Errorf("Expected DbPort 6379, got %d", inst1.Port.DbPort)
	}

	// Verify second instance
	inst2 := instances[1]
	if inst2.ID != "instance-2" {
		t.Errorf("Expected ID 'instance-2', got '%s'", inst2.ID)
	}
}
