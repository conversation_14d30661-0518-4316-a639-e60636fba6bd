package x1resource

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/scs/x1-base/component/suborder"
	"icode.baidu.com/baidu/scs/x1-base/utils/deepcopy"
)

type subOrderWarpper struct {
}

func (s *subOrderWarpper) RequestSplit(ctx context.Context, order *suborder.OrderRecord, request interface{}) (map[string]interface{}, error) {
	_, _ = ctx, order
	req, ok := request.(*CreateInstanceParams)
	if !ok {
		return nil, fmt.Errorf("request is not CreateInstanceParams, got %T", request)
	}
	splitRequests := make(map[string]interface{})
	for _, item := range req.Items {
		if _, ok := splitRequests[item.Spec.Name]; !ok {
			splitRequests[item.Spec.Name] = deepcopy.Copy(req)
			splitRequests[item.Spec.Name].(*CreateInstanceParams).Items = []CreateInstanceParamsItem{item}
		} else {
			splitRequests[item.Spec.Name].(*CreateInstanceParams).Items = append(
				splitRequests[item.Spec.Name].(*CreateInstanceParams).Items, item)
		}
	}
	return splitRequests, nil
}

func (s *subOrderWarpper) ResponseMerger(ctx context.Context, order *suborder.OrderRecord, resps map[string]interface{}) (interface{}, error) {
	_, _ = ctx, order
	mergedResponse := make([]ResInstance, 0)
	for _, resp := range resps {
		resp, ok := resp.([]ResInstance)
		if !ok {
			return nil, fmt.Errorf("response is not []ResInstance, got %T", resp)
		}
		mergedResponse = append(mergedResponse, resp...)
	}
	if len(mergedResponse) == 0 {
		return nil, fmt.Errorf("no instances created")
	}
	return mergedResponse, nil
}

func (s *subOrderWarpper) SendSub(ctx context.Context, order *suborder.OrderRecord,
	subOrder *suborder.SubOrderRecord) (realRequest interface{}, orderID string, err error) {
	_ = order
	subRequest := &CreateInstanceParams{}
	if err := json.Unmarshal([]byte(subOrder.Parameters), subRequest); err != nil {
		return nil, "", fmt.Errorf("unmarshal subRequestString failed: %w", err)
	}
	return Instance().CreateInstanceSub(ctx, subRequest)
}

func (s *subOrderWarpper) QuerySub(ctx context.Context, order *suborder.OrderRecord,
	subOrder *suborder.SubOrderRecord) (inOperation bool, response interface{}, err error) {
	queryReq := &ShowInstanceParams{
		UserID:  order.UserID,
		OrderID: subOrder.BccOrderID,
	}
	response, err = Instance().ShowCreateInstanceByOrderSub(ctx, queryReq)
	if err != nil {
		if errors.Is(err, ErrInstanceOrderInOperation) {
			return true, nil, errors.Wrapf(err, "query sub order %s in operation", subOrder.BccOrderID)
		} else if errors.Is(err, ErrInstanceOrderFailed) {
			return false, nil, errors.Wrapf(err, "query sub order %s failed", subOrder.BccOrderID)
		} else {
			if strings.Contains(err.Error(), "x1 instance error") {
				return false, nil, errors.Wrapf(err, "query sub order %s failed", subOrder.BccOrderID)
			}
			return true, nil, errors.Wrapf(err, "query sub order %s error", subOrder.BccOrderID)
		}
	}
	return false, response, nil
}

func NewSubOrderWarpper() suborder.Wrapper {
	return &subOrderWarpper{}
}
