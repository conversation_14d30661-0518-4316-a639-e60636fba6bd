/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * author: AI Assistant
 * Date: 2025-07-21
 * File: suborder_warpper_test.go
 */

/*
 * DESCRIPTION
 *   suborder wrapper unit tests
 */

package x1resource

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/scs/x1-base/component/specification"
	"icode.baidu.com/baidu/scs/x1-base/component/suborder"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	x1ResourceSDK "icode.baidu.com/baidu/scs/x1-base/sdk/x1_resource"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest/sdkmock"
)

func TestNewSubOrderWarpper(t *testing.T) {
	wrapper := NewSubOrderWarpper()
	if wrapper == nil {
		t.Error("NewSubOrderWarpper should not return nil")
	}

	// Verify it implements the Wrapper interface
	_, ok := wrapper.(suborder.Wrapper)
	if !ok {
		t.Error("NewSubOrderWarpper should return an object that implements suborder.Wrapper interface")
	}
}

func TestSubOrderWarpper_RequestSplit_Success(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	wrapper := &subOrderWarpper{}
	order := &suborder.OrderRecord{
		OrderID: "test-order-id",
		UserID:  TestIamUserID,
	}

	request := &CreateInstanceParams{
		AppID:  TestAppID,
		UserID: TestIamUserID,
		Items: []CreateInstanceParamsItem{
			{
				Spec: specification.Specification{
					Name: "spec-1",
				},
				Engine: "redis",
				Count:  1,
			},
			{
				Spec: specification.Specification{
					Name: "spec-2",
				},
				Engine: "redis",
				Count:  2,
			},
			{
				Spec: specification.Specification{
					Name: "spec-1", // Same as first item
				},
				Engine: "redis",
				Count:  1,
			},
		},
	}

	splitRequests, err := wrapper.RequestSplit(ctx, order, request)
	if err != nil {
		t.Errorf("RequestSplit failed: %v", err)
	}

	// Should have 2 split requests (spec-1 and spec-2)
	if len(splitRequests) != 2 {
		t.Errorf("Expected 2 split requests, got %d", len(splitRequests))
	}

	// Verify spec-1 has 2 items (first and third)
	spec1Request, ok := splitRequests["spec-1"].(*CreateInstanceParams)
	if !ok {
		t.Error("Split request for spec-1 should be *CreateInstanceParams")
	}
	if len(spec1Request.Items) != 2 {
		t.Errorf("Expected 2 items for spec-1, got %d", len(spec1Request.Items))
	}

	// Verify spec-2 has 1 item
	spec2Request, ok := splitRequests["spec-2"].(*CreateInstanceParams)
	if !ok {
		t.Error("Split request for spec-2 should be *CreateInstanceParams")
	}
	if len(spec2Request.Items) != 1 {
		t.Errorf("Expected 1 item for spec-2, got %d", len(spec2Request.Items))
	}

	// Verify the split requests maintain other fields
	if spec1Request.AppID != TestAppID {
		t.Errorf("Expected AppID %s, got %s", TestAppID, spec1Request.AppID)
	}
	if spec2Request.UserID != TestIamUserID {
		t.Errorf("Expected UserID %s, got %s", TestIamUserID, spec2Request.UserID)
	}
}

func TestSubOrderWarpper_RequestSplit_InvalidRequest(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	wrapper := &subOrderWarpper{}
	order := &suborder.OrderRecord{}

	// Test with invalid request type
	invalidRequest := "not a CreateInstanceParams"

	_, err := wrapper.RequestSplit(ctx, order, invalidRequest)
	if err == nil {
		t.Error("Expected error for invalid request type, but got nil")
	}
}

func TestSubOrderWarpper_ResponseMerger_Success(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	wrapper := &subOrderWarpper{}
	order := &suborder.OrderRecord{}

	responses := map[string]interface{}{
		"spec-1": []ResInstance{
			{ID: "instance-1", Name: "test-1"},
			{ID: "instance-2", Name: "test-2"},
		},
		"spec-2": []ResInstance{
			{ID: "instance-3", Name: "test-3"},
		},
	}

	merged, err := wrapper.ResponseMerger(ctx, order, responses)
	if err != nil {
		t.Errorf("ResponseMerger failed: %v", err)
	}

	mergedInstances, ok := merged.([]ResInstance)
	if !ok {
		t.Error("Merged response should be []ResInstance")
	}

	if len(mergedInstances) != 3 {
		t.Errorf("Expected 3 merged instances, got %d", len(mergedInstances))
	}

	// Verify all instances are present
	instanceIDs := make(map[string]bool)
	for _, inst := range mergedInstances {
		instanceIDs[inst.ID] = true
	}

	expectedIDs := []string{"instance-1", "instance-2", "instance-3"}
	for _, id := range expectedIDs {
		if !instanceIDs[id] {
			t.Errorf("Expected instance ID %s not found in merged response", id)
		}
	}
}

func TestSubOrderWarpper_ResponseMerger_InvalidResponse(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	wrapper := &subOrderWarpper{}
	order := &suborder.OrderRecord{}

	responses := map[string]interface{}{
		"spec-1": "invalid response type",
	}

	_, err := wrapper.ResponseMerger(ctx, order, responses)
	if err == nil {
		t.Error("Expected error for invalid response type, but got nil")
	}
}

func TestSubOrderWarpper_ResponseMerger_EmptyResponse(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	wrapper := &subOrderWarpper{}
	order := &suborder.OrderRecord{}

	responses := map[string]interface{}{}

	_, err := wrapper.ResponseMerger(ctx, order, responses)
	if err == nil {
		t.Error("Expected error for empty response, but got nil")
	}
}

func TestSubOrderWarpper_SendSub_Success(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Setup test configuration
	GetSpecResourceConfig = func(azone, spec string) *SpecResourceConfig {
		return &SpecResourceConfig{
			AZone:       azone,
			Spec:        spec,
			ResourceTag: "test-tag",
		}
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().CreateInstance(ctx, gomock.Any()).Return(
		&x1ResourceSDK.CreateInstanceResponse{
			OrderId: TestOrderID,
		}, nil)

	// Create a test component with mock SDK
	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	subRequest := &CreateInstanceParams{
		AppID:  TestAppID,
		UserID: TestIamUserID,
		Items: []CreateInstanceParamsItem{
			{
				Spec: specification.Specification{
					Name: "test-spec",
				},
			},
		},
	}

	// Test CreateInstanceSub directly instead of going through Instance()
	realRequest, orderID, err := testComponent.CreateInstanceSub(ctx, subRequest)
	if err != nil {
		t.Errorf("CreateInstanceSub failed: %v", err)
	}

	if orderID != TestOrderID {
		t.Errorf("Expected orderID %s, got %s", TestOrderID, orderID)
	}

	if realRequest == nil {
		t.Error("Expected non-nil realRequest")
	}
}

func TestSubOrderWarpper_SendSub_InvalidJSON(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	wrapper := &subOrderWarpper{}
	order := &suborder.OrderRecord{}
	subOrder := &suborder.SubOrderRecord{
		Parameters: "invalid json",
	}

	_, _, err := wrapper.SendSub(ctx, order, subOrder)
	if err == nil {
		t.Error("Expected error for invalid JSON, but got nil")
	}
}

func TestSubOrderWarpper_QuerySub_Success(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().ShowCreateOrder(ctx, gomock.Any()).Return(
		&x1ResourceSDK.ShowCreateOrderResponse{
			Status: "Success",
			Instances: []x1ResourceSDK.Instance{
				{
					Id:   "instance-1",
					Name: "test-instance",
				},
			},
		}, nil)

	// Create a test component with mock SDK
	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	params := &ShowInstanceParams{
		UserID:  TestIamUserID,
		OrderID: TestOrderID,
	}

	// Test ShowCreateInstanceByOrderSub directly
	instances, err := testComponent.ShowCreateInstanceByOrderSub(ctx, params)
	if err != nil {
		t.Errorf("ShowCreateInstanceByOrderSub failed: %v", err)
	}

	if len(instances) != 1 {
		t.Errorf("Expected 1 instance, got %d", len(instances))
	}

	if instances[0].ID != "instance-1" {
		t.Errorf("Expected instance ID 'instance-1', got '%s'", instances[0].ID)
	}
}

func TestSubOrderWarpper_QuerySub_InOperation(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().ShowCreateOrder(ctx, gomock.Any()).Return(
		&x1ResourceSDK.ShowCreateOrderResponse{
			Status: "waiting",
		}, nil)

	// Create a test component with mock SDK
	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	params := &ShowInstanceParams{
		UserID:  TestIamUserID,
		OrderID: TestOrderID,
	}

	// Test ShowCreateInstanceByOrderSub directly
	_, err := testComponent.ShowCreateInstanceByOrderSub(ctx, params)
	if err == nil {
		t.Error("Expected ErrInstanceOrderInOperation, but got nil")
	}
	if !errors.Is(err, ErrInstanceOrderInOperation) {
		t.Errorf("Expected ErrInstanceOrderInOperation, got %v", err)
	}
}

func TestSubOrderWarpper_QuerySub_Failed(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().ShowCreateOrder(ctx, gomock.Any()).Return(
		&x1ResourceSDK.ShowCreateOrderResponse{
			Status: "Fail",
		}, nil)

	// Create a test component with mock SDK
	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	params := &ShowInstanceParams{
		UserID:  TestIamUserID,
		OrderID: TestOrderID,
	}

	// Test ShowCreateInstanceByOrderSub directly
	_, err := testComponent.ShowCreateInstanceByOrderSub(ctx, params)
	if err == nil {
		t.Error("Expected error for failed order, but got nil")
	}
}

func TestSubOrderWarpper_QuerySub_UnknownError(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().ShowCreateOrder(ctx, gomock.Any()).Return(
		nil, errors.New("unknown error"))

	// Create a test component with mock SDK
	testComponent := &component{
		x1ResourceSdk: mockX1InstanceSDK,
	}

	params := &ShowInstanceParams{
		UserID:  TestIamUserID,
		OrderID: TestOrderID,
	}

	// Test ShowCreateInstanceByOrderSub directly
	_, err := testComponent.ShowCreateInstanceByOrderSub(ctx, params)
	if err == nil {
		t.Error("Expected error for unknown error, but got nil")
	}
}
