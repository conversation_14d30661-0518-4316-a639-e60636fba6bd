package x1resource

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
)

// x1_resource.toml
//# pegadb使用指定的tag
//[[SpecResourceConfig]]
//AZone="*"
//Spec="pega.l5ds1.small"
//ResourceTag="xxx"
//DeviceWriteIOPS=10000000
//DeviceReadIOPS=100000000
//DeviceWriteIOBPS=100000000
//DeviceReadIOBPS=1000000000
//# DeviceWriteIOPSLimit=${DataDiskCapacityInGB}/${TotalCapacityInGB} * ${DeviceWriteIOPS} * ${DeviceWriteIOPSLimitRate}/1000
//DeviceWriteIOPSLimitRate=1000
//# DeviceReadIOPSLimit=${DataDiskCapacityInGB}/${TotalCapacityInGB} * ${DeviceReadIOPS} * ${DeviceReadIOPSLimitRate}/1000
//DeviceReadIOPSLimitRate=1000
//# DeviceWriteIOBPSLimit=${DataDiskCapacityInGB}/${TotalCapacityInGB} * ${DeviceWriteIOBPS} * ${DeviceWriteIOBPSLimitRate}/1000
//DeviceWriteIOBPSLimitRate=1000
//# DeviceReadIOBPSLimit=${DataDiskCapacityInGB}/${TotalCapacityInGB} * ${DeviceReadIOBPS} * ${DeviceREADIOBPSLimitRate}/1000
//DeviceReadIOBPSLimitRate=1000

type Config struct {
	SpecResourceConfigs []*SpecResourceConfig `toml:"SpecResourceConfig"`
	DeploySetTagPrefix  string                `toml:"DeploySetTagPrefix"`
}

type SpecResourceConfig struct {
	AZone                     string `toml:"AZone"`
	Spec                      string `toml:"Spec"`
	ResourceTag               string `toml:"ResourceTag"`
	DeviceCapacityInGB        int64  `toml:"DeviceCapacityInGB"`
	DeviceWriteIOPS           int64  `toml:"DeviceWriteIOPS,omitempty"`
	DeviceReadIOPS            int64  `toml:"DeviceReadIOPS,omitempty"`
	DeviceWriteIOBPS          int64  `toml:"DeviceWriteIOBPS,omitempty"`
	DeviceReadIOBPS           int64  `toml:"DeviceReadIOBPS,omitempty"`
	DeviceWriteIOPSLimitRate  int64  `toml:"DeviceWriteIOPSLimitRate,omitempty"`
	DeviceReadIOPSLimitRate   int64  `toml:"DeviceReadIOPSLimitRate,omitempty"`
	DeviceWriteIOBPSLimitRate int64  `toml:"DeviceWriteIOPSLimitRate,omitempty"`
	DeviceReadIOBPSLimitRate  int64  `toml:"DeviceReadIOPSLimitRate,omitempty"`
}

var (
	GetSpecResourceConfig func(azone, spec string) *SpecResourceConfig
	GetDeploySetTagPrefix func() string
)

func MustLoadConf(ctx context.Context) {
	config := &Config{}
	if err := compo_utils.LoadConf("x1-resource", config); err != nil {
		panic(err.Error())
	}
	GetSpecResourceConfig = func(azone, spec string) *SpecResourceConfig {
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == spec {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == spec {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == "*" {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == "*" {
				return item
			}
		}
		return &SpecResourceConfig{
			AZone:       azone,
			Spec:        spec,
			ResourceTag: "",
		}
	}
	GetDeploySetTagPrefix = func() string {
		if config.DeploySetTagPrefix == "" {
			return "x1-deployset-"
		}
		return config.DeploySetTagPrefix
	}
}
