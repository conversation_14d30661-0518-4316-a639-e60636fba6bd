/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * author: AI Assistant
 * Date: 2025-07-21
 * File: conf_test.go
 */

/*
 * DESCRIPTION
 *   x1resource configuration unit tests
 */

package x1resource

import (
	"context"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

func TestConfig_StructFields(t *testing.T) {
	config := &Config{
		SpecResourceConfigs: []*SpecResourceConfig{
			{
				AZone:                     "test-zone",
				Spec:                      "test-spec",
				ResourceTag:               "test-tag",
				DeviceCapacityInGB:        100,
				DeviceWriteIOPS:           1000,
				DeviceReadIOPS:            2000,
				DeviceWriteIOBPS:          3000,
				DeviceReadIOBPS:           4000,
				DeviceWriteIOPSLimitRate:  500,
				DeviceReadIOPSLimitRate:   600,
				DeviceWriteIOBPSLimitRate: 700,
				DeviceReadIOBPSLimitRate:  800,
			},
		},
		DeploySetTagPrefix: "test-prefix-",
	}

	if len(config.SpecResourceConfigs) != 1 {
		t.Errorf("Expected 1 SpecResourceConfig, got %d", len(config.SpecResourceConfigs))
	}

	spec := config.SpecResourceConfigs[0]
	if spec.AZone != "test-zone" {
		t.Errorf("Expected AZone 'test-zone', got '%s'", spec.AZone)
	}
	if spec.Spec != "test-spec" {
		t.Errorf("Expected Spec 'test-spec', got '%s'", spec.Spec)
	}
	if spec.ResourceTag != "test-tag" {
		t.Errorf("Expected ResourceTag 'test-tag', got '%s'", spec.ResourceTag)
	}
	if spec.DeviceCapacityInGB != 100 {
		t.Errorf("Expected DeviceCapacityInGB 100, got %d", spec.DeviceCapacityInGB)
	}
}

func TestMustLoadConf_Success(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Reset global functions before test
	GetSpecResourceConfig = nil
	GetDeploySetTagPrefix = nil

	// This should not panic with valid test configuration
	defer func() {
		if r := recover(); r != nil {
			t.Errorf("MustLoadConf panicked: %v", r)
		}
	}()

	MustLoadConf(ctx)

	// Verify that global functions are set
	if GetSpecResourceConfig == nil {
		t.Error("GetSpecResourceConfig function was not set")
	}
	if GetDeploySetTagPrefix == nil {
		t.Error("GetDeploySetTagPrefix function was not set")
	}
}

func TestMustLoadConf_PanicOnError(t *testing.T) {
	// This test demonstrates that MustLoadConf will panic if config file is missing
	// We skip this test in normal runs to avoid test failures
	t.Skip("Skipping panic test - this would cause test failure")

	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Reset global functions before test
	GetSpecResourceConfig = nil
	GetDeploySetTagPrefix = nil

	// This should panic due to missing configuration file
	defer func() {
		if r := recover(); r == nil {
			t.Error("Expected MustLoadConf to panic with missing config file, but it didn't")
		}
	}()

	// Try to load a non-existent config (this will panic)
	MustLoadConf(ctx)
}

func TestGetSpecResourceConfig_ExactMatch(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Setup test configuration
	config := &Config{
		SpecResourceConfigs: []*SpecResourceConfig{
			{
				AZone:       "zone-a",
				Spec:        "spec-1",
				ResourceTag: "exact-match",
			},
			{
				AZone:       "*",
				Spec:        "spec-1",
				ResourceTag: "wildcard-zone",
			},
		},
	}

	// Simulate the logic from MustLoadConf
	GetSpecResourceConfig = func(azone, spec string) *SpecResourceConfig {
		// Exact match first
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == spec {
				return item
			}
		}
		// Wildcard zone match
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == spec {
				return item
			}
		}
		// Wildcard spec match
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == "*" {
				return item
			}
		}
		// Both wildcard
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == "*" {
				return item
			}
		}
		// Default fallback
		return &SpecResourceConfig{
			AZone:       azone,
			Spec:        spec,
			ResourceTag: "",
		}
	}

	result := GetSpecResourceConfig("zone-a", "spec-1")
	if result.ResourceTag != "exact-match" {
		t.Errorf("Expected exact match, got ResourceTag: %s", result.ResourceTag)
	}
}

func TestGetSpecResourceConfig_WildcardZoneMatch(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	config := &Config{
		SpecResourceConfigs: []*SpecResourceConfig{
			{
				AZone:       "*",
				Spec:        "spec-1",
				ResourceTag: "wildcard-zone",
			},
		},
	}

	GetSpecResourceConfig = func(azone, spec string) *SpecResourceConfig {
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == spec {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == spec {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == "*" {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == "*" {
				return item
			}
		}
		return &SpecResourceConfig{
			AZone:       azone,
			Spec:        spec,
			ResourceTag: "",
		}
	}

	result := GetSpecResourceConfig("zone-b", "spec-1")
	if result.ResourceTag != "wildcard-zone" {
		t.Errorf("Expected wildcard zone match, got ResourceTag: %s", result.ResourceTag)
	}
}

func TestGetSpecResourceConfig_WildcardSpecMatch(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	config := &Config{
		SpecResourceConfigs: []*SpecResourceConfig{
			{
				AZone:       "zone-a",
				Spec:        "*",
				ResourceTag: "wildcard-spec",
			},
		},
	}

	GetSpecResourceConfig = func(azone, spec string) *SpecResourceConfig {
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == spec {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == spec {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == "*" {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == "*" {
				return item
			}
		}
		return &SpecResourceConfig{
			AZone:       azone,
			Spec:        spec,
			ResourceTag: "",
		}
	}

	result := GetSpecResourceConfig("zone-a", "spec-2")
	if result.ResourceTag != "wildcard-spec" {
		t.Errorf("Expected wildcard spec match, got ResourceTag: %s", result.ResourceTag)
	}
}

func TestGetSpecResourceConfig_BothWildcardMatch(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	config := &Config{
		SpecResourceConfigs: []*SpecResourceConfig{
			{
				AZone:       "*",
				Spec:        "*",
				ResourceTag: "both-wildcard",
			},
		},
	}

	GetSpecResourceConfig = func(azone, spec string) *SpecResourceConfig {
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == spec {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == spec {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == "*" {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == "*" {
				return item
			}
		}
		return &SpecResourceConfig{
			AZone:       azone,
			Spec:        spec,
			ResourceTag: "",
		}
	}

	result := GetSpecResourceConfig("zone-c", "spec-3")
	if result.ResourceTag != "both-wildcard" {
		t.Errorf("Expected both wildcard match, got ResourceTag: %s", result.ResourceTag)
	}
}

func TestGetSpecResourceConfig_DefaultFallback(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	config := &Config{
		SpecResourceConfigs: []*SpecResourceConfig{},
	}

	GetSpecResourceConfig = func(azone, spec string) *SpecResourceConfig {
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == spec {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == spec {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == "*" {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == "*" {
				return item
			}
		}
		return &SpecResourceConfig{
			AZone:       azone,
			Spec:        spec,
			ResourceTag: "",
		}
	}

	result := GetSpecResourceConfig("zone-d", "spec-4")
	if result.AZone != "zone-d" {
		t.Errorf("Expected AZone 'zone-d', got '%s'", result.AZone)
	}
	if result.Spec != "spec-4" {
		t.Errorf("Expected Spec 'spec-4', got '%s'", result.Spec)
	}
	if result.ResourceTag != "" {
		t.Errorf("Expected empty ResourceTag, got '%s'", result.ResourceTag)
	}
}

func TestGetDeploySetTagPrefix_WithCustomPrefix(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	config := &Config{
		DeploySetTagPrefix: "custom-prefix-",
	}

	GetDeploySetTagPrefix = func() string {
		if config.DeploySetTagPrefix == "" {
			return "x1-deployset-"
		}
		return config.DeploySetTagPrefix
	}

	result := GetDeploySetTagPrefix()
	if result != "custom-prefix-" {
		t.Errorf("Expected 'custom-prefix-', got '%s'", result)
	}
}

func TestGetDeploySetTagPrefix_WithDefaultPrefix(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	config := &Config{
		DeploySetTagPrefix: "",
	}

	GetDeploySetTagPrefix = func() string {
		if config.DeploySetTagPrefix == "" {
			return "x1-deployset-"
		}
		return config.DeploySetTagPrefix
	}

	result := GetDeploySetTagPrefix()
	if result != "x1-deployset-" {
		t.Errorf("Expected 'x1-deployset-', got '%s'", result)
	}
}

func TestGetSpecResourceConfig_PriorityOrder(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Test that exact match has higher priority than wildcard matches
	config := &Config{
		SpecResourceConfigs: []*SpecResourceConfig{
			{
				AZone:       "*",
				Spec:        "*",
				ResourceTag: "both-wildcard",
			},
			{
				AZone:       "*",
				Spec:        "spec-1",
				ResourceTag: "wildcard-zone",
			},
			{
				AZone:       "zone-a",
				Spec:        "*",
				ResourceTag: "wildcard-spec",
			},
			{
				AZone:       "zone-a",
				Spec:        "spec-1",
				ResourceTag: "exact-match",
			},
		},
	}

	GetSpecResourceConfig = func(azone, spec string) *SpecResourceConfig {
		// Exact match first (highest priority)
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == spec {
				return item
			}
		}
		// Wildcard zone match
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == spec {
				return item
			}
		}
		// Wildcard spec match
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == "*" {
				return item
			}
		}
		// Both wildcard (lowest priority)
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == "*" {
				return item
			}
		}
		return &SpecResourceConfig{
			AZone:       azone,
			Spec:        spec,
			ResourceTag: "",
		}
	}

	// Test exact match priority
	result := GetSpecResourceConfig("zone-a", "spec-1")
	if result.ResourceTag != "exact-match" {
		t.Errorf("Expected exact match priority, got ResourceTag: %s", result.ResourceTag)
	}

	// Test wildcard zone priority over wildcard spec
	result = GetSpecResourceConfig("zone-b", "spec-1")
	if result.ResourceTag != "wildcard-zone" {
		t.Errorf("Expected wildcard zone priority, got ResourceTag: %s", result.ResourceTag)
	}

	// Test wildcard spec priority over both wildcard
	result = GetSpecResourceConfig("zone-a", "spec-2")
	if result.ResourceTag != "wildcard-spec" {
		t.Errorf("Expected wildcard spec priority, got ResourceTag: %s", result.ResourceTag)
	}

	// Test both wildcard as fallback
	result = GetSpecResourceConfig("zone-c", "spec-3")
	if result.ResourceTag != "both-wildcard" {
		t.Errorf("Expected both wildcard fallback, got ResourceTag: %s", result.ResourceTag)
	}
}

func TestSpecResourceConfig_AllFields(t *testing.T) {
	spec := &SpecResourceConfig{
		AZone:                     "test-zone",
		Spec:                      "test-spec",
		ResourceTag:               "test-tag",
		DeviceCapacityInGB:        100,
		DeviceWriteIOPS:           1000,
		DeviceReadIOPS:            2000,
		DeviceWriteIOBPS:          3000,
		DeviceReadIOBPS:           4000,
		DeviceWriteIOPSLimitRate:  500,
		DeviceReadIOPSLimitRate:   600,
		DeviceWriteIOBPSLimitRate: 700,
		DeviceReadIOBPSLimitRate:  800,
	}

	// Test all fields are properly set
	if spec.AZone != "test-zone" {
		t.Errorf("Expected AZone 'test-zone', got '%s'", spec.AZone)
	}
	if spec.Spec != "test-spec" {
		t.Errorf("Expected Spec 'test-spec', got '%s'", spec.Spec)
	}
	if spec.ResourceTag != "test-tag" {
		t.Errorf("Expected ResourceTag 'test-tag', got '%s'", spec.ResourceTag)
	}
	if spec.DeviceCapacityInGB != 100 {
		t.Errorf("Expected DeviceCapacityInGB 100, got %d", spec.DeviceCapacityInGB)
	}
	if spec.DeviceWriteIOPS != 1000 {
		t.Errorf("Expected DeviceWriteIOPS 1000, got %d", spec.DeviceWriteIOPS)
	}
	if spec.DeviceReadIOPS != 2000 {
		t.Errorf("Expected DeviceReadIOPS 2000, got %d", spec.DeviceReadIOPS)
	}
	if spec.DeviceWriteIOBPS != 3000 {
		t.Errorf("Expected DeviceWriteIOBPS 3000, got %d", spec.DeviceWriteIOBPS)
	}
	if spec.DeviceReadIOBPS != 4000 {
		t.Errorf("Expected DeviceReadIOBPS 4000, got %d", spec.DeviceReadIOBPS)
	}
	if spec.DeviceWriteIOPSLimitRate != 500 {
		t.Errorf("Expected DeviceWriteIOPSLimitRate 500, got %d", spec.DeviceWriteIOPSLimitRate)
	}
	if spec.DeviceReadIOPSLimitRate != 600 {
		t.Errorf("Expected DeviceReadIOPSLimitRate 600, got %d", spec.DeviceReadIOPSLimitRate)
	}
	if spec.DeviceWriteIOBPSLimitRate != 700 {
		t.Errorf("Expected DeviceWriteIOBPSLimitRate 700, got %d", spec.DeviceWriteIOBPSLimitRate)
	}
	if spec.DeviceReadIOBPSLimitRate != 800 {
		t.Errorf("Expected DeviceReadIOBPSLimitRate 800, got %d", spec.DeviceReadIOBPSLimitRate)
	}
}

func TestMustLoadConf_Integration(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Reset global functions before test
	GetSpecResourceConfig = nil
	GetDeploySetTagPrefix = nil

	// Load configuration
	MustLoadConf(ctx)

	// Test that functions are properly initialized and work with real config
	if GetSpecResourceConfig == nil {
		t.Fatal("GetSpecResourceConfig function was not set")
	}
	if GetDeploySetTagPrefix == nil {
		t.Fatal("GetDeploySetTagPrefix function was not set")
	}

	// Test GetDeploySetTagPrefix with loaded config
	prefix := GetDeploySetTagPrefix()
	if prefix == "" {
		t.Error("GetDeploySetTagPrefix returned empty string")
	}

	// Test GetSpecResourceConfig with loaded config
	result := GetSpecResourceConfig("test-zone", "test-spec")
	if result == nil {
		t.Error("GetSpecResourceConfig returned nil")
	}
	if result.AZone == "" {
		t.Error("GetSpecResourceConfig returned config with empty AZone")
	}
	if result.Spec == "" {
		t.Error("GetSpecResourceConfig returned config with empty Spec")
	}
}

func TestMustLoadConf_WithRealConfig(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	// Reset global functions before test
	GetSpecResourceConfig = nil
	GetDeploySetTagPrefix = nil

	// Load configuration
	MustLoadConf(ctx)

	// Test with exact match from our test config
	result := GetSpecResourceConfig("AZONE-bjyz", "pega.l5ds1.small")
	if result.ResourceTag != "test-tag-exact" {
		t.Errorf("Expected exact match from config, got ResourceTag: %s", result.ResourceTag)
	}
	if result.DeviceCapacityInGB != 100 {
		t.Errorf("Expected DeviceCapacityInGB 100, got %d", result.DeviceCapacityInGB)
	}

	// Test with wildcard zone match
	result = GetSpecResourceConfig("AZONE-unknown", "pega.l5ds1.medium")
	if result.ResourceTag != "test-tag-wildcard-zone" {
		t.Errorf("Expected wildcard zone match from config, got ResourceTag: %s", result.ResourceTag)
	}

	// Test with wildcard spec match
	result = GetSpecResourceConfig("AZONE-gzyz", "unknown-spec")
	if result.ResourceTag != "test-tag-wildcard-spec" {
		t.Errorf("Expected wildcard spec match from config, got ResourceTag: %s", result.ResourceTag)
	}

	// Test with both wildcard match
	result = GetSpecResourceConfig("unknown-zone", "unknown-spec")
	if result.ResourceTag != "test-tag-both-wildcard" {
		t.Errorf("Expected both wildcard match from config, got ResourceTag: %s", result.ResourceTag)
	}

	// Test GetDeploySetTagPrefix with custom prefix from config
	prefix := GetDeploySetTagPrefix()
	if prefix != "test-deployset-" {
		t.Errorf("Expected custom prefix from config 'test-deployset-', got '%s'", prefix)
	}
}

func TestConfig_EmptyConfig(t *testing.T) {
	config := &Config{}

	if config.SpecResourceConfigs != nil && len(config.SpecResourceConfigs) != 0 {
		t.Error("Expected empty SpecResourceConfigs for new Config")
	}
	if config.DeploySetTagPrefix != "" {
		t.Error("Expected empty DeploySetTagPrefix for new Config")
	}
}

func TestSpecResourceConfig_ZeroValues(t *testing.T) {
	spec := &SpecResourceConfig{}

	if spec.AZone != "" {
		t.Error("Expected empty AZone for new SpecResourceConfig")
	}
	if spec.Spec != "" {
		t.Error("Expected empty Spec for new SpecResourceConfig")
	}
	if spec.ResourceTag != "" {
		t.Error("Expected empty ResourceTag for new SpecResourceConfig")
	}
	if spec.DeviceCapacityInGB != 0 {
		t.Error("Expected zero DeviceCapacityInGB for new SpecResourceConfig")
	}
	if spec.DeviceWriteIOPS != 0 {
		t.Error("Expected zero DeviceWriteIOPS for new SpecResourceConfig")
	}
	if spec.DeviceReadIOPS != 0 {
		t.Error("Expected zero DeviceReadIOPS for new SpecResourceConfig")
	}
}

func TestGetSpecResourceConfig_EdgeCases(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	config := &Config{
		SpecResourceConfigs: []*SpecResourceConfig{
			{
				AZone:       "",
				Spec:        "",
				ResourceTag: "empty-strings",
			},
		},
	}

	GetSpecResourceConfig = func(azone, spec string) *SpecResourceConfig {
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == spec {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == spec {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == azone && item.Spec == "*" {
				return item
			}
		}
		for _, item := range config.SpecResourceConfigs {
			if item.AZone == "*" && item.Spec == "*" {
				return item
			}
		}
		return &SpecResourceConfig{
			AZone:       azone,
			Spec:        spec,
			ResourceTag: "",
		}
	}

	// Test with empty strings
	result := GetSpecResourceConfig("", "")
	if result.ResourceTag != "empty-strings" {
		t.Errorf("Expected match with empty strings, got ResourceTag: %s", result.ResourceTag)
	}

	// Test with nil-like behavior (empty strings should still work)
	result = GetSpecResourceConfig("", "some-spec")
	if result.AZone != "" || result.Spec != "some-spec" {
		t.Errorf("Expected fallback behavior for empty zone, got AZone: '%s', Spec: '%s'", result.AZone, result.Spec)
	}
}
