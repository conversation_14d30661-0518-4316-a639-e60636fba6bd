package dns

import (
	"context"
	"fmt"
	//"time"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

const (
	TestIamUserID = "ea2c4a2286ca4540afcb7f7d4ba2d199"
	TestVpcID     = "58d53a2e-27fe-4ead-9041-c72ec67ec1c1"
	TestName      = "redis.testname.scs.bj.baidubce.com"
	TestInetName  = "redis.testname-inet"
	TestEIP       = "***********"
)

func TestCreateDomain(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	dnsOp := Instance()

	// case 1
	createReq := CreateDomainParam{
		UserID:     TestIamUserID,
		VpcID:      TestVpcID,
		BindIp:     "***********",
		Domain:     TestName,
		EnableIpv6: false,
		BindIpIpv6: "",
	}
	err := dnsOp.CreateDomain(ctx, &createReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("success")
}

func TestDeleteDomain(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	dnsOp := Instance()

	// case 1
	deleteReq := DeleteDomainParam{
		UserID:     TestIamUserID,
		VpcID:      TestVpcID,
		BindIp:     "***********",
		Domain:     TestName,
		EnableIpv6: false,
		BindIpIpv6: "",
	}
	err := dnsOp.DeleteDomain(ctx, &deleteReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("success")
}

func TestCreateInetDomain(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	dnsOp := Instance()

	// case 1
	createReq := CreateInetDomainParam{
		UserID: TestIamUserID,
		Domain: TestInetName,
		Eip:    TestEIP,
	}
	err := dnsOp.CreateInetDomain(ctx, &createReq)
	if err != nil {
		fmt.Println(err)
		return
	}

	// retry is OK
	err = dnsOp.CreateInetDomain(ctx, &createReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("success")
}

func TestDeleteInetDomain(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	dnsOp := Instance()

	// case 1
	deleteReq := DeleteInetDomainParam{
		UserID: TestIamUserID,
		Domain: TestInetName,
		Eip:    TestEIP,
	}
	err := dnsOp.DeleteInetDomain(ctx, &deleteReq)
	if err != nil {
		fmt.Println(err)
		return
	}

	// retry is OK
	err = dnsOp.DeleteInetDomain(ctx, &deleteReq)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("success")
}
