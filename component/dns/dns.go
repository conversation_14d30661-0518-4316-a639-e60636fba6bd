/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2021/12/30
 * File: dns.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package dns TODO package function desc
package dns

import (
	"context"
	"strings"
	"sync"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/dbstack/dns/adaptor"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc/neutron"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/dns"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
)

type config struct {
	Zone     string
	Ttl      int32
	PnetTtl  int32
	PnetZone string
	InetZone string
}

type component struct {
	conf       *config
	dnsSdk     dns.DNSService
	dnsPnetSdk dns.DNSService
	neutronSdk bcc.NeutronService
	initOnce   sync.Once
}

type CreateDomainParam struct {
	UserID     string
	VpcID      string
	BindIp     string // elb_pnetip
	Domain     string
	EnableIpv6 bool
	BindIpIpv6 string // elb_ipv6
}

type DeleteDomainParam struct {
	UserID     string
	VpcID      string
	BindIp     string // elb_pnetip
	Domain     string
	EnableIpv6 bool
	BindIpIpv6 string // elb_ipv6
}

type CreateInetDomainParam struct {
	UserID string
	Eip    string
	Domain string
}

type DeleteInetDomainParam struct {
	UserID string
	Eip    string
	Domain string
}

var defaultComponent = &component{
	conf: &config{},
}

// Instance 获取默认的dns组件，必须在gdp初始化env和ral后使用
func Instance() *component {
	defaultComponent.initOnce.Do(func() {
		// load default conf
		if err := compo_utils.LoadConf("dns", defaultComponent.conf); err != nil {
			panic(err.Error())
		}

		// default elb sdk
		defaultComponent.dnsSdk = dns.NewDefaultDnsSdk()
		defaultComponent.dnsPnetSdk = dns.NewDefaultPnetDnsSdk()
		defaultComponent.neutronSdk = neutron.NewDefaultNeutronSdk()

		if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
			defaultComponent.dnsSdk = adaptor.GetDbstackDnsAdaptor()
			defaultComponent.dnsPnetSdk = adaptor.GetDbstackDnsAdaptor()
		}
	})

	return defaultComponent
}

func (c *component) CreateDomain(ctx context.Context, params *CreateDomainParam) (err error) {
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     params.UserID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return
	}
	auth := authRsp.Auth

	if params.VpcID == "" {
		err = cerrs.ErrNotFound.Errorf("VpcID is empty")
		return err
	}

	listReq := dns.DNSListRequest{
		VpcID: params.VpcID,
		Name:  params.Domain,
		Auth:  auth,
	}
	logger.ComponentLogger.Trace(ctx, "Create Domain Req: %+v", *params)
	dnsListRsp, err := c.dnsSdk.ListDomain(ctx, &listReq)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "list domain fail,err:%s", err.Error())
		return err
	}

	hasBindV4 := false
	hasBindV6 := false
	if dnsListRsp != nil {
		logger.ComponentLogger.Trace(ctx, "list rsp ret: %+v", *dnsListRsp)

		for _, dnsRecordPtr := range dnsListRsp.Records {
			if dnsRecordPtr.Type == Ipv4RdType {
				if params.BindIp == dnsRecordPtr.Value {
					hasBindV4 = true
				}
			}
			if dnsRecordPtr.Type == Ipv6RdType {
				if params.BindIpIpv6 == dnsRecordPtr.Value {
					hasBindV6 = true
				}
			}
		}
	}

	if !hasBindV4 && params.BindIp != "" {
		createDnsDomainReq := dns.DNSCreateRequest{
			VpcID:  params.VpcID,
			Name:   params.Domain,
			Domain: c.conf.Zone,
			Type:   Ipv4RdType,
			Value:  params.BindIp,
			TTL:    c.conf.Ttl,
			Auth:   auth,
		}
		_, err = c.dnsSdk.CreateDomain(ctx, &createDnsDomainReq)
		if err != nil {
			return err
		}
	}

	if !hasBindV6 && params.EnableIpv6 && params.BindIpIpv6 != "" {
		createDnsDomainReq := dns.DNSCreateRequest{
			VpcID:  params.VpcID,
			Name:   params.Domain,
			Domain: c.conf.Zone,
			Type:   Ipv6RdType,
			Value:  params.BindIpIpv6,
			TTL:    c.conf.Ttl,
			Auth:   auth,
		}
		_, err = c.dnsSdk.CreateDomain(ctx, &createDnsDomainReq)
		if err != nil {
			return err
		}
	}

	return
}

func (c *component) DeleteDomain(ctx context.Context, params *DeleteDomainParam) (err error) {
	authRsp, err := sts.NewDefaultStsSdk().GetOpenApiAuth(ctx, &sts.GetOpenApiAuthRequest{
		TransactionId: uuid.NewString(),
		IamUserId:     params.UserID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get auth fail,err:%s", err.Error())
		return
	}
	auth := authRsp.Auth

	listReq := dns.DNSListRequest{
		VpcID: params.VpcID,
		Name:  params.Domain,
		Auth:  auth,
	}
	logger.ComponentLogger.Trace(ctx, "Delete Domain Req: %+v", *params)
	dnsListRsp, err := c.dnsSdk.ListDomain(ctx, &listReq)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		logger.ComponentLogger.Warning(ctx, "list domain fail,err:%s", err.Error())
		return err
	}

	if dnsListRsp != nil {
		logger.ComponentLogger.Trace(ctx, "list rsp ret: %+v", *dnsListRsp)

		for _, record := range dnsListRsp.Records {
			if record.Type == Ipv4RdType && record.Value == params.BindIp {
				err = c.deleteDomainById(ctx, record.ID, auth)
				if err != nil {
					logger.ComponentLogger.Warning(ctx, "Del Ipv4 Dns Record Fail, record: %+v", *record)
					return err
				}
			}
			if record.Type == Ipv6RdType && record.Value == params.BindIpIpv6 {
				err = c.deleteDomainById(ctx, record.ID, auth)
				if err != nil {
					logger.ComponentLogger.Warning(ctx, "Del Ipv6 Dns Record Fail, record: %+v", *record)
					return err
				}
			}
		}
	}

	return nil
}

func (c *component) deleteDomainById(ctx context.Context, id string, auth *common.Authentication) error {
	delReq := dns.DNSDeleteRequest{
		ID:   id,
		Auth: auth,
	}
	logger.ComponentLogger.Trace(ctx, "Start Del Dns Record req: %+v", delReq)
	err := c.dnsSdk.DeleteDomain(ctx, &delReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Del Dns Record Fail, req: %+v", delReq)
		return err
	}
	logger.ComponentLogger.Trace(ctx, "Del Dns Record Suc req: %+v", delReq)
	return nil
}

func (c *component) CreateInetDomain(ctx context.Context, params *CreateInetDomainParam) (err error) {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get token fail,err:%s", err.Error())
		return
	}

	createPnetDomainReq := dns.PnetResquest{
		Domain: params.Domain,
		Zone:   c.conf.PnetZone,
		Type:   "A",
		Ttl:    c.conf.PnetTtl,
		Rdata:  params.Eip,
		View:   "inet",
		Token:  token,
	}

	err = c.dnsPnetSdk.CreatePnetDomain(ctx, &createPnetDomainReq)
	if err != nil {
		// {"ErrNum":401,"ErrMsg":"This type and rdata is already","RetData":null,"UrlManual":"","UrlRedirect":"","RespBodyType":0}
		if strings.Contains(err.Error(), "This type and rdata is already") {
			logger.ComponentLogger.Warning(ctx, "%s type and rdata is already", params.Domain)
			return nil
		}
		return err
	}

	return
}

func (c *component) DeleteInetDomain(ctx context.Context, params *DeleteInetDomainParam) (err error) {
	token, err := compo_utils.GetOpenapiToken(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get token fail,err:%s", err.Error())
		return
	}

	deletePnetDomainReq := dns.PnetResquest{
		Domain: params.Domain,
		Zone:   c.conf.PnetZone,
		Type:   "A",
		Ttl:    c.conf.PnetTtl,
		Rdata:  params.Eip,
		View:   "inet",
		Token:  token,
	}

	err = c.dnsPnetSdk.DeletePnetDomain(ctx, &deletePnetDomainReq)
	if err != nil {
		// {"ErrNum":401,"ErrMsg":"RR is not exit","RetData":null,"UrlManual":"","UrlRedirect":"","RespBodyType":0}
		if strings.Contains(err.Error(), "RR is not exit") {
			logger.ComponentLogger.Warning(ctx, "%s is not exist", params.Domain)
			return nil
		}
		return err
	}

	return
}
