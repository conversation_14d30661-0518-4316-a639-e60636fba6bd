package bnsresource

// bns node 配置
const (
	bnsNodeType = "service"
)

type ParentConfig struct {
	Path  string
	Token string
}

type GroupConfig struct {
	DeleteLimit       bool
	ConfIsJson        int //  缺省为1，进行json校验
	ConstrainGroup    int //  0：表示不限制，缺省值   1：表示限制
	OpenDeadhostCheck int //  0：表示不开启，缺省值   1：表示开启
}

type ServiceConfig struct {
	UseDynamicNode bool
}

type InstanceConfig struct {
	DefaultStatus  int
	DefaultRunUser string
}

type BnsConfig struct {
	ParentConfig   *ParentConfig
	GroupConfig    *GroupConfig
	InstanceConfig *InstanceConfig
	ServiceConfig  *ServiceConfig
}

var (
	config *BnsConfig
)
