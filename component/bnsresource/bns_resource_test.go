package bnsresource

//func TestBnsNodeCreate(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	rsp, err := bccOp.BnsNodeCreate(ctx, &CreateBnsNodeParam{
//		ClusterName: "ral-test-ttt",
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		return
//	}
//
//	fmt.Println("BnsNodeCreate resp:", base_utils.Format(rsp))
//}
//func TestBnsServiceCreate(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsServiceCreate(ctx, &BnsServiceParam{
//		ClusterName: "ral_test",
//		Region:      "yq",
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		t.<PERSON>("expect nil error, actual %s", err.Error())
//	}
//}
//
//func TestBnsServiceDelete(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsServiceDelete(ctx, &BnsServiceParam{
//		ClusterName: "ral_test",
//		Region:      "bj",
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}
//
//func TestBnsServiceAddInstance(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsServiceAddInstance(ctx, &BnsAddInstanceParam{
//		ClusterName: "ral_test",
//		Region:      "nj",
//		HostName:    "nj02-bdrp-share59.nj02",
//		Port: map[string]string{
//			"main": "1236",
//			"test": "7787",
//		},
//		Tag: map[string]string{
//			"module": "test1",
//			"test":   "proxy",
//		},
//		Disable:         1,
//		InstanceId:      5,
//		DeployPath:      "/home/<USER>/redis_bbb",
//		HealthCheckCmd:  "prot:6666",
//		HealthCheckType: "proc",
//		ContainerId:     "x1-base-test-55",
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}
//
//func TestBnsServiceClearInstance(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsServiceClearInstance(ctx, &BnsServiceParam{
//		ClusterName: "ral_test",
//		Region:      "nj",
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}
//
//func TestBnsServiceDeleteInstance(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsServiceDeleteInstance(ctx, &BnsDeleteInstanceParam{
//		ClusterName: "ral_test",
//		Region:      "nj",
//		HostName:    "nj02-bdrp-share59.nj02",
//		InstanceId:  1,
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}
//
//func TestBnsGroupModifyServices(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsGroupModifyServices(ctx, &ModifyBnsGroupParam{
//		ClusterName: "ral-test",
//		Regions: []string{
//			"yq",
//			"bd",
//		},
//
//		Action: "add",
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}
//
//func TestBnsGroupCreate(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsGroupCreate(ctx, &CreateBnsGroupParam{
//		ClusterName: "ral-test",
//		Regions: []string{
//			"gz",
//			"bj",
//			"nj",
//		},
//		Threshold:        5,
//		ThresholdPercent: 20,
//		ProtocolName:     "nshead_test",
//		ConverterName:    "mcpack2_test",
//		ServiceRtimeout:  100,
//		ServiceCtimeout:  50,
//		ServiceWtimeout:  60,
//		ServiceRetry:     4,
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}
//
//func TestBnsGroupDelete(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BnsGroupDelete(ctx, &DeleteBnsGroupParam{
//		ClusterName: "ral-test",
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}
//
//func TestBnsServiceInstanceInfo(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	rsp, err := bccOp.BnsServiceInstanceInfo(ctx, &BnsServiceParam{
//		ClusterName: "ral_test",
//		Region:      "nj",
//	})
//
//	if err != nil {
//		fmt.Println(err)
//		return
//	}
//
//	fmt.Println("BnsServiceInstanceInfo resp:", base_utils.Format(rsp))
//}
//
//func TestDisableAndEnableInstance(t *testing.T) {
//	ctx := context.Background()
//	defer sdk_utils.TestEnvDefer(ctx)()
//
//	bccOp := BnsResourceOp()
//
//	err := bccOp.BatchDisableAndEnableInstancesByContainerId(ctx, &InstanceDisableAndEnableParam{
//		ClusterName: "ral_test",
//		Region:      "nj",
//		Enable:      "enable",
//		ContainerIds: []string{
//			"x1-base-test-88",
//			"x1-base-test-99",
//		},
//	})
//
//	// test2
//	// rsp, err := bccOp.BatchDisableAndEnableInstancesByContainerId(ctx, nil)
//
//	if err != nil {
//		fmt.Println(err)
//		t.Errorf("expect nil error, actual %s", err.Error())
//	}
//}
