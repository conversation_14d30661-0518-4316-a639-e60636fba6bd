package bnsresource

import (
	"context"
	"strings"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bns"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type BnsResource interface {
	BnsNodeCreate(ctx context.Context, req *CreateBnsNodeParam) (rsp *bns.BnsGeneralResponse2, err error)
	BnsServiceCreate(ctx context.Context, req *BnsServiceParam) error
	BnsServiceDelete(ctx context.Context, req *BnsServiceParam) error
	// BnsServiceDelete(ctx context.Context, req *bns.BnsDeleteRequest) (rsp *bns.BnsGeneralResponse, err error)
	BnsServiceAddInstance(ctx context.Context, req *BnsAddInstanceParam) error
	// BnsServiceAddInstance(ctx context.Context, req *bns.BnsAddInstanceRequest) (rsp *bns.BnsAddInstanceResponse, err error)
	BnsServiceClearInstance(ctx context.Context, req *BnsServiceParam) error
	// BnsServiceClearInstance(ctx context.Context, req *bns.BnsClearInstanceRequest) (rsp *bns.BnsGeneralResponse, err error)
	BnsServiceDeleteInstance(ctx context.Context, req *BnsDeleteInstanceParam) error
	// BnsServiceDeleteInstance(ctx context.Context, req *bns.BnsDeleteInstanceRequest) (rsp *bns.BnsGeneralResponse, err error)
	BnsGroupModifyServices(ctx context.Context, req *ModifyBnsGroupParam) error
	// BnsGroupModifyServices(ctx context.Context, req *bns.BnsGroupModifyServicesRequest) (rsp *bns.BnsGeneralResponse2, err error)
	BnsGroupCreate(ctx context.Context, req *CreateBnsGroupParam) error
	// BnsGroupCreate(ctx context.Context, req *bns.BnsGroupCreateRequest) (rsp *bns.BnsGeneralResponse, err error)
	BnsGroupDelete(ctx context.Context, req *DeleteBnsGroupParam) error
	// BnsGroupDelete(ctx context.Context, req *bns.BnsGroupDeleteRequest) (rsp *bns.BnsGeneralResponse, err error)
	BnsServiceInstanceInfo(ctx context.Context, req *BnsServiceParam) (rsp *bns.BnsServiceInstanceInfoResponse, err error)
	// BnsServiceInstanceInfo(ctx context.Context, req *bns.BnsServiceInstanceInfoRequest) (rsp *bns.BnsServiceInstanceInfoResponse, err error)
	BatchDisableAndEnableInstancesByContainerId(ctx context.Context, req *InstanceDisableAndEnableParam) error
	// BatchDisableAndEnableInstancesByContainerId(ctx context.Context, req *bns.InstanceDisableAndEnableRequest) (rsp *bns.BnsGeneralResponse, err error)
	GenerateDefaultIdcMap(bnsGroupConf *bns.BnsGroupConfList) (idcMap string, err error)
}

type bnsResourceOp struct {
	bnsSdk bns.BnsService
	nodeSdk bns.BnsService
}

var bnsResourceOpObj BnsResource

var once sync.Once

func BnsResourceOp() BnsResource {
	once.Do(func() {
		config = &BnsConfig{}
		if err := compo_utils.LoadConf("blb", config); err != nil {
			panic(err.Error())
		}
		bnsResourceOpObj = &bnsResourceOp{
			bnsSdk: bns.NewBefaultBnsSdk(),
			nodeSdk: bns.NewBnsSdk(bns.SpecialServiceName),
		}

	})
	return bnsResourceOpObj
}

func (br *bnsResourceOp) BnsNodeCreate(ctx context.Context, req *CreateBnsNodeParam) (rsp *bns.BnsGeneralResponse2, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.ClusterName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param clusterName is null")
	}

	rsp, err = br.nodeSdk.BnsNodeCreate(ctx, &bns.BnsNodeCreateRequest{
		ParentPath: config.ParentConfig.Path,
		Token:      config.ParentConfig.Token,
		NodeName:   req.ClusterName,
		Type:       bnsNodeType,
	})

	if rsp == nil {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsNodeCreate request fail,req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}

	if !rsp.Success {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsNodeCreate request fail,req:%s , BnsError RetCode: %t , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.Success, rsp.Message)
	}
	return
}

func (br *bnsResourceOp) BnsServiceCreate(ctx context.Context, req *BnsServiceParam) error {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.ClusterName == "" {
		return cerrs.ErrInvalidParams.Errorf("req param clusterName is null")
	}
	if req.Region == "" {
		return cerrs.ErrInvalidParams.Errorf("req param region is null")
	}

	request := &bns.BnsCreateRequest{
		ParentPath: config.ParentConfig.Path,
		AuthKey: config.ParentConfig.Token,
		NodeName: GenerateBnsServiceName(req),
	}
	if config.ServiceConfig.UseDynamicNode {
		request.ParentPath += "_" + GenerateBnsNodeName(req.ClusterName)
	}

	rsp, err := br.bnsSdk.BnsServiceCreate(ctx, request)

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceCreate request fail,req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceCreate request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) BnsServiceDelete(ctx context.Context, req *BnsServiceParam) error {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.ClusterName == "" {
		return cerrs.ErrInvalidParams.Errorf("req param clusterName is null")
	}
	if req.Region == "" {
		return cerrs.ErrInvalidParams.Errorf("req param region is null")
	}

	rsp, err := br.bnsSdk.BnsServiceDelete(ctx, &bns.BnsDeleteRequest{
		ServiceName: GenerateBnsServiceName(req),
		AuthKey:     config.ParentConfig.Token,
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceDelete request fail,req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceDelete request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) BnsServiceAddInstance(ctx context.Context, req *BnsAddInstanceParam) error {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.ClusterName == "" {
		return cerrs.ErrInvalidParams.Errorf("req param clusterName is null")
	}
	if req.Region == "" {
		return cerrs.ErrInvalidParams.Errorf("req param region is null")
	}

	bnsServiceName := GenerateBnsServiceName(&BnsServiceParam{
		ClusterName: req.ClusterName,
		Region:      req.Region,
	})

	rsp, err := br.bnsSdk.BnsServiceAddInstance(ctx, &bns.BnsAddInstanceRequest{
		ServiceName: bnsServiceName,
		AuthKey:     config.ParentConfig.Token,
		InstanceInfo: &bns.BnsInstanceInfo{
			HostName:        req.HostName,
			Port:            req.Port,
			Tag:             req.Tag,
			Disable:         req.Disable,
			InstanceId:      req.InstanceId,
			Status:          config.InstanceConfig.DefaultStatus,
			DeployPath:      req.DeployPath,
			RunUser:         config.InstanceConfig.DefaultRunUser,
			HealthCheckCmd:  req.HealthCheckCmd,
			HealthCheckType: req.HealthCheckType,
			ContainerId:     req.ContainerId,
		},
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceAddInstance request fail,req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceAddInstance request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) BnsServiceClearInstance(ctx context.Context, req *BnsServiceParam) error {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.ClusterName == "" {
		return cerrs.ErrInvalidParams.Errorf("req param clusterName is null")
	}
	if req.Region == "" {
		return cerrs.ErrInvalidParams.Errorf("req param region is null")
	}

	bnsServiceName := GenerateBnsServiceName(req)

	rsp, err := br.bnsSdk.BnsServiceClearInstance(ctx, &bns.BnsClearInstanceRequest{
		ServiceName: bnsServiceName,
		AuthKey:     config.ParentConfig.Token,
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceClearInstance request fail,req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceClearInstance request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) BnsServiceDeleteInstance(ctx context.Context, req *BnsDeleteInstanceParam) error {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.ClusterName == "" {
		return cerrs.ErrInvalidParams.Errorf("req param clusterName is null")
	}
	if req.Region == "" {
		return cerrs.ErrInvalidParams.Errorf("req param region is null")
	}

	bnsServiceName := GenerateBnsServiceName(&BnsServiceParam{
		ClusterName: req.ClusterName,
		Region:      req.Region,
	})

	rsp, err := br.bnsSdk.BnsServiceDeleteInstance(ctx, &bns.BnsDeleteInstanceRequest{
		ServiceName: bnsServiceName,
		AuthKey:     config.ParentConfig.Token,
		HostName:    req.HostName,
		InstanceId:  req.InstanceId,
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceDeleteInstance request fail,req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BnsServiceDeleteInstance request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) BnsGroupModifyServices(ctx context.Context, req *ModifyBnsGroupParam) error {

	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.ClusterName == "" {
		return cerrs.ErrInvalidParams.Errorf("req param clusterName is null")
	}

	bnsGroupName := GenerateBnsGroupName(req.ClusterName)

	var serviceNames []string

	for _, region := range req.Regions {
		bnsServiceName := GenerateBnsServiceName(&BnsServiceParam{
			ClusterName: req.ClusterName,
			Region:      region,
		})
		serviceNames = append(serviceNames, bnsServiceName)
	}

	rsp, err := br.bnsSdk.BnsGroupModifyServices(ctx, &bns.BnsGroupModifyServicesRequest{
		GroupName:    bnsGroupName,
		AuthKey:      config.ParentConfig.Token,
		ServiceNames: serviceNames,
		Action:       req.Action,
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsGroupModifyServices request fail,req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if !rsp.Success {
		return cerrs.ErrBNSRequestFail.Errorf("BnsGroupModifyServices request fail,req:%s , BnsError Success: %t , BnsError ErrorMessage: %s",
			base_utils.Format(req), rsp.Success, rsp.Message)
	}
	return nil
}

func (br *bnsResourceOp) BnsGroupCreate(ctx context.Context, req *CreateBnsGroupParam) error {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.ClusterName == "" {
		return cerrs.ErrInvalidParams.Errorf("req param clusterName is null")
	}

	bnsGroupName := GenerateBnsGroupName(req.ClusterName)

	var serviceNames []string

	for _, region := range req.Regions {
		bnsServiceName := GenerateBnsServiceName(&BnsServiceParam{
			ClusterName: req.ClusterName,
			Region:      region,
		})
		serviceNames = append(serviceNames, bnsServiceName)
	}

	nodeName := GenerateBnsNodeName(req.ClusterName)
	nodePath := config.ParentConfig.Path + "_" + nodeName

	rsp, err := br.bnsSdk.BnsGroupCreate(ctx, &bns.BnsGroupCreateRequest{
		ServiceNames:     serviceNames,
		NodePath:         nodePath,
		AuthKey:          config.ParentConfig.Token,
		Threshold:        req.Threshold,
		ThresholdPercent: req.ThresholdPercent,
		GroupConf: &bns.BnsGroupConfList{
			GroupName:       bnsGroupName,
			IdcMap:          req.IdcMap,
			ProtocolName:    req.ProtocolName,
			ConverterName:   req.ConverterName,
			ServiceRtimeout: req.ServiceRtimeout,
			ServiceCtimeout: req.ServiceCtimeout,
			ServiceWtimeout: req.ServiceWtimeout,
			ServiceRetry:    req.ServiceRetry,
		},
		ConfIsJson:        config.GroupConfig.ConfIsJson, // 对GroupConf进行json格式化校验
		ConstrainGroup:    config.GroupConfig.ConstrainGroup,
		OpenDeadhostCheck: config.GroupConfig.OpenDeadhostCheck, // 开启死机检测
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsGroupCreate request fail,req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BnsGroupCreate request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) BnsGroupDelete(ctx context.Context, req *DeleteBnsGroupParam) error {

	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.ClusterName == "" {
		return cerrs.ErrInvalidParams.Errorf("req param clusterName is null")
	}

	bnsGroupName := GenerateBnsGroupName(req.ClusterName)

	rsp, err := br.bnsSdk.BnsGroupDelete(ctx, &bns.BnsGroupDeleteRequest{
		GroupName: bnsGroupName,
		AuthKey:   config.ParentConfig.Token,
		Limit:     config.GroupConfig.DeleteLimit,
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BnsGroupDelete request fail,req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BnsGroupDelete request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) BnsServiceInstanceInfo(ctx context.Context, req *BnsServiceParam) (rsp *bns.BnsServiceInstanceInfoResponse, err error) {

	if req == nil {
		return nil, cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.ClusterName == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param clusterName is null")
	}
	if req.Region == "" {
		return nil, cerrs.ErrInvalidParams.Errorf("req param region is null")
	}

	bnsServiceName := GenerateBnsServiceName(req)

	rsp, err = br.bnsSdk.BnsServiceInstanceInfo(ctx, &bns.BnsServiceInstanceInfoRequest{
		ServiceName: bnsServiceName,
		AuthKey:     config.ParentConfig.Token,
	})

	if err != nil || rsp == nil {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsServiceInstanceInfo request fail,req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if rsp.RetCode != 0 {
		return nil, cerrs.ErrBNSRequestFail.Errorf("BnsServiceInstanceInfo request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return
}

func (br *bnsResourceOp) BatchDisableAndEnableInstancesByContainerId(ctx context.Context, req *InstanceDisableAndEnableParam) error {
	if req == nil {
		return cerrs.ErrInvalidParams.Errorf("req param is null")
	}
	if req.ClusterName == "" {
		return cerrs.ErrInvalidParams.Errorf("req param clusterName is null")
	}
	if req.Region == "" {
		return cerrs.ErrInvalidParams.Errorf("req param region is null")
	}

	bnsServiceName := GenerateBnsServiceName(&BnsServiceParam{
		ClusterName: req.ClusterName,
		Region:      req.Region,
	})

	containerIds, err := br.bnsSdk.BnsArrayToString(req.ContainerIds)
	if err != nil {
		return cerrs.ErrInvalidParams.Errorf("req param containerIds format failed")
	}

	rsp, err := br.bnsSdk.BatchDisableAndEnableInstancesByContainerId(ctx, &bns.InstanceDisableAndEnableRequest{
		ServiceName:  bnsServiceName,
		AuthKey:      config.ParentConfig.Token,
		Enable:       req.Enable,
		ContainerIds: containerIds,
	})

	if err != nil || rsp == nil {
		return cerrs.ErrBNSRequestFail.Errorf("BatchDisableAndEnableInstancesByContainerId request fail,req:%s , BnsError: %s",
			base_utils.Format(req), err)
	}
	if rsp.RetCode != 0 {
		return cerrs.ErrBNSRequestFail.Errorf("BatchDisableAndEnableInstancesByContainerId request fail,req:%s , BnsError RetCode: %d , BnsError ErrorMsg: %s",
			base_utils.Format(req), rsp.RetCode, rsp.Msg)
	}
	return nil
}

func (br *bnsResourceOp) GenerateDefaultIdcMap(bnsGroupConf *bns.BnsGroupConfList) (idcMap string, err error) {
	idcMap, err = br.bnsSdk.GenerateDefaultIdcMap(bnsGroupConf)
	if err != nil {
		return "", cerrs.ErrBNSRequestFail.Errorf("GenerateDefaultIdcMap request fail,req:%s , BnsError: %s",
			base_utils.Format(bnsGroupConf), err)
	}
	return
}

func GenerateBnsNodeName(nodeName string) (bnsNodeName string) {
	bnsNodeName = strings.ReplaceAll(nodeName, "_", "-")
	return
}

func GenerateBnsServiceName(param *BnsServiceParam) (bnsServiceName string) {
	suffix := strings.Split(param.AppID, "-")[len(strings.Split(param.AppID, "-")) - 1]
	clusterName := strings.ReplaceAll(param.ClusterName, "_", "-")
	bnsServiceName = clusterName + "_" + suffix  + "." + "redis" + "." + param.Region
	return
}

func GenerateBnsGroupName(clusterName string) (bnsGroupName string) {
	clusterNameNew := strings.ReplaceAll(clusterName, "_", "-")
	bnsGroupName = "group." + clusterNameNew + "-proxy." + "redis" + ".all"
	return
}
