package bccresource

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"time"
)

func MustInit(ctx context.Context, conf *x1model.Conf, taskOp iface.TaskOperator) {
	MustLoadConf(ctx)
	MustInitBccOrderModel(ctx, conf)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRollbackBccResourceRollback,
		Workflow:        WorkflowRollbackBccResource,
		StepProcess:     ProcessRollbackBccResource,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepRollbackBccResourceRollback},
		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithMaxReentry(5, workflow.FinalStepError))
	taskOperator = taskOp
}
