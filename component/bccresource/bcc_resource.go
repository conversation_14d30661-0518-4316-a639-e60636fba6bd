/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建BCC 资源
*/

package bccresource

import (
	"context"
	"fmt"
	"sync"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/specification"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc/openstack"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

type BccResource interface {
	ShowBccResourcesByOrderV1(ctx context.Context, params *ShowBccResourcesParams) ([]BccResources, error)
	ShowBccResourcesByOrder(ctx context.Context, params *ShowBccResourcesParams) ([]BccResources, error)
	CreateBccResources(ctx context.Context, params *CreateBccResourceParams) (string, error)
	DeleteBccResource(ctx context.Context, params *DeleteBccResourceParams) error
	ResizeVm(ctx context.Context, params *ResizeVmParam) error
	ResizeCds(ctx context.Context, params *ResizeCdsParam) error
	IsVmResizeInOperation(ctx context.Context, params *IsVmResizeInOperationParam) (bool, error)
	IsVmSupportLiveResize(ctx context.Context, params *IsVmSupportResizeParam) (bool, error)
	GetBccVmInfo(ctx context.Context, params *GetBccVmInfoRequest) (*bcc.InstanceInfo, error)
	GetBccCdsInfo(ctx context.Context, params *GetBccCdsInfoRequest) ([]*bcc.CdsVolumeInfo, error)
	ExchangeProductLongIDToShort(ctx context.Context, params *ExchangeIDParams) ([]ExchangeIDMapping, error)
	GetCdsStock(ctx context.Context, params *GetCdsStockReq) (*bcc.GetCdsStockResponse, error)
	SendRollbackTask(ctx context.Context, params *RollbackBccResourceParams) error
	CreateCds(ctx context.Context, params *CreateCdsParam) (string, error)
	AttachCds(ctx context.Context, params *AttachCdsParam) error
	DetachCds(ctx context.Context, params *DetachCdsParam) error
	DeleteCds(ctx context.Context, params *DeleteCdsParam) error
	GetCdsDetail(ctx context.Context, params *GetCdsDetailParam) (*bcc.VolumeDetail, error)

	rollbackOneSubOrder(ctx context.Context, params *RollbackBccResourceParams, subOrder *SubOrderRecord) error
}

const (
	CODE_BCC_ORDER_IN_OPERATION = cerrs.CODE_RESOURCE_COMPO_BASE + 1
	CODE_BCC_ORDER_ERROR        = cerrs.CODE_RESOURCE_COMPO_BASE + 2
)

type CreateBccResourceParams struct {
	AppID                string
	UserID               string
	Product              string
	ImageID              string
	VpcID                string
	CustomerDeploySetIDs []string
	LogicalZone          string
	Azone                string
	// pega本地盘为了兼容proxy和存储节点规格不一致的情况，改为map结构，key为engine，value为storeType
	StoreType               map[string]string
	Items                   []CreateBccResourceParamsItem
	Retry                   int
	TaskID                  string
	DataDiskStorageType     string
	PAASDeploySetStrategies []PAASDeploySetStrategy
	InstanceType            string
}

type CreateBccResourceParamsItem struct {
	Specification       specification.Specification
	Subnet              string
	Engine              string
	Count               int
	DeploySetID         string
	EntityIDs           []string
	SecurityGroupID     string
	ReplacedInstanceIDs []string
}

// PAASDeploySetStrategy 定义了单个部署策略的结构
type PAASDeploySetStrategy struct {
	Concurrency int
	ID          string
	Policy      string
	Quota       int
}

type ShowBccResourcesParams struct {
	UserID  string
	OrderID string
}

type GetDefaultConfigParams struct {
	Azone     string
	StoreType string
}

type DeleteBccResourceParams struct {
	InstanceIds []string
	UserID      string
	IsShortID   bool
}

type BccResources struct {
	ID           string
	ShortID      string
	Name         string
	RootPassword string
	FixIP        string
	FixIPv6      string
	FloatingIP   string
	Flavor       string
	EntityID     string
	MetaData     map[string]string
}

type GetBccVmInfoRequest struct {
	VmID      string
	UserID    string
	IsShortID bool
}

type GetBccCdsInfoRequest struct {
	VmID      string
	UserID    string
	IsShortID bool
}

type GetCdsStockReq struct {
	UserID   string
	ZoneName string
}

type GetSubnetDetailReq struct {
	UserID   string
	SubnetID string
}

type ResizeContext struct {
	ShortVmID  string
	Auth       *common.Authentication
	VmInfo     *bcc.InstanceInfo
	DataVolume *bcc.CdsVolumeInfo
}

type IsVmSupportResizeParam struct {
	VmID               string
	UserID             string
	TargetDataDiskSize int
	Context            *ResizeContext
}

type IsVmResizeInOperationParam struct {
	VmID    string
	UserID  string
	Context *ResizeContext
}

type ResizeVmParam struct {
	VmID                 string
	UserID               string
	TargetCPUCount       int
	TargetMemorySizeInGB int
	LiveResize           bool
	Context              *ResizeContext
}

type ResizeCdsParam struct {
	VolumeID           string
	UserID             string
	TargetDataDiskSize int
	TargetType         string
	Context            *ResizeContext
}

type ExchangeIDParams struct {
	IdType      string
	ObjectType  string
	InstanceIds []string
	UserID      string
}

type ExchangeIDMapping struct {
	ID   string
	UUID string
}

type CreateCdsParam struct {
	UserID      string
	Name        string
	Description string
	CdsSizeInGB int
	SnapshotID  string
	ZoneName    string
}

type AttachCdsParam struct {
	VmID      string
	VolumeID  string
	UserID    string
	IsShortID bool
}

type DetachCdsParam struct {
	VmID      string
	VolumeID  string
	UserID    string
	IsShortID bool
}

type DeleteCdsParam struct {
	VolumeID       string
	UserID         string
	AutoSnapshot   string
	ManualSnapshot string
	Recycle        string
}

type GetCdsDetailParam struct {
	VolumeID string
	UserID   string
}

type bccResourceOp struct {
	stsSdk       sts.StsService
	openstackSdk bcc.OpenStackService
}

var (
	ErrBccOrderInOperation = cerrs.NewConst(CODE_BCC_ORDER_IN_OPERATION, "BCC order is in operation", false, nil)
	ErrBccOrderError       = cerrs.NewConst(CODE_BCC_ORDER_ERROR, "BCC order error", false, nil)
)

var bccResourceOpObj BccResource

var once sync.Once

func BccResourceOp() BccResource {
	once.Do(func() {
		if privatecloud.IsPrivateENV() {
			if privatecloud.IsDBStackAdaptorBCCENV() {
				bccResourceOpObj = &bccResourceOp{
					stsSdk:       privatecloud.GetPrivateStsSDK(),
					openstackSdk: openstack.NewDefaultOpenstackSdk(),
				}
			} else {
				bccResourceOpObj = &bccResourceOp{
					stsSdk:       privatecloud.GetPrivateStsSDK(),
					openstackSdk: privatecloud.GetPrivateOpenStackSDK(),
				}
			}
		} else {
			bccResourceOpObj = &bccResourceOp{
				stsSdk:       sts.NewDefaultStsSdk(),
				openstackSdk: openstack.NewDefaultOpenstackSdk(),
			}
		}
	})
	return bccResourceOpObj
}

func SetBccResourceOpForUt(br BccResource) {
	once.Do(func() {
		bccResourceOpObj = br
	})
}

// CreateBccResources 创建bcc资源；返回OrderID，可以根据OrderID查询资源创建情况
//  1. 生成bcc sdk格式的请求
//     a) 将ImageID、SubnetID转换为短ID 参考BccComponents::exchange_id
//     b) 填入DeploySetID
//     c) 填入instanceType，如果params中未指定，使用配置中的instanceType
//     d) 填入规格信息，由于不是所有规格bcc均接受，因此，使用BCCFlavorAllowed配置，寻找一个大于等于原始规格最接近的规格
//     e) 填入磁盘规格，如果params未指定，使用配置中的规格
//     f) 填入用户自定义信息
//  2. 通过bcc sdk发送请求，如果发送成功，取resp中的OrderID返回
//
// 相关代码: ResourceReqToBccReqProcessor::process, BccComponents::create_bcc_resource
func (br *bccResourceOp) CreateBccResources(ctx context.Context, params *CreateBccResourceParams) (string, error) {
	logger.ComponentLogger.Trace(ctx, "bcc request before converting %s", base_utils.Format(params))
	auth, err := br.getBccOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "getBccOpenapiAuth failed, err %s", err.Error())
		return "", err
	}
	if err := br.exchangeLongIDsToShort(ctx, params, auth); err != nil {
		logger.ComponentLogger.Warning(ctx, "exchangeLongIDsToShort failed, err %s", err.Error())
		return "", err
	}
	dcMap, err := getDefaultConfig(ctx, params)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "getDefaultConfig failed, err %s", err.Error())
		return "", err
	}
	order, subOrders, err := br.createBccSubOrders(ctx, params)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "create bcc sub orders failed, err %s", err.Error())
		return "", err
	}
	if err := br.sendSubOrderRequests(ctx, order, subOrders, auth, dcMap); err != nil {
		logger.ComponentLogger.Warning(ctx, "send bcc sub orders failed, err %s", err.Error())
		return order.OrderID, err
	}

	alternaticeInstanceTypeCnt := -1
	for _, dc := range dcMap {
		if len(dc.AlternativeInstanceTypes) > alternaticeInstanceTypeCnt {
			alternaticeInstanceTypeCnt = len(dc.AlternativeInstanceTypes)
		}
	}

	if order.Status == OrderStatusToRollback || order.Status == OrderStatusError {
		// 如果订单失败，且重试次数小于可选规格数量，返回正常err，否则返回ErrorTaskManualErrorTaskManual
		if params.Retry < alternaticeInstanceTypeCnt {
			logger.ComponentLogger.Warning(ctx, "retry times less than allowed instance types %s", base_utils.Format(order.ErrMsg))
			return order.OrderID, fmt.Errorf(order.ErrMsg)
		}
		logger.ComponentLogger.Warning(ctx, "retry times more than allowed instance types %s", base_utils.Format(order.ErrMsg))
		return order.OrderID, cerrs.ErrorTaskManual.Errorf(order.ErrMsg)
	}

	return order.OrderID, nil
}

// ShowBccResources 查询bcc资源的创建结果；返回相关的资源信息
//  1. 检查订单的执行状态
//     a) 如果订单执行中，返回ErrBCCOrderInOperation；
//     b) 如果订单执行失败，返回错误
//     c) 如果订单执行成功，进入2
//  2. 将订单返回转换为BccResources
//     a) 将短id转换为长id
//     b) 其他对应字段填入BccResources中
//
// 相关代码 BccRespToResourceResp::process、BccComponents::query_by_order
func (br *bccResourceOp) ShowBccResourcesByOrder(ctx context.Context, params *ShowBccResourcesParams) ([]BccResources, error) {
	auth, err := br.getBccOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return nil, err
	}
	var orders []*OrderRecord
	if err := bccOrderModel.GetAllByCond(ctx, &orders, "order_id = ?", params.OrderID); err != nil {
		logger.ComponentLogger.Warning(ctx, "get order failed, err %s", err.Error())
		return nil, err
	}
	if len(orders) == 0 {
		return nil, ErrBccOrderError.Errorf("order not found, order_id: %s", params.OrderID)
	}
	order := orders[0]
	var subOrders []*SubOrderRecord
	if err = bccOrderModel.GetAllByCond(ctx, &subOrders, "p_order_id = ?", order.OrderID); err != nil {
		logger.ComponentLogger.Warning(ctx, "get bcc sub order failed, order_id: %s, err: %w", params.OrderID, err)
		return nil, err
	}
	if len(subOrders) == 0 {
		return nil, ErrBccOrderError.Errorf("sub order not found, order_id: %s", params.OrderID)
	}
	return br.getSubOrdersResponse(ctx, order, subOrders, auth)
}

// ShowBccResources 查询bcc资源的创建结果；返回相关的资源信息
//  1. 检查订单的执行状态
//     a) 如果订单执行中，返回ErrBCCOrderInOperation；
//     b) 如果订单执行失败，返回错误
//     c) 如果订单执行成功，进入2
//  2. 将订单返回转换为BccResources
//     a) 将短id转换为长id
//     b) 其他对应字段填入BccResources中
//
// 相关代码 BccRespToResourceResp::process、BccComponents::query_by_order
// 用于大虚机订单查询(容器化混布)
func (br *bccResourceOp) ShowBccResourcesByOrderV1(ctx context.Context, params *ShowBccResourcesParams) ([]BccResources, error) {
	auth, err := br.getBccOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return nil, err
	}
	response, err := br.openstackSdk.ShowOrder(ctx, &bcc.ShowOrderRequest{
		OrderId: params.OrderID,
		Auth:    auth,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "show order %s failed, resp: %s", params.OrderID, base_utils.Format(response))
		return nil, err
	}

	var bccErr error
	var ret []BccResources
	switch response.Status {
	case "operating":
		logger.ComponentLogger.Trace(ctx, "bcc order %s is operating", params.OrderID)
		bccErr = ErrBccOrderInOperation.Errorf("bcc order %s is operating", params.OrderID)
	case "error":
		logger.ComponentLogger.Warning(ctx, "bcc order %s error: %s", params.OrderID, response.Message)
		bccErr = ErrBccOrderError.Errorf("bcc order %s error: %s", params.OrderID, response.Message)
	case "succ":
		ret, err = br.getBccResources(ctx, auth, response)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "show bcc order resp convert failed, bcc order %s", params.OrderID)
			return nil, ErrBccOrderInOperation.Wrap(err)
		}
	default:
		logger.ComponentLogger.Warning(ctx, "unknown status %s", response.Status)
		bccErr = ErrBccOrderInOperation.Errorf("bcc order %s is %s", params.OrderID, response.Status)
	}

	return ret, bccErr
}

// DeleteBccResource 删除bcc资源
func (br *bccResourceOp) DeleteBccResource(ctx context.Context, params *DeleteBccResourceParams) error {
	if len(params.InstanceIds) == 0 {
		return nil
	}

	auth, err := br.getBccOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	var ids []string
	if !params.IsShortID {
		ids, err = br.exchangeBccLongIdsToShort(ctx, params.InstanceIds, auth)
		if err != nil {
			return err
		}
	}
	var toDeleteIds []string
	for _, id := range ids {
		if id == "" {
			// 已经删除过的bcc返回的shortId为空
			continue
		}
		toDeleteIds = append(toDeleteIds, id)
	}
	if len(toDeleteIds) == 0 {
		logger.ComponentLogger.Trace(ctx, "bcc server already deleted, instanceIds:%s",
			base_utils.Format(params.InstanceIds))
		return nil
	}

	req := &bcc.BatchDeleteServersRequest{
		InstanceIds:    toDeleteIds,
		RelatedRelease: 1,
		Auth:           auth,
	}
	_, err = br.openstackSdk.BatchDeleteServers(ctx, req)
	return err
}

// ExchangeProductLongIDToShort exchange ids
func (br *bccResourceOp) ExchangeProductLongIDToShort(ctx context.Context, params *ExchangeIDParams) ([]ExchangeIDMapping, error) {
	auth, err := br.getBccOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return nil, err
	}

	idList := make([]string, len(params.InstanceIds))
	exchangeIDMapping := make([]ExchangeIDMapping, len(params.InstanceIds))
	resp, err := br.openstackSdk.ExchangeId(ctx, &bcc.ExchangeIdRequest{
		IdType:      bcc.ExchangeIDTypeLong,
		ObjectType:  params.ObjectType,
		InstanceIds: params.InstanceIds,
		Auth:        auth,
	})
	if err != nil {
		return nil, err
	}
	if len(resp.Mappings) != len(idList) {
		return nil, errors.Errorf("transform short ids to long ids failed, resp:%+v", resp)
	}

	for idx, id := range params.InstanceIds {
		exchangeIDMapping[idx].ID = id
	}
	for idx := range idList {
		for _, idPair := range resp.Mappings {
			if exchangeIDMapping[idx].ID == idPair.Uuid {
				exchangeIDMapping[idx].UUID = idPair.Id
			}
		}
	}
	return exchangeIDMapping, nil
}

func (br *bccResourceOp) GetCdsStock(ctx context.Context, params *GetCdsStockReq) (*bcc.GetCdsStockResponse, error) {
	auth, err := br.getBccOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return nil, err
	}

	req := &bcc.GetCdsStockRequest{
		ZoneName: params.ZoneName,
		Auth:     auth,
	}
	resp, err := br.openstackSdk.GetCdsStockWithZone(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
