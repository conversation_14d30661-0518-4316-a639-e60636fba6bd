package bccresource

import (
	"context"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
)

type MockOpenstackSdk struct {
}

func (m MockOpenstackSdk) ShowServer(ctx context.Context, req *bcc.ShowServerRequest) (rsp *bcc.ShowServerResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) ShowTransaction(ctx context.Context, req *bcc.ShowTransactionRequest) (rsp *bcc.ShowTransactionResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) SetShowTransactionForUt(ctx context.Context, req *bcc.SetTransactionRequest) (rsp *bcc.SetTransactionResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) ExchangeId(ctx context.Context, req *bcc.ExchangeIdRequest) (rsp *bcc.ExchangeIdResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) BatchCreateServers(ctx context.Context, req *bcc.BatchCreateServersRequest) (rsp *bcc.BatchCreateServersResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) BatchDeleteServers(ctx context.Context, req *bcc.BatchDeleteServersRequest) (rsp *bcc.BatchDeleteServersResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) ShowOrder(ctx context.Context, req *bcc.ShowOrderRequest) (rsp *bcc.ShowOrderResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) ShowInstanceInfo(ctx context.Context, req *bcc.ShowInstanceRequest) (rsp *bcc.ShowInstanceResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) ResizeInstance(ctx context.Context, req *bcc.ResizeInstanceRequest) (rsp *bcc.ResizeInstanceResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) InstanceAttachedCdsList(ctx context.Context, req *bcc.CdsMountListRequest) (rsp *bcc.CdsMountListResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) ResizeCds(ctx context.Context, req *bcc.CdsResizeRequest) (rsp *bcc.CdsResizeResponse, err error) {
	return &bcc.CdsResizeResponse{
		Message:   "",
		Code:      "",
		Requestid: "",
	}, nil
}

func (m MockOpenstackSdk) CreateDeploySet(ctx context.Context, req *bcc.CreateDeploySetRequest) (rsp *bcc.DeploySetIdsResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) ShowDeploySet(ctx context.Context, req *bcc.ShowDeploySetRequest) (rsp *bcc.ShowDeploySetResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) GetCdsStockWithZone(ctx context.Context, req *bcc.GetCdsStockRequest) (rsp *bcc.GetCdsStockResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) SubnetDetail(ctx context.Context, req *bcc.GetSubnetDetailRequest) (rsp *bcc.GetSubnetDetailResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) CreateCds(ctx context.Context, req *bcc.CdsCreateRequest) (rsp *bcc.CdsCreateResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) AttachCds(ctx context.Context, req *bcc.CdsAttachRequest) (rsp *bcc.CdsAttachResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) DetachCds(ctx context.Context, req *bcc.CdsDetachRequest) (rsp *bcc.CdsDetachResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) DeleteCds(ctx context.Context, req *bcc.CdsDeleteRequest) (rsp *bcc.CdsDeleteResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func (m MockOpenstackSdk) GetCdsDetail(ctx context.Context, req *bcc.GetCdsDetailRequest) (rsp *bcc.GetCdsDetailResponse, err error) {
	//TODO implement me
	panic("implement me")
}

func Test_bccResourceOp_ResizeCds(t *testing.T) {
	type fields struct {
		stsSdk       sts.StsService
		openstackSdk bcc.OpenStackService
	}
	type args struct {
		ctx    context.Context
		params *ResizeCdsParam
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "test resize cds",
			fields: fields{
				stsSdk:       nil,
				openstackSdk: &MockOpenstackSdk{},
			},
			args: args{
				ctx: context.Background(),
				params: &ResizeCdsParam{
					VolumeID:           "aaa",
					UserID:             "mock",
					TargetDataDiskSize: 30,
					Context:            nil,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			br := &bccResourceOp{
				stsSdk:       tt.fields.stsSdk,
				openstackSdk: tt.fields.openstackSdk,
			}
			if err := br.ResizeCds(tt.args.ctx, tt.args.params); (err != nil) != tt.wantErr {
				t.Errorf("ResizeCds() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
