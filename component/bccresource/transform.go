package bccresource

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func (br *bccResourceOp) exchangeBccLongIdsToShort(ctx context.Context, ids []string, auth *common.Authentication) ([]string, error) {
	if privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
		return ids, nil
	}

	resp, err := br.openstackSdk.ExchangeId(ctx, &bcc.ExchangeIdRequest{
		IdType:      bcc.ExchangeIDTypeLong,
		ObjectType:  bcc.ExchangeIDObjectTypeVM,
		InstanceIds: ids,
		Auth:        auth,
	})
	if err != nil {
		return nil, err
	}
	ret := make([]string, len(resp.Mappings))
	for idx, idPair := range resp.Mappings {
		ret[idx] = idPair.Id
	}
	return ret, nil
}

// exchangeLongIDsToShort 将请求中的长id转换为bcc OpenAPI需要的短id
func (br *bccResourceOp) exchangeLongIDsToShort(ctx context.Context, params *CreateBccResourceParams, auth *common.Authentication) error {
	if privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
		return nil
	}

	{
		// 转换ImageID长id到短id
		resp, err := br.openstackSdk.ExchangeId(ctx, &bcc.ExchangeIdRequest{
			IdType:      bcc.ExchangeIDTypeLong,
			ObjectType:  bcc.ExchangeIDObjectTypeImage,
			InstanceIds: []string{params.ImageID},
			Auth:        auth,
		})
		if err != nil {
			return err
		}
		for _, idPair := range resp.Mappings {
			if idPair.Uuid == params.ImageID {
				params.ImageID = idPair.Id
			}
		}
	}
	{
		// 转换Subent到短id
		subnetSet := make(map[string]bool)
		subnetList := make([]string, len(subnetSet))
		for _, item := range params.Items {
			subnetSet[item.Subnet] = true
		}
		for k := range subnetSet {
			subnetList = append(subnetList, k)
		}
		resp, err := br.openstackSdk.ExchangeId(ctx, &bcc.ExchangeIdRequest{
			IdType:      bcc.ExchangeIDTypeLong,
			ObjectType:  bcc.ExchangeIDObjectTypeSubnet,
			InstanceIds: subnetList,
			Auth:        auth,
		})
		if err != nil {
			return err
		}
		for _, idPair := range resp.Mappings {
			for idx := range params.Items {
				if idPair.Uuid == params.Items[idx].Subnet {
					params.Items[idx].Subnet = idPair.Id
				}
			}
		}
	}
	return nil
}

func (br *bccResourceOp) fillBccRequestInstanceConfigs(ctx context.Context, ci *bcc.CreateInstances,
	item *CreateBccResourceParamsItem, params *CreateBccResourceParams) error {
	ci.Configs = append(ci.Configs, &bcc.Configs{
		Key:         "entity_ids",
		ConfigValue: strings.Join(item.EntityIDs, "__"),
	})
	if params.Product == "scs" {
		ci.Configs = append(ci.Configs, &bcc.Configs{
			Key:         "flavor_type",
			ConfigValue: strconv.Itoa(item.Specification.AvailableVolume),
		})
	}
	it := "0"
	switch item.Engine {
	case x1model.EngineRedis, x1model.EngineBDRPRedis, x1model.EnginePegaDB:
		it = "3"
	case x1model.EngineBDRPProxy, x1model.EngineMcProxy:
		it = "0"
	case x1model.EngineMc:
		it = "4"
	}
	ci.Configs = append(ci.Configs, &bcc.Configs{
		Key:         "instance_type",
		ConfigValue: it,
	})
	ci.Configs = append(ci.Configs, &bcc.Configs{
		Key:         "image_type",
		ConfigValue: it,
	})
	ci.Configs = append(ci.Configs, &bcc.Configs{
		Key:         "master_endpoint",
		ConfigValue: config.MasterEndpoint,
	})
	ci.Configs = append(ci.Configs, &bcc.Configs{
		Key:         "env_type",
		ConfigValue: "public_cloud",
	})
	ci.Configs = append(ci.Configs, &bcc.Configs{
		Key:         "deploy_set_id",
		ConfigValue: item.DeploySetID,
	})

	if params.Product == x1model.ProductVDB && privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
		if params.InstanceType == x1model.AppTypeStandalone {
			ci.Configs = append(ci.Configs, &bcc.Configs{
				Key:         "portCount",
				ConfigValue: "8",
			})
		} else {
			switch item.Engine {
			case x1model.EngineVDBDataNode:
				ci.Configs = append(ci.Configs, &bcc.Configs{
					Key:         "portCount",
					ConfigValue: "5",
				})
			case x1model.EngineVDBProxy:
				ci.Configs = append(ci.Configs, &bcc.Configs{
					Key:         "portCount",
					ConfigValue: "2",
				})
			case x1model.EngineVDBMaster:
				ci.Configs = append(ci.Configs, &bcc.Configs{
					Key:         "portCount",
					ConfigValue: "3",
				})
			}
		}
	}
	return nil
}

func (br *bccResourceOp) fillBccRequestInstanceName(ctx context.Context, ci *bcc.CreateInstances,
	item *CreateBccResourceParamsItem, params *CreateBccResourceParams) error {
	if params.Product == x1model.ProductVDB && privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV() {
		result := strings.Split(params.AppID, "-")
		ci.Name = params.AppID + "-" + result[1] + "_" + result[2] + "-" + ci.ZoneName
	}

	return nil
}

// dcMap 在做pegadb本地盘时，为适配proxy和存储节点bcc规格不一致的情况，从单一配置改为map
// key为engine，value为DefaultConfig
func (br *bccResourceOp) getBccCreateServersRequest(ctx context.Context, auth *common.Authentication, params *CreateBccResourceParams,
	dcMap map[string]*DefaultConfig) (*bcc.BatchCreateServersRequest, error) {
	request := &bcc.BatchCreateServersRequest{
		OrderOverTime: int32(config.OrderOverTime),
		Auth:          auth,
	}
	for _, item := range params.Items {
		ci := &bcc.CreateInstances{}
		if err := fillClosestBccFlavorIntoSpec(ctx, &item.Specification); err != nil {
			return nil, err
		}
		// item.Engine在x1-task中构造请求时传入，为cluster.Engine或itf.Engine
		dc, ok := dcMap[item.Engine]
		if !ok {
			return nil, fmt.Errorf("item engine %s not in dcMap, dcMap:%s", item.Engine, base_utils.Format(dcMap))
		}

		if params.Retry%(len(dc.AlternativeInstanceTypes)+1) == 0 {
			ci.InstanceType = dc.DefaultInstanceType
		} else {
			ci.InstanceType = dc.AlternativeInstanceTypes[params.Retry%(len(dc.AlternativeInstanceTypes)+1)-1]
		}
		if strings.HasPrefix(ci.InstanceType, "spec:") {
			ci.Spec = ci.InstanceType[5:] + ".c" + strconv.Itoa(item.Specification.CPUCount) + "m" + strconv.Itoa(item.Specification.MemoryCapacityInGB)
			ci.InstanceType = ""
		}
		if strings.HasPrefix(ci.InstanceType, "spectpl:") {
			// pegaDB本地盘需求引入的新机制，在bcc-resource.toml中新增一种特殊的DefaultInstanceType，以spectpl:开头
			// 举例：spectpl:bcc.l5d.c$CPU_NUMm$MEM_GB.1d
			// 通过下列操作，会拼装为如 bcc.l5d.c8m32.1d 的bcc规格
			bccSpecTpl := ci.InstanceType
			bccSpec := strings.ReplaceAll(bccSpecTpl, "spectpl:", "")
			bccSpec = strings.ReplaceAll(bccSpec, "$CPU_NUM", cast.ToString(item.Specification.CPUCount))
			bccSpec = strings.ReplaceAll(bccSpec, "$MEM_GB", cast.ToString(item.Specification.MemoryCapacityInGB))
			ci.Spec = bccSpec
			ci.InstanceType = ""
		}

		logger.ComponentLogger.Trace(ctx, "bcc spec", logit.String("spec", ci.Spec), logit.String("instance_type", ci.InstanceType))
		ci.DeploySetId = item.DeploySetID
		for _, strategy := range params.PAASDeploySetStrategies {
			ci.PAASDeploySetStrategies = append(ci.PAASDeploySetStrategies, &bcc.PAASDeploySetStrategy{
				Concurrency: strategy.Concurrency,
				ID:          strategy.ID,
				Policy:      strategy.Policy,
				Quota:       strategy.Quota,
			})
		}
		ci.CpuCount = int32(item.Specification.CPUCount)
		ci.ZoneName = params.LogicalZone
		ci.MemoryCapacityInGB = int32(item.Specification.MemoryCapacityInGB)
		ci.RootDiskSizeInGb = int32(item.Specification.RootDiskCapacityInGB)
		ci.RootDiskStorageType = dc.RootDiskStorageType
		if dc.RootDiskUseLocalDisk {
			ci.RootOnLocal = 1
		} else {
			ci.RootOnLocal = 0
		}
		dataDiskStorageType := dc.DataDiskStorageType
		if params.DataDiskStorageType != "" {
			dataDiskStorageType = params.DataDiskStorageType
		}

		if dc.DataDiskUseLocalDisk {
			ci.EphemeralDisks = append(ci.EphemeralDisks, &bcc.EphemeralDisks{
				StorageType: dataDiskStorageType,
				SizeInGB:    int32(item.Specification.DataDiskCapacityInGB),
			})
		} else {
			ci.CreateCdsList = append(ci.CreateCdsList, &bcc.CreateCdsList{
				StorageType: dataDiskStorageType,
				CdsSizeInGB: int32(item.Specification.DataDiskCapacityInGB),
			})
		}
		ci.UserData = params.AppID
		ci.ImageId = params.ImageID
		ci.SubnetId = item.Subnet
		ci.SecurityGroupId = item.SecurityGroupID
		ci.PurchaseCount = int32(item.Count)
		ci.Billing = &bcc.Billing{
			PaymentTiming: "Postpaid",
		}
		ci.RelationTag = 0
		if err := br.fillBccRequestInstanceConfigs(ctx, ci, &item, params); err != nil {
			return nil, err
		}

		if err := br.fillBccRequestInstanceName(ctx, ci, &item, params); err != nil {
			return nil, err
		}

		// 自愈 tag
		if len(item.ReplacedInstanceIDs) > 0 {
			ci.IsReplace = true
			// 将参数中的长 ID 改为短 ID
			for _, instanceID := range item.ReplacedInstanceIDs {
				if strings.HasPrefix(instanceID, "i-") {
					ci.ReplacedInstanceIDs = append(ci.ReplacedInstanceIDs, instanceID)
					continue
				}

				// long2short
				ids, err := br.exchangeBccLongIdsToShort(ctx, []string{instanceID}, auth)
				if err != nil {
					return nil, err
				}
				if len(ids) < 1 {
					return nil, cerrs.ErrBCCTransShortIdFail.Errorf("id %s", instanceID)
				}
				ci.ReplacedInstanceIDs = append(ci.ReplacedInstanceIDs, ids[0])
			}
		}
		request.CreateInstances = append(request.CreateInstances, ci)
	}
	return request, nil
}

func (br *bccResourceOp) getBccOrderRespMetaData(ctx context.Context, metaStr string) (map[string]string, error) {
	ret := make(map[string]string)
	// metadata string like {instance_type=3,replace_uuid=,source=scs}
	metaStr = base_utils.Substr(metaStr, 1, len(metaStr)-2)
	chunks := strings.Split(metaStr, ",")
	for _, chunk := range chunks {
		pair := strings.Split(chunk, "=")
		if len(pair) < 2 {
			continue
		}
		k := strings.TrimSpace(pair[0])
		v := strings.TrimSpace(pair[1])
		ret[k] = v
	}
	return ret, nil
}

func (br *bccResourceOp) getBccResources(ctx context.Context, auth *common.Authentication, response *bcc.ShowOrderResponse) ([]BccResources, error) {
	bccResources := make([]BccResources, len(response.Instances))
	entitySet := make(map[string]bool)
	idList := make([]string, len(response.Instances))
	for idx, server := range response.Instances {
		bccResources[idx].Name = server.Name
		bccResources[idx].FixIP = server.InternalIp
		bccResources[idx].FloatingIP = server.FloatingIp
		bccResources[idx].FixIPv6 = server.Ipv6
		bccResources[idx].Flavor = server.Flavor
		bccResources[idx].RootPassword = server.AdminPass
		bccResources[idx].ID = server.InstanceId
		bccResources[idx].ShortID = server.InstanceId
		metaData, err := br.getBccOrderRespMetaData(ctx, server.MetaData)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "get bcc order resp meta data failed meta data is %s err: %w", server.MetaData, err)
			return nil, err
		}
		bccResources[idx].MetaData = metaData

		entityIds, found := metaData["entity_ids"]
		if !found {
			logger.ComponentLogger.Warning(ctx, "get bcc order resp meta data failed entity_ids not found meta data is %s", server.MetaData)
			return nil, errors.Errorf("entity id not found in metadata")
		}
		for _, entityId := range strings.Split(entityIds, "__") {
			if _, found := entitySet[entityId]; !found {
				bccResources[idx].EntityID = entityId
				entitySet[entityId] = true
				break
			}
		}
		if bccResources[idx].EntityID == "" {
			logger.ComponentLogger.Warning(ctx, "get bcc order resp meta data failed entity_ids not found server is %s",
				base_utils.Format(server))
			return nil, errors.Errorf("entity id not found")
		}
		bccResources[idx].MetaData = metaData
		idList[idx] = server.InstanceId
	}

	if !(privatecloud.IsPrivateENV() && privatecloud.IsDBStackAdaptorBCCENV()) {
		resp, err := br.openstackSdk.ExchangeId(ctx, &bcc.ExchangeIdRequest{
			IdType:      bcc.ExchangeIDTypeShort,
			ObjectType:  bcc.ExchangeIDObjectTypeVM,
			InstanceIds: idList,
			Auth:        auth,
		})
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "exchange id failed resp:%+v err: %w", resp, err)
			return nil, err
		}
		if len(resp.Mappings) != len(idList) {
			logger.ComponentLogger.Warning(ctx, "exchange id failed resp:%+v", resp)
			return nil, errors.Errorf("transform vm short ids to long ids failed, resp:%+v", resp)
		}
		for idx := range bccResources {
			for _, idPair := range resp.Mappings {
				if bccResources[idx].ID == idPair.Id {
					bccResources[idx].ID = idPair.Uuid
				}
			}
		}
	}

	return bccResources, nil
}
