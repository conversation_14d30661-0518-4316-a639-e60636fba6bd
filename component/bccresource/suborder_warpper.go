package bccresource

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/component/suborder"
)

type subOrderWarpper struct {
}

func (s subOrderWarpper) RequestSplit(ctx context.Context, order *suborder.OrderRecord, request interface{}) (map[string]interface{}, error) {
	//TODO implement me
	panic("implement me")
}

func (s subOrderWarpper) ResponseMerger(ctx context.Context, order *suborder.OrderRecord, resps map[string]interface{}) (interface{}, error) {
	//TODO implement me
	panic("implement me")
}

func (s subOrderWarpper) SendSub(ctx context.Context, order *suborder.OrderRecord,
	subOrder *suborder.SubOrderRecord) (realRequest interface{}, orderID string, err error) {
	//TODO implement me
	panic("implement me")
}

func (s subOrderWarpper) QuerySub(ctx context.Context, order *suborder.OrderRecord,
	subOrder *suborder.SubOrderRecord) (inOperation bool, response interface{}, err error) {
	//TODO implement me
	panic("implement me")
}

func NewSubOrderWarpper() suborder.Wrapper {
	return &subOrderWarpper{}
}
