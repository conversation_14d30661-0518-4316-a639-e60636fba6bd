package bccresource

import (
	"context"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func (br *bccResourceOp) CreateCds(ctx context.Context, params *CreateCdsParam) (string, error) {
	auth, err := br.getBccOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "gen auth failed, userID: %s", params.UserID)
		return "", err
	}

	req := &bcc.CdsCreateRequest{
		Name:          params.Name,
		Description:   params.Description,
		CdsSizeInGB:   params.CdsSizeInGB,
		SnapshotID:    params.SnapshotID,
		ZoneName:      params.ZoneName,
		PurchaseCount: 1,
		ChargeType:    "Postpaid",
		Auth:          auth,
	}
	resp, err := br.openstackSdk.CreateCds(ctx, req)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "send create cds failed, resp: %s", base_utils.Format(resp))
		return "", err
	}

	if len(resp.VolumeIDs) != 1 {
		logger.ComponentLogger.Warning(ctx, "check cds num not 1, resp: %s", base_utils.Format(resp))
		return "", errors.Errorf("check cds num not 1")
	}
	return resp.VolumeIDs[0], nil
}

func (br *bccResourceOp) AttachCds(ctx context.Context, params *AttachCdsParam) error {
	auth, err := br.getBccOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	if !params.IsShortID {
		ids, err := br.exchangeBccLongIdsToShort(ctx, []string{params.VmID}, auth)
		if err != nil {
			return err
		}
		if len(ids) < 1 {
			return cerrs.ErrBCCTransShortIdFail.Errorf("id %s", params.VmID)
		}
		params.VmID = ids[0]
	}

	req := &bcc.CdsAttachRequest{
		InstanceId: params.VmID,
		VolumeId:   params.VolumeID,
		Auth:       auth,
	}
	resp, err := br.openstackSdk.AttachCds(ctx, req)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "send attach cds failed, resp: %s", base_utils.Format(resp))
		return err
	}
	return nil
}

func (br *bccResourceOp) DetachCds(ctx context.Context, params *DetachCdsParam) error {
	auth, err := br.getBccOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}
	if !params.IsShortID {
		ids, err := br.exchangeBccLongIdsToShort(ctx, []string{params.VmID}, auth)
		if err != nil {
			return err
		}
		if len(ids) < 1 {
			return cerrs.ErrBCCTransShortIdFail.Errorf("id %s", params.VmID)
		}
		params.VmID = ids[0]
	}

	req := &bcc.CdsDetachRequest{
		InstanceId: params.VmID,
		VolumeId:   params.VolumeID,
		Auth:       auth,
	}
	resp, err := br.openstackSdk.DetachCds(ctx, req)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "send detach cds failed, resp: %s", base_utils.Format(resp))
		return err
	}
	return nil
}

func (br *bccResourceOp) DeleteCds(ctx context.Context, params *DeleteCdsParam) error {
	auth, err := br.getBccOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	req := &bcc.CdsDeleteRequest{
		AutoSnapshot:   params.AutoSnapshot,
		ManualSnapshot: params.ManualSnapshot,
		Recycle:        params.Recycle,
		VolumeId:       params.VolumeID,
		Auth:           auth,
	}
	resp, err := br.openstackSdk.DeleteCds(ctx, req)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "send delete cds failed, resp: %s", base_utils.Format(resp))
		return err
	}
	return nil
}

func (br *bccResourceOp) GetCdsDetail(ctx context.Context, params *GetCdsDetailParam) (*bcc.VolumeDetail, error) {
	auth, err := br.getBccOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return nil, err
	}
	req := &bcc.GetCdsDetailRequest{
		VolumeId: params.VolumeID,
		Auth:     auth,
	}
	resp, err := br.openstackSdk.GetCdsDetail(ctx, req)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "send get cds detail failed, resp: %s", base_utils.Format(resp))
		return nil, err
	}
	return resp.Volume, nil
}
