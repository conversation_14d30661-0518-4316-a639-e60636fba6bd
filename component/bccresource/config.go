/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
BCC创建配置
*/

package bccresource

import (
	"context"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/component/specification"
)

type Config struct {
	DefaultConfigList          []DefaultConfig
	BCCAllowedFlavorList       []BCCAllowedFlavor
	InstanceTypesResizeAllowed []string
	ForbidLiveResize           bool
	OrderOverTime              int
	MasterEndpoint             string
	BccImageId                 string
	BccImageIdForIpv6          string
	BccImageIdForPegaDB        string
	SubOrderSize               int
}

type DefaultConfig struct {
	AZone                    string
	StoreType                string
	DefaultInstanceType      string
	AlternativeInstanceTypes []string
	RootDiskStorageType      string
	RootDiskUseLocalDisk     bool
	DataDiskStorageType      string
	DataDiskUseLocalDisk     bool
}

type BCCAllowedFlavor struct {
	MemoryCapacityInGB int
	CpuList            []int
}

var config *Config

func MustLoadConf(ctx context.Context) {
	config = &Config{}
	if err := compo_utils.LoadConf("bcc-resource", config); err != nil {
		panic(err.Error())
	}
}

func GetBccImageId() string {
	return config.BccImageId
}

func GetBccImageIdForIpv6() string {
	return config.BccImageIdForIpv6
}

func GetBccImageIdForPegaDB() string {
	return config.BccImageIdForPegaDB
}

func GetSubOrderSize() int {
	return config.SubOrderSize
}

// getClosestBccFlavor 获取最接近的bcc规格
// 由于bcc有固定的规格列表，创建scs时，寻找最近的规格进行创建
func fillClosestBccFlavorIntoSpec(ctx context.Context, flavor *specification.Specification) error {
	if flavor.CPUCount <= 0 || flavor.MemoryCapacityInGB <= 0 {
		return errors.Errorf("flavor %+v not valid", flavor)
	}
	// config.BCCAllowedFlavorList 是按照MemoryCapacityInMB从小到大排序的
	// 每个BCCAllowedFlavorList中CPUList也是从小到大排列的
	found := false
	for _, bccAllowedFlavor := range config.BCCAllowedFlavorList {
		if found {
			break
		}
		if bccAllowedFlavor.MemoryCapacityInGB >= flavor.MemoryCapacityInGB {
			for _, bccAllowedCpu := range bccAllowedFlavor.CpuList {
				if bccAllowedCpu >= flavor.CPUCount {
					flavor.MemoryCapacityInGB = bccAllowedFlavor.MemoryCapacityInGB
					flavor.CPUCount = bccAllowedCpu
					found = true
					break
				}
			}
		}
	}
	if !found {
		return errors.Errorf("flavor %+v not allowed", flavor)
	}
	return nil
}

// getDefaultConfig 获取默认的BCC配置
// 通过Azone和StoreType两个维度
// 在pegadb本地盘需求中，出现了proxy和存储节点storetype不一致的情况，因此从单一配置修改为map，key为engine
func getDefaultConfig(ctx context.Context, params *CreateBccResourceParams) (map[string]*DefaultConfig, error) {
	dcMap := make(map[string]*DefaultConfig)
	for engine, storeType := range params.StoreType {
		defaultConfigByStoreTypes := []*DefaultConfig{}
		for i := range config.DefaultConfigList {
			dc := &config.DefaultConfigList[i]
			if dc.StoreType == storeType {
				defaultConfigByStoreTypes = append(defaultConfigByStoreTypes, dc)
			}
		}
		var aZoneAny *DefaultConfig
		var defaultConfigByAzone *DefaultConfig
		for idx := range defaultConfigByStoreTypes {
			dc := defaultConfigByStoreTypes[idx]
			if dc.AZone == "*" {
				aZoneAny = dc
			} else if dc.AZone == params.Azone && defaultConfigByAzone == nil {
				defaultConfigByAzone = dc
			}
		}
		if defaultConfigByAzone == nil {
			defaultConfigByAzone = aZoneAny
		}
		if defaultConfigByAzone == nil {
			return nil, errors.Errorf("cannot find default config for azone %s, store type %s", params.Azone, storeType)
		}
		dcMap[engine] = defaultConfigByAzone
	}
	return dcMap, nil
}

// getDefaultConfig 获取默认的BCC配置
// 通过Azone和StoreType两个维度
func GetBccDefaultConfig(ctx context.Context, params *GetDefaultConfigParams) (*DefaultConfig, error) {
	defaultConfigByStoreTypes := []*DefaultConfig{}
	for i := range config.DefaultConfigList {
		dc := &config.DefaultConfigList[i]
		if dc.StoreType == params.StoreType {
			defaultConfigByStoreTypes = append(defaultConfigByStoreTypes, dc)
		}
	}
	var aZoneAny *DefaultConfig
	var defaultConfigByAzone *DefaultConfig
	for idx := range defaultConfigByStoreTypes {
		dc := defaultConfigByStoreTypes[idx]
		if dc.AZone == "*" {
			aZoneAny = dc
		} else if dc.AZone == params.Azone && defaultConfigByAzone == nil {
			defaultConfigByAzone = dc
		}
	}
	if defaultConfigByAzone == nil {
		defaultConfigByAzone = aZoneAny
	}
	if defaultConfigByAzone == nil {
		return nil, errors.Errorf("cannot find default config for azone %s, store type %s", params.Azone, params.StoreType)
	}
	return defaultConfigByAzone, nil
}
