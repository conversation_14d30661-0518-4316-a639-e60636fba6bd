package bccresource

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func (br *bccResourceOp) ResizeVm(ctx context.Context, params *ResizeVmParam) error {
	var err error
	if params.Context, err = br.initResizeContext(ctx, params.Context, params.VmID, params.UserID); err != nil {
		return err
	}
	if params.TargetCPUCount != int(params.Context.VmInfo.CpuCount) || params.TargetMemorySizeInGB != int(params.Context.VmInfo.MemoryCapacityInGB) {
		req := &bcc.ResizeInstanceRequest{
			CpuCount:           int32(params.TargetCPUCount),
			MemoryCapacityInGB: int32(params.TargetMemorySizeInGB),
			LiveResize:         params.LiveResize,
			VmId:               params.Context.ShortVmID,
			Auth:               params.Context.Auth,
		}
		resp, err := br.openstackSdk.ResizeInstance(ctx, req)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "send resize vm failed, resp: %s", base_utils.Format(resp))
			return err
		}
		return nil
	}
	return cerrs.ErrBCCHasResized
}

func (br *bccResourceOp) ResizeCds(ctx context.Context, params *ResizeCdsParam) error {
	auth, err := br.getBccOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}
	req := &bcc.CdsResizeRequest{
		NewCdsSizeInGB: int32(params.TargetDataDiskSize),
		NewVolumeType:  params.TargetType,
		VolumeId:       params.VolumeID,
		Auth:           auth,
	}
	resp, err := br.openstackSdk.ResizeCds(ctx, req)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "send resize cds failed, resp: %s", base_utils.Format(resp))
		return err
	}
	return nil
}

// IsVmResizeInOperation 检查Vm是否在热变配中
func (br *bccResourceOp) IsVmResizeInOperation(ctx context.Context, params *IsVmResizeInOperationParam) (bool, error) {
	var err error
	if params.Context, err = br.initResizeContext(ctx, params.Context, params.VmID, params.UserID); err != nil {
		return false, err
	}
	if params.Context.VmInfo.Status != "Running" {
		return true, nil
	}
	if params.Context.DataVolume.Status != "InUse" {
		return true, nil
	}
	return false, nil
}

// IsVmSupportResize 判断Vm是否支持热迁移
// 通过两个维度（1）InstanceType是否符合预期 （2）数据盘如果需要扩展，类型是否为CDS
func (br *bccResourceOp) IsVmSupportLiveResize(ctx context.Context, params *IsVmSupportResizeParam) (bool, error) {
	var err error
	if params.Context, err = br.initResizeContext(ctx, params.Context, params.VmID, params.UserID); err != nil {
		return false, err
	}
	if inArray, _ := base_utils.InArray(params.Context.VmInfo.InstanceType, config.InstanceTypesResizeAllowed); !inArray {
		return false, nil
	}
	if params.Context.DataVolume.Type != "Cds" && params.TargetDataDiskSize > int(params.Context.DataVolume.DiskSizeInGB) {
		return false, nil
	}
	return true, nil
}

func (br *bccResourceOp) GetBccVmInfo(ctx context.Context, params *GetBccVmInfoRequest) (*bcc.InstanceInfo, error) {
	auth, err := br.getBccOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return nil, err
	}
	if !params.IsShortID {
		ids, err := br.exchangeBccLongIdsToShort(ctx, []string{params.VmID}, auth)
		if err != nil {
			return nil, err
		}
		if len(ids) < 1 {
			return nil, cerrs.ErrBCCTransShortIdFail.Errorf("id %s", params.VmID)
		}
		params.VmID = ids[0]
	}
	req := &bcc.ShowInstanceRequest{
		VmUuid: params.VmID,
		Auth:   auth,
	}
	resp, err := br.openstackSdk.ShowInstanceInfo(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.Instance, nil
}

func (br *bccResourceOp) GetBccCdsInfo(ctx context.Context, params *GetBccCdsInfoRequest) ([]*bcc.CdsVolumeInfo, error) {
	auth, err := br.getBccOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return nil, err
	}
	if !params.IsShortID {
		ids, err := br.exchangeBccLongIdsToShort(ctx, []string{params.VmID}, auth)
		if err != nil {
			return nil, err
		}
		if len(ids) < 1 {
			return nil, cerrs.ErrBCCTransShortIdFail.Errorf("id %s", params.VmID)
		}
		params.VmID = ids[0]
	}
	req := &bcc.CdsMountListRequest{
		VmId: params.VmID,
		Auth: auth,
	}
	resp, err := br.openstackSdk.InstanceAttachedCdsList(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.Volumes, nil
}

func (br *bccResourceOp) initResizeContext(ctx context.Context, resizeCtx *ResizeContext, vmId string, userId string) (*ResizeContext, error) {
	if resizeCtx == nil {
		resizeCtx = &ResizeContext{}
	}
	var err error
	if resizeCtx.Auth == nil {
		resizeCtx.Auth, err = br.getBccOpenapiAuth(ctx, userId)
		if err != nil {
			return resizeCtx, err
		}
	}
	if resizeCtx.ShortVmID == "" {
		ids, err := br.exchangeBccLongIdsToShort(ctx, []string{vmId}, resizeCtx.Auth)
		if err != nil {
			return resizeCtx, err
		}
		if len(ids) < 1 {
			return resizeCtx, cerrs.ErrBCCTransShortIdFail.Errorf("id %s", vmId)
		}
		resizeCtx.ShortVmID = ids[0]
	}
	if resizeCtx.VmInfo == nil {
		req := &bcc.ShowInstanceRequest{
			VmUuid: resizeCtx.ShortVmID,
			Auth:   resizeCtx.Auth,
		}
		resp, err := br.openstackSdk.ShowInstanceInfo(ctx, req)
		if err != nil {
			return resizeCtx, err
		}
		resizeCtx.VmInfo = resp.Instance
	}
	if resizeCtx.DataVolume == nil {
		req := &bcc.CdsMountListRequest{
			VmId: resizeCtx.ShortVmID,
			Auth: resizeCtx.Auth,
		}
		resp, err := br.openstackSdk.InstanceAttachedCdsList(ctx, req)
		if err != nil {
			return resizeCtx, err
		}
		for _, volumes := range resp.Volumes {
			for _, attach := range volumes.Attachments {
				if attach.InstanceId == resizeCtx.VmInfo.Id && attach.Device == "/dev/vdb" {
					resizeCtx.DataVolume = volumes
				}
			}
		}
		if resizeCtx.DataVolume == nil {
			return resizeCtx, cerrs.ErrNotFound.Errorf("data volume for instance %s not found", resizeCtx.ShortVmID)
		}
	}
	return resizeCtx, nil
}

// getBccOpenapiAuth 获取请求BCC OpenAPI的鉴权信息
func (br *bccResourceOp) getBccOpenapiAuth(ctx context.Context, userId string) (*common.Authentication, error) {
	if userId == "mock" {
		return nil, nil
	}
	return compo_utils.GetOpenapiAuthWithResourceAccount2(ctx, userId, br.stsSdk)
}
