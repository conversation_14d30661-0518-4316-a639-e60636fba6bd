package bccresource

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/goccy/go-json"
	"github.com/google/uuid"
	"icode.baidu.com/baidu/gdp/extension/gtask"

	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func (br *bccResourceOp) createBccSubOrders(ctx context.Context, params *CreateBccResourceParams) (*OrderRecord, []*SubOrderRecord, error) {
	var orders []*OrderRecord
	var err error
	if err = bccOrderModel.GetAllByCond(ctx, &orders, "task_id = ? AND azone = ? AND status IN ?",
		params.TaskID, params.Azone, []string{OrderStatusToCreate, OrderStatusCreating, OrderStatusCreated, OrderStatusToRollback}); err != nil {
		logger.ComponentLogger.Warning(ctx, "get bcc order failed, task_id: %s, err: %w", params.TaskID, err)
		return nil, nil, err
	}
	if len(orders) == 0 {
		orders = append(orders, &OrderRecord{
			OrderID:    uuid.New().String(),
			CreatedAt:  time.Time{},
			Status:     OrderStatusToCreate,
			TaskID:     params.TaskID,
			Azone:      params.Azone,
			AppID:      params.AppID,
			Parameters: base_utils.Format(params),
		})
		if err = bccOrderModel.FullSaveAssociationsSave(ctx, &orders); err != nil {
			logger.ComponentLogger.Warning(ctx, "save bcc order failed, task_id: %s, err: %w", params.TaskID, err)
			return nil, nil, err
		}
		logger.ComponentLogger.Trace(ctx, "create order record for task %s, orderId %s", params.TaskID, orders[0].OrderID)
	}

	var subOrders []*SubOrderRecord
	if err = bccOrderModel.GetAllByCond(ctx, &subOrders, "p_order_id = ?", orders[0].OrderID); err != nil {
		logger.ComponentLogger.Warning(ctx, "get bcc sub order failed, task_id: %s, err: %w", params.TaskID, err)
		return nil, nil, err
	}
	if len(subOrders) == 0 {
		orderChunksSize := len(params.Items) / GetSubOrderSize()
		if len(params.Items)%GetSubOrderSize() != 0 {
			orderChunksSize++
		}
		logger.ComponentLogger.Trace(ctx, "create sub order for task %s, orderChunksSize %d", params.TaskID, orderChunksSize)
		chunks := make([][]CreateBccResourceParamsItem, orderChunksSize)
		for index, item := range params.Items {
			chunks[index/GetSubOrderSize()] = append(chunks[index/GetSubOrderSize()], item)
		}
		for i := 0; i < orderChunksSize; i++ {
			subOrderReq := CreateBccResourceParams{
				AppID:                   params.AppID,
				UserID:                  params.UserID,
				Product:                 params.Product,
				ImageID:                 params.ImageID,
				VpcID:                   params.VpcID,
				CustomerDeploySetIDs:    params.CustomerDeploySetIDs,
				LogicalZone:             params.LogicalZone,
				Azone:                   params.Azone,
				StoreType:               params.StoreType,
				Items:                   chunks[i],
				Retry:                   params.Retry,
				TaskID:                  params.TaskID,
				DataDiskStorageType:     params.DataDiskStorageType,
				PAASDeploySetStrategies: params.PAASDeploySetStrategies,
				InstanceType:            params.InstanceType,
			}
			subOrders = append(subOrders, &SubOrderRecord{
				OrderID:    uuid.New().String(),
				POrderID:   orders[0].OrderID,
				BccOrderID: "",
				CreatedAt:  time.Time{},
				Parameters: base_utils.Format(subOrderReq),
				Status:     OrderStatusToCreate,
				ErrMsg:     "",
			})
		}
		if err = bccOrderModel.FullSaveAssociationsSave(ctx, &subOrders); err != nil {
			logger.ComponentLogger.Warning(ctx, "save bcc sub order failed, task_id: %s, err: %w", params.TaskID, err)
			return nil, nil, err
		}
	}
	// 上一步的资源申请有失败的，需要重新申请
	if len(subOrders) != 0 && orders[0].Status == OrderStatusToRollback {
		for _, subOrder := range subOrders {
			if err := updateSubOrderForRetry(ctx, subOrder, params); err != nil {
				return nil, nil, err
			}
		}
		if err = bccOrderModel.FullSaveAssociationsSave(ctx, &subOrders); err != nil {
			logger.ComponentLogger.Warning(ctx, "save bcc sub order failed, task_id: %s, err: %w", params.TaskID, err)
			return nil, nil, err
		}
		orders[0].Status = OrderStatusToCreate
		orders[0].ErrMsg = ""
		if err = bccOrderModel.FullSaveAssociationsSave(ctx, &orders); err != nil {
			logger.ComponentLogger.Warning(ctx, "save bcc order failed, task_id: %s, err: %w", params.TaskID, err)
			return nil, nil, err
		}
	}
	return orders[0], subOrders, nil
}

func updateSubOrderForRetry(ctx context.Context, subOrder *SubOrderRecord, params *CreateBccResourceParams) error {
	if subOrder.Status == OrderStatusError {
		subOrder.OrderID = genNewSubOrderID(subOrder.OrderID)
		subOrder.Status = OrderStatusToCreate
		subOrder.ErrMsg = ""
		subOrder.BccOrderID = ""
		subOrder.BccRequest = ""
		subOrder.BccResponse = ""
	}
	// 对于需要本次发送请求的子订单,更新其中的Retry标记,保证机型随着重试可以变化
	if subOrder.Status == OrderStatusToCreate {
		subOrderParams, err := parseBccRequestParams(ctx, subOrder.Parameters)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "parse bcc request params failed, err: %w", err)
			return err
		}
		subOrderParams.Retry = params.Retry
		subOrder.Parameters = base_utils.Format(subOrderParams)
	}
	return nil
}

func genNewSubOrderID(orderID string) string {
	splitOrderIds := strings.Split(orderID, "-")
	if len(splitOrderIds) < 5 {
		return orderID + "-1"
	}
	retryTimes, err := strconv.Atoi(splitOrderIds[len(splitOrderIds)-1])
	if err != nil {
		return orderID + "-1"
	}
	return fmt.Sprintf("%s-%d", strings.Join(splitOrderIds[:len(splitOrderIds)-1], "-"), retryTimes+1)
}

func (br *bccResourceOp) sendSubOrderRequests(ctx context.Context, order *OrderRecord, subOrders []*SubOrderRecord,
	auth *common.Authentication, dcMap map[string]*DefaultConfig) error {
	if order.Status == OrderStatusCreating || order.Status == OrderStatusCreated {
		return nil
	}
	if order.Status == OrderStatusError ||
		order.Status == OrderStatusRollbacking ||
		order.Status == OrderStatusRollbacked ||
		order.Status == OrderStatusToRollback {
		return fmt.Errorf("order status is %s, can not send sub order request", order.Status)
	}

	var reqErr error
	for _, subOrder := range subOrders {
		reqErr = br.sendOneSubOrderRequest(ctx, subOrder, auth, dcMap)
		if reqErr != nil {
			break
		}
		// time.Sleep(time.Second)
	}
	if reqErr != nil {
		for _, subOrder := range subOrders {
			if subOrder.Status == OrderStatusError {
				order.ErrMsg = subOrder.ErrMsg
				break
			}
		}
		order.Status = OrderStatusToRollback
	} else {
		order.Status = OrderStatusCreating
	}
	if err := bccOrderModel.FullSaveAssociationsSave(ctx, &subOrders); err != nil {
		logger.ComponentLogger.Warning(ctx, "save bcc sub order failed, task_id: %s, err: %w", order.TaskID, err)
		return err
	}
	if err := bccOrderModel.FullSaveAssociationsSave(ctx, &[]*OrderRecord{order}); err != nil {
		logger.ComponentLogger.Warning(ctx, "save bcc order failed, task_id: %s, err: %w", order.TaskID, err)
		return err
	}
	return nil
}

func (br *bccResourceOp) sendOneSubOrderRequest(ctx context.Context, subOrder *SubOrderRecord,
	auth *common.Authentication, dcMap map[string]*DefaultConfig) error {
	if subOrder.Status == OrderStatusCreating {
		return nil
	}
	if subOrder.Status == OrderStatusError {
		return fmt.Errorf(subOrder.ErrMsg)
	}
	if subOrder.Status == OrderStatusToRollback {
		return fmt.Errorf("sub order %s need rollback", subOrder.OrderID)
	}
	params := &CreateBccResourceParams{}
	if err := json.Unmarshal([]byte(subOrder.Parameters), params); err != nil {
		subOrder.Status = OrderStatusError
		subOrder.ErrMsg = err.Error()
		return err
	}
	request, err := br.getBccCreateServersRequest(ctx, auth, params, dcMap)
	if err != nil {
		subOrder.Status = OrderStatusError
		subOrder.ErrMsg = err.Error()
		return err
	}
	logger.ComponentLogger.Trace(ctx, "send sub order request, subOrderID: %s, request: %s",
		subOrder.OrderID, base_utils.Format(request))
	subOrder.BccRequest = base_utils.Format(request)
	request.ClientToken = subOrder.OrderID
	request.Auth.TransactionId = subOrder.OrderID
	if privatecloud.IsPrivateENV() {
		request.Auth = &common.Authentication{
			IamUserId: params.VpcID,
		}
	}
	response, err := br.openstackSdk.BatchCreateServers(ctx, request)
	if err != nil {
		subOrder.Status = OrderStatusError
		subOrder.ErrMsg = err.Error()
		return err
	}
	subOrder.Status = OrderStatusCreating
	subOrder.BccOrderID = response.OrderId
	return nil
}

func (br *bccResourceOp) getSubOrdersResponse(ctx context.Context, order *OrderRecord, subOrders []*SubOrderRecord,
	auth *common.Authentication) ([]BccResources, error) {
	if order.Status == OrderStatusError || order.Status == OrderStatusRollbacking || order.Status == OrderStatusRollbacked {
		return nil, ErrBccOrderError.Errorf("order status is %s, can not get sub order response", order.Status)
	}

	g := gtask.Group{}
	var respLock sync.Mutex
	var respList []BccResources
	for _, subOrder := range subOrders {
		subOrder := subOrder
		g.Go(func() error {
			resp, err := br.getOneSubOrderResponse(ctx, subOrder, auth)
			if err != nil {
				return err
			}
			if resp != nil {
				respLock.Lock()
				respList = append(respList, resp...)
				respLock.Unlock()
			}
			return nil
		})
	}
	_, reqErr := g.Wait()
	if reqErr != nil {
		isError := false
		for _, subOrder := range subOrders {
			if subOrder.Status == OrderStatusError {
				isError = true
				order.ErrMsg = subOrder.ErrMsg
				reqErr = ErrBccOrderError.Errorf("sub order %s error: %s", subOrder.OrderID, subOrder.ErrMsg)
				break
			}
		}
		if isError {
			order.Status = OrderStatusToRollback
			if err := bccOrderModel.FullSaveAssociationsSave(ctx, &order); err != nil {
				logger.ComponentLogger.Warning(ctx, "save bcc order failed, task_id: %s, err: %w", order.TaskID, err)
				return nil, ErrBccOrderInOperation.Wrap(err)
			}
		} else {
			reqErr = ErrBccOrderInOperation.Errorf("order %s in operation", order.OrderID)
		}
		return nil, reqErr
	}
	order.Status = OrderStatusCreated
	logger.ComponentLogger.Trace(ctx, "create bcc success resp %s", base_utils.Format(respList))
	if err := bccOrderModel.FullSaveAssociationsSave(ctx, &[]*OrderRecord{order}); err != nil {
		logger.ComponentLogger.Warning(ctx, "save bcc order failed, task_id: %s, err: %w", order.TaskID, err)
		return nil, ErrBccOrderInOperation.Wrap(err)
	}
	return respList, nil
}

func (br *bccResourceOp) getOneSubOrderResponse(ctx context.Context, subOrder *SubOrderRecord, auth *common.Authentication) ([]BccResources, error) {
	req := &bcc.ShowOrderRequest{
		OrderId: subOrder.BccOrderID,
		Auth:    auth,
	}
	auth.TransactionId = subOrder.OrderID
	response, err := br.openstackSdk.ShowOrder(ctx, req)
	if err != nil {
		return nil, err
	}
	var bccErr error
	var ret []BccResources
	switch response.Status {
	case "operating":
		logger.ComponentLogger.Trace(ctx, "bcc order %s is operating", subOrder.BccOrderID)
		bccErr = ErrBccOrderInOperation.Errorf("bcc order %s is operating", subOrder.BccOrderID)
	case "error":
		subOrder.Status = OrderStatusError
		subOrder.ErrMsg = response.ErrMsg
		logger.ComponentLogger.Warning(ctx, "bcc order %s error: %s", subOrder.BccOrderID, response.ErrMsg)
		bccErr = ErrBccOrderError.Errorf("bcc order %s error: %s", subOrder.BccOrderID, response.ErrMsg)
	case "succ":
		ret, err = br.getBccResources(ctx, auth, response)
		if err != nil {
			logger.ComponentLogger.Warning(ctx, "show bcc order resp convert failed, bcc order %s", subOrder.BccOrderID)
			return nil, ErrBccOrderInOperation.Wrap(err)
		}
		subOrder.Status = OrderStatusCreated
		subOrder.BccResponse = base_utils.Format(response)
	default:
		logger.ComponentLogger.Warning(ctx, "unknown status %s", response.Status)
		bccErr = ErrBccOrderInOperation.Errorf("bcc order %s is %s", subOrder.BccOrderID, response.Status)
	}
	if err := bccOrderModel.FullSaveAssociationsSave(ctx, &[]*SubOrderRecord{subOrder}); err != nil {
		logger.ComponentLogger.Warning(ctx, "save bcc sub order failed, suborderid %s, err: %w", subOrder.OrderID, err)
		return nil, ErrBccOrderInOperation.Wrap(err)
	}
	return ret, bccErr
}

func parseBccRequestParams(ctx context.Context, paramStr string) (*CreateBccResourceParams, error) {
	params := &CreateBccResourceParams{}
	if err := json.Unmarshal([]byte(paramStr), params); err != nil {
		logger.ComponentLogger.Warning(ctx, "parse bcc request params failed, err: %w", err)
		return nil, err
	}
	return params, nil
}
