/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建BCC 资源
*/

package bccresource

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/scs/x1-base/component/specification"
	"icode.baidu.com/baidu/scs/x1-base/model"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sts"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

func init() {
	unittest.UnitTestInit(2)
	MustLoadConf(context.Background())
}

func TestLoadConfig(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	spec := &specification.Specification{}
	if err := fillClosestBccFlavorIntoSpec(ctx, spec); err == nil {
		t.Fatalf("expect err, actual no error")
	}
	spec.MemoryCapacityInGB = 2
	spec.CPUCount = 1
	if err := fillClosestBccFlavorIntoSpec(ctx, spec); err != nil {
		t.Fatalf("expect no error, actual err:%s", err.Error())
	}
	if spec.MemoryCapacityInGB != 2 || spec.CPUCount != 1 {
		t.Fatalf("expect cpu 1 mem 2, actual cpu %d mem %d", spec.CPUCount, spec.MemoryCapacityInGB)
	}
	spec.MemoryCapacityInGB = 3
	spec.CPUCount = 1
	if err := fillClosestBccFlavorIntoSpec(ctx, spec); err != nil {
		t.Fatalf("expect no error, actual err:%s", err.Error())
	}
	if spec.MemoryCapacityInGB != 4 || spec.CPUCount != 1 {
		t.Fatalf("expect cpu 1 mem 4, actual cpu %d mem %d", spec.CPUCount, spec.MemoryCapacityInGB)
	}
	spec.MemoryCapacityInGB = 11
	spec.CPUCount = 1
	if err := fillClosestBccFlavorIntoSpec(ctx, spec); err != nil {
		t.Fatalf("expect no error, actual err:%s", err.Error())
	}
	if spec.MemoryCapacityInGB != 12 || spec.CPUCount != 2 {
		t.Fatalf("expect cpu 2 mem 12, actual cpu %d mem %d", spec.CPUCount, spec.MemoryCapacityInGB)
	}
	spec.MemoryCapacityInGB = 11
	spec.CPUCount = 13
	if err := fillClosestBccFlavorIntoSpec(ctx, spec); err != nil {
		t.Fatalf("expect no error, actual err:%s", err.Error())
	}
	if spec.MemoryCapacityInGB != 16 || spec.CPUCount != 16 {
		t.Fatalf("expect cpu 16 mem 16, actual cpu %d mem %d", spec.CPUCount, spec.MemoryCapacityInGB)
	}
	spec.MemoryCapacityInGB = 129
	spec.CPUCount = 13
	if err := fillClosestBccFlavorIntoSpec(ctx, spec); err == nil {
		t.Fatalf("expect err, actual no error")
	}
	spec.MemoryCapacityInGB = 11
	spec.CPUCount = 65
	if err := fillClosestBccFlavorIntoSpec(ctx, spec); err == nil {
		t.Fatalf("expect err, actual no error")
	}
}

func TestShowBccResourcesByOrderV1(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// case1
	servers, err := BccResourceOp().ShowBccResourcesByOrderV1(ctx, &ShowBccResourcesParams{
		UserID:  "4093ae6b9e48423e89c69916bc6d5d5e",
		OrderID: "125933e9b8bc4eaf9b5942fa01b8793a",
	})
	fmt.Println(err)
	fmt.Println(servers)
}

type mockOpenstackSdk struct {
}

func (m mockOpenstackSdk) ShowServer(ctx context.Context, req *bcc.ShowServerRequest) (rsp *bcc.ShowServerResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m mockOpenstackSdk) ShowTransaction(ctx context.Context, req *bcc.ShowTransactionRequest) (rsp *bcc.ShowTransactionResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m mockOpenstackSdk) SetShowTransactionForUt(ctx context.Context, req *bcc.SetTransactionRequest) (rsp *bcc.SetTransactionResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m mockOpenstackSdk) ExchangeId(ctx context.Context, req *bcc.ExchangeIdRequest) (rsp *bcc.ExchangeIdResponse, err error) {
	for _, instanceID := range req.InstanceIds {
		if instanceID == "MOCKUUID_NOT_FOUND" {
			return &bcc.ExchangeIdResponse{
				Message:   "message",
				Code:      "code",
				Requestid: "request-id",
				Mappings:  []*bcc.Mappings{},
			}, nil

		}
	}

	return &bcc.ExchangeIdResponse{
		Message:   "message",
		Code:      "code",
		Requestid: "request-id",
		Mappings: []*bcc.Mappings{
			{
				Id:   "a",
				Uuid: "b",
			},
		},
	}, nil
}

func (m mockOpenstackSdk) BatchCreateServers(ctx context.Context, req *bcc.BatchCreateServersRequest) (rsp *bcc.BatchCreateServersResponse, err error) {
	// fmt.Printf("mock batch create servers request: %s\n", base_utils.Format(req))
	if strings.Contains(req.CreateInstances[0].DeploySetId, "mockerr") {
		// fmt.Println("mock batch create servers request fail")
		return &bcc.BatchCreateServersResponse{
			Message:     "mockerr",
			Code:        "mockerr",
			Requestid:   "mock-request-id",
			OrderId:     "",
			InstanceIds: nil,
		}, errors.New("batch create servers request fail, code: mockerr, message: mockerr")
	}
	return &bcc.BatchCreateServersResponse{
		Message:     "success",
		Code:        "success",
		Requestid:   "mock-request-id",
		OrderId:     "mock-order-id",
		InstanceIds: nil,
	}, nil
}

func (m mockOpenstackSdk) BatchDeleteServers(ctx context.Context, req *bcc.BatchDeleteServersRequest) (rsp *bcc.BatchDeleteServersResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m mockOpenstackSdk) ShowOrder(ctx context.Context, req *bcc.ShowOrderRequest) (rsp *bcc.ShowOrderResponse, err error) {
	if strings.Contains(req.OrderId, "mockerr") {
		return &bcc.ShowOrderResponse{
			Status: "error",
			ErrMsg: "mockerr",
		}, nil
	}
	succOrderStr :=
		`{
  "message": "",
  "code": "",
  "requestid": "",
  "orderId": "3d5698f943c646baa73b25ee93f0f463",
  "status": "succ",
  "errMsg": "",
  "action": "CreateServers",
  "zoneName": "zoneD",
  "instances": [
    {
      "status": "Running",
      "name": "instance-7ifqzqt7",
      "adminPass": "4zz7AHNjWitR",
      "instanceId": "i-raOHHhzb",
      "floatingIp": "************",
      "sysVolume": [
        {
          "status": "InUse",
          "bootIndex": 0,
          "volumeId": "v-nkKdneLR",
          "name": "volume-oj6pd0dw",
          "deviceId": "/dev/vda",
          "type": "premium_ssd",
          "createTime": "",
          "sizeInGB": 20
        }
      ],
      "internalIp": "************",
      "cdsVolume": [
        {
          "status": "InUse",
          "bootIndex": -1,
          "volumeId": "v-F4Qjakg1",
          "name": "volume-weikkh4r",
          "deviceId": "/dev/vdb",
          "type": "premium_ssd",
          "createTime": "2023-07-26T13:50:27Z",
          "sizeInGB": 20
        }
      ],
      "flavor": "4-8192-20-0",
      "createTime": "2023-07-26T13:50:28Z",
      "metaData": "{deploy_set_id=scs-bj-tdrjdruhcbas-bj_nibzigbdclgk_0, entity_ids=scs-bj-tdrjdruhcbas-0.2}",
      "ipv6": ""
    }
  ]
}`
	succOrder := &bcc.ShowOrderResponse{}
	err = json.Unmarshal([]byte(succOrderStr), succOrder)
	return succOrder, err
}

func (m mockOpenstackSdk) ShowInstanceInfo(ctx context.Context, req *bcc.ShowInstanceRequest) (rsp *bcc.ShowInstanceResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m mockOpenstackSdk) ResizeInstance(ctx context.Context, req *bcc.ResizeInstanceRequest) (rsp *bcc.ResizeInstanceResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m mockOpenstackSdk) InstanceAttachedCdsList(ctx context.Context, req *bcc.CdsMountListRequest) (rsp *bcc.CdsMountListResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m mockOpenstackSdk) ResizeCds(ctx context.Context, req *bcc.CdsResizeRequest) (rsp *bcc.CdsResizeResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m mockOpenstackSdk) CreateCds(ctx context.Context, req *bcc.CdsCreateRequest) (rsp *bcc.CdsCreateResponse, err error) {
	if strings.Contains(req.Name, "mockerr") {
		return nil, errors.New("create cds failed")
	}

	if strings.Contains(req.Name, "mockexcept") {
		rsp = &bcc.CdsCreateResponse{}
		rsp.VolumeIDs = []string{}
		return rsp, nil
	}

	rsp = &bcc.CdsCreateResponse{}
	rsp.VolumeIDs = []string{"v-xxxxxx"}
	return rsp, nil
}

func (m mockOpenstackSdk) AttachCds(ctx context.Context, req *bcc.CdsAttachRequest) (rsp *bcc.CdsAttachResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m mockOpenstackSdk) DetachCds(ctx context.Context, req *bcc.CdsDetachRequest) (rsp *bcc.CdsDetachResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m mockOpenstackSdk) DeleteCds(ctx context.Context, req *bcc.CdsDeleteRequest) (rsp *bcc.CdsDeleteResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m mockOpenstackSdk) GetCdsDetail(ctx context.Context, req *bcc.GetCdsDetailRequest) (rsp *bcc.GetCdsDetailResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m mockOpenstackSdk) CreateDeploySet(ctx context.Context, req *bcc.CreateDeploySetRequest) (rsp *bcc.DeploySetIdsResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m mockOpenstackSdk) ShowDeploySet(ctx context.Context, req *bcc.ShowDeploySetRequest) (rsp *bcc.ShowDeploySetResponse, err error) {
	rsp = &bcc.ShowDeploySetResponse{}
	rsp.Concurrency = 2
	return rsp, nil
}

func (m mockOpenstackSdk) GetCdsStockWithZone(ctx context.Context, req *bcc.GetCdsStockRequest) (rsp *bcc.GetCdsStockResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func (m mockOpenstackSdk) SubnetDetail(ctx context.Context, req *bcc.GetSubnetDetailRequest) (rsp *bcc.GetSubnetDetailResponse, err error) {
	// TODO implement me
	panic("implement me")
}

func getMockOpenstackSdk() bcc.OpenStackService {
	return &mockOpenstackSdk{}
}

type mockStsSdk struct {
}

func (m mockStsSdk) GetAssumeRole(ctx context.Context, req *sts.GetAssumeRoleRequest) (rsp *sts.GetAssumeRoleResponse, err error) {
	return &sts.GetAssumeRoleResponse{
		AccessKeyId:     "mockAccessKeyId",
		SecretAccessKey: "mockSecretAccessKey",
		SessionToken:    "mockSessionToken",
		CreateTime:      "2020-01-01T00:00:00Z",
		Expiration:      "1800",
		UserId:          "mockUserId",
		RoleId:          "mockRoleId",
		Token: &sts.Token{
			Id:        "mockTokenId",
			ExpiresAt: "2020-01-01T00:30:00Z",
			IssuedAt:  "2020-01-01T00:00:00Z",
		},
		Code:      "mockCode",
		Message:   "mockMessage",
		RequestId: "mockRequestId",
	}, nil
}

func (m mockStsSdk) GetEncryptResourceAccountId(ctx context.Context) (rsp *sts.EncryptResourceAccountIdResponse, err error) {
	return &sts.EncryptResourceAccountIdResponse{
		EncryptAccountId: "mockEncryptAccountId",
		ResourceAk:       "mockResourceAk",
	}, nil
}

func (m mockStsSdk) GetOpenApiAuth(ctx context.Context, req *sts.GetOpenApiAuthRequest) (rsp *sts.GetOpenApiAuthResponse, err error) {
	return &sts.GetOpenApiAuthResponse{
		Auth: &common.Authentication{
			IamUserId:     "mockIamUserId",
			TransactionId: "mockTransactionId",
			Credential: &common.Credential{
				Ak:           "mockAccessKeyId",
				Sk:           "mockSecretAccessKey",
				SessionToken: "mockSessionToken",
			},
			ResourceAccount: &common.ResourceAccount{
				ResourceAk:       "mockResourceAk",
				EncryptAccountId: "mockEncryptAccountId",
			},
		},
	}, nil
}

func getMockStsSdk() sts.StsService {
	return &mockStsSdk{}
}

func getMockBccResource(t *testing.T) (*bccResourceOp, sqlmock.Sqlmock, func()) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("error creating mock database: %s", err)
	}
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("error creating gorm database: %s", err)
	}
	bccOrderModel = &model.Resource{
		ModelGorm: gormDB,
	}
	config = &Config{
		DefaultConfigList: []DefaultConfig{
			{
				AZone:                    "*",
				StoreType:                "DRAM",
				DefaultInstanceType:      "N6",
				AlternativeInstanceTypes: []string{"N5"},
				RootDiskStorageType:      "cloud_hp1",
				RootDiskUseLocalDisk:     false,
				DataDiskStorageType:      "cloud_hp1",
				DataDiskUseLocalDisk:     false,
			},
		},
		BCCAllowedFlavorList: []BCCAllowedFlavor{
			{
				MemoryCapacityInGB: 4,
				CpuList:            []int{1, 2, 4, 8, 16, 32, 64},
			},
		},
		InstanceTypesResizeAllowed: nil,
		ForbidLiveResize:           false,
		OrderOverTime:              120,
		MasterEndpoint:             "",
		BccImageId:                 "",
		BccImageIdForIpv6:          "",
		BccImageIdForPegaDB:        "",
		SubOrderSize:               4,
	}
	return &bccResourceOp{
			stsSdk:       getMockStsSdk(),
			openstackSdk: getMockOpenstackSdk(),
		}, mock, func() {
			_ = db.Close()
		}
}

func orderRecordHeaders() []string {
	return []string{
		"id", "order_id", "created_at", "status", "task_id", "azone", "app_id", "parameters", "err_msg",
	}
}

func orderSubRecordHeaders() []string {
	return []string{
		"id", "order_id", "p_order_id", "bcc_order_id", "created_at", "parameters", "status", "bcc_request", "bcc_response", "err_msg",
	}
}

func testCreateBccResourcesFromZero(t *testing.T) {
	br, mock, toDefer := getMockBccResource(t)
	defer toDefer()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderRecordHeaders()))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(),
			sqlmock.AnyArg(),
			OrderStatusToCreate,
			"mock_task_id",
			"AZONE-bjyz",
			"mock_app_id_01",
			sqlmock.AnyArg(),
			sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderSubRecordHeaders()))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "", sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToCreate, "", "", "").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(),
			sqlmock.AnyArg(),
			"mock-order-id",
			sqlmock.AnyArg(),
			sqlmock.AnyArg(),
			OrderStatusCreating,
			sqlmock.AnyArg(),
			"",
			"",
			sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(),
			sqlmock.AnyArg(),
			OrderStatusCreating,
			"mock_task_id",
			"AZONE-bjyz",
			"mock_app_id_01",
			sqlmock.AnyArg(),
			sqlmock.AnyArg(),
			sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()
	orderid, err := br.CreateBccResources(ctx, &CreateBccResourceParams{
		AppID:                "mock_app_id_01",
		UserID:               "mock_user_id_01",
		Product:              "scs",
		ImageID:              "mock_image_id_01",
		VpcID:                "mock_vpc_id_01",
		CustomerDeploySetIDs: nil,
		LogicalZone:          "zoneA",
		Azone:                "AZONE-bjyz",
		StoreType:            map[string]string{"redis": "DRAM"},
		Items: []CreateBccResourceParamsItem{
			{
				Specification: specification.Specification{
					AvailableVolume:      1,
					Name:                 "cache.n1.small",
					CPUCount:             2,
					MemoryCapacityInGB:   4,
					RootDiskCapacityInGB: 20,
					DataDiskCapacityInGB: 20,
				},
				Subnet:          "mock_subnet_id_01",
				Engine:          "redis",
				Count:           1,
				DeploySetID:     "mock_app_id_01-0",
				EntityIDs:       []string{"mock_entity_id_01.0"},
				SecurityGroupID: "mock_sg_id_01",
			},
		},
		Retry:  0,
		TaskID: "mock_task_id",
	})
	if err != nil {
		t.Fatalf("create bcc resources failed: %s", err)
	}
	if orderid == "" {
		t.Fatalf("create bcc resources failed: orderid is empty")
	}
}

func testCreateBccResourcesError(t *testing.T) {
	br, mock, toDefer := getMockBccResource(t)
	defer toDefer()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderRecordHeaders()))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToCreate, "mock_task_id",
			"AZONE-bjyz", "mock_app_id_01", sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderSubRecordHeaders()))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "", sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToCreate, "", "", "").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "", sqlmock.AnyArg(), sqlmock.AnyArg(),
			OrderStatusError, sqlmock.AnyArg(), "", sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToRollback, "mock_task_id", "AZONE-bjyz",
			"mock_app_id_01", sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()
	_, err := br.CreateBccResources(ctx, &CreateBccResourceParams{
		AppID:                "mock_app_id_01",
		UserID:               "mock_user_id_01",
		Product:              "scs",
		ImageID:              "mock_image_id_01",
		VpcID:                "mock_vpc_id_01",
		CustomerDeploySetIDs: nil,
		LogicalZone:          "zoneA",
		Azone:                "AZONE-bjyz",
		StoreType: map[string]string{"redis": "DRAM", x1model.EngineMc: "DRAM", x1model.EngineMcProxy: "DRAM"},
		Items: []CreateBccResourceParamsItem{
			{
				Specification: specification.Specification{
					AvailableVolume:      1,
					Name:                 "cache.n1.small",
					CPUCount:             2,
					MemoryCapacityInGB:   4,
					RootDiskCapacityInGB: 20,
					DataDiskCapacityInGB: 20,
				},
				Subnet:          "mock_subnet_id_01",
				Engine:          "redis",
				Count:           1,
				DeploySetID:     "mockerr_app_id_01-0",
				EntityIDs:       []string{"mock_entity_id_01.0"},
				SecurityGroupID: "mock_sg_id_01",
			},
			{
				Specification: specification.Specification{
					AvailableVolume:      1,
					Name:                 "cache.n1.small",
					CPUCount:             2,
					MemoryCapacityInGB:   4,
					RootDiskCapacityInGB: 20,
					DataDiskCapacityInGB: 20,
				},
				Subnet:          "mock_subnet_id_01",
				Engine:          x1model.EngineMc,
				Count:           1,
				DeploySetID:     "mockerr_app_id_01-0",
				EntityIDs:       []string{"mock_entity_id_01.0"},
				SecurityGroupID: "mock_sg_id_01",
			},
			{
				Specification: specification.Specification{
					AvailableVolume:      1,
					Name:                 "cache.n1.small",
					CPUCount:             2,
					MemoryCapacityInGB:   4,
					RootDiskCapacityInGB: 20,
					DataDiskCapacityInGB: 20,
				},
				Subnet:          "mock_subnet_id_01",
				Engine:          x1model.EngineMcProxy,
				Count:           1,
				DeploySetID:     "mockerr_app_id_01-0",
				EntityIDs:       []string{"mock_entity_id_01.0"},
				SecurityGroupID: "mock_sg_id_01",
			},
		},
		Retry:  0,
		TaskID: "mock_task_id",
	})
	if err == nil {
		t.Fatalf("create bcc resources expect error, but got nil")
	}
}

func testCreateBccResourcesWithFailoverTag(t *testing.T) {
	br, mock, toDefer := getMockBccResource(t)
	defer toDefer()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderRecordHeaders()))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToCreate, "mock_task_id",
			"AZONE-bjyz", "mock_app_id_01", sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderSubRecordHeaders()))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "", sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToCreate, "", "", "").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "", sqlmock.AnyArg(), sqlmock.AnyArg(),
			OrderStatusError, sqlmock.AnyArg(), "", sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToRollback, "mock_task_id", "AZONE-bjyz",
			"mock_app_id_01", sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()
	_, err := br.CreateBccResources(ctx, &CreateBccResourceParams{
		AppID:                "mock_app_id_01",
		UserID:               "mock_user_id_01",
		Product:              "scs",
		ImageID:              "mock_image_id_01",
		VpcID:                "mock_vpc_id_01",
		CustomerDeploySetIDs: nil,
		LogicalZone:          "zoneA",
		Azone:                "AZONE-bjyz",
		StoreType: map[string]string{"redis": "DRAM", x1model.EngineMc: "DRAM", x1model.EngineMcProxy: "DRAM"},
		Items: []CreateBccResourceParamsItem{
			{
				Specification: specification.Specification{
					AvailableVolume:      1,
					Name:                 "cache.n1.small",
					CPUCount:             2,
					MemoryCapacityInGB:   4,
					RootDiskCapacityInGB: 20,
					DataDiskCapacityInGB: 20,
				},
				Subnet:              "mock_subnet_id_01",
				Engine:              "redis",
				Count:               1,
				DeploySetID:         "mockerr_app_id_01-0",
				EntityIDs:           []string{"mock_entity_id_01.0"},
				SecurityGroupID:     "mock_sg_id_01",
				ReplacedInstanceIDs: []string{"i-xxxxxx"},
			},
			{
				Specification: specification.Specification{
					AvailableVolume:      1,
					Name:                 "cache.n1.small",
					CPUCount:             2,
					MemoryCapacityInGB:   4,
					RootDiskCapacityInGB: 20,
					DataDiskCapacityInGB: 20,
				},
				Subnet:              "mock_subnet_id_01",
				Engine:              x1model.EngineMc,
				Count:               1,
				DeploySetID:         "mockerr_app_id_01-0",
				EntityIDs:           []string{"mock_entity_id_01.0"},
				SecurityGroupID:     "mock_sg_id_01",
				ReplacedInstanceIDs: []string{"MOCKUUID"},
			},
			{
				Specification: specification.Specification{
					AvailableVolume:      1,
					Name:                 "cache.n1.small",
					CPUCount:             2,
					MemoryCapacityInGB:   4,
					RootDiskCapacityInGB: 20,
					DataDiskCapacityInGB: 20,
				},
				Subnet:              "mock_subnet_id_01",
				Engine:              x1model.EngineMcProxy,
				Count:               1,
				DeploySetID:         "mockerr_app_id_01-0",
				EntityIDs:           []string{"mock_entity_id_01.0"},
				SecurityGroupID:     "mock_sg_id_01",
				ReplacedInstanceIDs: []string{"MOCKUUID_NOT_FOUND"},
			},
		},
		Retry:  0,
		TaskID: "mock_task_id",
	})
	if err == nil {
		t.Fatalf("create bcc resources expect error, but got nil")
	}
}

func testCreateBccResourcesWithPAASDeploySet(t *testing.T) {
	br, mock, toDefer := getMockBccResource(t)
	defer toDefer()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderRecordHeaders()))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToCreate, "mock_task_id",
			"AZONE-bjyz", "mock_app_id_01", sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderSubRecordHeaders()))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "", sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToCreate, "", "", "").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "", sqlmock.AnyArg(), sqlmock.AnyArg(),
			OrderStatusError, sqlmock.AnyArg(), "", sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToRollback, "mock_task_id", "AZONE-bjyz",
			"mock_app_id_01", sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()
	_, err := br.CreateBccResources(ctx, &CreateBccResourceParams{
		AppID:   "mock_app_id_01",
		UserID:  "mock_user_id_01",
		Product: "scs",
		ImageID: "mock_image_id_01",
		VpcID:   "mock_vpc_id_01",
		PAASDeploySetStrategies: []PAASDeploySetStrategy{{
			ID:          "scs-dset-xxx",
			Concurrency: 1,
			Policy:      "HOST_HA",
			Quota:       300000,
		}},
		LogicalZone: "zoneA",
		Azone:       "AZONE-bjyz",
		StoreType: map[string]string{"redis": "DRAM", x1model.EngineMc: "DRAM", x1model.EngineMcProxy: "DRAM"},
		Items: []CreateBccResourceParamsItem{
			{
				Specification: specification.Specification{
					AvailableVolume:      1,
					Name:                 "cache.n1.small",
					CPUCount:             2,
					MemoryCapacityInGB:   4,
					RootDiskCapacityInGB: 20,
					DataDiskCapacityInGB: 20,
				},
				Subnet:              "mock_subnet_id_01",
				Engine:              "redis",
				Count:               1,
				DeploySetID:         "mockerr_app_id_01-0",
				EntityIDs:           []string{"mock_entity_id_01.0"},
				SecurityGroupID:     "mock_sg_id_01",
				ReplacedInstanceIDs: []string{"i-xxxxxx"},
			},
			{
				Specification: specification.Specification{
					AvailableVolume:      1,
					Name:                 "cache.n1.small",
					CPUCount:             2,
					MemoryCapacityInGB:   4,
					RootDiskCapacityInGB: 20,
					DataDiskCapacityInGB: 20,
				},
				Subnet:              "mock_subnet_id_01",
				Engine:              x1model.EngineMc,
				Count:               1,
				DeploySetID:         "mockerr_app_id_01-0",
				EntityIDs:           []string{"mock_entity_id_01.0"},
				SecurityGroupID:     "mock_sg_id_01",
				ReplacedInstanceIDs: []string{"MOCKUUID"},
			},
			{
				Specification: specification.Specification{
					AvailableVolume:      1,
					Name:                 "cache.n1.small",
					CPUCount:             2,
					MemoryCapacityInGB:   4,
					RootDiskCapacityInGB: 20,
					DataDiskCapacityInGB: 20,
				},
				Subnet:              "mock_subnet_id_01",
				Engine:              x1model.EngineMcProxy,
				Count:               1,
				DeploySetID:         "mockerr_app_id_01-0",
				EntityIDs:           []string{"mock_entity_id_01.0"},
				SecurityGroupID:     "mock_sg_id_01",
				ReplacedInstanceIDs: []string{"MOCKUUID_NOT_FOUND"},
			},
		},
		Retry:  0,
		TaskID: "mock_task_id",
	})
	if err == nil {
		t.Fatalf("create bcc resources expect error, but got nil")
	}
}

func testCreateBccResourcesErrorRetry(t *testing.T) {
	br, mock, toDefer := getMockBccResource(t)
	defer toDefer()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderRecordHeaders()).AddRow(
		0,
		"mock-order-id",
		time.Now(),
		OrderStatusToRollback,
		"mock_task_id",
		"AZONE-bjyz",
		"mock_app_id_01",
		"{\"AppID\":\"mock_app_id_01\",\"UserID\":\"mock_user_id_01\","+
			"\"Product\":\"scs\",\"ImageID\":\"mock_image_id_01\",\"VpcID\":\"mock_vpc_id_01\","+
			"\"CustomerDeploySetIDs\":null,\"LogicalZone\":\"zoneA\",\"Azone\":\"AZONE-bjyz\","+
			"\"StoreType\":\"DRAM\",\"Items\":[{\"Specification\":{\"AvailableVolume\":1,"+
			"\"Name\":\"cache.n1.small\",\"CPUCount\":2,\"MemoryCapacityInGB\":4,"+
			"\"RootDiskCapacityInGB\":20,\"DataDiskCapacityInGB\":20},\"Subnet\":\"mock_subnet_id_01\","+
			"\"Engine\":\"redis\",\"Count\":1,\"DeploySetID\":\"mock_app_id_01-0\","+
			"\"EntityIDs\":[\"mock_entity_id_01.0\"],\"SecurityGroupID\":\"mock_sg_id_01\"}],"+
			"\"Retry\":0,\"TaskID\":\"mock_task_id\"}",
		""))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderSubRecordHeaders()).AddRow(
		0,
		"mock-sub-order-id-xx-01",
		"mock-order-id",
		"",
		time.Now(),
		"{\"AppID\":\"mock_app_id_01\",\"UserID\":\"mock_user_id_01\",\"Product\":\"scs\","+
			"\"ImageID\":\"mock_image_id_01\",\"VpcID\":\"mock_vpc_id_01\","+
			"\"CustomerDeploySetIDs\":null,\"LogicalZone\":\"zoneA\",\"Azone\":\"AZONE-bjyz\","+
			"\"StoreType\":\"DRAM\",\"Items\":[{\"Specification\":{\"AvailableVolume\":1,"+
			"\"Name\":\"cache.n1.small\",\"CPUCount\":2,\"MemoryCapacityInGB\":4,"+
			"\"RootDiskCapacityInGB\":20,\"DataDiskCapacityInGB\":20},\"Subnet\":\"mock_subnet_id_01\","+
			"\"Engine\":\"redis\",\"Count\":1,\"DeploySetID\":\"mock_app_id_01-0\","+
			"\"EntityIDs\":[\"mock_entity_id_01.0\"],\"SecurityGroupID\":\"mock_sg_id_01\"}],"+
			"\"Retry\":0,\"TaskID\":\"mock_task_id\"}",
		OrderStatusError,
		"{\"orderOverTime\":120,\"createInstances\":[{\"instanceType\":\"N6\",\"cpuCount\":2,"+
			"\"zoneName\":\"zoneA\",\"memoryCapacityInGB\":4,\"rootDiskSizeInGb\":20,"+
			"\"rootDiskStorageType\":\"cloud_hp1\",\"createCdsList\":[{\"storageType\":\"cloud_hp1\","+
			"\"cdsSizeInGB\":20}],\"name\":\"\",\"imageId\":\"mock_image_id_01\",\"purchaseCount\":1,"+
			"\"subnetId\":\"mock_subnet_id_01\",\"securityGroupId\":\"mock_sg_id_01\","+
			"\"billing\":{\"paymentTiming\":\"Postpaid\"},\"relationTag\":0,\"rootOnLocal\":0,"+
			"\"tags\":null,\"deploySetId\":\"mock_app_id_01-0\",\"userData\":\"mock_app_id_01\","+
			"\"configs\":[{\"key\":\"entity_ids\",\"configValue\":\"mock_entity_id_01.0\"},"+
			"{\"key\":\"flavor_type\",\"configValue\":\"1\"},{\"key\":\"instance_type\",\"configValue\":\"3\"},"+
			"{\"key\":\"image_type\",\"configValue\":\"3\"},{\"key\":\"master_endpoint\",\"configValue\":\"\"},"+
			"{\"key\":\"env_type\",\"configValue\":\"public_cloud\"},{\"key\":\"deploy_set_id\","+
			"\"configValue\":\"mock_app_id_01-0\"}]}]}",
		"",
		""))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "", sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToCreate, "", "", "").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToCreate, "mock_task_id", "AZONE-bjyz",
			"mock_app_id_01", sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "mock-order-id", sqlmock.AnyArg(), sqlmock.AnyArg(),
			OrderStatusCreating, sqlmock.AnyArg(), sqlmock.AnyArg(), "", sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusCreating, "mock_task_id", "AZONE-bjyz",
			"mock_app_id_01", sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()
	_, err := br.CreateBccResources(ctx, &CreateBccResourceParams{
		AppID:                "mock_app_id_01",
		UserID:               "mock_user_id_01",
		Product:              "scs",
		ImageID:              "mock_image_id_01",
		VpcID:                "mock_vpc_id_01",
		CustomerDeploySetIDs: nil,
		LogicalZone:          "zoneA",
		Azone:                "AZONE-bjyz",
		StoreType:            map[string]string{"redis": "DRAM"},
		Items: []CreateBccResourceParamsItem{
			{
				Specification: specification.Specification{
					AvailableVolume:      1,
					Name:                 "cache.n1.small",
					CPUCount:             2,
					MemoryCapacityInGB:   4,
					RootDiskCapacityInGB: 20,
					DataDiskCapacityInGB: 20,
				},
				Subnet:          "mock_subnet_id_01",
				Engine:          "redis",
				Count:           1,
				DeploySetID:     "mock_app_id_01-0",
				EntityIDs:       []string{"mock_entity_id_01.0"},
				SecurityGroupID: "mock_sg_id_01",
			},
		},
		Retry:  0,
		TaskID: "mock_task_id",
	})
	if err != nil {
		t.Fatalf("create bcc resources error: %v", err)
	}
}

func testCreateBccResourcesErrorRetrySaveSubOrderError(t *testing.T) {
	br, mock, toDefer := getMockBccResource(t)
	defer toDefer()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderRecordHeaders()).AddRow(
		0,
		"mock-order-id",
		time.Now(),
		OrderStatusToRollback,
		"mock_task_id",
		"AZONE-bjyz",
		"mock_app_id_01",
		"{\"AppID\":\"mock_app_id_01\",\"UserID\":\"mock_user_id_01\",\"Product\":\"scs\",\"ImageID\":\"mock_image_id_01\","+
			"\"VpcID\":\"mock_vpc_id_01\",\"CustomerDeploySetIDs\":null,\"LogicalZone\":\"zoneA\",\"Azone\":\"AZONE-bjyz\","+
			"\"StoreType\":\"DRAM\",\"Items\":[{\"Specification\":{\"AvailableVolume\":1,\"Name\":\"cache.n1.small\","+
			"\"CPUCount\":2,\"MemoryCapacityInGB\":4,\"RootDiskCapacityInGB\":20,\"DataDiskCapacityInGB\":20},"+
			"\"Subnet\":\"mock_subnet_id_01\",\"Engine\":\"redis\",\"Count\":1,\"DeploySetID\":\"mock_app_id_01-0\","+
			"\"EntityIDs\":[\"mock_entity_id_01.0\"],\"SecurityGroupID\":\"mock_sg_id_01\"}],\"Retry\":0,\"TaskID\":\"mock_task_id\"}",
		""))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderSubRecordHeaders()).AddRow(
		0,
		"mock-sub-order-id-xx-01",
		"mock-order-id",
		"",
		time.Now(),
		"{\"AppID\":\"mock_app_id_01\",\"UserID\":\"mock_user_id_01\",\"Product\":\"scs\",\"ImageID\":\"mock_image_id_01\","+
			"\"VpcID\":\"mock_vpc_id_01\",\"CustomerDeploySetIDs\":null,\"LogicalZone\":\"zoneA\",\"Azone\":\"AZONE-bjyz\","+
			"\"StoreType\":\"DRAM\",\"Items\":[{\"Specification\":{\"AvailableVolume\":1,\"Name\":\"cache.n1.small\","+
			"\"CPUCount\":2,\"MemoryCapacityInGB\":4,\"RootDiskCapacityInGB\":20,\"DataDiskCapacityInGB\":20},"+
			"\"Subnet\":\"mock_subnet_id_01\",\"Engine\":\"redis\",\"Count\":1,\"DeploySetID\":\"mock_app_id_01-0\","+
			"\"EntityIDs\":[\"mock_entity_id_01.0\"],\"SecurityGroupID\":\"mock_sg_id_01\"}],\"Retry\":0,\"TaskID\":\"mock_task_id\"}",
		OrderStatusError,
		"{\"orderOverTime\":120,\"createInstances\":[{\"instanceType\":\"N6\",\"cpuCount\":2,"+
			"\"zoneName\":\"zoneA\",\"memoryCapacityInGB\":4,\"rootDiskSizeInGb\":20,"+
			"\"rootDiskStorageType\":\"cloud_hp1\",\"createCdsList\":[{\"storageType\":\"cloud_hp1\","+
			"\"cdsSizeInGB\":20}],\"name\":\"\",\"imageId\":\"mock_image_id_01\",\"purchaseCount\":1,"+
			"\"subnetId\":\"mock_subnet_id_01\",\"securityGroupId\":\"mock_sg_id_01\","+
			"\"billing\":{\"paymentTiming\":\"Postpaid\"},\"relationTag\":0,\"rootOnLocal\":0,"+
			"\"tags\":null,\"deploySetId\":\"mock_app_id_01-0\",\"userData\":\"mock_app_id_01\","+
			"\"configs\":[{\"key\":\"entity_ids\",\"configValue\":\"mock_entity_id_01.0\"},"+
			"{\"key\":\"flavor_type\",\"configValue\":\"1\"},{\"key\":\"instance_type\",\"configValue\":\"3\"},"+
			"{\"key\":\"image_type\",\"configValue\":\"3\"},{\"key\":\"master_endpoint\",\"configValue\":\"\"},"+
			"{\"key\":\"env_type\",\"configValue\":\"public_cloud\"},{\"key\":\"deploy_set_id\","+
			"\"configValue\":\"mock_app_id_01-0\"}]}]}",
		"",
		""))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "", sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToCreate, "", "", "").
		WillReturnError(errors.New("mock error"))
	mock.ExpectRollback()

	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()
	_, err := br.CreateBccResources(ctx, &CreateBccResourceParams{
		AppID:                "mock_app_id_01",
		UserID:               "mock_user_id_01",
		Product:              "scs",
		ImageID:              "mock_image_id_01",
		VpcID:                "mock_vpc_id_01",
		CustomerDeploySetIDs: nil,
		LogicalZone:          "zoneA",
		Azone:                "AZONE-bjyz",
		StoreType:            map[string]string{"redis": "DRAM"},
		Items: []CreateBccResourceParamsItem{
			{
				Specification: specification.Specification{
					AvailableVolume:      1,
					Name:                 "cache.n1.small",
					CPUCount:             2,
					MemoryCapacityInGB:   4,
					RootDiskCapacityInGB: 20,
					DataDiskCapacityInGB: 20,
				},
				Subnet:          "mock_subnet_id_01",
				Engine:          "redis",
				Count:           1,
				DeploySetID:     "mock_app_id_01-0",
				EntityIDs:       []string{"mock_entity_id_01.0"},
				SecurityGroupID: "mock_sg_id_01",
			},
		},
		Retry:  0,
		TaskID: "mock_task_id",
	})
	if err == nil {
		t.Fatalf("create bcc resources expect error, but got nil")
	}
}

func testCreateBccResourcesErrorRetrySaveOrderError(t *testing.T) {
	br, mock, toDefer := getMockBccResource(t)
	defer toDefer()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderRecordHeaders()).AddRow(
		0,
		"mock-order-id",
		time.Now(),
		OrderStatusToRollback,
		"mock_task_id",
		"AZONE-bjyz",
		"mock_app_id_01",
		"{\"AppID\":\"mock_app_id_01\",\"UserID\":\"mock_user_id_01\",\"Product\":\"scs\",\"ImageID\":\"mock_image_id_01\","+
			"\"VpcID\":\"mock_vpc_id_01\",\"CustomerDeploySetIDs\":null,\"LogicalZone\":\"zoneA\",\"Azone\":\"AZONE-bjyz\","+
			"\"StoreType\":\"DRAM\",\"Items\":[{\"Specification\":{\"AvailableVolume\":1,\"Name\":\"cache.n1.small\","+
			"\"CPUCount\":2,\"MemoryCapacityInGB\":4,\"RootDiskCapacityInGB\":20,\"DataDiskCapacityInGB\":20},"+
			"\"Subnet\":\"mock_subnet_id_01\",\"Engine\":\"redis\",\"Count\":1,\"DeploySetID\":\"mock_app_id_01-0\","+
			"\"EntityIDs\":[\"mock_entity_id_01.0\"],\"SecurityGroupID\":\"mock_sg_id_01\"}],\"Retry\":0,\"TaskID\":\"mock_task_id\"}",
		""))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderSubRecordHeaders()).AddRow(
		0,
		"mock-sub-order-id-xx-01",
		"mock-order-id",
		"",
		time.Now(),
		"{\"AppID\":\"mock_app_id_01\",\"UserID\":\"mock_user_id_01\",\"Product\":\"scs\",\"ImageID\":\"mock_image_id_01\","+
			"\"VpcID\":\"mock_vpc_id_01\",\"CustomerDeploySetIDs\":null,\"LogicalZone\":\"zoneA\",\"Azone\":\"AZONE-bjyz\","+
			"\"StoreType\":\"DRAM\",\"Items\":[{\"Specification\":{\"AvailableVolume\":1,\"Name\":\"cache.n1.small\","+
			"\"CPUCount\":2,\"MemoryCapacityInGB\":4,\"RootDiskCapacityInGB\":20,\"DataDiskCapacityInGB\":20},"+
			"\"Subnet\":\"mock_subnet_id_01\",\"Engine\":\"redis\",\"Count\":1,\"DeploySetID\":\"mock_app_id_01-0\","+
			"\"EntityIDs\":[\"mock_entity_id_01.0\"],\"SecurityGroupID\":\"mock_sg_id_01\"}],\"Retry\":0,\"TaskID\":\"mock_task_id\"}",
		OrderStatusError,
		"{\"orderOverTime\":120,\"createInstances\":[{\"instanceType\":\"N6\",\"cpuCount\":2,"+
			"\"zoneName\":\"zoneA\",\"memoryCapacityInGB\":4,\"rootDiskSizeInGb\":20,"+
			"\"rootDiskStorageType\":\"cloud_hp1\",\"createCdsList\":[{\"storageType\":\"cloud_hp1\","+
			"\"cdsSizeInGB\":20}],\"name\":\"\",\"imageId\":\"mock_image_id_01\",\"purchaseCount\":1,"+
			"\"subnetId\":\"mock_subnet_id_01\",\"securityGroupId\":\"mock_sg_id_01\","+
			"\"billing\":{\"paymentTiming\":\"Postpaid\"},\"relationTag\":0,\"rootOnLocal\":0,"+
			"\"tags\":null,\"deploySetId\":\"mock_app_id_01-0\",\"userData\":\"mock_app_id_01\","+
			"\"configs\":[{\"key\":\"entity_ids\",\"configValue\":\"mock_entity_id_01.0\"},"+
			"{\"key\":\"flavor_type\",\"configValue\":\"1\"},{\"key\":\"instance_type\",\"configValue\":\"3\"},"+
			"{\"key\":\"image_type\",\"configValue\":\"3\"},{\"key\":\"master_endpoint\",\"configValue\":\"\"},"+
			"{\"key\":\"env_type\",\"configValue\":\"public_cloud\"},{\"key\":\"deploy_set_id\","+
			"\"configValue\":\"mock_app_id_01-0\"}]}]}",
		"",
		""))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "", sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToCreate, "", "", "").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), OrderStatusToCreate, "mock_task_id", "AZONE-bjyz",
			"mock_app_id_01", sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnError(errors.New("mock error"))
	mock.ExpectRollback()

	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()
	_, err := br.CreateBccResources(ctx, &CreateBccResourceParams{
		AppID:                "mock_app_id_01",
		UserID:               "mock_user_id_01",
		Product:              "scs",
		ImageID:              "mock_image_id_01",
		VpcID:                "mock_vpc_id_01",
		CustomerDeploySetIDs: nil,
		LogicalZone:          "zoneA",
		Azone:                "AZONE-bjyz",
		StoreType:            map[string]string{"redis": "DRAM"},
		Items: []CreateBccResourceParamsItem{
			{
				Specification: specification.Specification{
					AvailableVolume:      1,
					Name:                 "cache.n1.small",
					CPUCount:             2,
					MemoryCapacityInGB:   4,
					RootDiskCapacityInGB: 20,
					DataDiskCapacityInGB: 20,
				},
				Subnet:          "mock_subnet_id_01",
				Engine:          "redis",
				Count:           1,
				DeploySetID:     "mock_app_id_01-0",
				EntityIDs:       []string{"mock_entity_id_01.0"},
				SecurityGroupID: "mock_sg_id_01",
			},
		},
		Retry:  0,
		TaskID: "mock_task_id",
	})
	if err == nil {
		t.Fatalf("create bcc resources expect error, but got nil")
	}
}

func Test_bccResourceOp_CreateBccResources(t *testing.T) {
	t.Run("create bcc resources from zero", testCreateBccResourcesFromZero)
	t.Run("create bcc resources error", testCreateBccResourcesError)
	t.Run("create bcc resources with failover tag", testCreateBccResourcesWithFailoverTag)
	t.Run("create bcc resources with paas deployset", testCreateBccResourcesWithPAASDeploySet)
	t.Run("create bcc resources error retry", testCreateBccResourcesErrorRetry)
	t.Run("create bcc resources error retry save sub order error", testCreateBccResourcesErrorRetrySaveSubOrderError)
	t.Run("create bcc resources error retry save order error", testCreateBccResourcesErrorRetrySaveOrderError)
}

func testShowBccResourcesByOrderNormal(t *testing.T) {
	br, mock, toDefer := getMockBccResource(t)
	defer toDefer()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderRecordHeaders()).AddRow(
		0,
		"mock-order-id",
		time.Now(),
		OrderStatusCreating,
		"mock_task_id",
		"AZONE-bjyz",
		"mock_app_id_01",
		"{\"AppID\":\"mock_app_id_01\",\"UserID\":\"mock_user_id_01\",\"Product\":\"scs\",\"ImageID\":\"mock_image_id_01\","+
			"\"VpcID\":\"mock_vpc_id_01\",\"CustomerDeploySetIDs\":null,\"LogicalZone\":\"zoneA\",\"Azone\":\"AZONE-bjyz\","+
			"\"StoreType\":\"DRAM\",\"Items\":[{\"Specification\":{\"AvailableVolume\":1,\"Name\":\"cache.n1.small\","+
			"\"CPUCount\":2,\"MemoryCapacityInGB\":4,\"RootDiskCapacityInGB\":20,\"DataDiskCapacityInGB\":20},"+
			"\"Subnet\":\"mock_subnet_id_01\",\"Engine\":\"redis\",\"Count\":1,\"DeploySetID\":\"mock_app_id_01-0\","+
			"\"EntityIDs\":[\"mock_entity_id_01.0\"],\"SecurityGroupID\":\"mock_sg_id_01\"}],\"Retry\":0,\"TaskID\":\"mock_task_id\"}",
		""))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderSubRecordHeaders()).AddRow(
		0,
		"mock-sub-order-id",
		"mock-order-id",
		"mock-bcc-order-id",
		time.Now(),
		"{\"AppID\":\"mock_app_id_01\",\"UserID\":\"mock_user_id_01\",\"Product\":\"scs\",\"ImageID\":\"mock_image_id_01\","+
			"\"VpcID\":\"mock_vpc_id_01\",\"CustomerDeploySetIDs\":null,\"LogicalZone\":\"zoneA\",\"Azone\":\"AZONE-bjyz\","+
			"\"StoreType\":\"DRAM\",\"Items\":[{\"Specification\":{\"AvailableVolume\":1,\"Name\":\"cache.n1.small\","+
			"\"CPUCount\":2,\"MemoryCapacityInGB\":4,\"RootDiskCapacityInGB\":20,\"DataDiskCapacityInGB\":20},"+
			"\"Subnet\":\"mock_subnet_id_01\",\"Engine\":\"redis\",\"Count\":1,\"DeploySetID\":\"mock_app_id_01-0\","+
			"\"EntityIDs\":[\"mock_entity_id_01.0\"],\"SecurityGroupID\":\"mock_sg_id_01\"}],\"Retry\":0,\"TaskID\":\"mock_task_id\"}",
		OrderStatusCreating,
		"{\"orderOverTime\":120,\"createInstances\":[{\"instanceType\":\"N6\",\"cpuCount\":2,"+
			"\"zoneName\":\"zoneA\",\"memoryCapacityInGB\":4,\"rootDiskSizeInGb\":20,"+
			"\"rootDiskStorageType\":\"cloud_hp1\",\"createCdsList\":[{\"storageType\":\"cloud_hp1\","+
			"\"cdsSizeInGB\":20}],\"name\":\"\",\"imageId\":\"mock_image_id_01\",\"purchaseCount\":1,"+
			"\"subnetId\":\"mock_subnet_id_01\",\"securityGroupId\":\"mock_sg_id_01\","+
			"\"billing\":{\"paymentTiming\":\"Postpaid\"},\"relationTag\":0,\"rootOnLocal\":0,"+
			"\"tags\":null,\"deploySetId\":\"mock_app_id_01-0\",\"userData\":\"mock_app_id_01\","+
			"\"configs\":[{\"key\":\"entity_ids\",\"configValue\":\"mock_entity_id_01.0\"},"+
			"{\"key\":\"flavor_type\",\"configValue\":\"1\"},{\"key\":\"instance_type\",\"configValue\":\"3\"},"+
			"{\"key\":\"image_type\",\"configValue\":\"3\"},{\"key\":\"master_endpoint\",\"configValue\":\"\"},"+
			"{\"key\":\"env_type\",\"configValue\":\"public_cloud\"},{\"key\":\"deploy_set_id\","+
			"\"configValue\":\"mock_app_id_01-0\"}]}]}",
		"",
		""))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), "mock-order-id", "mock-bcc-order-id", sqlmock.AnyArg(), sqlmock.AnyArg(),
			OrderStatusCreated, sqlmock.AnyArg(), sqlmock.AnyArg(), "").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs("mock-order-id", sqlmock.AnyArg(), OrderStatusCreated, "mock_task_id",
			"AZONE-bjyz", "mock_app_id_01", sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	resp, err := br.ShowBccResourcesByOrder(context.Background(), &ShowBccResourcesParams{
		UserID:  "mock_user_id_01",
		OrderID: "mock-order-id",
	})
	if err != nil {
		t.Fatalf("show bcc resources by order error: %v", err)
	}
	if len(resp) != 1 {
		t.Fatalf("show bcc resources by order error: %v", err)
	}
}

func testShowBccResourcesByOrderError(t *testing.T) {
	br, mock, toDefer := getMockBccResource(t)
	defer toDefer()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderRecordHeaders()).AddRow(
		0,
		"mock-order-id",
		time.Now(),
		OrderStatusCreating,
		"mock_task_id",
		"AZONE-bjyz",
		"mock_app_id_01",
		"{\"AppID\":\"mock_app_id_01\",\"UserID\":\"mock_user_id_01\",\"Product\":\"scs\",\"ImageID\":\"mock_image_id_01\","+
			"\"VpcID\":\"mock_vpc_id_01\",\"CustomerDeploySetIDs\":null,\"LogicalZone\":\"zoneA\",\"Azone\":\"AZONE-bjyz\","+
			"\"StoreType\":\"DRAM\",\"Items\":[{\"Specification\":{\"AvailableVolume\":1,\"Name\":\"cache.n1.small\","+
			"\"CPUCount\":2,\"MemoryCapacityInGB\":4,\"RootDiskCapacityInGB\":20,\"DataDiskCapacityInGB\":20},"+
			"\"Subnet\":\"mock_subnet_id_01\",\"Engine\":\"redis\",\"Count\":1,\"DeploySetID\":\"mock_app_id_01-0\","+
			"\"EntityIDs\":[\"mock_entity_id_01.0\"],\"SecurityGroupID\":\"mock_sg_id_01\"}],\"Retry\":0,\"TaskID\":\"mock_task_id\"}",
		""))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectQuery("^SELECT .*").WillReturnRows(sqlmock.NewRows(orderSubRecordHeaders()).AddRow(
		0,
		"mock-sub-order-id",
		"mock-order-id",
		"mockerr-bcc-order-id",
		time.Now(),
		"{\"AppID\":\"mock_app_id_01\",\"UserID\":\"mock_user_id_01\",\"Product\":\"scs\",\"ImageID\":\"mock_image_id_01\","+
			"\"VpcID\":\"mock_vpc_id_01\",\"CustomerDeploySetIDs\":null,\"LogicalZone\":\"zoneA\",\"Azone\":\"AZONE-bjyz\","+
			"\"StoreType\":\"DRAM\",\"Items\":[{\"Specification\":{\"AvailableVolume\":1,\"Name\":\"cache.n1.small\","+
			"\"CPUCount\":2,\"MemoryCapacityInGB\":4,\"RootDiskCapacityInGB\":20,\"DataDiskCapacityInGB\":20},"+
			"\"Subnet\":\"mock_subnet_id_01\",\"Engine\":\"redis\",\"Count\":1,\"DeploySetID\":\"mock_app_id_01-0\","+
			"\"EntityIDs\":[\"mock_entity_id_01.0\"],\"SecurityGroupID\":\"mock_sg_id_01\"}],\"Retry\":0,\"TaskID\":\"mock_task_id\"}",
		OrderStatusCreating,
		"{\"orderOverTime\":120,\"createInstances\":[{\"instanceType\":\"N6\",\"cpuCount\":2,"+
			"\"zoneName\":\"zoneA\",\"memoryCapacityInGB\":4,\"rootDiskSizeInGb\":20,"+
			"\"rootDiskStorageType\":\"cloud_hp1\",\"createCdsList\":[{\"storageType\":\"cloud_hp1\","+
			"\"cdsSizeInGB\":20}],\"name\":\"\",\"imageId\":\"mock_image_id_01\",\"purchaseCount\":1,"+
			"\"subnetId\":\"mock_subnet_id_01\",\"securityGroupId\":\"mock_sg_id_01\","+
			"\"billing\":{\"paymentTiming\":\"Postpaid\"},\"relationTag\":0,\"rootOnLocal\":0,"+
			"\"tags\":null,\"deploySetId\":\"mock_app_id_01-0\",\"userData\":\"mock_app_id_01\","+
			"\"configs\":[{\"key\":\"entity_ids\",\"configValue\":\"mock_entity_id_01.0\"},"+
			"{\"key\":\"flavor_type\",\"configValue\":\"1\"},{\"key\":\"instance_type\",\"configValue\":\"3\"},"+
			"{\"key\":\"image_type\",\"configValue\":\"3\"},{\"key\":\"master_endpoint\",\"configValue\":\"\"},"+
			"{\"key\":\"env_type\",\"configValue\":\"public_cloud\"},{\"key\":\"deploy_set_id\","+
			"\"configValue\":\"mock_app_id_01-0\"}]}]}",
		"",
		""))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs(sqlmock.AnyArg(), "mock-order-id", "mockerr-bcc-order-id", sqlmock.AnyArg(), sqlmock.AnyArg(),
			OrderStatusError, sqlmock.AnyArg(), sqlmock.AnyArg(), "mockerr").
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	mock.ExpectBegin()
	mock.ExpectExec("^INSERT INTO .*").
		WithArgs("mock-order-id", sqlmock.AnyArg(), OrderStatusToRollback, "mock_task_id", "AZONE-bjyz",
			"mock_app_id_01", sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	_, err := br.ShowBccResourcesByOrder(context.Background(), &ShowBccResourcesParams{
		UserID:  "mock_user_id_01",
		OrderID: "mock-order-id",
	})
	if err == nil {
		t.Fatalf("show bcc resources by order expect error, but got nil")
	}
}

func Test_bccResourceOp_ShowBccResourcesByOrder(t *testing.T) {
	t.Run("show bcc resources by order normal", testShowBccResourcesByOrderNormal)
	t.Run("show bcc resources by order error", testShowBccResourcesByOrderError)
}

func testCreateCdsNormal(t *testing.T) {
	br, _, toDefer := getMockBccResource(t)
	defer toDefer()
	volumeID, err := br.CreateCds(context.Background(), &CreateCdsParam{
		UserID: "mock_user_id_01",
	})
	if err != nil {
		t.Fatalf("create cds error")
	}

	if volumeID != "v-xxxxxx" {
		t.Fatalf("create cds error")
	}
}

func testCreateCdsError(t *testing.T) {
	br, _, toDefer := getMockBccResource(t)
	defer toDefer()
	_, err := br.CreateCds(context.Background(), &CreateCdsParam{
		UserID: "mockerr_user_id_01",
		Name:   "mockerr_name",
	})

	if err == nil {
		t.Errorf("[%s] test create cds fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}

func testCreateCdsNumNotOne(t *testing.T) {
	br, _, toDefer := getMockBccResource(t)
	defer toDefer()
	_, err := br.CreateCds(context.Background(), &CreateCdsParam{
		UserID: "mockerr_user_id_01",
		Name:   "mockexcept_name",
	})

	if err == nil {
		t.Errorf("[%s] test create cds fail", t.Name())
	} else {
		fmt.Printf("%s\n", err.Error())
	}
}

func Test_bccResourceOp_CreateCds(t *testing.T) {
	t.Run("create cds normal", testCreateCdsNormal)
	t.Run("create cds error", testCreateCdsError)
	t.Run("create cds except", testCreateCdsNumNotOne)
}
