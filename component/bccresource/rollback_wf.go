package bccresource

import (
	"context"
	"encoding/json"
	"github.com/google/uuid"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"time"
)

const (
	WorkflowRollbackBccResource     = "rollback_bcc_resource"
	StepRollbackBccResourceRollback = "rollback_bcc_resource_rollback"
)

type RollbackBccResourceParams struct {
	OrderID string
	TaskID  string
	UserID  string
	AppID   string
}

var taskOperator iface.TaskOperator

func ProcessRollbackBccResource(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params := &RollbackBccResourceParams{}
	if err := json.Unmarshal([]byte(teu.Parameters), params); err != nil {
		logger.ComponentLogger.Warning(ctx, "unmarshal params failed, err %s", err.Error())
		return err
	}
	var orders []*OrderRecord
	if err := bccOrderModel.GetAllByCond(ctx, &orders, "order_id = ?", params.OrderID); err != nil {
		logger.ComponentLogger.Warning(ctx, "get bcc order failed, err %s", err.Error())
		return err
	}
	if len(orders) == 0 {
		logger.ComponentLogger.Warning(ctx, "get bcc order failed, order_id: %s", params.OrderID)
		return nil
	}
	if orders[0].Status != OrderStatusToRollback {
		logger.ComponentLogger.Warning(ctx, "order status is not to rollback, order_id: %s", params.OrderID)
	}
	var subOrders []*SubOrderRecord
	if err := bccOrderModel.GetAllByCond(ctx, &subOrders, "p_order_id = ?", orders[0].OrderID); err != nil {
		logger.ComponentLogger.Warning(ctx, "get bcc sub order failed, order_id: %s, err: %w", params.OrderID, err)
		return nil
	}
	if len(subOrders) == 0 {
		logger.ComponentLogger.Warning(ctx, "get bcc sub order failed, order_id: %s", params.OrderID)
		return nil
	}
	logger.ComponentLogger.Trace(ctx, "get bcc sub order success, order_id: %s, sub_order: %v", params.OrderID, subOrders)
	g := gtask.Group{}
	for _, subOrder := range subOrders {
		subOrder := subOrder
		if subOrder.BccOrderID == "" {
			continue
		}
		g.Go(func() error {
			return BccResourceOp().rollbackOneSubOrder(ctx, params, subOrder)
		})
	}
	_, err := g.Wait()
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "rollback bcc resource failed, err %s", err.Error())
		return err
	}
	orders[0].Status = OrderStatusRollbacked
	if err := bccOrderModel.FullSaveAssociationsSave(ctx, &subOrders); err != nil {
		logger.ComponentLogger.Warning(ctx, "save bcc sub order failed, task_id: %s, err: %w", orders[0].TaskID, err)
		return err
	}
	if err := bccOrderModel.FullSaveAssociationsSave(ctx, &orders); err != nil {
		logger.ComponentLogger.Warning(ctx, "save bcc order failed, task_id: %s, err: %w", orders[0].TaskID, err)
		return err
	}
	return err
}

func (br *bccResourceOp) rollbackOneSubOrder(ctx context.Context, params *RollbackBccResourceParams, subOrder *SubOrderRecord) error {
	auth, err := compo_utils.GetOpenapiAuthWithResourceAccount(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get openapi auth failed, err %s", err.Error())
		return err
	}
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(10 * time.Second):
			resources, err := br.getOneSubOrderResponse(ctx, subOrder, auth)
			if err == nil {
				if resources != nil {
					deleteReq := DeleteBccResourceParams{
						UserID: params.UserID,
					}
					for _, resource := range resources {
						deleteReq.InstanceIds = append(deleteReq.InstanceIds, resource.ID)
					}
					logger.ComponentLogger.Trace(ctx, "delete bcc resource, req: %s", base_utils.Format(deleteReq))
					if err := br.DeleteBccResource(ctx, &deleteReq); err != nil {
						logger.ComponentLogger.Warning(ctx, "delete bcc resource failed, err %s", err.Error())
						return err
					}
				}
				subOrder.Status = OrderStatusRollbacked
				return nil
			}
			if cerrs.Code(err) == CODE_BCC_ORDER_IN_OPERATION {
				continue
			}
			if cerrs.Code(err) == CODE_BCC_ORDER_ERROR {
				subOrder.Status = OrderStatusError
				return nil
			}
		}
	}
}

func (br *bccResourceOp) SendRollbackTask(ctx context.Context, params *RollbackBccResourceParams) error {
	if params.OrderID != "" {
		return taskOperator.CreateTask(ctx, &iface.Task{
			TaskID:     uuid.New().String(),
			WorkFlow:   WorkflowRollbackBccResource,
			Entity:     params.AppID,
			Status:     iface.TaskStatusWaiting,
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
			Schedule:   time.Now(),
			Deadline:   time.Now().Add(time.Hour * 24),
			Mutex:      "",
			Parameters: base_utils.Format(params),
		})
	}
	if params.TaskID != "" {
		var orders []*OrderRecord
		if err := bccOrderModel.GetAllByCond(ctx, &orders, "task_id = ?", params.TaskID); err != nil {
			logger.ComponentLogger.Warning(ctx, "get bcc order failed, err %s", err.Error())
			return err
		}
		for _, order := range orders {
			params.OrderID = order.OrderID
			if order.Status != OrderStatusError && order.Status != OrderStatusRollbacked {
				if err := taskOperator.CreateTask(ctx, &iface.Task{
					TaskID:     uuid.New().String(),
					WorkFlow:   WorkflowRollbackBccResource,
					Entity:     params.AppID,
					Status:     iface.TaskStatusWaiting,
					CreatedAt:  time.Now(),
					UpdatedAt:  time.Now(),
					Schedule:   time.Now(),
					Deadline:   time.Now().Add(time.Hour * 24),
					Mutex:      "",
					Parameters: base_utils.Format(params),
				}); err != nil {
					logger.ComponentLogger.Warning(ctx, "create task failed, err %s", err.Error())
					return err
				}
			}
		}
	}
	return nil
}
