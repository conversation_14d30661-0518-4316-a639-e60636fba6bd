package bccresource

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/model"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"time"
)

const (
	OrderStatusToCreate    = "to_create"
	OrderStatusCreating    = "creating"
	OrderStatusCreated     = "created"
	OrderStatusToRollback  = "to_rollback"
	OrderStatusRollbacking = "rollbacking"
	OrderStatusRollbacked  = "rollbacked"
	OrderStatusError       = "error"
)

type OrderRecord struct {
	Id         int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	OrderID    string    `gorm:"column:order_id;NOT NULL" json:"order_id"`
	CreatedAt  time.Time `gorm:"column:created_at;NOT NULL" json:"create_time"`
	Status     string    `gorm:"column:status;NOT NULL" json:"status"`
	TaskID     string    `gorm:"column:task_id;NOT NULL" json:"task_id"`
	Azone      string    `gorm:"column:azone;NOT NULL" json:"azone"`
	AppID      string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	Parameters string    `gorm:"column:parameters;NOT NULL" json:"parameters"`
	ErrMsg     string    `gorm:"column:err_msg;NOT NULL" json:"err_msg"`
}

func (OrderRecord) TableName() string {
	return "order_record"
}

type SubOrderRecord struct {
	Id          int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	OrderID     string    `gorm:"column:order_id;NOT NULL" json:"order_id"`
	POrderID    string    `gorm:"column:p_order_id;NOT NULL" json:"p_order_id"`
	BccOrderID  string    `gorm:"column:bcc_order_id;NOT NULL" json:"bcc_order_id"`
	CreatedAt   time.Time `gorm:"column:created_at;NOT NULL" json:"create_time"`
	Parameters  string    `gorm:"column:parameters;NOT NULL" json:"parameters"`
	Status      string    `gorm:"column:status;NOT NULL" json:"status"`
	BccRequest  string    `gorm:"column:bcc_request;NOT NULL" json:"bcc_request"`
	BccResponse string    `gorm:"column:bcc_response;NOT NULL" json:"bcc_response"`
	ErrMsg      string    `gorm:"column:err_msg;NOT NULL" json:"err_msg"`
}

func (SubOrderRecord) TableName() string {
	return "sub_order_record"
}

var bccOrderModel *model.Resource

func BccOrderModel() *model.Resource {
	return bccOrderModel
}

func MustInitBccOrderModel(ctx context.Context, conf *x1model.Conf) {
	resourcePtr, err := model.InitModel(ctx, model.ResourceCfg{
		ServicerName: func() string {
			if conf.ServicerName != "" {
				return conf.ServicerName
			}
			return "bcc-order-model"
		}(),
		DbLogger:    conf.DbLogger,
		GormLogger:  conf.GormLogger,
		PreloadConf: nil,
		AutoPreload: false,
	})
	if err != nil {
		panic("init X1model fail")
	}
	bccOrderModel = resourcePtr
}
