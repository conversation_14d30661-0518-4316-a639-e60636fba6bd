package bccresource

import (
	"context"
	"encoding/json"
	"testing"
)

func Test_genNewSubOrderID(t *testing.T) {
	tests := []struct {
		name     string
		orderID  string
		expected string
	}{
		{
			name:     "Test with orderID without dash",
			orderID:  "12345",
			expected: "12345-1",
		},
		{
			name:     "Test with orderID with dash",
			orderID:  "1c2513cd-d09b-405a-abd9-583c33bd8dce",
			expected: "1c2513cd-d09b-405a-abd9-583c33bd8dce-1",
		},
		{
			name:     "Test with orderID with dash",
			orderID:  "1c2513cd-d09b-405a-abd9-583c33bd8dce-1",
			expected: "1c2513cd-d09b-405a-abd9-583c33bd8dce-2",
		},
		{
			name:     "Test with orderID with non-numeric suffix",
			orderID:  "1c2513cd-d09b-405a-abd9-583c33bd8dce-abc",
			expected: "1c2513cd-d09b-405a-abd9-583c33bd8dce-abc-1",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := genNewSubOrderID(tt.orderID)
			if tt.orderID != "12345-abc" && got != tt.expected {
				t.Errorf("genNewSubOrderID() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestUpdateSubOrderForRetryOrderStatusToCreateValidParameters(t *testing.T) {
	ctx := context.Background()
	subOrder := &SubOrderRecord{
		Status: OrderStatusToCreate,
		Parameters: `{
			"AppID": "oldApp",
			"Retry": 1
		}`,
	}
	params := &CreateBccResourceParams{
		AppID: "newApp",
		Retry: 2,
	}

	err := updateSubOrderForRetry(ctx, subOrder, params)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	// You might need to parse the Parameters string back into a struct to validate
	newParams := &CreateBccResourceParams{}
	_ = json.Unmarshal([]byte(subOrder.Parameters), newParams)

	if newParams.Retry != 2 {
		t.Errorf("Expected Retry in Parameters to be 2, got: %v", newParams.Retry)
	}
	if newParams.AppID != "oldApp" {
		t.Errorf("Expected AppID in Parameters to remain 'oldApp', got: %v", newParams.AppID)
	}

	subOrder = &SubOrderRecord{
		Status: OrderStatusError,
		Parameters: `{
			"AppID": "oldApp",
			"Retry": 1
		}`,
	}
	err = updateSubOrderForRetry(ctx, subOrder, params)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	newParams = &CreateBccResourceParams{}
	_ = json.Unmarshal([]byte(subOrder.Parameters), newParams)
	if newParams.Retry != 2 {
		t.Errorf("Expected Retry in Parameters to be 2, got: %v", newParams.Retry)
	}
	if newParams.AppID != "oldApp" {
		t.Errorf("Expected AppID in Parameters to remain 'oldApp', got: %v", newParams.AppID)
	}
	if subOrder.Status != OrderStatusToCreate {
		t.Errorf("Expected Status to be %v, got: %v", OrderStatusToCreate, subOrder.Status)
	}

	subOrder = &SubOrderRecord{
		Status: OrderStatusError,
		Parameters: `{
			"AppID": "oldApp"
			"Retry": 1
		}`,
	}
	err = updateSubOrderForRetry(ctx, subOrder, params)
	if err == nil {
		t.Errorf("Expected error, got: %v", err)
	}
}
