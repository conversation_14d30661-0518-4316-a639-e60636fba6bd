/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
在配置中获取具体规格的相关方法
*/

package specification

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
)

type Config struct {
	SpecificationList []ConfigItem
}

type ConfigItem struct {
	Specification Specification
	EngineList    []string
	StoreTypeList []string
	AppTypeList   []string
}

type Specification struct {
	AvailableVolume      int
	Name                 string
	CPUCount             int
	MemoryCapacityInGB   int
	RootDiskCapacityInGB int
	DataDiskCapacityInGB int
}

type GetSpecificationParams struct {
	UserID    string
	Name      string
	Engine    string
	StoreType string
	AppType   string
}

type CorrectionFunc func(spec *Specification, features *GetSpecificationParams) *Specification

var config map[string]*Specification

var correctionFuncMap map[string]CorrectionFunc

func getKey(params *GetSpecificationParams) string {
	return params.AppType + "__" + params.Engine + "__" + params.StoreType + "__" + params.Name
}

func MustLoadConf(ctx context.Context) {
	c := &Config{}
	config = make(map[string]*Specification)
	if err := compo_utils.LoadConf("specification", c); err != nil {
		panic(err.Error())
	}
	fmt.Printf("c:%+v\n", *c)
	for idx := range c.SpecificationList {
		item := &c.SpecificationList[idx]
		for _, engine := range item.EngineList {
			for _, st := range item.StoreTypeList {
				for _, at := range item.AppTypeList {
					config[getKey(&GetSpecificationParams{
						Name:      item.Specification.Name,
						Engine:    engine,
						StoreType: st,
						AppType:   at,
					})] = &item.Specification
				}
			}
		}
	}
	correctionFuncMap = make(map[string]CorrectionFunc)
}

func RegisterCorrectionFunc(name string, f CorrectionFunc) {
	if _, found := correctionFuncMap[name]; found {
		panic("occupied correction func")
	}
	correctionFuncMap[name] = f
}

// GetSpecification 获取规格信息, 返回规格*Specification
// 1. 通过params.SpecificationName从配置中获取规格作为默认规格，如果没有这个规格则返回失败
// 2. 执行correctionFuncMap中所有的修正函数，修正规格；
func GetSpecification(ctx context.Context, params *GetSpecificationParams) (*Specification, error) {
	spec, found := config[getKey(params)]
	if !found {
		return nil, errors.Errorf("spec %s not found", params.Name)
	}
	for _, correctFunc := range correctionFuncMap {
		spec = correctFunc(spec, params)
	}
	return spec, nil
}
