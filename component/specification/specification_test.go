/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
在配置中获取具体规格的相关方法
*/

package specification

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

func init() {
	unittest.UnitTestInit(2)
	MustLoadConf(context.Background())
}

func TestSpec(t *testing.T) {
	fmt.Printf("%+v", config)
	spec, err := GetSpecification(context.Background(), &GetSpecificationParams{
		Name:      "cache.n1.small",
		Engine:    x1model.EngineRedis,
		StoreType: x1model.StoreTypeDRAM,
		AppType:   x1model.AppTypeStandalone,
	})
	if err != nil {
		t.Fatalf("expect no error, actual err %s", err.Error())
	}
	expectSpec := Specification{
		AvailableVolume:      2,
		Name:                 "cache.n1.small",
		CPUCount:             2,
		MemoryCapacityInGB:   4,
		RootDiskCapacityInGB: 20,
		DataDiskCapacityInGB: 20,
	}
	if !reflect.DeepEqual(*spec, expectSpec) {
		t.Fatalf("expect %+v, actual %+v", expectSpec, *spec)
	}
	spec, err = GetSpecification(context.Background(), &GetSpecificationParams{
		Name:      "cache.n1.small",
		Engine:    x1model.EngineBDRPRedis,
		StoreType: x1model.StoreTypeDRAM,
		AppType:   x1model.AppTypeCluster,
	})
	if err != nil {
		t.Fatalf("expect no error, actual err %s", err.Error())
	}
	expectSpec = Specification{
		AvailableVolume:      2,
		Name:                 "cache.n1.small",
		CPUCount:             2,
		MemoryCapacityInGB:   4,
		RootDiskCapacityInGB: 20,
		DataDiskCapacityInGB: 20,
	}
	if !reflect.DeepEqual(*spec, expectSpec) {
		t.Fatalf("expect %+v, actual %+v", expectSpec, *spec)
	}
	_, err = GetSpecification(context.Background(), &GetSpecificationParams{
		Name:      "cache.n1.error",
		Engine:    x1model.EngineBDRPRedis,
		StoreType: x1model.StoreTypeDRAM,
		AppType:   x1model.AppTypeCluster,
	})
	if err == nil {
		t.Fatalf("expect error, actual no err")
	}
	_, err = GetSpecification(context.Background(), &GetSpecificationParams{
		Name:      "cache.n1.small",
		Engine:    x1model.EnginePegaDB,
		StoreType: x1model.StoreTypeDRAM,
		AppType:   x1model.AppTypeCluster,
	})
	if err == nil {
		t.Fatalf("expect error, actual no err")
	}
	RegisterCorrectionFunc("test", func(spec *Specification, features *GetSpecificationParams) *Specification {
		if spec.CPUCount > 1 {
			spec.CPUCount = 1
		}
		return spec
	})
	spec, err = GetSpecification(context.Background(), &GetSpecificationParams{
		Name:      "cache.n1.small",
		Engine:    x1model.EngineBDRPRedis,
		StoreType: x1model.StoreTypeDRAM,
		AppType:   x1model.AppTypeCluster,
	})
	if err != nil {
		t.Fatalf("expect no error, actual err %s", err.Error())
	}
	expectSpec = Specification{
		AvailableVolume:      2,
		Name:                 "cache.n1.small",
		CPUCount:             1,
		MemoryCapacityInGB:   4,
		RootDiskCapacityInGB: 20,
		DataDiskCapacityInGB: 20,
	}
	if !reflect.DeepEqual(*spec, expectSpec) {
		t.Fatalf("expect %+v, actual %+v", expectSpec, *spec)
	}
}
