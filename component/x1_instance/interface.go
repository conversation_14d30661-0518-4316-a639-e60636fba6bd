/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: z<PERSON><PERSON><PERSON><PERSON>
 * Date: 2022-5-20
 * File: x1_instance.go
 */

/*
 * DESCRIPTION
 *   x1 instance
 */

// Package x1_instance

package x1_instance

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/component/specification"
)

type CreateInstanceParams struct {
	AppID                string
	UserID               string
	Product              string
	ImageID              string
	VpcID                string
	CustomerDeploySetIDs []string
	LogicalZone          string
	Azone                string
	StoreType            string
	ResouceType          string
	Priority             string
	Items                []CreateInstanceParamsItem
	X1TaskID             string   `json:"x1TaskId,omitempty"`
	CustomLabels         []string `json:"customLabels,omitempty"`
	NodeType             string   `json:"nodeType,omitempty"`
	LogicalRegion        string   `json:"logicalRegion,omitempty"`
}

type CreateInstanceParamsItem struct {
	Spec            specification.Specification
	Engine          string
	Count           int64
	DeploySetID     string
	EntityIDs       []string
	SecurityGroupID string
}

type ShowInstanceParams struct {
	UserID  string
	OrderID string
}

type DeleteInstanceParams struct {
	InstanceIds []string
	UserID      string
	X1TaskID    string
}

type InstancePort struct {
	DbPort         int
	StatPort       int
	XagentPort     int
	XagentSyncPort int
	McpackPort     int
}

type ResInstance struct {
	ID           string
	Name         string
	RootPassword string
	FixIP        string
	FixIPv6      string
	FloatingIP   string
	Flavor       string
	EntityID     string
	Port         InstancePort
	HostUUID     string
	HostName     string
	MetaData     map[string]string
}

type ResizeInstanceParam struct {
	InstanceID           string
	UserID               string
	TargetCPUCount       int64
	TargetMemorySizeInMB int64
	TargetDiskSizeInGB   int64
	Engine               string
	X1TaskID             string
}

type X1Instance interface {
	CreateInstance(ctx context.Context, params *CreateInstanceParams) (string, error)
	ShowCreateInstanceByOrder(ctx context.Context, params *ShowInstanceParams) ([]ResInstance, error)
	DeleteInstances(ctx context.Context, params *DeleteInstanceParams) error
	ResizeInstance(ctx context.Context, params *ResizeInstanceParam) (string, error)
	ShowResizeInstanceByOrder(ctx context.Context, params *ShowInstanceParams) error
}
