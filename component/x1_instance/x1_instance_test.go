/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * author: wang<PERSON><PERSON>
 * Date: 2023-12-06
 * File: x1_instance_test.go
 */

/*
 * DESCRIPTION
 *   x1 instance

	sdkRequest := &x1ResourceSDK.CreateInstanceReq{
		AppID:                TestAppID,
		UserID:               TestIamUserID,
		Product:              TestProduct,
		ImageID:              TestImageID,
		VpcID:                TestVpcID,
		CustomerDeploySetIDs: []string{},
		LogicalZone:          TestLogicalZone,
		Azone:                TestAzone,
		StoreType:            TestStoreType,
		ResouceType:          TestResouceType,
		Priority:             TestPriority,
	}
	sdkItem := x1ResourceSDK.InstanceParam{
		Spec: x1ResourceSDK.InstSpec{
			AvailableVolume:      1,
			Name:                 "",
			CPUCount:             1000,
			MemoryCapacityInMB:   256,
			RootDiskCapacityInGB: 20,
			DataDiskCapacityInGB: 20,
		},
		Subnet:          "",
		Engine:          "redis",
		Count:           1,
		DeploySetID:     TestDeploySetID,
		EntityIDs:       nil,
		SecurityGroupID: TestSecurityGroupID,
	}
	sdkRequest.Items = append(sdkRequest.Items, sdkItem)
*/

package x1_instance

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/scs/x1-base/component/specification"
	x1ResourceSDK "icode.baidu.com/baidu/scs/x1-base/sdk/x1_resource"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest/sdkmock"
)

const (
	TestAppID           = "scs-test-app"
	TestIamUserID       = "ea2c4a2286ca4540afcb7f7d4ba2d199"
	TestProduct         = "scs"
	TestImageID         = "test_image_id"
	TestVpcID           = "58d53a2e-27fe-4ead-9041-c72ec67ec1c1"
	TestOrderID         = "8f1b1f09-4625-4b22-be4d-5d27ca047bf2"
	TestLogicalZone     = "zoneB"
	TestAzone           = "AZONE-bjyz"
	TestStoreType       = "DRAM"
	TestResouceType     = "container"
	TestPriority        = "low"
	TestNodeType        = "cache.n1.nano"
	TestDeploySetID     = "test_deployset_id"
	TestSecurityGroupID = "test_secgroup_id"
)

func TestCreateInstance(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockX1InstanceSDK := sdkmock.NewMockX1ResourceService(ctrl)
	mockX1InstanceSDK.EXPECT().CreateInstance(ctx, gomock.Any()).AnyTimes().DoAndReturn(
		func(ctx context.Context, req *x1ResourceSDK.CreateInstanceReq) (rsp *x1ResourceSDK.CreateInstanceResponse, err error) {
			if req.Items[0].Spec.MemoryCapacityInMB != 256 {
				return nil, errors.New("Create tiny reqeust failed")
			}

			rsp = &x1ResourceSDK.CreateInstanceResponse{
				Message:   "",
				Code:      0,
				Requestid: "",
				OrderId:   TestOrderID,
				Status:    "",
				ErrMsg:    "",
			}
			return rsp, nil
		})

	testComponent := &component{}
	testComponent.x1ResourceSdk = mockX1InstanceSDK
	x1Request := CreateInstanceParams{
		AppID:        TestAppID,
		UserID:       TestIamUserID,
		Product:      TestProduct,
		ImageID:      TestImageID,
		VpcID:        TestVpcID,
		LogicalZone:  TestLogicalZone,
		Azone:        TestAzone,
		StoreType:    TestStoreType,
		ResouceType:  TestResouceType,
		Priority:     TestPriority,
		X1TaskID:     "",
		CustomLabels: nil,
		NodeType:     TestNodeType,
	}

	item := &CreateInstanceParamsItem{
		Spec: specification.Specification{
			AvailableVolume:      1,
			Name:                 "",
			CPUCount:             1,
			MemoryCapacityInGB:   1,
			RootDiskCapacityInGB: 20,
			DataDiskCapacityInGB: 20,
		},
		Engine:          "redis",
		Count:           1,
		DeploySetID:     TestDeploySetID,
		SecurityGroupID: TestSecurityGroupID,
		EntityIDs:       nil,
	}
	x1Request.Items = append(x1Request.Items, *item)
	orderID, err := testComponent.CreateInstance(ctx, &x1Request)
	if orderID != TestOrderID {
		t.Errorf("Unexpected error: %v", err)
	}

}
