/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * author: zengshangyou
 * Date: 2022-5-20
 * File: x1_instance.go
 */

/*
 * DESCRIPTION
 *   x1 instance
 */

// Package x1_instance

package x1_instance

import (
	"context"
	"sync"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/model/x1resource"
	"icode.baidu.com/baidu/scs/x1-base/sdk/x1_resource"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

var (
	ErrInstanceOrderInOperation = errors.New("x1 order in operation")
	ErrInstanceOrderFailed      = errors.New("x1 order failed")
)

type component struct {
	x1ResourceSdk x1_resource.X1ResourceService
	initOnce      sync.Once
}

var defaultComponent = &component{}

// Instance 获取默认的组件，必须在gdp初始化env和ral后使用
func Instance() *component {
	defaultComponent.initOnce.Do(func() {
		defaultComponent.x1ResourceSdk = x1_resource.NewDefaultX1ResourceSdk()
	})

	return defaultComponent
}

// CreateInstance 创建x1资源；返回OrderID，可以根据OrderID查询资源创建情况
func (c *component) CreateInstance(ctx context.Context, params *CreateInstanceParams) (string, error) {
	logger.ComponentLogger.Trace(ctx, "x1 instance request %s", base_utils.Format(params))
	request, err := getCreateInstanceRequest(ctx, params)
	if err != nil {
		return "", err
	}
	logger.ComponentLogger.Trace(ctx, "x1 instance request %s", base_utils.Format(request))
	response, err := c.x1ResourceSdk.CreateInstance(ctx, request)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "send x1 instance request %s failed, resp: %s", base_utils.Format(request), base_utils.Format(response))
		return "", errors.Wrap(err, "send x1 instance request failed")
	}
	logger.ComponentLogger.Trace(ctx, "send x1 instance request succ, order id %s", response.OrderId)
	return response.OrderId, nil
}

func (c *component) ShowCreateInstanceByOrder(ctx context.Context, params *ShowInstanceParams) ([]ResInstance, error) {
	response, err := c.x1ResourceSdk.ShowCreateOrder(ctx, &x1_resource.ShowCreateInstanceOrderReq{
		OrderID: params.OrderID,
	})
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "show create instance order %s failed, resp: %s", params.OrderID, base_utils.Format(response))
		return nil, err
	}
	logger.ComponentLogger.Trace(ctx, "show create instance order %s succ, resp: %s", params.OrderID, base_utils.Format(response))
	switch response.Status {
	case x1resource.OrderStatusWaiting:
		return nil, ErrInstanceOrderInOperation
	case x1resource.OrderStatusFail:
		return nil, errors.Errorf("x1 instance error order %s, resp: %s", params.OrderID, base_utils.Format(response))
	case x1resource.OrderStatusSuccess:
		// pass
	default:
		logger.ComponentLogger.Warning(ctx, "error status %s", response.Status)
		return nil, errors.Errorf("error create instance order status %s", response.Status)
	}
	ret, err := getInstanceResources(ctx, params, response)
	if err != nil {
		logger.ComponentLogger.Trace(ctx, "show create instance order resp convert failed")
		return nil, err
	}
	logger.ComponentLogger.Trace(ctx, "show create instance order resp after convert is %s", base_utils.Format(response))
	return ret, nil
}

// DeleteInstances 删除instance资源
func (c *component) DeleteInstances(ctx context.Context, params *DeleteInstanceParams) error {
	if len(params.InstanceIds) == 0 {
		return nil
	}

	req := &x1_resource.DeleteInstancesReq{
		InstanceIds: params.InstanceIds,
		UserID:      params.UserID,
		X1TaskID:    params.X1TaskID,
	}
	_, err := c.x1ResourceSdk.DeleteInstance(ctx, req)
	return err
}

func (c *component) ResizeInstance(ctx context.Context, params *ResizeInstanceParam) (string, error) {
	logger.ComponentLogger.Trace(ctx, "x1 resize instance request %s", base_utils.Format(params))
	request := &x1_resource.ResizeInstanceReq{
		InstanceId:                 params.InstanceID,
		UserID:                     params.UserID,
		TargetCPUCount:             int64(params.TargetCPUCount * 1000),
		TargetMemoryCapacityInMB:   params.TargetMemorySizeInMB,
		TargetDataDiskCapacityInGB: params.TargetDiskSizeInGB,
		Engine:                     params.Engine,
		X1TaskID:                   params.X1TaskID,
	}

	response, err := c.x1ResourceSdk.ResizeInstance(ctx, request)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "send x1 resize instance request %s failed, resp: %s", base_utils.Format(request), base_utils.Format(response))
		return "", errors.Wrap(err, "send x1 resize instance request failed")
	}
	logger.ComponentLogger.Trace(ctx, "send x1 resize instance request succ, order id %s", response.OrderId)
	return response.OrderId, nil
}

func (c *component) ShowResizeInstanceByOrder(ctx context.Context, params *ShowInstanceParams) error {
	response, err := c.x1ResourceSdk.ShowResizeOrder(ctx, &x1_resource.ShowResizeInstanceReq{
		OrderID: params.OrderID,
	})

	if err != nil {
		logger.ComponentLogger.Warning(ctx, "show resize instance order %s failed, resp: %s", params.OrderID, base_utils.Format(response))
		return err
	}
	logger.ComponentLogger.Trace(ctx, "show resize instance order %s succ, resp: %s", params.OrderID, base_utils.Format(response))

	switch response.Status {
	case x1resource.OrderStatusWaiting:
		return ErrInstanceOrderInOperation
	case x1resource.OrderStatusFail:
		return ErrInstanceOrderFailed
	case x1resource.OrderStatusSuccess:
		// pass
	default:
		logger.ComponentLogger.Warning(ctx, "unknown status %s", response.Status)
		return errors.Errorf("unknown resize instance order status %s", response.Status)
	}

	logger.ComponentLogger.Trace(ctx, "show resize instance order resp", base_utils.Format(response))
	return nil
}

func getCreateInstanceRequest(ctx context.Context, params *CreateInstanceParams) (*x1_resource.CreateInstanceReq, error) {
	request := &x1_resource.CreateInstanceReq{
		AppID:                params.AppID,
		UserID:               params.UserID,
		Product:              params.Product,
		ImageID:              params.ImageID,
		VpcID:                params.VpcID,
		CustomerDeploySetIDs: params.CustomerDeploySetIDs,
		LogicalZone:          params.LogicalZone,
		Azone:                params.Azone,
		StoreType:            params.StoreType,
		ResouceType:          "container",
		Priority:             params.Priority,
		X1TaskID:             params.X1TaskID,
		CustomLabels:         params.CustomLabels,
		LogicalRegion:        params.LogicalRegion,
	}

	for _, item := range params.Items {
		ci := &x1_resource.InstanceParam{
			DeploySetID:     item.DeploySetID,
			Count:           item.Count,
			Engine:          item.Engine,
			EntityIDs:       item.EntityIDs,
			SecurityGroupID: item.SecurityGroupID,
		}

		param_spec := &item.Spec
		spec := &x1_resource.InstSpec{
			AvailableVolume:      int64(param_spec.AvailableVolume),
			Name:                 param_spec.Name,
			CPUCount:             int64(param_spec.CPUCount) * 1000,
			MemoryCapacityInMB:   int64(param_spec.MemoryCapacityInGB) * 1024,
			RootDiskCapacityInGB: int64(param_spec.RootDiskCapacityInGB),
			DataDiskCapacityInGB: int64(param_spec.DataDiskCapacityInGB),
		}
		if params.NodeType == "cache.n1.nano" {
			spec.MemoryCapacityInMB = 256
		}
		ci.Spec = *spec

		request.Items = append(request.Items, *ci)
	}
	return request, nil
}

func getInstanceResources(ctx context.Context, params *ShowInstanceParams, response *x1_resource.ShowCreateOrderResponse) ([]ResInstance, error) {
	x1Resources := make([]ResInstance, len(response.Instances))

	for idx, server := range response.Instances {
		x1Resources[idx].ID = server.Id
		x1Resources[idx].Name = server.Name
		x1Resources[idx].RootPassword = server.RootPassword
		x1Resources[idx].FixIP = server.FixedIp
		x1Resources[idx].FixIPv6 = server.Ipv6FixedIp
		x1Resources[idx].FloatingIP = server.FloatingIp
		x1Resources[idx].Flavor = server.Flavor
		x1Resources[idx].EntityID = server.EntityID
		x1Resources[idx].Port = InstancePort(server.Port)
		x1Resources[idx].HostUUID = server.HostUUID
		x1Resources[idx].HostName = server.HostName
	}

	return x1Resources, nil
}
