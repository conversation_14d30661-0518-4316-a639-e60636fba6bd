/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/06/07 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file config.go
 * <AUTHOR>
 * @date 2022/06/07 20:53:06
 * @brief conf
 *
 **/

package blbv2

type config struct {
	BlbConf             *blbConf
	BlbTCPListenerConf  *blbTCPListenerConf
	BlbHTTPListenerConf *blbHTTPListenerConf
	BlbEnv              *BlbEnv
}

type blbConf struct {
	Type                        string
	HealthCheck                 string
	HealthCheckTimeoutInSecond  int
	HealthCheckUpRetry          int
	HealthCheckDownRetry        int
	HealthCheckIntervalInSecond int
	Product                     string `toml:"Product,omitempty"`
}

type blbTCPListenerConf struct {
	TCPSessionTimeout int
}

type blbHTTPListenerConf struct {
	KeepSession        bool
	KeepSessionTimeout int
	XForwardedFor      bool
	XForwardedProto    bool
	ServerTimeout      int
}

type BlbEnv struct {
	ResourcePrivateUserId string
	ResourceCloudUserId   string

	ResourceCloudVpcId   string
	ResourcePrivateVpcId string

	ResourceCloudSubnet   []*CloudSubnet
	ResourcePrivateSubnet []*PrivateSubnet

	CloudUserOfUsePrivateResource []*User
}

type CloudSubnet struct {
	Zone     string
	SubnetId string
}

type PrivateSubnet struct {
	Zone     string
	SubnetId string
}

type User struct {
	UserId string
}
