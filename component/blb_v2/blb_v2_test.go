package blbv2

import (
	"context"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
)

func TestQueryBlbDetail(t *testing.T) {
	ctx := context.Background()
	//defer sdk_utils.TestEnvDefer(ctx)()

	blbOp := Instance()

	// case 1
	resp, err := blbOp.QueryBlbDetail(ctx, &CommonBLBParams{})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")

	// case 2
	resp, err = blbOp.QueryBlbDetail(ctx, nil)
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")

	// case 3
	resp, err = blbOp.QueryBlbDetail(ctx, &CommonBLBParams{
		ElbID: "lb-xxx",
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")

	// case 4
	resp, err = blbOp.QueryBlbDetail(ctx, &CommonBLBParams{
		UserID: "a11602e1c4c24e59865b9bb9209ff16d",
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")

	// case 5
	resp, err = blbOp.QueryBlbDetail(ctx, &CommonBLBParams{
		UserID: "a11602e1c4c24e59865b9bb9209ff16d",
		ElbID:  "lb-xxx",
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")

}

func TestGetAppBLBDetail(t *testing.T) {
	ctx := context.Background()
	//defer sdk_utils.TestEnvDefer(ctx)()

	blbOp := Instance()

	// case 1
	resp, err := blbOp.GetAppBLBDetail(ctx, &CommonBLBParams{})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")

	// case 2
	resp, err = blbOp.GetAppBLBDetail(ctx, &CommonBLBParams{
		ElbID: "lb-xxx",
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")

	// case 3
	resp, err = blbOp.GetAppBLBDetail(ctx, &CommonBLBParams{
		UserID: "a11602e1c4c24e59865b9bb9209ff16d",
	})
	if !cerrs.ErrNotFound.Is(err) {
		fmt.Println(err)
	}
	fmt.Println(resp)
	fmt.Println("success")

}
