/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/06/07 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file interface.go
 * <AUTHOR>
 * @date 2022/06/07 20:50:13
 * @brief blb_v1 interface
 *
 **/

package blbv2

// CreateBLBParams definition
type CreateBLBParams struct {
	UserID                 string
	VpcID                  string
	SubnetID               string
	Type                   string
	Name                   string
	Layer4MasterAz         string
	AllocateVip            bool
	Layer4ClusterID        string
	Layer4ClusterExclusive bool
}

// CommonBLBParams definition
type CommonBLBParams struct {
	UserID string
	ElbID  string
}

// UpdateBLBParams definition
type UpdateBLBParams struct {
	UserID string
	ElbID  string
	Name   string
	Desc   string
}

// ListenerInfo definition
type ListenerInfo struct {
	Port string
	Type string
}

// BLB definition
type BLB struct {
	BLBID           string
	Address         string
	UnderlayVip     string
	Name            string
	Status          string
	Desc            string
	PublicIP        string
	Cidr            string
	VpcName         string
	SubnetName      string
	SubnetCider     string
	CreateTime      string
	ReleaseTime     string
	Listener        []*ListenerInfo
	AllowDelete     bool
	Layer4ClusterId string
	Layer4MasterAz  string
	Layer4SlaveAz   string
}

// GetEncryptParams definition
type GetEncryptParams struct {
	UserID    string
	AccountID string
	Source    string
}

// CreateAppServerGroupParams definition
type CreateAppServerGroupParams struct {
	UserID         string
	BlbID          string
	Name           string
	BackendServers []*BackendServerList
}

// PortList definition
type PortList struct {
	ID                          string
	Port                        int
	Type                        string
	HealthCheck                 string
	HealthCheckNormalStatus     string
	HealthCheckPort             int
	HealthCheckTimeoutInSecond  int
	HealthCheckDownRetry        int
	HealthCheckUpRetry          int
	HealthCheckIntervalInSecond int
	HealthCheckURLPath          string
	HealtchCheckHost            string
	Status                      string
}

// BackendServerList definition
type BackendServerList struct {
	InstanceID string
	PrivateIP  string
	Weight     int
	PortList   []*PortList
}

// CommonSeverGroupParams definition
type CommonSeverGroupParams struct {
	BlbID         string
	ServerGroupID string
	UserID        string
}

// CreateServerGroupPortParams definition
type CreateServerGroupPortParams struct {
	BlbID         string
	ServerGroupID string
	UserID        string
	Port          int
	Type          string
}

// DeleteServerGroupPortParams definition
type DeleteServerGroupPortParams struct {
	BlbID         string
	ServerGroupID string
	UserID        string
	PortIDList    []string
}

// CommonAppBlbRsParams definition
type CommonAppBlbRsParams struct {
	UserID         string
	BlbID          string
	ServerGroupID  string
	BackendServers []*BackendServerList
}

// DeleteAppBlbRsParams definition
type DeleteAppBlbRsParams struct {
	UserID         string
	BlbID          string
	ServerGroupID  string
	BackendServers []string
}

// BackendServers definition
type BackendServers struct {
	InstanceID string
	PrivateIP  string
	Weight     int
	PortList   []*PortInfo
}

// PortInfo definition
type PortInfo struct {
	ListenerPort        string
	BackendPort         string
	PortType            string
	HealthCheckPortType string
	PortID              string
	PolicyID            string
	Status              string
	Staus               string
}

// CommonServerGroupParams definition
type CommonServerGroupParams struct {
	UserID string
	BlbID  string
}

// ListAppPolicysParams definition
type ListAppPolicysParams struct {
	UserID string
	BlbID  string
	Port   int
	Type   string
}

// Policys definition
type Policys struct {
	ID                 string
	AppServerGroupID   string
	AppServerGroupName string
	FrontendPort       int
	Type               string
	BackendPort        int
	PortType           string
	Priority           int
	Desc               string
	GroupType          string
	RuleList           []*RuleList
}

// RuleList definition
type RuleList struct {
	Key   string
	Value string
}

// DeleteAppPolicysParams definition
type DeleteAppPolicysParams struct {
	UserID    string
	BlbID     string
	Port      int
	Type      string
	PolicyIDs []string
}

// AppPolicyVos definition
type AppPolicyVos struct {
	AppServerGroupID string
	BackendPort      int
	IPGroupID        string
	Priority         int
	Desc             string
	RuleList         []*RuleList
}

// CreateAppPolicysParams definition
type CreateAppPolicysParams struct {
	BlbID        string
	UserID       string
	ListenerPort int
	Type         string
	GroupType    string
	AppPolicyVos []*AppPolicyVos
}

// HTTPListener definition
type HTTPListener struct {
	ListenerPort int
	BackendPort  int
	Scheduler    string
}

// CommonListenerParams definition
type CommonListenerParams struct {
	BlbID        string
	ListenerPort int
	Scheduler    string
	UserID       string
}

// TCPListener definition
type TCPListener struct {
	ListenerPort      int
	Scheduler         string
	TCPSessionTimeout int
}

// PortTypeList definition
type PortTypeList struct {
	Port int
	Type string
}

// DeleteAppListenerParams definition
type DeleteAppListenerParams struct {
	BlbID    string
	PortList []int
	UserID   string
}

// BlbIDListParams definition
type BlbIDListParams struct {
	BlbID  []string
	UserID string
}

// BlbIDMappingList definition
type BlbIDMappingList struct {
	ShortID string
	LongID  string
}

// CommonIPGroupParams definition
type CommonIPGroupParams struct {
	BlbID     string
	IPGroupID string
	Type      string
	UserID    string
}

// DeleteIPGroupPolicyParams definition
type DeleteIPGroupPolicyParams struct {
	BlbID        string
	IPGroupID    string
	PolicyIDList []string
	UserID       string
}

// Member definition
type Member struct {
	IP     string
	Weight int
	Port   int
	ID     string
}

// CreateIPGroupMemberParams definition
type CreateIPGroupMemberParams struct {
	BlbID      string
	IPGroupID  string
	MemberList []*Member
	UserID     string
}

// DeleteIPGroupMemberParams definition
type DeleteIPGroupMemberParams struct {
	BlbID      string
	IPGroupID  string
	MemberList []string
	UserID     string
}

// BackendPolicyList definiton
type BackendPolicyList struct {
	ID                          string
	Type                        string
	HealthCheck                 string
	HealthCheckPort             int
	HealthCheckURLPath          string
	HealthCheckTimeoutInSecond  int
	HealthCheckIntervalInSecond int
	HealthCheckDownRetry        int
	HealthCheckUpRetry          int
	HealthCheckNormalStatus     string
	HealthCheckHost             string
	UDPHealthCheckString        string
}

// AppIPGroupList definition
type AppIPGroupList struct {
	ID                string
	Name              string
	Desc              string
	BackendPolicyList []*BackendPolicyList
}

// CreateBatchBLBParams definition
type CreateBatchBLBParams struct {
	UserID    string
	Product   string
	BLBParams []*CreateBLBParams
}

// Rs definition
type Rs struct {
	UUID   string
	IP     string
	Weight int
	Port   int
}

// BindRsParams definition
type BindRsParams struct {
	UserID  string
	BLBID   string
	IPGroup string
	Rss     []*Rs
}

// UnbindRsParams defintion
type UnbindRsParams struct {
	UserID     string
	BLBID      string
	IPGroup    string
	MemberList []string
}

// UpdateElbRsForRoParams definition
type UpdateElbRsForRoParams struct {
	UserID     string
	ToUpdateRs []*Rs
	BLBID      string
	IPGroupID  string
}
