/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/09/05 <EMAIL> Exp
 *
 **************************************************************************/
package blbv2

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	elbv2 "icode.baidu.com/baidu/scs/x1-base/sdk/elb_v2"
)

// CreatePublishEndpoint will create publishEndpoint
func (c *component) CreatePublishEndpoint(ctx context.Context, params *CreatePublishEndpointParams) (service string, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return "", err
	}
	allowAuth := elbv2.Authentication{
		UID:  params.AllowUserId,
		Auth: "allow",
	}
	var allowAuthList []*elbv2.Authentication
	allowAuthList = append(allowAuthList, &allowAuth)

	productResource := "scs"
	if c.conf.BlbConf.Product != "" {
		productResource = c.conf.BlbConf.Product
	}

	endpointName := fmt.Sprintf("%s-%s", productResource, "-publish-endpoint")

	endpoint := elbv2.CreatePublishEndpointRequest{
		Name:        endpointName,
		Description: productResource,
		ServiceName: params.ServiceName,
		InstanceID:  params.ElbID,
		AuthList:    allowAuthList,
		Auth:        auth,
	}

	createEndpointResponse, err := c.elbEndpointSdk.CreatePublishEndpoint(ctx, &endpoint)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Create publish endpoint Fail,req:%+v, user:%s, err:%s", endpoint, endpoint.Auth.IamUserId, err.Error())
		return "", err
	}
	logger.ComponentLogger.Trace(ctx, "Create publish endpoint Suc,req:%+v, user:%s response:%+v", endpoint, endpoint.Auth.IamUserId, createEndpointResponse)
	return createEndpointResponse.Service, nil
}

// DeletePublishEndpoint will unbind service and release publish endpoint
func (c *component) DeletePublishEndpoint(ctx context.Context, params *DeletePublishEndpointParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	// 解绑服务
	unbindservice := elbv2.UnbindServiceRequest{
		Service: params.Service,
		Auth:    auth,
	}
	_, err = c.elbEndpointSdk.UnbindService(ctx, &unbindservice)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "unbind publish endpoint service Fail,req:%+v, user:%s, err:%s", unbindservice, auth.IamUserId, err.Error())
		return err
	}

	// 删除服务发布点
	endpoint := elbv2.DeletePublishEndpointRequest{
		Service: params.Service,
		Auth:    auth,
	}

	_, err = c.elbEndpointSdk.DeletePublishEndpoint(ctx, &endpoint)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Create publish endpoint Fail,req:%+v, user:%s, err:%s", endpoint, endpoint.Auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "Create publish endpoint Suc,req:%+v, token:%s", endpoint, endpoint.Auth.IamUserId)
	return nil
}

// AddPublishEndpointAllowACL
func (c *component) AddPublishEndpointAllowACL(ctx context.Context, params *AddPublishEndpointAllowACLParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}

	// 添加 ACL
	setServiceACLReq := elbv2.CommonPublishEndpointACLRequest{
		Service:  params.Service,
		AuthList: []*elbv2.Authentication{},
		Auth:     auth,
	}

	for _, uid := range params.AllowUserIDList {
		setServiceACLReq.AuthList = append(setServiceACLReq.AuthList, &elbv2.Authentication{
			UID:  uid,
			Auth: "allow",
		})
	}
	_, err = c.elbEndpointSdk.SetPublishEndpointACL(ctx, &setServiceACLReq)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "add publish endpoint service allow acl Fail,req:%+v, user:%s, err:%s", setServiceACLReq, auth.IamUserId, err.Error())
		return err
	}
	return nil
}
