/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/06/07 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file blb.go
 * <AUTHOR>
 * @date 2022/06/07 20:51:11
 * @brief blb_v1 impl
 *
 **/

package blbv2

import (
	"context"
	"fmt"
	"sync"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/logger"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud/dbstack/lb/adaptor"
	"icode.baidu.com/baidu/scs/x1-base/sdk/common"
	elbv2 "icode.baidu.com/baidu/scs/x1-base/sdk/elb_v2"
)

type component struct {
	conf           *config
	elbSdk         elbv2.ELBService
	appElbSdk      elbv2.AppELBService
	elbEndpointSdk elbv2.ELBPublishEndpointService
	initOnce       sync.Once
}

var defaultComponent = &component{
	conf: &config{},
}

// Instance 获取默认的blb组件，必须在gdp初始化env和ral后使用
func Instance() *component {
	defaultComponent.initOnce.Do(func() {
		// load default conf
		if err := compo_utils.LoadConf("blb", defaultComponent.conf); err != nil {
			panic(err.Error())
		}
		// default elbEndpointSdk
		if privatecloud.IsPrivateENV() {
			switch privatecloud.GetPrivateEnvType() {
			case privatecloud.DBStackPrefix:
				defaultComponent.appElbSdk = adaptor.GetDefaultDbstackLbAdaptor()
			default:
				panic(fmt.Sprintf("unsupport private env type: %s", privatecloud.GetPrivateEnvType()))
			}
		} else {
			defaultComponent.elbSdk = elbv2.NewDefaultElbSdk()
			defaultComponent.appElbSdk = elbv2.NewDefaultAppELBSdk()
			defaultComponent.elbEndpointSdk = elbv2.NewDefaultElbEndpointSdk()
		}
	})

	return defaultComponent
}

// CreateBLB will create normal blb
func (c *component) CreateBLB(ctx context.Context, params *CreateBLBParams) (blbID, address string, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return "", "", err
	}
	blbParams := elbv2.CreateELBRequest{
		Name:     params.Name,
		VpcID:    params.VpcID,
		Type:     params.Type,
		SubnetID: params.SubnetID,
		Auth:     auth,
	}

	createBLBResponse, err := c.elbSdk.CreateELB(ctx, &blbParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Create blb Fail,req:%+v, user:%s, err:%s", blbParams, auth.IamUserId, err.Error())
		return "", "", err
	}
	logger.ComponentLogger.Trace(ctx, "Create blb Suc,req:%+v, user:%s response:%+v", blbParams, auth.IamUserId, createBLBResponse)
	return createBLBResponse.BLBID, createBLBResponse.Address, nil
}

func (c *component) QueryBlbDetail(ctx context.Context, params *CommonBLBParams) (blbDetail *elbv2.BlbDetailResponse, err error) {
	if params == nil {
		logger.ComponentLogger.Warning(ctx, "Query blb detail Fail, params is null")
		return nil, cerrs.ErrInvalidParams.Errorf("params is null")
	}
	if params.ElbID == "" || params.UserID == "" {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, params is incomplete, userid:%s ,elbId:%s ", params.UserID, params.ElbID)
		return nil, cerrs.ErrInvalidParams.Errorf("params is incomplete")
	}
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}
	blbParams := elbv2.QueryBlbDetail{
		BlbID: params.ElbID,
		Auth:  auth,
	}

	blbDetailResp, err := c.elbSdk.QueryBlbDetail(ctx, &blbParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Query blb detail Fail,req:%+v, user:%s, err:%s", blbParams, auth.IamUserId, err.Error())
		return nil, err
	}
	logger.ComponentLogger.Trace(ctx, "Query blb detail Suc,req:%+v, user:%s response:%+v", blbParams, auth.IamUserId, blbDetailResp)
	return blbDetailResp, nil
}

// CreateAppBLB will create application app
func (c *component) CreateAppBLB(ctx context.Context, params *CreateBLBParams) (blbID, address string, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return "", "", err
	}
	blbParams := elbv2.CreateELBRequest{
		Name:           params.Name,
		VpcID:          params.VpcID,
		SubnetID:       params.SubnetID,
		Layer4MasterAz: params.Layer4MasterAz,
		Auth:           auth,
	}

	createBLBResponse, err := c.appElbSdk.CreateAppELB(ctx, &blbParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Create app blb Fail,req:%+v, user:%s, err:%s", blbParams, auth.IamUserId, err.Error())
		return "", "", err
	}
	logger.ComponentLogger.Trace(ctx, "Create app blb Suc,req:%+v, user:%s response:%+v", blbParams, auth.IamUserId, createBLBResponse)
	return createBLBResponse.BLBID, createBLBResponse.Address, nil
}

// CreateBatchAppBLB will batch create application app
func (c *component) CreateBatchAppBLB(ctx context.Context, params *CreateBatchBLBParams) (blbList []*BLB, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}

	// do request
	blbList = make([]*BLB, 0)
	for k, blbParam := range params.BLBParams {
		createReq := &elbv2.CreateELBRequest{
			Name:                   blbParam.Name,
			VpcID:                  blbParam.VpcID,
			SubnetID:               blbParam.SubnetID,
			Type:                   blbParam.Type,
			Layer4MasterAz:         blbParam.Layer4MasterAz,
			Auth:                   auth.Reuse(k),
			AllocateVip:            blbParam.AllocateVip,
			Layer4ClusterID:        blbParam.Layer4ClusterID,
			Layer4ClusterExclusive: blbParam.Layer4ClusterExclusive,
		}

		createBLBResponse, err := c.appElbSdk.CreateAppELB(ctx, createReq)
		if err != nil {
			return nil, err
		}

		blbList = append(blbList, &BLB{
			BLBID:       createBLBResponse.BLBID,
			Address:     createBLBResponse.Address,
			Name:        createBLBResponse.Name,
			Desc:        createBLBResponse.Desc,
			UnderlayVip: createBLBResponse.UnderlayVip,
		})
	}

	return blbList, nil
}

// DeleteAppBLB will delete application app
func (c *component) DeleteAppBLB(ctx context.Context, params *CommonBLBParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	blbParams := elbv2.DeleteELBRequest{
		BLBID: params.ElbID,
		Auth:  auth,
	}

	deleteBLBResponse, err := c.appElbSdk.DeleteAppELB(ctx, &blbParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "delete app blb Fail,req:%+v, user:%s, err:%s", blbParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "delete app blb Suc,req:%+v, user:%s response:%+v", blbParams, auth.IamUserId, deleteBLBResponse)
	return nil
}

// UpdateAppBLB will update application app
func (c *component) UpdateAppBLB(ctx context.Context, params *UpdateBLBParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	blbParams := elbv2.UpdateELBRequest{
		BLBID: params.ElbID,
		Name:  params.Name,
		Desc:  params.Desc,
		Auth:  auth,
	}

	updateBLBResponse, err := c.appElbSdk.UpdateAppELB(ctx, &blbParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "update app blb Fail,req:%+v, user:%s, err:%s", blbParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "update app blb Suc,req:%+v, user:%s response:%+v", blbParams, auth.IamUserId, updateBLBResponse)
	return nil
}

// ListAppBLB will list application app by user
func (c *component) ListAppBLB(ctx context.Context, params *CommonBLBParams) (ret []BLB, err error) {
	ret = make([]BLB, 0)
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return ret, err
	}
	blbParams := elbv2.ListELBRequest{
		BLBID: params.ElbID,
		Auth:  auth,
	}

	listBLBResponse, err := c.appElbSdk.ListAppELB(ctx, &blbParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "list app blb Fail,req:%+v, user:%s, err:%s", blbParams, auth.IamUserId, err.Error())
		return ret, err
	}
	for _, b := range listBLBResponse.BLBList {
		ret = append(ret, BLB{
			BLBID:   b.BLBID,
			Address: b.Address,
			Name:    b.Name,
			Status:  b.Status,
		})
	}
	logger.ComponentLogger.Trace(ctx, "list app blb Suc,req:%+v, user:%s response:%+v", blbParams, auth.IamUserId, listBLBResponse)
	return ret, nil
}

// GetAppBLBDetail will list application app by blb id
func (c *component) GetAppBLBDetail(ctx context.Context, params *CommonBLBParams) (ret BLB, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return ret, err
	}
	blbParams := elbv2.GetAppELBDetailRequest{
		BLBID: params.ElbID,
		Auth:  auth,
	}

	getBLBResponse, err := c.appElbSdk.GetAppELBDetail(ctx, &blbParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get app blb Fail,req:%+v, user:%s, err:%s", blbParams, auth.IamUserId, err.Error())
		return ret, err
	}
	listener := make([]*ListenerInfo, 0)
	for _, l := range getBLBResponse.Listener {
		listener = append(listener, &ListenerInfo{
			Port: l.Port,
			Type: l.Type,
		})
	}
	ret = BLB{
		BLBID:           getBLBResponse.BlbID,
		Address:         getBLBResponse.Address,
		Name:            getBLBResponse.Name,
		Status:          getBLBResponse.Status,
		Listener:        listener,
		AllowDelete:     getBLBResponse.AllowDelete,
		Cidr:            getBLBResponse.Cidr,
		CreateTime:      getBLBResponse.CreateTime,
		VpcName:         getBLBResponse.VpcName,
		SubnetName:      getBLBResponse.SubnetName,
		SubnetCider:     getBLBResponse.SubnetCider,
		Layer4ClusterId: getBLBResponse.Layer4ClusterId,
		Layer4MasterAz:  getBLBResponse.Layer4MasterAz,
		Layer4SlaveAz:   getBLBResponse.Layer4SlaveAz,
	}
	logger.ComponentLogger.Trace(ctx, "get app blb Suc,req:%+v, user:%s response:%+v", blbParams, auth.IamUserId, getBLBResponse)
	return ret, nil
}

// GetEncryptAccountID will get encrypt account id
func (c *component) GetEncryptAccountID(ctx context.Context, params *GetEncryptParams) (encrypAccount string, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return "", err
	}
	encryptParams := elbv2.GetEncryptAccountRequest{
		AccountID: params.AccountID,
		Source:    params.Source,
		Auth:      auth,
	}

	getEncrypAccountResponse, err := c.appElbSdk.GetEncryptAccountID(ctx, &encryptParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "get app account Fail,req:%+v, user:%s, err:%s", encryptParams, auth.IamUserId, err.Error())
		return "", err
	}
	logger.ComponentLogger.Trace(ctx, "get app account Suc,req:%+v, user:%s response:%+v", encrypAccount, auth.IamUserId, getEncrypAccountResponse)
	return getEncrypAccountResponse.EncryptAccountID, nil
}

// CreateAppServerGroup will get create server group
func (c *component) CreateAppServerGroup(ctx context.Context, params *CreateAppServerGroupParams) (id string, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return "", err
	}
	backendServer := make([]*elbv2.BackendServerList, 0)
	for _, bs := range params.BackendServers {
		backendServer = append(backendServer, &elbv2.BackendServerList{
			InstanceID: bs.InstanceID,
			Weight:     bs.Weight,
		})
	}
	createParams := elbv2.CreateServerGroupRequest{
		Name:              params.Name,
		BlbID:             params.BlbID,
		BackendServerList: backendServer,
		Auth:              auth,
	}

	resp, err := c.appElbSdk.CreateAppServerGroup(ctx, &createParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "create app server group Fail,req:%+v, user:%s, err:%s", createParams, auth.IamUserId, err.Error())
		return "", err
	}
	logger.ComponentLogger.Trace(ctx, "create app server group Suc,req:%+v, user:%s response:%+v", createParams, auth.IamUserId, resp)
	return resp.ID, nil
}

// ListAppServerGroup will list server group
func (c *component) ListAppServerGroup(ctx context.Context, params *CommonServerGroupParams) (ids []string, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}

	listParams := elbv2.ListServerGroupRequest{
		BlbID: params.BlbID,
		Auth:  auth,
	}

	resp, err := c.appElbSdk.ListAppServerGroup(ctx, &listParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "create app server group Fail,req:%+v, user:%s, err:%s", listParams, auth.IamUserId, err.Error())
		return nil, err
	}
	logger.ComponentLogger.Trace(ctx, "create app server group Suc,req:%+v, user:%s response:%+v", listParams, auth.IamUserId, resp)
	for _, sg := range resp.AppServerGroupList {
		ids = append(ids, sg.ID)
	}
	return ids, nil
}

// DeleteAppServerGroup will delete application app sg
func (c *component) DeleteAppServerGroup(ctx context.Context, params *CommonSeverGroupParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	deleteParams := elbv2.DeleteServerGroupRequest{
		BlbID:         params.BlbID,
		ServerGroupID: params.ServerGroupID,
		Auth:          auth,
	}

	resp, err := c.appElbSdk.DeleteAppServerGroup(ctx, &deleteParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "delete app blb server group Fail,req:%+v, user:%s, err:%s", deleteParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "delete app blb server group Suc,req:%+v, user:%s response:%+v", deleteParams, auth.IamUserId, resp)
	return nil
}

// CreateAppServerGroupPort will get create server group
func (c *component) CreateAppServerGroupPort(ctx context.Context, params *CreateServerGroupPortParams) (id string, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return "", err
	}
	createParams := elbv2.CommonServerGroupPortRequest{
		BlbID:         params.BlbID,
		ServerGroupID: params.ServerGroupID,
		Type:          params.Type,
		Port:          params.Port,
		Auth:          auth,
	}

	resp, err := c.appElbSdk.CreateAppServerGroupPort(ctx, &createParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "create app server group port Fail,req:%+v, user:%s, err:%s", createParams, auth.IamUserId, err.Error())
		return "", err
	}
	logger.ComponentLogger.Trace(ctx, "create app server group port Suc,req:%+v, user:%s response:%+v", createParams, auth.IamUserId, resp)
	return resp.ID, nil
}

// DeleteAppServerGroupPort will delete application app sg port
func (c *component) DeleteAppServerGroupPort(ctx context.Context, params *DeleteServerGroupPortParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}

	portList := make([]string, 0)
	for _, portID := range params.PortIDList {
		portList = append(portList, portID)
	}

	deleteParams := elbv2.DeleteServerGroupPortRequest{
		BlbID:         params.BlbID,
		ServerGroupID: params.ServerGroupID,
		PortIDList:    portList,
		Auth:          auth,
	}

	resp, err := c.appElbSdk.DeleteAppServerGroupPort(ctx, &deleteParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "delete app blb server group port Fail,req:%+v, user:%s, err:%s", deleteParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "delete app blb server group port Suc,req:%+v, user:%s response:%+v", deleteParams, auth.IamUserId, resp)
	return nil
}

// CreateAppBlbRs will create rs
func (c *component) CreateAppBlbRs(ctx context.Context, params *CommonAppBlbRsParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}

	backendServer := make([]*elbv2.BackendServerList, 0)
	for _, bs := range params.BackendServers {
		backendServer = append(backendServer, &elbv2.BackendServerList{
			InstanceID: bs.InstanceID,
			Weight:     bs.Weight,
		})
	}

	createParams := elbv2.CommonAppBlbRsRequest{
		BlbID:             params.BlbID,
		ServerGroupID:     params.ServerGroupID,
		BackendServerList: backendServer,
		Auth:              auth,
	}

	resp, err := c.appElbSdk.CreateAppBLBRs(ctx, &createParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "create app blb rs Fail,req:%+v, user:%s, err:%s", createParams, auth.IamUserId, err.Error())
		return err
	}

	logger.ComponentLogger.Trace(ctx, "create app blb rs Suc,req:%+v, user:%s response:%+v", createParams, auth.IamUserId, resp)
	return nil
}

// DeleteAppBlbRs will delete app blb rs
func (c *component) DeleteAppBlbRs(ctx context.Context, params *DeleteAppBlbRsParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}

	deleteParams := elbv2.DeleteAppBlbRsRequest{
		BlbID:               params.BlbID,
		SgID:                params.ServerGroupID,
		BackendServerIDList: params.BackendServers,
		Auth:                auth,
	}

	resp, err := c.appElbSdk.DeleteAppBLBRs(ctx, &deleteParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "delete app blb rs Fail,req:%+v, user:%s, err:%s", deleteParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "delete app blb rs Suc,req:%+v, user:%s response:%+v", deleteParams, auth.IamUserId, resp)
	return nil
}

// UpdateAppBlbRs will update app blb rs
func (c *component) UpdateAppBlbRs(ctx context.Context, params *CommonAppBlbRsParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}

	backendServer := make([]*elbv2.BackendServerList, 0)
	for _, bs := range params.BackendServers {
		backendServer = append(backendServer, &elbv2.BackendServerList{
			InstanceID: bs.InstanceID,
			Weight:     bs.Weight,
		})
	}

	updateParams := elbv2.CommonAppBlbRsRequest{
		BlbID:             params.BlbID,
		ServerGroupID:     params.ServerGroupID,
		BackendServerList: backendServer,
		Auth:              auth,
	}

	resp, err := c.appElbSdk.UpdateAppBLBRs(ctx, &updateParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "update app blb rs Fail,req:%+v, user:%s, err:%s", updateParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "update app blb rs Suc,req:%+v, user:%s response:%+v", updateParams, auth.IamUserId, resp)
	return nil
}

// ListAppBlbRs will list app blb rs
func (c *component) ListAppBlbRs(ctx context.Context, params *CommonAppBlbRsParams) (servers []BackendServers, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}

	listParams := elbv2.ListAppBlbRsRequest{
		BlbID:         params.BlbID,
		ServerGroupID: params.ServerGroupID,
		Auth:          auth,
	}

	resp, err := c.appElbSdk.ListAppBLBRs(ctx, &listParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "list app blb rs Fail,req:%+v, user:%s, err:%s", listParams, auth.IamUserId, err.Error())
		return nil, err
	}

	servers = make([]BackendServers, 0)
	for _, bs := range resp.BackendServerList {
		var portList []*PortInfo
		for _, port := range bs.PortList {
			portList = append(portList, &PortInfo{
				ListenerPort: port.ListenerPort,
				BackendPort:  port.BackendPort,
				PortType:     port.PortType,
				PortID:       port.PortID,
				PolicyID:     port.PolicyID,
				Status:       port.Status,
			})
		}

		servers = append(servers, BackendServers{
			InstanceID: bs.InstanceID,
			PrivateIP:  bs.PrivateIP,
			Weight:     bs.Weight,
			PortList:   portList,
		})
	}

	logger.ComponentLogger.Trace(ctx, "list app blb rs Suc,req:%+v, user:%s response:%+v", listParams, auth.IamUserId, resp)
	return servers, nil
}

// ListAppBlbMountRs will list app blb mount rs
func (c *component) ListAppBlbMountRs(ctx context.Context, params *CommonAppBlbRsParams) (servers []BackendServers, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}

	listParams := elbv2.CommonAppBlbRsListRequest{
		BlbID:         params.BlbID,
		ServerGroupID: params.ServerGroupID,
		Auth:          auth,
	}

	resp, err := c.appElbSdk.ListAppBLBRsMount(ctx, &listParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "list app blb mount rs Fail,req:%+v, user:%s, err:%s", listParams, auth.IamUserId, err.Error())
		return nil, err
	}

	servers = make([]BackendServers, 0)
	for _, bs := range resp.BackendServerList {
		var portList []*PortInfo
		for _, port := range bs.PortList {
			portList = append(portList, &PortInfo{
				ListenerPort: port.ListenerPort,
				BackendPort:  port.BackendPort,
				PortType:     port.PortType,
				PortID:       port.PortID,
				PolicyID:     port.PolicyID,
				Status:       port.Status,
			})
		}

		servers = append(servers, BackendServers{
			InstanceID: bs.InstanceID,
			PrivateIP:  bs.PrivateIP,
			Weight:     bs.Weight,
			PortList:   portList,
		})
	}

	logger.ComponentLogger.Trace(ctx, "list app blb mount rs Suc,req:%+v, user:%s response:%+v", listParams, auth.IamUserId, resp)
	return servers, nil
}

// ListAppBlbUnmountRs will list app blb unmount rs
func (c *component) ListAppBlbUnmountRs(ctx context.Context, params *CommonAppBlbRsParams) (servers []BackendServers, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}

	listParams := elbv2.CommonAppBlbRsListRequest{
		BlbID:         params.BlbID,
		ServerGroupID: params.ServerGroupID,
		Auth:          auth,
	}

	resp, err := c.appElbSdk.ListAppBLBRsUnmount(ctx, &listParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "list app blb unmount rs Fail,req:%+v, user:%s, err:%s", listParams, auth.IamUserId, err.Error())
		return nil, err
	}

	servers = make([]BackendServers, 0)
	for _, bs := range resp.BackendServerList {
		var portList []*PortInfo
		for _, port := range bs.PortList {
			portList = append(portList, &PortInfo{
				ListenerPort: port.ListenerPort,
				BackendPort:  port.BackendPort,
				PortType:     port.PortType,
				PortID:       port.PortID,
				PolicyID:     port.PolicyID,
				Status:       port.Status,
			})
		}

		servers = append(servers, BackendServers{
			InstanceID: bs.InstanceID,
			PrivateIP:  bs.PrivateIP,
			Weight:     bs.Weight,
			PortList:   portList,
		})
	}

	logger.ComponentLogger.Trace(ctx, "list app blb unmount rs Suc,req:%+v, user:%s response:%+v", listParams, auth.IamUserId, resp)
	return servers, nil
}

// DeleteListener will delete app blb listener
func (c *component) DeleteListener(ctx context.Context, params *DeleteAppListenerParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	deleteParams := elbv2.DeleteAppListenerRequest{
		BlbID:    params.BlbID,
		PortList: params.PortList,
		Auth:     auth,
	}

	resp, err := c.appElbSdk.DeleteAppListener(ctx, &deleteParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "delete app blb listener Fail,req:%+v, user:%s, err:%s", deleteParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "delete app blb listener Suc,req:%+v, user:%s response:%+v", deleteParams, auth.IamUserId, resp)
	return nil
}

// CreateAppTCPListener will create tcp listener
func (c *component) CreateAppTCPListener(ctx context.Context, params *CommonListenerParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}

	createParams := elbv2.CreateAppTCPListenerRequest{
		BlbID:             params.BlbID,
		ListenerPort:      params.ListenerPort,
		Scheduler:         params.Scheduler,
		DrainingTimeout:   0,
		Auth:              auth,
		TCPSessionTimeout: c.conf.BlbTCPListenerConf.TCPSessionTimeout,
	}

	resp, err := c.appElbSdk.CreateAppTCPListener(ctx, &createParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "create app blb tcp listener Fail,req:%+v, user:%s, err:%s", createParams, auth.IamUserId, err.Error())
		return err
	}

	logger.ComponentLogger.Trace(ctx, "create app blb tcp listener Suc,req:%+v, user:%s response:%+v", createParams, auth.IamUserId, resp)
	return nil
}

// UpdateAppTCPListener will update app blb listener
func (c *component) UpdateAppTCPListener(ctx context.Context, params *CommonListenerParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}

	updateParams := elbv2.CommonAppTCPListenerRequest{
		BlbID:        params.BlbID,
		ListenerPort: params.ListenerPort,
		Scheduler:    params.Scheduler,
		Auth:         auth,
	}

	resp, err := c.appElbSdk.UpdateAppTCPListener(ctx, &updateParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "update app blb tcp listener Fail,req:%+v, user:%s, err:%s", updateParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "update app blb tcp listener Suc,req:%+v, user:%s response:%+v", updateParams, auth.IamUserId, resp)
	return nil
}

// ListAppTCPListener will list app blb all tcp listener
func (c *component) ListAppTCPListener(ctx context.Context, params *CommonListenerParams) (listeners []TCPListener, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}

	listParams := elbv2.CommonListAppListenerRequest{
		BlbID: params.BlbID,
		Auth:  auth,
	}

	resp, err := c.appElbSdk.ListAppTCPListener(ctx, &listParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "list app blb tcp listener Fail,req:%+v, user:%s, err:%s", listParams, auth.IamUserId, err.Error())
		return nil, err
	}

	listeners = make([]TCPListener, 0)
	for _, l := range resp.ListenerList {
		listeners = append(listeners, TCPListener{
			ListenerPort:      l.ListenerPort,
			Scheduler:         l.Scheduler,
			TCPSessionTimeout: l.TCPSessionTimeout,
		})
	}

	logger.ComponentLogger.Trace(ctx, "list app tcp listener Suc,req:%+v, user:%s response:%+v", listParams, auth.IamUserId, resp)
	return listeners, nil
}

// DescAppTCPListener will get app blb  tcp listener
func (c *component) DescAppTCPListener(ctx context.Context, params *CommonListenerParams) (listeners []TCPListener, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}

	listParams := elbv2.CommonListAppListenerRequest{
		BlbID:        params.BlbID,
		ListenerPort: params.ListenerPort,
		Auth:         auth,
	}

	resp, err := c.appElbSdk.ListAppTCPListener(ctx, &listParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "desc app blb tcp listener Fail,req:%+v, user:%s, err:%s", listParams, auth.IamUserId, err.Error())
		return nil, err
	}

	listeners = make([]TCPListener, 0)
	for _, l := range resp.ListenerList {
		listeners = append(listeners, TCPListener{
			ListenerPort:      l.ListenerPort,
			Scheduler:         l.Scheduler,
			TCPSessionTimeout: l.TCPSessionTimeout,
		})
	}

	logger.ComponentLogger.Trace(ctx, "desc app tcp listener Suc,req:%+v, user:%s response:%+v", listParams, auth.IamUserId, resp)
	return listeners, nil
}

// CreateAppHTTPListener will create http listener
func (c *component) CreateAppHTTPListener(ctx context.Context, params *CommonListenerParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}

	createParams := elbv2.CommonAppHTTPListenerRequest{
		BlbID:              params.BlbID,
		ListenerPort:       params.ListenerPort,
		Scheduler:          params.Scheduler,
		Auth:               auth,
		KeepSession:        c.conf.BlbHTTPListenerConf.KeepSession,
		KeepSessionTimeout: c.conf.BlbHTTPListenerConf.KeepSessionTimeout,
		XForwardedFor:      c.conf.BlbHTTPListenerConf.XForwardedFor,
		XForwardedProto:    c.conf.BlbHTTPListenerConf.XForwardedProto,
		ServerTimeout:      c.conf.BlbHTTPListenerConf.ServerTimeout,
	}

	resp, err := c.appElbSdk.CreateAppHTTPListener(ctx, &createParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "create app blb http listener Fail,req:%+v, user:%s, err:%s", createParams, auth.IamUserId, err.Error())
		return err
	}

	logger.ComponentLogger.Trace(ctx, "create app blb http listener Suc,req:%+v, user:%s response:%+v", createParams, auth.IamUserId, resp)
	return nil
}

// UpdateAppHTTPListener will update app blb  http listener
func (c *component) UpdateAppHTTPListener(ctx context.Context, params *CommonListenerParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}

	updateParams := elbv2.CommonAppHTTPListenerRequest{
		BlbID:        params.BlbID,
		ListenerPort: params.ListenerPort,
		Scheduler:    params.Scheduler,
		Auth:         auth,
	}

	resp, err := c.appElbSdk.UpdateAppHTTPListener(ctx, &updateParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "update app blb http listener Fail,req:%+v, user:%s, err:%s", updateParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "update app blb http listener Suc,req:%+v, user:%s response:%+v", updateParams, auth.IamUserId, resp)
	return nil
}

// ListAppHTTPListener will list app blb all http listener
func (c *component) ListAppHTTPListener(ctx context.Context, params *CommonListenerParams) (listeners []HTTPListener, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}

	listParams := elbv2.CommonListAppListenerRequest{
		BlbID: params.BlbID,
		Auth:  auth,
	}

	resp, err := c.appElbSdk.ListAppHTTPListener(ctx, &listParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "list app blb listener Fail,req:%+v, user:%s, err:%s", listParams, auth.IamUserId, err.Error())
		return nil, err
	}

	listeners = make([]HTTPListener, 0)
	for _, l := range resp.ListenerList {
		listeners = append(listeners, HTTPListener{
			ListenerPort: l.ListenerPort,
			BackendPort:  l.BackendPort,
			Scheduler:    l.Scheduler,
		})
	}

	logger.ComponentLogger.Trace(ctx, "list app http listener Suc,req:%+v, user:%s response:%+v", listParams, auth.IamUserId, resp)
	return listeners, nil
}

// DescAppHTTPListener will desc app blb http listener
func (c *component) DescAppHTTPListener(ctx context.Context, params *CommonListenerParams) (listeners []HTTPListener, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}

	listParams := elbv2.CommonListAppListenerRequest{
		BlbID:        params.BlbID,
		ListenerPort: params.ListenerPort,
		Auth:         auth,
	}

	resp, err := c.appElbSdk.ListAppHTTPListener(ctx, &listParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "desc app blb http listener Fail,req:%+v, user:%s, err:%s", listParams, auth.IamUserId, err.Error())
		return nil, err
	}

	listeners = make([]HTTPListener, 0)
	for _, l := range resp.ListenerList {
		listeners = append(listeners, HTTPListener{
			ListenerPort: l.ListenerPort,
			BackendPort:  l.BackendPort,
			Scheduler:    l.Scheduler,
		})
	}

	logger.ComponentLogger.Trace(ctx, "desc app blb http listener Suc,req:%+v, user:%s response:%+v", listParams, auth.IamUserId, resp)
	return listeners, nil
}

// CreateAppPolicys will create policys
func (c *component) CreateAppPolicys(ctx context.Context, params *CreateAppPolicysParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}

	policys := make([]*elbv2.AppPolicyVos, 0)
	for _, p := range params.AppPolicyVos {
		ruleList := make([]*elbv2.RuleList, 0)
		for _, rule := range p.RuleList {
			ruleList = append(ruleList, &elbv2.RuleList{
				Key:   rule.Key,
				Value: rule.Value,
			})
		}
		if params.GroupType == BLBPolicyIPGroup {
			policys = append(policys, &elbv2.AppPolicyVos{
				IPGroupID: p.IPGroupID,
				RuleList:  ruleList,
				Priority:  p.Priority,
			})
		}

		if params.GroupType == BLBPolicyServerGroup {
			policys = append(policys, &elbv2.AppPolicyVos{
				AppServerGroupID: p.AppServerGroupID,
				BackendPort:      p.BackendPort,
				RuleList:         ruleList,
				Priority:         p.Priority,
			})
		}
	}

	createParams := elbv2.CreateAppPolicysRequest{
		BlbID:        params.BlbID,
		ListenerPort: params.ListenerPort,
		Type:         params.Type,
		AppPolicyVos: policys,
		Auth:         auth,
	}

	resp, err := c.appElbSdk.CreateAppPolicys(ctx, &createParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "create app blb policy Fail,req:%+v, user:%s, err:%s", createParams, auth.IamUserId, err.Error())
		return err
	}

	logger.ComponentLogger.Trace(ctx, "create app blb policy Suc,req:%+v, user:%s response:%+v", createParams, auth.IamUserId, resp)
	return nil
}

// UpdateAppHTTPListener will update app blb policys
func (c *component) DeleteAppPolicys(ctx context.Context, params *DeleteAppPolicysParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}

	deleteParams := elbv2.DeleteAppPolicysRequest{
		BlbID:        params.BlbID,
		Port:         params.Port,
		Type:         params.Type,
		PolicyIDList: params.PolicyIDs,
		Auth:         auth,
	}

	resp, err := c.appElbSdk.DeleteAppPolicys(ctx, &deleteParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "delete app blb policy Fail,req:%+v, user:%s, err:%s", deleteParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "delete app blb policy Suc,req:%+v, user:%s response:%+v", deleteParams, auth.IamUserId, resp)
	return nil
}

// ListAppPolicys will list app blb policys
func (c *component) ListAppPolicys(ctx context.Context, params *ListAppPolicysParams) (policys []Policys, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}

	listParams := elbv2.ListAppPolicysRequest{
		BlbID: params.BlbID,
		Port:  params.Port,
		Type:  params.Type,
		Auth:  auth,
	}

	resp, err := c.appElbSdk.ListAppPolicys(ctx, &listParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "list app blb policy Fail,req:%+v, user:%s, err:%s", listParams, auth.IamUserId, err.Error())
		return nil, err
	}

	policys = make([]Policys, 0)
	for _, p := range resp.PolicyList {
		var ruleList []*RuleList
		for _, rule := range p.RuleList {
			ruleList = append(ruleList, &RuleList{
				Key:   rule.Key,
				Value: rule.Value,
			})
		}

		policys = append(policys, Policys{
			ID:                 p.ID,
			AppServerGroupID:   p.AppServerGroupID,
			AppServerGroupName: p.AppServerGroupName,
			FrontendPort:       p.FrontendPort,
			Type:               p.Type,
			BackendPort:        p.BackendPort,
			PortType:           p.PortType,
			Priority:           p.Priority,
			GroupType:          p.GroupType,
			RuleList:           ruleList,
		})
	}

	logger.ComponentLogger.Trace(ctx, "list app blb policy Suc,req:%+v, user:%s response:%+v", listParams, auth.IamUserId, resp)
	return policys, nil
}

// ExchangeBlbID will exchange blb id
func (c *component) ExchangeBlbID(ctx context.Context, params *BlbIDListParams) (ids []BlbIDMappingList, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}
	exchangeParams := elbv2.ExchangeBlbIDRequest{
		BlbIDList: params.BlbID,
		Auth:      auth,
	}

	resp, err := c.elbSdk.ExchangeBlbID(ctx, &exchangeParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "exchange blb id,req:%+v, user:%s, err:%s", exchangeParams, auth.IamUserId, err.Error())
		return nil, err
	}
	ids = make([]BlbIDMappingList, 0)
	for _, mapping := range resp.BlbIDMappingList {
		ids = append(ids, BlbIDMappingList{
			ShortID: mapping.ShortID,
			LongID:  mapping.LongID,
		})
	}
	logger.ComponentLogger.Trace(ctx, "exchange blb id Suc,req:%+v, user:%s response:%+v", exchangeParams, auth.IamUserId, resp)
	return ids, nil
}

// CreateAppIPGroup will create blb ip group
func (c *component) CreateAppIPGroup(ctx context.Context, params *CommonBLBParams) (id string, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return "", err
	}
	createParams := elbv2.CreateAppIPGroupRequest{
		BlbID: params.ElbID,
		Auth:  auth,
	}

	resp, err := c.appElbSdk.CreateAppIPGroup(ctx, &createParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "CreateAppIPGroup,req:%+v, user:%s, err:%s", createParams, auth.IamUserId, err.Error())
		return "", err
	}
	id = resp.ID
	logger.ComponentLogger.Trace(ctx, "CreateAppIPGroup Suc,req:%+v, user:%s response:%+v", createParams, auth.IamUserId, resp)
	return id, nil
}

// DeleteAppIPGroup will delete blb ip group
func (c *component) DeleteAppIPGroup(ctx context.Context, params *CommonIPGroupParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	deleteParams := elbv2.DeleteAppIPGroupRequest{
		BlbID:     params.BlbID,
		IPGroupID: params.IPGroupID,
		Auth:      auth,
	}

	resp, err := c.appElbSdk.DeleteAppIPGroup(ctx, &deleteParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "delete blb ip group,req:%+v, user:%s, err:%s", deleteParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "delete blb ip group Suc,req:%+v, user:%s response:%+v", deleteParams, auth.IamUserId, resp)
	return nil
}

// ListAppIPGroup will list blb ip group
func (c *component) ListAppIPGroup(ctx context.Context, params *CommonBLBParams) (list []*AppIPGroupList, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}
	listParams := elbv2.ListAppIPGroupRequest{
		BlbID:        params.ElbID,
		Auth:         auth,
		ExactlyMatch: "false",
	}

	resp, err := c.appElbSdk.ListAppIPGroup(ctx, &listParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "list blb ip group,req:%+v, user:%s, err:%s", listParams, auth.IamUserId, err.Error())
		return nil, err
	}
	list = make([]*AppIPGroupList, 0)
	for _, m := range resp.AppIPGroupList {
		policyList := make([]*BackendPolicyList, 0)
		for _, p := range m.BackendPolicyList {
			policyList = append(policyList, &BackendPolicyList{
				ID:                          p.ID,
				Type:                        p.Type,
				HealthCheck:                 p.HealthCheck,
				HealthCheckPort:             p.HealthCheckPort,
				HealthCheckURLPath:          p.HealthCheckURLPath,
				HealthCheckTimeoutInSecond:  p.HealthCheckTimeoutInSecond,
				HealthCheckIntervalInSecond: p.HealthCheckIntervalInSecond,
				HealthCheckDownRetry:        p.HealthCheckDownRetry,
				HealthCheckUpRetry:          p.HealthCheckUpRetry,
				HealthCheckNormalStatus:     p.HealthCheckNormalStatus,
			})
		}
		list = append(list, &AppIPGroupList{
			ID:                m.ID,
			Desc:              m.Desc,
			BackendPolicyList: policyList,
		})
	}
	logger.ComponentLogger.Trace(ctx, "list blb ip group Suc,req:%+v, user:%s response:%+v", listParams, auth.IamUserId, resp)
	return list, nil
}

// CreateAppIPGroupPolicy will create blb ip group policy
func (c *component) CreateAppIPGroupPolicy(ctx context.Context, params *CommonIPGroupParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	createParams := elbv2.CommonAppIPGroupPolicyRequest{
		BlbID:                       params.BlbID,
		IPGroupID:                   params.IPGroupID,
		Type:                        params.Type,
		Auth:                        auth,
		HealthCheck:                 c.conf.BlbConf.HealthCheck,
		HealthCheckTimeoutInSecond:  c.conf.BlbConf.HealthCheckTimeoutInSecond,
		HealthCheckUpRetry:          c.conf.BlbConf.HealthCheckUpRetry,
		HealthCheckDownRetry:        c.conf.BlbConf.HealthCheckDownRetry,
		HealthCheckIntervalInSecond: c.conf.BlbConf.HealthCheckIntervalInSecond,
	}

	resp, err := c.appElbSdk.CreateAppIPGroupPolicy(ctx, &createParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "create blb ip group policy, req:%+v, user:%s, err:%s", createParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "create blb ip group policy Suc,req:%+v, user:%s response:%+v", createParams, auth.IamUserId, resp)
	return nil
}

// DeleteAppIPGroupPolicy will delete blb ip group policy
func (c *component) DeleteAppIPGroupPolicy(ctx context.Context, params *DeleteIPGroupPolicyParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	deleteParams := elbv2.DeleteAppIPGroupPolicyRequest{
		BlbID:               params.BlbID,
		IPGroupID:           params.IPGroupID,
		BackendPolicyIDList: params.PolicyIDList,
		Auth:                auth,
	}

	resp, err := c.appElbSdk.DeleteAppIPGroupPolicy(ctx, &deleteParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "delete blb ip group policy,req:%+v, user:%s, err:%s", deleteParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "delete blb ip group policy Suc,req:%+v, user:%s response:%+v", deleteParams, auth.IamUserId, resp)
	return nil
}

// CreateAppIPGroupMember will create blb ip group member
func (c *component) CreateAppIPGroupMember(ctx context.Context, params *CreateIPGroupMemberParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	member := make([]*elbv2.MemberList, 0)
	for _, m := range params.MemberList {
		member = append(member, &elbv2.MemberList{
			IP:     m.IP,
			Port:   m.Port,
			Weight: m.Weight,
		})
	}

	createParams := elbv2.CommonAppIPGroupMemberRequest{
		BlbID:      params.BlbID,
		IPGroupID:  params.IPGroupID,
		MemberList: member,
		Auth:       auth,
	}

	resp, err := c.appElbSdk.CreateAppIPGroupMember(ctx, &createParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "create blb ip group member,req:%+v, user:%s, err:%s", createParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "create blb ip group member Suc,req:%+v, user:%s response:%+v", createParams, auth.IamUserId, resp)
	return nil
}

// DeleteAppIPGroupMember will delete blb ip group member
func (c *component) DeleteAppIPGroupMember(ctx context.Context, params *DeleteIPGroupMemberParams) (err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return err
	}
	deleteParams := elbv2.DeleteAppIPGroupMemberRequest{
		BlbID:        params.BlbID,
		IPGroupID:    params.IPGroupID,
		MemberIDList: params.MemberList,
		Auth:         auth,
	}

	resp, err := c.appElbSdk.DeleteAppIPGroupMember(ctx, &deleteParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "delete blb ip group member,req:%+v, user:%s, err:%s", deleteParams, auth.IamUserId, err.Error())
		return err
	}
	logger.ComponentLogger.Trace(ctx, "delete blb ip group member Suc,req:%+v, user:%s response:%+v", deleteParams, auth.IamUserId, resp)
	return nil
}

// ListAppIPGroupMember will list blb ip group member
func (c *component) ListAppIPGroupMember(ctx context.Context, params *CommonIPGroupParams) (member []Member, err error) {
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "Get auth Fail, userid:%s , err:%s", params.UserID, err.Error())
		return nil, err
	}
	listParams := elbv2.ListAppIPGroupMemberRequest{
		BlbID:     params.BlbID,
		IPGroupID: params.IPGroupID,
		Auth:      auth,
	}

	resp, err := c.appElbSdk.ListAppIPGroupMember(ctx, &listParams)
	if err != nil {
		logger.ComponentLogger.Warning(ctx, "list blb ip group member,req:%+v, user:%s, err:%s", listParams, auth.IamUserId, err.Error())
		return nil, err
	}
	member = make([]Member, 0)
	for _, m := range resp.MemberList {
		member = append(member, Member{
			IP:     m.IP,
			Port:   m.Port,
			Weight: m.Weight,
			ID:     m.MemberID,
		})
	}
	logger.ComponentLogger.Trace(ctx, "list blb ip group member Suc,req:%+v, user:%s response:%+v", listParams, auth.IamUserId, resp)
	return member, nil
}

// BindRs will add ip into ipGroup member
func (c *component) BindRs(ctx context.Context, params *BindRsParams) error {
	// get auth
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	// 查询已经存在的ip
	listReq := &elbv2.ListAppIPGroupMemberRequest{
		BlbID:     params.BLBID,
		IPGroupID: params.IPGroup,
		Auth:      auth,
	}
	listRsp, err := c.appElbSdk.ListAppIPGroupMember(ctx, listReq)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return err
	}

	// 获得没有建立过的backend
	rsExists := map[string]bool{}
	for _, rs := range listRsp.MemberList {
		exKey := fmt.Sprintf("%s:%d", rs.IP, rs.Port)
		rsExists[exKey] = true
	}

	backendList := make([]*elbv2.MemberList, 0)
	for _, rs := range params.Rss {
		rsIpPort := fmt.Sprintf("%s:%d", rs.IP, rs.Port)
		if rsExists[rsIpPort] {
			continue
		}
		backendList = append(backendList, &elbv2.MemberList{
			IP:     rs.IP,
			Port:   rs.Port,
			Weight: rs.Weight,
		})
	}
	if len(backendList) == 0 {
		return nil
	}

	// 创建这些backend
	createReq := &elbv2.CommonAppIPGroupMemberRequest{
		MemberList: backendList,
		IPGroupID:  params.IPGroup,
		BlbID:      params.BLBID,
		Auth:       auth,
	}
	_, err = c.appElbSdk.CreateAppIPGroupMember(ctx, createReq)
	return err
}

// UnbindRs will remove ip into ipGroup member
func (c *component) UnbindRs(ctx context.Context, params *UnbindRsParams) error {
	// get auth
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}

	// 查询已经存在的ip
	listReq := &elbv2.ListAppIPGroupMemberRequest{
		BlbID:     params.BLBID,
		IPGroupID: params.IPGroup,
		Auth:      auth,
	}
	listRsp, err := c.appElbSdk.ListAppIPGroupMember(ctx, listReq)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return err
	}

	rsExistsMemberID := map[string]string{}
	for _, rs := range listRsp.MemberList {
		rsKey := fmt.Sprintf("%s:%d", rs.IP, rs.Port)
		rsExistsMemberID[rsKey] = rs.MemberID
	}

	backendList := make([]string, 0)
	// to unbind rs by ip:port, need params.MemberList element be like this: `ip:port`
	for _, rs := range params.MemberList {
		if _, ok := rsExistsMemberID[rs]; !ok {
			continue
		}
		backendList = append(backendList, rsExistsMemberID[rs])
	}
	if len(backendList) == 0 {
		return nil
	}

	// 删除这些backend
	deleteReq := &elbv2.DeleteAppIPGroupMemberRequest{
		MemberIDList: backendList,
		IPGroupID:    params.IPGroup,
		BlbID:        params.BLBID,
		Auth:         auth,
	}
	_, err = c.appElbSdk.DeleteAppIPGroupMember(ctx, deleteReq)
	return err
}

// 获取资源层环境变量
func (c *component) GetEnv(ctx context.Context) *BlbEnv {
	return c.conf.BlbEnv
}

// 获取资源层子网(cloud)
func (c *component) GetResourceCloudSubnet(ctx context.Context, zone string) (string, bool) {
	subnetMap := make(map[string]string)
	for _, subnet := range c.conf.BlbEnv.ResourceCloudSubnet {
		subnetMap[subnet.Zone] = subnet.SubnetId
	}
	subnetID, found := subnetMap[zone]
	return subnetID, found
}

// 获取资源层子网(private)
func (c *component) GetResourcePrivateSubnet(ctx context.Context, zone string) (string, bool) {
	subnetMap := make(map[string]string)
	for _, subnet := range c.conf.BlbEnv.ResourcePrivateSubnet {
		subnetMap[subnet.Zone] = subnet.SubnetId
	}
	subnetID, found := subnetMap[zone]
	return subnetID, found
}

// clouduser 是否使用 private resource
func (c *component) IsUsePrivateResource(ctx context.Context, userId string) bool {
	for _, user := range c.conf.BlbEnv.CloudUserOfUsePrivateResource {
		if user.UserId == userId {
			return true
		}
	}

	return false
}

// UpdateElbsRsForRo will update weight for ip group member
func (c *component) UpdateElbsRsForRo(ctx context.Context, params *UpdateElbRsForRoParams) (err error) {
	// get auth
	auth, err := compo_utils.GetOpenapiAuth(ctx, params.UserID)
	if err != nil {
		return err
	}
	err = c.updateElbRsForRo(ctx, params, auth)
	if err != nil {
		return err
	}
	return
}

func (c *component) updateElbRsForRo(ctx context.Context, params *UpdateElbRsForRoParams, auth *common.Authentication) (err error) {
	// 查询已经存在的ip
	listReq := &elbv2.ListAppIPGroupMemberRequest{
		BlbID:     params.BLBID,
		IPGroupID: params.IPGroupID,
		Auth:      auth,
	}
	listRsp, err := c.appElbSdk.ListAppIPGroupMember(ctx, listReq)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		return err
	}

	rsExistsMemberID := map[string]string{}
	for _, rs := range listRsp.MemberList {
		rsKey := fmt.Sprintf("%s:%d", rs.IP, rs.Port)
		rsExistsMemberID[rsKey] = rs.MemberID
	}

	toUpdateMemberList := make([]*elbv2.MemberList, 0)
	for _, rs := range params.ToUpdateRs {
		rsIpPort := fmt.Sprintf("%s:%d", rs.IP, rs.Port)
		toUpdateMemberList = append(toUpdateMemberList, &elbv2.MemberList{
			IP:       rs.IP,
			Weight:   rs.Weight,
			Port:     rs.Port,
			MemberID: rsExistsMemberID[rsIpPort],
		})
	}

	if len(toUpdateMemberList) != 0 {
		updateReq := &elbv2.CommonAppIPGroupMemberRequest{
			IPGroupID:  params.IPGroupID,
			MemberList: toUpdateMemberList,
			BlbID:      params.BLBID,
			Auth:       auth,
		}
		if _, err = c.appElbSdk.UpdateAppIPGroupMember(ctx, updateReq); err != nil {
			return err
		}
	}

	return nil
}
