/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * author: wangbin34 (<EMAIL>)
 * Date: 2024-09-10
 * File: blb_service_test.go
 */
package blbv2

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	elbSDK "icode.baidu.com/baidu/scs/x1-base/sdk/elb_v2"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest/sdkmock"
)

const (
	TestUserID         = "ea2c4a2286ca4540afcb7f7d4ba2d199"
	TestBlbServiceName = "service-xx"
)

func TestCreatePublishEndpointOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockBlbServiceSDK := sdkmock.NewMockELBPublishEndpointService(ctrl)
	mockBlbServiceSDK.EXPECT().CreatePublishEndpoint(gomock.Any(), gomock.Any()).Return(&elbSDK.CreatePublishEndpointResponse{
		Service: TestBlbServiceName,
	}, nil)

	testComponent := &component{
		conf: &config{},
	}
	if err := compo_utils.LoadConf("blb", testComponent.conf); err != nil {
		t.Errorf("Read conf failed: %v", err)
	}
	testComponent.elbEndpointSdk = mockBlbServiceSDK

	createParams := CreatePublishEndpointParams{
		UserID: TestUserID,
	}
	serviceName, err := testComponent.CreatePublishEndpoint(ctx, &createParams)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
		return
	}

	if serviceName != TestBlbServiceName {
		t.Errorf("service name not match")
		return
	}
}

func TestDeletePublishEndpointOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockBlbServiceSDK := sdkmock.NewMockELBPublishEndpointService(ctrl)
	mockBlbServiceSDK.EXPECT().UnbindService(gomock.Any(), gomock.Any()).Return(&elbSDK.CommonPublishEndpointResponse{}, nil)
	mockBlbServiceSDK.EXPECT().DeletePublishEndpoint(gomock.Any(), gomock.Any()).Return(&elbSDK.CommonPublishEndpointResponse{}, nil)

	testComponent := &component{
		conf: &config{},
	}
	if err := compo_utils.LoadConf("blb", testComponent.conf); err != nil {
		t.Errorf("Read conf failed: %v", err)
	}
	testComponent.elbEndpointSdk = mockBlbServiceSDK

	createParams := DeletePublishEndpointParams{
		UserID:  TestUserID,
		Service: TestUserID,
	}
	err := testComponent.DeletePublishEndpoint(ctx, &createParams)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
		return
	}
}

func TestAddPublishEndpointAllowACLOk(t *testing.T) {
	ctx := context.Background()
	defer sdk_utils.TestEnvDefer(ctx)()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockBlbServiceSDK := sdkmock.NewMockELBPublishEndpointService(ctrl)
	mockBlbServiceSDK.EXPECT().SetPublishEndpointACL(gomock.Any(), gomock.Any()).Return(&elbSDK.CommonPublishEndpointResponse{}, nil)

	testComponent := &component{
		conf: &config{},
	}
	if err := compo_utils.LoadConf("blb", testComponent.conf); err != nil {
		t.Errorf("Read conf failed: %v", err)
	}
	testComponent.elbEndpointSdk = mockBlbServiceSDK

	createParams := AddPublishEndpointAllowACLParams{
		UserID:          TestUserID,
		Service:         TestUserID,
		AllowUserIDList: []string{TestUserID},
	}
	err := testComponent.AddPublishEndpointAllowACL(ctx, &createParams)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
		return
	}
}
